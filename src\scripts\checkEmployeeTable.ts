import { DataSource } from 'typeorm';

// Database configuration
const AppDataSource = new DataSource({
  type: "mysql",
  host: process.env.DB_HOST || "localhost",
  port: parseInt(process.env.DB_PORT || "3306"),
  username: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "root",
  database: process.env.DB_NAME || "ims_db",
  synchronize: false,
  logging: false,
  entities: [],
  subscribers: [],
  migrations: []
});

const checkEmployeeTable = async () => {
  console.log("Checking employees table structure...\n");

  try {
    // Initialize the data source
    await AppDataSource.initialize();

    // Get query runner
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    
    // Check if employees table exists
    const [tables] = await queryRunner.query(
      "SHOW TABLES LIKE 'employees'"
    );
    
    if (tables.length === 0) {
      console.log("❌ Employees table does not exist!");
      return;
    }
    
    console.log("✅ Employees table exists\n");
    
    // Get table structure
    console.log("📋 Current employees table structure:");
    console.log("=" .repeat(60));
    
    const columns = await queryRunner.query("DESCRIBE employees");
    
    console.log("Column Name".padEnd(25) + "Type".padEnd(20) + "Null".padEnd(10) + "Key".padEnd(10) + "Default");
    console.log("-".repeat(80));
    
    columns.forEach((col: any, index: number) => {
      console.log(
        `${(index + 1).toString().padStart(2)}. ${col.Field.padEnd(20)} ${col.Type.padEnd(15)} ${col.Null.padEnd(8)} ${col.Key.padEnd(8)} ${col.Default || 'NULL'}`
      );
    });
    
    console.log("\n" + "=".repeat(60));
    console.log(`Total columns: ${columns.length}\n`);
    
    // Check for missing columns that the entity expects
    const expectedColumns = [
      'firstName', 'lastName', 'fatherName', 'middleName', 'gender', 
      'dateOfBirth', 'religion', 'cnicNumber', 'cnicExpiryDate', 
      'nationality', 'maritalStatus', 'bloodType', 'employeeId', 'status', 'status_date'
    ];
    
    const existingColumns = columns.map((col: any) => col.Field);
    const missingColumns = expectedColumns.filter(col => !existingColumns.includes(col));
    
    if (missingColumns.length > 0) {
      console.log("⚠️  Missing columns that the Entity expects:");
      missingColumns.forEach(col => {
        console.log(`   - ${col}`);
      });
    } else {
      console.log("✅ All expected columns are present!");
    }
    
    console.log("\n📊 Employee table statistics:");
    const [countResult] = await queryRunner.query("SELECT COUNT(*) as count FROM employees");
    console.log(`   Total employees: ${countResult.count}`);
    
    if (countResult.count > 0) {
      console.log("\n📄 Sample data (first 3 records):");
      const sampleData = await queryRunner.query("SELECT * FROM employees LIMIT 3");
      console.table(sampleData);
    }
    
    await queryRunner.release();
    await AppDataSource.destroy();
    
  } catch (error) {
    console.error("❌ Error checking database:", error);
  }
};

checkEmployeeTable().catch(console.error); 