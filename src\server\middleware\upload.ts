import multer from 'multer';
import { Request, Response, NextFunction } from 'express';
import path from 'path';
import fs from 'fs';

// Ensure uploads directory exists
const uploadDir = path.join(__dirname, '../../uploads');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Create invoices subdirectory
const invoicesDir = path.join(uploadDir, 'invoices');
if (!fs.existsSync(invoicesDir)) {
  fs.mkdirSync(invoicesDir, { recursive: true });
}

const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // Store invoice files in the invoices directory
    if (file.fieldname === 'invoice_file' || file.fieldname === 'Signed Invoice') {
      cb(null, invoicesDir);
    } else {
      cb(null, uploadDir);
    }
  },
  filename: function (_req, file, cb) {
    // Generate a unique filename with original extension
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `${uniqueSuffix}${ext}`);
  }
});

const fileFilter = function (_req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) {
  console.log('Processing file upload:', file.fieldname, file.originalname, file.mimetype);
  
  // Check both extension and mimetype
  const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx'];
  const allowedMimeTypes = [
    'image/jpeg', 
    'image/png', 
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ];
  
  const extname = path.extname(file.originalname).toLowerCase();
  const isValidExtension = allowedExtensions.includes(extname);
  const isValidMimetype = allowedMimeTypes.includes(file.mimetype);

  // For testing, let any file type through in development
  const isDev = process.env.NODE_ENV === 'development';
  if (isDev) {
    console.log('Development mode: accepting all file types');
    return cb(null, true);
  }

  // Either valid extension or valid mimetype is acceptable
  if (isValidExtension || isValidMimetype) {
    cb(null, true);
  } else {
    console.log('File rejected:', file.originalname, file.mimetype);
    cb(new Error(`File type not allowed for ${file.originalname}. Allowed types: JPG, JPEG, PNG, GIF, PDF, DOC, DOCX`));
  }
};

// Create a basic multer instance with our configuration
const upload = multer({ 
  storage: storage, 
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// SIMPLIFIED APPROACH: Define field configurations for all possible file uploads
const fieldConfig = [
  { name: 'invoice_file', maxCount: 1 },
  { name: 'Signed Invoice', maxCount: 1 },
  { name: 'file', maxCount: 1 }, // This is the field name used in frontend FormData
  { name: 'files', maxCount: 5 },
  { name: 'attachments', maxCount: 5 }
];

// Export the middleware functions with different configurations
export const uploadSingle = (req: Request, res: Response, next: NextFunction) => {
  console.log('Starting file upload middleware');
  console.log('Content-Type:', req.headers['content-type']);
  console.log('Request method:', req.method);
  console.log('Request URL:', req.originalUrl);
  
  // Get the raw form data for debugging 
  const rawFormData = {...req.body};
  console.log('Raw form data before multer:', Object.keys(rawFormData));
  
  // Use the fields approach which is more flexible than .single()
  const fieldsHandler = upload.fields(fieldConfig);
  
  fieldsHandler(req, res, (err) => {
    if (err) {
      console.error('File upload error:', err.message);
      
      // For size errors, return appropriate error
      if (err instanceof multer.MulterError && err.code === 'LIMIT_FILE_SIZE') {
        return res.status(413).json({
          error: 'File too large',
          details: 'File exceeds the 10MB limit'
        });
      }
      
      // Continue processing even if file upload fails
      console.warn('Upload issue, continuing anyway:', err.message);
    }
    
    // Log what files were uploaded
    if (req.files && typeof req.files === 'object') {
      Object.entries(req.files).forEach(([fieldName, files]) => {
        console.log(`Files in field '${fieldName}':`, 
          Array.isArray(files) ? files.map(f => f.originalname) : 'None');
      });
    } else {
      console.log('No files were uploaded');
    }
    
    // Also check for a single file (backward compatibility)
    if (req.file) {
      console.log('Single file detected:', req.file.originalname);
    }
    
    // Ensure we still have the form data
    console.log('Form data after multer processing:', Object.keys(req.body));
    
    // Set the main file for older code that expects req.file
    if (req.files && typeof req.files === 'object') {
      // Try to find any uploaded file and use the first one as req.file
      const allFiles = Object.values(req.files).flat();
      if (allFiles.length > 0) {
        req.file = allFiles[0];
        console.log('Set main file to:', req.file.originalname);
      }
    }
    
    // Add this: Debug log the form data to diagnose missing fields
    console.log('FORM DATA CONTENTS DUMP:');
    for (const [key, value] of Object.entries(req.body)) {
      console.log(`- ${key}: ${typeof value === 'object' ? JSON.stringify(value) : value}`);
    }
    
    next();
  });
};

// Legacy support for multiple file uploads
export const uploadMultiple = upload.array('files', 5);

// Main middleware with strong error handling
export const uploadMiddleware = (req: Request, res: Response, next: NextFunction) => {
  console.log('Using upload middleware');
  console.log('Content-Type:', req.headers['content-type']);
  console.log('Request keys before processing:', Object.keys(req.body));
  
  const fieldsHandler = upload.fields(fieldConfig);
  
  fieldsHandler(req, res, (err) => {
    if (err) {
      console.error('Upload middleware error:', err);
      
      if (err instanceof multer.MulterError) {
        return res.status(400).json({
          error: 'File upload error',
          details: err.message
        });
      } else {
        return res.status(500).json({
          error: 'Server error during upload',
          details: err.message
        });
      }
    }
    
    // Debug log all received form fields after processing
    console.log('Form data after processing:');
    for (const key in req.body) {
      const value = typeof req.body[key] === 'object' 
        ? JSON.stringify(req.body[key]) 
        : req.body[key];
      console.log(`- ${key}: ${value}`);
    }
    
    next();
  });
};
