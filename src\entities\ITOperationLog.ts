import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from './User';
import { IsNotEmpty, IsString, IsOptional, IsDate, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';

export enum IssueCategory {
  Network = 'Network',
  Hardware = 'Hardware',
  Software = 'Software',
  Email = 'Email',
  VPN = 'VPN',
  Other = 'Other'
}

export enum ImpactLevel {
  Low = 'Low',
  Medium = 'Medium',
  High = 'High',
  Critical = 'Critical'
}

export enum OperationStatus {
  Completed = 'Completed',
  InProgress = 'In Progress',
  NeedsReview = 'Needs Review'
}

@Entity('it_operation_logs')
export class ITOperationLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  @IsNotEmpty({ message: 'Title is required' })
  @IsString({ message: 'Title must be a string' })
  title: string;

  @Column({ type: 'text' })
  @IsNotEmpty({ message: 'Description is required' })
  @IsString({ message: 'Description must be a string' })
  description: string;

  @Column({ type: 'enum', enum: IssueCategory, default: IssueCategory.Other })
  @IsNotEmpty({ message: 'Issue category is required' })
  @IsEnum(IssueCategory, { message: 'Issue category must be a valid option' })
  issueCategory: IssueCategory;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString({ message: 'Assigned to must be a string' })
  assignedTo: string;

  @Column({ type: 'varchar', length: 100 })
  @IsNotEmpty({ message: 'Logged by is required' })
  @IsString({ message: 'Logged by must be a string' })
  loggedBy: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @IsString({ message: 'Location must be a string' })
  location: string;

  @Column({ type: 'enum', enum: ImpactLevel, default: ImpactLevel.Low })
  @IsNotEmpty({ message: 'Impact level is required' })
  @IsEnum(ImpactLevel, { message: 'Impact level must be a valid option' })
  impactLevel: ImpactLevel;

  @Column({ type: 'enum', enum: OperationStatus, default: OperationStatus.Completed })
  @IsNotEmpty({ message: 'Status is required' })
  @IsEnum(OperationStatus, { message: 'Status must be a valid option' })
  status: OperationStatus;

  @Column({ type: 'date' })
  @IsNotEmpty({ message: 'Date occurred is required' })
  @IsDate({ message: 'Date occurred must be a valid date' })
  @Type(() => Date)
  dateOccurred: Date;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDate({ message: 'Date resolved must be a valid date' })
  @Type(() => Date)
  dateResolved: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @IsString({ message: 'Tags must be a string' })
  tags: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  @IsOptional()
  @IsString({ message: 'External links must be a string' })
  externalLinks: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  @IsString({ message: 'Attachment must be a string' })
  attachment: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString({ message: 'Notes must be a string' })
  notes: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'createdById' })
  createdBy: User;

  @Column({ type: 'varchar', length: 36, nullable: true })
  createdById: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'lastModifiedById' })
  lastModifiedBy: User;

  @Column({ type: 'varchar', length: 36, nullable: true })
  lastModifiedById: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 