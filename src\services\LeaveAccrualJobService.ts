import { leaveAccrualService } from './LeaveAccrualService';
import { RecurringTaskService } from './RecurringTaskService';
import { LeaveAccrualRule } from '../types/attendance';
import * as cron from 'node-cron';

export class LeaveAccrualJobService {
  private recurringTaskService: RecurringTaskService;
  private scheduledJobs: Map<string, cron.ScheduledTask> = new Map();

  constructor() {
    this.recurringTaskService = new RecurringTaskService();
  }

  /**
   * Initialize accrual processing jobs
   */
  async initializeAccrualJobs(): Promise<void> {
    console.log('🔄 Initializing Leave Accrual Jobs...');

    // Schedule daily accrual processing at 2 AM
    const dailyJob = cron.schedule('0 2 * * *', async () => {
      await this.processDailyAccruals();
    });

    this.scheduledJobs.set('daily-accrual', dailyJob);

    // Schedule monthly accrual processing on 1st of each month at 3 AM
    const monthlyJob = cron.schedule('0 3 1 * *', async () => {
      await this.processMonthlyAccruals();
    });

    this.scheduledJobs.set('monthly-accrual', monthlyJob);

    // Schedule quarterly accrual processing on 1st of quarter at 4 AM
    const quarterlyJob = cron.schedule('0 4 1 1,4,7,10 *', async () => {
      await this.processQuarterlyAccruals();
    });

    this.scheduledJobs.set('quarterly-accrual', quarterlyJob);

    // Schedule annual accrual processing on Jan 1st at 5 AM
    const annualJob = cron.schedule('0 5 1 1 *', async () => {
      await this.processAnnualAccruals();
    });

    this.scheduledJobs.set('annual-accrual', annualJob);

    console.log('✅ Leave Accrual Jobs initialized successfully');
  }

  /**
   * Process daily accruals
   */
  private async processDailyAccruals(): Promise<void> {
    try {
      console.log('🌅 Starting daily accrual processing...');
      
      const accrualRules = await this.getActiveAccrualRules();
      const dailyRules = accrualRules.filter(rule => rule.accrualFrequency === 'daily');
      
      if (dailyRules.length === 0) {
        console.log('ℹ️ No daily accrual rules found');
        return;
      }

      const result = await leaveAccrualService.processAccruals(dailyRules);
      
      console.log(`✅ Daily accruals completed: ${result.processedEmployees} employees, ${result.totalAccrualsProcessed} days accrued`);
      
      if (result.errors.length > 0) {
        console.error(`⚠️ ${result.errors.length} errors during daily accrual processing`);
      }
      
    } catch (error) {
      console.error('❌ Error in daily accrual processing:', error);
    }
  }

  /**
   * Process monthly accruals
   */
  private async processMonthlyAccruals(): Promise<void> {
    try {
      console.log('📅 Starting monthly accrual processing...');
      
      const accrualRules = await this.getActiveAccrualRules();
      const monthlyRules = accrualRules.filter(rule => rule.accrualFrequency === 'monthly');
      
      if (monthlyRules.length === 0) {
        console.log('ℹ️ No monthly accrual rules found');
        return;
      }

      const result = await leaveAccrualService.processAccruals(monthlyRules);
      
      console.log(`✅ Monthly accruals completed: ${result.processedEmployees} employees, ${result.totalAccrualsProcessed} days accrued`);
      
      if (result.errors.length > 0) {
        console.error(`⚠️ ${result.errors.length} errors during monthly accrual processing`);
      }
      
    } catch (error) {
      console.error('❌ Error in monthly accrual processing:', error);
    }
  }

  /**
   * Process quarterly accruals
   */
  private async processQuarterlyAccruals(): Promise<void> {
    try {
      console.log('📊 Starting quarterly accrual processing...');
      
      const accrualRules = await this.getActiveAccrualRules();
      const quarterlyRules = accrualRules.filter(rule => rule.accrualFrequency === 'quarterly');
      
      if (quarterlyRules.length === 0) {
        console.log('ℹ️ No quarterly accrual rules found');
        return;
      }

      const result = await leaveAccrualService.processAccruals(quarterlyRules);
      
      console.log(`✅ Quarterly accruals completed: ${result.processedEmployees} employees, ${result.totalAccrualsProcessed} days accrued`);
      
      if (result.errors.length > 0) {
        console.error(`⚠️ ${result.errors.length} errors during quarterly accrual processing`);
      }
      
    } catch (error) {
      console.error('❌ Error in quarterly accrual processing:', error);
    }
  }

  /**
   * Process annual accruals
   */
  private async processAnnualAccruals(): Promise<void> {
    try {
      console.log('🎊 Starting annual accrual processing...');
      
      const accrualRules = await this.getActiveAccrualRules();
      const annualRules = accrualRules.filter(rule => rule.accrualFrequency === 'annually');
      
      if (annualRules.length === 0) {
        console.log('ℹ️ No annual accrual rules found');
        return;
      }

      const result = await leaveAccrualService.processAccruals(annualRules);
      
      console.log(`✅ Annual accruals completed: ${result.processedEmployees} employees, ${result.totalAccrualsProcessed} days accrued`);
      
      if (result.errors.length > 0) {
        console.error(`⚠️ ${result.errors.length} errors during annual accrual processing`);
      }
      
    } catch (error) {
      console.error('❌ Error in annual accrual processing:', error);
    }
  }

  /**
   * Get active accrual rules (for now returns sample rules, later integrate with database)
   */
  private async getActiveAccrualRules(): Promise<LeaveAccrualRule[]> {
    // Create some mock accrual rules for testing (remove in production)
    const mockRules: any[] = [
      {
        id: 1,
        leaveType: 'annual',
        accrualFrequency: 'monthly' as const,
        accrualAmount: 1.75,
        maxAccumulation: 21,
        carryOverLimit: 5,
        carryOverExpiry: 365,
        effectiveDate: '2024-01-01',
        isProrated: true,
        waitingPeriod: 90,
        description: 'Monthly accrual for annual leave',
        applicableRoles: [],
        applicableDepartments: []
      },
      {
        id: 2,
        leaveType: 'sick',
        accrualFrequency: 'quarterly' as const,
        accrualAmount: 3,
        maxAccumulation: 12,
        carryOverLimit: 0,
        carryOverExpiry: 0,
        effectiveDate: '2024-01-01',
        isProrated: false,
        waitingPeriod: 0,
        description: 'Quarterly accrual for sick leave',
        applicableRoles: [],
        applicableDepartments: []
      }
    ];

    return mockRules as LeaveAccrualRule[];
  }

  /**
   * Manual trigger for accrual processing (for testing/admin use)
   */
  async triggerManualAccrualProcessing(
    frequency?: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually'
  ): Promise<any> {
    try {
      console.log(`🔧 Manual accrual processing triggered${frequency ? ` for ${frequency}` : ''}`);
      
      const accrualRules = await this.getActiveAccrualRules();
      let rulesToProcess = accrualRules;

      if (frequency) {
        rulesToProcess = accrualRules.filter(rule => rule.accrualFrequency === frequency);
      }

      if (rulesToProcess.length === 0) {
        console.log('ℹ️ No matching accrual rules found');
        return { success: false, message: 'No matching accrual rules found' };
      }

      const result = await leaveAccrualService.processAccruals(rulesToProcess);
      
      console.log(`✅ Manual accrual processing completed: ${result.processedEmployees} employees, ${result.totalAccrualsProcessed} days accrued`);
      
      return {
        success: true,
        result,
        message: `Processed ${result.processedEmployees} employees, ${result.totalAccrualsProcessed} days accrued`
      };
      
    } catch (error: any) {
      console.error('❌ Error in manual accrual processing:', error);
      return {
        success: false,
        message: `Error: ${error.message}`,
        error: error.message
      };
    }
  }

  /**
   * Get accrual processing status/statistics
   */
  async getAccrualStatus(): Promise<any> {
    return {
      scheduledJobs: Array.from(this.scheduledJobs.keys()),
      nextRuns: {
        daily: 'Every day at 2:00 AM UTC',
        monthly: '1st of each month at 3:00 AM UTC',
        quarterly: '1st of quarter (Jan, Apr, Jul, Oct) at 4:00 AM UTC',
        annual: 'January 1st at 5:00 AM UTC'
      },
      activeRules: await this.getActiveAccrualRules()
    };
  }

  /**
   * Stop all accrual jobs
   */
  stopAllJobs(): void {
    console.log('🛑 Stopping all accrual jobs...');
    
    this.scheduledJobs.forEach((job, name) => {
      job.stop();
      console.log(`⏹️ Stopped ${name} job`);
    });
    
    this.scheduledJobs.clear();
    console.log('✅ All accrual jobs stopped');
  }

  /**
   * Restart all accrual jobs
   */
  async restartAllJobs(): Promise<void> {
    console.log('🔄 Restarting all accrual jobs...');
    this.stopAllJobs();
    await this.initializeAccrualJobs();
  }
}

export const leaveAccrualJobService = new LeaveAccrualJobService(); 