import React, { useState, useEffect } from 'react';
import {
  FileText,
  Download,
  Search,
  Send,
  Filter,
  ChevronDown,
  ChevronRight,
  Calendar,
  Mail,
  Check,
  Users,
  Edit,
  Clock,
  Info
} from 'lucide-react';
import { TaxStatement } from '../../../types/payroll';

interface TaxStatementsProps {
  isAdmin?: boolean;
}

const TaxStatements: React.FC<TaxStatementsProps> = ({ isAdmin = false }) => {
  const [loading, setLoading] = useState(true);
  const [statements, setStatements] = useState<TaxStatement[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedYear, setSelectedYear] = useState<string>(new Date().getFullYear().toString());
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  
  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'PKR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };
  
  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="mr-1 h-3 w-3" />
            Draft
          </span>
        );
      case 'final':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <Check className="mr-1 h-3 w-3" />
            Final
          </span>
        );
      case 'amended':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <Edit className="mr-1 h-3 w-3" />
            Amended
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };
  
  // Load mock data
  useEffect(() => {
    setLoading(true);
    
    // Simulate API call to fetch tax statements
    setTimeout(() => {
      const mockStatements: TaxStatement[] = [
        {
          id: 1,
          employeeId: 101,
          employeeName: "Ahmed Khan",
          taxYear: 2023,
          statementType: "Form16",
          generatedDate: "2023-04-10",
          totalEarnings: 1200000,
          taxableEarnings: 950000,
          totalTaxWithheld: 85000,
          federalTax: 75000,
          stateTax: 10000,
          status: 'final',
          documentUrl: '/documents/tax/statements/2023/101_form16.pdf',
          sentToEmployee: true,
          sentDate: "2023-04-12"
        },
        {
          id: 2,
          employeeId: 102,
          employeeName: "Sara Ahmed",
          taxYear: 2023,
          statementType: "Form16",
          generatedDate: "2023-04-10",
          totalEarnings: 1800000,
          taxableEarnings: 1600000,
          totalTaxWithheld: 175000,
          federalTax: 150000,
          stateTax: 25000,
          status: 'final',
          documentUrl: '/documents/tax/statements/2023/102_form16.pdf',
          sentToEmployee: true,
          sentDate: "2023-04-12"
        },
        {
          id: 3,
          employeeId: 103,
          employeeName: "Bilal Malik",
          taxYear: 2023,
          statementType: "Form16",
          generatedDate: "2023-04-10",
          totalEarnings: 960000,
          taxableEarnings: 790000,
          totalTaxWithheld: 60000,
          federalTax: 50000,
          stateTax: 10000,
          status: 'final',
          documentUrl: '/documents/tax/statements/2023/103_form16.pdf',
          sentToEmployee: true,
          sentDate: "2023-04-12"
        },
        {
          id: 4,
          employeeId: 104,
          employeeName: "Aisha Kamran",
          taxYear: 2023,
          statementType: "Form16",
          generatedDate: "2023-04-10",
          totalEarnings: 2400000,
          taxableEarnings: 2200000,
          totalTaxWithheld: 300000,
          federalTax: 260000,
          stateTax: 40000,
          status: 'final',
          documentUrl: '/documents/tax/statements/2023/104_form16.pdf',
          sentToEmployee: true,
          sentDate: "2023-04-12"
        },
        {
          id: 5,
          employeeId: 105,
          employeeName: "Faizan Ali",
          taxYear: 2023,
          statementType: "Form16",
          generatedDate: "2023-04-15",
          totalEarnings: 3600000,
          taxableEarnings: 3100000,
          totalTaxWithheld: 550000,
          federalTax: 480000,
          stateTax: 70000,
          status: 'amended',
          documentUrl: '/documents/tax/statements/2023/105_form16_amended.pdf',
          sentToEmployee: true,
          sentDate: "2023-04-16"
        },
        {
          id: 6,
          employeeId: 106,
          employeeName: "Zara Hashmi",
          taxYear: 2023,
          statementType: "Form16",
          generatedDate: "2023-04-10",
          totalEarnings: 1080000,
          taxableEarnings: 890000,
          totalTaxWithheld: 72000,
          federalTax: 62000,
          stateTax: 10000,
          status: 'final',
          documentUrl: '/documents/tax/statements/2023/106_form16.pdf',
          sentToEmployee: true,
          sentDate: "2023-04-12"
        },
        {
          id: 7,
          employeeId: 107,
          employeeName: "Imran Khan",
          taxYear: 2023,
          statementType: "Form16",
          generatedDate: "2023-04-10",
          totalEarnings: 1500000,
          taxableEarnings: 1300000,
          totalTaxWithheld: 130000,
          federalTax: 110000,
          stateTax: 20000,
          status: 'final',
          documentUrl: '/documents/tax/statements/2023/107_form16.pdf',
          sentToEmployee: false
        },
        {
          id: 8,
          employeeId: 101,
          employeeName: "Ahmed Khan",
          taxYear: 2022,
          statementType: "Form16",
          generatedDate: "2022-04-05",
          totalEarnings: 1100000,
          taxableEarnings: 880000,
          totalTaxWithheld: 75000,
          federalTax: 65000,
          stateTax: 10000,
          status: 'final',
          documentUrl: '/documents/tax/statements/2022/101_form16.pdf',
          sentToEmployee: true,
          sentDate: "2022-04-08"
        },
        {
          id: 9,
          employeeId: 102,
          employeeName: "Sara Ahmed",
          taxYear: 2022,
          statementType: "Form16",
          generatedDate: "2022-04-05",
          totalEarnings: 1650000,
          taxableEarnings: 1480000,
          totalTaxWithheld: 156000,
          federalTax: 136000,
          stateTax: 20000,
          status: 'final',
          documentUrl: '/documents/tax/statements/2022/102_form16.pdf',
          sentToEmployee: true,
          sentDate: "2022-04-08"
        }
      ];
      
      setStatements(mockStatements);
      setLoading(false);
    }, 1000);
  }, []);
  
  // Filter statements based on selected filters
  const filteredStatements = statements.filter((statement) => {
    const yearMatch = selectedYear === 'all' || statement.taxYear.toString() === selectedYear;
    const typeMatch = selectedType === 'all' || statement.statementType === selectedType;
    const statusMatch = selectedStatus === 'all' || statement.status === selectedStatus;
    const queryMatch = searchQuery === '' || 
      statement.employeeName.toLowerCase().includes(searchQuery.toLowerCase());
    
    return yearMatch && typeMatch && statusMatch && queryMatch;
  });
  
  // Handle sending statements to employees
  const handleSendStatement = (statementId: number) => {
    if (window.confirm("Are you sure you want to send this tax statement to the employee?")) {
      setStatements(statements.map(statement => {
        if (statement.id === statementId) {
          return {
            ...statement,
            sentToEmployee: true,
            sentDate: new Date().toISOString().split('T')[0]
          };
        }
        return statement;
      }));
    }
  };
  
  // Handle bulk send statements
  const handleBulkSendStatements = () => {
    if (window.confirm("Are you sure you want to send all unsent tax statements to employees?")) {
      setStatements(statements.map(statement => {
        if (!statement.sentToEmployee) {
          return {
            ...statement,
            sentToEmployee: true,
            sentDate: new Date().toISOString().split('T')[0]
          };
        }
        return statement;
      }));
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const unsentStatementsCount = statements.filter(s => !s.sentToEmployee).length;

  return (
    <div>
      {/* Header and Actions */}
      <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Tax Statements</h3>
          <p className="text-sm text-gray-500">
            Tax documents for employees (Form16, W-2 equivalents)
          </p>
        </div>
        <div className="mt-3 sm:mt-0 flex space-x-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
            {showFilters ? <ChevronDown className="h-4 w-4 ml-1" /> : <ChevronRight className="h-4 w-4 ml-1" />}
          </button>
          
          {isAdmin && (
            <>
              <button
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none"
              >
                <FileText className="h-4 w-4 mr-2" />
                Generate Statements
              </button>
              
              {unsentStatementsCount > 0 && (
                <button
                  onClick={handleBulkSendStatements}
                  className="inline-flex items-center px-3 py-2 border border-green-500 text-sm font-medium rounded-md text-green-700 bg-green-50 hover:bg-green-100 focus:outline-none"
                >
                  <Mail className="h-4 w-4 mr-2" />
                  Send All ({unsentStatementsCount})
                </button>
              )}
            </>
          )}
        </div>
      </div>
      
      {/* Filters */}
      {showFilters && (
        <div className="mb-6 p-4 bg-gray-50 rounded-md border border-gray-200">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <label htmlFor="year-filter" className="block text-xs font-medium text-gray-700 mb-1">Tax Year</label>
              <select
                id="year-filter"
                value={selectedYear}
                onChange={(e) => setSelectedYear(e.target.value)}
                className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="all">All Years</option>
                <option value="2023">2023</option>
                <option value="2022">2022</option>
                <option value="2021">2021</option>
              </select>
            </div>
            
            <div>
              <label htmlFor="type-filter" className="block text-xs font-medium text-gray-700 mb-1">Statement Type</label>
              <select
                id="type-filter"
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="all">All Types</option>
                <option value="Form16">Form 16</option>
                <option value="W2">W-2</option>
                <option value="T4">T4</option>
                <option value="other">Other</option>
              </select>
            </div>
            
            <div>
              <label htmlFor="status-filter" className="block text-xs font-medium text-gray-700 mb-1">Status</label>
              <select
                id="status-filter"
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="all">All Statuses</option>
                <option value="draft">Draft</option>
                <option value="final">Final</option>
                <option value="amended">Amended</option>
              </select>
            </div>
            
            <div>
              <label htmlFor="search-filter" className="block text-xs font-medium text-gray-700 mb-1">Search Employee</label>
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  id="search-filter"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search by name..."
                  className="focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md"
                />
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Tax Statements Table */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Employee
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Statement Details
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tax Amounts
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredStatements.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                    No tax statements found matching the selected filters.
                  </td>
                </tr>
              ) : (
                filteredStatements.map((statement) => (
                  <tr key={statement.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center bg-blue-100 rounded-full">
                          <Users className="h-5 w-5 text-blue-600" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{statement.employeeName}</div>
                          <div className="text-sm text-gray-500">Employee ID: {statement.employeeId}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{statement.statementType} - {statement.taxYear}</div>
                      <div className="text-sm text-gray-500">
                        Generated: {formatDate(statement.generatedDate)}
                      </div>
                      {statement.sentToEmployee && statement.sentDate && (
                        <div className="text-sm text-green-600">
                          <Mail className="inline-block h-3 w-3 mr-1" />
                          Sent: {formatDate(statement.sentDate)}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        Total Tax: {formatCurrency(statement.totalTaxWithheld)}
                      </div>
                      <div className="text-sm text-gray-500">
                        Earnings: {formatCurrency(statement.totalEarnings)}
                      </div>
                      <div className="text-sm text-gray-500">
                        Taxable: {formatCurrency(statement.taxableEarnings)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(statement.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-3">
                        {statement.documentUrl && (
                          <button
                            className="text-blue-600 hover:text-blue-900"
                            title="Download Statement"
                          >
                            <Download className="h-4 w-4" />
                          </button>
                        )}
                        
                        {isAdmin && !statement.sentToEmployee && (
                          <button
                            onClick={() => handleSendStatement(statement.id)}
                            className="text-green-600 hover:text-green-900"
                            title="Send to Employee"
                          >
                            <Send className="h-4 w-4" />
                          </button>
                        )}
                        
                        <button
                          className="text-gray-600 hover:text-gray-900"
                          title="View Details"
                        >
                          <FileText className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default TaxStatements; 