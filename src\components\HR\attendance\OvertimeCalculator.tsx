import React, { useState, useEffect } from 'react';
import { 
  Clock, 
  DollarSign, 
  Calendar, 
  Bar<PERSON>hart, 
  Filter, 
  Save,
  CheckCircle,
  AlertTriangle,
  Clipboard,
  HelpCircle,
  Info,
  UserCheck
} from 'lucide-react';
import { OvertimeType, ShiftType } from '../../../types/timeAttendance';
import { PayrollCurrency } from '../../../types/payroll';
import { 
  hrPrimaryButtonStyle, 
  hrSecondaryButtonStyle, 
  hrCardStyle,
  hrErrorAlertStyle,
  hrInfoAlertStyle,
  hrSectionTitleStyle,
  hrSubsectionTitleStyle,
  hrInputStyle,
  hrTextareaStyle,
  hrActionButtonStyle
} from '../../../styles/hrWorkflow';

interface OvertimeCalculatorProps {
  employeeId?: number;
  employeeName?: string;
  baseSalary?: number;
  hourlyRate?: number;
  currency?: PayrollCurrency;
  onSave?: (overtimeData: OvertimeCalculation) => void;
  prefilledHours?: {[key in OvertimeType]?: number};
  departmentPolicies?: {
    maximumOvertimeHours: number;
    requiresApproval: boolean;
    overtimeRates: {[key in OvertimeType]?: number};
  };
}

export interface OvertimeCalculation {
  employeeId: number;
  employeeName: string;
  periodStart: string;
  periodEnd: string;
  overtimeHours: {[key in OvertimeType]?: number};
  calculations: {
    regularAmount: number;
    premiumAmount: number;
    totalAmount: number;
    currency: PayrollCurrency;
  };
  notes: string;
  status: 'draft' | 'pending_approval' | 'approved' | 'rejected';
  approverNotes?: string;
}

// Default overtime rates if not provided by department policies
const DEFAULT_OVERTIME_RATES: {[key in OvertimeType]: number} = {
  [OvertimeType.REGULAR]: 1.5,
  [OvertimeType.DOUBLE]: 2.0,
  [OvertimeType.HOLIDAY]: 2.5,
  [OvertimeType.WEEKEND]: 1.75,
  [OvertimeType.NIGHT_SHIFT]: 1.7
};

const OvertimeCalculator: React.FC<OvertimeCalculatorProps> = ({
  employeeId = 0,
  employeeName = '',
  baseSalary = 0,
  hourlyRate = 0,
  currency = PayrollCurrency.USD,
  onSave,
  prefilledHours = {},
  departmentPolicies = {
    maximumOvertimeHours: 40,
    requiresApproval: true,
    overtimeRates: DEFAULT_OVERTIME_RATES
  }
}) => {
  const [periodStart, setPeriodStart] = useState<string>(
    new Date().toISOString().split('T')[0]
  );
  const [periodEnd, setPeriodEnd] = useState<string>(
    new Date(new Date().setDate(new Date().getDate() + 6)).toISOString().split('T')[0]
  );
  const [overtimeHours, setOvertimeHours] = useState<{[key in OvertimeType]?: number}>({
    [OvertimeType.REGULAR]: 0,
    [OvertimeType.DOUBLE]: 0,
    [OvertimeType.HOLIDAY]: 0,
    [OvertimeType.WEEKEND]: 0,
    [OvertimeType.NIGHT_SHIFT]: 0,
    ...prefilledHours
  });
  const [calculatedAmounts, setCalculatedAmounts] = useState({
    regularAmount: 0,
    premiumAmount: 0,
    totalAmount: 0
  });
  const [notes, setNotes] = useState<string>('');
  const [errors, setErrors] = useState<string[]>([]);
  const [showRatesInfo, setShowRatesInfo] = useState<boolean>(false);

  // Calculate effective hourly rate if not provided
  const effectiveHourlyRate = hourlyRate > 0 ? hourlyRate : (baseSalary / 2080); // 2080 = 40 hours/week * 52 weeks
  
  // Format currency values
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  // Get overtime rates (from department policies or defaults)
  const getOvertimeRates = () => {
    return {
      ...DEFAULT_OVERTIME_RATES,
      ...departmentPolicies.overtimeRates
    };
  };

  // Calculate overtime amounts
  const calculateOvertimeAmounts = () => {
    const rates = getOvertimeRates();
    let regularAmount = 0;
    let premiumAmount = 0;
    
    // Calculate for each overtime type
    Object.entries(overtimeHours).forEach(([type, hours]) => {
      if (hours && hours > 0) {
        const rate = rates[type as OvertimeType] || 1.5;
        const baseAmount = hours * effectiveHourlyRate;
        regularAmount += baseAmount;
        premiumAmount += baseAmount * (rate - 1); // Just the premium portion
      }
    });
    
    const totalAmount = regularAmount + premiumAmount;
    
    setCalculatedAmounts({
      regularAmount,
      premiumAmount,
      totalAmount
    });
  };

  // Validate overtime hours against policies
  const validateOvertimeHours = () => {
    const newErrors: string[] = [];
    const totalHours = Object.values(overtimeHours).reduce((sum, hours) => sum + (hours || 0), 0);
    
    if (totalHours === 0) {
      newErrors.push('At least one type of overtime hours must be entered');
    }
    
    if (totalHours > departmentPolicies.maximumOvertimeHours) {
      newErrors.push(`Total overtime hours (${totalHours}) exceed the maximum allowed (${departmentPolicies.maximumOvertimeHours})`);
    }
    
    setErrors(newErrors);
    return newErrors.length === 0;
  };

  // Update calculations when overtime hours change
  useEffect(() => {
    calculateOvertimeAmounts();
  }, [overtimeHours, effectiveHourlyRate]);

  // Handle changes to overtime hours
  const handleOvertimeHoursChange = (type: OvertimeType, value: string) => {
    const hours = parseFloat(value);
    
    setOvertimeHours(prev => ({
      ...prev,
      [type]: isNaN(hours) ? 0 : hours
    }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateOvertimeHours()) {
      return;
    }
    
    if (onSave) {
      const overtimeData: OvertimeCalculation = {
        employeeId,
        employeeName,
        periodStart,
        periodEnd,
        overtimeHours,
        calculations: {
          regularAmount: calculatedAmounts.regularAmount,
          premiumAmount: calculatedAmounts.premiumAmount,
          totalAmount: calculatedAmounts.totalAmount,
          currency
        },
        notes,
        status: departmentPolicies.requiresApproval ? 'pending_approval' : 'approved'
      };
      
      onSave(overtimeData);
    }
  };

  return (
    <div className={hrCardStyle}>
      <div className="flex items-center justify-between mb-6">
        <h2 className={hrSectionTitleStyle}>
          <Clock className="h-6 w-6 mr-2 text-blue-500" />
          Advanced Overtime Calculator
        </h2>
        
        <button
          type="button"
          className={hrActionButtonStyle}
          onClick={() => setShowRatesInfo(!showRatesInfo)}
          aria-label="Show rate information"
        >
          <HelpCircle className="h-5 w-5" />
        </button>
      </div>
      
      {showRatesInfo && (
        <div className={hrInfoAlertStyle}>
          <div className="flex items-start mb-2">
            <Info className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
            <h3 className="font-medium text-blue-800">Overtime Rate Information</h3>
          </div>
          <div className="text-sm text-blue-800 ml-7 space-y-1">
            <p><strong>Regular Overtime (1.5x):</strong> Applies to hours worked beyond regular schedule</p>
            <p><strong>Double Time (2.0x):</strong> Typically for excessive overtime hours</p>
            <p><strong>Holiday (2.5x):</strong> Work performed on company holidays</p>
            <p><strong>Weekend (1.75x):</strong> Work performed on weekends</p>
            <p><strong>Night Shift (1.7x):</strong> Extra compensation for overnight hours</p>
          </div>
        </div>
      )}
      
      {employeeId && employeeName && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="font-medium text-gray-700 mb-2">Employee Information</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">Name</p>
              <p className="font-medium">{employeeName}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Hourly Rate</p>
              <p className="font-medium">{formatCurrency(effectiveHourlyRate)}</p>
            </div>
          </div>
        </div>
      )}
      
      <form onSubmit={handleSubmit}>
        {/* Time period selection */}
        <div className="mb-6">
          <h3 className={hrSubsectionTitleStyle}>
            <Calendar className="h-5 w-5 mr-2 text-blue-500" />
            Overtime Period
          </h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="periodStart" className="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <input
                type="date"
                id="periodStart"
                value={periodStart}
                onChange={(e) => setPeriodStart(e.target.value)}
                className={hrInputStyle}
                required
              />
            </div>
            <div>
              <label htmlFor="periodEnd" className="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <input
                type="date"
                id="periodEnd"
                value={periodEnd}
                onChange={(e) => setPeriodEnd(e.target.value)}
                className={hrInputStyle}
                min={periodStart}
                required
              />
            </div>
          </div>
        </div>
        
        {/* Overtime hours inputs */}
        <div className="mb-6">
          <h3 className={hrSubsectionTitleStyle}>
            <Clock className="h-5 w-5 mr-2 text-blue-500" />
            Overtime Hours
          </h3>
          
          <div className="space-y-4">
            {Object.entries(getOvertimeRates()).map(([type, rate]) => (
              <div key={type} className="flex items-center">
                <div className="w-48">
                  <label htmlFor={`overtime-${type}`} className="block text-sm font-medium text-gray-700">
                    {type.replace('_', ' ').split('_').map(word => 
                      word.charAt(0) + word.slice(1).toLowerCase()
                    ).join(' ')}
                  </label>
                </div>
                <div className="flex-1 flex items-center">
                  <input
                    type="number"
                    id={`overtime-${type}`}
                    value={overtimeHours[type as OvertimeType] || ''}
                    onChange={(e) => handleOvertimeHoursChange(type as OvertimeType, e.target.value)}
                    min="0"
                    step="0.5"
                    className={hrInputStyle}
                    placeholder="0.0"
                  />
                  <span className="ml-2 text-gray-500 text-sm whitespace-nowrap">
                    hours @ {rate}x
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Notes */}
        <div className="mb-6">
          <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
            Notes / Justification
          </label>
          <textarea
            id="notes"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            rows={3}
            className={hrTextareaStyle}
            placeholder="Explain reason for overtime..."
          />
        </div>
        
        {/* Calculation results */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h3 className="font-medium text-gray-700 mb-3 flex items-center">
            <DollarSign className="h-5 w-5 mr-2 text-green-500" />
            Calculation Summary
          </h3>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Regular Pay:</span>
              <span className="font-medium">{formatCurrency(calculatedAmounts.regularAmount)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Premium Pay:</span>
              <span className="font-medium">{formatCurrency(calculatedAmounts.premiumAmount)}</span>
            </div>
            <div className="border-t border-gray-200 pt-2 mt-2 flex justify-between">
              <span className="font-medium">Total Overtime Pay:</span>
              <span className="font-bold text-blue-600">{formatCurrency(calculatedAmounts.totalAmount)}</span>
            </div>
          </div>
        </div>
        
        {/* Error messages */}
        {errors.length > 0 && (
          <div className={hrErrorAlertStyle}>
            <div className="flex items-start mb-1">
              <AlertTriangle className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" />
              <span className="font-medium text-red-800">Please fix the following issues:</span>
            </div>
            <ul className="list-disc ml-10 text-sm text-red-700">
              {errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </div>
        )}
        
        {/* Form actions */}
        <div className="flex justify-end space-x-3">
          {errors.length === 0 && (
            <button
              type="button"
              className={hrSecondaryButtonStyle}
              onClick={() => {
                const clipboardData = `Overtime calculation for ${employeeName || `Employee #${employeeId}`}:\n` +
                  `Period: ${periodStart} to ${periodEnd}\n` +
                  `Total amount: ${formatCurrency(calculatedAmounts.totalAmount)}`;
                
                navigator.clipboard.writeText(clipboardData);
                alert('Overtime calculation copied to clipboard!');
              }}
            >
              <Clipboard className="h-4 w-4 mr-2" />
              Copy Details
            </button>
          )}
          
          <button
            type="submit"
            className={hrPrimaryButtonStyle}
          >
            <Save className="h-4 w-4 mr-2" />
            {departmentPolicies.requiresApproval ? 'Submit for Approval' : 'Save Calculation'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default OvertimeCalculator; 