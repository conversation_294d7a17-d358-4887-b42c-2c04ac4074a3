import React, { useState, useEffect } from 'react';
import { useN<PERSON><PERSON>, Link } from 'react-router-dom';
import axios from 'axios';
import { KnowledgeStatus } from '../entities/KnowledgeBase';
import { UserRole } from '../types/common';
import { BookOpen, Search, Filter, Tag, Star, Clock, PlusCircle, Bookmark, TrendingUp, FileText, Eye, User, ChevronRight, HardDrive, Layers, Server, Shield, Database, Smartphone, Monitor, Settings, Cpu, Calendar } from 'lucide-react';
import { toast } from 'react-hot-toast';
import KnowledgeBaseHeader from './KnowledgeBaseHeader';
import { useAuth } from '../contexts/AuthContext';
import api from '../services/api';
import KnowledgeArticleEditor from './KnowledgeArticleEditor';

interface KnowledgeArticle {
  id: string;
  title: string;
  summary?: string;
  content: string;
  status: KnowledgeStatus;
  viewCount: number;
  isFeatured: boolean;
  category: {
    id: string;
    name: string;
    slug: string;
  };
  createdBy: {
    id: string;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
  tags: Array<{
    id: string;
    name: string;
  }>;
}

interface KnowledgeCategory {
  id: string;
  name: string;
  description?: string;
  slug: string;
  displayOrder: number;
  parentId?: string;
}

// Add a helper function to extract the first image URL from content
const extractFirstImageUrl = (content: string): string | null => {
  const imgRegex = /<img[^>]+src="([^">]+)"/;
  const match = content.match(imgRegex);
  return match ? match[1] : null;
};

// Add this helper function near the top of the file
const stripHtmlTags = (html: string): string => {
  const tmp = document.createElement('div');
  tmp.innerHTML = html;
  return tmp.textContent || tmp.innerText || '';
};

const KnowledgeBasePage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [articles, setArticles] = useState<KnowledgeArticle[]>([]);
  const [categories, setCategories] = useState<KnowledgeCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [featuredArticles, setFeaturedArticles] = useState<KnowledgeArticle[]>([]);
  const [popularArticles, setPopularArticles] = useState<KnowledgeArticle[]>([]);
  const [showEditor, setShowEditor] = useState<boolean>(false);

  // Add hardcoded categories since database was reset
  const predefinedCategories = [
    { id: "cat1", name: "Getting Started", slug: "getting-started", description: "", displayOrder: 1 },
    { id: "cat2", name: "My Account & Settings", slug: "account-settings", description: "", displayOrder: 2 },
    { id: "cat3", name: "Company Profile", slug: "company-profile", description: "", displayOrder: 3 },
    { id: "cat4", name: "Standard Operating Procedures (SOPs)", slug: "sops", description: "", displayOrder: 4 },
    { id: "cat6", name: "Internet & Network Access", slug: "internet-network", description: "", displayOrder: 5 },
    { id: "cat7", name: "Software Usage", slug: "software-usage", description: "", displayOrder: 6 },
    { id: "cat11", name: "Email & Communication Tools", slug: "email-communication", description: "", displayOrder: 7 },
    { id: "cat10", name: "IT Support & Ticketing", slug: "it-support", description: "", displayOrder: 8 },
    { id: "cat13", name: "HR Policies & Employee Handbook", slug: "hr-policies", description: "", displayOrder: 9 },
    { id: "cat14", name: "FAQs & Troubleshooting", slug: "faqs", description: "", displayOrder: 10 }
  ];

  // Fetch articles and categories on component mount
  useEffect(() => {
    let isMounted = true; // Flag to prevent state updates if component unmounts
    
    const fetchData = async () => {
      if (!isMounted) return;
      
      setIsLoading(true);
      try {

        
        // Get articles and categories with proper error handling for each request
        let articlesData = [];
        let categoriesData = [];
        
        try {
          const articlesResponse = await api.get('/knowledge-base/articles');
          articlesData = articlesResponse.data || [];
        } catch (err: any) {
          console.error('Error fetching articles:', err);
          
          if (isMounted) {
            toast.error('Could not load articles. Please try again later.');
          }
        }
        
        try {
          const categoriesResponse = await api.get('/knowledge-base/categories');
          categoriesData = categoriesResponse.data || [];
        } catch (err) {
          console.error('Error fetching categories:', err);
          if (isMounted) {
            toast.error('Could not load categories. Using predefined categories instead.');
          }
          // Use predefined categories as a fallback
          categoriesData = predefinedCategories;
        }
        
        if (!isMounted) return;
        
        setArticles(articlesData);
        
        // Extract featured articles
        const featured = articlesData.filter((article: KnowledgeArticle) => article.isFeatured);
        setFeaturedArticles(featured);
        
        // Extract popular articles (based on view count)
        const popular = [...articlesData]
          .filter((article: KnowledgeArticle) => article.status === KnowledgeStatus.PUBLISHED)
          .sort((a, b) => b.viewCount - a.viewCount)
          .slice(0, 4);
        setPopularArticles(popular);
        
        // Sort categories if we have any
        if (categoriesData.length > 0) {
          // Combine fetched categories with predefined ones to ensure all are available
          // Create a map of existing category IDs and names to avoid duplicates
          const existingCategoryIds = new Set(categoriesData.map((cat: KnowledgeCategory) => cat.id));
          const existingCategoryNames = new Set(categoriesData.map((cat: KnowledgeCategory) => cat.name.toLowerCase()));
          
          // Add predefined categories that don't already exist (check both ID and name)
          const combinedCategories = [...categoriesData];
          for (const predefinedCat of predefinedCategories) {
            if (!existingCategoryIds.has(predefinedCat.id) && 
                !existingCategoryNames.has(predefinedCat.name.toLowerCase())) {
              combinedCategories.push(predefinedCat);
            }
          }
          
          // Remove any potential duplicates by creating a Map with unique IDs
          const uniqueCategories = new Map();
          combinedCategories.forEach(cat => {
            if (!uniqueCategories.has(cat.id)) {
              uniqueCategories.set(cat.id, cat);
            }
          });
          
          const sortedCategories = Array.from(uniqueCategories.values()).sort((a, b) => {
            // Move Technology Knowledge to the specified position
            if (a.name === 'Technology Knowledge') return -1;
            if (b.name === 'Technology Knowledge') return 1;
            // For other categories, maintain their original order
            return a.displayOrder - b.displayOrder;
          });
          
          setCategories(sortedCategories);
        } else {
          // Use predefined categories if no categories were fetched
          setCategories(predefinedCategories);
        }
        
        setError(null);
      } catch (err: any) {
        console.error('Error fetching knowledge base data:', err);
        if (!isMounted) return;
        
        if (err.response?.status === 401) {
          toast.error('Please log in to view the knowledge base');
          navigate('/login');
        } else {
          setError('Failed to load knowledge base data. Please try again later.');
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchData();
    
    // Cleanup function to prevent state updates if component unmounts
    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array to run only once on mount

  // Filter articles based on search query and selected category
  const filteredArticles = articles.filter(article => {
    const matchesSearch = searchQuery === '' || 
      article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (article.summary && article.summary.toLowerCase().includes(searchQuery.toLowerCase())) ||
      article.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      article.tags.some(tag => tag.name.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || article.category.id === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Handle article click
  const handleArticleClick = async (articleId: string) => {
    if (!user) {
      toast.error('Please log in to view articles');
      navigate('/login');
      return;
    }

    try {
      // Check if the article exists (optional validation)
      await api.get(`/knowledge-base/articles/${articleId}`);
      
      // Construct the correct URL based on current location
      const articlePath = window.location.pathname.includes('/service-desk') 
        ? `/service-desk/knowledge/articles/${articleId}`
        : `/knowledge/articles/${articleId}`;
      
      // Use React Router's navigate
      navigate(articlePath);
      
    } catch (error: any) {
      if (error.response?.status === 401) {
        toast.error('Please log in to view articles');
        navigate('/login');
      } else {
        console.error('Error fetching article:', error);
        toast.error('Error fetching article. Please try again.');
      }
    }
  };

  // Handle category change
  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value);
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Handle search form submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // The filtering happens automatically via the filteredArticles computed value
  };

  // Handle create article button click
  const handleCreateArticle = () => {
    if (user?.role === UserRole.IT_ADMIN || user?.role === UserRole.IT_STAFF) {
      // Use React Router's navigate for create
      const createPath = window.location.pathname.includes('/service-desk')
        ? '/service-desk/knowledge/create'
        : '/knowledge/create';
      navigate(createPath);
    } else {
      toast.error('You do not have permission to create knowledge base articles');
    }
  };

  // Show the editor if the create button was clicked
  useEffect(() => {
    // Listen for a custom event to close the editor
    const handleEditorClose = () => {
      setShowEditor(false);
    };
    
    // Add event listener
    window.addEventListener('close-knowledge-editor', handleEditorClose);
    
    // Clean up
    return () => {
      window.removeEventListener('close-knowledge-editor', handleEditorClose);
    };
  }, []);

  // Debounce search to prevent excessive API calls
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      // Only trigger search if there's actually a search query
      if (searchQuery.trim()) {
        // Search logic can be added here if needed
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  if (showEditor) {
    return <KnowledgeArticleEditor />;
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* Header with search */}
      <div className="bg-white border-b border-gray-200 py-6">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <BookOpen className="h-6 w-6 text-blue-600 mr-2" />
              Enterprise Knowledge Hub
            </h1>
            
            <div className="w-full md:w-1/2 lg:w-1/3">
              <form onSubmit={handleSearchSubmit} className="relative">
                <input
                  type="text"
                  placeholder="Search resources..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pl-10"
                />
                <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
              </form>
            </div>
            
            <div className="flex space-x-4">
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                All Resources
              </button>
              <button className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                Featured
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Categories section */}
      <div className="bg-gray-50 py-4 border-b border-gray-200">
        <div className="container mx-auto px-4">
          <div className="flex items-center flex-wrap gap-2">
            <div className="flex items-center mr-3">
              <Tag className="h-5 w-5 text-gray-700 mr-2" />
              <h2 className="text-lg font-semibold text-gray-900">Categories</h2>
            </div>
            
            <button 
              className={`px-3 py-1.5 rounded-lg text-sm font-medium ${selectedCategory === 'all' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'}`}
              onClick={() => setSelectedCategory('all')}
            >
              All
            </button>
            
            {categories.map(category => (
              <button 
                key={category.id}
                className={`px-3 py-1.5 rounded-lg text-sm font-medium ${selectedCategory === category.id ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'}`}
                onClick={() => setSelectedCategory(category.id)}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>
      </div>
      
      {/* Main content */}
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          {filteredArticles.length > 0 ? (
            filteredArticles.map(article => (
              <div 
                key={article.id}
                className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow cursor-pointer flex flex-col md:flex-row"
              >
                {/* Create an actual anchor tag that wraps the entire article card */}
                <Link 
                  to={window.location.pathname.includes('/service-desk') 
                    ? `/service-desk/knowledge/articles/${article.id}` 
                    : `/knowledge/articles/${article.id}`}
                  className="flex flex-col md:flex-row w-full"
                  onClick={(e) => {
                    // Only prevent default and use the handler if there's a need for custom logic
                    // Otherwise let React Router handle the navigation
                    if (!user) {
                      e.preventDefault();
                      handleArticleClick(article.id);
                    }
                  }}
                >
                  {/* Article image (left side) */}
                  <div className="relative md:w-80 h-56 md:h-auto bg-gradient-to-r from-blue-500 to-indigo-600 flex-shrink-0">
                    {extractFirstImageUrl(article.content) ? (
                      <img
                        src={extractFirstImageUrl(article.content)!}
                        alt={article.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <FileText className="h-12 w-12 text-white/50" />
                      </div>
                    )}
                    {article.isFeatured && (
                      <div className="absolute top-3 left-3">
                        <span className="bg-amber-500 text-white px-2 py-1 rounded-md text-xs font-medium">
                          Featured
                        </span>
                      </div>
                    )}
                  </div>
                  
                  {/* Article content (right side) */}
                  <div className="p-6 flex-grow">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{article.title}</h3>
                    <p className="text-gray-600 mb-4 line-clamp-2">{
                      article.summary || stripHtmlTags(article.content).substring(0, 150) + '...'
                    }</p>
                    
                    <div className="flex flex-wrap items-center text-sm text-gray-500 mt-auto">
                      <div className="flex items-center mr-4">
                        <Calendar className="h-4 w-4 mr-1.5" />
                        <span>{new Date(article.createdAt).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center mr-4">
                        <Eye className="h-4 w-4 mr-1.5" />
                        <span>{article.viewCount} views</span>
                      </div>
                      <div className="flex items-center">
                        <Tag className="h-4 w-4 mr-1.5" />
                        <span>{article.category.name}</span>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            ))
          ) : (
            <div className="bg-white border border-gray-200 rounded-lg p-8 text-center">
              <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-medium text-gray-900 mb-2">No records found</h3>
              <p className="text-gray-600">
                {searchQuery ? 
                  `No articles match your search "${searchQuery}"${selectedCategory === 'all' ? '' : ` in category "${selectedCategory}"`}.` : 
                  'There are no articles available in this category.'}
              </p>
            </div>
          )}
        </div>
      </div>
      
      {/* Conditionally render create button based on user role */}
      {user && (user.role === UserRole.IT_ADMIN || user.role === UserRole.IT_STAFF) && (
        <>
          <button
            onClick={handleCreateArticle}
            className="fixed bottom-6 right-6 bg-blue-600 text-white rounded-full p-4 shadow-lg hover:bg-blue-700 transition-colors duration-200"
            title="Create New Article"
          >
            <PlusCircle className="w-6 h-6" />
          </button>
          
          {/* Test direct link */}
          <button 
            onClick={() => {
              const createPath = window.location.pathname.includes('/service-desk')
                ? '/service-desk/knowledge/create'
                : '/knowledge/create';
              navigate(createPath);
            }}
            className="fixed bottom-6 left-20 bg-green-600 text-white rounded-full p-4 shadow-lg hover:bg-green-700 transition-colors duration-200"
            title="Test Direct Link"
          >
            <PlusCircle className="w-6 h-6" />
          </button>
        </>
      )}
    </div>
  );
};

export default KnowledgeBasePage;