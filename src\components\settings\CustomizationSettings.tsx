import React, { useState, useEffect } from 'react';
import { Palette, Save, AlertCircle, Eye, Monitor, Smartphone } from 'lucide-react';

interface Setting {
  id: string;
  category: string;
  key: string;
  value: string;
  description: string;
  lastUpdated?: string;
  updatedBy?: string;
}

interface CustomizationSettingsProps {
  settings: Setting[];
  onSettingChange: (id: string, value: string) => void;
  onSave: () => void;
  isSaving?: boolean;
  saveSuccess?: boolean;
}

const CustomizationSettings: React.FC<CustomizationSettingsProps> = ({
  settings,
  onSettingChange,
  onSave,
  isSaving = false,
  saveSuccess = false
}) => {
  const [localSettings, setLocalSettings] = useState<Setting[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);

  useEffect(() => {
    const customizationSettings = settings.filter(setting => setting.category === 'customization');
    setLocalSettings(customizationSettings);
  }, [settings]);

  const handleChange = (id: string, value: string) => {
    setLocalSettings(prev => prev.map(setting => 
      setting.id === id ? { ...setting, value } : setting
    ));
    setHasChanges(true);
    onSettingChange(id, value);
  };

  const handleSave = () => {
    onSave();
    setHasChanges(false);
  };

  const isColorField = (key: string) => {
    return key.toLowerCase().includes('color') || key.toLowerCase().includes('theme');
  };

  const isSelectField = (key: string) => {
    return key === 'defaultTheme' || key === 'dateFormat' || key === 'timeFormat';
  };

  const getThemeOptions = () => [
    { value: 'light', label: 'Light Theme' },
    { value: 'dark', label: 'Dark Theme' },
    { value: 'auto', label: 'Auto (System)' },
    { value: 'custom', label: 'Custom Theme' }
  ];

  const getDateFormatOptions = () => [
    { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
    { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
    { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' },
    { value: 'DD MMM YYYY', label: 'DD MMM YYYY' }
  ];

  const getTimeFormatOptions = () => [
    { value: '12', label: '12 Hour (AM/PM)' },
    { value: '24', label: '24 Hour' }
  ];

  const getCurrentTheme = () => {
    return localSettings.find(s => s.key === 'defaultTheme')?.value || 'light';
  };

  const getPrimaryColor = () => {
    return localSettings.find(s => s.key === 'primaryColor')?.value || '#3B82F6';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Palette className="h-6 w-6 text-pink-600" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Customization Settings</h2>
            <p className="text-sm text-gray-600">Configure themes, colors, and visual preferences</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => setPreviewMode(!previewMode)}
            className="flex items-center gap-2 px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors"
          >
            <Eye className="h-4 w-4" />
            {previewMode ? 'Exit Preview' : 'Preview Changes'}
          </button>
          {hasChanges && (
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isSaving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save className="h-4 w-4" />
              )}
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          )}
        </div>
      </div>

      {saveSuccess && (
        <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700">
          <AlertCircle className="h-4 w-4" />
          Customization settings saved successfully!
        </div>
      )}

      {previewMode && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Eye className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-900">Preview Mode Active</span>
          </div>
          <p className="text-sm text-blue-700">
            You are currently previewing your customization changes. Changes will be applied when you save.
          </p>
        </div>
      )}

      {/* Theme Preview */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center gap-3 mb-4">
          <Monitor className="h-5 w-5 text-pink-600" />
          <h3 className="text-lg font-medium text-gray-900">Theme Preview</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className={`rounded-lg p-4 border-2 ${
            getCurrentTheme() === 'light' ? 'border-pink-500 bg-white' : 'border-gray-200 bg-white'
          }`}>
            <div className="flex items-center gap-2 mb-2">
              <div className="w-3 h-3 rounded-full bg-gray-300"></div>
              <div className="text-sm font-medium text-gray-900">Light Theme</div>
            </div>
            <div className="space-y-2">
              <div className="h-2 bg-gray-200 rounded"></div>
              <div className="h-2 bg-gray-100 rounded w-3/4"></div>
            </div>
          </div>
          <div className={`rounded-lg p-4 border-2 ${
            getCurrentTheme() === 'dark' ? 'border-pink-500 bg-gray-800' : 'border-gray-200 bg-gray-800'
          }`}>
            <div className="flex items-center gap-2 mb-2">
              <div className="w-3 h-3 rounded-full bg-gray-600"></div>
              <div className="text-sm font-medium text-white">Dark Theme</div>
            </div>
            <div className="space-y-2">
              <div className="h-2 bg-gray-600 rounded"></div>
              <div className="h-2 bg-gray-700 rounded w-3/4"></div>
            </div>
          </div>
          <div className={`rounded-lg p-4 border-2 ${
            getCurrentTheme() === 'auto' ? 'border-pink-500 bg-gradient-to-r from-white to-gray-800' : 'border-gray-200 bg-gradient-to-r from-white to-gray-800'
          }`}>
            <div className="flex items-center gap-2 mb-2">
              <div className="w-3 h-3 rounded-full bg-gradient-to-r from-gray-300 to-gray-600"></div>
              <div className="text-sm font-medium text-gray-900">Auto Theme</div>
            </div>
            <div className="space-y-2">
              <div className="h-2 bg-gradient-to-r from-gray-200 to-gray-600 rounded"></div>
              <div className="h-2 bg-gradient-to-r from-gray-100 to-gray-700 rounded w-3/4"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Color Palette */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center gap-3 mb-4">
          <Palette className="h-5 w-5 text-pink-600" />
          <h3 className="text-lg font-medium text-gray-900">Color Palette</h3>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div 
              className="w-16 h-16 rounded-lg mx-auto mb-2 border border-gray-200"
              style={{ backgroundColor: getPrimaryColor() }}
            ></div>
            <div className="text-sm font-medium text-gray-900">Primary</div>
            <div className="text-xs text-gray-600">{getPrimaryColor()}</div>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 rounded-lg mx-auto mb-2 border border-gray-200 bg-gray-600"></div>
            <div className="text-sm font-medium text-gray-900">Secondary</div>
            <div className="text-xs text-gray-600">#6B7280</div>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 rounded-lg mx-auto mb-2 border border-gray-200 bg-green-500"></div>
            <div className="text-sm font-medium text-gray-900">Success</div>
            <div className="text-xs text-gray-600">#10B981</div>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 rounded-lg mx-auto mb-2 border border-gray-200 bg-red-500"></div>
            <div className="text-sm font-medium text-gray-900">Error</div>
            <div className="text-xs text-gray-600">#EF4444</div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 divide-y divide-gray-200">
        {localSettings.map((setting) => (
          <div key={setting.id} className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-900 mb-1">
                  {setting.key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </label>
                <p className="text-sm text-gray-600 mb-3">{setting.description}</p>
                
                {setting.key.includes('enable') || setting.key.includes('Enable') ? (
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={setting.id}
                      checked={setting.value === 'true'}
                      onChange={(e) => handleChange(setting.id, e.target.checked ? 'true' : 'false')}
                      className="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded"
                    />
                    <label htmlFor={setting.id} className="ml-2 text-sm text-gray-700">
                      {setting.value === 'true' ? 'Enabled' : 'Disabled'}
                    </label>
                  </div>
                ) : isColorField(setting.key) && setting.key.includes('Color') ? (
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      value={setting.value}
                      onChange={(e) => handleChange(setting.id, e.target.value)}
                      className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                    />
                    <input
                      type="text"
                      value={setting.value}
                      onChange={(e) => handleChange(setting.id, e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                      placeholder="#000000"
                    />
                  </div>
                ) : isSelectField(setting.key) ? (
                  <select
                    value={setting.value}
                    onChange={(e) => handleChange(setting.id, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  >
                    {setting.key === 'defaultTheme' 
                      ? getThemeOptions().map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))
                      : setting.key === 'dateFormat'
                      ? getDateFormatOptions().map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))
                      : getTimeFormatOptions().map(option => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))
                    }
                  </select>
                ) : (
                  <input
                    type="text"
                    value={setting.value}
                    onChange={(e) => handleChange(setting.id, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                    placeholder={`Enter ${setting.key}`}
                  />
                )}
              </div>
            </div>
            
            {setting.lastUpdated && (
              <div className="mt-3 text-xs text-gray-500">
                Last updated: {new Date(setting.lastUpdated).toLocaleString()} by {setting.updatedBy}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default CustomizationSettings;