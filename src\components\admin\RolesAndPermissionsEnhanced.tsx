// Enhanced version of your existing RolesAndPermissions component
// This shows how to add dynamic features with minimal changes

import React, { useState, useEffect } from 'react';
import { Shield, Plus, Edit, Trash2, Lock, User, CheckSquare, AlertCircle, Users, Copy, Search, ChevronDown, CheckCircle, Save, Download, UploadCloud, FileText, Star, X, Monitor, Clock, BarChart2, SearchX, Settings, Zap } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import api from '../../services/api';
import { toast } from 'react-hot-toast';
import { Toaster } from 'react-hot-toast';
import { useDynamicRoles } from '../../hooks/useDynamicRoles';
import { EnhancedRoleForm } from './EnhancedRoleForm';

// Your existing interfaces (keep them as they are)
interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
  critical?: boolean;
  impacts?: string[];
}

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  parentId?: string;
  category: string;
  dashboardAccess?: string[];
  userCount: number;
  createdAt: string;
  // NEW: Dynamic features
  isDynamic?: boolean;
  hasRules?: boolean;
  dynamicId?: string;
}

export const RolesAndPermissionsEnhanced = () => {
  const { user } = useAuth();
  
  // Enhanced role management with backward compatibility
  const {
    roles,
    permissions: dynamicPermissions,
    loading: isLoadingRoles,
    error: roleError,
    dynamicFeatures,
    createRole: createRoleEnhanced,
    updateRole: updateRoleEnhanced,
    deleteRole: deleteRoleEnhanced,
    cloneRole: cloneRoleEnhanced,
    migrateToDynamic,
    canUseDynamicFeatures
  } = useDynamicRoles();

  // Your existing state (keep as is)
  const [activeTab, setActiveTab] = useState<'roles' | 'users' | 'groups' | 'jit' | 'analytics'>('roles');
  const [activeView, setActiveView] = useState<'list' | 'hierarchy' | 'impact'>('list');
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [showRoleForm, setShowRoleForm] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showEnhancedForm, setShowEnhancedForm] = useState(false);

  // Initialize permissions (your existing logic)
  useEffect(() => {
    const systemPermissions: Permission[] = [
      {
        id: 'canCreateTickets',
        name: 'Create Tickets',
        description: 'Can create new tickets',
        category: 'Tickets',
        critical: false
      },
      {
        id: 'canEditTickets',
        name: 'Edit Tickets',
        description: 'Can edit existing tickets',
        category: 'Tickets',
        critical: false
      },
      {
        id: 'canDeleteTickets',
        name: 'Delete Tickets',
        description: 'Can delete tickets',
        category: 'Tickets',
        critical: true,
        impacts: ['Data Loss', 'Audit Trail']
      },
      // ... your existing permissions
    ];
    
    setPermissions(systemPermissions);
  }, []);

  // Your existing handlers (keep as is, but optionally enhance)
  const handleAddRole = async (role: Omit<Role, 'id' | 'createdAt'>) => {
    try {
      // Use enhanced creation if dynamic features enabled
      if (canUseDynamicFeatures && role.isDynamic) {
        await createRoleEnhanced(role);
      } else {
        // Your existing logic
        const response = await api.post('/roles', {
          ...role,
          category: role.category || 'system',
          dashboardAccess: role.dashboardAccess || []
        });
        
        if (response.status === 201 && response.data) {
          toast.success('Role created successfully');
        }
      }
      setShowRoleForm(false);
      setShowEnhancedForm(false);
    } catch (error) {
      console.error('Error creating role:', error);
      toast.error('Failed to create role. Please try again.');
    }
  };

  const handleUpdateRole = async (updatedRole: Role) => {
    try {
      if (canUseDynamicFeatures && updatedRole.isDynamic) {
        await updateRoleEnhanced(updatedRole.id, updatedRole);
      } else {
        // Your existing logic
        const response = await api.put(`/roles/${updatedRole.id}`, updatedRole);
        if (response.status === 200 || response.status === 204) {
          toast.success('Role updated successfully');
        }
      }
      setSelectedRole(null);
      setShowRoleForm(false);
      setShowEnhancedForm(false);
    } catch (error) {
      console.error('Error updating role:', error);
      toast.error('Failed to update role. Please try again.');
    }
  };

  const handleDeleteRole = async (roleId: string) => {
    try {
      if (canUseDynamicFeatures) {
        await deleteRoleEnhanced(roleId);
      } else {
        // Your existing logic
        await api.delete(`/roles/${roleId}`);
        toast.success('Role deleted successfully');
      }
      setShowDeleteConfirm(false);
      setSelectedRole(null);
    } catch (error) {
      console.error('Error deleting role:', error);
      toast.error('Failed to delete role. Please try again.');
    }
  };

  // NEW: Enhanced clone with dynamic features
  const handleCloneRole = async (sourceRole: Role) => {
    try {
      if (canUseDynamicFeatures) {
        await cloneRoleEnhanced(sourceRole.id, `${sourceRole.name} (Copy)`);
      } else {
        // Your existing clone logic
        const newRole: Role = {
          ...sourceRole,
          id: `role-${Date.now()}`,
          name: `${sourceRole.name} (Copy)`,
          createdAt: new Date().toISOString(),
          userCount: 0
        };
        await handleAddRole(newRole);
      }
    } catch (error) {
      console.error('Error cloning role:', error);
      toast.error('Failed to clone role');
    }
  };

  // Group permissions by category (your existing logic)
  const permissionsByCategory = permissions.reduce<Record<string, Permission[]>>((acc, permission) => {
    if (!acc[permission.category]) {
      acc[permission.category] = [];
    }
    acc[permission.category].push(permission);
    return acc;
  }, {});

  return (
    <div className="p-6 bg-white">
      <Toaster position="top-right" />
      
      {/* Header with dynamic features indicator */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Roles & Permissions</h2>
          <p className="text-gray-600">Manage user roles and permissions</p>
        </div>
        
        {/* NEW: Dynamic features status */}
        {canUseDynamicFeatures && (
          <div className="flex items-center gap-2">
            <Zap className="h-4 w-4 text-green-500" />
            <span className="text-sm text-green-600 font-medium">Dynamic Features Enabled</span>
          </div>
        )}
      </div>

      {/* Your existing tab navigation (keep as is) */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('roles')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
              activeTab === 'roles' ? 'bg-blue-500 text-white shadow' : 'text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Shield className="h-4 w-4" />
            Roles
          </button>
          {/* ... your other tabs */}
        </div>
        
        {/* Enhanced action buttons */}
        <div className="flex gap-2 ml-4">
          {activeTab === 'roles' && (
            <>
              <button
                onClick={() => {
                  setSelectedRole(null);
                  setShowRoleForm(true);
                  setShowEnhancedForm(false);
                }}
                className="flex items-center gap-2 px-4 py-2 h-10 bg-blue-500 text-white font-semibold rounded-lg shadow hover:bg-blue-600 transition-all duration-150"
              >
                <Plus className="h-4 w-4" />
                Create Role
              </button>
              
              {/* NEW: Enhanced role creation */}
              {canUseDynamicFeatures && (
                <button
                  onClick={() => {
                    setSelectedRole(null);
                    setShowEnhancedForm(true);
                    setShowRoleForm(false);
                  }}
                  className="flex items-center gap-2 px-4 py-2 h-10 bg-purple-500 text-white font-semibold rounded-lg shadow hover:bg-purple-600 transition-all duration-150"
                >
                  <Zap className="h-4 w-4" />
                  Create Dynamic Role
                </button>
              )}
            </>
          )}
        </div>
      </div>

      {/* Roles content */}
      {activeTab === 'roles' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Role list with dynamic indicators */}
          <div className="lg:col-span-1">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Roles</h3>
              
              {isLoadingRoles ? (
                <div className="flex justify-center items-center py-10">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                </div>
              ) : (
                <div className="space-y-2">
                  {roles.map(role => (
                    <div
                      key={role.id}
                      className={`p-3 rounded-lg cursor-pointer flex justify-between items-center ${
                        selectedRole?.id === role.id ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50 hover:bg-gray-100'
                      }`}
                      onClick={() => setSelectedRole(role)}
                    >
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-gray-200 rounded-full">
                          <Shield className="h-5 w-5 text-gray-700" />
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium text-gray-900">{role.name}</h4>
                            {/* NEW: Dynamic indicators */}
                            {role.isDynamic && (
                              <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                                Dynamic
                              </span>
                            )}
                            {role.hasRules && (
                              <span className="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full">
                                Rules
                              </span>
                            )}
                          </div>
                          <p className="text-xs text-gray-500">{role.userCount} users</p>
                        </div>
                      </div>
                      
                      {/* Enhanced action menu */}
                      <div className="flex gap-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleCloneRole(role);
                          }}
                          className="p-1 text-gray-400 hover:text-gray-600 rounded"
                          title="Clone role"
                        >
                          <Copy className="h-4 w-4" />
                        </button>
                        
                        {/* NEW: Migration button */}
                        {!role.isDynamic && canUseDynamicFeatures && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              migrateToDynamic(role.id);
                            }}
                            className="p-1 text-blue-400 hover:text-blue-600 rounded"
                            title="Make dynamic"
                          >
                            <Zap className="h-4 w-4" />
                          </button>
                        )}
                        
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedRole(role);
                            if (role.isDynamic && canUseDynamicFeatures) {
                              setShowEnhancedForm(true);
                            } else {
                              setShowRoleForm(true);
                            }
                          }}
                          className="p-1 text-gray-400 hover:text-gray-600 rounded"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Role details (your existing logic with enhancements) */}
          <div className="lg:col-span-2">
            {selectedRole ? (
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <h3 className="text-xl font-bold text-gray-900">{selectedRole.name}</h3>
                    {selectedRole.isDynamic && (
                      <span className="px-3 py-1 text-sm bg-green-100 text-green-800 rounded-full">
                        Dynamic Role
                      </span>
                    )}
                  </div>
                  {/* Your existing action buttons */}
                </div>
                
                <p className="text-gray-600 mb-6">{selectedRole.description}</p>
                
                {/* Your existing role details display */}
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Permissions ({selectedRole.permissions.length})</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {selectedRole.permissions.map(permissionId => {
                        const permission = permissions.find(p => p.id === permissionId);
                        return permission ? (
                          <div key={permissionId} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <span className="text-sm">{permission.name}</span>
                          </div>
                        ) : null;
                      })}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-white border border-gray-200 rounded-lg p-6 text-center">
                <h3 className="text-lg font-medium text-gray-900 mb-1">No Role Selected</h3>
                <p className="text-gray-500 mb-4">Select a role to view its details and permissions</p>
                {/* Your existing buttons */}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Enhanced Role Form Modal */}
      {(showRoleForm || showEnhancedForm) && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl m-4 p-6 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-gray-900">
                {selectedRole ? 'Edit Role' : 'Create New Role'}
                {showEnhancedForm && (
                  <span className="ml-2 px-2 py-1 text-sm bg-purple-100 text-purple-800 rounded">
                    Dynamic
                  </span>
                )}
              </h3>
              <button 
                onClick={() => {
                  setShowRoleForm(false);
                  setShowEnhancedForm(false);
                }}
                className="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            {showEnhancedForm ? (
              <EnhancedRoleForm 
                role={selectedRole} 
                permissions={permissions} 
                permissionsByCategory={permissionsByCategory}
                roles={roles}
                onSave={selectedRole ? handleUpdateRole : handleAddRole} 
                onCancel={() => setShowEnhancedForm(false)}
                enableDynamicFeatures={true}
              />
            ) : (
              // Your existing RoleForm component
              <div>Your existing RoleForm goes here</div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
