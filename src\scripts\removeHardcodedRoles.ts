import { AppDataSource } from '../config/database';
import { RoleSyncService } from '../services/RoleSyncService';
import logger from '../utils/logger';

/**
 * Script to systematically remove hardcoded roles and replace with dynamic system
 */
class HardcodedRoleRemoval {
  
  /**
   * Main migration to remove hardcoded roles
   */
  async migrate(): Promise<void> {
    try {
      logger.info('🚀 Starting hardcoded role removal migration...');

      // Step 1: Initialize database and dynamic system
      await AppDataSource.initialize();
      await RoleSyncService.syncRoles();
      logger.info('✅ Dynamic role system initialized');

      // Step 2: Create permission mapping
      await this.createPermissionMapping();
      logger.info('✅ Permission mapping created');

      // Step 3: Validate all users have dynamic role assignments
      await this.validateUserRoleAssignments();
      logger.info('✅ User role assignments validated');

      // Step 4: Create replacement middleware documentation
      await this.generateReplacementGuide();
      logger.info('✅ Replacement guide generated');

      logger.info('🎉 Hardcoded role removal migration completed!');
      logger.info('📋 Next steps:');
      logger.info('   1. Replace middleware imports in your routes');
      logger.info('   2. Update frontend role checks');
      logger.info('   3. Test all endpoints');
      logger.info('   4. Remove old middleware files');
      
    } catch (error) {
      logger.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Create mapping between old hardcoded roles and new permissions
   */
  private async createPermissionMapping(): Promise<void> {
    const mapping = {
      // Old hardcoded role checks -> New dynamic permissions
      'authorize([UserRole.IT_ADMIN])': 'requirePermission("system.admin")',
      'authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF])': 'requireAnyPermission(["system.admin", "tickets.manage"])',
      'authorize([UserRole.HR_ADMIN])': 'requirePermission("employees.admin")',
      'authorize([UserRole.HR_ADMIN, UserRole.HR_STAFF])': 'requireAnyPermission(["employees.admin", "employees.manage"])',
      'authorize([UserRole.SYSTEM_ADMIN])': 'requirePermission("system.admin")',
      'authorize([UserRole.SYSTEM_ADMIN, UserRole.IT_ADMIN])': 'requirePermission("system.admin")',
      
      // Role checks in controllers
      'user.role === UserRole.IT_ADMIN': 'await hasPermission(user.id, "system.admin")',
      'user.role === UserRole.HR_ADMIN': 'await hasPermission(user.id, "employees.admin")',
      '[UserRole.IT_ADMIN, UserRole.IT_STAFF].includes(user.role)': 'await hasAnyPermission(user.id, ["system.admin", "tickets.manage"])',
      
      // Frontend role checks
      'user.role === "IT_ADMIN"': 'userPermissions["system.admin"]',
      'user.role === "HR_ADMIN"': 'userPermissions["employees.admin"]',
      'hasAccess(["admin", "manager"])': 'hasAnyPermission(["system.admin", "department.manage"])',
    };

    // Save mapping to file for reference
    const fs = require('fs');
    const path = require('path');
    
    const mappingContent = `# Hardcoded Role to Dynamic Permission Mapping

This file contains the mapping between old hardcoded role checks and new dynamic permissions.

## Middleware Replacements

\`\`\`typescript
${Object.entries(mapping).map(([old, newCheck]) => `// OLD: ${old}\n// NEW: ${newCheck}`).join('\n\n')}
\`\`\`

## Permission Categories

### System Permissions
- \`system.admin\` - Full system administration (replaces IT_ADMIN, SYSTEM_ADMIN)
- \`system.configure\` - System configuration access
- \`system.backup\` - Backup and restore operations

### Ticket Permissions  
- \`tickets.create\` - Create tickets
- \`tickets.read\` - View tickets
- \`tickets.update\` - Edit tickets
- \`tickets.delete\` - Delete tickets
- \`tickets.assign\` - Assign tickets
- \`tickets.manage\` - Full ticket management (replaces IT_STAFF)

### Employee Permissions
- \`employees.admin\` - Full employee administration (replaces HR_ADMIN)
- \`employees.create\` - Create employee records
- \`employees.read\` - View employee records
- \`employees.update\` - Edit employee records
- \`employees.delete\` - Delete employee records
- \`employees.manage\` - General employee management (replaces HR_STAFF)

### Department Permissions
- \`department.manage\` - Manage department (replaces DEPT_HEAD)
- \`department.view\` - View department information

## Usage Examples

### Route Protection
\`\`\`typescript
// OLD
router.get('/admin', authorize([UserRole.IT_ADMIN]), handler);

// NEW  
router.get('/admin', requirePermission('system.admin'), handler);
\`\`\`

### Controller Logic
\`\`\`typescript
// OLD
if (user.role === UserRole.IT_ADMIN) {
  // admin logic
}

// NEW
if (await PermissionEvaluationService.hasPermission(user.id, 'system.admin')) {
  // admin logic
}
\`\`\`

### Frontend Components
\`\`\`typescript
// OLD
{user.role === 'IT_ADMIN' && <AdminButton />}

// NEW
{userPermissions['system.admin'] && <AdminButton />}
\`\`\`
`;

    fs.writeFileSync(
      path.join(process.cwd(), 'ROLE_MIGRATION_MAPPING.md'),
      mappingContent
    );
  }

  /**
   * Validate that all users have proper role assignments
   */
  private async validateUserRoleAssignments(): Promise<void> {
    const { User } = await import('../server/entities/User');
    const { UserRoleAssignment } = await import('../server/entities/UserRoleAssignment');
    
    const userRepo = AppDataSource.getRepository(User);
    const assignmentRepo = AppDataSource.getRepository(UserRoleAssignment);
    
    const users = await userRepo.find();
    const usersWithoutAssignments: string[] = [];
    
    for (const user of users) {
      const assignments = await assignmentRepo.find({
        where: { userId: user.id }
      });
      
      if (assignments.length === 0) {
        usersWithoutAssignments.push(user.email);
        logger.warn(`User ${user.email} has no role assignments`);
      }
    }
    
    if (usersWithoutAssignments.length > 0) {
      logger.warn(`⚠️  ${usersWithoutAssignments.length} users without role assignments found`);
      logger.info('Run the migration script first to assign roles to all users');
    }
  }

  /**
   * Generate step-by-step replacement guide
   */
  private async generateReplacementGuide(): Promise<void> {
    const fs = require('fs');
    const path = require('path');
    
    const guide = `# Step-by-Step Hardcoded Role Removal Guide

## Phase 1: Replace Route Middleware (High Priority)

### Files to Update:
1. \`src/server/routes/roleRoutes.ts\`
2. \`src/server/routes/permissionGroupRoutes.ts\`
3. \`src/routes/userRoutes.ts\`
4. \`src/server/routes/roleTemplateRoutes.ts\`

### Replacements:
\`\`\`typescript
// Replace all instances of:
import { authorize } from '../middleware/auth';
// With:
import { requirePermission, requireAnyPermission } from '../middleware/dynamicPermission';

// Replace:
authorize([UserRole.IT_ADMIN])
// With:
requirePermission('system.admin')

// Replace:
authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF])
// With:
requireAnyPermission(['system.admin', 'tickets.manage'])
\`\`\`

## Phase 2: Replace Controller Logic (Medium Priority)

### Files to Update:
1. \`src/server/controllers/userController.ts\`
2. \`src/server/controllers/authController.ts\`
3. \`src/server/controllers/dashboardController.ts\`

### Replacements:
\`\`\`typescript
// Replace role checks with permission checks
import { PermissionEvaluationService } from '../services/PermissionEvaluationService';

// OLD:
if (user.role === UserRole.IT_ADMIN) {
  // logic
}

// NEW:
if (await PermissionEvaluationService.hasPermission(user.id, 'system.admin')) {
  // logic
}
\`\`\`

## Phase 3: Replace Frontend Components (Medium Priority)

### Files to Update:
1. \`src/components/Dashboard.tsx\`
2. \`src/components/KnowledgeBasePage.tsx\`
3. \`src/components/ServiceDesk.tsx\`
4. \`src/routes/AppRoutes.tsx\`
5. \`src/App.tsx\`

### Add Permission Hook:
\`\`\`typescript
import { usePermissions } from '../hooks/usePermissions';

const MyComponent = () => {
  const { userPermissions, hasPermission } = usePermissions();
  
  // OLD:
  // {user.role === 'IT_ADMIN' && <AdminButton />}
  
  // NEW:
  {userPermissions['system.admin'] && <AdminButton />}
  
  // OR:
  {hasPermission('system.admin') && <AdminButton />}
};
\`\`\`

## Phase 4: Remove Old Middleware (Low Priority)

### Files to Remove:
1. \`src/server/middleware/checkRole.ts\`
2. \`src/middleware/authorize.ts\`
3. \`src/utils/roleAccess.ts\`
4. \`src/utils/roleChecks.ts\`
5. \`src/server/routers/hr/hrPermissionMiddleware.ts\`

### Files to Update:
1. \`src/server/middleware/authMiddleware.ts\` - Remove hardcoded role checks
2. \`src/server/middleware/auth.ts\` - Remove authorize function

## Testing Checklist

- [ ] All routes still work with new middleware
- [ ] Admin users can access admin functions
- [ ] Regular users are properly restricted
- [ ] Frontend components show/hide based on permissions
- [ ] No console errors related to role checks
- [ ] All tests pass

## Rollback Plan

If issues occur:
1. Revert middleware imports
2. Restore old authorize middleware
3. Check logs for permission evaluation errors
4. Verify role assignments are correct

## Commands

\`\`\`bash
# Test the new system
npm run test:permissions

# Check for remaining hardcoded roles
grep -r "UserRole\\." src/ --exclude-dir=node_modules

# Verify all users have permissions
npm run verify:user-permissions
\`\`\`
`;

    fs.writeFileSync(
      path.join(process.cwd(), 'HARDCODED_ROLE_REMOVAL_GUIDE.md'),
      guide
    );
  }
}

// Script execution
async function removeHardcodedRoles() {
  const removal = new HardcodedRoleRemoval();
  
  try {
    await removal.migrate();
    process.exit(0);
  } catch (error) {
    console.error('❌ Hardcoded role removal failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  removeHardcodedRoles();
}

export { HardcodedRoleRemoval };
