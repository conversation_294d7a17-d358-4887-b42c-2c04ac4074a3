import { MigrationInterface, QueryRunner } from "typeorm";

export class OptimizeEmployeeTableFields1745488588080 implements MigrationInterface {
    name = 'OptimizeEmployeeTableFields1745488588080'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`deviceEntries\` longtext NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`accommodationProvidedByEmployer\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`accommodationType\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`accommodationAddress\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`workSchedule\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`shiftType\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`previousEmployeeId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`remoteWorkEligible\` tinyint NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`nextReviewDate\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`trainingRequirements\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`status\` varchar(255) NULL DEFAULT 'active'`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`healthInsuranceProvider\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`healthInsurancePolicyNumber\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`healthInsuranceExpiryDate\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`lifeInsuranceProvider\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`lifeInsurancePolicyNumber\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`lifeInsuranceExpiryDate\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`vaccinationRecords\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`medicalHistory\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`bloodGroup\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`allergies\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`chronicConditions\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`regularMedications\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`spouseName\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`spouseDateOfBirth\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`spouseOccupation\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`spouseEmployer\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`spouseContactNumber\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`spouseCNIC\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`children\` longtext NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`dependents\` longtext NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`documents\` longtext NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`requiredDocuments\` longtext NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`projectEntries\` longtext NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`notes\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`specialInstructions\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`otherSocialProfiles\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`otherSocialProfiles\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`professionalMemberships\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`professionalMemberships\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`hobbiesInterests\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`hobbiesInterests\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`educationEntries\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`educationEntries\` longtext NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`experienceEntries\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`experienceEntries\` longtext NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`professionalSkills\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`professionalSkills\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`technicalSkills\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`technicalSkills\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`certifications\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`certifications\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`languages\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`languages\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`permanentAddress\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`permanentAddress\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`currentAddress\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`currentAddress\` text NULL`);
        
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`officialEmail\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`officialEmail\` text NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`personalEmail\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`personalEmail\` text NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`personalEmail\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`personalEmail\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`officialEmail\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`officialEmail\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`currentAddress\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`currentAddress\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`permanentAddress\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`permanentAddress\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`languages\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`languages\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`certifications\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`certifications\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`technicalSkills\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`technicalSkills\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`professionalSkills\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`professionalSkills\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`experienceEntries\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`experienceEntries\` json NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`educationEntries\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`educationEntries\` json NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`hobbiesInterests\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`hobbiesInterests\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`professionalMemberships\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`professionalMemberships\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`otherSocialProfiles\``);
        await queryRunner.query(`ALTER TABLE \`employees\` ADD \`otherSocialProfiles\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`specialInstructions\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`notes\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`projectEntries\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`requiredDocuments\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`documents\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`dependents\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`children\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`spouseCNIC\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`spouseContactNumber\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`spouseEmployer\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`spouseOccupation\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`spouseDateOfBirth\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`spouseName\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`regularMedications\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`chronicConditions\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`allergies\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`bloodGroup\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`medicalHistory\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`vaccinationRecords\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`lifeInsuranceExpiryDate\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`lifeInsurancePolicyNumber\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`lifeInsuranceProvider\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`healthInsuranceExpiryDate\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`healthInsurancePolicyNumber\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`healthInsuranceProvider\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`status\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`trainingRequirements\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`nextReviewDate\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`remoteWorkEligible\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`previousEmployeeId\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`shiftType\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`workSchedule\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`accommodationAddress\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`accommodationType\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`accommodationProvidedByEmployer\``);
        await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`deviceEntries\``);
    }

}
