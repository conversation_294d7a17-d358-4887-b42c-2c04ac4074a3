import React, { useState } from 'react';
import { 
  DollarSign, 
  Calculator, 
  FileText, 
  Settings, 
  Calendar, 
  Plus,
  Search,
  Filter,
  Download,
  Edit,
  Trash2,
  ArrowUp,
  ArrowDown,
  CheckCircle,
  Clock,
  AlertCircle,
  Info
} from 'lucide-react';
import TaxSlabConfiguration from './tax/TaxSlabConfiguration';
import TaxCalculator from './tax/TaxCalculator';
import TaxComplianceReports from './tax/TaxComplianceReports';
import TaxStatements from './tax/TaxStatements';

interface TaxManagementProps {
  isAdmin?: boolean;
}

const TaxManagement: React.FC<TaxManagementProps> = ({ isAdmin = false }) => {
  const [activeTab, setActiveTab] = useState('slabs');

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Tax Management</h3>
          <p className="text-sm text-gray-500">Configure tax slabs, calculate taxes, and generate tax reports</p>
        </div>
        
        {/* Action Buttons */}
        <div className="flex space-x-3">
          {isAdmin && (
            <button
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Plus className="h-4 w-4 mr-2" />
              New Tax Configuration
            </button>
          )}
          
          <button
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
        </div>
      </div>
      
      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="flex -mb-px space-x-8">
          <button
            onClick={() => setActiveTab('slabs')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'slabs'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Settings className="inline-block mr-2 h-4 w-4" />
            Tax Slabs
          </button>
          <button
            onClick={() => setActiveTab('calculator')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'calculator'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Calculator className="inline-block mr-2 h-4 w-4" />
            Tax Calculator
          </button>
          <button
            onClick={() => setActiveTab('compliance')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'compliance'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <FileText className="inline-block mr-2 h-4 w-4" />
            Compliance Reports
          </button>
          <button
            onClick={() => setActiveTab('statements')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'statements'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <DollarSign className="inline-block mr-2 h-4 w-4" />
            Tax Statements
          </button>
        </nav>
      </div>
      
      {/* Content based on active tab */}
      <div>
        {activeTab === 'slabs' && <TaxSlabConfiguration isAdmin={isAdmin} />}
        {activeTab === 'calculator' && <TaxCalculator />}
        {activeTab === 'compliance' && <TaxComplianceReports isAdmin={isAdmin} />}
        {activeTab === 'statements' && <TaxStatements isAdmin={isAdmin} />}
      </div>
    </div>
  );
};

export default TaxManagement; 