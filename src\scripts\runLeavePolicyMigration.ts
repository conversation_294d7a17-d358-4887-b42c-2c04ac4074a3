import { AppDataSource } from '../config/database';
import { SimplifyLeavePolicySettings1750300000000 } from '../migrations/1750300000000-SimplifyLeavePolicySettings';

async function runMigration() {
  try {
    console.log('🚀 Starting Leave Policy Settings Migration...');
    
    // Initialize database connection
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ Database connection established');
    }

    // Create migration instance
    const migration = new SimplifyLeavePolicySettings1750300000000();
    
    // Run the migration
    await migration.up(AppDataSource.createQueryRunner());
    
    console.log('✅ Migration completed successfully!');
    console.log('📋 Summary:');
    console.log('   - Removed unnecessary columns from leave_policy_settings table');
    console.log('   - Kept only: minDaysNotice, allowLeaveModification, and system fields');
    console.log('   - Database schema now matches simplified UI interface');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  runMigration();
}

export { runMigration }; 