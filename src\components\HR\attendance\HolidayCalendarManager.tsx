import React, { useState, useEffect } from 'react';
import { Holiday, HolidayCalendar } from '../../../types/attendance';
import { 
  Calendar, 
  Plus,
  Edit2,
  Trash2,
  CalendarDays,
  Save,
  X,
  Check
} from 'lucide-react';

interface HolidayCalendarManagerProps {
  isAdmin: boolean;
}

const HolidayCalendarManager: React.FC<HolidayCalendarManagerProps> = ({ isAdmin }) => {
  const [calendars, setCalendars] = useState<HolidayCalendar[]>([]);
  const [holidays, setHolidays] = useState<Holiday[]>([]);
  const [selectedCalendar, setSelectedCalendar] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [showHolidayForm, setShowHolidayForm] = useState(false);
  const [editingHoliday, setEditingHoliday] = useState<Holiday | null>(null);

  // Mock data initialization
  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      const mockCalendars: HolidayCalendar[] = [
        {
          id: 1,
          name: 'Default Calendar 2023',
          year: 2023,
          holidays: []
        },
        {
          id: 2,
          name: 'US Holidays 2023',
          year: 2023,
          holidays: []
        }
      ];
      
      const mockHolidays: Holiday[] = [
        {
          id: 1,
          name: 'New Year\'s Day',
          date: '2023-01-01',
          isRecurring: true,
          isOptional: false
        },
        {
          id: 2,
          name: 'Independence Day',
          date: '2023-07-04',
          isRecurring: true,
          isOptional: false
        },
        {
          id: 3,
          name: 'Christmas',
          date: '2023-12-25',
          isRecurring: true,
          isOptional: false
        },
        {
          id: 4,
          name: 'Company Foundation Day',
          date: '2023-06-15',
          description: 'Annual company celebration',
          isRecurring: true,
          isOptional: false
        }
      ];
      
      setCalendars(mockCalendars);
      setHolidays(mockHolidays);
      setSelectedCalendar(1);
      setLoading(false);
    }, 800);
  }, []);

  const handleAddHoliday = () => {
    setEditingHoliday({
      name: '',
      date: new Date().toISOString().split('T')[0],
      isRecurring: false,
      isOptional: false
    });
    setShowHolidayForm(true);
  };

  const handleEditHoliday = (holiday: Holiday) => {
    setEditingHoliday({ ...holiday });
    setShowHolidayForm(true);
  };

  const handleDeleteHoliday = (id: number | undefined) => {
    if (window.confirm('Are you sure you want to delete this holiday?')) {
      setHolidays(holidays.filter(h => h.id !== id));
    }
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    if (!editingHoliday) return;
    
    const { name, value, type } = e.target;
    let newValue = value;
    
    if (type === 'checkbox') {
      newValue = (e.target as HTMLInputElement).checked as any;
    }
    
    setEditingHoliday({
      ...editingHoliday,
      [name]: newValue
    });
  };

  const handleSaveHoliday = () => {
    if (!editingHoliday || !editingHoliday.name || !editingHoliday.date) {
      alert('Please fill in all required fields');
      return;
    }
    
    if (editingHoliday.id) {
      // Update existing holiday
      setHolidays(
        holidays.map(h => h.id === editingHoliday.id ? editingHoliday : h)
      );
    } else {
      // Add new holiday with generated ID
      const newId = Math.max(...holidays.map(h => h.id || 0), 0) + 1;
      setHolidays([...holidays, { ...editingHoliday, id: newId }]);
    }
    
    setShowHolidayForm(false);
    setEditingHoliday(null);
  };

  // Get active calendar
  const activeCalendar = calendars.find(c => c.id === selectedCalendar);

  return (
    <div className="bg-white rounded-lg shadow-sm p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-900">Holiday Calendar</h3>
        <div className="flex items-center space-x-2">
          <select
            value={selectedCalendar || ''}
            onChange={(e) => setSelectedCalendar(parseInt(e.target.value))}
            className="form-select block w-48 shadow-sm sm:text-sm border-gray-300 rounded-md"
          >
            <option value="">Select Calendar</option>
            {calendars.map(calendar => (
              <option key={calendar.id} value={calendar.id}>{calendar.name}</option>
            ))}
          </select>
          
          {isAdmin && (
            <button
              onClick={handleAddHoliday}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Holiday
            </button>
          )}
        </div>
      </div>
      
      {loading ? (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <>
          {holidays.length === 0 ? (
            <div className="bg-gray-50 rounded-md p-8 text-center">
              <CalendarDays className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <h3 className="text-sm font-medium text-gray-900">No holidays configured</h3>
              <p className="text-xs text-gray-500 mb-4">
                {isAdmin 
                  ? 'Start adding holidays to create your company calendar'
                  : 'No holidays have been configured for this calendar yet'}
              </p>
              {isAdmin && (
                <button
                  onClick={handleAddHoliday}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Holiday
                </button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Holiday Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    {isAdmin && (
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    )}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {holidays.map((holiday) => (
                    <tr key={holiday.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{holiday.name}</div>
                        {holiday.description && (
                          <div className="text-xs text-gray-500">{holiday.description}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(holiday.date).toLocaleDateString(undefined, { 
                          year: 'numeric', 
                          month: 'long', 
                          day: 'numeric' 
                        })}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {holiday.isRecurring ? 'Annual' : 'One-time'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          holiday.isOptional ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
                        }`}>
                          {holiday.isOptional ? 'Optional' : 'Mandatory'}
                        </span>
                      </td>
                      {isAdmin && (
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => handleEditHoliday(holiday)}
                            className="text-blue-600 hover:text-blue-900 mr-3"
                          >
                            <Edit2 className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteHoliday(holiday.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </td>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          
          {/* Holiday Add/Edit Form */}
          {showHolidayForm && editingHoliday && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    {editingHoliday.id ? 'Edit Holiday' : 'Add Holiday'}
                  </h3>
                  <button
                    onClick={() => {
                      setShowHolidayForm(false);
                      setEditingHoliday(null);
                    }}
                    className="text-gray-400 hover:text-gray-500"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                      Holiday Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      value={editingHoliday.name}
                      onChange={handleFormChange}
                      className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
                      Date *
                    </label>
                    <input
                      type="date"
                      id="date"
                      name="date"
                      required
                      value={editingHoliday.date}
                      onChange={handleFormChange}
                      className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      id="description"
                      name="description"
                      rows={2}
                      value={editingHoliday.description || ''}
                      onChange={handleFormChange}
                      className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isRecurring"
                      name="isRecurring"
                      checked={editingHoliday.isRecurring}
                      onChange={handleFormChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isRecurring" className="ml-2 block text-sm text-gray-700">
                      Recurring annually
                    </label>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isOptional"
                      name="isOptional"
                      checked={editingHoliday.isOptional}
                      onChange={handleFormChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isOptional" className="ml-2 block text-sm text-gray-700">
                      Optional holiday
                    </label>
                  </div>
                </div>
                
                <div className="mt-5 flex justify-end">
                  <button
                    type="button"
                    onClick={() => {
                      setShowHolidayForm(false);
                      setEditingHoliday(null);
                    }}
                    className="mr-3 inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleSaveHoliday}
                    className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save Holiday
                  </button>
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default HolidayCalendarManager; 