import { AppDataSource } from '../config/database';
import { Employee } from '../server/entities/Employee';
import { LeaveBalance } from '../entities/LeaveBalance';
import { LeaveAllocation } from '../entities/LeaveAllocation';

async function testEmployeePortalLeave() {
  try {
    console.log('🔍 Testing Employee Portal Leave Data...');
    
    await AppDataSource.initialize();
    console.log('✅ Database connected');
    
    const employeeRepository = AppDataSource.getRepository(Employee);
    const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);
    const leaveAllocationRepository = AppDataSource.getRepository(LeaveAllocation);
    
    // Get first 10 active employees with relations
    const employees = await employeeRepository.find({
      where: { status: 'active' },
      take: 10,
      relations: ['contact', 'job']
    });
    
    console.log(`\n📋 Found ${employees.length} active employees:`);
    
    for (const employee of employees) {
      const fullName = `${employee.firstName || ''} ${employee.lastName || ''}`.trim();
      const email = employee.contact?.personalEmail || employee.contact?.officialEmail || 'No email';
      console.log(`\n👤 Employee: ${fullName} (ID: ${employee.id}) - ${email}`);
      
      // Check leave balances
      const balances = await leaveBalanceRepository.find({
        where: {
          employeeId: employee.id,
          year: 2025,
          isActive: true
        }
      });
      
      console.log(`  💳 Leave Balances (${balances.length}):`);
      for (const balance of balances) {
        console.log(`    - ${balance.leaveType}: Total=${balance.totalAllocated}, Used=${balance.used}, Remaining=${balance.remaining}`);
      }
      
      // Check leave allocations
      const allocations = await leaveAllocationRepository.find({
        where: {
          employeeId: employee.id,
          year: 2025,
          isActive: true
        }
      });
      
      console.log(`  📊 Leave Allocations (${allocations.length}):`);
      for (const allocation of allocations) {
        console.log(`    - ${allocation.leaveType}: Total=${allocation.totalAllocated}, Policy=${allocation.policyAllocation}, Manual=${allocation.manualAdjustment}`);
      }
    }
    
    console.log('\n🔍 Testing specific employees from Leave Management screenshot:');
    
    // Test specific employees that we saw in the screenshot
    const testEmployees = [
      { name: 'Anusha Malik', empId: 'GC-0403' },
      { name: 'Alhudah Javaid', empId: 'GCK-131' },
      { name: 'Shoaib Shafqat', empId: 'GC-0345' }
    ];
    
    for (const testEmp of testEmployees) {
      const employee = await employeeRepository.findOne({
        where: [
          { employeeId: testEmp.empId },
          { firstName: testEmp.name.split(' ')[0] }
        ],
        relations: ['contact', 'job']
      });
      
      if (employee) {
        const email = employee.contact?.personalEmail || employee.contact?.officialEmail || 'No email';
        console.log(`\n🎯 Found ${testEmp.name} (${testEmp.empId}) with ID: ${employee.id} - Email: ${email}`);
        
        // Test leave balance API call
        const balances = await leaveBalanceRepository.find({
          where: { employeeId: employee.id, year: 2025, isActive: true }
        });
        
        console.log(`  Leave Balances: ${balances.length} found`);
        balances.forEach(balance => {
          console.log(`    ${balance.leaveType}: ${balance.remaining}/${balance.totalAllocated}`);
        });
      } else {
        console.log(`❌ ${testEmp.name} (${testEmp.empId}) not found in database`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await AppDataSource.destroy();
  }
}

testEmployeePortalLeave(); 