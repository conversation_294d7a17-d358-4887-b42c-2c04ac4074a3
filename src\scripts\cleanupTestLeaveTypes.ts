import { AppDataSource } from '../config/database';
import { LeaveTypePolicy } from '../entities/LeaveTypePolicy';

async function cleanupTestLeaveTypes() {
  try {
    console.log('🔍 Initializing database connection...');
    await AppDataSource.initialize();
    
    const leaveTypePolicyRepository = AppDataSource.getRepository(LeaveTypePolicy);
    
    console.log('\n🧹 Cleaning up test leave types...');
    
    // Delete the test SICK_LEAVE we created
    const testLeaveType = await leaveTypePolicyRepository.findOne({
      where: { leaveType: 'SICK_LEAVE' }
    });
    
    if (testLeaveType) {
      await leaveTypePolicyRepository.remove(testLeaveType);
      console.log('✅ Removed test SICK_LEAVE type');
    } else {
      console.log('❌ No test SICK_LEAVE found to remove');
    }
    
    // Check if any other leave types exist
    const allLeaveTypes = await leaveTypePolicyRepository.find();
    console.log(`\n📋 Remaining leave types: ${allLeaveTypes.length}`);
    allLeaveTypes.forEach((lt, index) => {
      console.log(`${index + 1}. ${lt.leaveType} (${lt.displayName})`);
    });
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    await AppDataSource.destroy();
  }
}

cleanupTestLeaveTypes().catch(console.error); 