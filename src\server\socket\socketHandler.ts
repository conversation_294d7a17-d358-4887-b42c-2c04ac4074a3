import { Server as SocketIOServer } from 'socket.io';
import { Server } from 'http';
import jwt from 'jsonwebtoken';
import logger from '../../utils/logger';

export const initializeSocketServer = (server: Server, corsOptions: any) => {
  // Initialize Socket.IO
  const io = new SocketIOServer(server, {
    cors: {
      origin: (origin, callback) => {
        // Allow requests with no origin (like mobile apps or curl requests)
        if (!origin) return callback(null, true);
        
        // For requests with origin, check against allowed origins
        // If corsOptions.origin is a wildcard (*) but we're using credentials,
        // we must specify the actual origin instead of using the wildcard
        if (corsOptions.origin === '*') {
          // Accept localhost origins for development
          if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
            return callback(null, true);
          }
          // Accept the actual origin of the request
          return callback(null, true);
        }
        
        // Check against the specific allowed origins in corsOptions
        if (typeof corsOptions.origin === 'string') {
          return callback(null, corsOptions.origin === origin);
        }
        
        // If corsOptions.origin is an array, check if the origin is in the array
        if (Array.isArray(corsOptions.origin) && corsOptions.origin.includes(origin)) {
          return callback(null, true);
        }
        
        // Default allow for development
        return callback(null, true);
      },
      methods: ["GET", "POST"],
      credentials: true,
      allowedHeaders: ["Content-Type", "Authorization"]
    },
    path: '/socket.io/',
    transports: ['polling', 'websocket'],
    allowEIO3: true,
    pingTimeout: 60000,
    pingInterval: 25000
  });
  
  // Log when server is created
  logger.info(`Socket.IO server initialized with CORS origin: ${corsOptions.origin}`);
  
  // Socket.IO authentication middleware
  io.use((socket, next) => {
    try {
      // Log all handshake data for debugging
      logger.info(`Socket connection attempt: ${socket.id}`);
      logger.debug(`Socket handshake headers:`, socket.handshake.headers);
      logger.debug(`Socket handshake query:`, socket.handshake.query);
      
      // Try to get token from handshake query or auth
      const queryToken = socket.handshake.query.token;
      const authToken = socket.handshake.auth?.token;
      
      // Use the first available token
      const token = typeof queryToken === 'string' ? queryToken : authToken;
      
      logger.info(`Socket ${socket.id} token found: ${!!token}`);
      
      if (!token) {
        logger.warn(`Socket connection attempt without token: ${socket.id}`);
        return next(new Error('Authentication error: Token missing'));
      }
      
      // Verify the token
      const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
      
      try {
        // Log the token for debugging (only first few characters)
        logger.debug(`Token for socket ${socket.id}: ${token.substring(0, 10)}...`);
        
        const decoded = jwt.verify(token, JWT_SECRET);
        
        if (typeof decoded !== 'object' || decoded === null) {
          logger.warn(`Invalid token structure for socket ${socket.id}`);
          return next(new Error('Authentication error: Invalid token structure'));
        }
        
        // Log the decoded token for debugging
        logger.debug(`Decoded token for socket ${socket.id}:`, decoded);
        
        // Extract user data from token - check all possible property names
        const userId = (decoded as any).userId || 
                       (decoded as any).id || 
                       (decoded as any).sub;
                       
        const name = (decoded as any).name;
        const email = (decoded as any).email;
        const role = (decoded as any).role;
        
        if (!userId) {
          logger.warn(`No userId found in token for socket ${socket.id}. Token payload:`, decoded);
          return next(new Error('Authentication error: No user ID in token'));
        }
        
        // Store user data in socket
        socket.data.user = {
          userId,
          name,
          email,
          role
        };
        
        logger.info(`Socket authenticated for user: ${name || 'Unknown'} (${userId})`);
        next();
      } catch (jwtError: any) {
        logger.error(`JWT verification failed for socket ${socket.id}:`, jwtError);
        return next(new Error(`Authentication error: ${jwtError.message}`));
      }
    } catch (error) {
      logger.error(`Socket middleware error for ${socket.id}:`, error);
      return next(new Error('Internal server error'));
    }
  });
  
  // Create a namespace for notifications
  const notificationsNamespace = io.of('/notifications');
  
  notificationsNamespace.on('connection', (socket) => {
    logger.info(`Client connected to notifications: ${socket.id}`);
    
    // Join user to their own room for private messages
    const userId = socket.data?.user?.userId;
    if (userId) {
      socket.join(`user:${userId}`);
      logger.info(`User ${userId} joined their private room`);
      
      // Send a test notification to the user
      setTimeout(() => {
        const testNotification = {
          id: Math.random().toString(36).substring(2, 15),
          title: 'Welcome to IMS',
          message: 'Your socket connection is working properly!',
          read: false,
          priority: 'low',
          timestamp: new Date()
        };
        
        socket.emit('notification', testNotification);
        logger.info(`Sent test notification to user ${userId}`);
      }, 2000);
    } else {
      logger.warn(`No user ID found for socket ${socket.id}`);
    }
    
    // Handle subscribe event
    socket.on('subscribe', (data) => {
      const { userId } = data;
      if (userId) {
        socket.join(`user:${userId}`);
        logger.info(`User ${userId} subscribed to notifications`);
      }
    });
    
    // Handle unsubscribe event
    socket.on('unsubscribe', (data) => {
      const { userId } = data;
      if (userId) {
        socket.leave(`user:${userId}`);
        logger.info(`User ${userId} unsubscribed from notifications`);
      }
    });
    
    // Handle notification:mark_read event
    socket.on('notification:mark_read', (data) => {
      logger.debug('Notification marked as read:', data);
      // Here you would update the notification in the database
    });
    
    // Handle notification:get_preferences event
    socket.on('notification:get_preferences', (data, callback) => {
      // Here you would fetch the user's notification preferences
      callback({
        email: true,
        push: true,
        inApp: true
      });
    });
    
    // Handle notification:update_preferences event
    socket.on('notification:update_preferences', (preferences, callback) => {
      logger.debug('Updating notification preferences:', preferences);
      // Here you would update the user's notification preferences
      callback({ success: true });
    });
    
    socket.on('disconnect', () => {
      logger.info(`Client disconnected from notifications: ${socket.id}`);
    });
  });
  
  // Create a namespace for dashboard
  const dashboardNamespace = io.of('/dashboard');
  
  dashboardNamespace.on('connection', (socket) => {
    logger.info(`Client connected to dashboard: ${socket.id}`);
    
    // Join user to dashboard room
    socket.join('dashboard');
    
    socket.on('disconnect', () => {
      logger.info(`Client disconnected from dashboard: ${socket.id}`);
    });
  });
  
  // Return the io instance
  return io;
};

// Helper function to send a notification to a specific user
export const sendNotificationToUser = (io: SocketIOServer, userId: string, notification: any) => {
  io.of('/notifications').to(`user:${userId}`).emit('notification', notification);
};

// Helper function to broadcast a notification to all connected clients
export const broadcastNotification = (io: SocketIOServer, notification: any) => {
  io.of('/notifications').emit('notification', notification);
};

// Helper function to send a dashboard update to all connected clients
export const broadcastDashboardUpdate = (io: SocketIOServer, update: any) => {
  io.of('/dashboard').emit('dashboard:update', update);
}; 