import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('leave_policies')
export class LeavePolicy {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'varchar',
    length: 50,
    unique: true
  })
  leaveType: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  defaultAllocation: number;

  @Column({ type: 'int', default: 365 })
  accrualPeriodDays: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  maxCarryForward: number;

  @Column({ type: 'boolean', default: false })
  canCarryForward: boolean;

  @Column({ type: 'int', nullable: true })
  carryForwardExpiryDays?: number;

  @Column({ type: 'int', default: 0 })
  minimumServiceDays: number;

  @Column({ type: 'int', default: 0 })
  waitingPeriodDays: number;

  @Column({ type: 'int', default: 1 })
  minimumApplicationDays: number;

  @Column({ type: 'int', nullable: true })
  maximumConsecutiveDays?: number;

  @Column({ type: 'boolean', default: true })
  requiresApproval: boolean;

  @Column({ type: 'boolean', default: false })
  requiresDocumentation: boolean;

  @Column({ type: 'boolean', default: true })
  countsWeekends: boolean;

  @Column({ type: 'boolean', default: true })
  countsHolidays: boolean;

  @Column({ type: 'boolean', default: false })
  canApplyOnWeekends: boolean;

  @Column({ type: 'boolean', default: false })
  canApplyOnHolidays: boolean;

  @Column({ type: 'boolean', default: false })
  isPaid: boolean;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 1.0 })
  accrualRate: number; // Leaves accrued per period

  @Column({ type: 'text', nullable: true })
  eligibilityCriteria?: string;

  @Column({ type: 'text', nullable: true })
  approvalWorkflow?: string; // JSON string for workflow configuration

  @Column({ type: 'text', nullable: true })
  restrictedDates?: string; // JSON array of restricted date ranges

  @Column({ type: 'text', nullable: true })
  documentationRequired?: string; // JSON array of required document types

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'int', nullable: true })
  sortOrder?: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual properties for policy logic
  get isUnlimited(): boolean {
    return this.defaultAllocation < 0;
  }

  get requiresMinimumService(): boolean {
    return this.minimumServiceDays > 0;
  }

  get hasWaitingPeriod(): boolean {
    return this.waitingPeriodDays > 0;
  }

  get hasMaximumLimit(): boolean {
    return this.maximumConsecutiveDays !== null && this.maximumConsecutiveDays !== undefined && this.maximumConsecutiveDays > 0;
  }

  // Methods for policy operations
  calculateAccruedLeave(employmentDays: number): number {
    if (employmentDays < this.minimumServiceDays) {
      return 0;
    }

    if (this.isUnlimited) {
      return -1; // Unlimited leave
    }

    const periodsPassed = Math.floor(employmentDays / this.accrualPeriodDays);
    return periodsPassed * this.accrualRate;
  }

  isEligibleForLeave(employmentDays: number, requestDate: Date): boolean {
    // Check minimum service requirement
    if (employmentDays < this.minimumServiceDays) {
      return false;
    }

    // Check waiting period
    if (employmentDays < this.waitingPeriodDays) {
      return false;
    }

    // Check if request date is in restricted dates
    if (this.restrictedDates) {
      try {
        const restricted = JSON.parse(this.restrictedDates);
        const requestDateStr = requestDate.toISOString().split('T')[0];
        
        for (const range of restricted) {
          if (requestDateStr >= range.start && requestDateStr <= range.end) {
            return false;
          }
        }
      } catch (error) {
        console.error('Error parsing restricted dates:', error);
      }
    }

    return true;
  }

  validateRequest(requestDays: number, availableBalance: number): { isValid: boolean; message?: string } {
    // Check if unlimited leave
    if (this.isUnlimited) {
      return { isValid: true };
    }

    // Check available balance
    if (requestDays > availableBalance) {
      return {
        isValid: false,
        message: `Insufficient leave balance. Available: ${availableBalance} days, Requested: ${requestDays} days`
      };
    }

    // Check maximum consecutive days limit
    if (this.hasMaximumLimit && requestDays > this.maximumConsecutiveDays!) {
      return {
        isValid: false,
        message: `Cannot exceed maximum consecutive days limit of ${this.maximumConsecutiveDays} days`
      };
    }

    return { isValid: true };
  }
} 