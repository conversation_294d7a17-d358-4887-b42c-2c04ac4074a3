import React, { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ist, 
  Ta<PERSON>Trigger 
} from '../../ui/Tabs';
import BiometricClockIn from './BiometricClockIn';
import OvertimeCalculator from './OvertimeCalculator';
import AttendanceForm from './AttendanceForm';
import { CheckCircle, Clock, Calculator, FileText, User, AlertTriangle } from 'lucide-react';
import { OvertimeType, BiometricVerificationType } from '../../../types/timeAttendance';
import { PayrollCurrency } from '../../../types/payroll';
import { 
  getAlertStyle, 
  hrCardStyle 
} from '../../../styles/hrWorkflow';

// Common button style for consistency across all HR workflow components
export const hrPrimaryButtonStyle = 
  "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200";

export const hrSecondaryButtonStyle = 
  "inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200";

export const hrActionButtonStyle = 
  "text-gray-500 hover:text-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 rounded-full p-1 transition-colors duration-200";

interface AdvancedTimeAttendanceWrapperProps {
  employeeId?: number;
  employeeName?: string;
  defaultTab?: 'clockin' | 'overtime' | 'attendance';
  onReturnToStandard?: () => void;
  employeeData?: {
    id: number;
    name: string;
    department?: string;
    position?: string;
  };
}

const AdvancedTimeAttendanceWrapper: React.FC<AdvancedTimeAttendanceWrapperProps> = ({
  employeeId = 0,
  employeeName = '',
  defaultTab = 'clockin'
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab);
  const [message, setMessage] = useState<{type: 'success' | 'error' | 'info' | 'warning', text: string} | null>(null);

  // Common success handler for consistent notifications
  const handleSuccess = (message: string) => {
    setMessage({
      type: 'success',
      text: message
    });
    
    // Clear message after 5 seconds
    setTimeout(() => {
      setMessage(null);
    }, 5000);
  };

  // Common error handler for consistent notifications
  const handleError = (message: string) => {
    setMessage({
      type: 'error',
      text: message
    });
    
    // Clear message after 5 seconds
    setTimeout(() => {
      setMessage(null);
    }, 5000);
  };

  // Mock handlers for demo purposes
  const handleClockInSuccess = (data: any) => {
    handleSuccess(`Successfully ${data.mode === 'clock-in' ? 'clocked in' : 'clocked out'} at ${data.time}`);
  };

  const handleOvertimeSave = (data: any) => {
    handleSuccess(`Overtime calculation saved for period ${data.periodStart} to ${data.periodEnd}`);
  };

  const handleAttendanceSubmit = (data: any) => {
    handleSuccess(`Attendance record saved for ${data.employeeName} on ${data.date}`);
  };

  return (
    <div className={hrCardStyle}>
      {/* Message notifications */}
      {message && (
        <div className={getAlertStyle(message.type)}>
          {message.type === 'success' && <CheckCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5 text-green-500" />}
          {message.type === 'error' && <AlertTriangle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5 text-red-500" />}
          {message.type === 'info' && <Clock className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5 text-blue-500" />}
          {message.type === 'warning' && <AlertTriangle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5 text-yellow-500" />}
          <p>{message.text}</p>
        </div>
      )}

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)}>
        <TabsList className="grid w-full grid-cols-3 mb-6">
          <TabsTrigger 
            value="clockin" 
            className="flex items-center gap-2 py-2"
          >
            <Clock className="h-4 w-4" />
            <span>Clock In/Out</span>
          </TabsTrigger>
          <TabsTrigger 
            value="overtime" 
            className="flex items-center gap-2 py-2"
          >
            <Calculator className="h-4 w-4" />
            <span>Overtime</span>
          </TabsTrigger>
          <TabsTrigger 
            value="attendance" 
            className="flex items-center gap-2 py-2"
          >
            <FileText className="h-4 w-4" />
            <span>Attendance</span>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="clockin" className="p-4">
          <BiometricClockIn 
            employeeId={employeeId}
            employeeName={employeeName}
            onClockInSuccess={handleClockInSuccess}
            onClockInFail={handleError}
            mode="clock-in"
            verificationTypes={[BiometricVerificationType.FINGERPRINT, BiometricVerificationType.FACE]}
            requireLocation={true}
            saveToAttendanceSystem={true}
          />
        </TabsContent>
        
        <TabsContent value="overtime" className="p-4">
          <OvertimeCalculator 
            employeeId={employeeId}
            employeeName={employeeName}
            hourlyRate={25}
            currency={PayrollCurrency.USD}
            onSave={handleOvertimeSave}
            prefilledHours={{
              [OvertimeType.REGULAR]: 5,
              [OvertimeType.WEEKEND]: 2
            }}
          />
        </TabsContent>
        
        <TabsContent value="attendance" className="p-4">
          <AttendanceForm
            onSubmit={handleAttendanceSubmit}
            employees={[
              { id: employeeId, name: employeeName, department: 'IT', position: 'Developer' }
            ]}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdvancedTimeAttendanceWrapper; 