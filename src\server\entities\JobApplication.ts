import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn
} from 'typeorm';
import { IsNotEmpty, IsOptional, IsEmail, IsEnum, Length, <PERSON>N<PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { JobPosting } from './JobPosting';
import { User } from './User';
import { Interview } from './Interview';
import { ApplicationEvaluation } from './ApplicationEvaluation';

export enum ApplicationStatus {
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  SCREENING = 'screening',
  INTERVIEW_SCHEDULED = 'interview_scheduled',
  INTERVIEWING = 'interviewing',
  TECHNICAL_ASSESSMENT = 'technical_assessment',
  REFERENCE_CHECK = 'reference_check',
  OFFER_PENDING = 'offer_pending',
  OFFER_EXTENDED = 'offer_extended',
  OFFER_ACCEPTED = 'offer_accepted',
  OFFER_DECLINED = 'offer_declined',
  HIRED = 'hired',
  REJECTED = 'rejected',
  WITHDRAWN = 'withdrawn',
  ON_HOLD = 'on_hold'
}

export enum ApplicationSource {
  COMPANY_WEBSITE = 'company_website',
  JOB_BOARD = 'job_board',
  LINKEDIN = 'linkedin',
  REFERRAL = 'referral',
  RECRUITER = 'recruiter',
  SOCIAL_MEDIA = 'social_media',
  CAREER_FAIR = 'career_fair',
  DIRECT_APPLICATION = 'direct_application',
  OTHER = 'other'
}

@Entity('job_applications')
export class JobApplication {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 100 })
  @IsNotEmpty({ message: 'First name is required' })
  @Length(2, 100, { message: 'First name must be between 2 and 100 characters' })
  firstName: string;

  @Column({ type: 'varchar', length: 100 })
  @IsNotEmpty({ message: 'Last name is required' })
  @Length(2, 100, { message: 'Last name must be between 2 and 100 characters' })
  lastName: string;

  @Column({ type: 'varchar', length: 255 })
  @IsNotEmpty({ message: 'Email is required' })
  @IsEmail({}, { message: 'Invalid email format' })
  email: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  @IsOptional()
  phone: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  address: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  city: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  country: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  @IsOptional()
  postalCode: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  coverLetter: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  @IsOptional()
  resumeUrl: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  resumeFileName: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  @IsOptional()
  portfolioUrl: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  @IsOptional()
  linkedinUrl: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  @IsOptional()
  githubUrl: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  @IsOptional()
  websiteUrl: string;

  @Column({
    type: 'enum',
    enum: ApplicationStatus,
    default: ApplicationStatus.SUBMITTED
  })
  @IsEnum(ApplicationStatus, { message: 'Invalid application status' })
  status: ApplicationStatus;

  @Column({
    type: 'enum',
    enum: ApplicationSource,
    default: ApplicationSource.COMPANY_WEBSITE
  })
  @IsEnum(ApplicationSource, { message: 'Invalid application source' })
  source: ApplicationSource;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  referredBy: string;

  @Column({ type: 'int', default: 0 })
  @IsNumber({}, { message: 'Rating must be a number' })
  @Min(0, { message: 'Rating must be at least 0' })
  @Max(5, { message: 'Rating must be at most 5' })
  rating: number;

  @Column({ type: 'boolean', default: false })
  isStarred: boolean;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  notes: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  internalNotes: string;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  customFields: Record<string, any>;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  skills: string[];

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  experience: Array<{
    company: string;
    position: string;
    startDate: string;
    endDate?: string;
    current: boolean;
    description?: string;
  }>;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  education: Array<{
    institution: string;
    degree: string;
    field: string;
    startDate: string;
    endDate?: string;
    gpa?: string;
  }>;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  certifications: Array<{
    name: string;
    issuer: string;
    issueDate: string;
    expiryDate?: string;
    credentialId?: string;
  }>;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  languages: Array<{
    language: string;
    proficiency: string; // 'basic', 'intermediate', 'advanced', 'native'
  }>;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Expected salary must be a number' })
  expectedSalary: number;

  @Column({ type: 'varchar', length: 50, nullable: true })
  @IsOptional()
  salaryCurrency: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  @IsOptional()
  salaryPeriod: string;

  @Column({ type: 'boolean', default: true })
  isAvailable: boolean;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  availableStartDate: Date;

  @Column({ type: 'int', nullable: true })
  @IsOptional()
  noticePeriodDays: number;

  @Column({ type: 'boolean', default: false })
  requiresVisa: boolean;

  @Column({ type: 'boolean', default: false })
  willingToRelocate: boolean;

  @Column({ type: 'boolean', default: false })
  willingToTravel: boolean;

  @Column({ type: 'timestamp', nullable: true })
  statusChangedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  lastViewedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  lastContactedAt: Date;

  // Relations
  @ManyToOne(() => JobPosting, jobPosting => jobPosting.applications, { nullable: false })
  @JoinColumn({ name: 'jobPostingId' })
  jobPosting: JobPosting;

  @Column({ type: 'int' })
  jobPostingId: number;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'assignedToId' })
  assignedTo: User;

  @Column({ type: 'uuid', nullable: true })
  assignedToId: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'lastReviewedById' })
  lastReviewedBy: User;

  @Column({ type: 'uuid', nullable: true })
  lastReviewedById: string;

  @OneToMany(() => Interview, interview => interview.application, { cascade: true })
  interviews: Interview[];

  @OneToMany(() => ApplicationEvaluation, evaluation => evaluation.application, { cascade: true })
  evaluations: ApplicationEvaluation[];

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Virtual properties
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }

  get statusDisplayName(): string {
    return this.status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  get sourceDisplayName(): string {
    return this.source.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  get daysSinceApplication(): number {
    const now = new Date();
    const diffTime = now.getTime() - this.createdAt.getTime();
    return Math.floor(diffTime / (1000 * 60 * 60 * 24));
  }

  get totalExperienceYears(): number {
    if (!this.experience || this.experience.length === 0) return 0;
    
    let totalMonths = 0;
    this.experience.forEach(exp => {
      const startDate = new Date(exp.startDate);
      const endDate = exp.current ? new Date() : new Date(exp.endDate || exp.startDate);
      const diffTime = endDate.getTime() - startDate.getTime();
      const months = diffTime / (1000 * 60 * 60 * 24 * 30.44); // Average days per month
      totalMonths += months;
    });
    
    return Math.round(totalMonths / 12 * 10) / 10; // Round to 1 decimal place
  }

  get averageEvaluationRating(): number {
    if (!this.evaluations || this.evaluations.length === 0) return 0;
    const totalRating = this.evaluations.reduce((sum, evaluation) => sum + evaluation.overallRating, 0);
    return totalRating / this.evaluations.length;
  }

  get hasInterviews(): boolean {
    return this.interviews && this.interviews.length > 0;
  }

  get latestInterview(): Interview | null {
    if (!this.interviews || this.interviews.length === 0) return null;
    return this.interviews.sort((a, b) => b.startTime.getTime() - a.startTime.getTime())[0];
  }

  get isInProgress(): boolean {
    return ![
      ApplicationStatus.SUBMITTED,
      ApplicationStatus.HIRED,
      ApplicationStatus.REJECTED,
      ApplicationStatus.WITHDRAWN,
      ApplicationStatus.OFFER_DECLINED
    ].includes(this.status);
  }

  get requiresAction(): boolean {
    return [
      ApplicationStatus.SUBMITTED,
      ApplicationStatus.UNDER_REVIEW,
      ApplicationStatus.OFFER_PENDING
    ].includes(this.status);
  }

  get expectedSalaryFormatted(): string {
    if (!this.expectedSalary) return 'Not specified';
    return `${this.salaryCurrency || '$'}${this.expectedSalary.toLocaleString()} ${this.salaryPeriod || 'per year'}`;
  }
}
