import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn
} from 'typeorm';
import { Employee } from './Employee';

@Entity('employee_contacts')
export class EmployeeContact {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  mobileNumber: string;

  @Column({ nullable: true })
  officialNumber: string;

  @Column({ type: 'text', nullable: true })
  officialEmail: string;

  @Column({ type: 'text', nullable: true })
  personalEmail: string;

  @Column({ type: 'text', nullable: true })
  permanentAddress: string;

  @Column({ type: 'text', nullable: true })
  currentAddress: string;

  @Column({ type: 'text', nullable: true })
  emergencyContactName: string;

  @Column({ type: 'text', nullable: true })
  emergencyContactPhone: string;

  @Column({ type: 'text', nullable: true })
  emergencyContactRelationship: string;

  @Column({ type: 'text', nullable: true })
  linkedinProfile: string;
  
  @Column({ type: 'text', nullable: true })
  otherSocialProfiles: string;

  // Relation to Employee
  @OneToOne(() => Employee, employee => employee.contact, { onDelete: 'CASCADE' })
  @JoinColumn()
  employee: Employee;

  // System columns
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 