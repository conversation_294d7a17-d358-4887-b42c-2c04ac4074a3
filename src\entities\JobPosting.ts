import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn
} from 'typeorm';
import { IsNotEmpty, IsOptional, IsEnum, IsDateString, Length, IsNumber, Min, Max } from 'class-validator';
import { User } from './User';
import { JobApplication } from './JobApplication';

export enum JobType {
  FULL_TIME = 'full_time',
  PART_TIME = 'part_time',
  CONTRACT = 'contract',
  TEMPORARY = 'temporary',
  INTERNSHIP = 'internship',
  FREELANCE = 'freelance'
}

export enum ExperienceLevel {
  ENTRY_LEVEL = 'entry_level',
  JUNIOR = 'junior',
  MID_LEVEL = 'mid_level',
  SENIOR = 'senior',
  LEAD = 'lead',
  MANAGER = 'manager',
  DIRECTOR = 'director',
  EXECUTIVE = 'executive'
}

export enum WorkLocation {
  ONSITE = 'onsite',
  REMOTE = 'remote',
  HYBRID = 'hybrid'
}

export enum JobPostingStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  PAUSED = 'paused',
  CLOSED = 'closed',
  CANCELLED = 'cancelled'
}

@Entity('job_postings')
export class JobPosting {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255 })
  @IsNotEmpty({ message: 'Job title is required' })
  @Length(2, 255, { message: 'Job title must be between 2 and 255 characters' })
  title: string;

  @Column({ type: 'text' })
  @IsNotEmpty({ message: 'Job description is required' })
  description: string;

  @Column({ type: 'text' })
  @IsNotEmpty({ message: 'Job requirements are required' })
  requirements: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  responsibilities: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  benefits: string;

  @Column({ type: 'varchar', length: 100 })
  @IsNotEmpty({ message: 'Department is required' })
  department: string;

  @Column({ type: 'varchar', length: 100 })
  @IsNotEmpty({ message: 'Location is required' })
  location: string;

  @Column({
    type: 'enum',
    enum: JobType,
    default: JobType.FULL_TIME
  })
  @IsEnum(JobType, { message: 'Invalid job type' })
  jobType: JobType;

  @Column({
    type: 'enum',
    enum: ExperienceLevel,
    default: ExperienceLevel.MID_LEVEL
  })
  @IsEnum(ExperienceLevel, { message: 'Invalid experience level' })
  experienceLevel: ExperienceLevel;

  @Column({
    type: 'enum',
    enum: WorkLocation,
    default: WorkLocation.ONSITE
  })
  @IsEnum(WorkLocation, { message: 'Invalid work location' })
  workLocation: WorkLocation;

  @Column({
    type: 'enum',
    enum: JobPostingStatus,
    default: JobPostingStatus.DRAFT
  })
  @IsEnum(JobPostingStatus, { message: 'Invalid job posting status' })
  status: JobPostingStatus;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Minimum salary must be a number' })
  @Min(0, { message: 'Minimum salary must be positive' })
  minSalary: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Maximum salary must be a number' })
  @Min(0, { message: 'Maximum salary must be positive' })
  maxSalary: number;

  @Column({ type: 'varchar', length: 50, nullable: true })
  @IsOptional()
  salaryCurrency: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  @IsOptional()
  salaryPeriod: string; // 'hourly', 'monthly', 'yearly'

  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Number of positions must be a number' })
  @Min(1, { message: 'Number of positions must be at least 1' })
  numberOfPositions: number;

  @Column({ type: 'timestamp', nullable: true })
  @IsOptional()
  @IsDateString({}, { message: 'Invalid application deadline' })
  applicationDeadline: Date;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  requiredSkills: string[];

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  preferredSkills: string[];

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  qualifications: string[];

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  applicationInstructions: string;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  customFields: Record<string, any>;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'boolean', default: false })
  isUrgent: boolean;

  @Column({ type: 'boolean', default: false })
  isFeatured: boolean;

  @Column({ type: 'boolean', default: true })
  allowExternalApplications: boolean;

  @Column({ type: 'varchar', length: 500, nullable: true })
  @IsOptional()
  externalApplicationUrl: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  internalNotes: string;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  tags: string[];

  @Column({ type: 'int', default: 0 })
  viewCount: number;

  @Column({ type: 'int', default: 0 })
  applicationCount: number;

  @Column({ type: 'timestamp', nullable: true })
  publishedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  closedAt: Date;

  // Relations
  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'createdById' })
  createdBy: User;

  @Column({ type: 'uuid' })
  createdById: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'hiringManagerId' })
  hiringManager: User;

  @Column({ type: 'uuid', nullable: true })
  hiringManagerId: string;

  @OneToMany(() => JobApplication, application => application.jobPosting, { cascade: true })
  applications: JobApplication[];

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Virtual properties
  get isExpired(): boolean {
    return this.applicationDeadline ? new Date() > this.applicationDeadline : false;
  }

  get daysUntilDeadline(): number {
    if (!this.applicationDeadline) return -1;
    const now = new Date();
    const diffTime = this.applicationDeadline.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  get salaryRange(): string {
    if (!this.minSalary && !this.maxSalary) return 'Not specified';
    if (this.minSalary && this.maxSalary) {
      return `${this.salaryCurrency || '$'}${this.minSalary.toLocaleString()} - ${this.salaryCurrency || '$'}${this.maxSalary.toLocaleString()}`;
    }
    if (this.minSalary) {
      return `From ${this.salaryCurrency || '$'}${this.minSalary.toLocaleString()}`;
    }
    return `Up to ${this.salaryCurrency || '$'}${this.maxSalary.toLocaleString()}`;
  }

  get statusDisplayName(): string {
    return this.status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  get jobTypeDisplayName(): string {
    return this.jobType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  get experienceLevelDisplayName(): string {
    return this.experienceLevel.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  get workLocationDisplayName(): string {
    return this.workLocation.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  get isPublished(): boolean {
    return this.status === JobPostingStatus.PUBLISHED;
  }

  get canReceiveApplications(): boolean {
    return this.isPublished && this.isActive && !this.isExpired;
  }

  get urgencyLevel(): string {
    if (this.isUrgent) return 'Urgent';
    if (this.daysUntilDeadline <= 7 && this.daysUntilDeadline > 0) return 'Closing Soon';
    return 'Normal';
  }
}
