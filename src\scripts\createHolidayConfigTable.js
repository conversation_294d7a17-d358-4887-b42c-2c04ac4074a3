const mysql = require('mysql2/promise');

async function createHolidayConfigTable() {
  let connection;
  
  try {
    console.log('✅ Connecting to database...');
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'ims_db'
    });

    console.log('🔍 Checking if holiday_configurations table exists...');
    const [tables] = await connection.execute(
      'SHOW TABLES LIKE "holiday_configurations"'
    );

    if (tables.length === 0) {
      console.log('🏗️ Creating holiday_configurations table...');
      
      const createTableSQL = `
        CREATE TABLE holiday_configurations (
          id INT AUTO_INCREMENT PRIMARY KEY,
          organizationId VARCHAR(100) NOT NULL DEFAULT 'default',
          name VARCHAR(255) NOT NULL,
          weekendDays JSON NOT NULL DEFAULT '[0, 6]',
          timezone VARCHAR(100) DEFAULT 'UTC',
          workingHours JSON DEFAULT '{"start": "09:00", "end": "17:00"}',
          holidaySettings JSON DEFAULT '{}',
          isActive BOOLEAN DEFAULT TRUE,
          createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          createdBy VARCHAR(100) DEFAULT NULL,
          updatedBy VARCHAR(100) DEFAULT NULL,
          
          INDEX idx_organization (organizationId),
          INDEX idx_active (isActive)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `;

      await connection.execute(createTableSQL);
      console.log('✅ holiday_configurations table created successfully');

      // Insert default configuration
      console.log('📝 Inserting default holiday configuration...');
      const insertDefaultSQL = `
        INSERT INTO holiday_configurations (organizationId, name, weekendDays, timezone, workingHours, holidaySettings)
        VALUES (?, ?, ?, ?, ?, ?)
      `;

      await connection.execute(insertDefaultSQL, [
        'default',
        'Default Holiday Configuration',
        JSON.stringify([0, 6]), // Sunday, Saturday
        'UTC',
        JSON.stringify({ start: '09:00', end: '17:00' }),
        JSON.stringify({ 
          allowOptionalHolidays: true,
          maxOptionalPerYear: 5,
          requireApproval: false
        })
      ]);

      console.log('✅ Default configuration inserted');
    } else {
      console.log('✅ holiday_configurations table already exists');
    }

    // Verify table structure
    console.log('🔍 Verifying table structure...');
    const [columns] = await connection.execute(
      'DESCRIBE holiday_configurations'
    );

    console.log('📋 Table columns:');
    columns.forEach(col => {
      console.log(`   • ${col.Field} (${col.Type}) - ${col.Null === 'YES' ? 'nullable' : 'not null'}`);
    });

    // Check existing configurations
    const [configs] = await connection.execute(
      'SELECT * FROM holiday_configurations WHERE isActive = TRUE'
    );

    console.log(`\n📊 Found ${configs.length} active configuration(s)`);
    configs.forEach(config => {
      console.log(`   • ${config.name} (${config.organizationId})`);
    });

  } catch (error) {
    console.error('❌ Error creating holiday configuration table:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔐 Database connection closed');
    }
  }
}

// Run the script
createHolidayConfigTable(); 