import React, { useState, useEffect } from 'react';
import { Download, FileText, BarChart3, Users, Calendar, CheckCircle, TrendingUp, Plus, Edit, Trash2 } from 'lucide-react';
import { LeaveRequest, LeaveStatus } from '../../../types/attendance';

interface EmployeeLeaveData {
  employeeId: number;
  employeeName: string;
  employeeCode: string;
  department: string;
  position: string;
  leaveBalances: any[];
  recentRequests: LeaveRequest[];
  totalDaysUsed: number;
  totalDaysRemaining: number;
}

interface CustomLeaveType {
  id: string;
  name: string;
  maxDaysPerYear: number;
  carryForwardAllowed: boolean;
  carryForwardLimit?: number;
}

interface LeaveReportsAnalyticsProps {
  allLeaveRequests: LeaveRequest[];
  employeeLeaveData: EmployeeLeaveData[];
  customLeaveTypes?: CustomLeaveType[];
  showLeaveTypeModal?: boolean;
  setShowLeaveTypeModal?: (show: boolean) => void;
  showLeaveTypesTab?: boolean;
}

const LeaveReportsAnalytics: React.FC<LeaveReportsAnalyticsProps> = ({
  allLeaveRequests,
  employeeLeaveData,
  customLeaveTypes = [],
  showLeaveTypeModal = false,
  setShowLeaveTypeModal,
  showLeaveTypesTab = false
}) => {
  const calculateDays = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = end.getTime() - start.getTime();
    return Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Reports & Analytics</h3>
        <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 flex items-center text-sm">
          <Download className="h-4 w-4 mr-2" />
          Generate Report
        </button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600">Total Leave Days</p>
              <p className="text-2xl font-bold text-blue-900">
                {allLeaveRequests.reduce((sum, req) => sum + calculateDays(req.startDate, req.endDate), 0)}
              </p>
            </div>
            <Calendar className="h-8 w-8 text-blue-600" />
          </div>
        </div>
        <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4 border border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-600">Approved Requests</p>
              <p className="text-2xl font-bold text-green-900">
                {allLeaveRequests.filter(r => r.status === LeaveStatus.APPROVED).length}
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
        </div>
        <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-4 border border-yellow-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-yellow-600">Average per Employee</p>
              <p className="text-2xl font-bold text-yellow-900">
                {employeeLeaveData.length > 0 ? 
                  Math.round(allLeaveRequests.reduce((sum, req) => sum + calculateDays(req.startDate, req.endDate), 0) / employeeLeaveData.length) 
                  : 0}
              </p>
            </div>
            <Users className="h-8 w-8 text-yellow-600" />
          </div>
        </div>
        <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-4 border border-purple-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-600">This Month</p>
              <p className="text-2xl font-bold text-purple-900">
                {allLeaveRequests.filter(r => 
                  new Date(r.startDate).getMonth() === new Date().getMonth()
                ).length}
              </p>
            </div>
            <TrendingUp className="h-8 w-8 text-purple-600" />
          </div>
        </div>
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Department wise leave distribution */}
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Department-wise Leave Distribution</h4>
          <div className="space-y-3">
            {['Engineering', 'Marketing', 'Sales', 'HR', 'Finance'].map((dept) => {
              const deptRequests = allLeaveRequests.filter(req => 
                employeeLeaveData.find(emp => emp.employeeId === req.employeeId)?.department === dept
              );
              const percentage = allLeaveRequests.length > 0 ? (deptRequests.length / allLeaveRequests.length) * 100 : 0;
              
              return (
                <div key={dept} className="flex items-center">
                  <div className="flex-1">
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-600">{dept}</span>
                      <span className="font-medium">{deptRequests.length} requests ({percentage.toFixed(1)}%)</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-indigo-600 h-2 rounded-full" 
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Monthly trends */}
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Monthly Leave Trends</h4>
          <div className="space-y-3">
            {['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'].map((month, index) => {
              const monthRequests = allLeaveRequests.filter(req => 
                new Date(req.startDate).getMonth() === index
              );
              const maxRequests = Math.max(...Array.from({length: 6}, (_, i) => 
                allLeaveRequests.filter(req => new Date(req.startDate).getMonth() === i).length
              ));
              const percentage = maxRequests > 0 ? (monthRequests.length / maxRequests) * 100 : 0;
              
              return (
                <div key={month} className="flex items-center">
                  <div className="w-8 text-xs text-gray-600">{month}</div>
                  <div className="flex-1 ml-3">
                    <div className="flex justify-between text-sm mb-1">
                      <span className="font-medium">{monthRequests.length} requests</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full" 
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Report Actions */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="text-lg font-medium text-gray-900 mb-4">Generate Reports</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="p-4 border border-gray-200 rounded-lg bg-white hover:shadow-md transition-shadow text-left">
            <FileText className="h-6 w-6 text-blue-600 mb-2" />
            <h5 className="font-medium text-gray-900">Leave Summary Report</h5>
            <p className="text-sm text-gray-500 mt-1">Comprehensive leave usage report by department</p>
          </button>
          <button className="p-4 border border-gray-200 rounded-lg bg-white hover:shadow-md transition-shadow text-left">
            <BarChart3 className="h-6 w-6 text-green-600 mb-2" />
            <h5 className="font-medium text-gray-900">Analytics Dashboard</h5>
            <p className="text-sm text-gray-500 mt-1">Visual analytics with charts and trends</p>
          </button>
          <button className="p-4 border border-gray-200 rounded-lg bg-white hover:shadow-md transition-shadow text-left">
            <Users className="h-6 w-6 text-purple-600 mb-2" />
            <h5 className="font-medium text-gray-900">Employee Report</h5>
            <p className="text-sm text-gray-500 mt-1">Individual employee leave history and balances</p>
          </button>
        </div>
      </div>
    </div>
  );
};

export default LeaveReportsAnalytics; 