import { SystemLog } from '../types';
import { extractIpAddress } from './geoLocation';

export interface SessionInfo {
  id: string;
  user: string;
  ip: string | null;
  startTime: string;
  endTime: string | null;
  logIds: string[];
  actions: string[];
  threatScore: number;
  anomalies: Anomaly[];
}

export interface Anomaly {
  type: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  relatedLogIds: string[];
  score: number;
}

export interface SecurityInsight {
  totalSessions: number;
  activeSessions: number;
  totalAnomalies: number;
  highSeverityAnomalies: number;
  topThreatenedUsers: { user: string; threatScore: number }[];
  topSuspiciousIPs: { ip: string; threatScore: number }[];
  recentAnomalies: Anomaly[];
}

// Define time window for session tracking (in milliseconds)
const SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes

/**
 * Extract sessions from logs
 */
export const extractSessions = (logs: SystemLog[]): SessionInfo[] => {
  // Sort logs by timestamp
  const sortedLogs = [...logs].sort((a, b) => 
    new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  );
  
  const sessions: Map<string, SessionInfo> = new Map();
  const userIpMap: Map<string, string> = new Map();
  const sessionsByUserTime: Map<string, string> = new Map();
  
  // Process logs to identify sessions
  sortedLogs.forEach(log => {
    const timestamp = new Date(log.timestamp).getTime();
    const ip = extractIpAddress(log.details) || extractIpAddress(log.action);
    
    // Create a key for user session tracking
    const userKey = log.user;
    
    // Check if we have an active session for this user
    const existingSessionId = sessionsByUserTime.get(userKey);
    
    if (existingSessionId && sessions.has(existingSessionId)) {
      const session = sessions.get(existingSessionId)!;
      const sessionEndTime = session.endTime 
        ? new Date(session.endTime).getTime() 
        : new Date(session.startTime).getTime();
      
      // If this log is within the session timeout, add to existing session
      if (timestamp - sessionEndTime < SESSION_TIMEOUT) {
        session.endTime = log.timestamp;
        session.logIds.push(log.id);
        session.actions.push(log.action);
        
        // Update IP if it changed
        if (ip && (!session.ip || session.ip !== ip)) {
          // IP change within a session is suspicious - flag as anomaly
          if (session.ip && session.ip !== ip) {
            session.anomalies.push({
              type: 'ip_change',
              description: `IP changed from ${session.ip} to ${ip} within session`,
              severity: 'high',
              relatedLogIds: [log.id],
              score: 70
            });
            
            // Increase threat score
            session.threatScore += 70;
          }
          
          session.ip = ip;
        }
        
        // Continue using this session
        sessionsByUserTime.set(userKey, existingSessionId);
      } else {
        // Create a new session
        createNewSession(userKey, log, ip, sessions, sessionsByUserTime);
      }
    } else {
      // Create a new session
      createNewSession(userKey, log, ip, sessions, sessionsByUserTime);
    }
    
    // Track user-IP mappings for anomaly detection
    if (ip) {
      const knownIp = userIpMap.get(userKey);
      if (!knownIp) {
        userIpMap.set(userKey, ip);
      }
    }
  });
  
  // Calculate threat scores for each session
  sessions.forEach(session => {
    calculateSessionThreatScore(session, sortedLogs, userIpMap);
  });
  
  return Array.from(sessions.values());
};

/**
 * Create a new session from a log entry
 */
const createNewSession = (
  userKey: string,
  log: SystemLog,
  ip: string | null,
  sessions: Map<string, SessionInfo>,
  sessionsByUserTime: Map<string, string>
) => {
  const sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  
  const newSession: SessionInfo = {
    id: sessionId,
    user: log.user,
    ip: ip,
    startTime: log.timestamp,
    endTime: log.timestamp,
    logIds: [log.id],
    actions: [log.action],
    threatScore: 0,
    anomalies: []
  };
  
  sessions.set(sessionId, newSession);
  sessionsByUserTime.set(userKey, sessionId);
};

/**
 * Calculate threat score for a session based on patterns and behaviors
 */
const calculateSessionThreatScore = (
  session: SessionInfo,
  allLogs: SystemLog[],
  userIpMap: Map<string, string>
) => {
  let threatScore = 0;
  const sessionLogs = allLogs.filter(log => session.logIds.includes(log.id));
  
  // Check for anomalies
  
  // 1. Unusual login times (assuming working hours are 8 AM to 6 PM)
  sessionLogs.filter(log => log.action.toLowerCase().includes('login')).forEach(log => {
    const hour = new Date(log.timestamp).getHours();
    if (hour < 8 || hour > 18) {
      session.anomalies.push({
        type: 'unusual_time',
        description: `Login at unusual hour (${hour}:00)`,
        severity: 'medium',
        relatedLogIds: [log.id],
        score: 40
      });
      threatScore += 40;
    }
  });
  
  // 2. Failed login attempts
  const failedLogins = sessionLogs.filter(log => 
    log.action.toLowerCase().includes('login') && 
    log.action.toLowerCase().includes('fail')
  );
  
  if (failedLogins.length > 2) {
    session.anomalies.push({
      type: 'multiple_failed_logins',
      description: `${failedLogins.length} failed login attempts`,
      severity: failedLogins.length > 5 ? 'high' : 'medium',
      relatedLogIds: failedLogins.map(log => log.id),
      score: 30 + (failedLogins.length * 10)
    });
    threatScore += 30 + (failedLogins.length * 10);
  }
  
  // 3. Unusual IP address for this user
  if (session.ip) {
    const userUsualIp = userIpMap.get(session.user);
    if (userUsualIp && userUsualIp !== session.ip) {
      // Check if IP is known to be used by this user
      const userIpFrequency = allLogs
        .filter(log => log.user === session.user)
        .filter(log => {
          const logIp = extractIpAddress(log.details) || extractIpAddress(log.action);
          return logIp === session.ip;
        })
        .length;
      
      if (userIpFrequency <= 2) {
        session.anomalies.push({
          type: 'unusual_ip',
          description: `Unusual IP address (${session.ip}) for user`,
          severity: 'high',
          relatedLogIds: session.logIds.slice(0, 1),
          score: 60
        });
        threatScore += 60;
      }
    }
  }
  
  // 4. Privilege escalation attempts
  const privilegeEscalation = sessionLogs.filter(log => 
    log.action.toLowerCase().includes('admin') ||
    log.action.toLowerCase().includes('privilege') ||
    log.action.toLowerCase().includes('sudo') ||
    log.action.toLowerCase().includes('root')
  );
  
  if (privilegeEscalation.length > 0) {
    session.anomalies.push({
      type: 'privilege_escalation',
      description: 'Potential privilege escalation attempt',
      severity: 'critical',
      relatedLogIds: privilegeEscalation.map(log => log.id),
      score: 90
    });
    threatScore += 90;
  }
  
  // 5. High volume of actions in a short period
  if (session.logIds.length > 20) {
    const startTime = new Date(session.startTime).getTime();
    const endTime = session.endTime ? new Date(session.endTime).getTime() : Date.now();
    const durationMinutes = (endTime - startTime) / (1000 * 60);
    const actionsPerMinute = session.logIds.length / durationMinutes;
    
    if (actionsPerMinute > 5) {
      session.anomalies.push({
        type: 'high_activity',
        description: `High activity rate (${actionsPerMinute.toFixed(1)} actions/min)`,
        severity: 'medium',
        relatedLogIds: [],
        score: 50
      });
      threatScore += 50;
    }
  }
  
  // 6. Sensitive data access
  const sensitiveAccess = sessionLogs.filter(log => 
    log.action.toLowerCase().includes('password') ||
    log.action.toLowerCase().includes('key') ||
    log.action.toLowerCase().includes('secret') ||
    log.action.toLowerCase().includes('token') ||
    log.action.toLowerCase().includes('config')
  );
  
  if (sensitiveAccess.length > 0) {
    session.anomalies.push({
      type: 'sensitive_access',
      description: 'Access to sensitive data',
      severity: 'high',
      relatedLogIds: sensitiveAccess.map(log => log.id),
      score: 70
    });
    threatScore += 70;
  }
  
  // 7. Suspicious action patterns
  const suspiciousActions = sessionLogs.filter(log => 
    log.action.toLowerCase().includes('delete') ||
    log.action.toLowerCase().includes('drop') ||
    log.action.toLowerCase().includes('truncate') ||
    log.action.toLowerCase().includes('remove') ||
    log.action.toLowerCase().includes('download')
  );
  
  if (suspiciousActions.length > 2) {
    session.anomalies.push({
      type: 'suspicious_actions',
      description: 'Multiple suspicious actions performed',
      severity: 'high',
      relatedLogIds: suspiciousActions.map(log => log.id),
      score: 80
    });
    threatScore += 80;
  }
  
  // Cap the threat score at 100
  session.threatScore = Math.min(100, threatScore);
};

/**
 * Get security insights from logs and sessions
 */
export const getSecurityInsights = (logs: SystemLog[], sessions: SessionInfo[]): SecurityInsight => {
  // Calculate active sessions (those with activity in the last 30 minutes)
  const now = Date.now();
  const activeSessions = sessions.filter(session => {
    const lastActivity = session.endTime ? new Date(session.endTime).getTime() : 0;
    return (now - lastActivity) < SESSION_TIMEOUT;
  });
  
  // Collect all anomalies
  const allAnomalies = sessions.flatMap(session => session.anomalies);
  const highSeverityAnomalies = allAnomalies.filter(anomaly => 
    anomaly.severity === 'high' || anomaly.severity === 'critical'
  );
  
  // Calculate most threatened users
  const userThreatMap = new Map<string, number>();
  sessions.forEach(session => {
    const currentScore = userThreatMap.get(session.user) || 0;
    userThreatMap.set(session.user, currentScore + session.threatScore);
  });
  
  const topThreatenedUsers = Array.from(userThreatMap.entries())
    .map(([user, threatScore]) => ({ user, threatScore }))
    .sort((a, b) => b.threatScore - a.threatScore)
    .slice(0, 3);
  
  // Calculate suspicious IPs
  const ipThreatMap = new Map<string, number>();
  sessions.forEach(session => {
    if (session.ip) {
      const currentScore = ipThreatMap.get(session.ip) || 0;
      ipThreatMap.set(session.ip, currentScore + session.threatScore);
    }
  });
  
  const topSuspiciousIPs = Array.from(ipThreatMap.entries())
    .map(([ip, threatScore]) => ({ ip, threatScore }))
    .sort((a, b) => b.threatScore - a.threatScore)
    .slice(0, 3);
  
  // Get recent anomalies (last 24 hours)
  const oneDayAgo = now - (24 * 60 * 60 * 1000);
  const recentAnomalies = allAnomalies.filter(anomaly => {
    const logId = anomaly.relatedLogIds[0];
    if (!logId) return false;
    
    const log = logs.find(l => l.id === logId);
    if (!log) return false;
    
    return new Date(log.timestamp).getTime() > oneDayAgo;
  }).slice(0, 5);
  
  return {
    totalSessions: sessions.length,
    activeSessions: activeSessions.length,
    totalAnomalies: allAnomalies.length,
    highSeverityAnomalies: highSeverityAnomalies.length,
    topThreatenedUsers,
    topSuspiciousIPs,
    recentAnomalies
  };
};
