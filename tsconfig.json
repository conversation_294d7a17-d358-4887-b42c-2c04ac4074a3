{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["es2020", "dom"], "jsx": "react", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "moduleResolution": "node", "baseUrl": ".", "paths": {"src/*": ["./src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}