const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkEmployeeTable() {
  const {
    DB_HOST = 'localhost',
    DB_PORT = '3306',
    DB_USER = 'root',
    DB_PASSWORD = 'root',
    DB_NAME = 'ims_db'
  } = process.env;

  console.log('Connecting to database...');
  console.log(`Host: ${DB_HOST}, Port: ${DB_PORT}, Database: ${DB_NAME}`);

  try {
    // Create connection
    const connection = await mysql.createConnection({
      host: DB_HOST,
      port: parseInt(DB_PORT, 10),
      user: DB_USER,
      password: DB_PASSWORD,
      database: DB_NAME
    });

    console.log('Connected to database successfully');

    // Check if employees table exists
    console.log('Checking if employees table exists...');
    const [tableResult] = await connection.query(
      `SELECT TABLE_NAME FROM information_schema.TABLES 
       WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'employees'`,
      [DB_NAME]
    );

    if (tableResult.length === 0) {
      console.error('Employees table does not exist in the database');
      await connection.end();
      return;
    }

    console.log('Employees table exists');

    // Check table structure
    console.log('\nChecking table structure:');
    const [columns] = await connection.query(
      `SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_KEY 
       FROM information_schema.COLUMNS 
       WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'employees'`,
      [DB_NAME]
    );

    console.log('\nTable structure:');
    columns.forEach(col => {
      console.log(`- ${col.COLUMN_NAME} (${col.DATA_TYPE}, ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}${col.COLUMN_KEY === 'PRI' ? ', PRIMARY KEY' : ''})`);
    });

    // Count employees
    console.log('\nCounting employees:');
    const [countResult] = await connection.query('SELECT COUNT(*) as count FROM employees');
    console.log(`Total employees: ${countResult[0].count}`);

    // Get sample employees if any exist
    if (countResult[0].count > 0) {
      console.log('\nSample employee records:');
      const [employees] = await connection.query('SELECT * FROM employees LIMIT 3');
      
      employees.forEach((emp, index) => {
        console.log(`\nEmployee ${index + 1}:`);
        Object.keys(emp).forEach(key => {
          // For complex fields, check if they're valid JSON and parse them
          if (typeof emp[key] === 'string' && 
              ['educationEntries', 'experienceEntries', 'deviceEntries', 
               'children', 'dependents', 'documents', 'requiredDocuments', 
               'projectEntries'].includes(key)) {
            try {
              const parsedValue = JSON.parse(emp[key]);
              console.log(`${key}: ${JSON.stringify(parsedValue).substring(0, 50)}...`);
            } catch (e) {
              console.log(`${key}: ${emp[key]}`);
            }
          } else {
            console.log(`${key}: ${emp[key]}`);
          }
        });
      });
    }

    await connection.end();
    console.log('\nConnection closed');
  } catch (error) {
    console.error('Error:', error);
  }
}

checkEmployeeTable(); 