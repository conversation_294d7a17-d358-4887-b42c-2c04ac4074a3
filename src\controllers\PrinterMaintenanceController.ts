import { Request, Response } from 'express';
import { getRepository, FindOneOptions } from 'typeorm';
import { PrinterMaintenance } from '../entities/PrinterMaintenance';
import { Asset, AssetType, AssetStatus } from '../entities/Asset';
import { Vendor } from '../entities/Vendor';
import { User } from '../entities/User';
import { upload } from '../middleware/upload';
import { generatePDF } from '../utils/pdfGenerator';
import fs from 'fs';
import path from 'path';
import { Like, MoreThanOrEqual, LessThanOrEqual } from 'typeorm';

// Add standardizeStatus helper function
function standardizeStatus(status: string | null | undefined): string {
  if (!status) return 'Pending';
  
  // Normalize status string
  const normalizedStatus = status.trim().toLowerCase();
  
  // Map to standard values
  if (normalizedStatus.includes('complete') || normalizedStatus.includes('done')) {
    return 'Completed';
  } else if (normalizedStatus.includes('pend') || normalizedStatus.includes('wait')) {
    return 'Pending';
  } else if (normalizedStatus.includes('progress') || normalizedStatus.includes('ongoing')) {
    return 'In Progress';
  } else if (normalizedStatus.includes('cancel')) {
    return 'Cancelled';
  } else if (normalizedStatus.includes('schedule')) {
    return 'Scheduled';
  }
  
  // Default to the original value if no match
  return status;
}

export class PrinterMaintenanceController {
  
  // Record a new printer maintenance visit
  async recordMaintenance(req: Request, res: Response) {
    try {
      const maintenanceRepo = getRepository(PrinterMaintenance);
      const assetRepo = getRepository(Asset);
      const vendorRepo = getRepository(Vendor);
      const userRepo = getRepository(User);
      
      const { 
        asset_id: assetId, 
        vendor_id: vendorId, 
        service_date: serviceDate, 
        service_description: serviceDescription, 
        invoice_amount: invoiceAmount,
        invoice_number: invoiceNumber,
        assignee_id: assigneeId,
        parts_replaced: partsReplaced,
        toner_replaced: tonerReplaced,
        notes,
        next_service_date: nextServiceDate
      } = req.body;

      // Validate that the asset is a printer
      const asset = await assetRepo.findOne({ where: { id: assetId } as any });
      if (!asset) {
        return res.status(404).json({ message: 'Asset not found' });
      }
      
      if (asset.assetType !== AssetType.Printing) {
        return res.status(400).json({ message: 'Asset is not a printer or copier' });
      }

      // Validate vendor
      const vendor = await vendorRepo.findOne({ where: { id: vendorId } as any });
      if (!vendor) {
        return res.status(404).json({ message: 'Vendor not found' });
      }

      // Validate assignee
      const assignee = await userRepo.findOne({ where: { id: assigneeId } as any });
      if (!assignee) {
        return res.status(404).json({ message: 'Assignee user not found' });
      }

      // Create new maintenance record
      const maintenance = new PrinterMaintenance();
      maintenance.asset = asset;
      maintenance.vendor = vendor;
      maintenance.serviceDate = new Date(serviceDate);
      maintenance.serviceDescription = serviceDescription;
      maintenance.invoiceAmount = invoiceAmount;
      maintenance.invoiceNumber = invoiceNumber;
      maintenance.assignee = assignee;
      maintenance.partsReplaced = partsReplaced || [];
      maintenance.tonerReplaced = tonerReplaced || [];
      maintenance.notes = notes;
      maintenance.serviceStatus = 'Completed';
      
      if (nextServiceDate) {
        maintenance.nextServiceDate = new Date(nextServiceDate);
      }

      // Save maintenance record
      const savedMaintenance = await maintenanceRepo.save(maintenance);

      // Update the asset's last maintenance date
      asset.lastMaintenance = new Date(serviceDate);
      if (nextServiceDate) {
        asset.nextMaintenance = new Date(nextServiceDate);
      } else {
        asset.nextMaintenance = null as any;
      }
      asset.maintenanceBy = vendor.vendorName;
      await assetRepo.save(asset);

      return res.status(201).json({
        message: 'Printer maintenance recorded successfully',
        maintenance: savedMaintenance
      });
    } catch (error: any) {
      console.error('Error recording printer maintenance:', error);
      return res.status(500).json({ 
        message: 'Error recording printer maintenance', 
        error: error.message 
      });
    }
  }

  // Upload invoice file
  async uploadInvoice(req: Request, res: Response) {
    try {
      const maintenanceId = req.params.id;
      const maintenanceRepo = getRepository(PrinterMaintenance);
      
      // Get the maintenance record
      const maintenance = await maintenanceRepo.findOne({ where: { id: maintenanceId } as any });
      if (!maintenance) {
        return res.status(404).json({ message: 'Maintenance record not found' });
      }

      // Use the upload middleware
      upload.single('invoice')(req, res, async (err) => {
        if (err) {
          return res.status(400).json({ message: 'Error uploading file', error: err.message });
        }

        if (!req.file) {
          return res.status(400).json({ message: 'No file uploaded' });
        }

        // Update maintenance record with file path
        maintenance.invoiceFilePath = req.file.path;
        await maintenanceRepo.save(maintenance);

        return res.status(200).json({
          message: 'Invoice uploaded successfully',
          filePath: req.file.path
        });
      });
    } catch (error: any) {
      console.error('Error uploading invoice:', error);
      return res.status(500).json({ 
        message: 'Error uploading invoice', 
        error: error.message 
      });
    }
  }

  // Approve maintenance invoice
  async approveInvoice(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { approvedById } = req.body;
      
      const maintenanceRepo = getRepository(PrinterMaintenance);
      const userRepo = getRepository(User);
      
      // Get maintenance record
      const maintenance = await maintenanceRepo.findOne({ 
        where: { id } as any,
        relations: ['asset', 'vendor', 'assignee']
      });
      
      if (!maintenance) {
        return res.status(404).json({ message: 'Maintenance record not found' });
      }
      
      // Validate approver
      const approver = await userRepo.findOne({ where: { id: approvedById } as any });
      if (!approver) {
        return res.status(404).json({ message: 'Approver not found' });
      }
      
      // Update approval info
      maintenance.approvedBy = approver;
      maintenance.approvalDate = new Date();
      maintenance.approvalStatus = 'Approved';
      
      await maintenanceRepo.save(maintenance);
      
      return res.status(200).json({
        message: 'Maintenance invoice approved successfully',
        maintenance
      });
    } catch (error: any) {
      console.error('Error approving invoice:', error);
      return res.status(500).json({ 
        message: 'Error approving invoice', 
        error: error.message 
      });
    }
  }

  // Generate finance report for a date range
  async generateFinanceReport(req: Request, res: Response) {
    try {
      const { startDate, endDate } = req.query;
      
      if (!startDate || !endDate) {
        return res.status(400).json({ message: 'Start date and end date are required' });
      }
      
      const maintenanceRepo = getRepository(PrinterMaintenance);
      
      const report = await maintenanceRepo.createQueryBuilder("maintenance")
        .where("maintenance.serviceDate BETWEEN :startDate AND :endDate", {
          startDate: new Date(startDate as string),
          endDate: new Date(endDate as string)
        })
        .leftJoinAndSelect("maintenance.asset", "asset")
        .leftJoinAndSelect("maintenance.vendor", "vendor")
        .getMany();
      
      // Generate PDF if requested
      if (req.query.format === 'pdf') {
        const pdfPath = await generatePDF(report, 'Printer Maintenance Finance Report');
        
        return res.download(pdfPath, 'printer-maintenance-report.pdf', (err) => {
          if (err) {
            console.error('Error downloading PDF:', err);
          }
          
          // Delete the temporary file after download
          fs.unlinkSync(pdfPath);
        });
      }
      
      return res.status(200).json({
        message: 'Finance report generated',
        report,
        totalAmount: report.reduce((sum, item) => sum + Number(item.invoiceAmount), 0)
      });
    } catch (error: any) {
      console.error('Error generating finance report:', error);
      return res.status(500).json({ 
        message: 'Error generating finance report', 
        error: error.message 
      });
    }
  }

  // Mark records as submitted to finance
  async markAsSubmittedToFinance(req: Request, res: Response) {
    try {
      const { ids } = req.body;
      
      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        return res.status(400).json({ message: 'Valid array of record IDs is required' });
      }
      
      const maintenanceRepo = getRepository(PrinterMaintenance);
      
      // Update finance submission status for all IDs one by one
      const updatePromises = ids.map(id => 
        maintenanceRepo.update(
          id,
          { 
            submittedToFinance: true,
            updatedAt: new Date() 
          }
        )
      );
      
      const results = await Promise.all(updatePromises);
      const totalAffected = results.reduce((sum, r) => sum + (r.affected || 0), 0);
      
      return res.status(200).json({
        message: `${totalAffected} maintenance records marked as submitted to finance`
      });
    } catch (error: any) {
      console.error('Error marking records as submitted:', error);
      return res.status(500).json({ 
        message: 'Error marking records as submitted to finance', 
        error: error.message 
      });
    }
  }

  // Get pending approvals
  async getPendingApprovals(req: Request, res: Response) {
    try {
      const maintenanceRepo = getRepository(PrinterMaintenance);
      
      const pendingApprovals = await maintenanceRepo.find({
        where: { approvalStatus: 'Pending' } as any,
        relations: ['asset', 'vendor', 'assignee']
      });
      
      return res.status(200).json({
        count: pendingApprovals.length,
        pendingApprovals
      });
    } catch (error: any) {
      console.error('Error fetching pending approvals:', error);
      return res.status(500).json({ 
        message: 'Error fetching pending approvals', 
        error: error.message 
      });
    }
  }

  // Get printer maintenance history for an asset
  async getMaintenanceHistory(req: Request, res: Response) {
    try {
      const { assetId } = req.params;
      const maintenanceRepo = getRepository(PrinterMaintenance);
      
      // Use query builder instead of find to avoid type issues
      const history = await maintenanceRepo.createQueryBuilder("maintenance")
        .innerJoin("maintenance.asset", "asset")
        .where("asset.id = :assetId", { assetId })
        .leftJoinAndSelect("maintenance.vendor", "vendor")
        .leftJoinAndSelect("maintenance.assignee", "assignee")
        .leftJoinAndSelect("maintenance.approvedBy", "approvedBy")
        .orderBy("maintenance.serviceDate", "DESC")
        .getMany();
      
      return res.status(200).json({
        count: history.length,
        history
      });
    } catch (error: any) {
      console.error('Error fetching maintenance history:', error);
      return res.status(500).json({ 
        message: 'Error fetching maintenance history', 
        error: error.message 
      });
    }
  }

  // Update an existing maintenance record
  async updateMaintenanceRecord(req: Request, res: Response) {
    try {
      console.log('📝 Controller: updateMaintenanceRecord called');
      const maintenanceRepo = getRepository(PrinterMaintenance);
      
      // Get ID from the request
      const id = req.params.id || req.body.id;
      
      if (!id) {
        return res.status(400).json({ 
          error: 'Invalid ID',
          message: 'The record ID is missing or invalid'
        });
      }
      
      // Check if the record exists
      const existingRecord = await maintenanceRepo.findOne({ where: { id } as any });
      if (!existingRecord) {
        return res.status(404).json({
          error: 'Record not found',
          message: `No maintenance record found with ID: ${id}`
        });
      }
      
      console.log(`✅ Existing record found with ID: ${id}`);
      
      // Update the record
      const updateResult = await maintenanceRepo.update(id, req.body);
      
      console.log('🔄 Update result:', updateResult);
      
      // Check if record was updated
      if (updateResult.affected === 0) {
        return res.status(500).json({
          error: 'Update failed',
          message: 'Record could not be updated'
        });
      }
      
      // Get the updated record
      const updatedRecord = await maintenanceRepo.findOne({ where: { id } as any });
      
      return res.status(200).json({
        message: 'Maintenance record updated successfully',
        record: updatedRecord
      });
    } catch (error: any) {
      console.error('Error updating maintenance record:', error);
      return res.status(500).json({
        error: 'Server error',
        message: 'An error occurred while updating the maintenance record',
        details: error.message
      });
    }
  }
  
  // Alias for the update method to maintain backwards compatibility
  async updateMaintenance(req: Request, res: Response) {
    return this.updateMaintenanceRecord(req, res);
  }

  // Get a single maintenance record by ID
  async getMaintenanceRecord(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      if (!id) {
        return res.status(400).json({
          error: 'Invalid ID',
          message: 'The record ID is missing'
        });
      }
      
      const maintenanceRepo = getRepository(PrinterMaintenance);
      
      // Get the maintenance record
      const record = await maintenanceRepo.findOne({
        where: { id } as any,
        relations: ['asset', 'vendor', 'assignee']
      });
      
      if (!record) {
        return res.status(404).json({
          error: 'Record not found',
          message: `No maintenance record found with ID: ${id}`
        });
      }
      
      return res.status(200).json(record);
    } catch (error: any) {
      console.error('Error fetching maintenance record:', error);
      return res.status(500).json({
        error: 'Server error',
        message: 'An error occurred while fetching the maintenance record',
        details: error.message
      });
    }
  }

  // Get all maintenance records (with filtering and pagination)
  async getMaintenanceRecords(req: Request, res: Response) {
    try {
      const maintenanceRepo = getRepository(PrinterMaintenance);
      const { id } = req.params;

      // If ID provided, return single record
      if (id) {
        const record = await maintenanceRepo.findOne({
          where: { id } as any,
          relations: ['asset', 'vendor', 'assignee', 'approvedBy']
        });

        if (!record) {
          return res.status(404).json({
            error: 'Record not found',
            message: `No maintenance record found with ID: ${id}`
          });
        }

        return res.status(200).json(record);
      }

      console.log('Fetching all printer maintenance records with query params:', req.query);

      // Apply filters from query parameters
      const whereConditions: any = {};
      
      // Handle search query
      if (req.query.search) {
        // Add search conditions for relevant fields
        // This is a simplified example - adjust based on your schema
        const searchTerm = req.query.search as string;
        whereConditions.serviceDescription = Like(`%${searchTerm}%`);
      }

      // Filter by start date
      if (req.query.start_date) {
        try {
          const startDate = new Date(req.query.start_date as string);
          whereConditions.serviceDate = MoreThanOrEqual(startDate);
        } catch (e) {
          console.error('Invalid start_date format:', req.query.start_date);
        }
      }

      // Filter by end date
      if (req.query.end_date) {
        try {
          const endDate = new Date(req.query.end_date as string);
          whereConditions.serviceDate = LessThanOrEqual(endDate);
        } catch (e) {
          console.error('Invalid end_date format:', req.query.end_date);
        }
      }

      // Filter by printer_id
      if (req.query.printer_id) {
        whereConditions.asset = { id: req.query.printer_id };
      }

      // Filter by vendor_name
      if (req.query.vendor_name) {
        whereConditions.vendor = { vendorName: Like(`%${req.query.vendor_name}%`) };
      }

      // Filter by department
      if (req.query.department) {
        whereConditions.department = Like(`%${req.query.department}%`);
      }

      // Get pagination parameters
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const skip = (page - 1) * limit;

      console.log('Applying filters:', whereConditions);

      // Get records with filters, relationships, and pagination
      try {
        const [records, total] = await maintenanceRepo.findAndCount({
          relations: ['asset', 'vendor', 'assignee', 'approvedBy'],
          where: Object.keys(whereConditions).length > 0 ? whereConditions : undefined,
          order: { serviceDate: 'DESC' } as any,
          skip,
          take: limit
        });

        console.log(`Found ${records.length} maintenance records (total: ${total})`);

        return res.status(200).json({
          total,
          page,
          limit,
          pages: Math.ceil(total / limit),
          records
        });
      } catch (dbError: any) {
        console.error('Database error while fetching records:', dbError);
        throw new Error(`Database error: ${dbError.message}`);
      }
    } catch (error: any) {
      console.error('Error fetching maintenance records:', error);
      return res.status(500).json({
        error: 'Server error',
        message: 'An error occurred while fetching maintenance records',
        details: error.message
      });
    }
  }

  // Update invoice file for a maintenance record
  async updateInvoiceFile(id: string, filename: string) {
    try {
      const maintenanceRepo = getRepository(PrinterMaintenance);
      const record = await maintenanceRepo.findOne({ where: { id } });
      
      if (!record) {
        throw new Error('Maintenance record not found');
      }

      // Update only the filename
      record.invoiceFilePath = filename;
      await maintenanceRepo.save(record);
      
      return true;
    } catch (error) {
      console.error('Error updating invoice file:', error);
      throw error;
    }
  }

  // Download invoice file
  async downloadInvoice(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      // Debug log the params
      console.log('Download invoice request params:', {
        id,
        params: req.params,
        url: req.url,
        method: req.method
      });
      
      if (!id) {
        console.error('Missing ID parameter in download request');
        return res.status(400).json({ message: 'Maintenance record ID is required' });
      }

      const maintenanceRepo = getRepository(PrinterMaintenance);
      
      // Get the maintenance record
      console.log(`Looking for maintenance record with ID: ${id}`);
      const maintenance = await maintenanceRepo.findOne({ where: { id } as any });
      
      if (!maintenance) {
        console.error(`Maintenance record not found for ID: ${id}`);
        return res.status(404).json({ message: 'Maintenance record not found' });
      }

      console.log('Maintenance record found:', {
        id: maintenance.id,
        invoiceFilePath: maintenance.invoiceFilePath
      });

      // Check if invoice file exists
      if (!maintenance.invoiceFilePath) {
        console.error(`No invoice file path for maintenance record ID: ${id}`);
        return res.status(404).json({ message: 'No invoice file found for this record' });
      }

      // Get the file path
      let filePath = path.resolve(maintenance.invoiceFilePath);
      console.log('Resolved file path:', filePath);
      
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        console.error(`Invoice file not found at path: ${filePath}`);
        
        // Try to find the file in the uploads/invoices directory
        const fileName = path.basename(maintenance.invoiceFilePath);
        const alternativePaths = [
          path.join(process.cwd(), 'uploads', 'invoices', fileName),
          path.join(__dirname, '..', 'uploads', 'invoices', fileName),
          path.join(__dirname, '..', '..', 'uploads', 'invoices', fileName)
        ];
        
        console.log('Checking alternative paths:', alternativePaths);
        
        let foundPath = null;
        for (const altPath of alternativePaths) {
          if (fs.existsSync(altPath)) {
            foundPath = altPath;
            console.log(`File found at alternative path: ${altPath}`);
            break;
          }
        }
        
        if (!foundPath) {
          return res.status(404).json({ 
            message: 'Invoice file not found on server',
            details: {
              originalPath: filePath,
              checkedPaths: alternativePaths
            }
          });
        }
        
        // Use the alternative path
        filePath = foundPath;
      }

      // Get filename from path
      const fileName = path.basename(filePath);
      console.log(`Sending file: ${fileName}`);

      // Set headers for file download
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);

      // Stream the file
      const fileStream = fs.createReadStream(filePath);
      fileStream.pipe(res);

      // Handle errors
      fileStream.on('error', (error) => {
        console.error('Error streaming file:', error);
        if (!res.headersSent) {
          res.status(500).json({ message: 'Error downloading file' });
        }
      });
    } catch (error: any) {
      console.error('Error downloading invoice:', error);
      if (!res.headersSent) {
        res.status(500).json({ 
          message: 'Error downloading invoice', 
          error: error.message 
        });
      }
    }
  }
} 