import React, { useState, useMemo } from 'react';
import { X, Download, FileText, FileSpreadsheet, Calendar, Filter, Settings, Info, ChevronDown } from 'lucide-react';
import * as XLSX from 'xlsx';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { Attendance } from '../../../types/attendance';

interface AttendanceExportProps {
  attendances: Attendance[];
  onClose: () => void;
  isOpen: boolean;
  currentFilters?: {
    dateRange: { start: string; end: string };
    datePeriod?: string;
    selectedDepartment?: string;
    selectedStatus?: string;
    searchQuery?: string;
  };
  currentViewData?: any[];
}

interface ExportColumn {
  key: string;
  label: string;
  included: boolean;
  width?: number;
}

const AttendanceExport: React.FC<AttendanceExportProps> = ({
  attendances,
  onClose,
  isOpen,
  currentFilters,
  currentViewData
}) => {
  const [exportFormat, setExportFormat] = useState<'csv' | 'excel' | 'pdf'>('excel');
  
  const [dateRange, setDateRange] = useState({
    start: currentFilters?.dateRange?.start || new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Default to 1 year ago
    end: currentFilters?.dateRange?.end || new Date().toISOString().split('T')[0]
  });
  
  const [datePeriod, setDatePeriod] = useState<string>('User Defined');
  const [selectedDepartment, setSelectedDepartment] = useState(currentFilters?.selectedDepartment || '');
  const [selectedStatus, setSelectedStatus] = useState(currentFilters?.selectedStatus || '');
  const [isExporting, setIsExporting] = useState(false);
  const [useCurrentView, setUseCurrentView] = useState(true);
  
  const [exportColumns, setExportColumns] = useState<ExportColumn[]>([
    { key: 'employeeId', label: 'Employee ID', included: true, width: 12 },
    { key: 'employeeName', label: 'Employee Name', included: true, width: 25 },
    { key: 'department', label: 'Department', included: true, width: 18 },
    { key: 'position', label: 'Position', included: true, width: 18 },
    { key: 'date', label: 'Date', included: true, width: 12 },
    { key: 'checkInTime', label: 'Check In Time', included: true, width: 15 },
    { key: 'lateCheckin', label: 'Late Check-in', included: false, width: 12 },
    { key: 'earlyCheckin', label: 'Early Check-in', included: false, width: 12 },
    { key: 'checkOutTime', label: 'Check Out Time', included: true, width: 15 },
    { key: 'status', label: 'Attendance Status', included: true, width: 15 },
    { key: 'workHours', label: 'Working Hours', included: true, width: 12 },
    { key: 'overtime', label: 'Overtime Hours', included: true, width: 12 },
    { key: 'location', label: 'Work Location', included: true, width: 15 },
    { key: 'isRemote', label: 'Remote Work', included: false, width: 12 },
    { key: 'notes', label: 'Notes/Remarks', included: false, width: 30 },
    { key: 'isRegularized', label: 'Regularized', included: false, width: 12 }
  ]);

  const departments = useMemo(() => {
    const depts = new Set(
      attendances
        .map(a => a.employeeDepartment || a.department)
        .filter((dept): dept is string => Boolean(dept))
    );
    return Array.from(depts).sort();
  }, [attendances]);

  const statuses = useMemo(() => {
    const statusSet = new Set(attendances.map(a => a.status).filter(Boolean));
    return Array.from(statusSet).sort();
  }, [attendances]);

  const filteredAttendances = useMemo(() => {
    console.log('AttendanceExport - filteredAttendances calculation:', {
      useCurrentView,
      currentViewDataLength: currentViewData?.length || 0,
      attendancesLength: attendances?.length || 0,
      dateRange,
      selectedDepartment,
      selectedStatus
    });

    if (useCurrentView && currentViewData && currentViewData.length > 0) {
      // For current view, include all records from the current view
      const filtered = currentViewData;
      
      console.log('Using current view data:', {
        totalCurrentView: currentViewData.length,
        afterFilter: filtered.length,
        sampleData: filtered.slice(0, 2)
      });
      
      return filtered;
    }
    
    // Use custom filtering on all attendances
    if (!attendances || attendances.length === 0) {
      console.log('No attendances data available');
      return [];
    }
    
    const filtered = attendances.filter(attendance => {
      // More lenient filtering for "All Data" export
      let includeRecord = true;
      
      // Date range filter - but be more inclusive
      if (dateRange.start && dateRange.end) {
        const attendanceDate = new Date(attendance.date);
        const startDate = new Date(dateRange.start);
        const endDate = new Date(dateRange.end);
        
        // Only filter by date if the dates are valid
        if (!isNaN(attendanceDate.getTime()) && !isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
          includeRecord = includeRecord && (attendanceDate >= startDate && attendanceDate <= endDate);
        }
      }
      
      // Department filter - only apply if specifically selected
      if (selectedDepartment && selectedDepartment.trim() !== '') {
        const empDept = (attendance.employeeDepartment || attendance.department || '').trim();
        includeRecord = includeRecord && (empDept === selectedDepartment);
      }
      
      // Status filter - only apply if specifically selected
      if (selectedStatus && selectedStatus.trim() !== '') {
        const empStatus = (attendance.status || '').toLowerCase();
        includeRecord = includeRecord && empStatus.includes(selectedStatus.toLowerCase());
      }
      
      return includeRecord;
    });
    
    console.log('Using custom filter on all attendances:', {
      totalAttendances: attendances.length,
      afterFilter: filtered.length,
      filters: { dateRange, selectedDepartment, selectedStatus },
      sampleFiltered: filtered.slice(0, 2)
    });
    
    return filtered;
  }, [attendances, dateRange, selectedDepartment, selectedStatus, useCurrentView, currentViewData]);

  // Function to get position rank for sorting (lower number = higher priority)
  const getPositionRank = (position: string) => {
    if (!position) return 999; // Unknown positions go to bottom
    
    const pos = position.toLowerCase().trim();
    
    // Executive Level (1-10)
    if (pos.includes('ceo') && !pos.includes('driver') && !pos.includes('assistant')) return 1;
    if (pos.includes('cto') && !pos.includes('driver') && !pos.includes('assistant')) return 2;
    if (pos.includes('cfo') && !pos.includes('driver') && !pos.includes('assistant')) return 3;
    if (pos.includes('coo') && !pos.includes('driver') && !pos.includes('assistant')) return 4;
    if (pos.includes('chief') && !pos.includes('driver') && !pos.includes('assistant')) return 5;
    if (pos.includes('president') || pos.includes('chairman')) return 6;
    
    // Senior Leadership (7-10)
    if (pos.includes('vice president') || pos.includes('vp')) return 7;
    if (pos.includes('hod') || pos.includes('head of department')) return 8;
    if (pos.includes('director')) return 9;
    if (pos.includes('head of') || pos.includes('head -')) return 10;
    
    // Management Level (11-30)
    if (pos.includes('general manager') || pos.includes('gm')) return 11;
    if (pos.includes('senior manager') || pos.includes('sr manager')) return 12;
    if (pos.includes('manager') || pos.includes('mgr')) return 13;
    if (pos.includes('assistant manager') || pos.includes('asst manager')) return 14;
    if (pos.includes('team lead') || pos.includes('team leader') || pos.includes('lead')) return 15;
    if (pos.includes('supervisor') || pos.includes('super')) return 16;
    
    // Senior Level (31-50)
    if (pos.includes('principal') || pos.includes('architect')) return 31;
    if (pos.includes('senior') || pos.includes('sr.') || pos.includes('sr ')) return 32;
    
    // Mid Level (51-70)
    if (pos.includes('analyst') && pos.includes('senior')) return 51;
    if (pos.includes('developer') && pos.includes('senior')) return 52;
    if (pos.includes('engineer') && pos.includes('senior')) return 53;
    if (pos.includes('consultant') && pos.includes('senior')) return 54;
    if (pos.includes('specialist') && pos.includes('senior')) return 55;
    
    // Regular Level (71-90)
    if (pos.includes('analyst')) return 71;
    if (pos.includes('developer') || pos.includes('programmer')) return 72;
    if (pos.includes('engineer')) return 73;
    if (pos.includes('consultant')) return 74;
    if (pos.includes('specialist')) return 75;
    if (pos.includes('coordinator')) return 76;
    if (pos.includes('executive') && !pos.includes('chief')) return 77;
    if (pos.includes('officer') && !pos.includes('chief')) return 78;
    if (pos.includes('associate')) return 79;
    
    // Junior/Support Level (91-99)
    if (pos.includes('junior') || pos.includes('jr.') || pos.includes('jr ')) return 91;
    if (pos.includes('trainee') || pos.includes('intern')) return 92;
    if (pos.includes('assistant') && !pos.includes('manager')) return 93;
    if (pos.includes('driver')) return 94; // All drivers are junior, regardless of who they drive for
    if (pos.includes('security') || pos.includes('guard')) return 95;
    if (pos.includes('clerk') || pos.includes('peon')) return 96;
    if (pos.includes('cook') || pos.includes('chef') || pos.includes('mess')) return 97;
    if (pos.includes('helper') || pos.includes('cleaner') || pos.includes('janitor')) return 98;
    if (pos.includes('attendant') || pos.includes('boy') || pos.includes('office boy')) return 99;
    
    // Default for unmatched positions
    return 80;
  };

  // Function to sort attendance data by department first, then position hierarchy within department
  const sortAttendanceData = (attendances: any[]) => {
    return [...attendances].sort((a, b) => {
      // First sort by department alphabetically
      const deptA = (a.employeeDepartment || a.department || 'N/A').toLowerCase();
      const deptB = (b.employeeDepartment || b.department || 'N/A').toLowerCase();
      
      if (deptA !== deptB) {
        return deptA.localeCompare(deptB);
      }
      
      // Within same department, sort by position rank (hierarchy)
      const posA = a.designation || a.employeeDesignation || a.position || '';
      const posB = b.designation || b.employeeDesignation || b.position || '';
      const rankA = getPositionRank(posA);
      const rankB = getPositionRank(posB);
      
      if (rankA !== rankB) {
        return rankA - rankB; // Lower rank number = higher priority
      }
      
      // If same position within same department, sort by employee name
      const nameA = (a.employeeName || a.name || '').toLowerCase();
      const nameB = (b.employeeName || b.name || '').toLowerCase();
      return nameA.localeCompare(nameB);
    });
  };

  const toggleColumn = (key: string) => {
    setExportColumns(prev => prev.map(col => 
      col.key === key ? { ...col, included: !col.included } : col
    ));
  };

  // Function to format attendance status into readable words
  const formatAttendanceStatus = (status: any) => {
    if (!status) return 'N/A';
    
    const statusString = status.toString().toLowerCase();
    
    switch (statusString) {
      case 'present':
        return 'Present';
      case 'absent':
        return 'Absent';
      case 'late':
        return 'Late';
      case 'late_checkin':
      case 'late checkin':
      case 'late_check_in':
      case 'late check in':
        return 'Late Check-in';
      case 'early_checkin':
      case 'early checkin':
      case 'early_check_in':
      case 'early check in':
        return 'Early Check-in';
      case 'half_day':
      case 'half day':
        return 'Half Day';
      case 'leave':
        return 'Leave';
      case 'holiday':
        return 'Holiday';
      case 'weekend':
        return 'Weekend';
      case 'work_from_home':
      case 'work from home':
      case 'wfh':
        return 'Work From Home';
      case 'on_duty':
        return 'On Duty';
      case 'paid_time_off':
      case 'pto':
        return 'Paid Time Off';
      case 'unpaid_leave':
        return 'Unpaid Leave';
      case 'comp_off':
        return 'Comp Off';
      case 'sick_leave':
        return 'Sick Leave';
      case 'annual_leave':
        return 'Annual Leave';
      case 'maternity_leave':
        return 'Maternity Leave';
      case 'paternity_leave':
        return 'Paternity Leave';
      case 'business_trip':
        return 'Business Trip';
      case 'bereavement':
        return 'Bereavement Leave';
      case 'study':
        return 'Study Leave';
      case 'casual':
        return 'Casual Leave';
      case 'personal':
        return 'Personal Leave';
      case 'compensatory':
        return 'Compensatory Leave';
      case 'other':
        return 'Other Leave';
      default:
        // Convert snake_case or kebab-case to Title Case
        return statusString
          .split(/[_-]/)
          .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
    }
  };

  const formatAttendanceForExport = (attendance: any) => {
    const calculateWorkHours = (checkIn?: string, checkOut?: string) => {
      if (!checkIn || !checkOut) return 0;
      try {
        const startTime = new Date(`1970-01-01T${checkIn}`);
        const endTime = new Date(`1970-01-01T${checkOut}`);
        const diffMs = endTime.getTime() - startTime.getTime();
        return Math.max(0, diffMs / (1000 * 60 * 60));
      } catch {
        return 0;
      }
    };

    const calculateOvertime = (workHours: number, standardHours: number = 8) => {
      return Math.max(0, workHours - standardHours);
    };

    // Calculate work hours from check-in/check-out times or use stored value
    const workHours = attendance.workHours || calculateWorkHours(attendance.checkInTime, attendance.checkOutTime);
    const overtime = calculateOvertime(workHours);

    // Helper function to determine if check-in is late or early
    const analyzeCheckinTime = (checkInTime: string, standardStartTime: string = '09:00') => {
      if (!checkInTime) return { isLate: false, isEarly: false, minutesDifference: 0 };
      
      try {
        const checkIn = new Date(`1970-01-01T${checkInTime}`);
        const standardStart = new Date(`1970-01-01T${standardStartTime}`);
        const diffMs = checkIn.getTime() - standardStart.getTime();
        const minutesDifference = diffMs / (1000 * 60);
        
        return {
          isLate: minutesDifference > 0,
          isEarly: minutesDifference < 0,
          minutesDifference: Math.abs(minutesDifference)
        };
      } catch {
        return { isLate: false, isEarly: false, minutesDifference: 0 };
      }
    };

    const checkinAnalysis = analyzeCheckinTime(attendance.checkInTime);

    // All available data formatted
    const formatted: any = {
      employeeId: attendance.employeeCode || attendance.employeeId || `GC-${String(attendance.employeeId || attendance.id || 0).padStart(4, '0')}`,
      employeeName: attendance.employeeName || attendance.name || 'Unknown',
      department: attendance.employeeDepartment || attendance.department || 'N/A',
      position: attendance.designation || attendance.employeeDesignation || attendance.position || 'N/A',
      date: attendance.date ? new Date(attendance.date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }) : 'N/A',
      checkInTime: attendance.checkInTime || 'N/A',
      checkOutTime: attendance.checkOutTime || 'N/A',
      status: formatAttendanceStatus(attendance.status),
      workHours: typeof workHours === 'number' ? workHours.toFixed(2) : '0.00',
      overtime: overtime.toFixed(2),
      location: attendance.location || (attendance.isRemote ? 'Remote' : 'Office'),
      lateCheckin: checkinAnalysis.isLate ? `Yes (${Math.round(checkinAnalysis.minutesDifference)} min)` : 'No',
      earlyCheckin: checkinAnalysis.isEarly ? `Yes (${Math.round(checkinAnalysis.minutesDifference)} min)` : 'No',
      isRemote: attendance.isRemote ? 'Yes' : 'No',
      notes: attendance.notes || '',
      isRegularized: attendance.isRegularized ? 'Yes' : 'No'
    };

    // Create the final export object with only selected columns
    const result: any = {};
    const includedColumns = exportColumns.filter(col => col.included);
    
    includedColumns.forEach(col => {
      result[col.label] = formatted[col.key] || '';
    });

    // Debug log for first few records to verify column selection
    if (Math.random() < 0.1) { // Log ~10% of records for debugging
      console.log('Column filtering debug:', {
        includedColumnCount: includedColumns.length,
        includedColumnLabels: includedColumns.map(c => c.label),
        resultKeys: Object.keys(result),
        sampleResult: result
      });
    }

    return result;
  };

  const exportToCSV = () => {
    // Sort attendance data by department alphabetically, then position hierarchy within department
    const sortedAttendances = sortAttendanceData(filteredAttendances);
    
    const exportData = sortedAttendances.map(formatAttendanceForExport);
    
    const headers = exportColumns.filter(col => col.included).map(col => col.label);
    const csvContent = [
      headers.join(','),
      ...exportData.map(row => 
        headers.map(header => `"${row[header] || ''}"`).join(',')
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    
    const datePeriodSuffix = currentFilters?.datePeriod ? `-${currentFilters.datePeriod.toLowerCase().replace(' ', '-')}` : '';
    const filename = `attendance-report${datePeriodSuffix}-${dateRange.start}-to-${dateRange.end}.csv`;
    link.setAttribute('download', filename);
    
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const exportToExcel = () => {
    console.log('Starting Excel export...');
    console.log('Filtered attendances:', filteredAttendances.length);
    console.log('Sample attendance data:', filteredAttendances.slice(0, 2));
    
    // Sort attendance data by department alphabetically, then position hierarchy within department
    const sortedAttendances = sortAttendanceData(filteredAttendances);
    
    const exportData = sortedAttendances.map(formatAttendanceForExport);
    
    console.log('Formatted export data:', exportData.length);
    console.log('Sample formatted data:', exportData.slice(0, 2));
    
    try {
      // Create the main attendance data worksheet
      const worksheet = XLSX.utils.json_to_sheet(exportData);
      
      // Set column widths for better formatting
      const columnWidths = exportColumns
        .filter(col => col.included)
        .map(col => ({ wch: col.width || 15 }));
      worksheet['!cols'] = columnWidths;

      // Create workbook and add the main data sheet
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Attendance Data');
      
      // Create a separate summary sheet with report information
      const summary = [
        { Field: 'Report Title', Value: 'Employee Attendance Report' },
        { Field: 'Generated On', Value: new Date().toLocaleString() },
        { Field: 'Date Period', Value: currentFilters?.datePeriod || 'Custom Range' },
        { Field: 'Date Range', Value: `${dateRange.start} to ${dateRange.end}` },
        { Field: 'Total Records', Value: filteredAttendances.length },
        { Field: 'Department Filter', Value: selectedDepartment || 'All Departments' },
        { Field: 'Status Filter', Value: selectedStatus || 'All Statuses' },
        { Field: 'Search Query', Value: currentFilters?.searchQuery || 'None' },
        { Field: 'Data Source', Value: useCurrentView ? 'Current View (Filtered)' : 'All Data (Re-filtered)' },
        { Field: 'Sort Order', Value: 'Department → Position Hierarchy → Name' },
        { Field: '', Value: '' }, // Empty row
        { Field: 'Export Statistics', Value: '' },
        { Field: 'Present Employees', Value: exportData.filter(d => d['Attendance Status'] === 'Present').length },
        { Field: 'Absent Employees', Value: exportData.filter(d => d['Attendance Status'] === 'Absent').length },
        { Field: 'Late Arrivals', Value: exportData.filter(d => d['Attendance Status'] === 'Late').length },
        { Field: 'Work From Home', Value: exportData.filter(d => d['Attendance Status'] === 'Work From Home').length }
      ];
      
      const summarySheet = XLSX.utils.json_to_sheet(summary);
      summarySheet['!cols'] = [{ wch: 20 }, { wch: 30 }]; // Set column widths for summary
      XLSX.utils.book_append_sheet(workbook, summarySheet, 'Report Summary');

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().slice(0, 10);
      const datePeriodSuffix = currentFilters?.datePeriod ? `-${currentFilters.datePeriod.toLowerCase().replace(/\s+/g, '-')}` : '';
      const filename = `attendance-report${datePeriodSuffix}-${timestamp}.xlsx`;
      
      console.log('Saving Excel file:', filename);
      XLSX.writeFile(workbook, filename);
      console.log('Excel export completed successfully');
      
    } catch (error) {
      console.error('Error during Excel export:', error);
      alert('Error occurred during Excel export. Please try again.');
    }
  };

  const exportToPDF = () => {
    try {
      // Sort attendance data by department alphabetically, then position hierarchy within department
      const sortedAttendances = sortAttendanceData(filteredAttendances);
      
      if (sortedAttendances.length === 0) {
        alert('No data available to export to PDF.');
        return;
      }
      
      const exportData = sortedAttendances.map(formatAttendanceForExport);
      const selectedColumns = exportColumns.filter(col => col.included);
      
      if (selectedColumns.length === 0) {
        alert('Please select at least one column to export.');
        return;
      }

      // Create PDF document
      const doc = new jsPDF('landscape', 'mm', 'a4');
      
      // Simple header
      doc.setFontSize(18);
      doc.setFont('helvetica', 'bold');
      doc.text('Attendance Report', 148, 20, { align: 'center' });
      
      doc.setFontSize(10);
      doc.setFont('helvetica', 'normal');
      doc.text(`Generated: ${new Date().toLocaleDateString()}`, 148, 30, { align: 'center' });
      doc.text(`Date Range: ${dateRange.start} to ${dateRange.end}`, 148, 36, { align: 'center' });

      // Prepare table data
      const headers = selectedColumns.map(col => col.label);
      const tableData = exportData.map(row => 
        selectedColumns.map(col => String(row[col.label] || ''))
      );

      // Create table with simple, proven configuration
      autoTable(doc, {
        head: [headers],
        body: tableData,
        startY: 45,
        theme: 'grid',
        styles: {
          fontSize: 6,
          cellPadding: 2,
          lineColor: [0, 0, 0],       // ✅ Jet Black borders
          lineWidth: 0.5,             // ✅ Thicker lines (Excel-like)
          halign: 'center',
          valign: 'middle',
        },
        headStyles: {
          fillColor: [0, 0, 0],       // ✅ Black header background
          textColor: [255, 255, 255], // ✅ White text
          fontStyle: 'bold',
          fontSize: 8,
          lineColor: [0, 0, 0],
          lineWidth: 0.5,
        },
        bodyStyles: {
          fontSize: 7,
          textColor: [0, 0, 0],
          lineColor: [0, 0, 0],
          lineWidth: 0.5,
        },
        columnStyles: selectedColumns.reduce((acc, _, i) => {
          acc[i] = { cellWidth: 'auto', overflow: 'linebreak' };
          return acc;
        }, {} as Record<number, any>),
        margin: { top: 45, left: 14, right: 14, bottom: 20 },
        tableWidth: 'auto',
        showHead: 'everyPage',
        didDrawPage: function (data: any) {
          // Add page numbers
          doc.setFontSize(8);
          doc.text('Page ' + data.pageNumber, 280, 200);
        }
      });

      // Save the PDF
      const timestamp = new Date().toISOString().slice(0, 10);
      const filename = `attendance-report-${timestamp}.pdf`;
      doc.save(filename);
      
    } catch (error) {
      console.error('PDF export failed:', error);
      alert('PDF export failed: ' + (error as Error).message + '. Please try Excel or CSV export instead.');
    }
  };

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      switch (exportFormat) {
        case 'csv':
          exportToCSV();
          break;
        case 'excel':
          exportToExcel();
          break;
        case 'pdf':
          exportToPDF();
          break;
      }
      
      onClose();
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  // Handle date period changes
  const handleDatePeriodChange = (period: string) => {
    setDatePeriod(period);
    
    if (period === "Today") {
      const today = new Date().toISOString().split('T')[0];
      setDateRange({ start: today, end: today });
    } else if (period === "Yesterday") {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = yesterday.toISOString().split('T')[0];
      setDateRange({ start: yesterdayStr, end: yesterdayStr });
    } else if (period === "This Week") {
      const today = new Date();
      const firstDay = new Date(today.setDate(today.getDate() - today.getDay()));
      const lastDay = new Date(today.setDate(today.getDate() + 6));
      const startDate = firstDay.toISOString().split('T')[0];
      const endDate = lastDay.toISOString().split('T')[0];
      setDateRange({ start: startDate, end: endDate });
    } else if (period === "Last Week") {
      const today = new Date();
      const lastWeekStart = new Date(today.setDate(today.getDate() - today.getDay() - 7));
      const lastWeekEnd = new Date(today.setDate(today.getDate() + 6));
      const startDate = lastWeekStart.toISOString().split('T')[0];
      const endDate = lastWeekEnd.toISOString().split('T')[0];
      setDateRange({ start: startDate, end: endDate });
    } else if (period === "This Month") {
      const today = new Date();
      const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
      const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      const startDate = firstDay.toISOString().split('T')[0];
      const endDate = lastDay.toISOString().split('T')[0];
      setDateRange({ start: startDate, end: endDate });
    } else if (period === "Last Month") {
      const today = new Date();
      const firstDay = new Date(today.getFullYear(), today.getMonth() - 1, 1);
      const lastDay = new Date(today.getFullYear(), today.getMonth(), 0);
      const startDate = firstDay.toISOString().split('T')[0];
      const endDate = lastDay.toISOString().split('T')[0];
      setDateRange({ start: startDate, end: endDate });
    } else if (period === "This Year") {
      const today = new Date();
      const firstDay = new Date(today.getFullYear(), 0, 1);
      const lastDay = new Date(today.getFullYear(), 11, 31);
      const startDate = firstDay.toISOString().split('T')[0];
      const endDate = lastDay.toISOString().split('T')[0];
      setDateRange({ start: startDate, end: endDate });
    } else if (period === "Last Three Months") {
      const today = new Date();
      const firstDay = new Date(today.getFullYear(), today.getMonth() - 3, 1);
      const lastDay = new Date(today.getFullYear(), today.getMonth(), 0);
      const startDate = firstDay.toISOString().split('T')[0];
      const endDate = lastDay.toISOString().split('T')[0];
      setDateRange({ start: startDate, end: endDate });
    } else if (period === "Last Six Months") {
      const today = new Date();
      const firstDay = new Date(today.getFullYear(), today.getMonth() - 6, 1);
      const lastDay = new Date(today.getFullYear(), today.getMonth(), 0);
      const startDate = firstDay.toISOString().split('T')[0];
      const endDate = lastDay.toISOString().split('T')[0];
      setDateRange({ start: startDate, end: endDate });
    } else if (period === "Last Year") {
      const today = new Date();
      const firstDay = new Date(today.getFullYear() - 1, 0, 1);
      const lastDay = new Date(today.getFullYear() - 1, 11, 31);
      const startDate = firstDay.toISOString().split('T')[0];
      const endDate = lastDay.toISOString().split('T')[0];
      setDateRange({ start: startDate, end: endDate });
    }
    // For "User Defined", keep current dateRange values
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-3xl max-h-[85vh] overflow-y-auto">
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Export Attendance Report
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="p-4 space-y-4">
          {currentFilters && (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-start">
                <Info className="h-5 w-5 text-blue-500 dark:text-blue-400 mr-3 mt-0.5" />
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
                    {useCurrentView ? 'Current View Filters Applied' : 'Export Data Source Selection'}
                  </h3>
                  
                  {useCurrentView && (
                    <div className="grid grid-cols-2 gap-2 text-sm text-blue-700 dark:text-blue-300 mb-4">
                      <div><strong>Period:</strong> {currentFilters.datePeriod || 'Custom'}</div>
                      <div><strong>Date Range:</strong> {dateRange.start} to {dateRange.end}</div>
                      {currentFilters.selectedDepartment && (
                        <div><strong>Department:</strong> {currentFilters.selectedDepartment}</div>
                      )}
                      {currentFilters.selectedStatus && (
                        <div><strong>Status:</strong> {currentFilters.selectedStatus}</div>
                      )}
                      {currentFilters.searchQuery && (
                        <div><strong>Search:</strong> "{currentFilters.searchQuery}"</div>
                      )}
                    </div>
                  )}
                  
                  {!useCurrentView && (
                    <div className="text-sm text-blue-700 dark:text-blue-300 mb-4">
                      <p>You've selected to export all data. Configure your custom filters below to specify which records to include.</p>
                    </div>
                  )}
                  
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-3">
                      Export Data Source
                    </h4>
                    <div className="space-y-3">
                      <label className="flex items-start text-sm">
                        <input
                          type="radio"
                          name="exportSource"
                          checked={useCurrentView}
                          onChange={() => setUseCurrentView(true)}
                          className="text-blue-600 focus:ring-blue-500 mr-3 mt-0.5"
                        />
                        <div>
                          <span className="text-blue-800 dark:text-blue-200 font-medium">
                            Current View ({currentViewData?.length || 0} records)
                          </span>
                          <p className="text-xs text-blue-600 dark:text-blue-300 mt-1">
                            Export exactly what you're seeing now with all applied filters
                          </p>
                        </div>
                      </label>
                      
                      <label className="flex items-start text-sm">
                        <input
                          type="radio"
                          name="exportSource"
                          checked={!useCurrentView}
                          onChange={() => setUseCurrentView(false)}
                          className="text-blue-600 focus:ring-blue-500 mr-3 mt-0.5"
                        />
                        <div>
                          <span className="text-blue-800 dark:text-blue-200 font-medium">
                            All Data ({attendances?.length || 0} total records)
                          </span>
                          <p className="text-xs text-blue-600 dark:text-blue-300 mt-1">
                            Export all attendance data with custom date range and filters below
                          </p>
                        </div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Export Format
            </label>
            <div className="grid grid-cols-3 gap-4">
              <button
                onClick={() => setExportFormat('excel')}
                className={`p-4 border rounded-lg flex flex-col items-center space-y-2 transition-colors ${
                  exportFormat === 'excel'
                    ? 'border-green-500 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400'
                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                }`}
              >
                <FileSpreadsheet className="h-8 w-8" />
                <span className="text-sm font-medium">Excel</span>
                <span className="text-xs text-gray-500 dark:text-gray-400">Best for analysis</span>
              </button>
              
              <button
                onClick={() => setExportFormat('csv')}
                className={`p-4 border rounded-lg flex flex-col items-center space-y-2 transition-colors ${
                  exportFormat === 'csv'
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400'
                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                }`}
              >
                <FileText className="h-8 w-8" />
                <span className="text-sm font-medium">CSV</span>
                <span className="text-xs text-gray-500 dark:text-gray-400">Universal format</span>
              </button>
              
              <button
                onClick={() => setExportFormat('pdf')}
                className={`p-4 border rounded-lg flex flex-col items-center space-y-2 transition-colors ${
                  exportFormat === 'pdf'
                    ? 'border-red-500 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400'
                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                }`}
              >
                <FileText className="h-8 w-8" />
                <span className="text-sm font-medium">PDF</span>
                <span className="text-xs text-gray-500 dark:text-gray-400">Print ready</span>
              </button>
            </div>
          </div>

          {!useCurrentView && (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  <Calendar className="h-4 w-4 inline mr-2" />
                  Date Period
                </label>
                <div className="relative mb-4">
                  <select
                    className="appearance-none w-full pl-3 pr-8 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    value={datePeriod}
                    onChange={(e) => handleDatePeriodChange(e.target.value)}
                  >
                    <option value="Today">Today</option>
                    <option value="Yesterday">Yesterday</option>
                    <option value="This Week">This Week</option>
                    <option value="Last Week">Last Week</option>
                    <option value="This Month">This Month</option>
                    <option value="Last Month">Last Month</option>
                    <option value="Last Three Months">Last Three Months</option>
                    <option value="Last Six Months">Last Six Months</option>
                    <option value="This Year">This Year</option>
                    <option value="Last Year">Last Year</option>
                    <option value="User Defined">User Defined</option>
                  </select>
                  <ChevronDown className="h-4 w-4 text-gray-500 dark:text-gray-400 absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none" />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  <Calendar className="h-4 w-4 inline mr-2" />
                  Custom Date Range
                </label>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">From</label>
                    <input
                      type="date"
                      value={dateRange.start}
                      onChange={(e) => {
                        setDateRange(prev => ({ ...prev, start: e.target.value }));
                        if (datePeriod !== 'User Defined') {
                          setDatePeriod('User Defined');
                        }
                      }}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">To</label>
                    <input
                      type="date"
                      value={dateRange.end}
                      onChange={(e) => {
                        setDateRange(prev => ({ ...prev, end: e.target.value }));
                        if (datePeriod !== 'User Defined') {
                          setDatePeriod('User Defined');
                        }
                      }}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 mb-4">
                <div className="flex items-start">
                  <Info className="h-4 w-4 text-blue-500 dark:text-blue-400 mr-2 mt-0.5 flex-shrink-0" />
                  <div className="text-sm">
                    <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-1">Date Range Affects Employee Visibility</h4>
                    <p className="text-blue-700 dark:text-blue-300">
                      Only employees with attendance records within the selected date range will appear in the export. 
                      To see all employees, expand the date range to cover a longer period (e.g., "This Year" or "Last Year").
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  <Filter className="h-4 w-4 inline mr-2" />
                  Additional Filters
                </label>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">Department</label>
                    <select
                      value={selectedDepartment}
                      onChange={(e) => setSelectedDepartment(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">All Departments</option>
                      {departments.map(dept => (
                        <option key={dept} value={dept}>{dept}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-xs text-gray-500 dark:text-gray-400 mb-1">Status</label>
                    <select
                      value={selectedStatus}
                      onChange={(e) => setSelectedStatus(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">All Statuses</option>
                      {statuses.map(status => (
                        <option key={status} value={status}>{status}</option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            </>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              <Settings className="h-4 w-4 inline mr-2" />
              Columns to Export
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-48 overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-md p-3">
              {exportColumns.map(column => (
                <label key={column.key} className="flex items-center space-x-2 text-sm">
                  <input
                    type="checkbox"
                    checked={column.included}
                    onChange={() => toggleColumn(column.key)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-gray-700 dark:text-gray-300">{column.label}</span>
                </label>
              ))}
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Export Summary</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-500 dark:text-gray-400">Records:</span>
                <span className="ml-2 font-medium text-gray-900 dark:text-white">
                  {filteredAttendances.length}
                </span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Columns:</span>
                <span className="ml-2 font-medium text-gray-900 dark:text-white">
                  {exportColumns.filter(col => col.included).length}
                </span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Format:</span>
                <span className="ml-2 font-medium text-gray-900 dark:text-white">
                  {exportFormat.toUpperCase()}
                </span>
              </div>
              <div>
                <span className="text-gray-500 dark:text-gray-400">Source:</span>
                <span className="ml-2 font-medium text-gray-900 dark:text-white">
                  {useCurrentView ? 'Current View' : 'Custom Filter'}
                </span>
              </div>
            </div>
            
            {/* Show selected columns for verification */}
            <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
              <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Selected Columns:</h4>
              <div className="flex flex-wrap gap-1">
                {exportColumns.filter(col => col.included).map(col => (
                  <span 
                    key={col.key}
                    className="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200"
                  >
                    {col.label}
                  </span>
                ))}
              </div>
              {exportColumns.filter(col => col.included).length === 0 && (
                <p className="text-xs text-red-600 dark:text-red-400">No columns selected for export!</p>
              )}
            </div>
            
            {filteredAttendances.length === 0 && (
              <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded border border-yellow-200 dark:border-yellow-800">
                <h4 className="text-xs font-medium text-yellow-800 dark:text-yellow-200 mb-1">No Data Available</h4>
                <p className="text-xs text-yellow-700 dark:text-yellow-300">
                  {useCurrentView 
                    ? 'The current view has no attendance records to export. Try adjusting your filters or switching to custom filter mode.'
                    : 'No records match your export criteria. Try expanding your date range or removing filters.'
                  }
                </p>
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center justify-end space-x-3 p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            Cancel
          </button>
          <button
            onClick={handleExport}
            disabled={isExporting || filteredAttendances.length === 0 || exportColumns.filter(col => col.included).length === 0}
            className="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isExporting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                {filteredAttendances.length === 0 
                  ? 'No Data to Export' 
                  : exportColumns.filter(col => col.included).length === 0
                  ? 'Select Columns to Export'
                  : `Export ${filteredAttendances.length} Records`
                }
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AttendanceExport; 