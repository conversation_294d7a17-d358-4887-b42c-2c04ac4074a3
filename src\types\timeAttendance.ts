import { PayrollCurrency } from './payroll';

export enum AttendanceStatus {
  PRESENT = 'present',
  ABSENT = 'absent',
  LEAVE = 'leave',
  HOLIDAY = 'holiday',
  HALF_DAY = 'half_day',
  WORK_FROM_HOME = 'work_from_home',
  BUSINESS_TRIP = 'business_trip'
}

export enum ShiftType {
  REGULAR = 'regular',
  NIGHT = 'night',
  MORNING = 'morning',
  EVENING = 'evening',
  WEEKEND = 'weekend',
  CUSTOM = 'custom',
  ROTATING = 'rotating'
}

export enum OvertimeType {
  REGULAR = 'regular',        // 1.5x rate
  DOUBLE = 'double',          // 2x rate
  HOLIDAY = 'holiday',        // Holiday special rate
  WEEKEND = 'weekend',        // Weekend special rate
  NIGHT_SHIFT = 'night_shift' // Night differential
}

export enum BiometricVerificationType {
  FINGERPRINT = 'fingerprint',
  FACE = 'face',
  CARD = 'card',
  MANUAL = 'manual',
  MOBILE = 'mobile'
}

export interface ShiftDefinition {
  id: number;
  name: string;
  shiftType: ShiftType;
  startTime: string;  // Format: "HH:MM" in 24-hour format
  endTime: string;    // Format: "HH:MM" in 24-hour format
  breakDuration: number; // In minutes
  workingHours: number;  // Total working hours in the shift
  isDefault: boolean;
  isActive: boolean;
  description?: string;
  premiumRate?: number;   // Extra pay rate for special shifts (e.g., 1.1 for 10% premium)
  overtimeThreshold?: number; // Minutes after which overtime starts
  weekdays?: number[];    // Array of days of week (0-6, where 0 is Sunday)
  color?: string;         // For UI representation
}

export interface EmployeeTimeEntry {
  id: number;
  employeeId: number;
  employeeName: string;
  date: string;  // Format: "YYYY-MM-DD"
  status: AttendanceStatus;
  shiftId: number;
  shiftName: string;
  plannedStartTime: string;  // Format: "HH:MM"
  plannedEndTime: string;    // Format: "HH:MM"
  actualStartTime?: string;  // Format: "HH:MM"
  actualEndTime?: string;    // Format: "HH:MM"
  breakDuration: number;     // In minutes
  workDuration?: number;     // In minutes
  overtimeMinutes?: number;
  overtimeType?: OvertimeType;
  notes?: string;
  approvalStatus: 'pending' | 'approved' | 'rejected';
  approvedBy?: number;
  approvalDate?: string;
  locationData?: {
    clockInLocation?: {
      latitude: number;
      longitude: number;
      address?: string;
    };
    clockOutLocation?: {
      latitude: number;
      longitude: number;
      address?: string;
    };
  };
  biometricData?: {
    clockInVerified: boolean;
    clockOutVerified: boolean;
    verificationMethod: BiometricVerificationType;
  };
  isManualEntry: boolean;
  createdBy: number;
  createdAt: string;
  updatedBy?: number;
  updatedAt?: string;
}

export interface OvertimeCalculation {
  id: number;
  employeeId: number;
  employeeName: string;
  timeEntryId: number;
  date: string;
  overtimeMinutes: number;
  overtimeType: OvertimeType;
  baseHourlyRate: number;
  multiplier: number;
  totalAmount: number;
  currency: PayrollCurrency;
  status: 'pending' | 'approved' | 'rejected' | 'paid';
  approvedBy?: number;
  approvalDate?: string;
  payrollPeriodId?: number;
  notes?: string;
}

export interface LeaveBalance {
  id: number;
  employeeId: number;
  leaveType: string;
  year: number;
  totalEntitlement: number;  // In days
  used: number;              // In days
  remaining: number;         // In days
  carryForward?: number;     // Days carried forward from previous year
  expired?: number;          // Days expired/lost
  lastUpdated: string;
}

export interface LeaveRequest {
  id: number;
  employeeId: number;
  employeeName: string;
  leaveType: string;
  startDate: string;
  endDate: string;
  totalDays: number;
  halfDay?: boolean;
  reason?: string;
  attachments?: string[];
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  approvedBy?: number;
  approvedDate?: string;
  rejectionReason?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface AttendanceSummary {
  id: number;
  employeeId: number;
  employeeName: string;
  payrollPeriodId: number;
  periodStartDate: string;
  periodEndDate: string;
  totalWorkingDays: number;
  presentDays: number;
  absentDays: number;
  leaveDays: number;
  holidayDays: number;
  halfDays: number;
  totalMinutesWorked: number;
  overtimeMinutes: number;
  regularOvertimeMinutes: number;
  doubleOvertimeMinutes: number;
  holidayOvertimeMinutes: number;
  weekendOvertimeMinutes: number;
  nightShiftOvertimeMinutes: number;
  lateArrivalMinutes: number;
  earlyDepartureMinutes: number;
  totalLeavesTaken: Record<string, number>;
  status: 'draft' | 'finalized';
  lastCalculated: string;
}

export interface HolidayDefinition {
  id: number;
  name: string;
  date: string; // Format: "YYYY-MM-DD"
  isRecurringYearly: boolean;
  isPaidHoliday: boolean;
  applicableRegions?: string[];
  applicableDepartments?: string[];
  description?: string;
  overtimeMultiplier?: number; // For holiday pay calculation
}

export interface AttendanceSettings {
  workingDays: number[]; // Array of days of week (0-6, where 0 is Sunday)
  defaultShiftId: number;
  graceTimeForLateArrival: number; // In minutes
  graceTimeForEarlyDeparture: number; // In minutes
  overtimeCalculationSettings: {
    enableOvertimeCalculation: boolean;
    defaultOvertimeRate: number;
    holidayOvertimeRate: number;
    weekendOvertimeRate: number;
    nightShiftOvertimeRate: number;
    minimumOvertimeMinutes: number; // Minimum overtime to be considered
    roundOvertimeTo: number; // Round to nearest X minutes
  };
  leaveSettings: {
    leaveEntitlementsByType: {
      [key in string]?: {
        annualEntitlement: number;
        maxCarryForward: number;
        requiresApproval: boolean;
        requiresAttachment: boolean;
        minServicePeriodMonths: number;
      };
    };
    proRateBasis: 'joining_date' | 'fixed_date';
    allowNegativeBalance: boolean;
    compensatoryLeaveExpiryDays: number;
  };
  attendanceApprovalSettings: {
    requireApproval: boolean;
    autoApprovalAfterDays: number;
    approverRoles: string[];
  };
}

export interface Attendance {
  // ...
  leaveType: string; // Dynamic leave type
  // ...
} 