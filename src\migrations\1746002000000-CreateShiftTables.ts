import { MigrationInterface, QueryRunner, Table, TableIndex, TableForeign<PERSON>ey } from "typeorm";

export class CreateShiftTables1746002000000 implements MigrationInterface {
    name = 'CreateShiftTables1746002000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create shifts table
        await queryRunner.createTable(
            new Table({
                name: "shifts",
                columns: [
                    {
                        name: "id",
                        type: "int",
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: "increment"
                    },
                    {
                        name: "name",
                        type: "varchar",
                        length: "100",
                        isNullable: false
                    },
                    {
                        name: "description",
                        type: "varchar",
                        length: "255",
                        isNullable: true
                    },
                    {
                        name: "startTime",
                        type: "time",
                        isNullable: false
                    },
                    {
                        name: "endTime",
                        type: "time",
                        isNullable: false
                    },
                    {
                        name: "breakDuration",
                        type: "int",
                        default: 0
                    },
                    {
                        name: "workingDays",
                        type: "json",
                        isNullable: true
                    },
                    {
                        name: "isFlexible",
                        type: "boolean",
                        default: false
                    },
                    {
                        name: "graceTimeInMinutes",
                        type: "int",
                        default: 15
                    },
                    {
                        name: "requiredWorkHours",
                        type: "float",
                        default: 8
                    },
                    {
                        name: "color",
                        type: "varchar",
                        length: "7",
                        isNullable: true
                    },
                    {
                        name: "isActive",
                        type: "boolean",
                        default: true
                    },
                    {
                        name: "isNightShift",
                        type: "boolean",
                        default: false
                    },
                    {
                        name: "halfDayHours",
                        type: "int",
                        default: 4
                    },
                    {
                        name: "breakStartTime",
                        type: "time",
                        isNullable: true
                    },
                    {
                        name: "breakEndTime",
                        type: "time",
                        isNullable: true
                    },
                    {
                        name: "createdAt",
                        type: "timestamp",
                        default: "now()"
                    },
                    {
                        name: "updatedAt",
                        type: "timestamp",
                        default: "now()"
                    }
                ]
            }),
            true
        );

        // Create shift_assignments table
        await queryRunner.createTable(
            new Table({
                name: "shift_assignments",
                columns: [
                    {
                        name: "id",
                        type: "int",
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: "increment"
                    },
                    {
                        name: "employeeId",
                        type: "int",
                        isNullable: false
                    },
                    {
                        name: "shiftId",
                        type: "int",
                        isNullable: false
                    },
                    {
                        name: "startDate",
                        type: "date",
                        isNullable: false
                    },
                    {
                        name: "endDate",
                        type: "date",
                        isNullable: true
                    },
                    {
                        name: "isPermanent",
                        type: "boolean",
                        default: true
                    },
                    {
                        name: "notes",
                        type: "text",
                        isNullable: true
                    },
                    {
                        name: "isActive",
                        type: "boolean",
                        default: true
                    },
                    {
                        name: "createdAt",
                        type: "timestamp",
                        default: "now()"
                    },
                    {
                        name: "updatedAt",
                        type: "timestamp",
                        default: "now()"
                    }
                ]
            }),
            true
        );

        // Create foreign key for shift_assignments.employeeId -> employees.id
        await queryRunner.createForeignKey("shift_assignments", new TableForeignKey({
            columnNames: ["employeeId"],
            referencedColumnNames: ["id"],
            referencedTableName: "employees",
            onDelete: "CASCADE"
        }));

        // Create foreign key for shift_assignments.shiftId -> shifts.id
        await queryRunner.createForeignKey("shift_assignments", new TableForeignKey({
            columnNames: ["shiftId"],
            referencedColumnNames: ["id"],
            referencedTableName: "shifts",
            onDelete: "CASCADE"
        }));

        // Create indexes for better performance
        await queryRunner.createIndex("shift_assignments", new TableIndex({
            name: "IDX_SHIFT_ASSIGNMENTS_EMPLOYEE",
            columnNames: ["employeeId"]
        }));

        await queryRunner.createIndex("shift_assignments", new TableIndex({
            name: "IDX_SHIFT_ASSIGNMENTS_SHIFT",
            columnNames: ["shiftId"]
        }));

        await queryRunner.createIndex("shift_assignments", new TableIndex({
            name: "IDX_SHIFT_ASSIGNMENTS_DATE_RANGE",
            columnNames: ["startDate", "endDate"]
        }));
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign keys first
        const shiftAssignmentsTable = await queryRunner.getTable("shift_assignments");
        if (shiftAssignmentsTable) {
            const foreignKeys = shiftAssignmentsTable.foreignKeys;
            for (const foreignKey of foreignKeys) {
                await queryRunner.dropForeignKey("shift_assignments", foreignKey);
            }
        }

        // Drop indexes
        await queryRunner.dropIndex("shift_assignments", "IDX_SHIFT_ASSIGNMENTS_EMPLOYEE");
        await queryRunner.dropIndex("shift_assignments", "IDX_SHIFT_ASSIGNMENTS_SHIFT");
        await queryRunner.dropIndex("shift_assignments", "IDX_SHIFT_ASSIGNMENTS_DATE_RANGE");

        // Drop tables
        await queryRunner.dropTable("shift_assignments");
        await queryRunner.dropTable("shifts");
    }
} 