import React from 'react';
import { X, Download } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface UploadedImage {
  fileName: string;
  fileUrl: string;
  fileType: string;
  size: number;
}

interface ImageViewerProps {
  image: UploadedImage | null;
  onClose: () => void;
}

const ImageViewer: React.FC<ImageViewerProps> = ({ image, onClose }) => {
  if (!image) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4">
      <div className="relative max-w-4xl w-full bg-white rounded-lg shadow-xl">
        <div className="p-4 border-b flex justify-between items-center">
          <h3 className="font-medium text-gray-900">{image.fileName}</h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        <div className="p-4">
          <img
            src={image.fileUrl}
            alt={image.fileName}
            className="max-w-full h-auto mx-auto"
          />
        </div>
        <div className="p-4 border-t flex justify-between items-center">
          <div className="text-sm text-gray-500">
            {(image.size / 1024).toFixed(2)} KB • {image.fileType}
          </div>
          <div className="flex space-x-2">
            <a
              href={image.fileUrl}
              download={image.fileName}
              className="flex items-center px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
            >
              <Download className="h-4 w-4 mr-1" />
              Download
            </a>
            <button
              onClick={() => {
                navigator.clipboard.writeText(image.fileUrl);
                toast.success('Image URL copied to clipboard');
              }}
              className="flex items-center px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
            >
              Copy URL
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImageViewer; 