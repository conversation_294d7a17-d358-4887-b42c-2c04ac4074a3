const mysql = require('mysql2/promise');
require('dotenv').config();

// Database connection config
const DB_HOST = process.env.DB_HOST || 'localhost';
const DB_PORT = process.env.DB_PORT || '3306';
const DB_USERNAME = process.env.DB_USERNAME || 'root';
const DB_PASSWORD = process.env.DB_PASSWORD || 'root';
const DB_NAME = 'ims_db';

async function restoreFullDatabase() {
  let connection;
  
  try {
    // Create connection without database first
    connection = await mysql.createConnection({
      host: DB_HOST,
      port: parseInt(DB_PORT),
      user: DB_USERNAME,
      password: DB_PASSWORD,
    });
    
    console.log('Connected to MySQL server');
    
    // Check if database exists
    const [databases] = await connection.query(`SHOW DATABASES LIKE '${DB_NAME}'`);
    const databaseExists = databases.length > 0;
    
    if (!databaseExists) {
      // Create database if it doesn't exist
      await connection.query(`CREATE DATABASE ${DB_NAME}`);
      console.log(`Database ${DB_NAME} created`);
    } else {
      console.log(`Database ${DB_NAME} already exists`);
    }
    
    // Switch to the database
    await connection.query(`USE ${DB_NAME}`);
    console.log(`Using database ${DB_NAME}`);
    
    // Create users table if not exists
    await connection.query(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role ENUM('EMPLOYEE', 'DEPT_HEAD', 'IT_STAFF', 'IT_ADMIN', 'IT_SUPPORT') NOT NULL DEFAULT 'EMPLOYEE',
        department VARCHAR(255) NOT NULL,
        project TEXT NULL,
        location TEXT NULL,
        isActive TINYINT(1) NOT NULL DEFAULT 1,
        permissions JSON NULL,
        createdAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
        updatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
      )
    `);
    console.log('Users table created or verified');
    
    // Insert admin user if not exists
    const [adminExists] = await connection.query(`SELECT COUNT(*) as count FROM users WHERE email = '<EMAIL>'`);
    if (adminExists[0].count === 0) {
      await connection.query(`
        INSERT INTO users (id, name, email, password, role, department, isActive, permissions)
        VALUES (
          1,
          'Administrator',
          '<EMAIL>',
          '$2b$10$EjaI3g8bI/L0t4Z4Xg/FgeE/ZxRdT0YaGU.Fg0V9yA9UNqDJy6Pcy',
          'IT_ADMIN',
          'IT',
          1,
          '{"canCreateTickets":true,"canCreateTicketsForOthers":true,"canEditTickets":true,"canDeleteTickets":true,"canCloseTickets":true,"canLockTickets":true,"canAssignTickets":true,"canEscalateTickets":true,"canViewAllTickets":true}'
        )
      `);
      console.log('Admin user created');
    } else {
      await connection.query(`
        UPDATE users 
        SET 
          role = 'IT_ADMIN',
          isActive = 1,
          permissions = '{"canCreateTickets":true,"canCreateTicketsForOthers":true,"canEditTickets":true,"canDeleteTickets":true,"canCloseTickets":true,"canLockTickets":true,"canAssignTickets":true,"canEscalateTickets":true,"canViewAllTickets":true}'
        WHERE email = '<EMAIL>'
      `);
      console.log('Admin user updated');
    }
    
    // Create other essential tables
    await createTicketsTable(connection);
    await createCommentsTable(connection);
    await createAttachmentsTable(connection);
    await createAssetsTable(connection);
    await createKnowledgeBaseTables(connection);
    
    console.log('Database restoration completed successfully');
    
  } catch (error) {
    console.error('Error restoring database:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

async function createTicketsTable(connection) {
  await connection.query(`
    CREATE TABLE IF NOT EXISTS tickets (
      id INT AUTO_INCREMENT PRIMARY KEY,
      title VARCHAR(255) NOT NULL,
      description TEXT NOT NULL,
      status ENUM('OPEN', 'IN_PROGRESS', 'PENDING', 'RESOLVED', 'CLOSED') NOT NULL DEFAULT 'OPEN',
      priority ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT') NOT NULL DEFAULT 'MEDIUM',
      category VARCHAR(255) NOT NULL,
      createdById VARCHAR(255) NOT NULL,
      assignedToId VARCHAR(255) NULL,
      createdAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
      updatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
      dueDate DATETIME(6) NULL,
      resolvedAt DATETIME(6) NULL
    )
  `);
  console.log('Tickets table created or verified');
}

async function createCommentsTable(connection) {
  await connection.query(`
    CREATE TABLE IF NOT EXISTS comments (
      id INT NOT NULL PRIMARY KEY AUTO_INCREMENT,
      content TEXT NOT NULL,
      ticketId INT NOT NULL,
      createdById VARCHAR(255) NOT NULL,
      role ENUM('EMPLOYEE', 'DEPT_HEAD', 'IT_STAFF', 'IT_ADMIN', 'IT_SUPPORT') NOT NULL DEFAULT 'EMPLOYEE',
      createdAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
      updatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
    )
  `);
  console.log('Comments table created or verified');
}

async function createAttachmentsTable(connection) {
  await connection.query(`
    CREATE TABLE IF NOT EXISTS attachments (
      id INT AUTO_INCREMENT PRIMARY KEY,
      fileName VARCHAR(255) NOT NULL,
      filePath VARCHAR(255) NOT NULL,
      fileSize INT NOT NULL,
      mimeType VARCHAR(255) NOT NULL,
      ticketId INT NULL,
      commentId INT NULL,
      createdAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
      updatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
    )
  `);
  console.log('Attachments table created or verified');
}

async function createAssetsTable(connection) {
  await connection.query(`
    CREATE TABLE IF NOT EXISTS assets (
      id INT AUTO_INCREMENT PRIMARY KEY,
      name VARCHAR(255) NOT NULL,
      assetTag VARCHAR(50) NOT NULL UNIQUE,
      category ENUM('LAPTOP', 'DESKTOP', 'MONITOR', 'PRINTER', 'SERVER', 'NETWORK', 'MOBILE', 'OTHER') NOT NULL,
      status ENUM('AVAILABLE', 'IN_USE', 'IN_REPAIR', 'RETIRED', 'LOST', 'STOLEN') NOT NULL DEFAULT 'AVAILABLE',
      manufacturer VARCHAR(255) NULL,
      model VARCHAR(255) NULL,
      serialNumber VARCHAR(255) NULL,
      purchaseDate DATE NULL,
      warrantyExpiryDate DATE NULL,
      location VARCHAR(255) NULL,
      notes TEXT NULL,
      assignedToId VARCHAR(255) NULL,
      createdAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
      updatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
    )
  `);
  console.log('Assets table created or verified');
}

async function createKnowledgeBaseTables(connection) {
  // Create knowledge categories table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS knowledge_categories (
      id INT AUTO_INCREMENT PRIMARY KEY,
      name VARCHAR(255) NOT NULL UNIQUE,
      description TEXT NULL,
      createdAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
      updatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
    )
  `);
  console.log('Knowledge categories table created or verified');
  
  // Create knowledge tags table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS knowledge_tags (
      id INT AUTO_INCREMENT PRIMARY KEY,
      name VARCHAR(255) NOT NULL UNIQUE,
      createdAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
      updatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
    )
  `);
  console.log('Knowledge tags table created or verified');
  
  // Create knowledge base articles table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS knowledge_base (
      id INT AUTO_INCREMENT PRIMARY KEY,
      title VARCHAR(255) NOT NULL,
      content TEXT NOT NULL,
      categoryId INT NULL,
      createdById VARCHAR(255) NOT NULL,
      isPublished TINYINT(1) NOT NULL DEFAULT 0,
      createdAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
      updatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
      FOREIGN KEY (categoryId) REFERENCES knowledge_categories(id) ON DELETE SET NULL
    )
  `);
  console.log('Knowledge base table created or verified');
  
  // Create knowledge_base_tags junction table
  await connection.query(`
    CREATE TABLE IF NOT EXISTS knowledge_base_tags (
      knowledgeBaseId INT NOT NULL,
      tagId INT NOT NULL,
      PRIMARY KEY (knowledgeBaseId, tagId),
      FOREIGN KEY (knowledgeBaseId) REFERENCES knowledge_base(id) ON DELETE CASCADE,
      FOREIGN KEY (tagId) REFERENCES knowledge_tags(id) ON DELETE CASCADE
    )
  `);
  console.log('Knowledge base tags junction table created or verified');
}

// Run the restoration
restoreFullDatabase(); 