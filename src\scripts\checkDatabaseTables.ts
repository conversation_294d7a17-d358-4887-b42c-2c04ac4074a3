import { DataSource } from 'typeorm';

// Database configuration
const AppDataSource = new DataSource({
  type: "mysql",
  host: process.env.DB_HOST || "localhost",
  port: parseInt(process.env.DB_PORT || "3306"),
  username: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "root",
  database: process.env.DB_NAME || "ims_db",
  synchronize: false,
  logging: true,
  entities: [],
  subscribers: [],
  migrations: []
});

const checkTables = async () => {
  console.log("Checking database tables...");

  try {
    // Initialize the data source
    await AppDataSource.initialize();
    console.log("Database connection established");

    // Get query runner
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    
    // Show all tables
    console.log("Listing all tables in the database:");
    const tables = await queryRunner.query("SHOW TABLES");
    console.log("Tables found:", tables);
    
    // Check if users table exists with different name
    for (const table of tables) {
      const tableName = Object.values(table)[0] as string;
      console.log(`\nTable: ${tableName}`);
      
      if (tableName.toLowerCase().includes('user')) {
        console.log(`Describing user table: ${tableName}`);
        const columns = await queryRunner.query(`DESCRIBE \`${tableName}\``);
        console.log("Columns:", columns);
      }
    }
    
    await queryRunner.release();
    
  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    try {
      await AppDataSource.destroy();
      console.log("Database connection closed");
    } catch (error) {
      console.error("Error closing database connection:", error);
    }
  }
};

// Run the check
checkTables(); 