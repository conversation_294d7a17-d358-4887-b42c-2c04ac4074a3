import React, { useState, useEffect } from 'react';
import { 
  Settings,
  Save,
  DollarSign,
  Calendar,
  AlertTriangle
} from 'lucide-react';
import { 
  PayrollSettings as PayrollSettingsType,
  PayrollFrequency,
  PaymentMethod,
  TaxCalculationType
} from '../../../types/payroll';

const PayrollSettings: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [settings, setSettings] = useState<PayrollSettingsType | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // Fetch settings (simulated)
  useEffect(() => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      const mockSettings: PayrollSettingsType = {
        payrollFrequency: PayrollFrequency.MONTHLY,
        payrollStartDay: 1,
        payrollEndDay: 31,
        paymentDay: 5,
        defaultPaymentMethod: PaymentMethod.BANK_TRANSFER,
        taxSettings: {
          enableTaxCalculation: true,
          taxCalculationType: TaxCalculationType.SLAB_BASED,
          standardTaxRate: 10,
          taxSlabs: [
            { id: 1, minAmount: 0, maxAmount: 50000, rate: 0, additionalAmount: 0, description: 'No tax' },
            { id: 2, minAmount: 50001, maxAmount: 100000, rate: 5, additionalAmount: 0, description: '5% on amount exceeding 50,000' },
            { id: 3, minAmount: 100001, maxAmount: 200000, rate: 10, additionalAmount: 2500, description: '10% on amount exceeding 100,000 plus 2,500' },
            { id: 4, minAmount: 200001, maxAmount: 300000, rate: 15, additionalAmount: 12500, description: '15% on amount exceeding 200,000 plus 12,500' },
            { id: 5, minAmount: 300001, maxAmount: null, rate: 20, additionalAmount: 27500, description: '20% on amount exceeding 300,000 plus 27,500' }
          ]
        },
        allowanceSettings: {
          housingAllowancePercentage: 20,
          transportAllowancePercentage: 10,
          medicalAllowancePercentage: 10,
          otherAllowancePercentage: 5
        },
        deductionSettings: {
          providentFundPercentage: 5,
          insurancePercentage: 2,
          enableProvidentFund: true,
          enableInsurance: true
        },
        approvalSettings: {
          requireApproval: true,
          approvalWorkflow: 'single',
          approvers: [1, 2] // Manager IDs
        },
        emailSettings: {
          sendPayslipEmails: true,
          emailTemplate: 'default',
          ccEmails: ['<EMAIL>']
        }
      };
      
      setSettings(mockSettings);
      setLoading(false);
    }, 1000);
  }, []);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>,
    section?: string,
    subsection?: string
  ) => {
    if (!settings) return;
    
    const { name, value, type } = e.target;
    let updatedSettings = { ...settings };
    
    // Handle numeric inputs
    const numericValue = type === 'number' ? 
      Number(value) : 
      value;
      
    // Handle checkboxes
    const finalValue = type === 'checkbox' ? 
      (e.target as HTMLInputElement).checked : 
      numericValue;
    
    // Update nested object if section is provided
    if (section) {
      if (subsection) {
        updatedSettings = {
          ...updatedSettings,
          [section]: {
            ...updatedSettings[section as keyof PayrollSettingsType] as any,
            [subsection]: {
              ...(updatedSettings[section as keyof PayrollSettingsType] as any)[subsection],
              [name]: finalValue
            }
          }
        };
      } else {
        updatedSettings = {
          ...updatedSettings,
          [section]: {
            ...updatedSettings[section as keyof PayrollSettingsType] as any,
            [name]: finalValue
          }
        };
      }
    } else {
      updatedSettings = {
        ...updatedSettings,
        [name]: finalValue
      };
    }
    
    setSettings(updatedSettings);
    setHasChanges(true);
  };

  const handleSaveSettings = () => {
    // Save settings to API
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      setHasChanges(false);
      alert('Settings saved successfully!');
    }, 1000);
  };

  if (loading && !settings) {
    return (
      <div className="p-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-5 w-5 text-yellow-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Could not load payroll settings. Please try again later.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Payroll Settings</h3>
          <p className="text-sm text-gray-500">Configure your payroll system parameters</p>
        </div>
        
        <button
          onClick={handleSaveSettings}
          disabled={!hasChanges || loading}
          className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${hasChanges ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-300 cursor-not-allowed'}`}
        >
          {loading ? (
            <div className="mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          ) : (
            <Save className="mr-2 h-4 w-4" />
          )}
          Save Settings
        </button>
      </div>
      
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <div className="px-4 py-5 sm:px-6 bg-gray-50 border-b border-gray-200">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            General Payroll Settings
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Configure basic payroll parameters and default values.
          </p>
        </div>
        
        <div className="px-4 py-5 sm:p-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
          <div className="sm:col-span-3">
            <label htmlFor="payrollFrequency" className="block text-sm font-medium text-gray-700">
              Payroll Frequency
            </label>
            <select
              id="payrollFrequency"
              name="payrollFrequency"
              value={settings.payrollFrequency}
              onChange={(e) => handleInputChange(e)}
              className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value={PayrollFrequency.MONTHLY}>Monthly</option>
              <option value={PayrollFrequency.BIWEEKLY}>Bi-Weekly</option>
              <option value={PayrollFrequency.WEEKLY}>Weekly</option>
              <option value={PayrollFrequency.DAILY}>Daily</option>
              <option value={PayrollFrequency.CUSTOM}>Custom</option>
            </select>
          </div>

          <div className="sm:col-span-3">
            <label htmlFor="defaultPaymentMethod" className="block text-sm font-medium text-gray-700">
              Default Payment Method
            </label>
            <select
              id="defaultPaymentMethod"
              name="defaultPaymentMethod"
              value={settings.defaultPaymentMethod}
              onChange={(e) => handleInputChange(e)}
              className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value={PaymentMethod.BANK_TRANSFER}>Bank Transfer</option>
              <option value={PaymentMethod.CASH}>Cash</option>
              <option value={PaymentMethod.CHECK}>Check</option>
              <option value={PaymentMethod.MOBILE_PAYMENT}>Mobile Payment</option>
              <option value={PaymentMethod.SPLIT_PAYMENT}>Split Payment</option>
            </select>
          </div>

          <div className="sm:col-span-2">
            <label htmlFor="payrollStartDay" className="block text-sm font-medium text-gray-700">
              Payroll Start Day
            </label>
            <input
              type="number"
              name="payrollStartDay"
              id="payrollStartDay"
              min="1"
              max="31"
              value={settings.payrollStartDay}
              onChange={(e) => handleInputChange(e)}
              className="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
            />
          </div>

          <div className="sm:col-span-2">
            <label htmlFor="payrollEndDay" className="block text-sm font-medium text-gray-700">
              Payroll End Day
            </label>
            <input
              type="number"
              name="payrollEndDay"
              id="payrollEndDay"
              min="1"
              max="31"
              value={settings.payrollEndDay}
              onChange={(e) => handleInputChange(e)}
              className="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
            />
          </div>

          <div className="sm:col-span-2">
            <label htmlFor="paymentDay" className="block text-sm font-medium text-gray-700">
              Payment Day
            </label>
            <input
              type="number"
              name="paymentDay"
              id="paymentDay"
              min="1"
              max="31"
              value={settings.paymentDay}
              onChange={(e) => handleInputChange(e)}
              className="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
            />
          </div>
        </div>
        
        {/* Allowance Settings */}
        <div className="px-4 py-5 sm:px-6 bg-gray-50 border-t border-b border-gray-200">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Allowance Percentages
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Configure default allowance percentages based on base salary.
          </p>
        </div>
        
        <div className="px-4 py-5 sm:p-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-4">
          <div className="sm:col-span-1">
            <label htmlFor="housingAllowancePercentage" className="block text-sm font-medium text-gray-700">
              Housing Allowance (%)
            </label>
            <input
              type="number"
              name="housingAllowancePercentage"
              id="housingAllowancePercentage"
              min="0"
              max="100"
              value={settings.allowanceSettings.housingAllowancePercentage}
              onChange={(e) => handleInputChange(e, 'allowanceSettings')}
              className="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
            />
          </div>

          <div className="sm:col-span-1">
            <label htmlFor="transportAllowancePercentage" className="block text-sm font-medium text-gray-700">
              Transport Allowance (%)
            </label>
            <input
              type="number"
              name="transportAllowancePercentage"
              id="transportAllowancePercentage"
              min="0"
              max="100"
              value={settings.allowanceSettings.transportAllowancePercentage}
              onChange={(e) => handleInputChange(e, 'allowanceSettings')}
              className="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
            />
          </div>

          <div className="sm:col-span-1">
            <label htmlFor="medicalAllowancePercentage" className="block text-sm font-medium text-gray-700">
              Medical Allowance (%)
            </label>
            <input
              type="number"
              name="medicalAllowancePercentage"
              id="medicalAllowancePercentage"
              min="0"
              max="100"
              value={settings.allowanceSettings.medicalAllowancePercentage}
              onChange={(e) => handleInputChange(e, 'allowanceSettings')}
              className="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
            />
          </div>

          <div className="sm:col-span-1">
            <label htmlFor="otherAllowancePercentage" className="block text-sm font-medium text-gray-700">
              Other Allowances (%)
            </label>
            <input
              type="number"
              name="otherAllowancePercentage"
              id="otherAllowancePercentage"
              min="0"
              max="100"
              value={settings.allowanceSettings.otherAllowancePercentage}
              onChange={(e) => handleInputChange(e, 'allowanceSettings')}
              className="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
            />
          </div>
        </div>
        
        {/* Tax Settings */}
        <div className="px-4 py-5 sm:px-6 bg-gray-50 border-t border-b border-gray-200">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Tax Settings
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Configure tax calculation method and rates.
          </p>
        </div>
        
        <div className="px-4 py-5 sm:p-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
          <div className="sm:col-span-3">
            <div className="flex items-center">
              <input
                id="enableTaxCalculation"
                name="enableTaxCalculation"
                type="checkbox"
                checked={settings.taxSettings.enableTaxCalculation}
                onChange={(e) => handleInputChange(e, 'taxSettings')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="enableTaxCalculation" className="ml-2 block text-sm text-gray-700">
                Enable Tax Calculation
              </label>
            </div>
          </div>

          <div className="sm:col-span-3">
            <label htmlFor="taxCalculationType" className="block text-sm font-medium text-gray-700">
              Tax Calculation Type
            </label>
            <select
              id="taxCalculationType"
              name="taxCalculationType"
              value={settings.taxSettings.taxCalculationType}
              onChange={(e) => handleInputChange(e, 'taxSettings')}
              disabled={!settings.taxSettings.enableTaxCalculation}
              className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value={TaxCalculationType.FIXED}>Fixed Amount</option>
              <option value={TaxCalculationType.PERCENTAGE}>Fixed Percentage</option>
              <option value={TaxCalculationType.SLAB_BASED}>Slab Based</option>
              <option value={TaxCalculationType.EXEMPT}>Tax Exempt</option>
            </select>
          </div>

          {settings.taxSettings.taxCalculationType === TaxCalculationType.PERCENTAGE && (
            <div className="sm:col-span-2">
              <label htmlFor="standardTaxRate" className="block text-sm font-medium text-gray-700">
                Standard Tax Rate (%)
              </label>
              <input
                type="number"
                name="standardTaxRate"
                id="standardTaxRate"
                min="0"
                max="100"
                value={settings.taxSettings.standardTaxRate}
                onChange={(e) => handleInputChange(e, 'taxSettings')}
                disabled={!settings.taxSettings.enableTaxCalculation}
                className="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          )}

          {settings.taxSettings.taxCalculationType === TaxCalculationType.SLAB_BASED && (
            <div className="sm:col-span-6 mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">Tax Slabs</label>
              <div className="bg-white shadow overflow-hidden border border-gray-200 sm:rounded-lg">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Min Amount
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Max Amount
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tax Rate (%)
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Additional Amount
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {settings.taxSettings.taxSlabs && settings.taxSettings.taxSlabs.map((slab) => (
                      <tr key={slab.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {slab.minAmount.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {slab.maxAmount ? slab.maxAmount.toLocaleString() : 'Unlimited'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {slab.rate}%
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {slab.additionalAmount.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500">
                          {slab.description}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <p className="mt-2 text-xs text-gray-500">
                Tax slabs can be configured in more detail from the tax settings page.
              </p>
            </div>
          )}
        </div>
        
        {/* Approval Settings */}
        <div className="px-4 py-5 sm:px-6 bg-gray-50 border-t border-b border-gray-200">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Approval Settings
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Configure payroll approval workflow.
          </p>
        </div>
        
        <div className="px-4 py-5 sm:p-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
          <div className="sm:col-span-3">
            <div className="flex items-center">
              <input
                id="requireApproval"
                name="requireApproval"
                type="checkbox"
                checked={settings.approvalSettings.requireApproval}
                onChange={(e) => handleInputChange(e, 'approvalSettings')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="requireApproval" className="ml-2 block text-sm text-gray-700">
                Require Approval for Payroll Processing
              </label>
            </div>
          </div>

          <div className="sm:col-span-3">
            <label htmlFor="approvalWorkflow" className="block text-sm font-medium text-gray-700">
              Approval Workflow
            </label>
            <select
              id="approvalWorkflow"
              name="approvalWorkflow"
              value={settings.approvalSettings.approvalWorkflow}
              onChange={(e) => handleInputChange(e, 'approvalSettings')}
              disabled={!settings.approvalSettings.requireApproval}
              className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="single">Single Approver</option>
              <option value="multi">Multiple Approvers (Sequential)</option>
            </select>
          </div>
        </div>

        {/* Email Settings */}
        <div className="px-4 py-5 sm:px-6 bg-gray-50 border-t border-b border-gray-200">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Email Notifications
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            Configure payslip email notifications.
          </p>
        </div>
        
        <div className="px-4 py-5 sm:p-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
          <div className="sm:col-span-3">
            <div className="flex items-center">
              <input
                id="sendPayslipEmails"
                name="sendPayslipEmails"
                type="checkbox"
                checked={settings.emailSettings.sendPayslipEmails}
                onChange={(e) => handleInputChange(e, 'emailSettings')}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="sendPayslipEmails" className="ml-2 block text-sm text-gray-700">
                Send Payslip Emails to Employees
              </label>
            </div>
          </div>

          <div className="sm:col-span-3">
            <label htmlFor="emailTemplate" className="block text-sm font-medium text-gray-700">
              Email Template
            </label>
            <select
              id="emailTemplate"
              name="emailTemplate"
              value={settings.emailSettings.emailTemplate}
              onChange={(e) => handleInputChange(e, 'emailSettings')}
              disabled={!settings.emailSettings.sendPayslipEmails}
              className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="default">Default Template</option>
              <option value="formal">Formal Template</option>
              <option value="minimal">Minimal Template</option>
            </select>
          </div>

          <div className="sm:col-span-6">
            <label htmlFor="ccEmails" className="block text-sm font-medium text-gray-700">
              CC Emails
            </label>
            <input
              type="text"
              name="ccEmails"
              id="ccEmails"
              value={settings.emailSettings.ccEmails?.join(', ') || ''}
              onChange={(e) => {
                const emails = e.target.value.split(',').map(email => email.trim());
                setSettings({
                  ...settings,
                  emailSettings: {
                    ...settings.emailSettings,
                    ccEmails: emails.filter(email => email)
                  }
                });
                setHasChanges(true);
              }}
              disabled={!settings.emailSettings.sendPayslipEmails}
              placeholder="<EMAIL>, <EMAIL>"
              className="mt-1 focus:ring-blue-500 focus:border-blue-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
            />
            <p className="mt-2 text-xs text-gray-500">
              Separate multiple email addresses with commas.
            </p>
          </div>
        </div>
      </div>
      
      <div className="mt-6 flex justify-end">
        <button
          onClick={handleSaveSettings}
          disabled={!hasChanges || loading}
          className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${hasChanges ? 'bg-blue-600 hover:bg-blue-700' : 'bg-gray-300 cursor-not-allowed'}`}
        >
          {loading ? (
            <div className="mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          ) : (
            <Save className="mr-2 h-4 w-4" />
          )}
          Save Settings
        </button>
      </div>
    </div>
  );
};

export default PayrollSettings; 