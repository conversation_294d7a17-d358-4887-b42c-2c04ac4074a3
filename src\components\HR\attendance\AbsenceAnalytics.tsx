import React, { useState, useEffect } from 'react';
import { Bar<PERSON>hart3, Pie<PERSON>hart, Calendar, Users, TrendingUp, AlertCircle, CheckCircle, Clock, Calendar as CalendarIcon, Filter, Download } from 'lucide-react';
import { Attendance, AttendanceStatus } from '../../../types/attendance';

interface AbsenceAnalyticsProps {
  attendances: Attendance[];
}

const AbsenceAnalytics: React.FC<AbsenceAnalyticsProps> = ({ attendances }) => {
  const [selectedDateRange, setSelectedDateRange] = useState<'week' | 'month' | 'quarter'>('month');
  const [selectedDepartment, setSelectedDepartment] = useState<string>('all');
  const [dateFrom, setDateFrom] = useState<string>('');
  const [dateTo, setDateTo] = useState<string>('');
  const [departments, setDepartments] = useState<string[]>([]);
  
  // Metrics
  const [absenceMetrics, setAbsenceMetrics] = useState({
    absenceRate: 0,
    mostCommonDayForAbsence: '',
    lateArrivalRate: 0,
    earlyLeaveRate: 0,
    consecutiveAbsenceCount: 0,
    mondayFridayAbsenceRate: 0,
  });
  
  // Absence patterns by day of week
  const [absenceByDayOfWeek, setAbsenceByDayOfWeek] = useState<Record<string, number>>({
    'Monday': 0,
    'Tuesday': 0,
    'Wednesday': 0,
    'Thursday': 0,
    'Friday': 0,
    'Saturday': 0,
    'Sunday': 0,
  });
  
  // Absence patterns by employee
  const [employeeAbsenceRate, setEmployeeAbsenceRate] = useState<Array<{employeeName: string, absenceRate: number}>>([]);
  
  // Department absence rates
  const [departmentAbsence, setDepartmentAbsence] = useState<Array<{department: string, absenceRate: number}>>([]);

  useEffect(() => {
    // Extract all departments from attendances
    const deptSet = new Set<string>();
    attendances.forEach(attendance => {
      if (attendance.department) {
        deptSet.add(attendance.department);
      }
    });
    setDepartments(Array.from(deptSet));
    
    // Set default date range (last month)
    const today = new Date();
    const lastMonth = new Date(today);
    lastMonth.setMonth(lastMonth.getMonth() - 1);
    
    setDateFrom(lastMonth.toISOString().split('T')[0]);
    setDateTo(today.toISOString().split('T')[0]);
  }, [attendances]);

  useEffect(() => {
    // When date range or department selection changes, recalculate metrics
    calculateMetrics();
  }, [selectedDateRange, selectedDepartment, dateFrom, dateTo, attendances]);

  const handleDateRangeChange = (range: 'week' | 'month' | 'quarter') => {
    setSelectedDateRange(range);
    
    const today = new Date();
    let fromDate = new Date(today);
    
    switch (range) {
      case 'week':
        fromDate.setDate(fromDate.getDate() - 7);
        break;
      case 'month':
        fromDate.setMonth(fromDate.getMonth() - 1);
        break;
      case 'quarter':
        fromDate.setMonth(fromDate.getMonth() - 3);
        break;
    }
    
    setDateFrom(fromDate.toISOString().split('T')[0]);
    setDateTo(today.toISOString().split('T')[0]);
  };

  const calculateMetrics = () => {
    // Filter attendances by date range and department
    const filteredAttendances = attendances.filter(attendance => {
      const attendanceDate = new Date(attendance.date);
      const fromDateObj = new Date(dateFrom);
      const toDateObj = new Date(dateTo);
      
      // Filter by date range
      const isInDateRange = attendanceDate >= fromDateObj && attendanceDate <= toDateObj;
      
      // Filter by department if not 'all'
      const isInDepartment = selectedDepartment === 'all' || attendance.department === selectedDepartment;
      
      return isInDateRange && isInDepartment;
    });
    
    if (filteredAttendances.length === 0) {
      // No data available
      return;
    }
    
    // Calculate absence rate
    const absences = filteredAttendances.filter(a => 
      a.status === AttendanceStatus.ABSENT || 
      a.status === AttendanceStatus.LEAVE
    ).length;
    
    const absenceRate = (absences / filteredAttendances.length) * 100;
    
    // Calculate most common day for absence
    const absentAttendances = filteredAttendances.filter(a => 
      a.status === AttendanceStatus.ABSENT || 
      a.status === AttendanceStatus.LEAVE
    );
    
    const dayOfWeekCounts = {
      'Monday': 0,
      'Tuesday': 0,
      'Wednesday': 0,
      'Thursday': 0,
      'Friday': 0,
      'Saturday': 0,
      'Sunday': 0,
    };
    
    absentAttendances.forEach(a => {
      const date = new Date(a.date);
      const dayOfWeek = date.toLocaleDateString('en-US', { weekday: 'long' });
      dayOfWeekCounts[dayOfWeek]++;
    });
    
    // Find most common day
    let maxDay = 'Monday';
    let maxCount = 0;
    Object.entries(dayOfWeekCounts).forEach(([day, count]) => {
      if (count > maxCount) {
        maxDay = day;
        maxCount = count;
      }
    });
    
    setAbsenceByDayOfWeek(dayOfWeekCounts);
    
    // Calculate late arrival rate
    const lateArrivals = filteredAttendances.filter(a => a.status === AttendanceStatus.LATE).length;
    const lateArrivalRate = (lateArrivals / filteredAttendances.length) * 100;
    
    // Calculate Monday/Friday absence rate
    const mondayFridays = filteredAttendances.filter(a => {
      const date = new Date(a.date);
      const day = date.getDay();
      return day === 1 || day === 5; // Monday is 1, Friday is 5
    });
    
    const mondayFridayAbsences = mondayFridays.filter(a => 
      a.status === AttendanceStatus.ABSENT || 
      a.status === AttendanceStatus.LEAVE
    ).length;
    
    const mondayFridayAbsenceRate = mondayFridays.length > 0 
      ? (mondayFridayAbsences / mondayFridays.length) * 100 
      : 0;
    
    // Calculate absence rate by employee
    const employeeMap = new Map<string, { total: number, absences: number }>();
    
    filteredAttendances.forEach(a => {
      if (!employeeMap.has(a.employeeName)) {
        employeeMap.set(a.employeeName, { total: 0, absences: 0 });
      }
      
      const record = employeeMap.get(a.employeeName)!;
      record.total++;
      
      if (a.status === AttendanceStatus.ABSENT || a.status === AttendanceStatus.LEAVE) {
        record.absences++;
      }
    });
    
    const employeeAbsenceData = Array.from(employeeMap.entries()).map(([name, data]) => ({
      employeeName: name,
      absenceRate: (data.absences / data.total) * 100
    })).sort((a, b) => b.absenceRate - a.absenceRate);
    
    setEmployeeAbsenceRate(employeeAbsenceData);
    
    // Calculate department absence rates
    const departmentMap = new Map<string, { total: number, absences: number }>();
    
    filteredAttendances.forEach(a => {
      const dept = a.department || 'Unassigned';
      
      if (!departmentMap.has(dept)) {
        departmentMap.set(dept, { total: 0, absences: 0 });
      }
      
      const record = departmentMap.get(dept)!;
      record.total++;
      
      if (a.status === AttendanceStatus.ABSENT || a.status === AttendanceStatus.LEAVE) {
        record.absences++;
      }
    });
    
    const departmentAbsenceData = Array.from(departmentMap.entries()).map(([dept, data]) => ({
      department: dept,
      absenceRate: (data.absences / data.total) * 100
    })).sort((a, b) => b.absenceRate - a.absenceRate);
    
    setDepartmentAbsence(departmentAbsenceData);
    
    // Update all metrics
    setAbsenceMetrics({
      absenceRate: parseFloat(absenceRate.toFixed(1)),
      mostCommonDayForAbsence: maxDay,
      lateArrivalRate: parseFloat(lateArrivalRate.toFixed(1)),
      earlyLeaveRate: 0, // Would need check-out time data to calculate this
      consecutiveAbsenceCount: calculateConsecutiveAbsences(filteredAttendances),
      mondayFridayAbsenceRate: parseFloat(mondayFridayAbsenceRate.toFixed(1)),
    });
  };
  
  const calculateConsecutiveAbsences = (attendances: Attendance[]): number => {
    // Group by employee
    const employeeAttendances = new Map<number, Attendance[]>();
    
    attendances.forEach(a => {
      if (!employeeAttendances.has(a.employeeId)) {
        employeeAttendances.set(a.employeeId, []);
      }
      employeeAttendances.get(a.employeeId)!.push(a);
    });
    
    let maxConsecutive = 0;
    
    // For each employee, check consecutive absences
    employeeAttendances.forEach(attendances => {
      // Sort by date
      attendances.sort((a, b) => 
        new Date(a.date).getTime() - new Date(b.date).getTime()
      );
      
      let currentStreak = 0;
      let maxStreak = 0;
      
      for (const attendance of attendances) {
        if (attendance.status === AttendanceStatus.ABSENT || 
            attendance.status === AttendanceStatus.LEAVE) {
          currentStreak++;
          maxStreak = Math.max(maxStreak, currentStreak);
        } else {
          currentStreak = 0;
        }
      }
      
      maxConsecutive = Math.max(maxConsecutive, maxStreak);
    });
    
    return maxConsecutive;
  };
  
  const exportCSV = () => {
    // Create CSV content
    const headers = [
      'Metric', 'Value'
    ];
    
    const rows = [
      ['Absence Rate (%)', absenceMetrics.absenceRate],
      ['Most Common Day for Absence', absenceMetrics.mostCommonDayForAbsence],
      ['Late Arrival Rate (%)', absenceMetrics.lateArrivalRate],
      ['Monday/Friday Absence Rate (%)', absenceMetrics.mondayFridayAbsenceRate],
      ['Maximum Consecutive Absences', absenceMetrics.consecutiveAbsenceCount],
      ['', ''],
      ['Absence Rate by Day of Week', ''],
    ];
    
    Object.entries(absenceByDayOfWeek).forEach(([day, count]) => {
      rows.push([day, count]);
    });
    
    rows.push(['', '']);
    rows.push(['Absence Rate by Department', '']);
    
    departmentAbsence.forEach(dept => {
      rows.push([dept.department, dept.absenceRate.toFixed(1) + '%']);
    });
    
    rows.push(['', '']);
    rows.push(['Absence Rate by Employee', '']);
    
    employeeAbsenceRate.forEach(emp => {
      rows.push([emp.employeeName, emp.absenceRate.toFixed(1) + '%']);
    });
    
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');
    
    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `absence-analytics-${dateFrom}-to-${dateTo}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  const renderBarChart = (data: Record<string, number>) => {
    const maxValue = Math.max(...Object.values(data));
    
    return (
      <div className="w-full h-64 flex flex-col mt-2">
        <div className="flex items-end h-52 space-x-2">
          {Object.entries(data).map(([key, value]) => (
            <div key={key} className="flex flex-col items-center flex-1">
              <div 
                className="w-full bg-blue-500 rounded-t" 
                style={{ 
                  height: `${maxValue > 0 ? (value / maxValue) * 100 : 0}%`,
                  minHeight: value > 0 ? '8px' : '0'
                }}
              ></div>
            </div>
          ))}
        </div>
        <div className="flex justify-between mt-2">
          {Object.keys(data).map(key => (
            <div key={key} className="text-xs text-gray-500 text-center w-8">
              {key.substring(0, 3)}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center">
          <div className="h-10 w-10 rounded-lg bg-indigo-600 mr-3 flex items-center justify-center">
            <TrendingUp className="text-white h-6 w-6" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-800">Absence Patterns & Analytics</h2>
            <p className="text-sm text-gray-500">Analyze attendance patterns and identify trends</p>
          </div>
        </div>
      </div>
      
      <div className="p-4">
        {/* Filters */}
        <div className="mb-6 bg-gray-50 p-4 rounded-lg">
          <div className="flex flex-wrap items-center gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
              <div className="flex rounded-md shadow-sm">
                <button
                  onClick={() => handleDateRangeChange('week')}
                  className={`px-3 py-2 text-sm font-medium rounded-l-md ${
                    selectedDateRange === 'week'
                      ? 'bg-indigo-600 text-white'
                      : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Week
                </button>
                <button
                  onClick={() => handleDateRangeChange('month')}
                  className={`px-3 py-2 text-sm font-medium ${
                    selectedDateRange === 'month'
                      ? 'bg-indigo-600 text-white'
                      : 'bg-white border-t border-b border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Month
                </button>
                <button
                  onClick={() => handleDateRangeChange('quarter')}
                  className={`px-3 py-2 text-sm font-medium rounded-r-md ${
                    selectedDateRange === 'quarter'
                      ? 'bg-indigo-600 text-white'
                      : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  Quarter
                </button>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">From</label>
              <input
                type="date"
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">To</label>
              <input
                type="date"
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
              >
                <option value="all">All Departments</option>
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
            </div>
            
            <div className="ml-auto self-end">
              <button 
                onClick={exportCSV} 
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none"
              >
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </button>
            </div>
          </div>
        </div>
        
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-start">
              <div className="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center mr-3">
                <AlertCircle className="h-6 w-6 text-red-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Absence Rate</p>
                <div className="mt-1 flex items-baseline">
                  <p className="text-2xl font-semibold text-gray-900">{absenceMetrics.absenceRate}%</p>
                  <p className="ml-2 text-sm text-gray-500">of total attendance</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-start">
              <div className="h-10 w-10 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                <Calendar className="h-6 w-6 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Most Common Absence Day</p>
                <div className="mt-1 flex items-baseline">
                  <p className="text-2xl font-semibold text-gray-900">{absenceMetrics.mostCommonDayForAbsence}</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-start">
              <div className="h-10 w-10 rounded-full bg-orange-100 flex items-center justify-center mr-3">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Late Arrival Rate</p>
                <div className="mt-1 flex items-baseline">
                  <p className="text-2xl font-semibold text-gray-900">{absenceMetrics.lateArrivalRate}%</p>
                  <p className="ml-2 text-sm text-gray-500">of total attendance</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-start">
              <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                <Calendar className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Monday/Friday Absence Rate</p>
                <div className="mt-1 flex items-baseline">
                  <p className="text-2xl font-semibold text-gray-900">{absenceMetrics.mondayFridayAbsenceRate}%</p>
                </div>
                <p className="text-xs text-gray-500 mt-1">Higher than weekday average may indicate pattern</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-start">
              <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                <AlertCircle className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Consecutive Absences</p>
                <div className="mt-1 flex items-baseline">
                  <p className="text-2xl font-semibold text-gray-900">{absenceMetrics.consecutiveAbsenceCount}</p>
                  <p className="ml-2 text-sm text-gray-500">maximum streak</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Absence by Day of Week</h3>
            {renderBarChart(absenceByDayOfWeek)}
          </div>
          
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Department Absence Rates</h3>
            <div className="mt-4">
              {departmentAbsence.map((dept, index) => (
                <div key={dept.department} className="mb-3">
                  <div className="flex justify-between mb-1">
                    <span className="text-sm font-medium text-gray-700">{dept.department}</span>
                    <span className="text-sm text-gray-500">{dept.absenceRate.toFixed(1)}%</span>
                  </div>
                  <div className="overflow-hidden h-2 text-xs flex rounded bg-gray-200">
                    <div 
                      style={{ width: `${Math.min(100, dept.absenceRate)}%` }}
                      className={`shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center ${
                        index === 0 ? 'bg-red-500' : index === 1 ? 'bg-orange-500' : 'bg-blue-500'
                      }`}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* Employee Absence Ranking */}
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Employee Absence Ranking</h3>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Rank
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Employee
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Absence Rate
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {employeeAbsenceRate.length === 0 ? (
                  <tr>
                    <td colSpan={4} className="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">
                      No data available
                    </td>
                  </tr>
                ) : (
                  employeeAbsenceRate.slice(0, 10).map((employee, index) => (
                    <tr key={employee.employeeName} className={index < 3 ? 'bg-red-50' : ''}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {index + 1}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600">
                            {employee.employeeName.split(' ').map(name => name[0]).join('')}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{employee.employeeName}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2">
                            <div 
                              className={`bg-${index < 3 ? 'red' : index < 6 ? 'yellow' : 'green'}-500 h-2.5 rounded-full`} 
                              style={{ width: `${Math.min(100, employee.absenceRate)}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-900">
                            {employee.absenceRate.toFixed(1)}%
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          employee.absenceRate > 20 
                            ? 'bg-red-100 text-red-800' 
                            : employee.absenceRate > 10
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-green-100 text-green-800'
                        }`}>
                          {employee.absenceRate > 20 
                            ? 'High Absence' 
                            : employee.absenceRate > 10
                              ? 'Moderate'
                              : 'Normal'}
                        </span>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AbsenceAnalytics; 