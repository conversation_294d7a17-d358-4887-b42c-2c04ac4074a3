import { MigrationInterface, QueryRunner } from "typeorm";

export class AddProjectFieldToAssets1746000000003 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if the column already exists
        const table = await queryRunner.getTable("assets");
        const projectColumn = table?.findColumnByName("project");
        
        if (!projectColumn) {
            await queryRunner.query(`ALTER TABLE assets ADD COLUMN project VARCHAR(100) NULL`);
            console.log('Added project column to assets table');
        } else {
            console.log('Project column already exists in assets table');
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE assets DROP COLUMN project`);
        console.log('Removed project column from assets table');
    }
} 