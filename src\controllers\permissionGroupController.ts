import { Request, Response } from 'express';
import { getRepository } from 'typeorm';
import { PermissionGroup } from '../entities/PermissionGroup';
import { AppDataSource } from '../config/database';

// Get all permission groups
export const getPermissionGroups = async (req: Request, res: Response) => {
  try {
    // Check if database is connected
    if (!AppDataSource.isInitialized) {
      console.error('Database connection not initialized');
      // Return fallback permission groups
      return res.json({ 
        permissionGroups: getDefaultPermissionGroups() 
      });
    }
    
    try {
      const groupRepository = AppDataSource.getRepository(PermissionGroup);
      const permissionGroups = await groupRepository.find();
      return res.json({ permissionGroups });
    } catch (dbError: any) {
      console.error('Database query error:', dbError);
      // Return fallback permission groups
      return res.json({ 
        permissionGroups: getDefaultPermissionGroups() 
      });
    }
  } catch (error: any) {
    console.error('Error fetching permission groups:', error);
    // Return fallback permission groups
    return res.json({ 
      permissionGroups: getDefaultPermissionGroups() 
    });
  }
};

// Helper function to get default permission groups
function getDefaultPermissionGroups() {
  return [
    {
      id: 'group-admin',
      name: 'Administrative Permissions',
      description: 'Core system administration permissions',
      permissions: [
        'canAccessAllModules', 'canConfigureSystem', 'canManageRoles', 
        'canAddUsers', 'canEditUsers', 'canDeleteUsers'
      ],
      createdAt: new Date().toISOString()
    },
    {
      id: 'group-tickets',
      name: 'Ticket Management',
      description: 'Permissions for managing help desk tickets',
      permissions: [
        'canCreateTickets', 'canEditTickets', 'canDeleteTickets', 
        'canCloseTickets', 'canViewAllTickets'
      ],
      createdAt: new Date().toISOString()
    },
    {
      id: 'group-dashboards',
      name: 'Dashboard Access',
      description: 'Permissions for accessing and managing dashboards',
      permissions: [
        'canViewDashboards', 'canViewAllDashboards', 'canCreateDashboards', 
        'canEditDashboards', 'canShareDashboards'
      ],
      createdAt: new Date().toISOString()
    }
  ];
}

// Get permission group by ID
export const getPermissionGroupById = async (req: Request, res: Response) => {
  try {
    const groupRepository = getRepository(PermissionGroup);
    const group = await groupRepository.findOne({ where: { id: req.params.id } });
    
    if (!group) {
      return res.status(404).json({ message: 'Permission group not found' });
    }
    
    return res.json(group);
  } catch (error) {
    console.error('Error fetching permission group:', error);
    return res.status(500).json({ message: 'Server error while fetching permission group' });
  }
};

// Create a new permission group
export const createPermissionGroup = async (req: Request, res: Response) => {
  try {
    const groupRepository = getRepository(PermissionGroup);
    const { name, description, permissions } = req.body;
    
    // Check if group with same name already exists
    const existingGroup = await groupRepository.findOne({ where: { name } });
    if (existingGroup) {
      return res.status(400).json({ message: 'Permission group with this name already exists' });
    }
    
    const group = new PermissionGroup();
    group.name = name;
    group.description = description;
    group.permissions = permissions || [];
    group.isCustom = true; // All user-created groups are custom
    group.createdBy = req.user?.id || '';
    
    const savedGroup = await groupRepository.save(group);
    
    return res.status(201).json(savedGroup);
  } catch (error) {
    console.error('Error creating permission group:', error);
    return res.status(500).json({ message: 'Server error while creating permission group' });
  }
};

// Update an existing permission group
export const updatePermissionGroup = async (req: Request, res: Response) => {
  try {
    const groupRepository = getRepository(PermissionGroup);
    const { name, description, permissions } = req.body;
    
    const group = await groupRepository.findOne({ where: { id: req.params.id } });
    if (!group) {
      return res.status(404).json({ message: 'Permission group not found' });
    }
    
    // Only allow updating custom groups, not system ones
    if (!group.isCustom) {
      return res.status(403).json({ message: 'System permission groups cannot be modified' });
    }
    
    // Check for name conflict only if name is being changed
    if (name !== group.name) {
      const existingGroup = await groupRepository.findOne({ where: { name } });
      if (existingGroup) {
        return res.status(400).json({ message: 'Permission group with this name already exists' });
      }
    }
    
    group.name = name || group.name;
    group.description = description || group.description;
    group.permissions = permissions || group.permissions;
    
    const updatedGroup = await groupRepository.save(group);
    
    return res.json(updatedGroup);
  } catch (error) {
    console.error('Error updating permission group:', error);
    return res.status(500).json({ message: 'Server error while updating permission group' });
  }
};

// Delete a permission group
export const deletePermissionGroup = async (req: Request, res: Response) => {
  try {
    const groupRepository = getRepository(PermissionGroup);
    
    const group = await groupRepository.findOne({ where: { id: req.params.id } });
    if (!group) {
      return res.status(404).json({ message: 'Permission group not found' });
    }
    
    // Only allow deleting custom groups, not system ones
    if (!group.isCustom) {
      return res.status(403).json({ message: 'System permission groups cannot be deleted' });
    }
    
    await groupRepository.remove(group);
    
    return res.json({ message: 'Permission group deleted successfully' });
  } catch (error) {
    console.error('Error deleting permission group:', error);
    return res.status(500).json({ message: 'Server error while deleting permission group' });
  }
}; 