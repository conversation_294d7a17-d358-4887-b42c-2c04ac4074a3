import React, { useState, useRef, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Save,
  AlertCircle,
  X,
  Package,
  Building2,
  Calendar,
  DollarSign,
  Users,
  FileText,
  Upload,
  Tag,
  MessageSquare,
  Box,
  Store,
  Code,
  Key,
  UserCheck,
  UserX,
  CreditCard,
  Briefcase,
  CheckCircle,
  AlertTriangle,
  Clock,
  FileUp,
  StickyNote,
  Globe,
  Bell,
  Share2,
  Link,
  ChevronDown,
  ChevronUp,
  Lock,
  Globe2,
  MessageCircle,
  Shield,
  Network,
  Brain,
  Palette,
  Lock as LockIcon,
  MoreHorizontal,
  Wrench,
  BarChart,
  Cloud,
  Search,
  Info,
  KeyRound,
  MapPin,
  LogIn,
  ArrowLeft,
  Check,
  Plus,
  Laptop,
  Printer,
  Monitor,
  Smartphone,
  Tablet,
  Server,
  HardDrive,
  Wifi,
  Scan,
  Camera,
  Eye,
  EyeOff,
  History
} from 'lucide-react';
import { SoftwareLicenseService } from '../services/SoftwareLicenseService';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'react-hot-toast';
import { CreateSoftwareLicenseDto, LicenseType, PaymentFrequency, SoftwareStatus } from '../types/SoftwareLicense';
import { User } from '../entities/User';
import { Asset } from '../entities/Asset';
import { Vendor } from '../entities/Vendor';
import { AppDataSource } from '../config/database';
import axios from 'axios';

// Add DOM lib types for FormData
declare global {
  interface FormData {
    entries(): IterableIterator<[string, FormDataEntryValue]>;
  }
}

interface FormValues extends Omit<CreateSoftwareLicenseDto, 'purchaseDate' | 'expiryDate'> {
  purchaseDate: string;
  expiryDate: string;
  loginSharingInfo: string;
  multiLocation: boolean;
  loginUrl: string;
  loginEmail: string;
  password: string;
  softwareNature: string;
  paymentFrequency: PaymentFrequency;
  costPKR: number;
  costUSD: number;
  autoRenew: boolean;
  assignedUserIds: string[];
  linkedAssetIds: string[];
  notes: string;
  renewalReminder: boolean;
  socialMediaLinks: string[];
  paidBy: string;
}

const initialFormData: FormValues = {
  softwareName: '',
  vendorId: '',
  type: 'Monthly Subscription' as LicenseType,
  category: '',
  department: '',
  status: 'Active' as SoftwareStatus,
  licenseKey: '',
  totalSeats: 0,
  usedSeats: 0,
  purchaseDate: new Date().toISOString().split('T')[0],
  expiryDate: new Date().toISOString().split('T')[0],
  paymentFrequency: 'Monthly' as PaymentFrequency,
  costPKR: 0,
  costUSD: 0,
  autoRenew: false,
  assignedUserIds: [],
  linkedAssetIds: [],
  notes: '',
  multiLocation: false,
  renewalReminder: false,
  socialMediaLinks: [],
  loginUrl: '',
  loginEmail: '',
  password: '',
  softwareNature: 'desktop',
  paidBy: 'Company',
  loginSharingInfo: '',
};

export function SoftwareLicenseForm() {
  // Router hooks
  const { id } = useParams<{ id?: string }>();
  const navigate = useNavigate();
  const isEditMode = Boolean(id);
  
  // API data states
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [assets, setAssets] = useState<Asset[]>([]);
  const [softwareData, setSoftwareData] = useState<any>(null);
  const [pageLoading, setPageLoading] = useState(true);
  const [pageError, setPageError] = useState<string | null>(null);

  // Form states
  const [formData, setFormData] = useState<FormValues>(initialFormData);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [isCategoryOpen, setIsCategoryOpen] = useState(false);
  const categoryRef = useRef<HTMLDivElement>(null);
  const [userSearchQuery, setUserSearchQuery] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [isAssetsDropdownOpen, setIsAssetsDropdownOpen] = useState(false);
  const [assetSearchQuery, setAssetSearchQuery] = useState('');
  const assetsDropdownRef = useRef<HTMLDivElement>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useAuth();
  const [isDepartmentOpen, setIsDepartmentOpen] = useState(false);
  const departmentRef = useRef<HTMLDivElement>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [invoiceFile, setInvoiceFile] = useState<File | null>(null);

  // Helper function to get appropriate icon and color for asset types
  const getAssetTypeProperties = (assetType: string | undefined) => {
    const type = assetType?.toLowerCase() || '';
    
    if (type.includes('laptop') || type.includes('computer') || type.includes('pc')) {
      return { icon: <Laptop className="h-4 w-4" />, color: 'bg-blue-500', textColor: 'text-white' };
    } else if (type.includes('printer')) {
      return { icon: <Printer className="h-4 w-4" />, color: 'bg-red-500', textColor: 'text-white' };
    } else if (type.includes('monitor') || type.includes('display')) {
      return { icon: <Monitor className="h-4 w-4" />, color: 'bg-purple-500', textColor: 'text-white' };
    } else if (type.includes('server')) {
      return { icon: <Server className="h-4 w-4" />, color: 'bg-gray-700', textColor: 'text-white' };
    } else if (type.includes('phone') || type.includes('mobile')) {
      return { icon: <Smartphone className="h-4 w-4" />, color: 'bg-green-600', textColor: 'text-white' };
    } else if (type.includes('tablet')) {
      return { icon: <Tablet className="h-4 w-4" />, color: 'bg-indigo-500', textColor: 'text-white' };
    } else if (type.includes('network') || type.includes('router') || type.includes('switch')) {
      return { icon: <Wifi className="h-4 w-4" />, color: 'bg-cyan-600', textColor: 'text-white' };
    } else if (type.includes('scanner')) {
      return { icon: <Scan className="h-4 w-4" />, color: 'bg-orange-500', textColor: 'text-white' };
    } else if (type.includes('camera')) {
      return { icon: <Camera className="h-4 w-4" />, color: 'bg-pink-500', textColor: 'text-white' };
    } else {
      return { icon: <HardDrive className="h-4 w-4" />, color: 'bg-teal-500', textColor: 'text-white' };
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setPageLoading(true);
        const apiCalls = [
          axios.get('/api/vendors'),
          axios.get('/api/users'),
          axios.get('/api/assets')
        ];
        
        // Add software license fetch when in edit mode
        if (isEditMode) {
          apiCalls.push(axios.get(`/api/software-licenses/${id}`));
        }
        
        const responses = await Promise.all(apiCalls);
        
        setVendors(responses[0].data);
        let loadedUsers = responses[1].data as User[];
        
        // If no users are loaded, create sample users for display with UUIDs
        if (!Array.isArray(loadedUsers) || loadedUsers.length === 0) {
          console.log('No users returned from API, creating sample users');
          loadedUsers = [
            { id: '4fc3fa68-7acb-4055-b3a7-15cf2876f5cb', name: 'Admin', email: '<EMAIL>', department: 'IT' },
            { id: '2a118299-9841-476f-aaf0-27973a48d624', name: 'Admin', email: '<EMAIL>', department: 'IT' },
            { id: '096445df-8759-4b43-bf54-d4b48d121f56', name: 'Momin Ahsan', email: '<EMAIL>', department: 'IT' },
            { id: '7aa8c1b6-f848-40d7-a620-d05ceb91a752', name: 'Abdul Rehman', email: '<EMAIL>', department: 'IT' },
            { id: 'b2d712d2-1991-474f-bc67-6e3b90789034', name: 'Shoaib', email: '<EMAIL>', department: 'HR' }
          ] as User[];
        }
        
        setUsers(loadedUsers);
        console.log('Users loaded with UUIDs:', loadedUsers.map(u => ({ id: u.id, name: u.name })));
        
        setAssets(responses[2].data);
        
        if (isEditMode && responses.length > 3) {
          const softwareItem = responses[3].data;
          setSoftwareData(softwareItem);
          console.log('Loaded software license data:', softwareItem);
          
          // Check if assignedUsers array exists and has items
          let assignedUserIds: string[] = [];
          if (softwareItem.assignedUsers && Array.isArray(softwareItem.assignedUsers)) {
            console.log('Number of assigned users from API:', softwareItem.assignedUsers.length);
            console.log('Full assignedUsers data from API:', JSON.stringify(softwareItem.assignedUsers));
            
            // Extract IDs from assignedUsers objects, ensuring they're strings
            assignedUserIds = softwareItem.assignedUsers
              .filter((user: any) => user && user.id != null) // Filter out invalid user objects
              .map((user: any) => String(user.id)); // Convert all IDs to strings
            
            console.log('Extracted user IDs:', assignedUserIds);
            console.log('Types of IDs:', assignedUserIds.map(id => typeof id));
            
            // Check if we can find these users in our loaded users
            const foundUsers = loadedUsers.filter((user: any) => 
              assignedUserIds.includes(String(user.id))
            );
            
            console.log('Found matching users:', foundUsers.length, 
              'out of', assignedUserIds.length, 'assigned users');
            console.log('Found users details:', foundUsers.map(u => ({ id: u.id, name: u.name })));
            
            // If we couldn't find all users, log a warning
            if (foundUsers.length < assignedUserIds.length) {
              console.warn('Some assigned users could not be found in the loaded users list.');
              console.warn('Missing user IDs:', 
                assignedUserIds.filter(id => 
                  !loadedUsers.some((user: any) => String(user.id) === id)
                )
              );
            }
          } else {
            console.warn('No assignedUsers array in response or it is empty:', softwareItem.assignedUsers);
          }
          
          // Format dates properly for form fields
          const formatDate = (date: string | null | undefined) => {
            if (!date) return new Date().toISOString().split('T')[0];
            const d = new Date(date);
            return d.toISOString().split('T')[0];
          };
          
          // Create linked asset IDs array - ensure they're strings
          const linkedAssetIds = softwareItem.linkedAssets && Array.isArray(softwareItem.linkedAssets)
            ? softwareItem.linkedAssets.map((asset: any) => String(asset.id)) 
            : [];
            
            console.log('Assigned user IDs from API:', softwareItem.assignedUsers?.map((u: any) => String(u.id)) || []);
            console.log('Linked asset IDs from API:', linkedAssetIds);
            
            // Create a deep copy to avoid reference issues
            const formInitialData: FormValues = {
              softwareName: softwareItem.name,
              vendorId: String(softwareItem.vendor?.id || ''),
              type: softwareItem.type,
              category: softwareItem.category || '',
              department: softwareItem.department,
              status: softwareItem.status,
              licenseKey: softwareItem.licenseKey || '',
              totalSeats: softwareItem.totalSeats || 0,
              usedSeats: softwareItem.usedSeats || 0,
              purchaseDate: formatDate(softwareItem.purchaseDate),
              expiryDate: formatDate(softwareItem.expiryDate),
              paymentFrequency: softwareItem.paymentFrequency || 'Monthly' as PaymentFrequency,
              costPKR: softwareItem.costPKR || 0,
              costUSD: softwareItem.costUSD || 0,
              autoRenew: softwareItem.autoRenew || false,
              notes: softwareItem.notes || '',
              renewalReminder: softwareItem.renewalReminder || false,
              multiLocation: softwareItem.multiLocationUse || false,
              loginSharingInfo: softwareItem.loginSharingInfo || '',
              assignedUserIds: [...assignedUserIds],
              linkedAssetIds: [...linkedAssetIds],
              socialMediaLinks: softwareItem.socialMediaLinks || [],
              loginUrl: softwareItem.loginUrl || '',
              loginEmail: softwareItem.loginEmail || '',
              password: softwareItem.password || '',
              softwareNature: softwareItem.softwareNature || 'desktop',
              paidBy: softwareItem.paidBy || 'Company',
            };
            
            console.log('Form data to be set:', formInitialData);
            console.log('assignedUserIds type before setting form data:', 
                Array.isArray(formInitialData.assignedUserIds) ? 'Array' : typeof formInitialData.assignedUserIds);
            
            setFormData(formInitialData);
            
            console.log('Form data initialized:', { 
              assignedUserIds: formInitialData.assignedUserIds, 
              linkedAssetIds: formInitialData.linkedAssetIds 
            });
          } else if (!isEditMode) {
            // For new licenses, initialize with an empty form
            setFormData(initialFormData);
          }
        } catch (error) {
          console.error('Error fetching data:', error);
          toast.error('Failed to load necessary data');
          setPageError('Failed to load necessary data');
          
          // Create sample users if API fails - with UUIDs
          const sampleUsers = [
            { id: '4fc3fa68-7acb-4055-b3a7-15cf2876f5cb', name: 'Admin', email: '<EMAIL>', department: 'IT' },
            { id: '2a118299-9841-476f-aaf0-27973a48d624', name: 'Admin', email: '<EMAIL>', department: 'IT' },
            { id: '096445df-8759-4b43-bf54-d4b48d121f56', name: 'Momin Ahsan', email: '<EMAIL>', department: 'IT' },
            { id: '7aa8c1b6-f848-40d7-a620-d05ceb91a752', name: 'Abdul Rehman', email: '<EMAIL>', department: 'IT' },
            { id: 'b2d712d2-1991-474f-bc67-6e3b90789034', name: 'Shoaib', email: '<EMAIL>', department: 'HR' }
          ] as User[];
          setUsers(sampleUsers);
        } finally {
          setPageLoading(false);
        }
      };

      fetchData();
    }, [id, isEditMode]);

    // Handle outside clicks
    useEffect(() => {
      function handleClickOutside(event: MouseEvent) {
        if (categoryRef.current && !categoryRef.current.contains(event.target as Node)) {
          setIsCategoryOpen(false);
        }
        if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
          setIsDropdownOpen(false);
        }
        if (assetsDropdownRef.current && !assetsDropdownRef.current.contains(event.target as Node)) {
          setIsAssetsDropdownOpen(false);
        }
        if (departmentRef.current && !departmentRef.current.contains(event.target as Node)) {
          setIsDepartmentOpen(false);
        }
      }

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);

    // Filter users based on search query
    const filteredUsers = Array.isArray(users) 
      ? users.filter(user => 
          user.name?.toLowerCase().includes(userSearchQuery.toLowerCase()) ||
          user.email?.toLowerCase().includes(userSearchQuery.toLowerCase()) ||
          user.department?.toLowerCase().includes(userSearchQuery.toLowerCase())
        )
      : [];

    // Filter assets based on search query
    const filteredAssets = Array.isArray(assets)
      ? assets.filter(asset =>
      asset.assetTag?.toLowerCase().includes(assetSearchQuery.toLowerCase()) ||
      asset.assetType?.toLowerCase().includes(assetSearchQuery.toLowerCase()) ||
      asset.manufacturer?.toLowerCase().includes(assetSearchQuery.toLowerCase())
        )
      : [];

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
      const { name, value } = e.target;
      
      // Handle different input types appropriately
      if (name === 'type') {
        setFormData(prev => ({ ...prev, type: value as LicenseType }));
      } else if (name === 'status') {
        setFormData(prev => ({ ...prev, status: value as SoftwareStatus }));
      } else if (name === 'paymentFrequency') {
        setFormData(prev => ({ ...prev, paymentFrequency: value as PaymentFrequency }));
      } else if (name === 'totalSeats' || name === 'costPKR' || name === 'costUSD') {
        setFormData(prev => ({ ...prev, [name]: value === '' ? 0 : Number(value) }));
      } else {
        setFormData(prev => ({ ...prev, [name]: value }));
      }
    };

    const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, checked } = e.target;
      setFormData(prev => ({ ...prev, [name]: checked }));
    };

    // Handle file uploads
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files && e.target.files.length > 0) {
        setInvoiceFile(e.target.files[0]);
      }
    };

    // Debug function to log current state
    const logDebugInfo = () => {
      console.log('Current form state:', {
        assignedUserIds: formData.assignedUserIds,
        availableUsers: users.map(u => ({ id: u.id, name: u.name }))
      });
    };

    const handleUserSelect = (userId: string) => {
      // Force convert to string to ensure consistency
      const stringUserId = String(userId);
      console.log('Selecting user with ID:', stringUserId);
      
      // Find the user object for logging
      const userObj = users.find(u => String(u.id) === stringUserId);
      if (userObj) {
        console.log('Selected user details:', { id: userObj.id, name: userObj.name, department: userObj.department });
      } else {
        console.warn('Selected user not found in users array');
      }
      
      // Debug: Current state before update
      console.log('Current assignedUserIds before update:', formData.assignedUserIds);
      console.log('Current assignedUserIds type:', Array.isArray(formData.assignedUserIds) ? 'Array' : typeof formData.assignedUserIds);
      
      setFormData(prevData => {
        // Ensure we have an array, even if it was undefined or null
        const currentUserIds = Array.isArray(prevData.assignedUserIds) ? [...prevData.assignedUserIds] : [];
        console.log('Current user IDs (ensure array):', currentUserIds);
        
        // Convert all IDs to strings for consistent comparison
        const stringifiedIds = currentUserIds.map(id => String(id));
        console.log('Stringified user IDs for comparison:', stringifiedIds);
        
        // Check if the user is already selected using string comparison
        const existingIndex = stringifiedIds.indexOf(stringUserId);
        console.log('Existing index:', existingIndex);
        
        let newAssignedUserIds: string[];
        if (existingIndex >= 0) {
          // User is already selected, remove them
          newAssignedUserIds = [...currentUserIds];
          newAssignedUserIds.splice(existingIndex, 1);
          console.log('User removed, new selection:', newAssignedUserIds);
        } else {
          // User is not selected, add them
          newAssignedUserIds = [...currentUserIds, stringUserId];
          console.log('User added, new selection:', newAssignedUserIds);
        }
        
        // Log the complete list of assigned users with their details
        if (newAssignedUserIds.length > 0) {
          const assignedUsers = newAssignedUserIds.map(id => {
            const user = users.find(u => String(u.id) === String(id));
            return user ? { id, name: user.name } : { id, name: 'Unknown User' };
          });
          console.log('Current assigned users:', assignedUsers);
        }
        
        // Debug: Verify we're returning the right data structure
        console.log('New formData.assignedUserIds will be:', newAssignedUserIds);
        
        return {
          ...prevData,
          assignedUserIds: newAssignedUserIds
        };
      });
    };

    const handleAssetSelect = (assetId: number | string) => {
      // Toggle asset selection
      const stringAssetId = String(assetId);
      
      setFormData(prev => {
        // Ensure we're working with an array of strings
        const currentSelection = prev.linkedAssetIds || [];
        const stringifiedSelection = currentSelection.map(id => String(id));
        
        const index = stringifiedSelection.indexOf(stringAssetId);
        
        let newSelection: string[];
        if (index === -1) {
          // Add asset
          newSelection = [...stringifiedSelection, stringAssetId];
        } else {
          // Remove asset
          newSelection = [...stringifiedSelection];
          newSelection.splice(index, 1);
        }
        
        return { ...prev, linkedAssetIds: newSelection };
      });
    };

    // Handle form validation
    const validateForm = (): boolean => {
      const errors: Record<string, string> = {};
      
      if (!formData.softwareName?.trim()) {
        errors.softwareName = 'Software name is required';
      }
      
      if (!formData.vendorId) {
        errors.vendorId = 'Vendor is required';
      }
      
      if (!formData.type) {
        errors.type = 'License type is required';
      }
      
      if (!formData.status) {
        errors.status = 'Status is required';
      }
      
      if (formData.totalSeats && formData.totalSeats < 0) {
        errors.totalSeats = 'Total seats cannot be negative';
      }
      
      if (formData.usedSeats && formData.usedSeats < 0) {
        errors.usedSeats = 'Used seats cannot be negative';
      }
      
      if (formData.usedSeats && formData.totalSeats && 
          Number(formData.usedSeats) > Number(formData.totalSeats)) {
        errors.usedSeats = 'Used seats cannot exceed total seats';
      }
      
      setFormErrors(errors);
      return Object.keys(errors).length === 0;
    };

    // Handle form submission
    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      
      if (!validateForm()) {
        toast.error('Please fix the errors in the form before submitting');
        return;
      }
      
      // Add debug logging for assigned users
      console.log('FORM SUBMISSION - Current state of assignedUserIds:');
      console.log('Raw value:', formData.assignedUserIds);
      console.log('Type:', Array.isArray(formData.assignedUserIds) ? 'Array' : typeof formData.assignedUserIds);
      console.log('Length:', formData.assignedUserIds?.length || 0);
      
      if (Array.isArray(formData.assignedUserIds) && formData.assignedUserIds.length > 0) {
        // Log all assigned user IDs with their details
        formData.assignedUserIds.forEach(id => {
          const stringId = String(id);
          const userObj = users.find(user => String(user.id) === stringId);
          console.log(`User ID ${stringId}: ${userObj ? `${userObj.name} (${userObj.email})` : 'NOT FOUND'}`);
        });
      }
      
      setIsSubmitting(true);
      
      try {
        // Log assigned users before submission
        console.log('Current assignedUserIds before submission:', formData.assignedUserIds);
        if (Array.isArray(formData.assignedUserIds) && formData.assignedUserIds.length > 0) {
          // Log the actual user objects for assigned users
          const assignedUsers = formData.assignedUserIds.map(id => {
            const user = users.find(u => String(u.id) === String(id));
            return user ? { id: id, name: user.name } : { id: id, name: 'Unknown User' };
          });
          console.log('Assigned users being sent:', assignedUsers);
        } else {
          console.log('No users assigned');
        }
        
        // Prepare data for API, ensuring UUIDs remain as strings
        const apiData = {
          ...formData,
          multiLocationUse: formData.multiLocation,
          // For vendor ID, we can still use parseInt if it's a numeric ID
          vendor: { id: formData.vendorId ? parseInt(formData.vendorId) : 0 },
          // CRITICAL: Make sure we send user IDs as strings without trying to parse them as integers
          // FIXED: Ensure this is always a proper array, even if empty
          assignedUserIds: Array.isArray(formData.assignedUserIds) 
            ? formData.assignedUserIds.map(id => String(id)) 
            : [],
          // For assets, we still need to parse IDs since they're numeric
          linkedAssets: Array.isArray(formData.linkedAssetIds) && formData.linkedAssetIds.length > 0
            ? formData.linkedAssetIds.map(id => ({ id: parseInt(String(id)) })) 
            : [],
        };
        
        // Add one more debug log specifically for the assignedUserIds array
        console.log('FINAL assignedUserIds being sent:', 
          JSON.stringify(apiData.assignedUserIds),
          'Length:', apiData.assignedUserIds.length
        );
        
        // Log the actual API payload for debugging
        console.log('API data being sent:', apiData);
        console.log('User IDs being sent (should be UUIDs):', apiData.assignedUserIds);
        console.log('Types of user IDs being sent:', apiData.assignedUserIds.map(id => typeof id));
        
        let response;
        
        // If we have an invoice file, use FormData to send multipart/form-data
        if (invoiceFile) {
          const formDataObj = new FormData();
          
          // Append the JSON data
          formDataObj.append('data', JSON.stringify(apiData));
          
          // Append the file
          formDataObj.append('invoiceFile', invoiceFile);
          
          // Make API call with multipart/form-data
          response = isEditMode
            ? await axios.put(`/api/software-licenses/${id}`, formDataObj, {
                headers: { 'Content-Type': 'multipart/form-data' }
              })
            : await axios.post('/api/software-licenses', formDataObj, {
                headers: { 'Content-Type': 'multipart/form-data' }
              });
        } else {
          // Make regular API call without file
          response = isEditMode
            ? await axios.put(`/api/software-licenses/${id}`, apiData)
            : await axios.post('/api/software-licenses', apiData);
        }
        
        console.log('API response:', response.data);
        
        // Check if assigned users are in the response
        if (response.data.assignedUsers) {
          console.log('Assigned users in response:', response.data.assignedUsers);
          
          // Verify that all assigned users were saved correctly
          const returnedUserIds = response.data.assignedUsers.map((u: any) => String(u.id));
          const sentUserIds = formData.assignedUserIds?.map(id => String(id)) || [];
          
          const missingUserIds = sentUserIds.filter(id => !returnedUserIds.includes(id));
          if (missingUserIds.length > 0) {
            console.warn('Some user assignments were not saved:', missingUserIds);
            console.warn('This might indicate a UUID compatibility issue in the backend');
          } else {
            console.log('All user assignments were saved successfully');
          }
        } else {
          console.warn('No assigned users found in response. If you expected users to be assigned, check:');
          console.warn('1. Network tab to verify the request payload includes assignedUsers');
          console.warn('2. Server logs to see if the backend is processing user assignments');
          console.warn('3. Database schema to ensure relations are set up correctly');
        }
        
        toast.success(`Software license ${isEditMode ? 'updated' : 'created'} successfully!`);
        
        // Navigate back to licenses page after successful submission
        navigate('/software');
      } catch (error) {
        console.error('Error submitting form:', error);
        toast.error(`Failed to ${isEditMode ? 'update' : 'create'} software license`);
      } finally {
        setIsSubmitting(false);
      }
    };

    const handleGoBack = () => {
      navigate('/software');
    };

    // Loading state
    if (pageLoading) {
      return (
        <div className="flex items-center justify-center h-screen">
          <div className="h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
        </div>
      );
    }

    // Error state for edit mode
    if (isEditMode && (pageError || !softwareData)) {
      return (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center gap-3 mb-4">
              <button 
                onClick={handleGoBack}
                className="p-2 rounded-full hover:bg-gray-100"
              >
                <ArrowLeft className="h-5 w-5 text-gray-500" />
              </button>
              <div>
                <h2 className="text-xl font-bold text-gray-900">Error</h2>
                <p className="text-red-600">{pageError || 'Failed to load software license data'}</p>
              </div>
            </div>
            <button
              onClick={handleGoBack}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              Return to Software Licenses
            </button>
          </div>
        </div>
      );
    }

    // Render the form
    return (
      <div className="p-6">
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <button 
                  onClick={handleGoBack}
                  className="p-2 rounded-full hover:bg-gray-100"
                >
                  <ArrowLeft className="h-5 w-5 text-gray-500" />
                </button>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">
                    {isEditMode ? 'Edit Software License' : 'Add New Software License'}
                  </h2>
                  <p className="text-gray-600">
                    {isEditMode ? 'Update software license information' : 'Add new software license to the system'}
                  </p>
                </div>
              </div>
            </div>
          </div>
          
      <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {Object.entries(formErrors).length > 0 && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5" />
                  <span>Please fix the errors below:</span>
                </div>
                <ul className="mt-2 list-disc pl-5">
                  {Object.entries(formErrors).map(([fieldName, error]) => (
                    <li key={fieldName}>{error}</li>
                  ))}
                </ul>
              </div>
            )}

        {/* Basic Information */}
        <div>
          <div className="flex items-center gap-2 mb-4">
            <FileText className="h-5 w-5 text-gray-700" />
            <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1">
                <Box className="h-4 w-4" />
                <span>Software Name *</span>
              </label>
              <input
                type="text"
                value={formData.softwareName}
                onChange={handleInputChange}
                name="softwareName"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="e.g., Office 365, Kaspersky"
                required
              />
            </div>

                <div>
                  <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1">
                    <Tag className="h-4 w-4" />
                    <span>Category</span>
                  </label>
                  <div className="relative" ref={categoryRef}>
                    <div 
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg flex items-center justify-between cursor-pointer"
                      onClick={() => setIsCategoryOpen(!isCategoryOpen)}
                    >
                      {formData.category ? (
                        <div className="flex items-center gap-2">
                          {formData.category === "Productivity" && <FileText className="h-4 w-4 text-blue-500" />}
                          {formData.category === "Antivirus & Security" && <Shield className="h-4 w-4 text-red-500" />}
                          {formData.category === "Firewall & Network Security" && <Network className="h-4 w-4 text-orange-500" />}
                          {formData.category === "Domain & Hosting" && <Globe className="h-4 w-4 text-green-500" />}
                          {formData.category === "AI Tools" && <Brain className="h-4 w-4 text-purple-500" />}
                          {formData.category === "Design & Content Creation" && <Palette className="h-4 w-4 text-pink-500" />}
                          {formData.category === "VPN & Privacy Tools" && <LockIcon className="h-4 w-4 text-gray-600" />}
                          {formData.category === "Marketing & Communication" && <MessageCircle className="h-4 w-4 text-blue-400" />}
                          {formData.category === "Real Estate / CRM Tools" && <Building2 className="h-4 w-4 text-brown-500" />}
                          {formData.category === "Utility & System Tools" && <Wrench className="h-4 w-4 text-gray-500" />}
                          {formData.category === "ERP / Business Suite" && <Briefcase className="h-4 w-4 text-indigo-500" />}
                          {formData.category === "Development & IT Tools" && <Code className="h-4 w-4 text-green-600" />}
                          {formData.category === "Analytics & Reporting" && <BarChart className="h-4 w-4 text-blue-600" />}
                          {formData.category === "Cloud Storage & Backup" && <Cloud className="h-4 w-4 text-sky-500" />}
                          {formData.category === "Others" && <MoreHorizontal className="h-4 w-4 text-gray-400" />}
                          <span>{formData.category}</span>
                        </div>
                      ) : (
                        <span className="text-gray-500">Select Category</span>
                      )}
                      <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isCategoryOpen ? 'transform rotate-180' : ''}`} />
                    </div>
                    
                    {isCategoryOpen && (
                      <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-auto">
                        <div 
                          className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                          onClick={() => {
                            setFormData(prev => ({...prev, category: "Productivity"}));
                            setIsCategoryOpen(false);
                          }}
                        >
                          <FileText className="h-4 w-4 text-blue-500" />
                          <span>Productivity</span>
                        </div>
                        <div 
                          className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                          onClick={() => {
                            setFormData(prev => ({...prev, category: "Antivirus & Security"}));
                            setIsCategoryOpen(false);
                          }}
                        >
                          <Shield className="h-4 w-4 text-red-500" />
                          <span>Antivirus & Security</span>
                        </div>
                        <div 
                          className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                          onClick={() => {
                            setFormData(prev => ({...prev, category: "Firewall & Network Security"}));
                            setIsCategoryOpen(false);
                          }}
                        >
                          <Network className="h-4 w-4 text-orange-500" />
                          <span>Firewall & Network Security</span>
                        </div>
                        <div 
                          className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                          onClick={() => {
                            setFormData(prev => ({...prev, category: "Domain & Hosting"}));
                            setIsCategoryOpen(false);
                          }}
                        >
                          <Globe className="h-4 w-4 text-green-500" />
                          <span>Domain & Hosting</span>
                        </div>
                        <div 
                          className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                          onClick={() => {
                            setFormData(prev => ({...prev, category: "AI Tools"}));
                            setIsCategoryOpen(false);
                          }}
                        >
                          <Brain className="h-4 w-4 text-purple-500" />
                          <span>AI Tools</span>
                        </div>
                        <div 
                          className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                          onClick={() => {
                            setFormData(prev => ({...prev, category: "Design & Content Creation"}));
                            setIsCategoryOpen(false);
                          }}
                        >
                          <Palette className="h-4 w-4 text-pink-500" />
                          <span>Design & Content Creation</span>
                        </div>
                        <div 
                          className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                          onClick={() => {
                            setFormData(prev => ({...prev, category: "VPN & Privacy Tools"}));
                            setIsCategoryOpen(false);
                          }}
                        >
                          <LockIcon className="h-4 w-4 text-gray-600" />
                          <span>VPN & Privacy Tools</span>
                        </div>
                        <div 
                          className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                          onClick={() => {
                            setFormData(prev => ({...prev, category: "Marketing & Communication"}));
                            setIsCategoryOpen(false);
                          }}
                        >
                          <MessageCircle className="h-4 w-4 text-blue-400" />
                          <span>Marketing & Communication</span>
                        </div>
                        <div 
                          className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                          onClick={() => {
                            setFormData(prev => ({...prev, category: "Real Estate / CRM Tools"}));
                            setIsCategoryOpen(false);
                          }}
                        >
                          <Building2 className="h-4 w-4 text-gray-700" />
                          <span>Real Estate / CRM Tools</span>
                        </div>
                        <div 
                          className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                          onClick={() => {
                            setFormData(prev => ({...prev, category: "Utility & System Tools"}));
                            setIsCategoryOpen(false);
                          }}
                        >
                          <Wrench className="h-4 w-4 text-gray-500" />
                          <span>Utility & System Tools</span>
                        </div>
                        <div 
                          className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                          onClick={() => {
                            setFormData(prev => ({...prev, category: "ERP / Business Suite"}));
                            setIsCategoryOpen(false);
                          }}
                        >
                          <Briefcase className="h-4 w-4 text-indigo-500" />
                          <span>ERP / Business Suite</span>
                        </div>
                        <div 
                          className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                          onClick={() => {
                            setFormData(prev => ({...prev, category: "Development & IT Tools"}));
                            setIsCategoryOpen(false);
                          }}
                        >
                          <Code className="h-4 w-4 text-green-600" />
                          <span>Development & IT Tools</span>
                        </div>
                        <div 
                          className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                          onClick={() => {
                            setFormData(prev => ({...prev, category: "Analytics & Reporting"}));
                            setIsCategoryOpen(false);
                          }}
                        >
                          <BarChart className="h-4 w-4 text-blue-600" />
                          <span>Analytics & Reporting</span>
                        </div>
                        <div 
                          className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                          onClick={() => {
                            setFormData(prev => ({...prev, category: "Cloud Storage & Backup"}));
                            setIsCategoryOpen(false);
                          }}
                        >
                          <Cloud className="h-4 w-4 text-sky-500" />
                          <span>Cloud Storage & Backup</span>
                        </div>
                        <div 
                          className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                          onClick={() => {
                            setFormData(prev => ({...prev, category: "Others"}));
                            setIsCategoryOpen(false);
                          }}
                        >
                          <MoreHorizontal className="h-4 w-4 text-gray-400" />
                          <span>Others</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1">
                <Store className="h-4 w-4" />
                <span>Vendor/Provider *</span>
              </label>
              <select
                value={formData.vendorId}
                onChange={handleInputChange}
                name="vendorId"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="">Select Vendor</option>
                    {Array.isArray(vendors) && vendors.map(vendor => (
                  <option key={vendor.id} value={vendor.id}>
                        {vendor.companyName || vendor.vendorName}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1">
                <Tag className="h-4 w-4" />
                <span>Type *</span>
              </label>
              <select
                value={formData.type}
                onChange={handleInputChange}
                name="type"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="One-Time (Lifetime)">One-Time (Lifetime)</option>
                <option value="Monthly Subscription">Monthly Subscription</option>
                <option value="Yearly Subscription">Yearly Subscription</option>
                <option value="Trial">Trial</option>
                <option value="Freemium">Freemium</option>
                <option value="Open Source">Open Source</option>
                <option value="Free">Free</option>
              </select>
            </div>

            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1">
                <Briefcase className="h-4 w-4" />
                <span>Assigned Department *</span>
              </label>
              <div className="relative" ref={departmentRef}>
                <div 
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg flex items-center justify-between cursor-pointer"
                  onClick={() => setIsDepartmentOpen(!isDepartmentOpen)}
                >
                  {formData.department ? (
                    <div className="flex items-center gap-2">
                      {formData.department === "Information Technology" && <Code className="h-4 w-4 text-blue-500" />}
                      {formData.department === "Human Resources" && <Users className="h-4 w-4 text-purple-500" />}
                      {formData.department === "Finance" && <DollarSign className="h-4 w-4 text-green-500" />}
                      {formData.department === "Marketing" && <BarChart className="h-4 w-4 text-orange-500" />}
                      {formData.department === "Sales" && <CreditCard className="h-4 w-4 text-blue-400" />}
                      {formData.department === "Operations" && <Network className="h-4 w-4 text-indigo-500" />}
                      {formData.department === "Customer Service" && <MessageCircle className="h-4 w-4 text-pink-500" />}
                      {formData.department === "Land" && <MapPin className="h-4 w-4 text-red-500" />}
                      {formData.department === "Legal" && <Shield className="h-4 w-4 text-gray-700" />}
                      {formData.department === "Management" && <Briefcase className="h-4 w-4 text-gray-800" />}
                      {formData.department === "Planning & Development" && <FileText className="h-4 w-4 text-teal-500" />}
                      <span>{formData.department}</span>
                    </div>
                  ) : (
                    <span className="text-gray-500">Select Department</span>
                  )}
                  <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isDepartmentOpen ? 'transform rotate-180' : ''}`} />
                </div>
                
                {isDepartmentOpen && (
                  <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-auto">
                    <div 
                      className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                      onClick={() => {
                        setFormData(prev => ({...prev, department: "Information Technology"}));
                        setIsDepartmentOpen(false);
                      }}
                    >
                      <Code className="h-4 w-4 text-blue-500" />
                      <span>Information Technology</span>
                    </div>
                    <div 
                      className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                      onClick={() => {
                        setFormData(prev => ({...prev, department: "Human Resources"}));
                        setIsDepartmentOpen(false);
                      }}
                    >
                      <Users className="h-4 w-4 text-purple-500" />
                      <span>Human Resources</span>
                    </div>
                    <div 
                      className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                      onClick={() => {
                        setFormData(prev => ({...prev, department: "Finance"}));
                        setIsDepartmentOpen(false);
                      }}
                    >
                      <DollarSign className="h-4 w-4 text-green-500" />
                      <span>Finance</span>
                    </div>
                    <div 
                      className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                      onClick={() => {
                        setFormData(prev => ({...prev, department: "Marketing"}));
                        setIsDepartmentOpen(false);
                      }}
                    >
                      <BarChart className="h-4 w-4 text-orange-500" />
                      <span>Marketing</span>
                    </div>
                    <div 
                      className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                      onClick={() => {
                        setFormData(prev => ({...prev, department: "Sales"}));
                        setIsDepartmentOpen(false);
                      }}
                    >
                      <CreditCard className="h-4 w-4 text-blue-400" />
                      <span>Sales</span>
                    </div>
                    <div 
                      className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                      onClick={() => {
                        setFormData(prev => ({...prev, department: "Operations"}));
                        setIsDepartmentOpen(false);
                      }}
                    >
                      <Network className="h-4 w-4 text-indigo-500" />
                      <span>Operations</span>
                    </div>
                    <div 
                      className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                      onClick={() => {
                        setFormData(prev => ({...prev, department: "Customer Service"}));
                        setIsDepartmentOpen(false);
                      }}
                    >
                      <MessageCircle className="h-4 w-4 text-pink-500" />
                      <span>Customer Service</span>
                    </div>
                    <div 
                      className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                      onClick={() => {
                        setFormData(prev => ({...prev, department: "Land"}));
                        setIsDepartmentOpen(false);
                      }}
                    >
                      <MapPin className="h-4 w-4 text-red-500" />
                      <span>Land</span>
                    </div>
                    <div 
                      className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                      onClick={() => {
                        setFormData(prev => ({...prev, department: "Legal"}));
                        setIsDepartmentOpen(false);
                      }}
                    >
                      <Shield className="h-4 w-4 text-gray-700" />
                      <span>Legal</span>
                    </div>
                    <div 
                      className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                      onClick={() => {
                        setFormData(prev => ({...prev, department: "Management"}));
                        setIsDepartmentOpen(false);
                      }}
                    >
                      <Briefcase className="h-4 w-4 text-gray-800" />
                      <span>Management</span>
                    </div>
                    <div 
                      className="px-3 py-2 hover:bg-gray-100 flex items-center gap-2 cursor-pointer"
                      onClick={() => {
                        setFormData(prev => ({...prev, department: "Planning & Development"}));
                        setIsDepartmentOpen(false);
                      }}
                    >
                      <FileText className="h-4 w-4 text-teal-500" />
                      <span>Planning & Development</span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1">
                <CheckCircle className="h-4 w-4" />
                <span>Status *</span>
              </label>
              <select
                value={formData.status}
                onChange={handleInputChange}
                name="status"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="Active">Active</option>
                <option value="Expired">Expired</option>
                <option value="Trial">Trial</option>
                <option value="Blocked">Blocked</option>
              </select>
            </div>
          </div>
        </div>

        {/* Licensing & Cost */}
        <div>
          <div className="flex items-center gap-2 mb-4">
            <CreditCard className="h-5 w-5 text-gray-700" />
            <h3 className="text-lg font-semibold text-gray-900">Licensing & Cost</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1">
                <Lock className="h-4 w-4" />
                <span>License Key / Credentials</span>
              </label>
              <input
                type="text"
                value={formData.licenseKey}
                onChange={handleInputChange}
                name="licenseKey"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter license key or credentials"
              />
            </div>

            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1">
                <Upload className="h-4 w-4" />
                <span>Invoice / Receipt</span>
              </label>
              <div className="relative">
                <input
                  type="file"
                  onChange={handleFileChange}
                  className="hidden"
                  id="invoiceUpload"
                  accept=".pdf,.jpg,.jpeg,.png"
                />
                <label
                  htmlFor="invoiceUpload"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg flex items-center justify-between cursor-pointer hover:bg-gray-50"
                >
                  <span className="text-gray-500">
                    {invoiceFile ? invoiceFile.name : 'Upload invoice or receipt'}
                  </span>
                  <FileUp className="h-4 w-4 text-gray-400" />
                </label>
              </div>
              {invoiceFile && (
                <div className="mt-2 flex items-center justify-between text-sm">
                  <span className="text-gray-500">{invoiceFile.name.length > 20 ? `${invoiceFile.name.substring(0, 20)}...` : invoiceFile.name}</span>
                  <button
                    type="button"
                    onClick={() => setInvoiceFile(null)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              )}
            </div>

            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1">
                <Users className="h-4 w-4" />
                <span>Number of Seats / Users</span>
              </label>
              <input
                type="number"
                    value={formData.totalSeats === 0 ? '' : formData.totalSeats}
                onChange={handleInputChange}
                name="totalSeats"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                min="0"
                placeholder="e.g., 10"
              />
            </div>

            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1">
                <Calendar className="h-4 w-4" />
                <span>Purchased On</span>
              </label>
              <input
                type="date"
                value={formData.purchaseDate}
                onChange={handleInputChange}
                name="purchaseDate"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1">
                <Clock className="h-4 w-4" />
                <span>Expiry / Renewal Date</span>
              </label>
              <input
                type="date"
                value={formData.expiryDate}
                onChange={handleInputChange}
                name="expiryDate"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1">
                <DollarSign className="h-4 w-4" />
                    <span>Cost (USD)</span>
              </label>
              <input
                type="number"
                    value={formData.costUSD === 0 ? '' : formData.costUSD}
                onChange={handleInputChange}
                    name="costUSD"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                min="0"
                step="0.01"
                    placeholder="e.g., 99"
              />
            </div>

            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1">
                    <DollarSign className="h-4 w-4" />
                    <span>Cost (PKR)</span>
              </label>
              <input
                type="number"
                    value={formData.costPKR === 0 ? '' : formData.costPKR}
                onChange={handleInputChange}
                    name="costPKR"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                min="0"
                step="0.01"
                    placeholder="e.g., 25000"
              />
            </div>

            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1">
                    <Calendar className="h-4 w-4" />
                    <span>Payment Frequency</span>
              </label>
              <select
                    value={formData.paymentFrequency}
                onChange={handleInputChange}
                    name="paymentFrequency"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                    <option value="Monthly">Monthly</option>
                    <option value="Quarterly">Quarterly</option>
                    <option value="Yearly">Yearly</option>
                    <option value="One-time">One-time</option>
              </select>
            </div>

            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-1">
                <CheckCircle className="h-4 w-4" />
                <span>Auto-renew Enabled?</span>
              </label>
              <div className="flex items-center space-x-2">
                <button
                  type="button"
                    onClick={() => setFormData(prev => ({ ...prev, autoRenew: !prev.autoRenew }))}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                        formData.autoRenew ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          formData.autoRenew ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
                <span className="ml-2 text-sm text-gray-600">
                      {formData.autoRenew ? 'Yes' : 'No'}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        {/* User Assignment and Asset Linking (side by side) */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* User Assignment */}
          <div className="bg-white rounded-lg shadow-sm p-5">
            <div className="flex items-center gap-2 mb-4">
              <Users className="h-5 w-5 text-gray-700" />
              <h3 className="text-lg font-semibold text-gray-900">Assignment</h3>
              {Array.isArray(formData.assignedUserIds) && formData.assignedUserIds.length > 0 && (
                <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                  {formData.assignedUserIds.length} user{formData.assignedUserIds.length !== 1 ? 's' : ''} assigned
                </span>
              )}
            </div>
            
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-700 mb-2">Assigned To (Multiple)</p>
                <div className="relative" ref={dropdownRef}>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                      <Search className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      value={userSearchQuery}
                      onChange={(e) => setUserSearchQuery(e.target.value)}
                      onClick={() => setIsDropdownOpen(true)}
                      placeholder="Search users..."
                      className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <button
                      type="button"
                      onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                      className="absolute right-2 inset-y-0 flex items-center"
                    >
                      {isDropdownOpen ? <ChevronUp className="h-5 w-5 text-gray-400" /> : <ChevronDown className="h-5 w-5 text-gray-400" />}
                    </button>
                  </div>
                  
                  {isDropdownOpen && (
                    <div className="absolute left-0 right-0 mt-1 max-h-60 overflow-auto bg-white border border-gray-300 rounded-lg shadow-lg z-10">
                      {filteredUsers.length > 0 ? (
                        filteredUsers.map(user => {
                          // Make sure we're comparing strings as UUIDs
                          const userId = String(user.id);
                          
                          // Debug the current data
                          console.log(`Checking if user ${userId} is selected, formData.assignedUserIds:`, 
                            formData.assignedUserIds, 
                            `is array: ${Array.isArray(formData.assignedUserIds)}`);
                          
                          // More robust selection check - force string comparison
                          let isSelected = false;
                          if (Array.isArray(formData.assignedUserIds)) {
                            const idsAsStrings = formData.assignedUserIds.map(id => String(id));
                            isSelected = idsAsStrings.includes(userId);
                            console.log(
                              `Checking ${userId} (${user.name}) against`, 
                              idsAsStrings,
                              `Result: ${isSelected ? 'SELECTED' : 'not selected'}`
                            );
                          }
                          
                          return (
                            <div
                              key={userId}
                              className="py-2 px-3 hover:bg-gray-100 cursor-pointer"
                              onClick={() => {
                                // IMPORTANT: Call handleUserSelect directly with just this user's ID
                                // This ensures the toggle logic in handleUserSelect runs properly
                                handleUserSelect(userId);
                                
                                // No need to close dropdown after each selection in a multi-select
                                // Comment out or remove the line that closes the dropdown
                                // setIsDropdownOpen(false);
                              }}
                            >
                              <div className="flex items-center justify-between w-full">
                                <div className="flex items-center gap-3">
                                  <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
                                    {user.name?.charAt(0) || 'U'}
                                  </div>
                                  <div>
                                    <p className="font-medium text-gray-900">{user.name}</p>
                                    <p className="text-sm text-gray-500">{user.email}</p>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  <div className={`px-2 py-1 rounded text-xs font-medium ${user.department?.toLowerCase() === 'hr' ? 'bg-yellow-500 text-white' : 'bg-blue-500 text-white'}`}>
                                    {user.department?.substring(0, 2) || 'IT'}
                                  </div>
                                  <button
                                    type="button"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      // Call handleUserSelect directly without any further modifications
                                      handleUserSelect(userId);
                                    }}
                                    className={`w-5 h-5 flex items-center justify-center rounded-full ${
                                      isSelected
                                        ? 'bg-green-500 text-white hover:bg-red-500' 
                                        : 'bg-gray-200 text-gray-500 hover:bg-blue-500 hover:text-white'
                                    }`}
                                  >
                                    {isSelected ? (
                                      <Check className="h-3 w-3" />
                                    ) : (
                                      <Plus className="h-3 w-3" />
                                    )}
                                  </button>
                                </div>
                              </div>
                            </div>
                          );
                        })
                      ) : (
                        <div className="p-3 text-gray-500 text-center">No users found</div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {formData.assignedUserIds && formData.assignedUserIds.length > 0 ? (
                <div className="mt-4">
                  {formData.assignedUserIds.map(userId => {
                    const stringUserId = String(userId);
                    const selectedUser = users.find(u => String(u.id) === stringUserId);
                    
                    if (!selectedUser) {
                      // Create a placeholder for users that exist in the database but aren't loaded in the users array
                      return (
                        <div key={stringUserId} className="py-2 border-b border-gray-100 last:border-b-0">
                          <div className="flex items-center justify-between w-full">
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 rounded-full bg-gray-400 flex items-center justify-center text-white font-medium">
                                ?
                              </div>
                              <div>
                                <p className="font-medium text-gray-900">Unknown User</p>
                                <p className="text-sm text-gray-500">User data not available</p>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="px-2 py-1 rounded text-xs font-medium bg-gray-400 text-white">
                                N/A
                              </div>
                              <button
                                type="button"
                                onClick={() => handleUserSelect(stringUserId)}
                                className="text-red-500 hover:text-red-700"
                              >
                                <X className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    }
                    
                    return (
                      <div key={stringUserId} className="py-2 border-b border-gray-100 last:border-b-0">
                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
                              {selectedUser.name?.charAt(0) || 'U'}
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">{selectedUser.name}</p>
                              <p className="text-sm text-gray-500">{selectedUser.email}</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className={`px-2 py-1 rounded text-xs font-medium ${selectedUser.department?.toLowerCase() === 'hr' ? 'bg-yellow-500 text-white' : 'bg-blue-500 text-white'}`}>
                              {selectedUser.department?.substring(0, 2) || 'IT'}
                            </div>
                            <button
                              type="button"
                              onClick={() => handleUserSelect(stringUserId)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <p className="text-sm text-gray-500 italic">No users assigned</p>
              )}
            </div>
          </div>
          
          {/* Asset Linking */}
          <div className="bg-white rounded-lg shadow-sm p-5">
            <div className="flex items-center gap-2 mb-4">
              <Wrench className="h-5 w-5 text-gray-700" />
              <h3 className="text-lg font-semibold text-gray-900">Link to Assets</h3>
            </div>
            
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-700 mb-2">Select Assets</p>
                <div className="relative" ref={assetsDropdownRef}>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                      <Search className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      type="text"
                      value={assetSearchQuery}
                      onChange={(e) => setAssetSearchQuery(e.target.value)}
                      onClick={() => setIsAssetsDropdownOpen(true)}
                      placeholder="Search assets..."
                      className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <button
                      type="button"
                      onClick={() => setIsAssetsDropdownOpen(!isAssetsDropdownOpen)}
                      className="absolute right-2 inset-y-0 flex items-center"
                    >
                      {isAssetsDropdownOpen ? <ChevronUp className="h-5 w-5 text-gray-400" /> : <ChevronDown className="h-5 w-5 text-gray-400" />}
                    </button>
                  </div>
                  
                  {isAssetsDropdownOpen && (
                    <div className="absolute left-0 right-0 mt-1 max-h-60 overflow-auto bg-white border border-gray-300 rounded-lg shadow-lg z-10">
                      {filteredAssets.length > 0 ? (
                        filteredAssets.map(asset => (
                          <div
                            key={asset.id}
                            className="py-3 px-3 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
                            onClick={() => {
                              handleAssetSelect(asset.id);
                              setIsAssetsDropdownOpen(false);
                            }}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex flex-col">
                                <span className="font-medium text-gray-900">{asset.assetTag}</span>
                                <span className="text-sm text-gray-500">{asset.manufacturer} {asset.model}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <span className={`px-2 py-1 rounded text-xs font-medium flex items-center gap-1 ${getAssetTypeProperties(asset.assetType).color} ${getAssetTypeProperties(asset.assetType).textColor}`}>
                                  {getAssetTypeProperties(asset.assetType).icon}
                                  {asset.assetType || 'Hardware'}
                                </span>
                                <button
                                  type="button"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleAssetSelect(asset.id);
                                  }}
                                  className={`w-5 h-5 flex items-center justify-center rounded-full ${
                                    formData.linkedAssetIds?.some(id => String(id) === asset.id.toString())
                                      ? 'bg-green-500 text-white hover:bg-red-500' 
                                      : 'bg-gray-200 text-gray-500 hover:bg-blue-500 hover:text-white'
                                  }`}
                                >
                                  {formData.linkedAssetIds?.some(id => String(id) === asset.id.toString()) ? (
                                    <Check className="h-3 w-3" />
                                  ) : (
                                    <Plus className="h-3 w-3" />
                                  )}
                                </button>
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="p-3 text-gray-500 text-center">No assets found</div>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {formData.linkedAssetIds && formData.linkedAssetIds.length > 0 ? (
                <div className="mt-4">
                  {formData.linkedAssetIds.map(assetId => {
                    const selectedAsset = Array.isArray(assets) && assets.find(a => a.id.toString() === assetId);
                    return selectedAsset ? (
                      <div key={assetId} className="py-2 border-b border-gray-100 last:border-b-0">
                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center gap-3">
                            <div className={`w-10 h-10 rounded-full ${getAssetTypeProperties(selectedAsset.assetType).color} flex items-center justify-center text-white font-medium`}>
                              {getAssetTypeProperties(selectedAsset.assetType).icon}
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">{selectedAsset.assetTag}</p>
                              <p className="text-sm text-gray-500">{selectedAsset.manufacturer} {selectedAsset.model}</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className={`px-2 py-1 rounded text-xs font-medium flex items-center gap-1 ${getAssetTypeProperties(selectedAsset.assetType).color} ${getAssetTypeProperties(selectedAsset.assetType).textColor}`}>
                              {getAssetTypeProperties(selectedAsset.assetType).icon}
                              {selectedAsset.assetType || 'Hardware'}
                            </div>
                            <button
                              type="button"
                              onClick={() => handleAssetSelect(assetId)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ) : null;
                  })}
                </div>
              ) : (
                <p className="text-sm text-gray-500 italic">No assets linked</p>
              )}
            </div>
          </div>
        </div>

    {/* Advanced Options */}
    <div>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <MoreHorizontal className="h-5 w-5 text-gray-700" />
              <h3 className="text-lg font-semibold text-gray-900">Advanced Options</h3>
            </div>
      <button
        type="button"
        onClick={() => setShowAdvanced(!showAdvanced)}
              className="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
      >
              {showAdvanced ? 'Hide' : 'Show'} Options
        {showAdvanced ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
      </button>
          </div>

      {showAdvanced && (
            <div className="space-y-6 border border-gray-200 rounded-lg p-4">
              {/* Login Information */}
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-4 flex items-center gap-2">
                  <KeyRound className="h-4 w-4 text-blue-600" />
                  <span>Login Information</span>
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-1 block">
                      Login URL
                    </label>
                    <input
                      type="url"
                      value={formData.loginUrl || ''}
                      onChange={handleInputChange}
                      name="loginUrl"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="https://example.com/login"
                    />
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-1 block">
                      Login Email/Username
                    </label>
                    <input
                      type="text"
                      value={formData.loginEmail || ''}
                      onChange={handleInputChange}
                      name="loginEmail"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-700 mb-1 block">
                      Password
                    </label>
                    <div className="relative">
                      <input
                        type={showPassword ? "text" : "password"}
                        value={formData.password || ''}
                        onChange={handleInputChange}
                        name="password"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-10"
                        placeholder="••••••••"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                      >
                        {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Software Nature & Payment Info */}
              <div className="grid grid-cols-1 gap-6 pt-4 border-t border-gray-200">
                <div>
                  <h4 className="text-sm font-medium text-gray-900 mb-4 flex items-center gap-2">
                    <Monitor className="h-4 w-4 text-indigo-600" />
                    <span>Software Details</span>
                  </h4>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-700 mb-1 block">
                        Software Nature
                      </label>
                      <div className="flex flex-wrap gap-3">
                        <label className="flex items-center p-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                          <input
                            type="radio"
                            name="softwareNature"
                            value="desktop"
                            checked={formData.softwareNature === 'desktop'}
                            onChange={(e) => setFormData(prev => ({ ...prev, softwareNature: e.target.value }))}
                            className="h-4 w-4 mr-2 text-blue-600"
                          />
                          <Monitor className="h-4 w-4 mr-1 text-gray-600" />
                          Desktop
                        </label>
                        
                        <label className="flex items-center p-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                          <input
                            type="radio"
                            name="softwareNature"
                            value="web"
                            checked={formData.softwareNature === 'web'}
                            onChange={(e) => setFormData(prev => ({ ...prev, softwareNature: e.target.value }))}
                            className="h-4 w-4 mr-2 text-blue-600"
                          />
                          <Globe className="h-4 w-4 mr-1 text-gray-600" />
                          Web
                        </label>
                        
                        <label className="flex items-center p-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                          <input
                            type="radio"
                            name="softwareNature"
                            value="mobile"
                            checked={formData.softwareNature === 'mobile'}
                            onChange={(e) => setFormData(prev => ({ ...prev, softwareNature: e.target.value }))}
                            className="h-4 w-4 mr-2 text-blue-600"
                          />
                          <Smartphone className="h-4 w-4 mr-1 text-gray-600" />
                          Mobile
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
        )}
        </div>

      {/* Notes */}
        <div>
        <div className="flex items-center gap-2 mb-4">
          <StickyNote className="h-5 w-5 text-gray-700" />
          <h3 className="text-lg font-semibold text-gray-900">Additional Notes</h3>
        </div>
        
          <textarea
          value={formData.notes}
            onChange={handleInputChange}
          name="notes"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            rows={4}
          placeholder="Enter any additional information or notes..."
          />
</div>

      <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
    <button
      type="button"
          className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
          onClick={handleGoBack}
    >
      Cancel
    </button>
    <button
      type="submit"
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              {isEditMode ? 'Updating...' : 'Saving...'}
            </>
          ) : (
            <>
      <Save className="h-4 w-4" />
              {isEditMode ? 'Update' : 'Save'}
            </>
          )}
    </button>
  </div>
</form>
      </div>
    </div>
  );
}

// Rename and export with new name
export function SoftwareLicenceForm(): JSX.Element {
  return <SoftwareLicenseForm />;
}