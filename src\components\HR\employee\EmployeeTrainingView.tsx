import React, { useState, useEffect } from 'react';
import { 
  GraduationCap, 
  Play, 
  CheckCircle, 
  Clock, 
  Award, 
  Calendar, 
  Download, 
  BookOpen,
  Star,
  BarChart2,
  AlertCircle,
  Video,
  ChevronRight
} from 'lucide-react';
import { useAuth } from '../../../contexts/AuthContext';

// Training course type definitions
interface Course {
  id: string;
  title: string;
  description: string;
  category: 'technical' | 'soft-skills' | 'compliance' | 'safety' | 'professional';
  duration: number; // in minutes
  deadline?: Date;
  status: 'not-started' | 'in-progress' | 'completed';
  progress: number; // percentage
  dateAssigned: Date;
  dateCompleted?: Date;
  isMandatory: boolean;
  thumbnailUrl: string;
  certificateUrl?: string;
}

const EmployeeTrainingView: React.FC = () => {
  const { user } = useAuth();
  const [courses, setCourses] = useState<Course[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'all' | 'in-progress' | 'completed' | 'certificates'>('all');
  const [selectedCourse, setSelectedCourse] = useState<Course | null>(null);
  const [showCourseDetails, setShowCourseDetails] = useState<boolean>(false);

  // Fetch training courses
  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setIsLoading(true);
        // Mock API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Sample data
        const mockCourses: Course[] = [
          {
            id: '1',
            title: 'Cybersecurity Fundamentals',
            description: 'Learn the basics of cybersecurity including threat detection, prevention, and best practices for maintaining security in the workplace.',
            category: 'technical',
            duration: 120,
            deadline: new Date(2023, 8, 30),
            status: 'in-progress',
            progress: 45,
            dateAssigned: new Date(2023, 7, 15),
            isMandatory: true,
            thumbnailUrl: 'https://via.placeholder.com/300x200?text=Cybersecurity'
          },
          {
            id: '2',
            title: 'Effective Communication',
            description: 'Improve your communication skills for better workplace interactions, meetings, and presentations.',
            category: 'soft-skills',
            duration: 90,
            status: 'completed',
            progress: 100,
            dateAssigned: new Date(2023, 5, 10),
            dateCompleted: new Date(2023, 6, 5),
            isMandatory: false,
            thumbnailUrl: 'https://via.placeholder.com/300x200?text=Communication',
            certificateUrl: 'https://example.com/certificates/comm-cert.pdf'
          },
          {
            id: '3',
            title: 'Annual Compliance Training',
            description: 'Mandatory annual training covering company policies, code of conduct, and regulatory compliance.',
            category: 'compliance',
            duration: 60,
            deadline: new Date(2023, 11, 31),
            status: 'not-started',
            progress: 0,
            dateAssigned: new Date(2023, 7, 1),
            isMandatory: true,
            thumbnailUrl: 'https://via.placeholder.com/300x200?text=Compliance'
          },
          {
            id: '4',
            title: 'Project Management Essentials',
            description: 'Learn the fundamentals of project management including planning, execution, and monitoring.',
            category: 'professional',
            duration: 180,
            status: 'completed',
            progress: 100,
            dateAssigned: new Date(2023, 4, 15),
            dateCompleted: new Date(2023, 5, 20),
            isMandatory: false,
            thumbnailUrl: 'https://via.placeholder.com/300x200?text=Project+Management',
            certificateUrl: 'https://example.com/certificates/pm-cert.pdf'
          },
          {
            id: '5',
            title: 'Workplace Safety',
            description: 'Essential safety practices for preventing accidents and responding to emergencies in the workplace.',
            category: 'safety',
            duration: 45,
            deadline: new Date(2023, 9, 15),
            status: 'in-progress',
            progress: 75,
            dateAssigned: new Date(2023, 6, 1),
            isMandatory: true,
            thumbnailUrl: 'https://via.placeholder.com/300x200?text=Safety'
          }
        ];

        setCourses(mockCourses);
        setIsLoading(false);
      } catch (error) {
        setError('Failed to load training data. Please try again later.');
        setIsLoading(false);
      }
    };

    fetchCourses();
  }, []);

  // Filter courses based on active tab
  const filteredCourses = courses.filter(course => {
    if (activeTab === 'all') return true;
    if (activeTab === 'in-progress') return course.status === 'in-progress' || course.status === 'not-started';
    if (activeTab === 'completed') return course.status === 'completed';
    if (activeTab === 'certificates') return course.status === 'completed' && course.certificateUrl;
    return true;
  });

  // Get course status badge
  const getCourseStatusBadge = (status: Course['status']) => {
    switch (status) {
      case 'completed':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <CheckCircle className="h-3 w-3 mr-1" />
          Completed
        </span>;
      case 'in-progress':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          <Play className="h-3 w-3 mr-1" />
          In Progress
        </span>;
      case 'not-started':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          <Clock className="h-3 w-3 mr-1" />
          Not Started
        </span>;
      default:
        return null;
    }
  };

  // Format date
  const formatDate = (date?: Date) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Format duration
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 
      ? `${hours} hour${hours > 1 ? 's' : ''}${mins > 0 ? ` ${mins} min` : ''}` 
      : `${mins} minutes`;
  };

  // Check if a course is due soon (within 7 days)
  const isDueSoon = (course: Course) => {
    if (!course.deadline) return false;
    
    const today = new Date();
    const deadline = new Date(course.deadline);
    const diffTime = deadline.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays > 0 && diffDays <= 7;
  };

  // Handle course selection
  const handleCourseClick = (course: Course) => {
    setSelectedCourse(course);
    setShowCourseDetails(true);
  };

  // Calculate training stats
  const completedCourses = courses.filter(course => course.status === 'completed').length;
  const totalMandatory = courses.filter(course => course.isMandatory).length;
  const completedMandatory = courses.filter(course => course.isMandatory && course.status === 'completed').length;
  const overallProgress = courses.length > 0 
    ? Math.round(courses.reduce((acc, course) => acc + course.progress, 0) / courses.length) 
    : 0;

  // Course detail modal
  const renderCourseDetailModal = () => {
    if (!showCourseDetails || !selectedCourse) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[90vh] overflow-y-auto">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-xl font-semibold text-gray-900">{selectedCourse.title}</h3>
              <div className="mt-1 flex items-center">
                {getCourseStatusBadge(selectedCourse.status)}
                {selectedCourse.isMandatory && (
                  <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    Mandatory
                  </span>
                )}
              </div>
            </div>
            <button 
              onClick={() => setShowCourseDetails(false)}
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
            >
              <span className="sr-only">Close</span>
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div className="aspect-w-16 aspect-h-9 mb-4 overflow-hidden rounded-lg bg-gray-100">
            <div className="flex items-center justify-center h-full">
              <img 
                src={selectedCourse.thumbnailUrl} 
                alt={selectedCourse.title} 
                className="object-cover w-full h-full"
              />
            </div>
          </div>
          
          <div className="mb-6">
            <h4 className="text-lg font-medium text-gray-900 mb-2">Description</h4>
            <p className="text-gray-600">{selectedCourse.description}</p>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h5 className="text-sm font-medium text-gray-500 mb-1">Category</h5>
              <p className="text-gray-900 capitalize">{selectedCourse.category}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h5 className="text-sm font-medium text-gray-500 mb-1">Duration</h5>
              <p className="text-gray-900">{formatDuration(selectedCourse.duration)}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h5 className="text-sm font-medium text-gray-500 mb-1">Assigned Date</h5>
              <p className="text-gray-900">{formatDate(selectedCourse.dateAssigned)}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h5 className="text-sm font-medium text-gray-500 mb-1">
                {selectedCourse.status === 'completed' ? 'Completion Date' : 'Deadline'}
              </h5>
              <p className="text-gray-900">
                {selectedCourse.status === 'completed' 
                  ? formatDate(selectedCourse.dateCompleted)
                  : selectedCourse.deadline 
                    ? formatDate(selectedCourse.deadline)
                    : 'No deadline'}
              </p>
            </div>
          </div>
          
          {selectedCourse.status !== 'completed' && (
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium text-gray-900">Progress</h4>
                <span className="text-sm font-medium text-gray-900">{selectedCourse.progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full" 
                  style={{ width: `${selectedCourse.progress}%` }}
                ></div>
              </div>
            </div>
          )}
          
          <div className="flex justify-end space-x-3">
            {selectedCourse.status === 'completed' && selectedCourse.certificateUrl && (
              <button
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                <Award className="h-4 w-4 mr-2" />
                View Certificate
              </button>
            )}
            {selectedCourse.status !== 'completed' && (
              <button
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Play className="h-4 w-4 mr-2" />
                {selectedCourse.status === 'not-started' ? 'Start Course' : 'Continue Course'}
              </button>
            )}
          </div>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="p-4 flex justify-center items-center h-64">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
          <p className="text-gray-600">Loading training data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
          <div className="flex items-center">
            <AlertCircle className="h-6 w-6 text-red-500 mr-2" />
            <p className="text-red-700">{error}</p>
          </div>
          <button 
            className="mt-2 bg-red-100 text-red-700 px-4 py-2 rounded hover:bg-red-200"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Training & Development</h2>
      </div>

      {/* Training Stats Overview */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Overall Progress</p>
              <p className="text-2xl font-bold text-gray-900">{overallProgress}%</p>
            </div>
            <div className="bg-blue-100 rounded-full p-3">
              <BarChart2 className="h-6 w-6 text-blue-500" />
            </div>
          </div>
          <div className="mt-4 w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full" 
              style={{ width: `${overallProgress}%` }}
            ></div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Completed Courses</p>
              <p className="text-2xl font-bold text-gray-900">{completedCourses} of {courses.length}</p>
            </div>
            <div className="bg-green-100 rounded-full p-3">
              <CheckCircle className="h-6 w-6 text-green-500" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Mandatory Compliance</p>
              <p className="text-2xl font-bold text-gray-900">{completedMandatory} of {totalMandatory}</p>
            </div>
            <div className="bg-red-100 rounded-full p-3">
              <AlertCircle className="h-6 w-6 text-red-500" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Certificates Earned</p>
              <p className="text-2xl font-bold text-gray-900">
                {courses.filter(c => c.certificateUrl).length}
              </p>
            </div>
            <div className="bg-yellow-100 rounded-full p-3">
              <Award className="h-6 w-6 text-yellow-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Tabs for filtering courses */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="flex -mb-px">
            <button
              onClick={() => setActiveTab('all')}
              className={`mr-8 py-4 text-sm font-medium whitespace-nowrap ${
                activeTab === 'all'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              All Courses
            </button>
            <button
              onClick={() => setActiveTab('in-progress')}
              className={`mr-8 py-4 text-sm font-medium whitespace-nowrap ${
                activeTab === 'in-progress'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              In Progress
            </button>
            <button
              onClick={() => setActiveTab('completed')}
              className={`mr-8 py-4 text-sm font-medium whitespace-nowrap ${
                activeTab === 'completed'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Completed
            </button>
            <button
              onClick={() => setActiveTab('certificates')}
              className={`py-4 text-sm font-medium whitespace-nowrap ${
                activeTab === 'certificates'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Certificates
            </button>
          </nav>
        </div>
      </div>

      {/* Courses Grid */}
      {filteredCourses.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm p-8 text-center">
          <GraduationCap className="h-12 w-12 mx-auto text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-900">No courses found</h3>
          <p className="text-gray-500 mt-2">
            {activeTab === 'in-progress' 
              ? "You don't have any in-progress courses" 
              : activeTab === 'completed'
                ? "You haven't completed any courses yet"
                : activeTab === 'certificates'
                  ? "You haven't earned any certificates yet"
                  : "You don't have any assigned courses"}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCourses.map((course) => (
            <div 
              key={course.id}
              className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-100 hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => handleCourseClick(course)}
            >
              <div className="aspect-w-16 aspect-h-9 bg-gray-100">
                <img 
                  src={course.thumbnailUrl} 
                  alt={course.title} 
                  className="object-cover w-full h-48"
                />
                {course.isMandatory && (
                  <div className="absolute top-2 right-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-red-100 text-red-800">
                      Mandatory
                    </span>
                  </div>
                )}
                {course.certificateUrl && (
                  <div className="absolute top-2 left-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-yellow-100 text-yellow-800">
                      <Award className="h-3 w-3 mr-1" />
                      Certificate
                    </span>
                  </div>
                )}
              </div>
              
              <div className="p-4">
                <div className="flex justify-between items-start">
                  <h3 className="text-lg font-medium text-gray-900 mb-1">{course.title}</h3>
                  <div className="ml-2">
                    {getCourseStatusBadge(course.status)}
                  </div>
                </div>
                
                <p className="text-sm text-gray-500 mb-4 line-clamp-2">{course.description}</p>
                
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-1" />
                    {formatDuration(course.duration)}
                  </div>
                  
                  {course.deadline && (
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      <span className={isDueSoon(course) ? 'text-red-600 font-medium' : ''}>
                        Due: {formatDate(course.deadline)}
                        {isDueSoon(course) && ' (Soon)'}
                      </span>
                    </div>
                  )}
                </div>
                
                {course.status !== 'completed' && (
                  <div className="mt-4">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs font-medium text-gray-500">Progress</span>
                      <span className="text-xs font-medium text-gray-900">{course.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div 
                        className="bg-blue-600 h-1.5 rounded-full" 
                        style={{ width: `${course.progress}%` }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>
              
              <div className="px-4 py-3 bg-gray-50 border-t border-gray-100">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500">
                    {course.status === 'completed' 
                      ? `Completed on ${formatDate(course.dateCompleted)}` 
                      : `Assigned on ${formatDate(course.dateAssigned)}`}
                  </span>
                  <button className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-500">
                    {course.status === 'completed' 
                      ? course.certificateUrl 
                        ? 'View Certificate' 
                        : 'Details'
                      : course.status === 'in-progress' 
                        ? 'Continue' 
                        : 'Start'}
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Course detail modal */}
      {renderCourseDetailModal()}
    </div>
  );
};

export default EmployeeTrainingView; 