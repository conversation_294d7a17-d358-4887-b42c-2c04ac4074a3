import { Request, Response } from 'express';
import { ShiftService, CreateShiftDto, UpdateShiftDto, CreateShiftAssignmentDto } from '../services/ShiftService';
import { DataSource } from 'typeorm';
import { ShiftRepository } from '../repositories/ShiftRepository';

export class ShiftController {
  private shiftService: ShiftService;

  constructor(dataSource: DataSource) {
    const shiftRepository = new ShiftRepository(dataSource);
    this.shiftService = new ShiftService(shiftRepository);
  }

  // Shift CRUD endpoints
  createShift = async (req: Request, res: Response): Promise<void> => {
    try {
      const createShiftDto: CreateShiftDto = req.body;
      const shift = await this.shiftService.createShift(createShiftDto);
      
      res.status(201).json({
        success: true,
        message: 'Shift created successfully',
        data: shift
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create shift',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  getAllShifts = async (req: Request, res: Response): Promise<void> => {
    try {
      const shifts = await this.shiftService.getAllShifts();
      
      res.status(200).json({
        success: true,
        message: 'Shifts retrieved successfully',
        data: shifts
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve shifts',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  getActiveShifts = async (req: Request, res: Response): Promise<void> => {
    try {
      const shifts = await this.shiftService.getActiveShifts();
      
      res.status(200).json({
        success: true,
        message: 'Active shifts retrieved successfully',
        data: shifts
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve active shifts',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  getShiftById = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const shiftId = parseInt(id, 10);
      
      if (isNaN(shiftId)) {
        res.status(400).json({
          success: false,
          message: 'Invalid shift ID',
          error: 'Shift ID must be a number'
        });
        return;
      }

      const shift = await this.shiftService.getShiftById(shiftId);
      
      res.status(200).json({
        success: true,
        message: 'Shift retrieved successfully',
        data: shift
      });
    } catch (error) {
      res.status(404).json({
        success: false,
        message: error instanceof Error ? error.message : 'Shift not found',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  updateShift = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const shiftId = parseInt(id, 10);
      
      if (isNaN(shiftId)) {
        res.status(400).json({
          success: false,
          message: 'Invalid shift ID',
          error: 'Shift ID must be a number'
        });
        return;
      }

      const updateShiftDto: UpdateShiftDto = req.body;
      const shift = await this.shiftService.updateShift(shiftId, updateShiftDto);
      
      res.status(200).json({
        success: true,
        message: 'Shift updated successfully',
        data: shift
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to update shift',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  deleteShift = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const shiftId = parseInt(id, 10);
      
      if (isNaN(shiftId)) {
        res.status(400).json({
          success: false,
          message: 'Invalid shift ID',
          error: 'Shift ID must be a number'
        });
        return;
      }

      await this.shiftService.deleteShift(shiftId);
      
      res.status(200).json({
        success: true,
        message: 'Shift deleted successfully',
        data: null
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to delete shift',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  toggleShiftStatus = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const shiftId = parseInt(id, 10);
      
      if (isNaN(shiftId)) {
        res.status(400).json({
          success: false,
          message: 'Invalid shift ID',
          error: 'Shift ID must be a number'
        });
        return;
      }

      const shift = await this.shiftService.toggleShiftStatus(shiftId);
      
      res.status(200).json({
        success: true,
        message: `Shift ${shift.isActive ? 'activated' : 'deactivated'} successfully`,
        data: shift
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to toggle shift status',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  // Shift assignment endpoints
  assignShiftToEmployee = async (req: Request, res: Response): Promise<void> => {
    try {
      // Check if this is a bulk assignment request
      if (req.body.assignments && Array.isArray(req.body.assignments)) {
        // Handle bulk assignments
        const assignments = req.body.assignments;
        const results = [];
        
        for (const assignmentData of assignments) {
          try {
            const assignment = await this.shiftService.assignShiftToEmployee(assignmentData);
            results.push(assignment);
          } catch (error) {
            console.error('Failed to assign shift to employee:', assignmentData.employeeId, error);
            // Continue with other assignments even if one fails
          }
        }
        
        res.status(201).json({
          success: true,
          message: `Successfully assigned shifts to ${results.length} employees`,
          data: results
        });
      } else {
        // Handle single assignment
        const assignmentDto: CreateShiftAssignmentDto = req.body;
        const assignment = await this.shiftService.assignShiftToEmployee(assignmentDto);
        
        res.status(201).json({
          success: true,
          message: 'Shift assigned to employee successfully',
          data: assignment
        });
      }
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to assign shift to employee',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  getEmployeeShiftAssignments = async (req: Request, res: Response): Promise<void> => {
    try {
      const { employeeId } = req.params;
      const empId = parseInt(employeeId, 10);
      
      if (isNaN(empId)) {
        res.status(400).json({
          success: false,
          message: 'Invalid employee ID',
          error: 'Employee ID must be a number'
        });
        return;
      }

      const assignments = await this.shiftService.getEmployeeShiftAssignments(empId);
      
      res.status(200).json({
        success: true,
        message: 'Employee shift assignments retrieved successfully',
        data: assignments
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve employee shift assignments',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  getCurrentEmployeeShift = async (req: Request, res: Response): Promise<void> => {
    try {
      const { employeeId } = req.params;
      const { date } = req.query;
      const empId = parseInt(employeeId, 10);
      
      if (isNaN(empId)) {
        res.status(400).json({
          success: false,
          message: 'Invalid employee ID',
          error: 'Employee ID must be a number'
        });
        return;
      }

      const assignment = await this.shiftService.getCurrentEmployeeShift(empId, date as string);
      
      res.status(200).json({
        success: true,
        message: assignment ? 'Current employee shift retrieved successfully' : 'No current shift found for employee',
        data: assignment
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve current employee shift',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  getShiftAssignments = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const shiftId = parseInt(id, 10);
      
      if (isNaN(shiftId)) {
        res.status(400).json({
          success: false,
          message: 'Invalid shift ID',
          error: 'Shift ID must be a number'
        });
        return;
      }

      const assignments = await this.shiftService.getShiftAssignments(shiftId);
      
      res.status(200).json({
        success: true,
        message: 'Shift assignments retrieved successfully',
        data: assignments
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve shift assignments',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  endShiftAssignment = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const { endDate } = req.body;
      const assignmentId = parseInt(id, 10);
      
      if (isNaN(assignmentId)) {
        res.status(400).json({
          success: false,
          message: 'Invalid assignment ID',
          error: 'Assignment ID must be a number'
        });
        return;
      }

      const assignment = await this.shiftService.endShiftAssignment(assignmentId, endDate);
      
      res.status(200).json({
        success: true,
        message: 'Shift assignment ended successfully',
        data: assignment
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to end shift assignment',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  deleteShiftAssignment = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const assignmentId = parseInt(id, 10);
      
      if (isNaN(assignmentId)) {
        res.status(400).json({
          success: false,
          message: 'Invalid assignment ID',
          error: 'Assignment ID must be a number'
        });
        return;
      }

      await this.shiftService.deleteShiftAssignment(assignmentId);
      
      res.status(200).json({
        success: true,
        message: 'Shift assignment deleted successfully',
        data: null
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to delete shift assignment',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  // Utility endpoints
  getShiftStatistics = async (req: Request, res: Response): Promise<void> => {
    try {
      const statistics = await this.shiftService.getShiftStatistics();
      
      res.status(200).json({
        success: true,
        message: 'Shift statistics retrieved successfully',
        data: statistics
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve shift statistics',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };

  getEmployeesWithoutShift = async (req: Request, res: Response): Promise<void> => {
    try {
      const employeeIds = await this.shiftService.getEmployeesWithoutShift();
      
      res.status(200).json({
        success: true,
        message: 'Employees without shift retrieved successfully',
        data: employeeIds
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve employees without shift',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };
} 