import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, OneToMany, BeforeInsert } from 'typeorm';
import { User } from './User';
import { IsNotEmpty, IsString, IsOptional, IsDate, IsNumber, IsBoolean, IsIP, ValidateNested, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';

export enum AssetType {
  Computing = 'Computing',
  MobileTablet = 'Mobile & Tablet',
  NetworkingCommunication = 'Networking & Communication',
  Printing = 'Printing',
  DisplayMultimedia = 'Display & Multimedia',
  SecuritySurveillance = 'Security & Surveillance',
  PeripheralsAccessories = 'Peripherals & Accessories',
  OtherEquipment = 'Other Equipment'
}

export enum AssetStatus {
  Active = 'Active',
  Reserved = 'Reserved',
  UnderRepair = 'Under Repair',
  Retired = 'Retired',
  LostStolen = 'Lost/Stolen',
  Decommissioned = 'Decommissioned',
  InTransit = 'In Transit',
  Other = 'Other'
}

export enum AssetCondition {
  New = 'New',
  Good = 'Good',
  Fair = 'Fair',
  NeedsRepair = 'Needs Repair',
  Retired = 'Retired',
  Other = 'Other'
}

@Entity('assets')
export class Asset {
  @PrimaryGeneratedColumn({ type: 'int' })
  id: number;

  @Column({ type: 'varchar', length: 50 })
  @IsNotEmpty()
  @IsEnum(AssetType, { message: 'Asset type must be one of the valid types' })
  assetType: AssetType;

  @Column({ type: 'varchar', length: 100 })
  @IsNotEmpty({ message: 'Category is required' })
  @IsString({ message: 'Category must be a string' })
  category: string;

  @Column({ type: 'varchar', length: 100 })
  @IsNotEmpty({ message: 'Manufacturer is required' })
  @IsString({ message: 'Manufacturer must be a string' })
  manufacturer: string;

  @Column({ type: 'varchar', length: 100 })
  @IsNotEmpty({ message: 'Model is required' })
  @IsString({ message: 'Model must be a string' })
  model: string;

  @Column({ type: 'varchar', length: 100, unique: false, nullable: true })
  @IsOptional()
  @IsString({ message: 'Serial number must be a string' })
  serialNumber?: string;

  @Column({ type: 'varchar', length: 50, unique: true })
  @IsNotEmpty({ message: 'Asset tag is required' })
  @IsString({ message: 'Asset tag must be a string' })
  assetTag: string;

  @Column({ type: 'varchar', length: 100 })
  @IsNotEmpty({ message: 'Location is required' })
  @IsString({ message: 'Location must be a string' })
  location: string;

  @Column({ type: 'varchar', length: 50 })
  @IsNotEmpty({ message: 'Department is required' })
  @IsString({ message: 'Department must be a string' })
  department: string;

  @Column({ type: 'varchar', length: 50 })
  @IsNotEmpty({ message: 'Status is required' })
  @IsEnum(AssetStatus, { message: 'Status must be one of the valid statuses' })
  status: AssetStatus;

  @Column({ type: 'varchar', length: 50 })
  @IsNotEmpty({ message: 'Condition is required' })
  @IsEnum(AssetCondition, { message: 'Condition must be one of the valid conditions' })
  condition: AssetCondition;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  purchaseDate: Date;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  warrantyExpiry: Date;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber()
  cost: number;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString()
  vendor: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString({ message: 'Project must be a string' })
  project: string;

  @Column({ type: 'boolean', default: false })
  @IsBoolean()
  internetAccess: boolean;

  @Column({ type: 'varchar', length: 50, nullable: true })
  @IsOptional()
  @IsIP()
  ipAddress: string;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  @ValidateNested()
  attributes: Record<string, any>;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString()
  notes: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'assignedToId' })
  @IsOptional()
  assignedTo?: User;

  @Column({ type: 'varchar', length: 36, nullable: true })
  @IsOptional()
  @IsString()
  assignedToId?: string;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  assignedAt?: Date;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  lastMaintenance: Date;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString()
  maintenanceBy: string;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  nextMaintenance: Date;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber()
  maintenanceCost: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 