import { Request, Response } from 'express';
import ITOperationLogService from '../services/ITOperationLogService';
import { IssueCategory, ImpactLevel, OperationStatus } from '../entities/ITOperationLog';
import { ParsedQs } from 'qs';

export const ITOperationLogController = {
  // Get all operation logs
  getAllLogs: async (req: Request, res: Response): Promise<void> => {
    try {
      // Extract query parameters
      const { 
        page, 
        limit, 
        sortBy, 
        sortOrder, 
        search, 
        issueCategory, 
        status, 
        impactLevel, 
        dateFrom, 
        dateTo, 
        tags 
      } = req.query;

      // Parse pagination parameters
      const pageNum = page ? parseInt(page as string, 10) : undefined;
      const limitNum = limit ? parseInt(limit as string, 10) : undefined;
      
      // Parse tags - fix the type handling
      let parsedTags: string | string[] | undefined;
      if (tags) {
        if (typeof tags === 'string') {
          parsedTags = tags.split(','); 
        } else if (Array.isArray(tags)) {
          parsedTags = tags.map(t => t.toString());
        } else if (typeof tags === 'object' && tags !== null) {
          // Handle case where tags is a ParsedQs object
          const tagValue = (tags as ParsedQs)[0] || '';
          parsedTags = tagValue.toString().split(',');
        }
      }
      
      // Get logs with filtering and pagination
      const result = await ITOperationLogService.getAllLogs({
        page: pageNum,
        limit: limitNum,
        sortBy: sortBy as string,
        sortOrder: (sortOrder as 'ASC' | 'DESC'),
        search: search as string,
        issueCategory: issueCategory as string,
        status: status as string,
        impactLevel: impactLevel as string,
        dateFrom: dateFrom as string,
        dateTo: dateTo as string,
        tags: parsedTags
      });
      
      res.status(200).json({
        success: true,
        ...result
      });
    } catch (error) {
      console.error('Error in getAllLogs controller:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch operation logs',
        error: (error as Error).message
      });
    }
  },
  
  // Get a single operation log by ID
  getLogById: async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      console.log('Backend: Getting log with ID:', id);
      
      const log = await ITOperationLogService.getLogById(id);
      console.log('Backend: Found log:', log);
      
      res.status(200).json({
        success: true,
        data: log
      });
    } catch (error) {
      console.error(`Error in getLogById controller for ID ${req.params.id}:`, error);
      
      if ((error as Error).message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: `Operation log with ID ${req.params.id} not found`,
          error: (error as Error).message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Failed to fetch operation log',
          error: (error as Error).message
        });
      }
    }
  },
  
  // Create a new operation log
  createLog: async (req: Request, res: Response): Promise<void> => {
    try {
      const formData = req.body;
      
      // Add current user information
      // @ts-ignore - assuming req.user is added by auth middleware
      const currentUser = req.user;
      
      console.log('Current user from middleware:', currentUser); // Add this for debugging
      
      // Validate required fields
      if (!formData.title) {
        res.status(400).json({
          success: false,
          message: 'Title is required'
        });
        return;
      }
      
      if (!formData.description) {
        res.status(400).json({
          success: false,
          message: 'Description is required'
        });
        return;
      }
      
      if (!formData.loggedBy) {
        res.status(400).json({
          success: false,
          message: 'Logged by is required'
        });
        return;
      }
      
      if (!formData.dateOccurred) {
        res.status(400).json({
          success: false,
          message: 'Date occurred is required'
        });
        return;
      }
      
      // Validate enums
      if (formData.issueCategory && !Object.values(IssueCategory).includes(formData.issueCategory as IssueCategory)) {
        res.status(400).json({
          success: false,
          message: 'Invalid issue category'
        });
        return;
      }
      
      if (formData.impactLevel && !Object.values(ImpactLevel).includes(formData.impactLevel as ImpactLevel)) {
        res.status(400).json({
          success: false,
          message: 'Invalid impact level'
        });
        return;
      }
      
      if (formData.status && !Object.values(OperationStatus).includes(formData.status as OperationStatus)) {
        res.status(400).json({
          success: false,
          message: 'Invalid status'
        });
        return;
      }
      
      // File uploads handling (if file was uploaded)
      if (req.file) {
        formData.attachment = req.file.filename;
      }
      
      // Pass the actual user object, not just the ID
      const log = await ITOperationLogService.createLog(formData, currentUser);
      
      res.status(201).json({
        success: true,
        message: 'Operation log created successfully',
        data: log
      });
    } catch (error) {
      console.error('Error in createLog controller:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create operation log',
        error: (error as Error).message
      });
    }
  },
  
  // Update an existing operation log
  updateLog: async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const formData = req.body;
      
      // Add current user information
      // @ts-ignore - assuming req.user is added by auth middleware
      const currentUser = req.user;
      
      // Validate enums if provided
      if (formData.issueCategory && !Object.values(IssueCategory).includes(formData.issueCategory as IssueCategory)) {
        res.status(400).json({
          success: false,
          message: 'Invalid issue category'
        });
        return;
      }
      
      if (formData.impactLevel && !Object.values(ImpactLevel).includes(formData.impactLevel as ImpactLevel)) {
        res.status(400).json({
          success: false,
          message: 'Invalid impact level'
        });
        return;
      }
      
      if (formData.status && !Object.values(OperationStatus).includes(formData.status as OperationStatus)) {
        res.status(400).json({
          success: false,
          message: 'Invalid status'
        });
        return;
      }
      
      // File uploads handling (if file was uploaded)
      if (req.file) {
        formData.attachment = req.file.filename;
      }
      
      const log = await ITOperationLogService.updateLog(id, formData, currentUser);
      
      res.status(200).json({
        success: true,
        message: 'Operation log updated successfully',
        data: log
      });
    } catch (error) {
      console.error(`Error in updateLog controller for ID ${req.params.id}:`, error);
      
      if ((error as Error).message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: `Operation log with ID ${req.params.id} not found`,
          error: (error as Error).message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Failed to update operation log',
          error: (error as Error).message
        });
      }
    }
  },
  
  // Delete an operation log
  deleteLog: async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      
      await ITOperationLogService.deleteLog(id);
      
      res.status(200).json({
        success: true,
        message: 'Operation log deleted successfully'
      });
    } catch (error) {
      console.error(`Error in deleteLog controller for ID ${req.params.id}:`, error);
      
      if ((error as Error).message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: `Operation log with ID ${req.params.id} not found`,
          error: (error as Error).message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Failed to delete operation log',
          error: (error as Error).message
        });
      }
    }
  },
  
  // Get statistics for operation logs
  getStatistics: async (req: Request, res: Response): Promise<void> => {
    try {
      const statistics = await ITOperationLogService.getStatistics();
      
      res.status(200).json({
        success: true,
        data: statistics
      });
    } catch (error) {
      console.error('Error in getStatistics controller:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get operation log statistics',
        error: (error as Error).message
      });
    }
  }
};

export default ITOperationLogController; 