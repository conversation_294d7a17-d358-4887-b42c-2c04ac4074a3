import { AppDataSource } from '../config/database';

const addDateFieldsToLeaveTypePolicy = async () => {
  try {
    console.log('🔄 Starting database update...');
    
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ Database connection established');
    }

    const queryRunner = AppDataSource.createQueryRunner();

    try {
      // Check if effectiveFrom column exists
      const effectiveFromExists = await queryRunner.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'ims_db' 
        AND TABLE_NAME = 'leave_type_policies' 
        AND COLUMN_NAME = 'effectiveFrom'
      `);

      if (effectiveFromExists.length === 0) {
        await queryRunner.query(`
          ALTER TABLE leave_type_policies 
          ADD COLUMN effectiveFrom DATE NULL
        `);
        console.log('✅ Added effectiveFrom column');
      } else {
        console.log('⏩ effectiveFrom column already exists');
      }

      // Check if validUntil column exists
      const validUntilExists = await queryRunner.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'ims_db' 
        AND TABLE_NAME = 'leave_type_policies' 
        AND COLUMN_NAME = 'validUntil'
      `);

      if (validUntilExists.length === 0) {
        await queryRunner.query(`
          ALTER TABLE leave_type_policies 
          ADD COLUMN validUntil DATE NULL
        `);
        console.log('✅ Added validUntil column');
      } else {
        console.log('⏩ validUntil column already exists');
      }

      // Check if genderEligibility column exists
      const genderEligibilityExists = await queryRunner.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'ims_db' 
        AND TABLE_NAME = 'leave_type_policies' 
        AND COLUMN_NAME = 'genderEligibility'
      `);

      if (genderEligibilityExists.length === 0) {
        await queryRunner.query(`
          ALTER TABLE leave_type_policies 
          ADD COLUMN genderEligibility VARCHAR(20) NULL DEFAULT 'all'
        `);
        console.log('✅ Added genderEligibility column');
      } else {
        console.log('⏩ genderEligibility column already exists');
      }

      console.log('🎉 Database update completed successfully!');

    } catch (error) {
      console.error('❌ Error during database update:', error);
      throw error;
    } finally {
      await queryRunner.release();
    }

  } catch (error) {
    console.error('❌ Failed to update database:', error);
    process.exit(1);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('🔌 Database connection closed');
    }
  }
};

// Run the script
addDateFieldsToLeaveTypePolicy(); 