import { MigrationInterface, QueryRunner } from "typeorm";

export class TestHolidayTables1748946061758 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create holidays table if it doesn't exist
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS holidays (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(100) NOT NULL,
                date DATE NOT NULL,
                type ENUM('public', 'company', 'optional') DEFAULT 'public',
                description TEXT,
                isActive BOOLEAN DEFAULT true,
                metadata JSON,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);
        
        // Create holiday_configurations table if it doesn't exist
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS holiday_configurations (
                id INT PRIMARY KEY AUTO_INCREMENT,
                organizationId INT,
                weekendDays VARCHAR(20) DEFAULT '[0,6]',
                timezone VARCHAR(50) DEFAULT 'UTC',
                workingHours JSON,
                holidaySettings JSON,
                applicableRegions JSON,
                applicableDepartments JSON,
                createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);
        
        // Insert sample religious holidays
        await queryRunner.query(`
            INSERT IGNORE INTO holidays (name, date, type, description, metadata) VALUES
            ('Eid al-Fitr', '2024-04-10', 'optional', 'Islamic festival celebrating the end of Ramadan', '{"religion": "Islam", "duration": 3, "culturalSignificance": "Major Islamic holiday"}'),
            ('Eid al-Adha', '2024-06-16', 'optional', 'Islamic festival of sacrifice', '{"religion": "Islam", "duration": 4, "culturalSignificance": "Major Islamic holiday"}'),
            ('Diwali', '2024-11-01', 'optional', 'Hindu festival of lights', '{"religion": "Hinduism", "duration": 5, "culturalSignificance": "Major Hindu holiday"}'),
            ('Christmas Day', '2024-12-25', 'public', 'Christian holiday celebrating the birth of Jesus Christ', '{"religion": "Christianity", "duration": 1, "culturalSignificance": "Major Christian holiday"}'),
            ('Chinese New Year', '2024-02-10', 'optional', 'Traditional Chinese New Year celebration', '{"culturalSignificance": "Major Chinese cultural holiday", "duration": 3}')
        `);
        
        // Insert default configuration
        await queryRunner.query(`
            INSERT IGNORE INTO holiday_configurations (organizationId, weekendDays, holidaySettings) VALUES
            (1, '[0,6]', '{"allowOptionalHolidays": true, "maxOptionalHolidaysPerEmployee": 5}')
        `);
        
        console.log('Holiday tables and sample data created successfully');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE IF EXISTS holiday_configurations`);
        await queryRunner.query(`DROP TABLE IF EXISTS holidays`);
        console.log('Holiday tables dropped');
    }

}
