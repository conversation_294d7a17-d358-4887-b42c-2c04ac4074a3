import React, { useState, useEffect } from 'react';
import { Save, Cog, Shield, Mail, Database, RefreshCw, FileText, Bell, User, Palette, Zap, BarChart3, Globe } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import GeneralSettings from '../settings/GeneralSettings';
import SecuritySettings from '../settings/SecuritySettings';
import EmailSettings from '../settings/EmailSettings';
import BackupSettings from '../settings/BackupSettings';
import NotificationSettings from '../settings/NotificationSettings';
import CustomizationSettings from '../settings/CustomizationSettings';
import IntegrationSettings from '../settings/IntegrationSettings';
import PerformanceSettings from '../settings/PerformanceSettings';
import APISettings from '../settings/APISettings';

interface SystemSetting {
  id: string;
  category: string;
  key: string;
  value: string;
  description?: string;
  lastUpdated?: string;
  updatedBy?: string;
}

export const SystemSettings = () => {
  const { user } = useAuth();
  const [activeCategory, setActiveCategory] = useState('general');
  const [settings, setSettings] = useState<SystemSetting[]>([]);
  const [isChanged, setIsChanged] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  
  // Debug logging for state changes
  useEffect(() => {
    console.log('🔧 SystemSettings: activeCategory changed to:', activeCategory);
    console.log('🔧 SystemSettings: settings array length:', settings.length);
    console.log('🔧 SystemSettings: settings array sample:', settings.slice(0, 3));
    console.log('🔧 SystemSettings: all categories in settings:', [...new Set(settings.map(s => s.category))]);
  }, [activeCategory, settings]);
  
  // Load settings from storage (or API in real app)
  useEffect(() => {
    console.log('🔧 SystemSettings: Loading settings from localStorage');
    const storedSettings = localStorage.getItem('systemSettings');
    console.log('🔧 SystemSettings: Raw localStorage data:', storedSettings);
    
    if (storedSettings) {
      try {
        const parsedSettings = JSON.parse(storedSettings);
        console.log('🔧 SystemSettings: Found stored settings:', parsedSettings.length, 'items');
        console.log('🔧 SystemSettings: First 3 stored settings:', parsedSettings.slice(0, 3));
        setSettings(parsedSettings);
      } catch (error) {
        console.error('🔧 SystemSettings: Error parsing stored settings:', error);
        console.log('🔧 SystemSettings: Creating default settings due to parse error');
        createDefaultSettings();
      }
    } else {
      console.log('🔧 SystemSettings: No stored settings found, creating defaults');
      createDefaultSettings();
    }
  }, []);

  const createDefaultSettings = () => {
    // Default settings if none exist
    const defaultSettings = [
      // General settings
      {
        id: 'setting-1',
        category: 'general',
        key: 'systemName',
        value: 'IT Management System',
        description: 'The name displayed in the header and browser title'
      },
      {
        id: 'setting-2',
        category: 'general',
        key: 'defaultLanguage',
        value: 'en',
        description: 'Default language for the system'
      },
      {
        id: 'setting-3',
        category: 'general',
        key: 'timeZone',
        value: 'UTC',
        description: 'Time zone for date/time display'
      },
      {
        id: 'setting-1a',
        category: 'general',
        key: 'companyName',
        value: 'Your Company Name',
        description: 'Company name displayed throughout the system'
      },
      {
        id: 'setting-1b',
        category: 'general',
        key: 'companyAddress',
        value: '123 Business Street, City, State 12345',
        description: 'Company address for reports and documents'
      },
      {
        id: 'setting-1c',
        category: 'general',
        key: 'companyPhone',
        value: '+****************',
        description: 'Company phone number'
      },
      {
        id: 'setting-1d',
        category: 'general',
        key: 'companyEmail',
        value: '<EMAIL>',
        description: 'Company email address'
      },
      {
        id: 'setting-1e',
        category: 'general',
        key: 'dateFormat',
        value: 'MM/DD/YYYY',
        description: 'Default date format for the system'
      },
      {
        id: 'setting-1f',
        category: 'general',
        key: 'timeFormat',
        value: '12',
        description: 'Time format (12 or 24 hour)'
      },
      {
        id: 'setting-1g',
        category: 'general',
        key: 'currency',
        value: 'USD',
        description: 'Default currency for financial calculations'
      },
      {
        id: 'setting-1h',
        category: 'general',
        key: 'enableMaintenanceMode',
        value: 'false',
        description: 'Enable maintenance mode to restrict system access'
      },
      {
        id: 'setting-1i',
        category: 'general',
        key: 'maxFileUploadSize',
        value: '10',
        description: 'Maximum file upload size in MB'
      },
      {
        id: 'setting-1j',
        category: 'general',
        key: 'enableDebugMode',
        value: 'false',
        description: 'Enable debug mode for troubleshooting'
      },
      {
        id: 'setting-1k',
        category: 'general',
        key: 'systemVersion',
        value: '1.0.0',
        description: 'Current system version'
      },
      {
        id: 'setting-1l',
        category: 'general',
        key: 'defaultItemsPerPage',
        value: '25',
        description: 'Default number of items to display per page in lists'
      },
      
      // Security settings
      {
        id: 'setting-4',
        category: 'security',
        key: 'passwordPolicy',
        value: 'strong',
        description: 'Password complexity requirements'
      },
      {
        id: 'setting-5',
        category: 'security',
        key: 'sessionTimeout',
        value: '30',
        description: 'Minutes until inactive session expires'
      },
      {
        id: 'setting-6',
        category: 'security',
        key: 'twoFactorAuth',
        value: 'false',
        description: 'Require two-factor authentication'
      },
      
      // Email settings
      {
        id: 'setting-7',
        category: 'email',
        key: 'smtpServer',
        value: 'smtp.gmail.com',
        description: 'SMTP server hostname for sending emails'
      },
      {
        id: 'setting-8',
        category: 'email',
        key: 'smtpPort',
        value: '587',
        description: 'SMTP server port (587 for TLS, 465 for SSL)'
      },
      {
        id: 'setting-9',
        category: 'email',
        key: 'smtpUsername',
        value: '',
        description: 'SMTP authentication username'
      },
      {
        id: 'setting-9a',
        category: 'email',
        key: 'smtpPassword',
        value: '',
        description: 'SMTP authentication password'
      },
      {
        id: 'setting-9b',
        category: 'email',
        key: 'smtpSecurity',
        value: 'TLS',
        description: 'SMTP security protocol (TLS, SSL, or None)'
      },
      {
        id: 'setting-9c',
        category: 'email',
        key: 'emailFrom',
        value: '<EMAIL>',
        description: 'From email address for system emails'
      },
      {
        id: 'setting-9d',
        category: 'email',
        key: 'emailFromName',
        value: 'IT Management System',
        description: 'From name for system emails'
      },
      {
        id: 'setting-9e',
        category: 'email',
        key: 'emailTimeout',
        value: '30',
        description: 'Email sending timeout in seconds'
      },
      {
        id: 'setting-9f',
        category: 'email',
        key: 'enableEmailNotifications',
        value: 'true',
        description: 'Enable email notifications for system events'
      },
      {
        id: 'setting-9g',
        category: 'email',
        key: 'emailTemplate',
        value: 'Hello {{name}},\n\nThis is a notification from the IT Management System.\n\n{{message}}\n\nBest regards,\nIT Management Team',
        description: 'Default email template for notifications'
      },
      
      // Backup settings
      {
        id: 'setting-10',
        category: 'backup',
        key: 'backupFrequency',
        value: 'daily',
        description: 'How often to perform automatic backups'
      },
      {
        id: 'setting-11',
        category: 'backup',
        key: 'retentionPeriod',
        value: '30',
        description: 'Days to keep backups before deletion'
      },
      {
        id: 'setting-12',
        category: 'backup',
        key: 'autoBackup',
        value: 'true',
        description: 'Enable automatic backups'
      },
      
      // Notification settings
      {
        id: 'setting-13',
        category: 'notifications',
        key: 'enableEmailNotifications',
        value: 'true',
        description: 'Enable email notifications for system events'
      },
      {
        id: 'setting-14',
        category: 'notifications',
        key: 'enablePushNotifications',
        value: 'true',
        description: 'Enable push notifications for mobile devices'
      },
      {
        id: 'setting-15',
        category: 'notifications',
        key: 'enableInAppNotifications',
        value: 'true',
        description: 'Show in-app notifications'
      },
      {
        id: 'setting-16',
        category: 'notifications',
        key: 'smtpServer',
        value: 'smtp.gmail.com',
        description: 'SMTP server hostname for sending notification emails'
      },
      {
        id: 'setting-17',
        category: 'notifications',
        key: 'smtpPort',
        value: '587',
        description: 'SMTP server port (usually 587 for TLS, 465 for SSL)'
      },
      {
        id: 'setting-18',
        category: 'notifications',
        key: 'smtpUsername',
        value: '',
        description: 'SMTP authentication username'
      },
      {
        id: 'setting-19',
        category: 'notifications',
        key: 'smtpPassword',
        value: '',
        description: 'SMTP authentication password'
      },
      {
        id: 'setting-20',
        category: 'notifications',
        key: 'smtpSecurity',
        value: 'tls',
        description: 'SMTP security protocol (none, tls, ssl)'
      },
      {
        id: 'setting-21',
        category: 'notifications',
        key: 'emailFromAddress',
        value: '<EMAIL>',
        description: 'From email address for notifications'
      },
      {
        id: 'setting-22',
        category: 'notifications',
        key: 'emailFromName',
        value: 'IT Management System',
        description: 'From name for notification emails'
      },
      {
        id: 'setting-23',
        category: 'notifications',
        key: 'notificationFrequency',
        value: 'immediate',
        description: 'How often to send notification emails'
      },
      {
        id: 'setting-24',
        category: 'notifications',
        key: 'notificationSound',
        value: 'default',
        description: 'Sound to play for notifications'
      },
      {
        id: 'setting-25',
        category: 'notifications',
        key: 'notificationPriority',
        value: 'normal',
        description: 'Default priority level for notifications'
      },
      {
        id: 'setting-26',
        category: 'notifications',
        key: 'emailRetryAttempts',
        value: '3',
        description: 'Number of retry attempts for failed emails'
      },
      {
        id: 'setting-27',
        category: 'notifications',
        key: 'emailTimeout',
        value: '30',
        description: 'Timeout in seconds for email sending'
      },
      {
        id: 'setting-28',
        category: 'notifications',
        key: 'dailyEmailLimit',
        value: '1000',
        description: 'Maximum emails to send per day'
      },
      {
        id: 'setting-29',
        category: 'notifications',
        key: 'emailTemplate',
        value: 'default',
        description: 'Default email template for notifications'
      },
      {
        id: 'setting-30',
        category: 'notifications',
        key: 'emailSignature',
        value: 'Best regards,\nIT Support Team',
        description: 'Email signature for notification emails'
      },
      
      // Customization settings
      {
        id: 'setting-31',
        category: 'customization',
        key: 'applicationName',
        value: 'InfraSpine',
        description: 'The name of the application displayed in the interface'
      },
      {
        id: 'setting-32',
        category: 'customization',
        key: 'primaryColor',
        value: '#3B82F6',
        description: 'Primary color theme for the application'
      },
      {
        id: 'setting-33',
        category: 'customization',
        key: 'enableDarkMode',
        value: 'true',
        description: 'Allow users to switch to dark mode'
      },
      {
        id: 'setting-34',
        category: 'customization',
        key: 'companyLogo',
        value: '',
        description: 'URL or path to company logo'
      },
      {
        id: 'setting-35',
        category: 'customization',
        key: 'footerText',
        value: 'Powered by InfraSpine Technology',
        description: 'Custom text displayed in the application footer'
      },
      {
        id: 'setting-36',
        category: 'customization',
        key: 'enableCustomBranding',
        value: 'false',
        description: 'Enable custom branding and theming options'
      },
      
      // Integration settings
      {
        id: 'setting-37',
        category: 'integrations',
        key: 'enableActiveDirectory',
        value: 'false',
        description: 'Enable Active Directory integration for user authentication'
      },
      {
        id: 'setting-38',
        category: 'integrations',
        key: 'adServerUrl',
        value: 'ldap://your-ad-server.com',
        description: 'Active Directory server URL'
      },
      {
        id: 'setting-39',
        category: 'integrations',
        key: 'enableSlackIntegration',
        value: 'false',
        description: 'Enable Slack notifications and integrations'
      },
      {
        id: 'setting-40',
        category: 'integrations',
        key: 'slackWebhookUrl',
        value: '',
        description: 'Slack webhook URL for notifications'
      },
      {
        id: 'setting-41',
        category: 'integrations',
        key: 'enableTeamsIntegration',
        value: 'false',
        description: 'Enable Microsoft Teams integration'
      },
      {
        id: 'setting-26',
        category: 'integrations',
        key: 'teamsWebhookUrl',
        value: '',
        description: 'Microsoft Teams webhook URL'
      },
      
      // Performance settings
      {
        id: 'setting-27',
        category: 'performance',
        key: 'enableCaching',
        value: 'true',
        description: 'Enable application caching for better performance'
      },
      {
        id: 'setting-28',
        category: 'performance',
        key: 'cacheTimeout',
        value: '300',
        description: 'Cache timeout in seconds'
      },
      {
        id: 'setting-29',
        category: 'performance',
        key: 'enableCompression',
        value: 'true',
        description: 'Enable data compression for API responses'
      },
      {
        id: 'setting-30',
        category: 'performance',
        key: 'maxConcurrentUsers',
        value: '1000',
        description: 'Maximum number of concurrent users'
      },
      {
        id: 'setting-31',
        category: 'performance',
        key: 'enableLazyLoading',
        value: 'true',
        description: 'Enable lazy loading for better page performance'
      },
      
      // Audit settings
      {
        id: 'setting-32',
        category: 'audit',
        key: 'enableAuditLogging',
        value: 'true',
        description: 'Enable comprehensive audit logging'
      },
      {
        id: 'setting-33',
        category: 'audit',
        key: 'auditRetentionDays',
        value: '365',
        description: 'Number of days to retain audit logs'
      },
      {
        id: 'setting-34',
        category: 'audit',
        key: 'logUserActions',
        value: 'true',
        description: 'Log all user actions and changes'
      },
      {
        id: 'setting-35',
        category: 'audit',
        key: 'logSystemEvents',
        value: 'true',
        description: 'Log system events and errors'
      },
      {
        id: 'setting-36',
        category: 'audit',
        key: 'enableRealTimeMonitoring',
        value: 'false',
        description: 'Enable real-time system monitoring'
      },
      
      // API settings
      {
        id: 'setting-37',
        category: 'api',
        key: 'enableApiRateLimit',
        value: 'true',
        description: 'Enable API rate limiting'
      },
      {
        id: 'setting-38',
        category: 'api',
        key: 'apiRateLimit',
        value: '1000',
        description: 'API requests per hour per user'
      },
      {
        id: 'setting-39',
        category: 'api',
        key: 'enableApiLogging',
        value: 'true',
        description: 'Log all API requests and responses'
      },
      {
        id: 'setting-40',
        category: 'api',
        key: 'apiTimeout',
        value: '30',
        description: 'API request timeout in seconds'
      },
      {
        id: 'setting-41',
        category: 'api',
        key: 'enableCors',
        value: 'true',
        description: 'Enable Cross-Origin Resource Sharing (CORS)'
      }
    ];
    
    console.log('🔧 SystemSettings: Created default settings:', defaultSettings.length, 'items');
    setSettings(defaultSettings);
    localStorage.setItem('systemSettings', JSON.stringify(defaultSettings));
  };

  // Save settings to storage (or API in real app)
  const handleSaveSettings = () => {
    setIsSaving(true);
    
    // Simulate API call delay
    setTimeout(() => {
      // Update lastUpdated and updatedBy for changed settings
      const timestamp = new Date().toISOString();
      const updatedSettings = settings.map(setting => ({
        ...setting,
        lastUpdated: timestamp,
        updatedBy: user?.name || 'System'
      }));
      
      localStorage.setItem('systemSettings', JSON.stringify(updatedSettings));
      setSettings(updatedSettings);
      setIsChanged(false);
      setIsSaving(false);
      setSaveSuccess(true);
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    }, 800);
  };
  
  // Handle setting change
  const handleSettingChange = (id: string, value: string) => {
    setSettings(prev => prev.map(setting => 
      setting.id === id ? { ...setting, value } : setting
    ));
    setIsChanged(true);
  };
  
  // Get settings by category
  const getSettingsByCategory = (category: string) => {
    const filteredSettings = settings.filter(setting => setting.category === category);
    console.log(`🔧 SystemSettings: Getting settings for category '${category}':`, filteredSettings.length, 'items');
    console.log('🔧 SystemSettings: Filtered settings:', filteredSettings.map(s => ({ id: s.id, key: s.key, value: s.value })));
    return filteredSettings;
  };
  
  // Get icon for category
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'general': return <Cog className="h-5 w-5 text-blue-500" />;
      case 'security': return <Shield className="h-5 w-5 text-blue-500" />;
      case 'email': return <Mail className="h-5 w-5 text-blue-500" />;
      case 'backup': return <Database className="h-5 w-5 text-blue-500" />;
      case 'notifications': return <Bell className="h-5 w-5 text-blue-500" />;
      case 'customization': return <Palette className="h-5 w-5 text-blue-500" />;
      case 'integrations': return <Globe className="h-5 w-5 text-blue-500" />;
      case 'performance': return <Zap className="h-5 w-5 text-blue-500" />;
      case 'audit': return <FileSearch className="h-5 w-5 text-blue-500" />;
      case 'api': return <BarChart3 className="h-5 w-5 text-blue-500" />;
      default: return <Cog className="h-5 w-5 text-blue-500" />;
    }
  };
  
  // Convert key to display name
  const keyToDisplayName = (key: string) => {
    // Convert camelCase to Title Case with spaces
    return key
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase());
  };
  
  // Render appropriate input field based on setting
  const renderSettingInput = (setting: SystemSetting) => {
    // For boolean values
    if (setting.value === 'true' || setting.value === 'false') {
      return (
        <div className="flex items-center">
          <input
            type="checkbox"
            id={setting.id}
            checked={setting.value === 'true'}
            onChange={(e) => handleSettingChange(setting.id, e.target.checked ? 'true' : 'false')}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <label htmlFor={setting.id} className="ml-2 text-sm text-gray-600">
            {setting.description}
          </label>
        </div>
      );
    }
    
    // For select fields
    if (setting.key === 'defaultLanguage') {
      return (
        <select
          value={setting.value}
          onChange={(e) => handleSettingChange(setting.id, e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="en">English</option>
          <option value="es">Spanish</option>
          <option value="fr">French</option>
          <option value="de">German</option>
        </select>
      );
    }
    
    if (setting.key === 'timeZone') {
      return (
        <select
          value={setting.value}
          onChange={(e) => handleSettingChange(setting.id, e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="UTC">UTC</option>
          <option value="EST">Eastern Standard Time (EST)</option>
          <option value="CST">Central Standard Time (CST)</option>
          <option value="PST">Pacific Standard Time (PST)</option>
        </select>
      );
    }
    
    // For color picker
    if (setting.key === 'primaryColor') {
      return (
        <div className="flex items-center gap-3">
          <input
            type="color"
            value={setting.value}
            onChange={(e) => handleSettingChange(setting.id, e.target.value)}
            className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
          />
          <input
            type="text"
            value={setting.value}
            onChange={(e) => handleSettingChange(setting.id, e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="#3B82F6"
          />
        </div>
      );
    }
    
    if (setting.key === 'passwordPolicy') {
      return (
        <select
          value={setting.value}
          onChange={(e) => handleSettingChange(setting.id, e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="strong">Strong (8+ chars, uppercase, lowercase, number, special)</option>
          <option value="medium">Medium (8+ chars, at least one number)</option>
          <option value="basic">Basic (6+ chars)</option>
        </select>
      );
    }
    
    if (setting.key === 'backupFrequency') {
      return (
        <select
          value={setting.value}
          onChange={(e) => handleSettingChange(setting.id, e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="hourly">Hourly</option>
          <option value="daily">Daily</option>
          <option value="weekly">Weekly</option>
          <option value="monthly">Monthly</option>
        </select>
      );
    }
    
    // For URL inputs
    if (setting.key.includes('Url') || setting.key.includes('ServerUrl')) {
      return (
        <input
          type="url"
          value={setting.value}
          onChange={(e) => handleSettingChange(setting.id, e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="https://example.com"
        />
      );
    }
    
    // For cache timeout
    if (setting.key === 'cacheTimeout') {
      return (
        <div className="flex items-center gap-2">
          <input
            type="number"
            value={setting.value}
            onChange={(e) => handleSettingChange(setting.id, e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            min="60"
            max="3600"
          />
          <span className="text-sm text-gray-500">seconds</span>
        </div>
      );
    }
    
    // For API rate limit
    if (setting.key === 'apiRateLimit') {
      return (
        <div className="flex items-center gap-2">
          <input
            type="number"
            value={setting.value}
            onChange={(e) => handleSettingChange(setting.id, e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            min="100"
            max="10000"
          />
          <span className="text-sm text-gray-500">requests/hour</span>
        </div>
      );
    }
    
    // For retention days
    if (setting.key === 'auditRetentionDays' || setting.key === 'retentionPeriod') {
      return (
        <div className="flex items-center gap-2">
          <input
            type="number"
            value={setting.value}
            onChange={(e) => handleSettingChange(setting.id, e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            min="1"
            max="3650"
          />
          <span className="text-sm text-gray-500">days</span>
        </div>
      );
    }
    
    // For timeout settings
    if (setting.key.includes('Timeout') || setting.key === 'apiTimeout') {
      return (
        <div className="flex items-center gap-2">
          <input
            type="number"
            value={setting.value}
            onChange={(e) => handleSettingChange(setting.id, e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            min="5"
            max="300"
          />
          <span className="text-sm text-gray-500">seconds</span>
        </div>
      );
    }
    
    // For max concurrent users
    if (setting.key === 'maxConcurrentUsers') {
      return (
        <div className="flex items-center gap-2">
          <input
            type="number"
            value={setting.value}
            onChange={(e) => handleSettingChange(setting.id, e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            min="10"
            max="10000"
          />
          <span className="text-sm text-gray-500">users</span>
        </div>
      );
    }
    
    // For numeric values
    if (!isNaN(Number(setting.value))) {
      return (
        <input
          type="number"
          value={setting.value}
          onChange={(e) => handleSettingChange(setting.id, e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      );
    }
    
    // Default text input
    return (
      <input
        type="text"
        value={setting.value}
        onChange={(e) => handleSettingChange(setting.id, e.target.value)}
        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      />
    );
  };
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-bold text-gray-900">System Settings</h2>
          <p className="text-sm text-gray-600">Configure your system preferences and configuration</p>
        </div>
        
        {saveSuccess && (
          <div className="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg">
            Settings have been saved successfully.
          </div>
        )}
      </div>
        
      <div className="flex flex-col gap-6">
        {/* Tab Navigation */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-4 px-4" aria-label="Tabs">
              <button
                onClick={() => setActiveCategory('general')}
                className={`flex items-center gap-1 py-2 px-2 border-b-2 font-medium text-xs ${
                  activeCategory === 'general'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Cog className="h-3 w-3" />
                General
              </button>
              <button
                onClick={() => setActiveCategory('security')}
                className={`flex items-center gap-1 py-2 px-2 border-b-2 font-medium text-xs ${
                  activeCategory === 'security'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Shield className="h-3 w-3" />
                Security
              </button>
              <button
                onClick={() => setActiveCategory('email')}
                className={`flex items-center gap-1 py-2 px-2 border-b-2 font-medium text-xs ${
                  activeCategory === 'email'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Mail className="h-3 w-3" />
                Email
              </button>
              <button
                onClick={() => setActiveCategory('backup')}
                className={`flex items-center gap-1 py-2 px-2 border-b-2 font-medium text-xs ${
                  activeCategory === 'backup'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Database className="h-3 w-3" />
                Backup & Storage
              </button>
              <button
                onClick={() => setActiveCategory('notifications')}
                className={`flex items-center gap-1 py-2 px-2 border-b-2 font-medium text-xs ${
                  activeCategory === 'notifications'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Bell className="h-3 w-3" />
                Notifications
              </button>
              <button
                onClick={() => setActiveCategory('customization')}
                className={`flex items-center gap-1 py-2 px-2 border-b-2 font-medium text-xs ${
                  activeCategory === 'customization'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Palette className="h-3 w-3" />
                Customization
              </button>
              <button
                onClick={() => setActiveCategory('integrations')}
                className={`flex items-center gap-1 py-2 px-2 border-b-2 font-medium text-xs ${
                  activeCategory === 'integrations'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Globe className="h-3 w-3" />
                Integrations
              </button>
              <button
                onClick={() => setActiveCategory('performance')}
                className={`flex items-center gap-1 py-2 px-2 border-b-2 font-medium text-xs ${
                  activeCategory === 'performance'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Zap className="h-3 w-3" />
                Performance
              </button>

              <button
                onClick={() => setActiveCategory('api')}
                className={`flex items-center gap-1 py-2 px-2 border-b-2 font-medium text-xs ${
                  activeCategory === 'api'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <BarChart3 className="h-3 w-3" />
                API Settings
              </button>
            </nav>
          </div>
        </div>
        
        {/* Settings Content */}
        <div className="bg-white rounded-lg shadow-sm">
          {activeCategory === 'general' && (
            <>
              <GeneralSettings
                settings={settings}
                onSettingChange={handleSettingChange}
                onSave={handleSaveSettings}
                isSaving={isSaving}
                saveSuccess={saveSuccess}
              />
            </>
          )}
          {activeCategory === 'security' && (
            <SecuritySettings
              settings={settings}
              onSettingChange={handleSettingChange}
              onSave={handleSaveSettings}
              isSaving={isSaving}
              saveSuccess={saveSuccess}
            />
          )}
          {activeCategory === 'email' && (
            <EmailSettings
              settings={settings}
              onSettingChange={handleSettingChange}
              onSave={handleSaveSettings}
              isSaving={isSaving}
              saveSuccess={saveSuccess}
            />
          )}
          {activeCategory === 'backup' && (
            <BackupSettings
              settings={settings}
              onSettingChange={handleSettingChange}
              onSave={handleSaveSettings}
              isSaving={isSaving}
              saveSuccess={saveSuccess}
            />
          )}
          {activeCategory === 'notifications' && (
            <NotificationSettings
              settings={settings}
              onSettingChange={handleSettingChange}
              onSave={handleSaveSettings}
              isSaving={isSaving}
              saveSuccess={saveSuccess}
            />
          )}
          {activeCategory === 'customization' && (
            <CustomizationSettings
              settings={settings}
              onSettingChange={handleSettingChange}
              onSave={handleSaveSettings}
              isSaving={isSaving}
              saveSuccess={saveSuccess}
            />
          )}
          {activeCategory === 'integrations' && (
            <IntegrationSettings
              settings={settings}
              onSettingChange={handleSettingChange}
              onSave={handleSaveSettings}
              isSaving={isSaving}
              saveSuccess={saveSuccess}
            />
          )}
          {activeCategory === 'performance' && (
            <PerformanceSettings
              settings={settings}
              onSettingChange={handleSettingChange}
              onSave={handleSaveSettings}
              isSaving={isSaving}
              saveSuccess={saveSuccess}
            />
          )}

          {activeCategory === 'api' && (
            <APISettings
              settings={settings}
              onSettingChange={handleSettingChange}
              onSave={handleSaveSettings}
              isSaving={isSaving}
              saveSuccess={saveSuccess}
            />
          )}
        </div>
      </div>
    </div>
  );
};