import { UserRole } from '../types/common';
import { AppDataSource } from '../config/database';
import { RoleSyncService } from '../services/RoleSyncService';

async function syncRoles() {
  try {
    // Initialize database connection
    await AppDataSource.initialize();
    
    // Sync static roles to database
    await RoleSyncService.syncRoles();
    
    console.log('✅ Role synchronization completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during role synchronization:', error);
    process.exit(1);
  }
}

// Run the migration
syncRoles();
