import React, { useState } from 'react';
import CompanyProfileDisplay from '../components/CompanyProfileDisplay';
import CompanyProfileEdit from '../components/CompanyProfileEdit';
import CompanyTrademarkManagement from '../components/CompanyTrademarkManagement';
import TrademarkForm from '../components/TrademarkForm';
import TrademarkDetailView from '../components/TrademarkDetailView';
import { useNavigate } from 'react-router-dom';
import { Edit, ArrowLeft, Home, Shield } from 'lucide-react';

// Types for trademark management
interface Trademark {
  id: string;
  trademarkName: string;
  description?: string;
  trademarkType: 'word_mark' | 'design_mark' | 'combined_mark' | 'service_mark';
  status: 'pending' | 'registered' | 'opposed' | 'abandoned' | 'expired' | 'renewed';
  registrationNumber?: string;
  applicationNumber?: string;
  applicationDate?: string;
  registrationDate?: string;
  renewalDate?: string;
  expiryDate?: string;
  jurisdiction: string;
  registryOffice?: string;
  trademarkClasses: string[];
  goodsAndServices?: string;
  trademarkImageUrl?: string;
  attorney?: string;
  registrationFee?: number;
  renewalFee?: number;
  currency?: string;
  notes?: string;
  isActive: boolean;
  isPrimary: boolean;
  priority: number;
  createdAt: string;
  updatedAt: string;
}

const CompanyProfilePage: React.FC = () => {
  const navigate = useNavigate();
  const [isEditMode, setIsEditMode] = useState(false);
  const [activeTab, setActiveTab] = useState<'profile' | 'trademarks'>('profile');
  const [showTrademarkForm, setShowTrademarkForm] = useState(false);
  const [editingTrademark, setEditingTrademark] = useState<Trademark | null>(null);
  const [selectedTrademark, setSelectedTrademark] = useState<Trademark | null>(null);

  const handleSaveTrademark = (trademark: Trademark) => {
    // Here you would typically save to your backend
    console.log('Saving trademark:', trademark);
    setShowTrademarkForm(false);
    setEditingTrademark(null);
  };

  return (
    <div className="py-8 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
      <div className="mb-6 flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Company Profile & Trademarks</h1>
        <div className="flex space-x-3">
          <button
            onClick={() => navigate(-1)}
            className="flex items-center gap-1 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </button>
          <button
            onClick={() => navigate('/dashboard')}
            className="flex items-center gap-1 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <Home className="h-4 w-4" />
            Dashboard
          </button>
          {!isEditMode && activeTab === 'profile' && (
            <button
              onClick={() => setIsEditMode(true)}
              className="flex items-center gap-1 px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            >
              <Edit className="h-4 w-4" />
              Edit Profile
            </button>
          )}
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => {
                setActiveTab('profile');
                setIsEditMode(false);
              }}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'profile'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Company Profile
            </button>
            <button
              onClick={() => {
                setActiveTab('trademarks');
                setIsEditMode(false);
              }}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${
                activeTab === 'trademarks'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Shield className="h-4 w-4" />
              Trademarks
            </button>
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'profile' ? (
        isEditMode ? (
          <CompanyProfileEdit
            onCancel={() => setIsEditMode(false)}
            onSave={() => setIsEditMode(false)}
          />
        ) : (
          <CompanyProfileDisplay />
        )
      ) : (
        <CompanyTrademarkManagement />
      )}

      {/* Trademark Form Modal */}
      {showTrademarkForm && (
        <TrademarkForm
          trademark={editingTrademark}
          onSave={handleSaveTrademark}
          onCancel={() => {
            setShowTrademarkForm(false);
            setEditingTrademark(null);
          }}
        />
      )}

      {/* Trademark Detail View Modal */}
      {selectedTrademark && (
        <TrademarkDetailView
          trademark={selectedTrademark}
          onClose={() => setSelectedTrademark(null)}
          onEdit={() => {
            setEditingTrademark(selectedTrademark);
            setSelectedTrademark(null);
            setShowTrademarkForm(true);
          }}
        />
      )}
    </div>
  );
};

export default CompanyProfilePage; 