import { MigrationInterface, QueryRunner, Table, TableForeignKey } from "typeorm";

export class CreateAssetMaintenanceTable1710000000001 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
            new Table({
                name: "asset_maintenance",
                columns: [
                    {
                        name: "id",
                        type: "varchar",
                        length: "36",
                        isPrimary: true,
                        isGenerated: false
                    },
                    {
                        name: "assetId",
                        type: "varchar",
                        length: "36"
                    },
                    {
                        name: "maintenanceType",
                        type: "varchar",
                        length: "50"
                    },
                    {
                        name: "maintenanceDate",
                        type: "date"
                    },
                    {
                        name: "description",
                        type: "text"
                    },
                    {
                        name: "cost",
                        type: "decimal",
                        precision: 10,
                        scale: 2,
                        default: 0
                    },
                    {
                        name: "performedById",
                        type: "varchar",
                        length: "36",
                        isNullable: true
                    },
                    {
                        name: "vendor",
                        type: "varchar",
                        length: "100",
                        isNullable: true
                    },
                    {
                        name: "status",
                        type: "varchar",
                        length: "50",
                        default: "'Completed'"
                    },
                    {
                        name: "nextMaintenanceDate",
                        type: "date",
                        isNullable: true
                    },
                    {
                        name: "notes",
                        type: "text",
                        isNullable: true
                    },
                    {
                        name: "partsReplaced",
                        type: "json",
                        isNullable: true
                    },
                    {
                        name: "createdAt",
                        type: "timestamp",
                        default: "now()"
                    },
                    {
                        name: "updatedAt",
                        type: "timestamp",
                        default: "now()"
                    }
                ]
            }),
            true
        );

        // Add foreign key for assetId
        await queryRunner.createForeignKey(
            "asset_maintenance",
            new TableForeignKey({
                columnNames: ["assetId"],
                referencedColumnNames: ["id"],
                referencedTableName: "assets",
                onDelete: "CASCADE"
            })
        );

        // Add foreign key for performedById
        await queryRunner.createForeignKey(
            "asset_maintenance",
            new TableForeignKey({
                columnNames: ["performedById"],
                referencedColumnNames: ["id"],
                referencedTableName: "users",
                onDelete: "SET NULL"
            })
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const table = await queryRunner.getTable("asset_maintenance");
        
        if (table) {
            const assetForeignKey = table.foreignKeys.find(
                fk => fk.columnNames.indexOf("assetId") !== -1
            );
            
            if (assetForeignKey) {
                await queryRunner.dropForeignKey("asset_maintenance", assetForeignKey);
            }
            
            const performedByForeignKey = table.foreignKeys.find(
                fk => fk.columnNames.indexOf("performedById") !== -1
            );
            
            if (performedByForeignKey) {
                await queryRunner.dropForeignKey("asset_maintenance", performedByForeignKey);
            }
        }
        
        await queryRunner.dropTable("asset_maintenance");
    }
} 