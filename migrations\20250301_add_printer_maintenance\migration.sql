-- CreateTable
CREATE TABLE `printer_maintenance` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `service_date` DATE NOT NULL,
  `action_taken` TEXT NOT NULL,
  `parts_replaced` TEXT NULL,
  `technician_name` VARCHAR(100) NULL,
  `cost` DECIMAL(10,2) NOT NULL DEFAULT 0,
  `invoice_number` VARCHAR(50) NULL,
  `invoice_file` VARCHAR(255) NULL,
  `invoice_approval_status` ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
  `department` VARCHAR(100) NOT NULL,
  `location` VARCHAR(100) NULL,
  `assignee` VARCHAR(100) NULL,
  `added_by` VARCHAR(100) NOT NULL,
  `sent_to_finance` BOOLEAN NOT NULL DEFAULT false,
  `approved_by_it_director` VARCHAR(100) NULL,
  `asset_id` INT NOT NULL,
  `vendor_id` INT NOT NULL,
  `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `printer_maintenance` ADD CONSTRAINT `printer_maintenance_asset_id_fkey` FOREIGN KEY (`asset_id`) REFERENCES `assets`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `printer_maintenance` ADD CONSTRAINT `printer_maintenance_vendor_id_fkey` FOREIGN KEY (`vendor_id`) REFERENCES `vendors`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE; 