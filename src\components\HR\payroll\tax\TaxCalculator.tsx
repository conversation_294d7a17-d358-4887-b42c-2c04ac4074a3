import React, { useState, useEffect } from 'react';
import { Calculator, DollarSign, ArrowRight, Info, Check } from 'lucide-react';
import { TaxConfig, TaxSlab } from '../../../types/payroll';

const TaxCalculator: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [taxConfigs, setTaxConfigs] = useState<TaxConfig[]>([]);
  const [selectedConfigId, setSelectedConfigId] = useState<number | null>(null);
  const [annualIncome, setAnnualIncome] = useState<string>('');
  const [calculatedTax, setCalculatedTax] = useState<{
    totalTax: number;
    effectiveRate: number;
    breakdown: { 
      slabDescription: string; 
      taxAmount: number; 
      rate: number;
      incomeInSlab: number;
    }[];
  } | null>(null);

  // Load mock data
  useEffect(() => {
    setLoading(true);
    
    // Simulate API call to fetch tax configurations
    setTimeout(() => {
      const mockTaxConfigs: TaxConfig[] = [
        {
          id: 1,
          name: "Standard Tax Slabs 2023-2024",
          description: "Income tax slabs for salaried individuals for fiscal year 2023-2024",
          effectiveDate: "2023-07-01",
          expiryDate: "2024-06-30",
          isActive: true,
          country: "Pakistan",
          taxType: "income",
          taxSlabs: [
            {
              id: 1,
              minAmount: 0,
              maxAmount: 600000,
              rate: 0,
              additionalAmount: 0,
              description: "No tax up to Rs. 600,000"
            },
            {
              id: 2,
              minAmount: 600001,
              maxAmount: 1200000,
              rate: 2.5,
              additionalAmount: 0,
              description: "2.5% of amount exceeding Rs. 600,000"
            },
            {
              id: 3,
              minAmount: 1200001,
              maxAmount: 2400000,
              rate: 12.5,
              additionalAmount: 15000,
              description: "Rs. 15,000 + 12.5% of amount exceeding Rs. 1,200,000"
            },
            {
              id: 4,
              minAmount: 2400001,
              maxAmount: 3600000,
              rate: 20,
              additionalAmount: 165000,
              description: "Rs. 165,000 + 20% of amount exceeding Rs. 2,400,000"
            },
            {
              id: 5,
              minAmount: 3600001,
              maxAmount: 6000000,
              rate: 25,
              additionalAmount: 405000,
              description: "Rs. 405,000 + 25% of amount exceeding Rs. 3,600,000"
            },
            {
              id: 6,
              minAmount: 6000001,
              maxAmount: 12000000,
              rate: 32.5,
              additionalAmount: 1005000,
              description: "Rs. 1,005,000 + 32.5% of amount exceeding Rs. 6,000,000"
            },
            {
              id: 7,
              minAmount: 12000001,
              maxAmount: null,
              rate: 35,
              additionalAmount: 2955000,
              description: "Rs. 2,955,000 + 35% of amount exceeding Rs. 12,000,000"
            }
          ],
          createdAt: "2023-06-15"
        },
        {
          id: 2,
          name: "Business Income Tax 2023-2024",
          description: "Income tax slabs for business individuals for fiscal year 2023-2024",
          effectiveDate: "2023-07-01",
          expiryDate: "2024-06-30",
          isActive: true,
          country: "Pakistan",
          taxType: "income",
          taxSlabs: [
            {
              id: 1,
              minAmount: 0,
              maxAmount: 400000,
              rate: 0,
              additionalAmount: 0,
              description: "No tax up to Rs. 400,000"
            },
            {
              id: 2,
              minAmount: 400001,
              maxAmount: 800000,
              rate: 5,
              additionalAmount: 0,
              description: "5% of amount exceeding Rs. 400,000"
            },
            {
              id: 3,
              minAmount: 800001,
              maxAmount: 1200000,
              rate: 10,
              additionalAmount: 20000,
              description: "Rs. 20,000 + 10% of amount exceeding Rs. 800,000"
            },
            {
              id: 4,
              minAmount: 1200001,
              maxAmount: 2400000,
              rate: 15,
              additionalAmount: 60000,
              description: "Rs. 60,000 + 15% of amount exceeding Rs. 1,200,000"
            },
            {
              id: 5,
              minAmount: 2400001,
              maxAmount: 3000000,
              rate: 20,
              additionalAmount: 240000,
              description: "Rs. 240,000 + 20% of amount exceeding Rs. 2,400,000"
            },
            {
              id: 6,
              minAmount: 3000001,
              maxAmount: 5000000,
              rate: 25,
              additionalAmount: 360000,
              description: "Rs. 360,000 + 25% of amount exceeding Rs. 3,000,000"
            },
            {
              id: 7,
              minAmount: 5000001,
              maxAmount: null,
              rate: 30,
              additionalAmount: 860000,
              description: "Rs. 860,000 + 30% of amount exceeding Rs. 5,000,000"
            }
          ],
          createdAt: "2023-06-15"
        }
      ];
      
      setTaxConfigs(mockTaxConfigs);
      if (mockTaxConfigs.length > 0) {
        setSelectedConfigId(mockTaxConfigs[0].id);
      }
      setLoading(false);
    }, 1000);
  }, []);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'PKR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Calculate tax based on selected config and annual income
  const calculateTax = () => {
    if (!selectedConfigId || !annualIncome) return;
    
    const income = parseFloat(annualIncome);
    if (isNaN(income) || income < 0) return;
    
    const selectedConfig = taxConfigs.find(config => config.id === selectedConfigId);
    if (!selectedConfig) return;
    
    // Sort tax slabs by min amount (ascending)
    const sortedSlabs = [...selectedConfig.taxSlabs].sort((a, b) => a.minAmount - b.minAmount);
    
    let totalTax = 0;
    const breakdown: {
      slabDescription: string;
      taxAmount: number;
      rate: number;
      incomeInSlab: number;
    }[] = [];
    
    for (let i = 0; i < sortedSlabs.length; i++) {
      const currentSlab = sortedSlabs[i];
      const nextSlab = sortedSlabs[i + 1];
      
      // If income is less than min amount of this slab, no tax in this slab
      if (income < currentSlab.minAmount) continue;
      
      // Calculate the income that falls in this slab
      let incomeFallingInSlab: number;
      
      if (currentSlab.maxAmount === null || income <= currentSlab.maxAmount) {
        // If this is the highest slab or income is less than max of current slab
        incomeFallingInSlab = income - currentSlab.minAmount + 1;
      } else {
        // If income spans beyond this slab
        incomeFallingInSlab = currentSlab.maxAmount - currentSlab.minAmount + 1;
      }
      
      // Calculate tax for this slab
      let taxForSlab: number;
      
      if (i === 0 || currentSlab.additionalAmount === 0) {
        // First slab or no additional amount
        taxForSlab = (incomeFallingInSlab * currentSlab.rate) / 100;
      } else {
        // Additional amount + percentage of exceeding income
        if (income > currentSlab.minAmount) {
          const exceedingAmount = Math.min(income, currentSlab.maxAmount || Infinity) - currentSlab.minAmount + 1;
          taxForSlab = currentSlab.additionalAmount + (exceedingAmount * currentSlab.rate) / 100;
        } else {
          taxForSlab = currentSlab.additionalAmount;
        }
      }
      
      // Add to breakdown
      breakdown.push({
        slabDescription: currentSlab.description || `Income between ${formatCurrency(currentSlab.minAmount)} and ${currentSlab.maxAmount ? formatCurrency(currentSlab.maxAmount) : 'above'}`,
        taxAmount: taxForSlab,
        rate: currentSlab.rate,
        incomeInSlab: incomeFallingInSlab
      });
      
      totalTax += taxForSlab;
      
      // If we've accounted for all the income, break out of the loop
      if (currentSlab.maxAmount === null || income <= currentSlab.maxAmount) {
        break;
      }
    }
    
    // Calculate effective tax rate
    const effectiveRate = (totalTax / income) * 100;
    
    setCalculatedTax({
      totalTax,
      effectiveRate,
      breakdown
    });
  };

  const resetCalculator = () => {
    setAnnualIncome('');
    setCalculatedTax(null);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Calculator Inputs */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4 flex items-center">
          <Calculator className="h-5 w-5 mr-2 text-blue-500" />
          Tax Calculator
        </h3>
        
        <div className="space-y-4">
          <div>
            <label htmlFor="tax-config" className="block text-sm font-medium text-gray-700 mb-1">
              Tax Configuration
            </label>
            <select
              id="tax-config"
              value={selectedConfigId || ''}
              onChange={(e) => setSelectedConfigId(Number(e.target.value))}
              className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
            >
              {taxConfigs.map((config) => (
                <option key={config.id} value={config.id}>
                  {config.name}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label htmlFor="annual-income" className="block text-sm font-medium text-gray-700 mb-1">
              Annual Income (PKR)
            </label>
            <div className="relative rounded-md shadow-sm">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <DollarSign className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                id="annual-income"
                value={annualIncome}
                onChange={(e) => {
                  // Only allow numbers
                  const value = e.target.value.replace(/[^0-9]/g, '');
                  setAnnualIncome(value);
                }}
                className="focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 pr-12 sm:text-sm border-gray-300 rounded-md"
                placeholder="0"
              />
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <span className="text-gray-500 sm:text-sm">PKR</span>
              </div>
            </div>
          </div>
          
          <div className="pt-4 flex space-x-3">
            <button
              type="button"
              onClick={calculateTax}
              disabled={!selectedConfigId || !annualIncome}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              <Calculator className="h-4 w-4 mr-2" />
              Calculate Tax
            </button>
            
            <button
              type="button"
              onClick={resetCalculator}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Reset
            </button>
          </div>
        </div>
      </div>
      
      {/* Results */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
          Tax Calculation Results
        </h3>
        
        {calculatedTax ? (
          <div className="space-y-6">
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-blue-800">Total Tax</h4>
                  <p className="mt-1 text-2xl font-semibold text-blue-900">
                    {formatCurrency(calculatedTax.totalTax)}
                  </p>
                </div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-blue-800">Effective Tax Rate</h4>
                  <p className="mt-1 text-2xl font-semibold text-blue-900">
                    {calculatedTax.effectiveRate.toFixed(2)}%
                  </p>
                </div>
              </div>
              
              <div className="mt-3">
                <h4 className="text-sm font-medium text-blue-800">Annual Income</h4>
                <p className="mt-1 text-lg font-medium text-blue-900">
                  {formatCurrency(parseFloat(annualIncome))}
                </p>
              </div>
            </div>
            
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">Breakdown by Tax Slabs</h4>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Slab Description
                      </th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Income in Slab
                      </th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Rate
                      </th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tax Amount
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {calculatedTax.breakdown.map((item, index) => (
                      <tr key={index}>
                        <td className="px-4 py-2 text-sm text-gray-700">
                          {item.slabDescription}
                        </td>
                        <td className="px-4 py-2 text-sm text-gray-700">
                          {formatCurrency(item.incomeInSlab)}
                        </td>
                        <td className="px-4 py-2 text-sm text-gray-700">
                          {item.rate}%
                        </td>
                        <td className="px-4 py-2 text-sm text-gray-700">
                          {formatCurrency(item.taxAmount)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot>
                    <tr className="bg-gray-50">
                      <td colSpan={3} className="px-4 py-2 text-sm font-medium text-gray-900 text-right">
                        Total Tax
                      </td>
                      <td className="px-4 py-2 text-sm font-medium text-gray-900">
                        {formatCurrency(calculatedTax.totalTax)}
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-64 text-center">
            <Info className="h-12 w-12 text-gray-300 mb-3" />
            <h4 className="text-sm font-medium text-gray-900">No calculation results yet</h4>
            <p className="mt-1 text-sm text-gray-500">
              Enter income amount and click "Calculate Tax" to see the results.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TaxCalculator; 