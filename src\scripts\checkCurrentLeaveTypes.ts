import { AppDataSource } from '../config/database';
import { LeaveTypePolicy } from '../entities/LeaveTypePolicy';
import { LeavePolicyConfiguration } from '../entities/LeavePolicyConfiguration';

async function checkCurrentLeaveTypes() {
  try {
    console.log('🔍 Checking current leave types in database...');
    
    await AppDataSource.initialize();
    console.log('✅ Database connected');
    
    const leaveTypePolicyRepository = AppDataSource.getRepository(LeaveTypePolicy);
    const leavePolicyConfigRepository = AppDataSource.getRepository(LeavePolicyConfiguration);
    
    // Check active policy configurations
    const activePolicyConfigs = await leavePolicyConfigRepository.find({
      where: { isActive: true }
    });
    console.log(`\n📋 Found ${activePolicyConfigs.length} active policy configurations`);
    
    // Check all leave type policies
    const allLeaveTypes = await leaveTypePolicyRepository.find({
      order: { sortOrder: 'ASC', leaveType: 'ASC' }
    });
    
    console.log(`\n📊 Total leave types in database: ${allLeaveTypes.length}`);
    
    if (allLeaveTypes.length === 0) {
      console.log('❌ No leave types found in database!');
      return;
    }
    
    console.log('\n📝 Current leave types:');
    console.log('═'.repeat(80));
    console.log('ID | Type               | Display Name        | Days | Active | Config ID');
    console.log('─'.repeat(80));
    
    for (const leaveType of allLeaveTypes) {
      const status = leaveType.isActive ? '✅' : '❌';
      console.log(
        `${leaveType.id.toString().padEnd(2)} | ` +
        `${leaveType.leaveType.padEnd(18)} | ` +
        `${leaveType.displayName.padEnd(19)} | ` +
        `${leaveType.maxDaysPerYear.toString().padEnd(4)} | ` +
        `${status.padEnd(6)} | ` +
        `${leaveType.policyConfigurationId}`
      );
    }
    
    console.log('─'.repeat(80));
    
    // Check only active leave types
    const activeLeaveTypes = allLeaveTypes.filter(lt => lt.isActive);
    console.log(`\n✨ Active leave types: ${activeLeaveTypes.length}`);
    
    if (activeLeaveTypes.length > 0) {
      console.log('\n🎯 Active leave types details:');
      for (const leaveType of activeLeaveTypes) {
        console.log(`\n📋 ${leaveType.leaveType} (${leaveType.displayName})`);
        console.log(`   📊 Max days per year: ${leaveType.maxDaysPerYear}`);
        console.log(`   📅 Category: ${leaveType.category || 'N/A'}`);
        console.log(`   🏢 Policy Config ID: ${leaveType.policyConfigurationId}`);
        console.log(`   📝 Description: ${leaveType.description || 'N/A'}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error checking leave types:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
    }
  }
}

// Run the check if this script is executed directly
if (require.main === module) {
  checkCurrentLeaveTypes();
}

export { checkCurrentLeaveTypes }; 