import React, { useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  flexRender,
  SortingState,
  ColumnDef,
} from '@tanstack/react-table';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { AssetFrontend } from './AssetManagement';

interface AssetTableProps {
  data: AssetFrontend[];
  columns: any[];
  currentPage: number;
  setCurrentPage: (page: number) => void;
  itemsPerPage: number;
  setItemsPerPage: (pageSize: number) => void;
}

const AssetTable: React.FC<AssetTableProps> = ({
  data,
  columns,
  currentPage,
  setCurrentPage,
  itemsPerPage,
  setItemsPerPage,
}) => {
  const [sorting, setSorting] = React.useState<SortingState>([]);

  // Filter out empty rows
  const filteredData = useMemo(() => {
    return data.filter(row => {
      // Check if the row has any meaningful data
      return row.id || row.assetType || row.manufacturer || row.model || row.serialNumber;
    });
  }, [data]);

  // Convert your existing columns to TanStack Table format
  const tableColumns = useMemo<ColumnDef<AssetFrontend>[]>(
    () =>
      columns.map((col) => ({
        id: col.accessorKey || col.id,
        accessorKey: col.accessorKey,
        header: () => col.header,
        cell: col.cell
          ? ({ row, table }) => col.cell({ 
              getValue: () => row.getValue(col.accessorKey || col.id), 
              row: { 
                original: row.original, 
                index: row.index 
              },
              table
            })
          : ({ getValue }) => String(getValue() || ''),
      })),
    [columns]
  );

  const table = useReactTable({
    data: filteredData,
    columns: tableColumns,
    state: {
      sorting,
      pagination: {
        pageIndex: currentPage - 1,
        pageSize: itemsPerPage,
      },
    },
    onSortingChange: setSorting,
    onPaginationChange: (updater) => {
      if (typeof updater === 'function') {
        const newPagination = updater({ 
          pageIndex: currentPage - 1, 
          pageSize: itemsPerPage 
        });
        setCurrentPage(newPagination.pageIndex + 1);
        setItemsPerPage(newPagination.pageSize);
      } else {
        setCurrentPage(updater.pageIndex + 1);
        setItemsPerPage(updater.pageSize);
      }
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: false,
  });

  return (
    <div className="w-full">
      <div className="overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full table-fixed border border-gray-300">
            <thead>
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id} className="bg-gray-50 border-b border-gray-300">
                  {headerGroup.headers.map((header) => (
                    <th
                      key={header.id}
                      scope="col"
                      className="px-3 py-3.5 text-center text-xs font-bold text-gray-900 uppercase tracking-wider border-x border-gray-300 bg-gray-100"
                      onClick={header.column.getToggleSortingHandler()}
                      style={{ cursor: header.column.getCanSort() ? 'pointer' : 'default' }}
                    >
                      <div className="flex items-center justify-center">
                        <span className="font-bold">{flexRender(header.column.columnDef.header, header.getContext())}</span>
                        {header.column.getCanSort() && (
                          <span className="ml-1 inline-block">
                            {header.column.getIsSorted() ? (
                              header.column.getIsSorted() === 'asc' ? (
                                <ChevronUp className="w-4 h-4 inline" />
                              ) : (
                                <ChevronDown className="w-4 h-4 inline" />
                              )
                            ) : (
                              <span className="opacity-0 group-hover:opacity-50">⇅</span>
                            )}
                          </span>
                        )}
                      </div>
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody className="bg-white divide-y divide-gray-300">
              {table.getRowModel().rows.length === 0 ? (
                <tr>
                  <td
                    colSpan={columns.length}
                    className="px-3 py-4 text-center text-gray-500 border-x border-gray-300"
                  >
                    No assets found. Try adjusting your filters or add a new asset.
                  </td>
                </tr>
              ) : (
                table.getRowModel().rows.map((row) => (
                  <tr key={row.id}>
                    {row.getVisibleCells().map((cell) => {
                      // Special handling for S.NO column
                      if (cell.column.id === 'serialNo') {
                        // Simply use row index + 1 for S.NO, regardless of pagination
                        const allRows = table.getCoreRowModel().rows;
                        const rowIndex = allRows.findIndex(r => r.id === row.id);
                        const serialNo = rowIndex + 1;
                        return (
                          <td key={cell.id} className="px-3 py-4 text-sm text-center border-x border-gray-300">
                            {serialNo}
                          </td>
                        );
                      }
                      return (
                        <td
                          key={cell.id}
                          className="px-3 py-4 text-sm text-center border-x border-gray-300"
                        >
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </td>
                      );
                    })}
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {data.length > 0 && (
        <div className="mt-4 flex flex-col sm:flex-row items-center justify-between gap-3 border-t border-gray-200 bg-white px-4 py-3">
          <div className="flex items-center">
            <span className="text-sm text-gray-700">
              Showing{' '}
              <span className="font-medium">
                {filteredData.length === 0
                  ? 0
                  : ((currentPage - 1) * itemsPerPage) + 1}
              </span>{' '}
              to{' '}
              <span className="font-medium">
                {Math.min(
                  currentPage * itemsPerPage,
                  filteredData.length
                )}
              </span>{' '}
              of <span className="font-medium">{filteredData.length}</span> results
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <select
              value={itemsPerPage}
              onChange={(e) => {
                const newPageSize = Number(e.target.value);
                // First update the table's page size
                table.setPageSize(newPageSize);
                // Then reset to first page
                table.setPageIndex(0);
                // Finally update parent component state
                setItemsPerPage(newPageSize);
                setCurrentPage(1);
              }}
              className="rounded-md border border-gray-300 py-1 px-2 text-sm"
            >
              {[10, 20, 50].map((pageSize) => (
                <option key={pageSize} value={pageSize}>
                  {pageSize} per page
                </option>
              ))}
            </select>

            <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
              <button
                onClick={() => {
                  const newPageIndex = table.getState().pagination.pageIndex - 1;
                  table.setPageIndex(newPageIndex);
                  setCurrentPage(newPageIndex + 1);
                }}
                disabled={!table.getCanPreviousPage()}
                className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:bg-gray-100"
              >
                <span className="sr-only">Previous</span>
                &laquo;
              </button>

              {Array.from(
                { length: table.getPageCount() },
                (_, i) => {
                  const pageNumber = i + 1;
                  const isCurrent = table.getState().pagination.pageIndex === i;
                  
                  return (
                    <button
                      key={i}
                      onClick={() => {
                        table.setPageIndex(i);
                        setCurrentPage(i + 1);
                      }}
                      className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${
                        isCurrent
                          ? 'bg-blue-600 text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600'
                          : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0'
                      }`}
                    >
                      {pageNumber}
                    </button>
                  );
                }
              )}

              <button
                onClick={() => {
                  const newPageIndex = table.getState().pagination.pageIndex + 1;
                  table.setPageIndex(newPageIndex);
                  setCurrentPage(newPageIndex + 1);
                }}
                disabled={!table.getCanNextPage()}
                className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:bg-gray-100"
              >
                <span className="sr-only">Next</span>
                &raquo;
              </button>
            </nav>
          </div>
        </div>
      )}
    </div>
  );
};

export default AssetTable; 