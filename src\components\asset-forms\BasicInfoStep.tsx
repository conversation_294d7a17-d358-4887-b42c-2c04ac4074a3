import React from 'react';
import { Asset } from '../../types/asset';

interface BasicInfoStepProps {
  formData: Partial<Asset>;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  errors: Record<string, string>;
}

export const BasicInfoStep: React.FC<BasicInfoStepProps> = ({
  formData,
  onChange,
  errors,
}) => {
  const inputClassName = "w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors";
  const labelClassName = "block text-sm font-medium text-gray-700 mb-1";

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <label htmlFor="assetType" className={labelClassName}>Asset Type</label>
        <select
          id="assetType"
          name="assetType"
          value={formData.assetType || ''}
          onChange={onChange}
          className={inputClassName}
        >
          <option value="">Select Asset Type</option>
          <option value="Computing">Computing</option>
          <option value="Mobile Device">Mobile Device</option>
          <option value="Tablet">Tablet</option>
          <option value="Networking">Networking</option>
          <option value="Peripherals">Peripherals</option>
          <option value="Security">Security Equipment</option>
          <option value="Communication">Communication Equipment</option>
          <option value="Other">Other</option>
        </select>
        {errors.assetType && (
          <p className="mt-1 text-sm text-red-500">{errors.assetType}</p>
        )}
      </div>

      <div>
        <label htmlFor="category" className={labelClassName}>Category</label>
        <input
          type="text"
          id="category"
          name="category"
          value={formData.category || ''}
          onChange={onChange}
          className={inputClassName}
        />
        {errors.category && (
          <p className="mt-1 text-sm text-red-500">{errors.category}</p>
        )}
      </div>

      <div>
        <label htmlFor="manufacturer" className={labelClassName}>Manufacturer</label>
        <select
          id="manufacturer"
          name="manufacturer"
          value={formData.manufacturer || ''}
          onChange={onChange}
          className={inputClassName}
        >
          <option value="">Select Manufacturer</option>
          {formData.assetType === 'Computing' && (
            <>
              <option value="Dell">Dell</option>
              <option value="HP">HP</option>
              <option value="Lenovo">Lenovo</option>
              <option value="Apple">Apple</option>
              <option value="Asus">Asus</option>
              <option value="Microsoft">Microsoft</option>
              <option value="Other">Other</option>
            </>
          )}
          {/* Add other manufacturer options based on asset type */}
        </select>
        {errors.manufacturer && (
          <p className="mt-1 text-sm text-red-500">{errors.manufacturer}</p>
        )}
      </div>

      {/* Add other basic info fields */}
    </div>
  );
}; 