import React from 'react';
import { Doughnut } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import { ChartData } from '../../types/dashboard';

ChartJS.register(ArcElement, Tooltip, Legend);

interface ResponseTimeChartProps {
  data: ChartData;
}

export const ResponseTimeChart: React.FC<ResponseTimeChartProps> = ({ data }) => {
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
      },
      title: {
        display: true,
        text: 'Response Time Distribution',
      },
    },
    cutout: '60%',
  };

  return (
    <div style={{ height: '300px', width: '100%' }}>
      <Doughnut data={data} options={options} />
    </div>
  );
}; 