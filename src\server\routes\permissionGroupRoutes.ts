import { Router } from 'express';
import { 
  getPermissionGroups, 
  getPermissionGroupById, 
  createPermissionGroup, 
  updatePermissionGroup, 
  deletePermissionGroup
} from '../../controllers/permissionGroupController';
import { auth, authorize } from '../middleware/auth';
import { UserRole } from '../../types/common';
import rateLimit from 'express-rate-limit';

const router = Router();

// Configure rate limiting
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});

// Apply rate limiting to all permission group routes
router.use(apiLimiter);

// Add a fallback route that works even if database is down
router.get('/fallback', (req, res) => {
  const mockPermissionGroups = [
    {
      id: 'group-admin',
      name: 'Administrator Functions',
      description: 'Core system administration permissions',
      permissions: [
        'canConfigureSystem', 'canManageRoles', 'canAddUsers', 'canEditUsers', 
        'canDeleteUsers', 'canViewReports', 'canExportData', 'canImportData'
      ],
      createdAt: new Date().toISOString(),
      isCustom: false
    },
    {
      id: 'group-it-admin',
      name: 'IT Administrator',
      description: 'Full access across all system modules',
      permissions: [
        'canAccessAllModules', 'canConfigureSystem', 'canManageRoles', 
        'canAddUsers', 'canEditUsers', 'canDeleteUsers', 'canViewReports', 
        'canExportData', 'canImportData', 'canViewAllDepartments',
        'canCreateTickets', 'canEditTickets', 'canDeleteTickets', 'canCloseTickets',
        'canCreateEmployee', 'canEditEmployee', 'canDeleteEmployee', 'canViewEmployees'
      ],
      createdAt: new Date().toISOString(),
      isCustom: false
    },
    {
      id: 'group-service',
      name: 'Service Desk',
      description: 'Permissions for service desk management',
      permissions: [
        'canCreateTickets', 'canEditTickets', 'canCloseTickets', 'canAssignTickets', 
        'canEscalateTickets', 'canViewAllTickets'
      ],
      createdAt: new Date().toISOString(),
      isCustom: false
    },
    {
      id: 'group-dashboard',
      name: 'Dashboard Management',
      description: 'Permissions for dashboard creation and management',
      permissions: [
        'canViewDashboards', 'canViewAllDashboards', 'canCreateDashboards',
        'canEditDashboards', 'canDeleteDashboards', 'canShareDashboards'
      ],
      createdAt: new Date().toISOString(),
      isCustom: false
    }
  ];
  
  res.json({ permissionGroups: mockPermissionGroups });
});

// Get all permission groups
router.get('/',
  auth,
  authorize([UserRole.IT_ADMIN]),
  getPermissionGroups
);

// Get permission group by ID
router.get('/:id',
  auth,
  authorize([UserRole.IT_ADMIN]),
  getPermissionGroupById
);

// Create new permission group
router.post('/',
  auth,
  authorize([UserRole.IT_ADMIN]),
  createPermissionGroup
);

// Update permission group
router.put('/:id',
  auth,
  authorize([UserRole.IT_ADMIN]),
  updatePermissionGroup
);

// Delete permission group
router.delete('/:id',
  auth,
  authorize([UserRole.IT_ADMIN]),
  deletePermissionGroup
);

export default router; 