import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateHolidayTablesNew1748945006778 implements MigrationInterface {
  name = 'CreateHolidayTablesNew1748945006778';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create holidays table
    await queryRunner.createTable(
      new Table({
        name: 'holidays',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '100',
            isNullable: false,
          },
          {
            name: 'date',
            type: 'date',
            isNullable: false,
          },
          {
            name: 'type',
            type: 'enum',
            enum: ['public', 'company', 'optional'],
            default: "'public'",
            isNullable: false,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true,
            isNullable: false,
          },
          {
            name: 'createdBy',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'updatedBy',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'metadata',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
        ],
      }),
      true,
    );

    // Create holiday_configurations table
    await queryRunner.createTable(
      new Table({
        name: 'holiday_configurations',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'organizationId',
            type: 'varchar',
            length: '100',
            default: "'default'",
            isNullable: false,
          },
          {
            name: 'name',
            type: 'varchar',
            length: '100',
            default: "'Global Configuration'",
            isNullable: false,
          },
          {
            name: 'weekendDays',
            type: 'json',
            isNullable: false,
          },
          {
            name: 'timezone',
            type: 'varchar',
            length: '50',
            default: "'UTC'",
            isNullable: true,
          },
          {
            name: 'workingHours',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'holidaySettings',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'applicableRegions',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'applicableDepartments',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true,
            isNullable: false,
          },
          {
            name: 'createdBy',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'updatedBy',
            type: 'varchar',
            length: '100',
            isNullable: true,
          },
          {
            name: 'notes',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
        ],
      }),
      true,
    );

    // Create indexes for holidays table
    await queryRunner.createIndex('holidays', new TableIndex({
      name: 'IDX_holidays_date',
      columnNames: ['date']
    }));

    await queryRunner.createIndex('holidays', new TableIndex({
      name: 'IDX_holidays_type',
      columnNames: ['type']
    }));

    await queryRunner.createIndex('holidays', new TableIndex({
      name: 'IDX_holidays_isActive',
      columnNames: ['isActive']
    }));

    await queryRunner.createIndex('holidays', new TableIndex({
      name: 'IDX_holidays_date_type',
      columnNames: ['date', 'type']
    }));

    // Create indexes for holiday_configurations table
    await queryRunner.createIndex('holiday_configurations', new TableIndex({
      name: 'IDX_holiday_configurations_organizationId',
      columnNames: ['organizationId']
    }));

    await queryRunner.createIndex('holiday_configurations', new TableIndex({
      name: 'IDX_holiday_configurations_isActive',
      columnNames: ['isActive']
    }));

    // Insert default holiday configuration
    await queryRunner.query(`
      INSERT INTO holiday_configurations (organizationId, name, weekendDays, timezone, isActive, notes, createdAt, updatedAt)
      VALUES (
        'default',
        'Default Organization Configuration',
        '[0, 6]',
        'UTC',
        true,
        'Default weekend configuration: Sunday (0) and Saturday (6)',
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
      )
    `);

    // Insert comprehensive sample holidays including religious and cultural observances
    await queryRunner.query(`
      INSERT INTO holidays (name, date, type, description, isActive, createdAt, updatedAt) VALUES
      -- Public/National Holidays
      ('New Year''s Day', '2024-01-01', 'public', 'First day of the year', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Independence Day', '2024-07-04', 'public', 'National Independence Day', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Christmas Day', '2024-12-25', 'public', 'Christian holiday celebrating the birth of Jesus Christ', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      
      -- Company Holidays
      ('Company Foundation Day', '2024-03-15', 'company', 'Anniversary of company establishment', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Annual Team Retreat', '2024-09-15', 'company', 'Company-wide team building and planning event', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      
      -- Islamic Holidays
      ('Eid al-Fitr Day 1', '2024-04-10', 'optional', 'End of Ramadan, Festival of Breaking the Fast', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Eid al-Fitr Day 2', '2024-04-11', 'optional', 'Second day of Eid al-Fitr celebration', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Eid al-Fitr Day 3', '2024-04-12', 'optional', 'Third day of Eid al-Fitr celebration', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Eid al-Adha Day 1', '2024-06-17', 'optional', 'Festival of Sacrifice, commemorating Ibrahim''s willingness to sacrifice his son', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Eid al-Adha Day 2', '2024-06-18', 'optional', 'Second day of Eid al-Adha celebration', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Eid al-Adha Day 3', '2024-06-19', 'optional', 'Third day of Eid al-Adha celebration', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Islamic New Year', '2024-07-07', 'optional', 'First day of Muharram, Islamic New Year', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Day of Ashura', '2024-07-16', 'optional', 'Day of remembrance in Islam, 10th day of Muharram', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Mawlid an-Nabi', '2024-09-16', 'optional', 'Birthday of Prophet Muhammad', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      
      -- Hindu Holidays
      ('Diwali', '2024-11-01', 'optional', 'Festival of Lights, Hindu New Year celebration', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Holi', '2024-03-25', 'optional', 'Festival of Colors, Hindu spring festival', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Dussehra', '2024-10-12', 'optional', 'Victory of good over evil, Hindu festival', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Karva Chauth', '2024-10-20', 'optional', 'Hindu festival of love and togetherness', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      
      -- Buddhist Holidays
      ('Buddha Purnima', '2024-05-23', 'optional', 'Birthday of Gautama Buddha', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      
      -- Christian Holidays
      ('Good Friday', '2024-03-29', 'optional', 'Commemoration of the crucifixion of Jesus Christ', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Easter Sunday', '2024-03-31', 'optional', 'Resurrection of Jesus Christ', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Easter Monday', '2024-04-01', 'optional', 'Day after Easter Sunday', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      
      -- Jewish Holidays
      ('Rosh Hashanah Day 1', '2024-09-16', 'optional', 'Jewish New Year, first day', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Rosh Hashanah Day 2', '2024-09-17', 'optional', 'Jewish New Year, second day', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Yom Kippur', '2024-09-25', 'optional', 'Day of Atonement, holiest day in Judaism', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Passover Day 1', '2024-04-23', 'optional', 'Jewish festival commemorating exodus from Egypt', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      
      -- Sikh Holidays
      ('Guru Nanak Jayanti', '2024-11-15', 'optional', 'Birthday of Guru Nanak, founder of Sikhism', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Baisakhi', '2024-04-13', 'optional', 'Sikh New Year and harvest festival', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      
      -- Cultural and Regional Holidays
      ('Chinese New Year', '2024-02-10', 'optional', 'Traditional Chinese New Year celebration', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Dragon Boat Festival', '2024-06-10', 'optional', 'Traditional Chinese festival', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('Mid-Autumn Festival', '2024-09-17', 'optional', 'Traditional Chinese harvest festival', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      
      -- International Observances
      ('International Workers'' Day', '2024-05-01', 'optional', 'Labor Day, celebrated worldwide', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('World Health Day', '2024-04-07', 'optional', 'WHO global health awareness day', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
      ('International Women''s Day', '2024-03-08', 'optional', 'Celebrating women''s achievements globally', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.dropIndex('holidays', 'IDX_holidays_date');
    await queryRunner.dropIndex('holidays', 'IDX_holidays_type');
    await queryRunner.dropIndex('holidays', 'IDX_holidays_isActive');
    await queryRunner.dropIndex('holidays', 'IDX_holidays_date_type');
    await queryRunner.dropIndex('holiday_configurations', 'IDX_holiday_configurations_organizationId');
    await queryRunner.dropIndex('holiday_configurations', 'IDX_holiday_configurations_isActive');

    // Drop tables
    await queryRunner.dropTable('holidays');
    await queryRunner.dropTable('holiday_configurations');
  }
}
