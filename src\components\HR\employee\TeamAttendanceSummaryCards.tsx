import React from 'react';
import { Check<PERSON>ir<PERSON>, Clock, AlertCircle, Home } from 'lucide-react';

interface TeamAttendanceSummary {
  presentDays: number;
  presentPercent: number;
  lateDays: number;
  latePercent: number;
  absentDays: number;
  absentPercent: number;
  workHours: number;
  overTime: number;
  remoteWork: number;
  remotePercent: number;
}

interface TeamAttendanceSummaryCardsProps {
  summary: TeamAttendanceSummary;
}

const TeamAttendanceSummaryCards: React.FC<TeamAttendanceSummaryCardsProps> = ({ summary }) => {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-2 mb-4">
      {/* Present Days */}
      <div className="bg-white rounded-lg shadow-sm p-3 border-l-4 border-blue-500 min-h-[78px] flex flex-col justify-between">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-xs font-medium text-gray-500">Present Days</p>
            <div className="flex items-baseline mt-1">
              <p className="text-lg font-bold text-gray-900">{summary.presentDays}</p>
              <p className="ml-1 text-xs text-green-500">{summary.presentPercent}%</p>
            </div>
          </div>
          <div className="h-8 w-8 rounded-full bg-blue-50 flex items-center justify-center">
            <CheckCircle className="h-4 w-4 text-blue-500" />
          </div>
        </div>
      </div>
      {/* Late Days */}
      <div className="bg-white rounded-lg shadow-sm p-3 border-l-4 border-yellow-500 min-h-[78px] flex flex-col justify-between">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-xs font-medium text-gray-500">Late Days</p>
            <div className="flex items-baseline mt-1">
              <p className="text-lg font-bold text-gray-900">{summary.lateDays}</p>
              <p className="ml-1 text-xs text-yellow-500">{summary.latePercent}%</p>
            </div>
          </div>
          <div className="h-8 w-8 rounded-full bg-yellow-50 flex items-center justify-center">
            <Clock className="h-4 w-4 text-yellow-500" />
          </div>
        </div>
      </div>
      {/* Absent Days */}
      <div className="bg-white rounded-lg shadow-sm p-3 border-l-4 border-red-500 min-h-[78px] flex flex-col justify-between">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-xs font-medium text-gray-500">Absent Days</p>
            <div className="flex items-baseline mt-1">
              <p className="text-lg font-bold text-gray-900">{summary.absentDays}</p>
              <p className="ml-1 text-xs text-red-500">{summary.absentPercent}%</p>
            </div>
          </div>
          <div className="h-8 w-8 rounded-full bg-red-50 flex items-center justify-center">
            <AlertCircle className="h-4 w-4 text-red-500" />
          </div>
        </div>
      </div>
      {/* Work Hours */}
      <div className="bg-white rounded-lg shadow-sm p-3 border-l-4 border-green-500 min-h-[78px] flex flex-col justify-between">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-xs font-medium text-gray-500">Work Hours</p>
            <div className="flex items-baseline mt-1">
              <p className="text-lg font-bold text-gray-900">{summary.workHours.toFixed(1)}</p>
              <p className="ml-1 text-xs text-gray-500">total</p>
            </div>
          </div>
          <div className="h-8 w-8 rounded-full bg-green-50 flex items-center justify-center">
            <Clock className="h-4 w-4 text-green-500" />
          </div>
        </div>
      </div>
      {/* Over Time */}
      <div className="bg-white rounded-lg shadow-sm p-3 border-l-4 border-orange-500 min-h-[78px] flex flex-col justify-between">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-xs font-medium text-gray-500">Over Time</p>
            <div className="flex items-baseline mt-1">
              <p className="text-lg font-bold text-gray-900">{summary.overTime.toFixed(1)}</p>
              <p className="ml-1 text-xs text-orange-500">hours</p>
            </div>
          </div>
          <div className="h-8 w-8 rounded-full bg-orange-50 flex items-center justify-center">
            <Clock className="h-4 w-4 text-orange-500" />
          </div>
        </div>
      </div>
      {/* Remote Work */}
      <div className="bg-white rounded-lg shadow-sm p-3 border-l-4 border-purple-500 min-h-[78px] flex flex-col justify-between">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-xs font-medium text-gray-500">Remote Work</p>
            <div className="flex items-baseline mt-1">
              <p className="text-lg font-bold text-gray-900">{summary.remoteWork}</p>
              <p className="ml-1 text-xs text-purple-500">{summary.remotePercent}%</p>
            </div>
          </div>
          <div className="h-8 w-8 rounded-full bg-purple-50 flex items-center justify-center">
            <Home className="h-4 w-4 text-purple-500" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeamAttendanceSummaryCards;
