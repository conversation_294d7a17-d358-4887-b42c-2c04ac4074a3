import React, { useState, useEffect } from 'react';
import { X, Plus, MapPin, Building, Home, Trash2, Edit2, Save } from 'lucide-react';
import locationService from '../../../services/LocationService';

interface LocationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLocationAdded: (location: string) => void;
  employees?: any[];
}

interface LocationItem {
  id: string;
  name: string;
  type: 'office' | 'remote' | 'site';
  isEditing?: boolean;
}

const LocationModal: React.FC<LocationModalProps> = ({
  isOpen,
  onClose,
  onLocationAdded,
  employees = []
}) => {
  const [locations, setLocations] = useState<LocationItem[]>([]);
  const [newLocation, setNewLocation] = useState('');
  const [newLocationType, setNewLocationType] = useState<'office' | 'remote' | 'site'>('office');
  const [isLoading, setIsLoading] = useState(false);
  const [isAdding, setIsAdding] = useState(false);

  // Load existing locations
  useEffect(() => {
    if (isOpen) {
      loadLocations();
    }
  }, [isOpen]);

  const loadLocations = async () => {
    setIsLoading(true);
    try {
      const dynamicLocations = await locationService.getCombinedLocations(employees, []);
      const locationItems: LocationItem[] = dynamicLocations.map((location, index) => ({
        id: `${index}`,
        name: location,
        type: location.toLowerCase().includes('remote') ? 'remote' : 
              location.toLowerCase().includes('site') ? 'site' : 'office'
      }));
      setLocations(locationItems);
    } catch (error) {
      console.error('Error loading locations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddLocation = () => {
    if (newLocation.trim()) {
      const newLocationItem: LocationItem = {
        id: Date.now().toString(),
        name: newLocation.trim(),
        type: newLocationType
      };
      
      setLocations(prev => [...prev, newLocationItem]);
      onLocationAdded(newLocation.trim());
      setNewLocation('');
      setIsAdding(false);
    }
  };

  const handleEditLocation = (id: string) => {
    setLocations(prev => 
      prev.map(loc => 
        loc.id === id ? { ...loc, isEditing: true } : loc
      )
    );
  };

  const handleSaveLocation = (id: string, newName: string) => {
    setLocations(prev => 
      prev.map(loc => 
        loc.id === id ? { ...loc, name: newName, isEditing: false } : loc
      )
    );
  };

  const handleDeleteLocation = (id: string) => {
    setLocations(prev => prev.filter(loc => loc.id !== id));
  };

  const getLocationIcon = (type: string) => {
    switch (type) {
      case 'remote':
        return <Home className="h-4 w-4 text-blue-500" />;
      case 'site':
        return <MapPin className="h-4 w-4 text-green-500" />;
      default:
        return <Building className="h-4 w-4 text-gray-500" />;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-full p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={onClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Manage Locations</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Content */}
          <div className="p-4 overflow-y-auto max-h-[calc(80vh-120px)]">
            {/* Add new location */}
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-3">Add New Location</h4>
              
              {!isAdding ? (
                <button
                  onClick={() => setIsAdding(true)}
                  className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Location
                </button>
              ) : (
                <div className="space-y-3">
                  <div className="flex gap-3">
                    <input
                      type="text"
                      value={newLocation}
                      onChange={(e) => setNewLocation(e.target.value)}
                      placeholder="Enter location name"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      autoFocus
                    />
                    <select
                      value={newLocationType}
                      onChange={(e) => setNewLocationType(e.target.value as 'office' | 'remote' | 'site')}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="office">Office</option>
                      <option value="site">Site</option>
                      <option value="remote">Remote</option>
                    </select>
                  </div>
                  
                  <div className="flex gap-2">
                    <button
                      onClick={handleAddLocation}
                      className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 transition-colors"
                      disabled={!newLocation.trim()}
                    >
                      Add
                    </button>
                    <button
                      onClick={() => {
                        setIsAdding(false);
                        setNewLocation('');
                      }}
                      className="px-3 py-1 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400 transition-colors"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Existing locations */}
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-3">Existing Locations</h4>
              
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                </div>
              ) : locations.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No locations found. Add your first location above.
                </div>
              ) : (
                <div className="space-y-2">
                  {locations.map((location) => (
                    <LocationItem
                      key={location.id}
                      location={location}
                      onEdit={handleEditLocation}
                      onSave={handleSaveLocation}
                      onDelete={handleDeleteLocation}
                      getIcon={getLocationIcon}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end p-4 border-t border-gray-200">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Individual location item component
interface LocationItemProps {
  location: LocationItem;
  onEdit: (id: string) => void;
  onSave: (id: string, newName: string) => void;
  onDelete: (id: string) => void;
  getIcon: (type: string) => React.ReactNode;
}

const LocationItem: React.FC<LocationItemProps> = ({
  location,
  onEdit,
  onSave,
  onDelete,
  getIcon
}) => {
  const [editValue, setEditValue] = useState(location.name);

  const handleSave = () => {
    if (editValue.trim()) {
      onSave(location.id, editValue.trim());
    }
  };

  const handleCancel = () => {
    setEditValue(location.name);
    onSave(location.id, location.name); // This will set isEditing to false
  };

  return (
    <div className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-md hover:bg-gray-50 transition-colors">
      <div className="flex items-center flex-1">
        {getIcon(location.type)}
        {location.isEditing ? (
          <input
            type="text"
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            className="ml-3 flex-1 px-2 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            autoFocus
          />
        ) : (
          <span className="ml-3 text-gray-900">{location.name}</span>
        )}
      </div>
      
      <div className="flex items-center space-x-2">
        {location.isEditing ? (
          <>
            <button
              onClick={handleSave}
              className="p-1 text-green-600 hover:text-green-800 transition-colors"
              title="Save"
            >
              <Save className="h-4 w-4" />
            </button>
            <button
              onClick={handleCancel}
              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
              title="Cancel"
            >
              <X className="h-4 w-4" />
            </button>
          </>
        ) : (
          <>
            <button
              onClick={() => onEdit(location.id)}
              className="p-1 text-blue-600 hover:text-blue-800 transition-colors"
              title="Edit"
            >
              <Edit2 className="h-4 w-4" />
            </button>
            <button
              onClick={() => onDelete(location.id)}
              className="p-1 text-red-600 hover:text-red-800 transition-colors"
              title="Delete"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </>
        )}
      </div>
    </div>
  );
};

export default LocationModal; 