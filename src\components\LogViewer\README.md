# Modern System Log Viewer

A professional and modern system log viewer component designed for enterprise applications. This component provides a comprehensive interface for viewing, filtering, searching, and analyzing system logs.

## Features

- **Two View Modes**: Switch between table and card views based on preference
- **Advanced Filtering**: Filter logs by type, user, date range, and search text
- **Sorting**: Sort logs by various attributes 
- **Real-time Updates**: Support for real-time log updates
- **Export**: Export logs to CSV for further analysis
- **Detailed View**: View comprehensive log details in a modal
- **User Activity Visualization**: Visual breakdown of user activity with interactive filtering
- **Responsive Design**: Works on all device sizes
- **Performance Optimized**: Paginated display for handling large log volumes

## Usage

```tsx
import { LogViewer } from './components/LogViewer';

// Your component
function YourComponent() {
  const [logs, setLogs] = useState([]);

  const handleRefresh = () => {
    // Fetch new logs
  };

  const handleClear = () => {
    // Clear logs
  };

  const handleAddLog = (logData) => {
    // Add a new log
  };

  return (
    <LogViewer 
      logs={logs}
      onRefresh={handleRefresh}
      onClear={handleClear}
      onAddLog={handleAddLog}
    />
  );
}
```

## Props

| Prop | Type | Description |
|------|------|-------------|
| logs | SystemLog[] | Array of log objects to display |
| onRefresh | () => void | Optional function to refresh logs |
| onClear | () => void | Optional function to clear logs |
| onExport | (logs: SystemLog[]) => void | Optional custom export function |
| onAddLog | (log: LogData) => void | Optional function to add a new log |

## Log Object Structure

```ts
interface SystemLog {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  action: string;
  user: string;
  timestamp: string;
  details: string;
}
```

## Component Structure

The LogViewer is composed of several modular components:

- **LogViewer**: Main component that orchestrates all the others
- **LogViewerHeader**: Contains title and action buttons
- **LogFilters**: Manages all filtering functionality
- **LogTable**: Displays logs in a table format
- **LogCard**: Displays a log as a card (used in card view)
- **LogDetailsModal**: Shows detailed information about a selected log
- **LogViewerEmpty**: Shows when no logs match the current filters
- **UserActivityChart**: Displays a visual breakdown of user activity with interactive filtering 