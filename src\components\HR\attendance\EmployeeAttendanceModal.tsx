import React, { useState, useEffect } from 'react';
import { 
  Attendance, 
  AttendanceStatus,
  AttendanceRegularizationRequest
} from '../../../types/attendance';
import { 
  Calendar, 
  Clock, 
  FileText, 
  User, 
  CheckCircle,
  AlertCircle,
  Calendar as CalendarIcon,
  Home,
  X as XIcon,
  UserCircle,
  Filter,
  Maximize2,
  Briefcase
} from 'lucide-react';
import { Tab } from '@headlessui/react';
import { Employee } from '../../../server/entities/Employee';
import RegularizationManager from './RegularizationManager';
import RegularizationService from '../../../services/RegularizationService';

interface EmployeeAttendanceModalProps {
  employeeId: number;
  employeeName: string;
  employeeCode?: string;
  employeeDepartment?: string;
  employeePosition?: string;
  employeePhoto?: string;
  attendances: Attendance[];
  onSubmitRegularization: (request: Omit<AttendanceRegularizationRequest, 'id'>) => void;
  onClose: () => void;
  canSubmitRegularization?: boolean; // Add this prop
}

const EmployeeAttendanceModal: React.FC<EmployeeAttendanceModalProps> = ({
  employeeId,
  employeeName,
  employeeCode,
  employeeDepartment,
  employeePosition,
  employeePhoto,
  attendances,
  onSubmitRegularization,
  onClose,
  canSubmitRegularization = false // Default to false
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [selectedDate, setSelectedDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );
  const [isMaximized, setIsMaximized] = useState(false);
  
  // Enhanced filter states
  const [filters, setFilters] = useState({
    datePeriod: 'today',
    dateFrom: '',
    dateTo: '',
    status: 'all',
    location: 'all',
    department: 'all',
    shift: 'all',
    workHoursFilter: 'all', // 'all', 'full_day', 'partial_day', 'overtime'
    searchText: ''
  });
  
  // Date period options
  const getDateRangeFromPeriod = (period: string) => {
    const today = new Date();
    const startOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
    
    switch (period) {
      case 'today':
        return { start: startOfToday, end: endOfToday };
      case 'yesterday':
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        return { start: new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate()), 
                 end: new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59) };
      case 'this_week':
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay());
        return { start: new Date(startOfWeek.getFullYear(), startOfWeek.getMonth(), startOfWeek.getDate()), 
                 end: endOfToday };
      case 'last_week':
        const lastWeekStart = new Date(today);
        lastWeekStart.setDate(today.getDate() - today.getDay() - 7);
        const lastWeekEnd = new Date(lastWeekStart);
        lastWeekEnd.setDate(lastWeekStart.getDate() + 6);
        return { start: new Date(lastWeekStart.getFullYear(), lastWeekStart.getMonth(), lastWeekStart.getDate()), 
                 end: new Date(lastWeekEnd.getFullYear(), lastWeekEnd.getMonth(), lastWeekEnd.getDate(), 23, 59, 59) };
      case 'this_month':
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        return { start: startOfMonth, end: endOfToday };
      case 'last_month':
        const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0, 23, 59, 59);
        return { start: lastMonthStart, end: lastMonthEnd };
      case 'last_three_months':
        const threeMonthsStart = new Date(today.getFullYear(), today.getMonth() - 3, 1);
        return { start: threeMonthsStart, end: endOfToday };
      case 'last_six_months':
        const sixMonthsStart = new Date(today.getFullYear(), today.getMonth() - 6, 1);
        return { start: sixMonthsStart, end: endOfToday };
      case 'this_year':
        const startOfYear = new Date(today.getFullYear(), 0, 1);
        return { start: startOfYear, end: endOfToday };
      case 'last_year':
        const lastYearStart = new Date(today.getFullYear() - 1, 0, 1);
        const lastYearEnd = new Date(today.getFullYear() - 1, 11, 31, 23, 59, 59);
        return { start: lastYearStart, end: lastYearEnd };
      default:
        return null;
    }
  };
  
  // Filter attendances for the current employee
  const employeeAttendances = attendances.filter(a => a.employeeId === employeeId);
  
  // Get unique departments, shifts, and locations for filter dropdowns
  const uniqueDepartments = [...new Set(employeeAttendances.map(a => a.department || a.employeeDepartment).filter((dept): dept is string => Boolean(dept)))];
  const uniqueShifts = [...new Set(employeeAttendances.map(a => {
    if (a.shiftName && a.shiftName.trim() !== '') {
      return a.shiftName;
    } else if (a.shift) {
      const shiftNames = {
        1: 'Morning Shift',
        2: 'Evening Shift', 
        3: 'Night Shift',
        4: 'Flexible Hours'
      };
      return shiftNames[a.shift as keyof typeof shiftNames] || `Shift ${a.shift}`;
    }
    return null;
  }).filter((shift): shift is string => Boolean(shift)))];
  const uniqueLocations = [...new Set(employeeAttendances.map(a => a.location).filter((location): location is string => Boolean(location)))];
  
  // Apply filters to attendance data
  const filteredAttendances = employeeAttendances.filter(attendance => {
    // Date period filter (takes precedence over manual date range)
    if (filters.datePeriod !== 'all') {
      const dateRange = getDateRangeFromPeriod(filters.datePeriod);
      if (dateRange) {
        const attendanceDate = new Date(attendance.date);
        if (attendanceDate < dateRange.start || attendanceDate > dateRange.end) return false;
      }
    } else {
      // Manual date range filter (only when no period is selected)
      if (filters.dateFrom && new Date(attendance.date) < new Date(filters.dateFrom)) return false;
      if (filters.dateTo && new Date(attendance.date) > new Date(filters.dateTo)) return false;
    }
    
    // Status filter
    if (filters.status !== 'all' && attendance.status !== filters.status) return false;
    
    // Location filter
    if (filters.location !== 'all') {
      if (filters.location === 'office' && attendance.status === AttendanceStatus.WORK_FROM_HOME) return false;
      if (filters.location === 'remote' && attendance.status !== AttendanceStatus.WORK_FROM_HOME) return false;
      if (filters.location !== 'office' && filters.location !== 'remote' && attendance.location !== filters.location) return false;
    }
    
    // Department filter
    if (filters.department !== 'all') {
      const dept = attendance.department || attendance.employeeDepartment;
      if (dept !== filters.department) return false;
    }
    
    // Shift filter
    if (filters.shift !== 'all') {
      let shiftName = attendance.shiftName;
      if (!shiftName && attendance.shift) {
        const shiftNames = {
          1: 'Morning Shift',
          2: 'Evening Shift', 
          3: 'Night Shift',
          4: 'Flexible Hours'
        };
        shiftName = shiftNames[attendance.shift as keyof typeof shiftNames] || `Shift ${attendance.shift}`;
      }
      if (shiftName !== filters.shift) return false;
    }
    
    // Work hours filter
    if (filters.workHoursFilter !== 'all') {
      // Calculate actual work hours from check-in/check-out times for more accurate filtering
      let actualWorkHours = attendance.workHours || 0;
      
      if (attendance.checkInTime && attendance.checkOutTime && 
          attendance.checkInTime !== '-' && attendance.checkOutTime !== '-') {
        try {
          const [checkInHour, checkInMin] = attendance.checkInTime.split(':').map(Number);
          const [checkOutHour, checkOutMin] = attendance.checkOutTime.split(':').map(Number);
          
          const today = new Date();
          const checkIn = new Date(today.getFullYear(), today.getMonth(), today.getDate(), checkInHour, checkInMin);
          let checkOut = new Date(today.getFullYear(), today.getMonth(), today.getDate(), checkOutHour, checkOutMin);
          
          if (checkOut < checkIn) {
            checkOut.setDate(checkOut.getDate() + 1);
          }
          
          const diffMs = checkOut.getTime() - checkIn.getTime();
          actualWorkHours = diffMs / (1000 * 60 * 60);
        } catch (error) {
          actualWorkHours = attendance.workHours || 0;
        }
      }
      
      switch (filters.workHoursFilter) {
        case 'full_day':
          if (actualWorkHours < 7) return false;
          break;
        case 'partial_day':
          if (actualWorkHours >= 7) return false;
          break;
        case 'overtime':
          // Check if work hours > 8 OR if there's recorded overtime
          const hasCalculatedOvertime = actualWorkHours > 8;
          const hasRecordedOvertime = attendance.overtime && attendance.overtime > 0;
          if (!hasCalculatedOvertime && !hasRecordedOvertime) return false;
          break;
      }
    }
    
    // Search text filter (searches in location and notes if available)
    if (filters.searchText) {
      const searchLower = filters.searchText.toLowerCase();
      const locationMatch = attendance.location?.toLowerCase().includes(searchLower);
      const dateMatch = new Date(attendance.date).toLocaleDateString().toLowerCase().includes(searchLower);
      if (!locationMatch && !dateMatch) return false;
    }
    
    return true;
  });
  
  // Reset filters function
  const resetFilters = () => {
    setFilters({
      datePeriod: 'today',
      dateFrom: '',
      dateTo: '',
      status: 'all',
      location: 'all',
      department: 'all',
      shift: 'all',
      workHoursFilter: 'all',
      searchText: ''
    });
  };
  
  // Calculate statistics
  const totalWorkDays = filteredAttendances.length;
  const presentDays = filteredAttendances.filter(a => 
    a.status === AttendanceStatus.PRESENT || 
    a.status === AttendanceStatus.WORK_FROM_HOME
  ).length;
  const lateDays = filteredAttendances.filter(a => a.status === AttendanceStatus.LATE).length;
  const absentDays = filteredAttendances.filter(a => 
    a.status === AttendanceStatus.ABSENT || 
    a.status === AttendanceStatus.LEAVE
  ).length;
  const workFromHomeDays = filteredAttendances.filter(a => a.status === AttendanceStatus.WORK_FROM_HOME).length;
  
  // Calculate overtime hours - if work hours > 8, then overtime = work hours - 8
  const totalOvertimeHours = filteredAttendances.reduce((sum, a) => {
    // Calculate actual work hours from check-in/check-out times for more accurate overtime
    let actualWorkHours = a.workHours || 0;
    
    // If we have both check-in and check-out times, calculate precise work hours
    if (a.checkInTime && a.checkOutTime && 
        a.checkInTime !== '-' && a.checkOutTime !== '-') {
      try {
        const [checkInHour, checkInMin] = a.checkInTime.split(':').map(Number);
        const [checkOutHour, checkOutMin] = a.checkOutTime.split(':').map(Number);
        
        const today = new Date();
        const checkIn = new Date(today.getFullYear(), today.getMonth(), today.getDate(), checkInHour, checkInMin);
        let checkOut = new Date(today.getFullYear(), today.getMonth(), today.getDate(), checkOutHour, checkOutMin);
        
        // If check-out time is earlier than check-in time, assume it's next day
        if (checkOut < checkIn) {
          checkOut.setDate(checkOut.getDate() + 1);
        }
        
        // Calculate the difference in hours (with decimal precision)
        const diffMs = checkOut.getTime() - checkIn.getTime();
        actualWorkHours = diffMs / (1000 * 60 * 60); // Convert to hours
      } catch (error) {
        // Fall back to stored value if calculation fails
        actualWorkHours = a.workHours || 0;
      }
    }
    
    const overtimeFromWorkHours = actualWorkHours > 8 ? actualWorkHours - 8 : 0;
    const recordedOvertime = a.overtime || 0;
    
    // Use the higher value between calculated overtime and recorded overtime
    const actualOvertime = Math.max(overtimeFromWorkHours, recordedOvertime);
    
    return sum + actualOvertime;
  }, 0);
  
  // For regularization form
  const [isSubmittingRegularization, setIsSubmittingRegularization] = useState(false);
  const [employeeRegularizationRequests, setEmployeeRegularizationRequests] = useState<AttendanceRegularizationRequest[]>([]);

  useEffect(() => {
    if (employeeId) {
      RegularizationService.getInstance().getRequestsByEmployee(employeeId)
        .then(setEmployeeRegularizationRequests)
        .catch(() => setEmployeeRegularizationRequests([]));
    }
  }, [employeeId]);

  const handleSubmitRegularization = async (request: Omit<AttendanceRegularizationRequest, 'id'>) => {
    setIsSubmittingRegularization(true);
    
    try {
      await onSubmitRegularization(request);
      // Show success message
      alert('Regularization request submitted successfully!');
    } catch (error) {
      console.error('Error submitting regularization request:', error);
      alert('Error submitting request. Please try again.');
    } finally {
      setIsSubmittingRegularization(false);
    }
  };
  


  // Format work hours to show hours and minutes
  const formatWorkHours = (hours: number | null | undefined, checkInTime?: string | null, checkOutTime?: string | null): string => {
    // If we have both check-in and check-out times, calculate the actual work hours
    if (checkInTime && checkOutTime && checkInTime !== '-' && checkOutTime !== '-') {
      try {
        // Parse the time strings (assuming format like "10:23" or "19:48")
        const [checkInHour, checkInMin] = checkInTime.split(':').map(Number);
        const [checkOutHour, checkOutMin] = checkOutTime.split(':').map(Number);
        
        // Create date objects for today with the given times
        const today = new Date();
        const checkIn = new Date(today.getFullYear(), today.getMonth(), today.getDate(), checkInHour, checkInMin);
        let checkOut = new Date(today.getFullYear(), today.getMonth(), today.getDate(), checkOutHour, checkOutMin);
        
        // If check-out time is earlier than check-in time, assume it's next day (night shift)
        if (checkOut < checkIn) {
          checkOut.setDate(checkOut.getDate() + 1);
        }
        
        // Calculate the difference in milliseconds
        const diffMs = checkOut.getTime() - checkIn.getTime();
        const diffHours = diffMs / (1000 * 60 * 60); // Convert to hours
        
        const wholeHours = Math.floor(diffHours);
        const minutes = Math.round((diffHours - wholeHours) * 60);
        
        return `${wholeHours}h ${minutes}m`;
      } catch (error) {
        console.error('Error calculating work hours:', error);
        // Fall back to the stored value if calculation fails
      }
    }
    
    // Fall back to stored work hours value
    if (!hours || hours === 0) return '0h 0m';
    
    const wholeHours = Math.floor(hours);
    const minutes = Math.round((hours - wholeHours) * 60);
    
    return `${wholeHours}h ${minutes}m`;
  };

  return (
    <div className={`fixed inset-0 z-50 overflow-y-auto ${isMaximized ? 'h-screen w-screen flex flex-col' : ''}`}>
      {/* Backdrop with blur effect */}
      <div 
        className="fixed inset-0 bg-gray-900 bg-opacity-50 dark:bg-black dark:bg-opacity-60 backdrop-blur-sm transition-opacity"
        onClick={onClose}
      ></div>
      
      {/* Modal content */}
      <div className={`flex items-center justify-center ${isMaximized ? 'h-full w-full flex-1 p-0' : 'min-h-full p-4'}`}>
        <div className={`relative bg-gray-50 dark:bg-gray-900 shadow-xl transform transition-all ${
          isMaximized 
            ? 'w-full h-full max-w-none max-h-none m-0 rounded-none flex flex-col' 
            : 'w-full max-w-7xl rounded-lg'
        }`}>
          {/* Close and Maximize buttons */}
          <div className="absolute top-4 right-4 z-10 flex space-x-2">
            <button
              onClick={() => setIsMaximized(!isMaximized)}
              className="rounded-full bg-white dark:bg-gray-800 p-2 text-gray-400 dark:text-gray-300 hover:text-gray-600 dark:hover:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 shadow-sm"
              title={isMaximized ? "Restore" : "Maximize"}
            >
              <Maximize2 className="h-6 w-6" />
            </button>
            <button
              onClick={onClose}
              className="rounded-full bg-white dark:bg-gray-800 p-2 text-gray-400 dark:text-gray-300 hover:text-gray-600 dark:hover:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 shadow-sm"
              title="Close"
            >
              <XIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Header with employee info */}
          <div className={`bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm ${isMaximized ? 'rounded-none' : 'rounded-t-lg'}`}
            style={isMaximized ? { width: '100%' } : {}}>
            <div className="px-4 sm:px-6 lg:px-8 py-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 font-semibold text-xl mr-4 overflow-hidden">
                    {(() => {
                      const photoPath = employeePhoto;
                      const hasValidPhoto = photoPath && photoPath.trim() !== '';
                      
                      if (hasValidPhoto) {
                        // Handle different image path formats based on existing patterns
                        let imageSrc = photoPath;
                        
                        if (photoPath.startsWith('/uploads/')) {
                          imageSrc = photoPath;
                        } else if (photoPath.startsWith('uploads/')) {
                          imageSrc = `/${photoPath}`;
                        } else if (!photoPath.startsWith('http') && !photoPath.startsWith('/')) {
                          imageSrc = `/uploads/${photoPath}`;
                        }
                        
                        return (
                          <img 
                            className="h-12 w-12 rounded-full object-cover border-2 border-blue-50 dark:border-blue-800 shadow-sm" 
                            src={imageSrc} 
                            alt={employeeName}
                            onError={(e) => {
                              // If image fails to load, replace with fallback
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              const parent = target.parentElement;
                              if (parent) {
                                parent.innerHTML = `<div class=\"h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center\"><span class=\"text-xl font-semibold text-blue-600 dark:text-blue-400\">${employeeName?.substring(0, 2) || 'N/A'}</span></div>`;
                              }
                            }}
                          />
                        );
                      }
                      
                      // Fallback to initials if no photo
                      return <span>{employeeName.split(' ').map(n => n[0]).join('')}</span>;
                    })()}
                  </div>
                  <div>
                    <h1 className="text-xl font-semibold text-gray-900 dark:text-white leading-tight">{employeeName}</h1>
                    <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mt-0.5">
                      <span className="flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        ID: {employeeCode || `EMP-${employeeId}`}
                      </span>
                      {employeeDepartment && (
                        <span className="flex items-center">
                          <Briefcase className="h-4 w-4 mr-1" />
                          {employeeDepartment}
                        </span>
                      )}
                      {employeePosition && (
                        <span className="flex items-center">
                          <UserCircle className="h-4 w-4 mr-1" />
                          {employeePosition}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Main content */}
          <div className={`flex-1 ${isMaximized ? 'flex flex-col min-h-0 h-full w-full overflow-y-auto' : 'overflow-y-auto max-h-[80vh]'}`}
            style={isMaximized ? { minHeight: 0 } : {}}>
            <div className={`px-4 sm:px-6 lg:px-8 py-3 ${isMaximized ? 'flex-1 min-h-0 h-full w-full' : ''}`}
              style={isMaximized ? { minHeight: 0 } : {}}>
              {/* Stats cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-3 mb-2">
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 border-l-4 border-blue-500">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Present Days</p>
                      <div className="flex items-baseline mt-1">
                        <p className="text-2xl font-bold text-gray-900 dark:text-white">{presentDays}</p>
                        <p className="ml-2 text-xs text-green-500 dark:text-green-400">
                          {totalWorkDays > 0 ? Math.round((presentDays / totalWorkDays) * 100) : 0}%
                        </p>
                      </div>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-blue-50 dark:bg-blue-900/30 flex items-center justify-center">
                      <CheckCircle className="h-5 w-5 text-blue-500 dark:text-blue-400" />
                    </div>
                  </div>
                </div>
                
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 border-l-4 border-yellow-500">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Late Days</p>
                      <div className="flex items-baseline mt-1">
                        <p className="text-2xl font-bold text-gray-900 dark:text-white">{lateDays}</p>
                        <p className="ml-2 text-xs text-yellow-500 dark:text-yellow-400">
                          {totalWorkDays > 0 ? Math.round((lateDays / totalWorkDays) * 100) : 0}%
                        </p>
                      </div>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-yellow-50 dark:bg-yellow-900/30 flex items-center justify-center">
                      <Clock className="h-5 w-5 text-yellow-500 dark:text-yellow-400" />
                    </div>
                  </div>
                </div>
                
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 border-l-4 border-red-500">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Absent Days</p>
                      <div className="flex items-baseline mt-1">
                        <p className="text-2xl font-bold text-gray-900 dark:text-white">{absentDays}</p>
                        <p className="ml-2 text-xs text-red-500 dark:text-red-400">
                          {totalWorkDays > 0 ? Math.round((absentDays / totalWorkDays) * 100) : 0}%
                        </p>
                      </div>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-red-50 dark:bg-red-900/30 flex items-center justify-center">
                      <AlertCircle className="h-5 w-5 text-red-500 dark:text-red-400" />
                    </div>
                  </div>
                </div>
                
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 border-l-4 border-green-500">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Work Hours</p>
                      <div className="flex items-baseline mt-1">
                        <p className="text-2xl font-bold text-gray-900 dark:text-white">
                          {(filteredAttendances.reduce((sum, a) => sum + (a.workHours || 0), 0)).toFixed(1)}
                        </p>
                        <p className="ml-2 text-xs text-gray-500 dark:text-gray-400">total</p>
                      </div>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-green-50 dark:bg-green-900/30 flex items-center justify-center">
                      <Clock className="h-5 w-5 text-green-500 dark:text-green-400" />
                    </div>
                  </div>
                </div>
                
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 border-l-4 border-orange-500">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Over Time</p>
                      <div className="flex items-baseline mt-1">
                        <p className="text-2xl font-bold text-gray-900 dark:text-white">
                          {totalOvertimeHours.toFixed(1)}
                        </p>
                        <p className="ml-2 text-xs text-orange-500 dark:text-orange-400">hours</p>
                      </div>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-orange-50 dark:bg-orange-900/30 flex items-center justify-center">
                      <Clock className="h-5 w-5 text-orange-500 dark:text-orange-400" />
                    </div>
                  </div>
                </div>
                
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 border-l-4 border-purple-500">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Remote Work</p>
                      <div className="flex items-baseline mt-1">
                        <p className="text-2xl font-bold text-gray-900 dark:text-white">{workFromHomeDays}</p>
                        <p className="ml-2 text-xs text-purple-500 dark:text-purple-400">
                          {totalWorkDays > 0 ? Math.round((workFromHomeDays / totalWorkDays) * 100) : 0}%
                        </p>
                      </div>
                    </div>
                    <div className="h-10 w-10 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center">
                      <Home className="h-5 w-5 text-purple-500 dark:text-purple-400" />
                    </div>
                  </div>
                </div>
              </div>
            
              {/* Tab navigation */}
              <Tab.Group selectedIndex={activeTab} onChange={setActiveTab}>
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden mt-2 mb-2">
                  <Tab.List className="flex border-b border-gray-200 dark:border-gray-700 mb-0.5">
                    <Tab className={({ selected }) =>
                      `py-3 px-4 text-sm font-medium flex items-center ${
                        selected 
                          ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400' 
                          : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                      }`
                    }>
                      <CalendarIcon className="h-4 w-4 mr-2" />
                      {employeeName.split(' ')[0]}'s Attendance
                    </Tab>
                    <Tab className={({ selected }) =>
                      `py-3 px-4 text-sm font-medium flex items-center ${
                        selected 
                          ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400' 
                          : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                      }`
                    }>
                      <FileText className="h-4 w-4 mr-2" />
                      Regularization
                    </Tab>
                  </Tab.List>
                
                  <Tab.Panels>
                    {/* My Attendance Panel */}
                    <Tab.Panel>
                      <div className="pt-3 pb-2">
                        <div className="mb-2 flex justify-between items-center">
                          <h3 className="text-lg font-medium text-gray-900 dark:text-white leading-tight">{employeeName.split(' ')[0]}'s Attendance History</h3>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            Showing {filteredAttendances.length} of {employeeAttendances.length} records
                          </div>
                        </div>
                        
                        {/* Enhanced Filters Panel */}
                        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-2 mb-2">
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center">
                              <Filter className="h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Filters</h4>
                            </div>
                            <button
                              onClick={resetFilters}
                              className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 font-medium"
                            >
                              Reset
                            </button>
                          </div>
                          
                          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-8 gap-3">
                            {/* Date Period */}
                            <div>
                              <select
                                value={filters.datePeriod}
                                onChange={(e) => setFilters(prev => ({ ...prev, datePeriod: e.target.value, dateFrom: '', dateTo: '' }))}
                                className="w-full text-xs rounded border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
                              >
                                <option value="all">All Time</option>
                                <option value="today">Today</option>
                                <option value="yesterday">Yesterday</option>
                                <option value="this_week">This Week</option>
                                <option value="last_week">Last Week</option>
                                <option value="this_month">This Month</option>
                                <option value="last_month">Last Month</option>
                                <option value="last_three_months">Last 3 Months</option>
                                <option value="last_six_months">Last 6 Months</option>
                                <option value="this_year">This Year</option>
                                <option value="last_year">Last Year</option>
                              </select>
                            </div>
                            
                            {/* Custom Date Range (only show when "All Time" is selected) */}
                            {filters.datePeriod === 'all' && (
                              <>
                                <div>
                                  <input
                                    type="date"
                                    placeholder="From"
                                    value={filters.dateFrom}
                                    onChange={(e) => setFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
                                    className="w-full text-xs rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                  />
                                </div>
                                <div>
                                  <input
                                    type="date"
                                    placeholder="To"
                                    value={filters.dateTo}
                                    onChange={(e) => setFilters(prev => ({ ...prev, dateTo: e.target.value }))}
                                    className="w-full text-xs rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                  />
                                </div>
                              </>
                            )}
                            
                            {/* Status */}
                            <div>
                              <select
                                value={filters.status}
                                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                                className="w-full text-xs rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                              >
                                <option value="all">All Status</option>
                                <option value={AttendanceStatus.PRESENT}>Present</option>
                                <option value={AttendanceStatus.LATE}>Late</option>
                                <option value={AttendanceStatus.ABSENT}>Absent</option>
                                <option value={AttendanceStatus.WORK_FROM_HOME}>WFH</option>
                                <option value={AttendanceStatus.LEAVE}>Leave</option>
                                <option value={AttendanceStatus.HALF_DAY}>Half Day</option>
                              </select>
                            </div>
                            
                            {/* Location */}
                            <div>
                              <select
                                value={filters.location}
                                onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                                className="w-full text-xs rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                              >
                                <option value="all">All Locations</option>
                                <option value="office">Office</option>
                                <option value="remote">Remote</option>
                                {uniqueLocations.map(location => (
                                  <option key={location} value={location}>{location}</option>
                                ))}
                              </select>
                            </div>
                            
                            {/* Department */}
                            <div>
                              <select
                                value={filters.department}
                                onChange={(e) => setFilters(prev => ({ ...prev, department: e.target.value }))}
                                className="w-full text-xs rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                              >
                                <option value="all">All Departments</option>
                                {uniqueDepartments.map(dept => (
                                  <option key={dept} value={dept}>{dept}</option>
                                ))}
                              </select>
                            </div>
                            
                            {/* Shift */}
                            <div>
                              <select
                                value={filters.shift}
                                onChange={(e) => setFilters(prev => ({ ...prev, shift: e.target.value }))}
                                className="w-full text-xs rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                              >
                                <option value="all">All Shifts</option>
                                {uniqueShifts.map(shift => (
                                  <option key={shift} value={shift}>{shift}</option>
                                ))}
                              </select>
                            </div>
                            
                            {/* Work Hours Filter */}
                            <div>
                              <select
                                value={filters.workHoursFilter}
                                onChange={(e) => setFilters(prev => ({ ...prev, workHoursFilter: e.target.value }))}
                                className="w-full text-xs rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                              >
                                <option value="all">All Work Hours</option>
                                <option value="full_day">Full Day (7+ hrs)</option>
                                <option value="partial_day">Partial Day (&lt;7 hrs)</option>
                                <option value="overtime">With Overtime</option>
                              </select>
                            </div>
                            
                            {/* Search */}
                            <div>
                              <input
                                type="text"
                                placeholder="Search..."
                                value={filters.searchText}
                                onChange={(e) => setFilters(prev => ({ ...prev, searchText: e.target.value }))}
                                className="w-full text-xs rounded border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                              />
                            </div>
                          </div>
                        </div>
                        
                        <div className={`${isMaximized ? '' : 'overflow-x-auto'}`}>
                          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead className="bg-gray-50 dark:bg-gray-700">
                              <tr>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Check In</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Check Out</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Work Hours</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Overtime</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Location</th>
                              </tr>
                            </thead>
                            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                              {filteredAttendances.length > 0 ? (
                                filteredAttendances.map((attendance) => (
                                  <tr key={attendance.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                      {new Date(attendance.date).toLocaleDateString('en-US', {
                                        weekday: 'long',
                                        year: 'numeric',
                                        month: 'long',
                                        day: 'numeric'
                                      })}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                      <span className={`px-2 py-1 text-xs rounded-full ${
                                        attendance.status === AttendanceStatus.PRESENT 
                                          ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' 
                                          : attendance.status === AttendanceStatus.LATE
                                            ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400'
                                            : attendance.status === AttendanceStatus.WORK_FROM_HOME
                                              ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400'
                                              : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400'
                                      }`}>
                                        {attendance.status}
                                      </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                      {attendance.checkInTime || '-'}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                      {attendance.checkOutTime || '-'}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                      {formatWorkHours(attendance.workHours, attendance.checkInTime, attendance.checkOutTime)}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                      {(() => {
                                        // Calculate actual work hours from check-in/check-out times for more accurate overtime
                                        let actualWorkHours = attendance.workHours || 0;
                                        
                                        // If we have both check-in and check-out times, calculate precise work hours
                                        if (attendance.checkInTime && attendance.checkOutTime && 
                                            attendance.checkInTime !== '-' && attendance.checkOutTime !== '-') {
                                          try {
                                            const [checkInHour, checkInMin] = attendance.checkInTime.split(':').map(Number);
                                            const [checkOutHour, checkOutMin] = attendance.checkOutTime.split(':').map(Number);
                                            
                                            const today = new Date();
                                            const checkIn = new Date(today.getFullYear(), today.getMonth(), today.getDate(), checkInHour, checkInMin);
                                            let checkOut = new Date(today.getFullYear(), today.getMonth(), today.getDate(), checkOutHour, checkOutMin);
                                            
                                            // If check-out time is earlier than check-in time, assume it's next day
                                            if (checkOut < checkIn) {
                                              checkOut.setDate(checkOut.getDate() + 1);
                                            }
                                            
                                            // Calculate the difference in hours (with decimal precision)
                                            const diffMs = checkOut.getTime() - checkIn.getTime();
                                            actualWorkHours = diffMs / (1000 * 60 * 60); // Convert to hours
                                          } catch (error) {
                                            // Fall back to stored value if calculation fails
                                            actualWorkHours = attendance.workHours || 0;
                                          }
                                        }
                                        
                                        const overtimeFromWorkHours = actualWorkHours > 8 ? actualWorkHours - 8 : 0;
                                        const recordedOvertime = attendance.overtime || 0;
                                        const actualOvertime = Math.max(overtimeFromWorkHours, recordedOvertime);
                                        
                                        if (actualOvertime === 0) return '-';
                                        
                                        // Convert decimal hours to hours and minutes
                                        const wholeHours = Math.floor(actualOvertime);
                                        const minutes = Math.round((actualOvertime - wholeHours) * 60);
                                        
                                        const overtimeText = wholeHours > 0 && minutes > 0 
                                          ? `${wholeHours}h ${minutes}m`
                                          : wholeHours > 0 
                                            ? `${wholeHours}h 0m`
                                            : `0h ${minutes}m`;
                                        
                                        return (
                                          <span className={`${actualOvertime > 0 ? 'text-orange-600 dark:text-orange-400 font-medium' : ''}`}>
                                            {overtimeText}
                                          </span>
                                        );
                                      })()}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                      {attendance.location || '-'}
                                    </td>
                                  </tr>
                                ))
                              ) : employeeAttendances.length > 0 ? (
                                <tr>
                                  <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                                    <div className="flex flex-col items-center py-4">
                                      <AlertCircle className="h-8 w-8 text-gray-300 dark:text-gray-600 mb-2" />
                                      <p>No records match your current filters</p>
                                      <button
                                        onClick={resetFilters}
                                        className="mt-2 text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 text-sm font-medium"
                                      >
                                        Clear all filters
                                      </button>
                                    </div>
                                  </td>
                                </tr>
                              ) : (
                                <tr>
                                  <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                                    No attendance records found
                                  </td>
                                </tr>
                              )}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </Tab.Panel>
                    
                    {/* Regularization Request Panel */}
                    <Tab.Panel>
                      <div className="p-6">
                        <RegularizationManager
                          mode={canSubmitRegularization ? 'both' : 'table'}
                          showFormButton={canSubmitRegularization}
                          requests={employeeRegularizationRequests}
                          employeeId={employeeId}
                          employeeName={employeeName}
                          onSubmit={handleSubmitRegularization}
                          isSubmitting={isSubmittingRegularization}
                          compact={false}
                        />
                      </div>
                    </Tab.Panel>
                  </Tab.Panels>
                </div>
              </Tab.Group>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmployeeAttendanceModal; 