import React, { useState, useEffect } from 'react';
import { User } from '../types/user';
import { Search, User as UserIcon, X } from 'lucide-react';
import api from '../services/api';

interface UserSelectDropdownProps {
  onSelect: (user: User) => void;
  selectedUser?: User;
  className?: string;
}

export const UserSelectDropdown: React.FC<UserSelectDropdownProps> = ({
  onSelect,
  selectedUser,
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [users, setUsers] = useState<User[]>([]);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        // Use the api instance instead of fetch to ensure the auth token is included
        const response = await api.get('/users');
        const data = response.data;
        // Extract users array from the response
        const userArray = data.users || data || [];
        setUsers(userArray.filter((user: any) => user.isActive !== false));
      } catch (error) {
        console.error('Error fetching users:', error);
      }
    };
    fetchUsers();
  }, []);

  // Add a safety check before filtering
  const filteredUsers = Array.isArray(users) 
    ? users.filter(user =>
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (user.department && user.department.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    : [];

  return (
    <div className={`relative ${className}`}>
      <div 
        className="flex items-center gap-2 border rounded-lg p-2 hover:border-blue-500 cursor-pointer relative"
        onClick={() => setIsOpen(!isOpen)}
      >
        <UserIcon className="h-5 w-5 text-gray-400" />
        {selectedUser ? (
          <div className="flex items-center justify-between w-full">
            <span className="flex-1 text-left">
              {selectedUser.name} {selectedUser.department && `(${selectedUser.department})`}
            </span>
            <button
              onClick={(e) => {
                e.stopPropagation(); // Prevent dropdown from opening when clicking X
                onSelect({
                  id: '',
                  name: '',
                  email: '',
                  department: '',
                  password: '',
                  project: null,
                  location: null,
                  role: 'EMPLOYEE',
                  permissions: [],
                  isActive: true,
                  createdAt: new Date(),
                  updatedAt: new Date()
                });
                setIsOpen(false);
              }}
              className="p-1 hover:bg-gray-100 rounded-full"
            >
              <X className="h-4 w-4 text-gray-500" />
            </button>
          </div>
        ) : (
          <span className="text-gray-500">Select user...</span>
        )}
      </div>

      {isOpen && (
        <div className={`absolute z-10 w-full mt-1 bg-white border rounded-lg shadow-lg transition-all duration-300 ${isOpen ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2 pointer-events-none'}`}>
          <div className="p-2 border-b">
            <div className="flex items-center gap-2">
              <Search className="h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search users..."
                className="w-full outline-none"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          
          <div className="max-h-60 overflow-y-auto">
            {filteredUsers.length > 0 ? (
              filteredUsers.map(user => (
                <div
                  key={user.id}
                  onClick={() => {
                    onSelect(user);
                    setIsOpen(false);
                  }}
                  className="flex items-center gap-3 p-3 hover:bg-blue-50 cursor-pointer"
                >
                  <div className="flex-1">
                    <p className="font-medium">{user.name}</p>
                    <p className="text-sm text-gray-500">
                      {user.department && `${user.department}`} 
                      {user.department && user.role && ' • '} 
                      {user.role && `${user.role}`}
                    </p>
                  </div>
                  {selectedUser?.id === user.id && (
                    <span className="text-blue-500">✓</span>
                  )}
                </div>
              ))
            ) : (
              <div className="p-4 text-center text-gray-500 text-sm">
                {users.length === 0 ? 'Loading users...' : 'No employees found matching your search'}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}; 