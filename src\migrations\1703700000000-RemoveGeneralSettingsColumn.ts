import { MigrationInterface, QueryRunner } from "typeorm";

export class RemoveGeneralSettingsColumn1703700000000 implements MigrationInterface {
    name = 'RemoveGeneralSettingsColumn1703700000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if the column exists before dropping it
        const table = await queryRunner.getTable('leave_policy_configurations');
        const column = table?.findColumnByName('generalSettings');
        
        if (column) {
            await queryRunner.dropColumn('leave_policy_configurations', 'generalSettings');
            console.log('✅ Removed generalSettings column from leave_policy_configurations table');
        } else {
            console.log('ℹ️ generalSettings column does not exist in leave_policy_configurations table');
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Add the column back if needed for rollback
        await queryRunner.query(`
            ALTER TABLE \`leave_policy_configurations\` 
            ADD \`generalSettings\` json NULL
        `);
        console.log('✅ Added back generalSettings column to leave_policy_configurations table');
    }
} 