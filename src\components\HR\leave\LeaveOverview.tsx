import React from 'react';
import { Users, Clock, Calendar, Activity, Building2, CheckCircle, X } from 'lucide-react';
import { LeaveRequest, LeaveStatus } from '../../../types/attendance';

interface EmployeeLeaveData {
  employeeId: number;
  employeeName: string;
  employeeCode: string;
  department: string;
  position: string;
  leaveBalances: any[];
  recentRequests: LeaveRequest[];
  totalDaysUsed: number;
  totalDaysRemaining: number;
}

interface LeaveOverviewProps {
  employeeLeaveData: EmployeeLeaveData[];
  allLeaveRequests: LeaveRequest[];
  departments: string[];
  loading: boolean;
}

const LeaveOverview: React.FC<LeaveOverviewProps> = ({
  employeeLeaveData,
  allLeaveRequests,
  departments,
  loading
}) => {
  const calculateDays = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = end.getTime() - start.getTime();
    return Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1;
  };

  const getStatusBadge = (status: LeaveStatus) => {
    const statusStyles = {
      [LeaveStatus.PENDING]: 'bg-yellow-100 text-yellow-800',
      [LeaveStatus.APPROVED]: 'bg-green-100 text-green-800',
      [LeaveStatus.REJECTED]: 'bg-red-100 text-red-800',
      [LeaveStatus.CANCELLED]: 'bg-gray-100 text-gray-800'
    };

    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusStyles[status]}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const getLeaveTypeLabel = (leaveType: string) => {
    const labels = {
      'Annual': 'Annual Leave',
      'Sick': 'Sick Leave',
      'Personal': 'Personal Leave',
      'Unpaid': 'Unpaid Leave',
      'Maternity': 'Maternity Leave',
      'Paternity': 'Paternity Leave',
      'Bereavement': 'Bereavement Leave',
      'Compensatory Off': 'Compensatory Off',
      'Other': 'Other'
    };
    return labels[leaveType] || leaveType;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* HR Dashboard Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600">Total Employees</p>
              <p className="text-2xl font-bold text-blue-900">{employeeLeaveData.length}</p>
            </div>
            <Users className="h-8 w-8 text-blue-600" />
          </div>
        </div>
        <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-4 border border-yellow-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-yellow-600">Pending Approvals</p>
              <p className="text-2xl font-bold text-yellow-900">
                {allLeaveRequests.filter(r => r.status === LeaveStatus.PENDING).length}
              </p>
            </div>
            <Clock className="h-8 w-8 text-yellow-600" />
          </div>
        </div>
        <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4 border border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-600">Days Requested</p>
              <p className="text-2xl font-bold text-green-900">
                {allLeaveRequests.reduce((sum, req) => sum + calculateDays(req.startDate, req.endDate), 0)}
              </p>
            </div>
            <Calendar className="h-8 w-8 text-green-600" />
          </div>
        </div>
        <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-4 border border-purple-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-600">This Month</p>
              <p className="text-2xl font-bold text-purple-900">{allLeaveRequests.length}</p>
            </div>
            <Activity className="h-8 w-8 text-purple-600" />
          </div>
        </div>
      </div>

      {/* Recent Activity & Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Leave Requests */}
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Leave Requests</h3>
          <div className="space-y-3">
            {allLeaveRequests.slice(0, 5).map((request) => (
              <div key={request.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <Calendar className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{request.employeeName}</p>
                    <p className="text-xs text-gray-500">
                      {getLeaveTypeLabel(request.leaveType)} - {calculateDays(request.startDate, request.endDate)} day(s)
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusBadge(request.status)}
                  {request.status === LeaveStatus.PENDING && (
                    <div className="flex space-x-1">
                      <button className="p-1 text-green-600 hover:bg-green-50 rounded">
                        <CheckCircle className="h-4 w-4" />
                      </button>
                      <button className="p-1 text-red-600 hover:bg-red-50 rounded">
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Department Overview */}
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Department Overview</h3>
          <div className="space-y-3">
            {departments.length > 0 ? departments.map((dept) => {
              const deptEmployees = employeeLeaveData.filter(emp => emp.department === dept);
              const deptRequests = allLeaveRequests.filter(req => 
                deptEmployees.some(emp => emp.employeeId === req.employeeId)
              );
              
              return (
                <div key={dept} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <Building2 className="h-5 w-5 text-gray-400 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">{dept}</p>
                      <p className="text-xs text-gray-500">{deptEmployees.length} employees</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">{deptRequests.length}</p>
                    <p className="text-xs text-gray-500">requests</p>
                  </div>
                </div>
              );
            }) : (
              <div className="text-center py-4">
                <p className="text-gray-500">No departments found</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LeaveOverview; 