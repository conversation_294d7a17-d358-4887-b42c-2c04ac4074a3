import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Filter, 
  Calculator, 
  Save, 
  X, 
  ChevronDown, 
  DollarSign, 
  Percent, 
  Edit3,
  AlertCircle,
  Code
} from 'lucide-react';
import { SalaryComponent } from '../../../types/payroll';

interface SalaryComponentsProps {
  isAdmin?: boolean;
}

const SalaryComponents: React.FC<SalaryComponentsProps> = ({ isAdmin = false }) => {
  // State declarations
  const [components, setComponents] = useState<SalaryComponent[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingComponent, setEditingComponent] = useState<SalaryComponent | null>(null);
  const [activeFilter, setActiveFilter] = useState<string>('all');
  const [showFormulaHelper, setShowFormulaHelper] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  
  // Sample data for demonstration
  const sampleComponents: SalaryComponent[] = [
    {
      id: 1,
      name: 'Basic Salary',
      type: 'earning',
      calculationType: 'fixed',
      value: 0,
      taxable: true,
      description: 'Base salary component',
      isDefault: true,
      isActive: true
    },
    {
      id: 2,
      name: 'Housing Allowance',
      type: 'earning',
      calculationType: 'percentage',
      value: 25,
      taxable: true,
      description: '25% of basic salary',
      isDefault: true,
      isActive: true
    },
    {
      id: 3,
      name: 'Transport Allowance',
      type: 'earning',
      calculationType: 'fixed',
      value: 5000,
      taxable: true,
      description: 'Fixed transportation allowance',
      isDefault: true,
      isActive: true
    },
    {
      id: 4,
      name: 'Income Tax',
      type: 'tax',
      calculationType: 'percentage',
      value: 15,
      taxable: false,
      description: 'Standard income tax deduction',
      isDefault: true,
      isActive: true
    },
    {
      id: 5,
      name: 'Health Insurance',
      type: 'deduction',
      calculationType: 'fixed',
      value: 2000,
      taxable: false,
      description: 'Health insurance premium',
      isDefault: true,
      isActive: true
    },
    {
      id: 6,
      name: 'Performance Bonus',
      type: 'earning',
      calculationType: 'formula',
      value: 0,
      formula: '(basicSalary * 0.1) * performanceRating',
      taxable: true,
      description: 'Performance-based bonus calculation',
      isDefault: false,
      isActive: true
    }
  ];

  // Initialize with sample data
  useEffect(() => {
    setComponents(sampleComponents);
  }, []);

  // Form state
  const [formData, setFormData] = useState<Partial<SalaryComponent>>({
    name: '',
    type: 'earning',
    calculationType: 'fixed',
    value: 0,
    formula: '',
    taxable: true,
    description: '',
    isDefault: false,
    isActive: true
  });

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement;
      setFormData({ ...formData, [name]: checkbox.checked });
    } else if (name === 'value' && !isNaN(parseFloat(value))) {
      setFormData({ ...formData, [name]: parseFloat(value) });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  // Open modal for editing
  const handleEdit = (component: SalaryComponent) => {
    setEditingComponent(component);
    setFormData({...component});
    setIsModalOpen(true);
  };

  // Open modal for creating new component
  const handleAddNew = () => {
    setEditingComponent(null);
    setFormData({
      name: '',
      type: 'earning',
      calculationType: 'fixed',
      value: 0,
      formula: '',
      taxable: true,
      description: '',
      isDefault: false,
      isActive: true
    });
    setIsModalOpen(true);
  };

  // Save component (create or update)
  const handleSave = () => {
    if (!formData.name) {
      alert('Component name is required');
      return;
    }

    if (formData.calculationType === 'formula' && !formData.formula) {
      alert('Formula is required for formula-based calculation');
      return;
    }

    if (editingComponent) {
      // Update existing component
      setComponents(components.map(comp => 
        comp.id === editingComponent.id ? { ...formData, id: comp.id } as SalaryComponent : comp
      ));
    } else {
      // Add new component
      const newComponent: SalaryComponent = {
        ...formData,
        id: Math.max(0, ...components.map(c => c.id)) + 1,
      } as SalaryComponent;
      setComponents([...components, newComponent]);
    }

    setIsModalOpen(false);
    setEditingComponent(null);
  };

  // Delete component
  const handleDelete = (id: number) => {
    if (confirm('Are you sure you want to delete this component?')) {
      setComponents(components.filter(comp => comp.id !== id));
    }
  };

  // Toggle component active status
  const toggleActive = (id: number) => {
    setComponents(components.map(comp => 
      comp.id === id ? { ...comp, isActive: !comp.isActive } : comp
    ));
  };

  // Filter components based on active filter and search term
  const filteredComponents = components.filter(comp => {
    const matchesFilter = activeFilter === 'all' || comp.type === activeFilter;
    const matchesSearch = comp.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
                          comp.description?.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  // Component type label and color mapping
  const typeLabels = {
    'earning': { label: 'Earning', color: 'bg-green-100 text-green-800' },
    'deduction': { label: 'Deduction', color: 'bg-red-100 text-red-800' },
    'tax': { label: 'Tax', color: 'bg-blue-100 text-blue-800' },
    'contribution': { label: 'Contribution', color: 'bg-purple-100 text-purple-800' }
  };

  // Calculation type mapping for display
  const calculationTypeDisplay = {
    'fixed': { icon: <DollarSign className="h-4 w-4" />, label: 'Fixed Amount' },
    'percentage': { icon: <Percent className="h-4 w-4" />, label: 'Percentage' },
    'formula': { icon: <Calculator className="h-4 w-4" />, label: 'Formula' }
  };

  return (
    <div className="p-6">
      <div className="flex flex-wrap items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Salary Components</h3>
          <p className="text-sm text-gray-500">Manage earnings, deductions, and calculation methods</p>
        </div>
        
        <div className="flex flex-wrap items-center mt-4 md:mt-0 space-x-0 md:space-x-3 space-y-2 md:space-y-0">
          {/* Search box */}
          <div className="relative w-full md:w-64">
            <input
              type="text"
              placeholder="Search components..."
              className="w-full px-3 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          {/* Filter buttons */}
          <div className="flex space-x-1 w-full md:w-auto">
            <button 
              onClick={() => setActiveFilter('all')}
              className={`px-3 py-2 text-xs font-medium rounded transition-colors ${
                activeFilter === 'all' 
                  ? 'bg-gray-800 text-white' 
                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
              }`}
            >
              All
            </button>
            <button 
              onClick={() => setActiveFilter('earning')}
              className={`px-3 py-2 text-xs font-medium rounded transition-colors ${
                activeFilter === 'earning' 
                  ? 'bg-green-600 text-white' 
                  : 'bg-green-100 text-green-800 hover:bg-green-200'
              }`}
            >
              Earnings
            </button>
            <button 
              onClick={() => setActiveFilter('deduction')}
              className={`px-3 py-2 text-xs font-medium rounded transition-colors ${
                activeFilter === 'deduction' 
                  ? 'bg-red-600 text-white' 
                  : 'bg-red-100 text-red-800 hover:bg-red-200'
              }`}
            >
              Deductions
            </button>
            <button 
              onClick={() => setActiveFilter('tax')}
              className={`px-3 py-2 text-xs font-medium rounded transition-colors ${
                activeFilter === 'tax' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-blue-100 text-blue-800 hover:bg-blue-200'
              }`}
            >
              Taxes
            </button>
          </div>
          
          {/* Add new button */}
          {isAdmin && (
            <button
              onClick={handleAddNew}
              className="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 w-full md:w-auto justify-center"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Component
            </button>
          )}
        </div>
      </div>

      {/* Components table */}
      <div className="overflow-x-auto bg-white rounded-lg shadow">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Calculation
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Value
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Taxable
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredComponents.length > 0 ? (
              filteredComponents.map((component) => (
                <tr key={component.id} className={!component.isActive ? 'bg-gray-50' : ''}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {component.name}
                          {component.isDefault && (
                            <span className="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                              Default
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-500">
                          {component.description}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${typeLabels[component.type].color}`}>
                      {typeLabels[component.type].label}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center text-sm text-gray-900">
                      {calculationTypeDisplay[component.calculationType].icon}
                      <span className="ml-1">{calculationTypeDisplay[component.calculationType].label}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {component.calculationType === 'fixed' ? (
                      <span className="font-mono">{component.value.toLocaleString()}</span>
                    ) : component.calculationType === 'percentage' ? (
                      <span className="font-mono">{component.value}%</span>
                    ) : (
                      <button 
                        onClick={() => {
                          setEditingComponent(component);
                          setFormData({...component});
                          setIsModalOpen(true);
                          setShowFormulaHelper(true);
                        }}
                        className="inline-flex items-center text-xs text-blue-600 hover:text-blue-900"
                      >
                        <Code className="w-3 h-3 mr-1" />
                        View Formula
                      </button>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      component.taxable ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {component.taxable ? 'Yes' : 'No'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      component.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {component.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    {isAdmin && (
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => toggleActive(component.id)}
                          className={`p-1 rounded-full ${
                            component.isActive 
                              ? 'text-red-600 hover:bg-red-100' 
                              : 'text-green-600 hover:bg-green-100'
                          }`}
                          title={component.isActive ? 'Deactivate' : 'Activate'}
                        >
                          {component.isActive ? (
                            <X className="w-4 h-4" />
                          ) : (
                            <Save className="w-4 h-4" />
                          )}
                        </button>
                        <button
                          onClick={() => handleEdit(component)}
                          className="p-1 text-indigo-600 hover:text-indigo-900 hover:bg-indigo-100 rounded-full"
                          title="Edit"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        {!component.isDefault && (
                          <button
                            onClick={() => handleDelete(component.id)}
                            className="p-1 text-red-600 hover:text-red-900 hover:bg-red-100 rounded-full"
                            title="Delete"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    )}
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                  No components found matching your criteria
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Modal for adding/editing components */}
      {isModalOpen && (
        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen p-4">
            <div className="fixed inset-0 bg-black bg-opacity-25" onClick={() => setIsModalOpen(false)}></div>
            
            <div className="relative w-full max-w-2xl bg-white rounded-lg shadow-xl p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {editingComponent ? 'Edit Salary Component' : 'Add New Salary Component'}
                </h3>
                <button
                  onClick={() => setIsModalOpen(false)}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Name */}
                <div className="col-span-1 md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Component Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                    placeholder="e.g., Basic Salary, Medical Allowance"
                  />
                </div>

                {/* Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Component Type <span className="text-red-500">*</span>
                  </label>
                  <select
                    name="type"
                    value={formData.type}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="earning">Earning</option>
                    <option value="deduction">Deduction</option>
                    <option value="tax">Tax</option>
                    <option value="contribution">Contribution</option>
                  </select>
                </div>

                {/* Calculation Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Calculation Method <span className="text-red-500">*</span>
                  </label>
                  <select
                    name="calculationType"
                    value={formData.calculationType}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="fixed">Fixed Amount</option>
                    <option value="percentage">Percentage</option>
                    <option value="formula">Formula Based</option>
                  </select>
                </div>

                {/* Value */}
                {formData.calculationType !== 'formula' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {formData.calculationType === 'fixed' ? 'Amount' : 'Percentage'} <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        {formData.calculationType === 'fixed' ? (
                          <DollarSign className="h-4 w-4 text-gray-400" />
                        ) : (
                          <Percent className="h-4 w-4 text-gray-400" />
                        )}
                      </div>
                      <input
                        type="number"
                        name="value"
                        value={formData.value}
                        onChange={handleInputChange}
                        className="w-full pl-10 px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        required
                        min="0"
                        step={formData.calculationType === 'fixed' ? '1' : '0.01'}
                      />
                    </div>
                  </div>
                )}

                {/* Formula */}
                {formData.calculationType === 'formula' && (
                  <div className="col-span-1 md:col-span-2">
                    <div className="flex justify-between items-center">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Formula <span className="text-red-500">*</span>
                      </label>
                      <button
                        type="button"
                        onClick={() => setShowFormulaHelper(!showFormulaHelper)}
                        className="text-xs font-medium text-blue-600 hover:text-blue-800"
                      >
                        {showFormulaHelper ? 'Hide Help' : 'Show Help'}
                      </button>
                    </div>
                    <textarea
                      name="formula"
                      value={formData.formula}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                      rows={3}
                      placeholder="e.g., basicSalary * 0.1"
                      required
                    ></textarea>
                    
                    {showFormulaHelper && (
                      <div className="mt-2 p-3 bg-blue-50 rounded-md text-xs">
                        <h4 className="font-medium text-blue-700 mb-1">Formula Variables:</h4>
                        <ul className="list-disc list-inside text-gray-700 space-y-1">
                          <li><code className="font-mono bg-blue-100 px-1 rounded">basicSalary</code> - Employee's base salary</li>
                          <li><code className="font-mono bg-blue-100 px-1 rounded">grossSalary</code> - Total of all earnings</li>
                          <li><code className="font-mono bg-blue-100 px-1 rounded">workingDays</code> - Total working days in the period</li>
                          <li><code className="font-mono bg-blue-100 px-1 rounded">presentDays</code> - Days employee was present</li>
                          <li><code className="font-mono bg-blue-100 px-1 rounded">performanceRating</code> - Employee performance rating (0-5)</li>
                        </ul>
                        <h4 className="font-medium text-blue-700 mt-2 mb-1">Operators:</h4>
                        <ul className="list-disc list-inside text-gray-700 space-y-1">
                          <li>Basic arithmetic: <code className="font-mono bg-blue-100 px-1 rounded">+, -, *, /, ()</code></li>
                          <li>Comparison: <code className="font-mono bg-blue-100 px-1 rounded">&gt;, &lt;, &gt;=, &lt;=, ==, !=</code></li>
                          <li>Conditional: <code className="font-mono bg-blue-100 px-1 rounded">condition ? trueValue : falseValue</code></li>
                        </ul>
                        <h4 className="font-medium text-blue-700 mt-2 mb-1">Example:</h4>
                        <code className="font-mono bg-blue-100 px-2 py-1 rounded block">
                          (basicSalary * 0.1) * (performanceRating &gt; 3 ? 1.5 : 1)
                        </code>
                      </div>
                    )}
                  </div>
                )}

                {/* Description */}
                <div className="col-span-1 md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={2}
                    placeholder="Brief description of this salary component"
                  ></textarea>
                </div>

                {/* Checkboxes */}
                <div className="col-span-1 md:col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="taxable"
                      name="taxable"
                      checked={formData.taxable}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="taxable" className="ml-2 block text-sm text-gray-700">
                      Taxable Component
                    </label>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isActive"
                      name="isActive"
                      checked={formData.isActive}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
                      Active Component
                    </label>
                  </div>
                  
                  {isAdmin && (
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="isDefault"
                        name="isDefault"
                        checked={formData.isDefault}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="isDefault" className="ml-2 block text-sm text-gray-700">
                        Default Component
                      </label>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="flex justify-end mt-6 space-x-3">
                <button
                  type="button"
                  onClick={() => setIsModalOpen(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleSave}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  {editingComponent ? 'Update Component' : 'Add Component'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SalaryComponents; 