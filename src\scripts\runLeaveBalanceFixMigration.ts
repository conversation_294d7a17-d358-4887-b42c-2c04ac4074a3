import { AppDataSource } from '../config/database';
import { FixLeaveBalanceDecimals1703900000000 } from '../migrations/1703900000000-FixLeaveBalanceDecimals';

async function runLeaveBalanceFixMigration() {
  try {
    console.log('🚀 Starting leave balance decimal fix migration...');
    
    // Initialize database connection
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ Database connection established');
    }

    // Create migration instance
    const migration = new FixLeaveBalanceDecimals1703900000000();
    
    // Run the migration
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    
    try {
      await migration.up(queryRunner);
      console.log('🎉 Leave balance decimal fix migration completed successfully!');
    } finally {
      await queryRunner.release();
    }

    // Close database connection
    await AppDataSource.destroy();
    console.log('✅ Database connection closed');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  runLeaveBalanceFixMigration()
    .then(() => {
      console.log('✅ Migration script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration script failed:', error);
      process.exit(1);
    });
}

export { runLeaveBalanceFixMigration }; 