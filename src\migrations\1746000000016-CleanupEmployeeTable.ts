import { MigrationInterface, QueryRunner } from "typeorm";

export class CleanupEmployeeTable1746000000016 implements MigrationInterface {
    name = 'CleanupEmployeeTable1746000000016'

    public async up(queryRunner: QueryRunner): Promise<void> {
        try {
            // Make a backup of the original employees table just in case
            await queryRunner.query(`
                CREATE TABLE employees_backup LIKE employees;
                INSERT INTO employees_backup SELECT * FROM employees;
            `);
            console.log("Created backup of employees table as employees_backup");
            
            // Drop columns that have been migrated to other tables
            // Contact Info
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`mobileNumber\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`officialNumber\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`officialEmail\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`personalEmail\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`permanentAddress\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`currentAddress\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`emergencyContactName\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`emergencyContactPhone\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`emergencyContactRelationship\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`linkedinProfile\``);
            
            // Job Details
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`designation\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`department\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`project\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`location\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`employmentType\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`employmentStatus\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`employeeLevel\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`joinDate\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`probationEndDate\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`noticePeriod\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`reportingTo\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`previousEmployeeId\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`remoteWorkEligible\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`nextReviewDate\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`trainingRequirements\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`workSchedule\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`shiftType\``);
            
            // Benefits & Compensation
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`totalSalary\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`salaryTier\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`cashAmount\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`bankAmount\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`paymentMode\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`foodAllowanceInSalary\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`fuelAllowanceInSalary\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`numberOfMeals\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`fuelInLiters\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`fuelAmount\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`foodProvidedByCompany\``);
            
            // Bank Details
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`bankName\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`bankBranch\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`accountNumber\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`accountTitle\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`iban\``);
            
            // Vehicle 
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`vehicleType\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`registrationNumber\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`providedByCompany\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`handingOverDate\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`returnDate\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`vehicleMakeModel\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`vehicleColor\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`mileageAtIssuance\``);
            
            // Accommodation
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`accommodationProvidedByEmployer\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`accommodationType\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`accommodationAddress\``);
            
            // Health & Insurance
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`healthInsuranceProvider\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`healthInsurancePolicyNumber\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`healthInsuranceExpiryDate\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`lifeInsuranceProvider\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`lifeInsurancePolicyNumber\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`lifeInsuranceExpiryDate\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`vaccinationRecords\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`medicalHistory\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`bloodGroup\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`allergies\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`chronicConditions\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`regularMedications\``);
            
            // Family
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`spouseName\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`spouseDateOfBirth\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`spouseOccupation\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`spouseEmployer\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`spouseContactNumber\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`spouseCNIC\``);
            
            // JSON fields
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`educationEntries\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`experienceEntries\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`deviceEntries\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`children\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`dependents\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`documents\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`requiredDocuments\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`projectEntries\``);
            
            // Misc
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`otherSocialProfiles\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`professionalMemberships\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`hobbiesInterests\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`professionalSkills\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`technicalSkills\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`certifications\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`languages\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`notes\``);
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN IF EXISTS \`specialInstructions\``);
            
            console.log("Successfully cleaned up employee table");
            
        } catch (error) {
            console.error("Error in CleanupEmployeeTable migration:", error);
            throw error;
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        console.log("To restore the original employee table structure, restore from the employees_backup table.");
        console.log("This migration cannot be automatically reverted due to potential data loss.");
    }
} 