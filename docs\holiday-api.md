# Holiday Management API Documentation

This document describes the REST API endpoints for managing holidays and holiday configurations in the IT Management System.

## Base URL
```
/api/holidays
```

## Authentication
All endpoints require proper authentication. Include your authentication token in the request headers.

## Endpoints

### 1. Get All Holidays
**GET** `/api/holidays`

**Query Parameters:**
- `year` (optional): Filter holidays by year (e.g., 2024)
- `type` (optional): Filter by holiday type (`public`, `company`, `optional`)
- `isActive` (optional): Filter by active status (`true`, `false`)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "New Year's Day",
      "date": "2024-01-01",
      "type": "public",
      "description": "First day of the year",
      "isActive": true,
      "createdBy": "admin",
      "createdAt": "2023-12-01T10:00:00Z",
      "updatedAt": "2023-12-01T10:00:00Z"
    }
  ],
  "count": 1
}
```

### 2. Get Single Holiday
**GET** `/api/holidays/:id`

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "New Year's Day",
    "date": "2024-01-01",
    "type": "public",
    "description": "First day of the year",
    "isActive": true
  }
}
```

### 3. Create Holiday
**POST** `/api/holidays`

**Request Body:**
```json
{
  "name": "Independence Day",
  "date": "2024-07-04",
  "type": "public",
  "description": "National Independence Day"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "name": "Independence Day",
    "date": "2024-07-04",
    "type": "public",
    "description": "National Independence Day",
    "isActive": true,
    "createdBy": "admin",
    "createdAt": "2023-12-01T10:00:00Z"
  },
  "message": "Holiday created successfully"
}
```

### 4. Update Holiday
**PUT** `/api/holidays/:id`

**Request Body:**
```json
{
  "name": "Updated Holiday Name",
  "description": "Updated description"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "name": "Updated Holiday Name",
    "date": "2024-07-04",
    "type": "public",
    "description": "Updated description",
    "isActive": true,
    "updatedBy": "admin",
    "updatedAt": "2023-12-01T11:00:00Z"
  },
  "message": "Holiday updated successfully"
}
```

### 5. Delete Holiday
**DELETE** `/api/holidays/:id`

**Response:**
```json
{
  "success": true,
  "message": "Holiday deleted successfully"
}
```

### 6. Toggle Holiday Status
**PATCH** `/api/holidays/:id/toggle-status`

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "name": "Independence Day",
    "isActive": false
  },
  "message": "Holiday deactivated successfully"
}
```

### 7. Get Upcoming Holidays
**GET** `/api/holidays/upcoming`

**Query Parameters:**
- `limit` (optional): Number of holidays to return (default: 10)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 3,
      "name": "Christmas Day",
      "date": "2024-12-25",
      "type": "public",
      "description": "Christian holiday",
      "isActive": true
    }
  ],
  "count": 1
}
```

### 8. Bulk Import Holidays
**POST** `/api/holidays/import`

**Request Body:**
```json
{
  "holidays": [
    {
      "name": "Labor Day",
      "date": "2024-05-01",
      "type": "public",
      "description": "International Workers' Day"
    },
    {
      "name": "Company Anniversary",
      "date": "2024-06-15",
      "type": "company",
      "description": "Company founding anniversary"
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 4,
      "name": "Labor Day",
      "date": "2024-05-01",
      "type": "public"
    }
  ],
  "message": "Successfully imported 2 holidays",
  "summary": {
    "total": 2,
    "imported": 2,
    "failed": 0
  }
}
```

## Holiday Configuration Endpoints

### 9. Get Holiday Configuration
**GET** `/api/holidays/config`

**Query Parameters:**
- `organizationId` (optional): Organization identifier (default: 'default')

**Response:**
```json
{
  "success": true,
  "data": {
    "holidays": [
      {
        "id": 1,
        "name": "New Year's Day",
        "date": "2024-01-01",
        "type": "public"
      }
    ],
    "weekendDays": [0, 6],
    "timezone": "UTC",
    "workingHours": {
      "start": "09:00",
      "end": "17:00",
      "breakDuration": 60
    },
    "holidaySettings": {
      "allowOptionalHolidays": true,
      "maxOptionalHolidaysPerYear": 3,
      "requireApprovalForOptional": true
    }
  }
}
```

### 10. Save Holiday Configuration
**POST** `/api/holidays/config`

**Request Body:**
```json
{
  "organizationId": "default",
  "weekendDays": [0, 6],
  "timezone": "America/New_York",
  "workingHours": {
    "start": "08:00",
    "end": "16:00",
    "breakDuration": 30
  },
  "holidaySettings": {
    "allowOptionalHolidays": true,
    "maxOptionalHolidaysPerYear": 5,
    "requireApprovalForOptional": false
  },
  "holidays": [
    {
      "name": "New Holiday",
      "date": "2024-08-15",
      "type": "company"
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "configuration": {
      "id": 1,
      "organizationId": "default",
      "weekendDays": [0, 6],
      "timezone": "America/New_York"
    },
    "holidays": [
      {
        "id": 5,
        "name": "New Holiday",
        "date": "2024-08-15",
        "type": "company"
      }
    ]
  },
  "message": "Holiday configuration saved successfully"
}
```

## Error Responses

All endpoints return error responses in the following format:

```json
{
  "success": false,
  "error": "Error message",
  "details": "Additional error details (optional)"
}
```

### Common Error Codes:
- **400**: Bad Request - Invalid input data
- **404**: Not Found - Holiday or configuration not found
- **409**: Conflict - Duplicate holiday on the same date
- **500**: Internal Server Error - Server-side error

## Data Types

### Holiday Types
- `public`: National or public holidays
- `company`: Company-specific holidays
- `optional`: Optional holidays that employees can choose to observe

### Weekend Days
Weekend days are represented as numbers:
- 0: Sunday
- 1: Monday
- 2: Tuesday
- 3: Wednesday
- 4: Thursday
- 5: Friday
- 6: Saturday

## Usage Examples

### Frontend Integration with HolidayService

```typescript
// Fetch all holidays for current year
const response = await HolidayService.getHolidays(new Date().getFullYear());

// Create a new holiday
const newHoliday = await HolidayService.createHoliday({
  name: "Team Outing Day",
  date: "2024-09-15",
  type: "company",
  description: "Annual team building event"
});

// Get holiday configuration
const config = await HolidayService.getHolidayConfiguration();

// Save updated configuration
await HolidayService.saveHolidayConfiguration({
  holidays: updatedHolidays,
  weekendDays: [5, 6], // Friday-Saturday weekend
  timezone: "Asia/Dubai"
});
```

## Database Schema

### holidays table
```sql
CREATE TABLE holidays (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  date DATE NOT NULL,
  type ENUM('public', 'company', 'optional') DEFAULT 'public',
  description TEXT,
  isActive BOOLEAN DEFAULT TRUE,
  createdBy VARCHAR(100),
  updatedBy VARCHAR(100),
  metadata JSON,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### holiday_configurations table
```sql
CREATE TABLE holiday_configurations (
  id INT PRIMARY KEY AUTO_INCREMENT,
  organizationId VARCHAR(100) DEFAULT 'default',
  name VARCHAR(100) DEFAULT 'Global Configuration',
  weekendDays JSON NOT NULL,
  timezone VARCHAR(50) DEFAULT 'UTC',
  workingHours JSON,
  holidaySettings JSON,
  applicableRegions JSON,
  applicableDepartments JSON,
  isActive BOOLEAN DEFAULT TRUE,
  createdBy VARCHAR(100),
  updatedBy VARCHAR(100),
  notes TEXT,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
``` 