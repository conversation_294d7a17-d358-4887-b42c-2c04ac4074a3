import { MigrationInterface, QueryRunner, Table, Index, TableForeignKey } from 'typeorm';

export class CreateLeaveManagementTables1703000000000 implements MigrationInterface {
  name = 'CreateLeaveManagementTables1703000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create leave_policies table
    await queryRunner.createTable(
      new Table({
        name: 'leave_policies',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment'
          },
          {
            name: 'leaveType',
            type: 'enum',
            enum: [
              'ANNUAL',
              'SICK',
              'PERSONAL',
              'UNPAID',
              'MATERNITY',
              'PATERNITY',
              'BEREAVEMENT',
              'COMP_OFF',
              'OTHER'
            ],
            isUnique: true
          },
          {
            name: 'name',
            type: 'varchar',
            length: '255'
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true
          },
          {
            name: 'defaultAllocation',
            type: 'decimal',
            precision: 10,
            scale: 2
          },
          {
            name: 'accrualPeriodDays',
            type: 'int',
            default: 365
          },
          {
            name: 'maxCarryForward',
            type: 'decimal',
            precision: 10,
            scale: 2,
            default: 0
          },
          {
            name: 'canCarryForward',
            type: 'boolean',
            default: false
          },
          {
            name: 'carryForwardExpiryDays',
            type: 'int',
            isNullable: true
          },
          {
            name: 'minimumServiceDays',
            type: 'int',
            default: 0
          },
          {
            name: 'waitingPeriodDays',
            type: 'int',
            default: 0
          },
          {
            name: 'minimumApplicationDays',
            type: 'int',
            default: 1
          },
          {
            name: 'maximumConsecutiveDays',
            type: 'int',
            isNullable: true
          },
          {
            name: 'requiresApproval',
            type: 'boolean',
            default: true
          },
          {
            name: 'requiresDocumentation',
            type: 'boolean',
            default: false
          },
          {
            name: 'countsWeekends',
            type: 'boolean',
            default: true
          },
          {
            name: 'countsHolidays',
            type: 'boolean',
            default: true
          },
          {
            name: 'canApplyOnWeekends',
            type: 'boolean',
            default: false
          },
          {
            name: 'canApplyOnHolidays',
            type: 'boolean',
            default: false
          },
          {
            name: 'isPaid',
            type: 'boolean',
            default: false
          },
          {
            name: 'isEmergencyLeave',
            type: 'boolean',
            default: false
          },
          {
            name: 'accrualRate',
            type: 'decimal',
            precision: 5,
            scale: 2,
            default: 1.0
          },
          {
            name: 'eligibilityCriteria',
            type: 'text',
            isNullable: true
          },
          {
            name: 'approvalWorkflow',
            type: 'text',
            isNullable: true
          },
          {
            name: 'restrictedDates',
            type: 'text',
            isNullable: true
          },
          {
            name: 'documentationRequired',
            type: 'text',
            isNullable: true
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true
          },
          {
            name: 'sortOrder',
            type: 'int',
            isNullable: true
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP'
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
          }
        ]
      })
    );

    // Create leave_balances table
    await queryRunner.createTable(
      new Table({
        name: 'leave_balances',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment'
          },
          {
            name: 'employeeId',
            type: 'int'
          },
          {
            name: 'leaveType',
            type: 'enum',
            enum: [
              'ANNUAL',
              'SICK',
              'PERSONAL',
              'UNPAID',
              'MATERNITY',
              'PATERNITY',
              'BEREAVEMENT',
              'COMP_OFF',
              'OTHER'
            ]
          },
          {
            name: 'year',
            type: 'int'
          },
          {
            name: 'totalAllocated',
            type: 'decimal',
            precision: 10,
            scale: 2,
            default: 0
          },
          {
            name: 'used',
            type: 'decimal',
            precision: 10,
            scale: 2,
            default: 0
          },
          {
            name: 'pending',
            type: 'decimal',
            precision: 10,
            scale: 2,
            default: 0
          },
          {
            name: 'carriedForward',
            type: 'decimal',
            precision: 10,
            scale: 2,
            default: 0
          },
          {
            name: 'lapsed',
            type: 'decimal',
            precision: 10,
            scale: 2,
            default: 0
          },
          {
            name: 'expiryDate',
            type: 'date',
            isNullable: true
          },
          {
            name: 'notes',
            type: 'text',
            isNullable: true
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP'
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
          }
        ]
      })
    );

    // Create leave_requests table
    await queryRunner.createTable(
      new Table({
        name: 'leave_requests',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment'
          },
          {
            name: 'employeeId',
            type: 'int'
          },
          {
            name: 'leaveType',
            type: 'enum',
            enum: [
              'ANNUAL',
              'SICK',
              'PERSONAL',
              'UNPAID',
              'MATERNITY',
              'PATERNITY',
              'BEREAVEMENT',
              'COMP_OFF',
              'OTHER'
            ]
          },
          {
            name: 'startDate',
            type: 'date'
          },
          {
            name: 'endDate',
            type: 'date'
          },
          {
            name: 'daysRequested',
            type: 'int'
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['PENDING', 'APPROVED', 'REJECTED', 'CANCELLED'],
            default: "'PENDING'"
          },
          {
            name: 'reason',
            type: 'text'
          },
          {
            name: 'approverId',
            type: 'int',
            isNullable: true
          },
          {
            name: 'approverName',
            type: 'varchar',
            length: '255',
            isNullable: true
          },
          {
            name: 'approverComments',
            type: 'text',
            isNullable: true
          },
          {
            name: 'approvedAt',
            type: 'datetime',
            isNullable: true
          },
          {
            name: 'rejectedAt',
            type: 'datetime',
            isNullable: true
          },
          {
            name: 'attachments',
            type: 'text',
            isNullable: true
          },
          {
            name: 'isEmergencyLeave',
            type: 'boolean',
            default: false
          },
          {
            name: 'emergencyContact',
            type: 'text',
            isNullable: true
          },
          {
            name: 'delegatedTo',
            type: 'varchar',
            length: '255',
            isNullable: true
          },
          {
            name: 'handoverNotes',
            type: 'text',
            isNullable: true
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP'
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
          }
        ]
      })
    );

    // Create indexes using SQL
    await queryRunner.query(`CREATE UNIQUE INDEX IDX_leave_balances_employee_type_year ON leave_balances (employeeId, leaveType, year)`);
    await queryRunner.query(`CREATE INDEX IDX_leave_balances_employee ON leave_balances (employeeId)`);
    await queryRunner.query(`CREATE INDEX IDX_leave_balances_year ON leave_balances (year)`);
    await queryRunner.query(`CREATE INDEX IDX_leave_requests_employee ON leave_requests (employeeId)`);
    await queryRunner.query(`CREATE INDEX IDX_leave_requests_status ON leave_requests (status)`);
    await queryRunner.query(`CREATE INDEX IDX_leave_requests_dates ON leave_requests (startDate, endDate)`);
    await queryRunner.query(`CREATE INDEX IDX_leave_requests_approver ON leave_requests (approverId)`);
    await queryRunner.query(`CREATE INDEX IDX_leave_policies_active ON leave_policies (isActive)`);

    // Create foreign key constraints using SQL
    await queryRunner.query(`ALTER TABLE leave_balances ADD CONSTRAINT FK_leave_balances_employee FOREIGN KEY (employeeId) REFERENCES users(id) ON DELETE CASCADE`);
    await queryRunner.query(`ALTER TABLE leave_requests ADD CONSTRAINT FK_leave_requests_employee FOREIGN KEY (employeeId) REFERENCES users(id) ON DELETE CASCADE`);
    await queryRunner.query(`ALTER TABLE leave_requests ADD CONSTRAINT FK_leave_requests_approver FOREIGN KEY (approverId) REFERENCES users(id) ON DELETE SET NULL`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign keys first
    const leaveRequestsTable = await queryRunner.getTable('leave_requests');
    const leaveBalancesTable = await queryRunner.getTable('leave_balances');

    if (leaveRequestsTable) {
      const foreignKeys = leaveRequestsTable.foreignKeys;
      for (const foreignKey of foreignKeys) {
        await queryRunner.dropForeignKey('leave_requests', foreignKey);
      }
    }

    if (leaveBalancesTable) {
      const foreignKeys = leaveBalancesTable.foreignKeys;
      for (const foreignKey of foreignKeys) {
        await queryRunner.dropForeignKey('leave_balances', foreignKey);
      }
    }

    // Drop tables
    await queryRunner.dropTable('leave_requests');
    await queryRunner.dropTable('leave_balances');
    await queryRunner.dropTable('leave_policies');
  }
} 