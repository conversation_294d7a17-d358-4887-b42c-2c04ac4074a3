import { Repository, DataSource, FindOptionsWhere } from 'typeorm';
import { Shift } from '../server/entities/Shift';
import { ShiftAssignment } from '../server/entities/ShiftAssignment';

export class ShiftRepository {
  private shiftRepository: Repository<Shift>;
  private shiftAssignmentRepository: Repository<ShiftAssignment>;

  constructor(dataSource: DataSource) {
    this.shiftRepository = dataSource.getRepository(Shift);
    this.shiftAssignmentRepository = dataSource.getRepository(ShiftAssignment);
  }

  // Shift CRUD operations
  async createShift(shiftData: Partial<Shift>): Promise<Shift> {
    const shift = this.shiftRepository.create(shiftData);
    return await this.shiftRepository.save(shift);
  }

  async getAllShifts(): Promise<Shift[]> {
    return await this.shiftRepository.find({
      order: { name: 'ASC' }
    });
  }

  async getActiveShifts(): Promise<Shift[]> {
    return await this.shiftRepository.find({
      where: { isActive: true },
      order: { name: 'ASC' }
    });
  }

  async getShiftById(id: number): Promise<Shift | null> {
    return await this.shiftRepository.findOne({
      where: { id }
    });
  }

  async updateShift(id: number, updateData: Partial<Shift>): Promise<Shift | null> {
    await this.shiftRepository.update(id, updateData);
    return await this.getShiftById(id);
  }

  async deleteShift(id: number): Promise<boolean> {
    // Check if shift is assigned to any employee
    const assignmentCount = await this.shiftAssignmentRepository.count({
      where: { shiftId: id, isActive: true }
    });

    if (assignmentCount > 0) {
      throw new Error('Cannot delete shift that is assigned to employees');
    }

    const result = await this.shiftRepository.delete(id);
    return (result.affected ?? 0) > 0;
  }

  async toggleShiftStatus(id: number): Promise<Shift | null> {
    const shift = await this.getShiftById(id);
    if (!shift) {
      throw new Error('Shift not found');
    }

    return await this.updateShift(id, { isActive: !shift.isActive });
  }

  // Shift Assignment operations
  async assignShiftToEmployee(assignmentData: Partial<ShiftAssignment>): Promise<ShiftAssignment> {
    // Check if employee already has an active assignment for the same period
    const existingAssignment = await this.shiftAssignmentRepository.findOne({
      where: {
        employeeId: assignmentData.employeeId,
        isActive: true,
        ...(assignmentData.startDate && {
          startDate: assignmentData.startDate
        })
      }
    });

    if (existingAssignment) {
      // End the existing assignment
      await this.shiftAssignmentRepository.update(existingAssignment.id, {
        isActive: false,
        endDate: assignmentData.startDate
      });
    }

    const assignment = this.shiftAssignmentRepository.create(assignmentData);
    return await this.shiftAssignmentRepository.save(assignment);
  }

  async getEmployeeShiftAssignments(employeeId: number): Promise<ShiftAssignment[]> {
    return await this.shiftAssignmentRepository.find({
      where: { employeeId },
      relations: ['shift'],
      order: { startDate: 'DESC' }
    });
  }

  async getCurrentEmployeeShift(employeeId: number, date?: string): Promise<ShiftAssignment | null> {
    const currentDate = date || new Date().toISOString().split('T')[0];
    
    return await this.shiftAssignmentRepository
      .createQueryBuilder('assignment')
      .leftJoinAndSelect('assignment.shift', 'shift')
      .where('assignment.employeeId = :employeeId', { employeeId })
      .andWhere('assignment.isActive = true')
      .andWhere('assignment.startDate <= :currentDate', { currentDate })
      .andWhere('(assignment.endDate IS NULL OR assignment.endDate >= :currentDate)', { currentDate })
      .orderBy('assignment.startDate', 'DESC')
      .getOne();
  }

  async getShiftAssignments(shiftId: number): Promise<ShiftAssignment[]> {
    return await this.shiftAssignmentRepository.find({
      where: { shiftId },
      relations: ['employee'],
      order: { startDate: 'DESC' }
    });
  }

  async updateShiftAssignment(id: number, updateData: Partial<ShiftAssignment>): Promise<ShiftAssignment | null> {
    await this.shiftAssignmentRepository.update(id, updateData);
    return await this.shiftAssignmentRepository.findOne({
      where: { id },
      relations: ['shift', 'employee']
    });
  }

  async endShiftAssignment(id: number, endDate?: string): Promise<ShiftAssignment | null> {
    const currentDate = endDate || new Date().toISOString().split('T')[0];
    return await this.updateShiftAssignment(id, {
      endDate: currentDate,
      isActive: false
    });
  }

  async deleteShiftAssignment(id: number): Promise<boolean> {
    const result = await this.shiftAssignmentRepository.delete(id);
    return (result.affected ?? 0) > 0;
  }

  // Utility methods
  async getEmployeesWithoutShift(): Promise<number[]> {
    const result = await this.shiftAssignmentRepository
      .createQueryBuilder('assignment')
      .select('DISTINCT assignment.employeeId', 'employeeId')
      .where('assignment.isActive = true')
      .getRawMany();

    const assignedEmployeeIds = result.map(row => row.employeeId);
    
    // This would need to be combined with employee repository to get actual unassigned employees
    return assignedEmployeeIds;
  }

  async getShiftStatistics(): Promise<{
    totalShifts: number;
    activeShifts: number;
    totalAssignments: number;
    activeAssignments: number;
  }> {
    const [totalShifts, activeShifts, totalAssignments, activeAssignments] = await Promise.all([
      this.shiftRepository.count(),
      this.shiftRepository.count({ where: { isActive: true } }),
      this.shiftAssignmentRepository.count(),
      this.shiftAssignmentRepository.count({ where: { isActive: true } })
    ]);

    return {
      totalShifts,
      activeShifts,
      totalAssignments,
      activeAssignments
    };
  }
} 