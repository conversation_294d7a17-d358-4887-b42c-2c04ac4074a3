import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';
import api from '../services/api';
import { 
  User, Mail, Lock, Shield, Building2, FileText, MapPin, AlertCircle, MoreVertical, Settings,
  Users, UserCheck, UserX, Filter, Search, ChevronDown, ChevronUp, ChevronsUpDown,
  ChevronLeft, ChevronRight, Activity, Eye, EyeOff, Globe, Moon, Sun
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useDebounce } from 'use-debounce';

// Add these styles for animations
import './animations.css';

interface User {
  id: string;
  name: string;
  email: string;
  department: string;
  isActive: boolean;
  permissions: {
    canCreateTickets: boolean;
    canCreateTicketsForOthers: boolean;
    canEditTickets: boolean;
    canDeleteTickets: boolean;
    canCloseTickets: boolean;
    canLockTickets: boolean;
    canAssignTickets: boolean;
    canEscalateTickets: boolean;
    canViewAllTickets: boolean;
  };
  oversightPermissions: {
    departments: string[];
    projects: string[];
    locations: string[];
    hasFullAccess: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
  project: string;
  location: string;
  role: string;
}

const DEPARTMENTS = [
  'CSD',
  'FINANCE',
  'HR',
  'IT',
  'LAND',
  'LEGAL',
  'MANAGEMENT',
  'MARKETING',
  'OPERATIONS',
  'PND',
  'SALES'
];

const PROJECTS = [
  'Eurobiz Corporations',
  'Guardian International',
  'Guardian Developers',
  'Grand Developers',
  'ARD Developers'
] as const;

const PROJECT_LOCATIONS: Record<string, string[]> = {
  'Eurobiz Corporations': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Kharian'
  ],
  'Guardian Developers': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Arifwala'
  ],
  'Guardian International': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Vehari'
  ],
  'Grand Developers': [
    'Grand City Head Office Lahore',
    'Grand City Site Office Faisalabad',
    'Grand City Site Office Murree'
  ],
  'ARD Developers': [
    'ARD Head Office Lahore',
    'ARD Site Office RUDA'
  ]
};

// Update the HrUser interface to include all possible property names
interface HrUser {
  id?: string;
  employeeId?: string;
  firstName?: string;
  lastName?: string;
  name?: string;
  fullName?: string;
  email?: string;
  officialEmail?: string;
  personalEmail?: string;
  department?: string;
  dept?: string;
  project?: string;
  location?: string;
}

function UserActionsMenu({ user, onEdit, onToggleStatus, onDelete, isDeleting, isOpen, onToggle, isDarkMode }: {
  user: User;
  onEdit: () => void;
  onToggleStatus: () => void;
  onDelete: () => void;
  isDeleting: boolean;
  isOpen: boolean;
  onToggle: () => void;
  isDarkMode: boolean;
}) {
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onToggle();
      }
    }
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, onToggle]);

  return (
    <div className="relative">
      <button
        onClick={(e) => {
          e.stopPropagation();
          onToggle();
        }}
        className={`p-2 ${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'} rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1`}
        disabled={isDeleting}
        aria-label="User actions menu"
      >
        <Settings className={`h-5 w-5 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
      </button>
      {isOpen && (
        <div
          ref={menuRef}
          className={`origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg ${
            isDarkMode ? 'bg-gray-800 ring-gray-700' : 'bg-white ring-black'
          } ring-1 ring-opacity-5 focus:outline-none z-50`}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="py-1" role="menu" aria-orientation="vertical">
            <button
              onClick={() => {
                onEdit();
                onToggle();
              }}
              className={`group flex items-center w-full px-4 py-2 text-sm ${
                isDarkMode
                  ? 'text-gray-300 hover:bg-gray-700 hover:text-gray-100'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
              }`}
              disabled={isDeleting}
              role="menuitem"
            >
              <User className={`mr-3 h-4 w-4 ${
                isDarkMode ? 'text-gray-400 group-hover:text-gray-300' : 'text-gray-400 group-hover:text-gray-500'
              }`} />
              Edit
            </button>
            <button
              onClick={() => {
                onToggleStatus();
                onToggle();
              }}
              className={`group flex items-center w-full px-4 py-2 text-sm ${
                isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
              } ${
                user.isActive
                  ? isDarkMode ? 'text-orange-400 hover:text-orange-300' : 'text-orange-700 hover:text-orange-900'
                  : isDarkMode ? 'text-green-400 hover:text-green-300' : 'text-green-700 hover:text-green-900'
              }`}
              disabled={isDeleting}
              role="menuitem"
            >
              <Shield className={`mr-3 h-4 w-4 ${
                isDarkMode ? 'text-gray-400 group-hover:text-gray-300' : 'text-gray-400 group-hover:text-gray-500'
              }`} />
              {user.isActive ? 'Deactivate' : 'Activate'}
            </button>
            <button
              onClick={() => {
                onDelete();
                onToggle();
              }}
              className={`group flex items-center w-full px-4 py-2 text-sm ${
                isDarkMode
                  ? 'text-red-400 hover:bg-red-900 hover:text-red-300'
                  : 'text-red-700 hover:bg-red-100 hover:text-red-900'
              }`}
              disabled={isDeleting}
              role="menuitem"
            >
              <AlertCircle className={`mr-3 h-4 w-4 ${
                isDarkMode ? 'text-red-400 group-hover:text-red-300' : 'text-red-400 group-hover:text-red-500'
              }`} />
              Delete
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export function UserManagement() {
  const { user: currentUser } = useAuth();
  const { isDarkMode, toggleDarkMode } = useTheme();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [newUser, setNewUser] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    project: '',
    location: '',
    department: 'IT',
    oversightPermissions: {
      departments: [] as string[],
      projects: [] as string[],
      locations: [] as string[],
      hasFullAccess: false
    }
  });
  const [filters, setFilters] = useState({
    search: '',
    department: '',
    status: '',
    project: '',
    location: ''
  });
  const [sorting, setSorting] = useState({
    column: 'name',
    direction: 'asc' as 'asc' | 'desc'
  });
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    total: 0,
    limit: 15
  });
  const [showPassword, setShowPassword] = useState(false);
  const [createMode, setCreateMode] = useState<'manual' | 'hr'>('manual');
  const [selectedHrUser, setSelectedHrUser] = useState<string>('');
  const [hrUsers, setHrUsers] = useState<HrUser[]>([]);
  const [isLoadingHrUsers, setIsLoadingHrUsers] = useState(false);
  const [hrUsersError, setHrUsersError] = useState('');
  const [availableDepartments, setAvailableDepartments] = useState<string[]>([]);
  const [activeDropdownUserId, setActiveDropdownUserId] = useState<string | null>(null);

  // Debounce search filter
  const debouncedSearch = useDebounce(filters.search, 300);

  // Handle dropdown toggle - only one dropdown can be open at a time
  const handleDropdownToggle = (userId: string) => {
    setActiveDropdownUserId(prev => prev === userId ? null : userId);
  };

  // Close all dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Check if the click was inside any dropdown menu
      const target = event.target as Element;
      if (!target.closest('[data-dropdown-menu]') && !target.closest('[data-dropdown-trigger]')) {
        setActiveDropdownUserId(null);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  const loadUsers = async () => {
    const controller = new AbortController();

    try {
      setLoading(true);
      setError('');
      
      const params = new URLSearchParams({
        page: pagination.currentPage.toString(),
        limit: pagination.limit.toString(),
        ...(filters.search && { search: filters.search }),
        ...(filters.department !== 'all' && filters.department !== '' && { department: filters.department }),
        ...(filters.status !== '' && { status: filters.status }),
        ...(filters.project !== '' && { project: filters.project }),
        ...(filters.location !== '' && { location: filters.location })
      });

      const response = await api.get(`/users?${params}`, {
        signal: controller.signal
      });

      if (response.data.users) {
        setUsers(response.data.users);
        setPagination(prev => ({
          ...prev,
          total: response.data.pagination.total,
          totalPages: response.data.pagination.totalPages,
          currentPage: response.data.pagination.currentPage,
          limit: response.data.pagination.itemsPerPage
        }));
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error: any) {
      // Ignore aborted requests
      if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {
        return;
      }

      const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch users';
      setError(errorMessage);
      
      // Keep existing data if available
      if (!users.length) {
        setUsers([]);
        setPagination(prev => ({
          ...prev,
          total: 0,
          totalPages: 1,
          currentPage: 1
        }));
      }
    } finally {
        setLoading(false);
      }
    
    return () => {
      controller.abort();
    };
  };

  useEffect(() => {
    const controller = new AbortController();
    let isMounted = true;

    const loadUsers = async () => {
      try {
        setLoading(true);
        setError('');
        
        const params = new URLSearchParams({
          page: pagination.currentPage.toString(),
          limit: pagination.limit.toString(),
          ...(filters.search && { search: filters.search }),
          ...(filters.department !== 'all' && filters.department !== '' && { department: filters.department }),
          ...(filters.status !== '' && { status: filters.status }),
          ...(filters.project !== '' && { project: filters.project }),
          ...(filters.location !== '' && { location: filters.location })
        });

        const response = await api.get(`/users?${params}`, {
          signal: controller.signal
        });

        if (!isMounted) return;

        if (response.data.users) {
          setUsers(response.data.users);
          setPagination(prev => ({
            ...prev,
            total: response.data.pagination.total,
            totalPages: response.data.pagination.totalPages,
            currentPage: response.data.pagination.currentPage,
            limit: response.data.pagination.itemsPerPage
          }));
        } else {
          throw new Error('Invalid response format');
        }
      } catch (error: any) {
        if (!isMounted) return;
        
        // Ignore aborted requests
        if (error.name === 'AbortError' || error.code === 'ERR_CANCELED') {
          return;
        }

        const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch users';
        setError(errorMessage);
        
        // Keep existing data if available
        if (!users.length) {
          setUsers([]);
          setPagination(prev => ({
            ...prev,
            total: 0,
            totalPages: 1,
            currentPage: 1
          }));
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    // Add delay between rapid filter/page changes
    const timeoutId = setTimeout(() => {
      loadUsers();
    }, 300);

    return () => {
      isMounted = false;
      controller.abort();
      clearTimeout(timeoutId);
    };
  }, [pagination.currentPage, pagination.limit, filters.search, filters.department, filters.status, filters.project, filters.location]);

  // Add error clear handler
  const clearError = () => {
    setError('');
  };

  // Update filter handling to clear error
  const handleFilterChange = (key: string, value: string) => {
    clearError(); // Clear error when changing filters
    
    // Special handling for project selection affecting location filter
    if (key === 'project') {
      setFilters(prev => {
        // If project changes, we may need to reset location if it's not valid for this project
        let updatedLocation = prev.location;
        
        // If project is set and location doesn't belong to this project, reset location
        if (value && prev.location && !PROJECT_LOCATIONS[value]?.includes(prev.location)) {
          updatedLocation = '';
        }
        
        // If project is cleared, also clear location
        if (!value) {
          updatedLocation = '';
        }
        
        return { 
          ...prev, 
          [key]: value,
          location: updatedLocation 
        };
      });
    } else {
      // Normal filter update for other filters
      setFilters(prev => ({ ...prev, [key]: value }));
    }
    
    setPagination(prev => ({ ...prev, currentPage: 1 }));
  };

  // Update page change handling
  const handlePageChange = (pageNumber: number) => {
    clearError(); // Clear error when changing page
    setPagination(prev => ({ ...prev, currentPage: pageNumber }));
  };

  // Add error display component
  const ErrorDisplay = () => {
    if (!error) return null;

    return (
      <div className={`${isDarkMode ? 'bg-red-900/20 border-red-800' : 'bg-red-50 border-red-200'} border rounded-xl p-4 mb-5 flex items-center justify-between shadow-sm`}>
        <div className="flex items-center gap-3">
          <div className={`${isDarkMode ? 'bg-red-800' : 'bg-red-100'} p-2 rounded-lg`}>
            <AlertCircle className={`h-5 w-5 ${isDarkMode ? 'text-red-300' : 'text-red-600'}`} />
          </div>
          <p className={`${isDarkMode ? 'text-red-300' : 'text-red-700'} font-medium`}>{error}</p>
        </div>
        <button 
          onClick={clearError}
          className={`${isDarkMode ? 'text-red-300 hover:text-red-200 bg-red-800/50 hover:bg-red-800' : 'text-red-500 hover:text-red-700 bg-red-100 hover:bg-red-200'} px-3 py-1 rounded-lg transition-colors`}
        >
          Retry
        </button>
      </div>
    );
  };

  useEffect(() => {
    if (newUser.project && !PROJECT_LOCATIONS[newUser.project].includes(newUser.location)) {
      setNewUser(prev => ({ ...prev, location: '' }));
    }
  }, [newUser.project]);

  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    
    // Improved validation
    if (!newUser.name.trim()) {
      toast.error('Name is required');
      return;
    }
    
    if (!newUser.email.trim()) {
      toast.error('Email is required');
      return;
    }
    
    if (!newUser.department) {
      toast.error('Department is required');
      return;
    }
    
    // Check if passwords match
    if (newUser.password !== newUser.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }
    
    try {
      // Create user data object with all fields
      const userData = {
        name: newUser.name.trim(),
        email: newUser.email.trim().toLowerCase(),
        password: newUser.password,
        department: newUser.department,
        project: newUser.project || null,
        location: newUser.location || null,
        oversightPermissions: newUser.oversightPermissions,
        // Include default role to satisfy API validation
        // This is temporary until role management is handled in separate module
        role: 'EMPLOYEE',
        ...(createMode === 'hr' && { hrUserId: selectedHrUser })
      };

      console.log('Creating new user with data:', {
        ...userData,
        password: '********' // Hide password in logs
      });

      // Proceed with user creation
      const response = await api.post<User>('/users', userData);
      
      if (response.status === 201) {
        toast.success('User created successfully');
        setUsers(prevUsers => [...prevUsers, response.data]);
        setShowCreateForm(false);
        setNewUser({
          name: '',
          email: '',
          password: '',
          confirmPassword: '',
          project: '',
          location: '',
          department: 'IT',
          oversightPermissions: {
            departments: [],
            projects: [],
            locations: [],
            hasFullAccess: false
          }
        });
        setCreateMode('manual');
        setSelectedHrUser('');
        await loadUsers();
      }
    } catch (error: any) {
      console.error('Error creating user:', error);
      const errorMessage = error.response?.data?.error || error.response?.data?.message || 'Failed to create user';
      toast.error(errorMessage);
      setError(errorMessage);
    }
  };

  // Fetch HR users when switching to HR mode - only once when mode changes
  useEffect(() => {
    // Only fetch when switching to HR mode AND we don't already have data
    if (createMode === 'hr' && hrUsers.length === 0 && !isLoadingHrUsers) {
      fetchHrUsers();
    }
  }, [createMode, hrUsers.length, isLoadingHrUsers]);

  // Optimize the fetchHrUsers function
  const fetchHrUsers = async () => {
    setIsLoadingHrUsers(true);
    setHrUsersError('');
    
    try {
      // Add timestamp to prevent caching
      const timestamp = new Date().getTime();
      const response = await api.get(`/api/employees/listing?nocache=${timestamp}`);
      
      if (response.status === 200 && response.data) {
        const employees = response.data.employees || [];
        
        if (employees.length === 0) {
          setHrUsersError('No employees found in database. Please add employees first.');
          setHrUsers([]);
          if (availableDepartments.length === 0) {
            setAvailableDepartments(DEPARTMENTS);
          }
          return;
        }
        
        // Map employee data and extract departments in a single pass
        const mappedEmployees = [];
        const departmentSet = new Set<string>();
        
        for (const emp of employees) {
          const id = emp.id !== undefined && emp.id !== null ? emp.id.toString() : '';
          const department = emp.department || emp.job?.department || emp.designation || '';
            
          // Add valid departments to the set
          if (department && typeof department === 'string') {
            departmentSet.add(department);
          }
          
          mappedEmployees.push({
            id: id,
            employeeId: emp.employeeId || '',
            name: `${emp.firstName || ''} ${emp.lastName || ''}`.trim(),
            fullName: `${emp.firstName || ''} ${emp.lastName || ''}`.trim(),
            email: emp.officialEmail || emp.personalEmail || '',
            department: department,
            dept: department,
            // Add project and location if available
            project: emp.project || '',
            location: emp.location || ''
          });
        }
          
        // Convert department set to sorted array
        const uniqueDepartments = Array.from(departmentSet).sort();
        
        // Use employee departments if available, otherwise use defaults
        const departmentsToUse = uniqueDepartments.length > 0 
          ? uniqueDepartments as string[]
          : DEPARTMENTS;
        
        setAvailableDepartments(departmentsToUse);
        setHrUsers(mappedEmployees);
        
        console.log(`Loaded ${mappedEmployees.length} employees from HR database`);
      } else {
        throw new Error('Invalid response format from employees API');
      }
    } catch (err: any) {
      const errorMessage = 'Failed to load employee data. Please make sure employees exist in the database.';
      setHrUsersError(errorMessage);
      setHrUsers([]);
      
      // Make sure we at least have default departments available
      if (availableDepartments.length === 0) {
        setAvailableDepartments(DEPARTMENTS);
      }
      
      // Show toast only if we get a network error
      if (err.message && err.message.includes('Network Error')) {
        toast.error('Network error: Could not connect to the employee data source');
      }
    } finally {
      setIsLoadingHrUsers(false);
    }
  };

  // Function to handle HR employee selection
  const handleHrUserSelect = (hrUserId: string) => {
    setSelectedHrUser(hrUserId);
    if (!hrUserId) {
      // Clear form if no employee selected
      setNewUser({
        name: '',
        email: '',
        password: '',
        confirmPassword: '',
        project: '',
        location: '',
        department: 'IT',
        oversightPermissions: {
          departments: [],
          projects: [],
          locations: [],
          hasFullAccess: false
        }
      });
      return;
    }
      
    // Find the employee by ID
    const selectedEmployee = hrUsers.find(emp => {
      const empId = emp?.id;
      return empId !== undefined && empId !== null && empId.toString() === hrUserId.toString();
    });
      
    if (!selectedEmployee) {
      toast.error(`HR employee data not found for ID: ${hrUserId}. Please try another employee.`);
      setSelectedHrUser('');
      return;
    }
      
    // Generate a secure random password
    const securePassword = 
      Math.random().toString(36).slice(-10) + 
      Math.random().toString(36).toUpperCase().slice(-2) + 
      '@' + Math.floor(Math.random() * 100);
    
    // Get the department directly from employee data
    const employeeDepartment = selectedEmployee.department || selectedEmployee.dept || '';
    
    // Get project and location if available
    const employeeProject = selectedEmployee.project || '';
    const employeeLocation = selectedEmployee.location || '';
    
    // Prepare oversightPermissions based on department
    const oversightDepartments = employeeDepartment ? [employeeDepartment] : [];
    
    // Set user form data using HR employee fields
    setNewUser({
      name: selectedEmployee.name || selectedEmployee.fullName || '',
      email: selectedEmployee.email || '',
      department: employeeDepartment,
      password: securePassword,
      confirmPassword: securePassword,
      project: employeeProject,
      location: employeeLocation,
      oversightPermissions: {
        departments: oversightDepartments,
        projects: employeeProject ? [employeeProject] : [],
        locations: employeeLocation ? [employeeLocation] : [],
        hasFullAccess: false
      }
    });
      
    toast.success(`Selected HR employee: ${selectedEmployee.name || selectedEmployee.fullName}`);
  };

  const handleEditClick = (user: User) => {
    setSelectedUser(user);
    setNewUser({
      name: user.name,
      email: user.email,
      password: '', // Don't populate password for security
      confirmPassword: '',
      project: user.project || '',
      location: user.location || '',
      department: user.department,
      oversightPermissions: user.oversightPermissions || {
        departments: [],
        projects: [],
        locations: [],
        hasFullAccess: false
      }
    });
    setShowEditForm(true);
    setShowCreateForm(false);
  };

  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedUser) return;

    // Check if passwords match if a new password is provided
    if (newUser.password && newUser.password !== newUser.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    try {
      // Prepare update data
      const updateData = {
        name: newUser.name,
        email: newUser.email,
        department: newUser.department,
        project: newUser.project || null,
        location: newUser.location || null,
        oversightPermissions: newUser.oversightPermissions,
        // Preserve the user's existing role without changing it
        role: selectedUser.role,
        ...(newUser.password?.trim() ? { password: newUser.password } : {})
      };

      console.log('Updating user with data:', {
        ...updateData,
        password: updateData.password ? '********' : undefined // Hide password in logs
      });

      // Proceed with user update
      const response = await api.put(`/users/${selectedUser.id}`, updateData);
      
      if (response.status === 200 || response.status === 204) {
        toast.success('User updated successfully');
        
        // Reset form and state
        setShowEditForm(false);
        setSelectedUser(null);
        setNewUser({
          name: '',
          email: '',
          password: '',
          confirmPassword: '',
          project: '',
          location: '',
          department: 'IT',
          oversightPermissions: {
            departments: [],
            projects: [],
            locations: [],
            hasFullAccess: false
          }
        });

        // Refresh the user list
        await loadUsers();
      }
    } catch (error: any) {
      console.error('Error updating user:', error);
      const errorMessage = error.response?.data?.error 
        || error.response?.data?.message 
        || error.message 
        || 'Failed to update user';
      
      toast.error(errorMessage);
      setError(errorMessage);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (!window.confirm('Are you sure you want to delete this user?')) return;

    setIsDeleting(true);
    try {
      await api.delete(`/users/${userId}`);
      toast.success('User deleted successfully');
      await loadUsers(); // Refresh the user list after deletion
    } catch (error: any) {
      console.error('Error deleting user:', error);
      toast.error(error.response?.data?.error || 'Failed to delete user');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleToggleUserStatus = async (userId: string, currentStatus: boolean) => {
    try {
      await api.put(`/users/${userId}`, {
        isActive: !currentStatus
      });
      
      toast.success(`User ${!currentStatus ? 'activated' : 'deactivated'} successfully`);
      await loadUsers(); // Refresh the user list
    } catch (error: any) {
      console.error('Error toggling user status:', error);
      toast.error(error.response?.data?.error || 'Failed to update user status');
    }
  };

  const handleSort = (column: string) => {
    setSorting(prev => ({
      column,
      direction: prev.column === column && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const SortIcon = ({ column }: { column: string }) => {
    if (sorting.column !== column) {
      return <ChevronsUpDown className={`h-4 w-4 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`} />;
    }
    return sorting.direction === 'asc' 
      ? <ChevronUp className={`h-4 w-4 ${isDarkMode ? 'text-blue-400' : 'text-blue-500'}`} />
      : <ChevronDown className={`h-4 w-4 ${isDarkMode ? 'text-blue-400' : 'text-blue-500'}`} />;
  };

  const handlePageSizeChange = (newSize: number) => {
    setPagination(prev => ({ ...prev, limit: newSize, currentPage: 1 }));
  };

  const currentUsers = users;

  const metrics = {
    totalUsers: users.length,
    activeUsers: users.filter(u => u.isActive).length,
    inactiveUsers: users.filter(u => !u.isActive).length,
    departmentDistribution: DEPARTMENTS.map(dept => ({
      name: dept,
      count: users.filter(u => u.department === dept).length
    }))
  };

  // Helper function to toggle department oversight
  const toggleOversightDepartment = (dept: string) => {
    setNewUser(prev => {
      const departments = [...prev.oversightPermissions.departments];
      if (departments.includes(dept)) {
        return {
          ...prev,
          oversightPermissions: {
            ...prev.oversightPermissions,
            departments: departments.filter(d => d !== dept)
          }
        };
      } else {
        return {
          ...prev,
          oversightPermissions: {
            ...prev.oversightPermissions,
            departments: [...departments, dept]
          }
        };
      }
    });
  };

  // Helper function to toggle project oversight
  const toggleOversightProject = (project: string) => {
    setNewUser(prev => {
      const projects = [...prev.oversightPermissions.projects];
      if (projects.includes(project)) {
        // Also remove any locations from this project
        const projectLocations = PROJECT_LOCATIONS[project];
        const filteredLocations = prev.oversightPermissions.locations.filter(
          loc => !projectLocations.includes(loc)
        );
        
        return {
          ...prev,
          oversightPermissions: {
            ...prev.oversightPermissions,
            projects: projects.filter(p => p !== project),
            locations: filteredLocations
          }
        };
      } else {
        return {
          ...prev,
          oversightPermissions: {
            ...prev.oversightPermissions,
            projects: [...projects, project]
          }
        };
      }
    });
  };

  // Helper function to toggle location oversight
  const toggleOversightLocation = (location: string) => {
    setNewUser(prev => {
      const locations = [...prev.oversightPermissions.locations];
      if (locations.includes(location)) {
        return {
          ...prev,
          oversightPermissions: {
            ...prev.oversightPermissions,
            locations: locations.filter(l => l !== location)
          }
        };
      } else {
        return {
          ...prev,
          oversightPermissions: {
            ...prev.oversightPermissions,
            locations: [...locations, location]
          }
        };
      }
    });
  };

  // Toggle full access permission
  const toggleFullAccess = () => {
    setNewUser(prev => ({
      ...prev,
      oversightPermissions: {
        ...prev.oversightPermissions,
        hasFullAccess: !prev.oversightPermissions.hasFullAccess,
        // If enabling full access, clear other selections
        ...((!prev.oversightPermissions.hasFullAccess) ? {
          departments: [],
          projects: [],
          locations: []
        } : {})
      }
    }));
  };

  // Add this useEffect near the beginning of the UserManagement component to load HR data on mount
  useEffect(() => {
    // Load HR data when component mounts to ensure we have department data available
    if (hrUsers.length === 0 && !isLoadingHrUsers) {
      fetchHrUsers();
    }
  }, []);

  // Add a useEffect that fetches HR data when the form is opened

  // After the existing useEffects but before the handler functions
  useEffect(() => {
    // Fetch HR data when form is opened to ensure we have department data
    if ((showCreateForm || showEditForm) && !isLoadingHrUsers) {
      fetchHrUsers();
    }
  }, [showCreateForm, showEditForm]);

  // Form Overlay with Modal Design
  const renderEditForm = () => {
    if (!showEditForm || !selectedUser) return null;

    return (
      <div className="fixed inset-0 bg-gray-900/75 z-50 flex items-center justify-center p-6 backdrop-blur-sm">
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-xl shadow-xl w-full max-w-4xl max-h-[92vh] overflow-y-auto animate-fadeIn transition-colors`}>
          <div className={`p-6 pb-4 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-100'}`}>
            <div className="flex justify-between items-center">
              <h2 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>Edit User</h2>
            <button
                onClick={() => {
                  setShowEditForm(false);
                  setSelectedUser(null);
                }}
                className={`${isDarkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-500 hover:text-gray-700'} transition-colors`}
              >
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
            </div>
            <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} mt-1`}>Update user information below</p>
          </div>

          <form onSubmit={handleUpdateUser} className="px-6 pb-6">
            {/* Basic Information */}
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="bg-blue-100 p-1.5 rounded-lg">
                  <User className="h-5 w-5 text-blue-600" />
              </div>
                <h3 className="text-lg font-semibold text-gray-800">Basic Information</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Name Field */}
              <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                  <input
                    type="text"
                    value={newUser.name}
                    onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                    className="px-3 py-3 block w-full rounded-lg border border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-400 bg-white"
                    placeholder="Enter full name"
                    required
                  />
              </div>
                {/* Email Field */}
              <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                  <input
                    type="email"
                    value={newUser.email}
                    onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                    className="px-3 py-3 block w-full rounded-lg border border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-400 bg-white"
                    placeholder="Enter email address"
                    required
                  />
              </div>
                {/* Password Field */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                  <div className="relative">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      value={newUser.password}
                      onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                      className="px-3 py-3 pr-10 block w-full rounded-lg border border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-400 bg-white"
                      placeholder="Leave blank to keep password"
                    />
                <button
                      type="button" 
                      tabIndex={-1} 
                      onClick={() => setShowPassword(v => !v)} 
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
                </div>
                {/* Confirm Password Field */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
                  <div className="relative">
                <input
                      type={showPassword ? 'text' : 'password'}
                      value={newUser.confirmPassword}
                      onChange={(e) => setNewUser({ ...newUser, confirmPassword: e.target.value })}
                      className={`px-3 py-3 pr-10 block w-full rounded-lg border ${
                        newUser.password && newUser.confirmPassword && newUser.password !== newUser.confirmPassword
                          ? 'border-red-400 focus:border-red-400 focus:ring-2 focus:ring-red-400 bg-red-50'
                          : 'border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-400 bg-white'
                      }`}
                      placeholder="Confirm password"
                      required={!!newUser.password}
                    />
                    <button 
                      type="button" 
                      tabIndex={-1} 
                      onClick={() => setShowPassword(v => !v)} 
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                    {newUser.password && newUser.confirmPassword && newUser.password !== newUser.confirmPassword && (
                      <p className="mt-1 text-xs text-red-500 font-medium">
                        Passwords do not match
                      </p>
                    )}
              </div>
                    </div>
                {/* Department Field */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
                    <select
                    value={newUser.department}
                    onChange={(e) => setNewUser({ ...newUser, department: e.target.value })}
                    className="px-3 py-3 block w-full rounded-lg border border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-400 bg-white"
                    required
                  >
                    <option value="">Select Department</option>
                    {/* Display all available departments from our dynamic list */}
                    {availableDepartments.map((dept) => (
                        <option key={dept} value={dept}>{dept}</option>
                      ))}
                    </select>
                    </div>
                  </div>
                </div>

            {/* Project Information */}
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="bg-blue-100 p-1.5 rounded-lg">
                  <FileText className="h-5 w-5 text-blue-600" />
                    </div>
                <h3 className="text-lg font-semibold text-gray-800">Project Information</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Project */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Project</label>
                    <select
                    value={newUser.project}
                    onChange={(e) => setNewUser({ ...newUser, project: e.target.value })}
                    className="px-3 py-3 block w-full rounded-lg border border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-400 bg-white"
                  >
                    <option value="">Select Project</option>
                    {PROJECTS.map((project) => (
                      <option key={project} value={project}>{project}</option>
                    ))}
                    </select>
                    </div>
                {/* Location */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                    <select
                    value={newUser.location}
                    onChange={(e) => setNewUser({ ...newUser, location: e.target.value })}
                    className="px-3 py-3 block w-full rounded-lg border border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-400 bg-white disabled:bg-gray-100 disabled:text-gray-500"
                    disabled={!newUser.project}
                  >
                    <option value="">Select Location</option>
                    {newUser.project && PROJECT_LOCATIONS[newUser.project].map((location) => (
                      <option key={location} value={location}>{location}</option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>

            {/* Oversight Permissions */}
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="bg-blue-100 p-1.5 rounded-lg">
                  <Shield className="h-5 w-5 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800">Oversight Permissions</h3>
              </div>
              
              {/* Full Access Toggle */}
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={newUser.oversightPermissions.hasFullAccess}
                    onChange={toggleFullAccess}
                    className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 h-4 w-4"
                  />
                  <span className="ml-2 text-sm text-gray-700">Has Full Oversight Access</span>
                </label>
              </div>
              
              {/* Conditional Fields when not Full Access */}
              {!newUser.oversightPermissions.hasFullAccess && (
                <div className="space-y-4">
                  {/* Departments Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Department Oversight</label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {availableDepartments.map(dept => (
                        <label key={dept} className="flex items-center p-2 border border-gray-200 rounded-lg hover:bg-gray-50">
                          <input
                            type="checkbox"
                            checked={newUser.oversightPermissions.departments.includes(dept)}
                            onChange={() => toggleOversightDepartment(dept)}
                            className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 h-4 w-4"
                          />
                          <span className="ml-2 text-sm">{dept}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                  
                  {/* Projects Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Project Oversight</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {PROJECTS.map(project => (
                        <label key={project} className="flex items-center p-2 border border-gray-200 rounded-lg hover:bg-gray-50">
                          <input
                            type="checkbox"
                            checked={newUser.oversightPermissions.projects.includes(project)}
                            onChange={() => toggleOversightProject(project)}
                            className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 h-4 w-4"
                          />
                          <span className="ml-2 text-sm">{project}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                  
                  {/* Locations Selection (show locations for selected projects) */}
                  {newUser.oversightPermissions.projects.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Location Oversight</label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {newUser.oversightPermissions.projects.flatMap(project => 
                          PROJECT_LOCATIONS[project].map(location => (
                            <label key={location} className="flex items-center p-2 border border-gray-200 rounded-lg hover:bg-gray-50">
                              <input
                                type="checkbox"
                                checked={newUser.oversightPermissions.locations.includes(location)}
                                onChange={() => toggleOversightLocation(location)}
                                className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 h-4 w-4"
                              />
                              <span className="ml-2 text-sm">{location}</span>
                            </label>
                          ))
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Form Actions */}
            <div className="flex justify-end gap-4 pt-5 border-t border-gray-200">
              <button
                type="button"
                onClick={() => {
                  setShowEditForm(false);
                  setSelectedUser(null);
                }}
                className="px-4 py-2.5 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
                      <button
                type="submit"
                className="px-5 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 border border-transparent rounded-lg transition-all shadow-sm hover:shadow"
                      >
                Update User
                      </button>
                    </div>
          </form>
              </div>
            </div>
    );
  };

  // Function to render the Create User form
  const renderCreateForm = () => {
    if (!showCreateForm) return null;
    
    return (
      <div className="fixed inset-0 bg-gray-900/75 z-50 flex items-center justify-center p-6 backdrop-blur-sm">
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-xl shadow-xl w-full max-w-4xl max-h-[92vh] overflow-y-auto animate-fadeIn transition-colors`}>
          <div className={`p-6 pb-4 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-100'}`}>
            <div className="flex justify-between items-center">
              <h2 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>Create New User</h2>
              <button 
                onClick={() => {
                  setShowCreateForm(false);
                }}
                className={`${isDarkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-500 hover:text-gray-700'} transition-colors`}
              >
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} mt-1`}>Enter new user information below</p>
                  </div>

          <form onSubmit={handleCreateUser} className="px-6 pb-6">
            {/* User Creation Mode Selector */}
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="bg-blue-100 p-1.5 rounded-lg">
                  <Users className="h-5 w-5 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800">User Source</h3>
              </div>
              <div className="flex flex-wrap gap-3">
                          <button
                            type="button"
                            onClick={() => setCreateMode('manual')}
                  className={`px-4 py-2 rounded-lg flex items-center gap-2 ${
                              createMode === 'manual'
                      ? 'bg-blue-50 text-blue-600 border border-blue-200 font-medium'
                      : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'
                  }`}
                >
                  <User className="h-4 w-4" />
                  <span>Manual Entry</span>
                          </button>
                          <button
                            type="button"
                            onClick={() => setCreateMode('hr')}
                  className={`px-4 py-2 rounded-lg flex items-center gap-2 ${
                              createMode === 'hr'
                      ? 'bg-blue-50 text-blue-600 border border-blue-200 font-medium'
                      : 'bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100'
                  }`}
                >
                  <Globe className="h-4 w-4" />
                  <span>From HR Database</span>
                          </button>
                        </div>
                      </div>

            {/* HR User Selection (conditional) */}
            {createMode === 'hr' && (
              <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-gray-800">Select Employee from HR Database</h4>
                  {hrUsersError && (
                    <span className="text-sm text-red-500">{hrUsersError}</span>
                  )}
                        </div>
                
                          {isLoadingHrUsers ? (
                  <div className="flex justify-center py-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
                            </div>
                          ) : (
                            <>
                                <select
                                  value={selectedHrUser}
                                  onChange={(e) => handleHrUserSelect(e.target.value)}
                      className="px-3 py-3 block w-full rounded-lg border border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-400 bg-white"
                    >
                      <option value="">-- Select Employee --</option>
                      {hrUsers.map((emp) => (
                        <option key={emp.id} value={emp.id}>
                          {emp.name || emp.fullName} - {emp.department || emp.dept || 'No Department'} - {emp.email}
                                      </option>
                      ))}
                                </select>
                    
                    {hrUsers.length === 0 && !isLoadingHrUsers && (
                      <div className="mt-2 text-sm text-orange-500">
                        No employees found in HR database. Please use manual entry.
                      </div>
                              )}
                            </>
                          )}
                      </div>
                    )}

                    {/* Basic Information */}
                    <div className="mb-6">
                      <div className="flex items-center gap-2 mb-4">
                <div className="bg-blue-100 p-1.5 rounded-lg">
                  <User className="h-5 w-5 text-blue-600" />
                </div>
                        <h3 className="text-lg font-semibold text-gray-800">Basic Information</h3>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Name Field */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                            <input
                              type="text"
                              value={newUser.name}
                              onChange={(e) => setNewUser({ ...newUser, name: e.target.value })}
                    className="px-3 py-3 block w-full rounded-lg border border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-400 bg-white"
                              placeholder="Enter full name"
                              required
                            />
                          </div>
                        {/* Email Field */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                            <input
                              type="email"
                              value={newUser.email}
                              onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                    className="px-3 py-3 block w-full rounded-lg border border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-400 bg-white"
                              placeholder="Enter email address"
                              required
                            />
                          </div>
                        {/* Password Field */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                          <div className="relative">
                            <input
                              type={showPassword ? 'text' : 'password'}
                              value={newUser.password}
                              onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
                      className="px-3 py-3 pr-10 block w-full rounded-lg border border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-400 bg-white"
                      placeholder="Enter password"
                      required
                            />
                            <button 
                              type="button" 
                              tabIndex={-1} 
                              onClick={() => setShowPassword(v => !v)} 
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                            >
                              {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                            </button>
                          </div>
                        </div>
                        {/* Confirm Password Field */}
                    <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
                          <div className="relative">
                            <input
                              type={showPassword ? 'text' : 'password'}
                              value={newUser.confirmPassword}
                              onChange={(e) => setNewUser({ ...newUser, confirmPassword: e.target.value })}
                      className={`px-3 py-3 pr-10 block w-full rounded-lg border ${
                                newUser.password && newUser.confirmPassword && newUser.password !== newUser.confirmPassword
                          ? 'border-red-400 focus:border-red-400 focus:ring-2 focus:ring-red-400 bg-red-50'
                          : 'border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-400 bg-white'
                              }`}
                              placeholder="Confirm password"
                      required
                    />
                    <button 
                      type="button" 
                      tabIndex={-1} 
                      onClick={() => setShowPassword(v => !v)} 
                      className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                            {newUser.password && newUser.confirmPassword && newUser.password !== newUser.confirmPassword && (
                      <p className="mt-1 text-xs text-red-500 font-medium">
                                Passwords do not match
                              </p>
                            )}
                            </div>
                          </div>
                        {/* Department Field */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
                            <select
                              value={newUser.department}
                              onChange={(e) => setNewUser({ ...newUser, department: e.target.value })}
                    className="px-3 py-3 block w-full rounded-lg border border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-400 bg-white"
                              required
                            >
                              <option value="">Select Department</option>
                              {/* Display all available departments from our dynamic list */}
                              {availableDepartments.map((dept) => (
                                <option key={dept} value={dept}>{dept}</option>
                              ))}
                            </select>
                        </div>
                      </div>
                    </div>

                    {/* Project Information */}
                    <div className="mb-6">
                      <div className="flex items-center gap-2 mb-4">
                <div className="bg-blue-100 p-1.5 rounded-lg">
                  <FileText className="h-5 w-5 text-blue-600" />
                </div>
                        <h3 className="text-lg font-semibold text-gray-800">Project Information</h3>
                            </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Project */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Project</label>
                            <select
                              value={newUser.project}
                              onChange={(e) => setNewUser({ ...newUser, project: e.target.value })}
                    className="px-3 py-3 block w-full rounded-lg border border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-400 bg-white"
                            >
                              <option value="">Select Project</option>
                              {PROJECTS.map((project) => (
                              <option key={project} value={project}>{project}</option>
                              ))}
                            </select>
                          </div>
                        {/* Location */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                            <select
                              value={newUser.location}
                              onChange={(e) => setNewUser({ ...newUser, location: e.target.value })}
                    className="px-3 py-3 block w-full rounded-lg border border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-400 bg-white disabled:bg-gray-100 disabled:text-gray-500"
                              disabled={!newUser.project}
                            >
                              <option value="">Select Location</option>
                              {newUser.project && PROJECT_LOCATIONS[newUser.project].map((location) => (
                              <option key={location} value={location}>{location}</option>
                              ))}
                            </select>
                        </div>
                      </div>
                    </div>

            {/* Oversight Permissions */}
            <div className="mb-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="bg-blue-100 p-1.5 rounded-lg">
                  <Shield className="h-5 w-5 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800">Oversight Permissions</h3>
              </div>
              
              {/* Full Access Toggle */}
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={newUser.oversightPermissions.hasFullAccess}
                    onChange={toggleFullAccess}
                    className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 h-4 w-4"
                  />
                  <span className="ml-2 text-sm text-gray-700">Has Full Oversight Access</span>
                </label>
              </div>
              
              {/* Conditional Fields when not Full Access */}
              {!newUser.oversightPermissions.hasFullAccess && (
                <div className="space-y-4">
                  {/* Departments Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Department Oversight</label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {availableDepartments.map(dept => (
                        <label key={dept} className="flex items-center p-2 border border-gray-200 rounded-lg hover:bg-gray-50">
                          <input
                            type="checkbox"
                            checked={newUser.oversightPermissions.departments.includes(dept)}
                            onChange={() => toggleOversightDepartment(dept)}
                            className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 h-4 w-4"
                          />
                          <span className="ml-2 text-sm">{dept}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                  
                  {/* Projects Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Project Oversight</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {PROJECTS.map(project => (
                        <label key={project} className="flex items-center p-2 border border-gray-200 rounded-lg hover:bg-gray-50">
                          <input
                            type="checkbox"
                            checked={newUser.oversightPermissions.projects.includes(project)}
                            onChange={() => toggleOversightProject(project)}
                            className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 h-4 w-4"
                          />
                          <span className="ml-2 text-sm">{project}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                  
                  {/* Locations Selection (show locations for selected projects) */}
                  {newUser.oversightPermissions.projects.length > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Location Oversight</label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {newUser.oversightPermissions.projects.flatMap(project => 
                          PROJECT_LOCATIONS[project].map(location => (
                            <label key={location} className="flex items-center p-2 border border-gray-200 rounded-lg hover:bg-gray-50">
                              <input
                                type="checkbox"
                                checked={newUser.oversightPermissions.locations.includes(location)}
                                onChange={() => toggleOversightLocation(location)}
                                className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 h-4 w-4"
                              />
                              <span className="ml-2 text-sm">{location}</span>
                            </label>
                          ))
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

                    {/* Form Actions */}
            <div className="flex justify-end gap-4 pt-5 border-t border-gray-200">
                      <button
                        type="button"
                onClick={() => setShowCreateForm(false)}
                className="px-4 py-2.5 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 transition-colors"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                className="px-5 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 border border-transparent rounded-lg transition-all shadow-sm hover:shadow"
                      >
                Create User
                      </button>
                    </div>
                  </form>
                </div>
              </div>
    );
  };

  if (!currentUser?.role.includes('IT_ADMIN')) {
    return <div className="p-4">You don't have permission to manage users.</div>;
  }

  if (loading) {
    return <div className="p-4">Loading...</div>;
  }

  return (
    <div className={`${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'} w-full flex flex-col transition-colors duration-200`}>
      {/* Render the edit form popup */}
      {renderEditForm()}
      {/* Render the create form popup */}
      {renderCreateForm()}
      <div className="flex-1 w-full mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex flex-col h-full space-y-3">
          {/* Dashboard Metrics - with adjusted spacing */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-5 animate-fadeIn">
            <div className={`${isDarkMode ? 'bg-gray-800 border-gray-700 hover:shadow-indigo-900/10' : 'bg-white border-gray-100 hover:shadow-md'} p-6 rounded-xl shadow-sm border transition-colors duration-300 relative overflow-hidden`}>
              <div className={`absolute top-0 right-0 w-24 h-24 -mr-8 -mt-8 ${isDarkMode ? 'bg-blue-900' : 'bg-blue-50'} rounded-full opacity-70`}></div>
              <div className="flex items-center gap-4 relative z-10">
                <div className="bg-gradient-to-br from-blue-500 to-blue-600 p-3 rounded-xl shadow-inner">
                  <Users className="h-6 w-6 text-white" />
              </div>
              <div>
                  <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Total Users</p>
                  <p className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{metrics.totalUsers}</p>
              </div>
            </div>
            </div>
            <div className={`${isDarkMode ? 'bg-gray-800 border-gray-700 hover:shadow-green-900/10' : 'bg-white border-gray-100 hover:shadow-md'} p-6 rounded-xl shadow-sm border transition-colors duration-300 relative overflow-hidden`}>
              <div className={`absolute top-0 right-0 w-24 h-24 -mr-8 -mt-8 ${isDarkMode ? 'bg-green-900' : 'bg-green-50'} rounded-full opacity-70`}></div>
              <div className="flex items-center gap-4 relative z-10">
                <div className="bg-gradient-to-br from-green-500 to-green-600 p-3 rounded-xl shadow-inner">
                  <UserCheck className="h-6 w-6 text-white" />
              </div>
              <div>
                  <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Active Users</p>
                  <p className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{metrics.activeUsers}</p>
              </div>
            </div>
            </div>
            <div className={`${isDarkMode ? 'bg-gray-800 border-gray-700 hover:shadow-red-900/10' : 'bg-white border-gray-100 hover:shadow-md'} p-6 rounded-xl shadow-sm border transition-colors duration-300 relative overflow-hidden`}>
              <div className={`absolute top-0 right-0 w-24 h-24 -mr-8 -mt-8 ${isDarkMode ? 'bg-red-900' : 'bg-red-50'} rounded-full opacity-70`}></div>
              <div className="flex items-center gap-4 relative z-10">
                <div className="bg-gradient-to-br from-red-500 to-red-600 p-3 rounded-xl shadow-inner">
                  <UserX className="h-6 w-6 text-white" />
              </div>
              <div>
                  <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>Inactive Users</p>
                  <p className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>{metrics.inactiveUsers}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Header Section - with adjusted spacing and no bottom border */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-2">
            <div className="flex items-center gap-3">
              <h1 className={`text-xl sm:text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>User Management</h1>
              <button 
                onClick={toggleDarkMode}
                className={`p-2 rounded-full ${isDarkMode ? 'bg-gray-800 hover:bg-gray-700' : 'bg-gray-100 hover:bg-gray-200'} transition-colors`}
                aria-label="Toggle dark mode"
              >
                {isDarkMode ? <Sun className="h-5 w-5 text-yellow-400" /> : <Moon className="h-5 w-5 text-gray-600" />}
              </button>
            </div>
            <button
              onClick={() => setShowCreateForm(true)}
              className="w-full sm:w-auto bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-2.5 rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all shadow-sm hover:shadow flex items-center justify-center gap-2 text-sm font-medium"
            >
              <User className="h-4 w-4" />
              Create User
            </button>
          </div>

          {/* Compact Filters Section */}
          <div className="mb-0">
            {/* Search Bar - with increased size */}
            <div className="relative mb-2">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className={`h-5 w-5 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`} />
              </div>
              <input
                type="text"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                placeholder="Search by name, email, or department..."
                className={`block w-full pl-10 pr-16 py-2.5 text-sm border ${isDarkMode ? 'border-gray-700 bg-gray-800 text-gray-200 placeholder-gray-500 focus:ring-blue-500 focus:border-blue-500' : 'border-gray-200 bg-gray-50 focus:ring-blue-400 focus:border-blue-400'} rounded-md focus:ring-1 transition-colors`}
              />
              <div className="absolute inset-y-0 right-0 flex items-center">
                <button
                  onClick={() => {
                    setFilters({
                      search: '',
                      department: '',
                      status: '',
                      project: '',
                      location: ''
                    });
                    setPagination(prev => ({ ...prev, currentPage: 1 }));
                  }}
                  className={`mr-3 text-sm ${isDarkMode ? 'text-gray-400 hover:text-blue-400' : 'text-gray-400 hover:text-blue-600'} flex items-center gap-1`}
                >
                  <span>Reset</span>
                  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Filter Grid - rearranged with location field added */}
            <div className="grid grid-cols-1 sm:grid-cols-4 gap-2 mb-0">
                {/* Department Filter */}
                <div className="relative">
                <div className="flex items-center">
                  <Building2 className={`h-4 w-4 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'} absolute left-2.5 z-10`} />
                    <select
                      value={filters.department}
                      onChange={(e) => handleFilterChange('department', e.target.value)}
                    className={`block w-full pl-8 pr-8 py-1.5 text-sm border ${isDarkMode ? 'border-gray-700 bg-gray-800 text-gray-200' : 'border-gray-200 bg-gray-50'} rounded-md focus:ring-1 ${isDarkMode ? 'focus:ring-blue-500 focus:border-blue-500' : 'focus:ring-blue-400 focus:border-blue-400'} appearance-none transition-colors`}
                    >
                      <option value="">All Departments</option>
                    {(availableDepartments && availableDepartments.length > 0 ? availableDepartments : DEPARTMENTS).map(dept => (
                        <option key={dept} value={dept}>{dept}</option>
                      ))}
                    </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-2.5 pointer-events-none">
                    <ChevronDown className={`h-4 w-4 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`} />
                    </div>
                  </div>
                </div>

              {/* Project Filter */}
                <div className="relative">
                <div className="flex items-center">
                  <FileText className={`h-4 w-4 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'} absolute left-2.5 z-10`} />
                    <select
                    value={filters.project}
                    onChange={(e) => handleFilterChange('project', e.target.value)}
                    className={`block w-full pl-8 pr-8 py-1.5 text-sm border ${isDarkMode ? 'border-gray-700 bg-gray-800 text-gray-200' : 'border-gray-200 bg-gray-50'} rounded-md focus:ring-1 ${isDarkMode ? 'focus:ring-blue-500 focus:border-blue-500' : 'focus:ring-blue-400 focus:border-blue-400'} appearance-none transition-colors`}
                    >
                    <option value="">All Projects</option>
                    {PROJECTS.map(project => (
                      <option key={project} value={project}>{project}</option>
                    ))}
                    </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-2.5 pointer-events-none">
                    <ChevronDown className={`h-4 w-4 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`} />
                    </div>
                  </div>
                </div>

              {/* Location Filter - new */}
                <div className="relative">
                <div className="flex items-center">
                  <MapPin className={`h-4 w-4 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'} absolute left-2.5 z-10`} />
                  <select
                    value={filters.location || ''}
                    onChange={(e) => handleFilterChange('location', e.target.value)}
                    className={`block w-full pl-8 pr-8 py-1.5 text-sm border ${isDarkMode ? 'border-gray-700 bg-gray-800 text-gray-200' : 'border-gray-200 bg-gray-50'} rounded-md focus:ring-1 ${isDarkMode ? 'focus:ring-blue-500 focus:border-blue-500' : 'focus:ring-blue-400 focus:border-blue-400'} appearance-none transition-colors`}
                  >
                    <option value="">All Locations</option>
                    {/* Show locations based on selected project or all locations if no project selected */}
                    {filters.project 
                      ? PROJECT_LOCATIONS[filters.project].map(location => (
                          <option key={location} value={location}>{location}</option>
                        ))
                      : Object.values(PROJECT_LOCATIONS).flat().filter((loc, index, self) => 
                          self.indexOf(loc) === index
                        ).map(location => (
                          <option key={location} value={location}>{location}</option>
                        ))
                    }
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-2.5 pointer-events-none">
                    <ChevronDown className={`h-4 w-4 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`} />
                    </div>
                </div>
              </div>

                            {/* Status Filter - moved to end */}
              <div className="relative">
                <div className="flex items-center">
                  <Activity className={`h-4 w-4 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'} absolute left-2.5 z-10`} />
                  <select
                    value={filters.status}
                    onChange={(e) => handleFilterChange('status', e.target.value)}
                    className={`block w-full pl-8 pr-8 py-1.5 text-sm border ${isDarkMode ? 'border-gray-700 bg-gray-800 text-gray-200' : 'border-gray-200 bg-gray-50'} rounded-md focus:ring-1 ${isDarkMode ? 'focus:ring-blue-500 focus:border-blue-500' : 'focus:ring-blue-400 focus:border-blue-400'} appearance-none transition-colors`}
                  >
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-2.5 pointer-events-none">
                    <ChevronDown className={`h-4 w-4 ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`} />
                  </div>
                </div>
              </div>
            </div>

            {/* Active Filters - removing this section to fix the space issue */}
            {/* <div className="flex flex-wrap gap-1.5 mb-2">
                {Object.entries(filters).map(([key, value]) => {
                  if (!value) return null;
                  return (
                    <div
                      key={key}
                    className="inline-flex items-center px-2.5 py-0.75 rounded-md text-sm bg-blue-50 text-blue-600 border border-blue-100"
                    >
                      <span className="capitalize">{key}: {value}</span>
                      <button
                        onClick={() => handleFilterChange(key, '')}
                        className="ml-1.5 hover:text-blue-900"
                      >
                      <svg className="h-3.5 w-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  );
                })}
            </div> */}
          </div>

          {/* Error Display */}
          <ErrorDisplay />

          {/* Table Container - Completely Recreated */}
          <div className="flex-1 flex flex-col min-h-0 -mt-2">
            <div className={`${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-100'} rounded-xl shadow-sm border animate-fadeIn flex-1 transition-colors`}>

              {/* Table Wrapper with proper overflow handling */}
              <div className="relative overflow-hidden rounded-xl">
                <div className="overflow-x-auto">
                  <table className={`min-w-full divide-y ${isDarkMode ? 'divide-gray-700' : 'divide-gray-200'}`}>
                    <thead className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                      <tr>
                        <th className={`px-6 py-3 text-left text-xs font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`}>
                          S.No
                        </th>
                        <th className={`px-6 py-3 text-left text-xs font-medium ${isDarkMode ? 'text-gray-300 hover:bg-gray-600' : 'text-gray-500 hover:bg-gray-100'} uppercase tracking-wider cursor-pointer`} onClick={() => handleSort('name')}>
                          <div className="flex items-center gap-1">
                            Name
                            <SortIcon column="name" />
                          </div>
                        </th>
                        <th className={`px-6 py-3 text-left text-xs font-medium ${isDarkMode ? 'text-gray-300 hover:bg-gray-600' : 'text-gray-500 hover:bg-gray-100'} uppercase tracking-wider cursor-pointer`} onClick={() => handleSort('email')}>
                          <div className="flex items-center gap-1">
                            Email
                            <SortIcon column="email" />
                          </div>
                        </th>
                        <th className={`px-6 py-3 text-left text-xs font-medium ${isDarkMode ? 'text-gray-300 hover:bg-gray-600' : 'text-gray-500 hover:bg-gray-100'} uppercase tracking-wider cursor-pointer`} onClick={() => handleSort('project')}>
                          <div className="flex items-center gap-1">
                            Project
                            <SortIcon column="project" />
                          </div>
                        </th>
                        <th className={`px-6 py-3 text-left text-xs font-medium ${isDarkMode ? 'text-gray-300 hover:bg-gray-600' : 'text-gray-500 hover:bg-gray-100'} uppercase tracking-wider cursor-pointer`} onClick={() => handleSort('location')}>
                          <div className="flex items-center gap-1">
                            Location
                            <SortIcon column="location" />
                          </div>
                        </th>
                        <th className={`px-6 py-3 text-left text-xs font-medium ${isDarkMode ? 'text-gray-300 hover:bg-gray-600' : 'text-gray-500 hover:bg-gray-100'} uppercase tracking-wider cursor-pointer`} onClick={() => handleSort('department')}>
                          <div className="flex items-center gap-1">
                            Department
                            <SortIcon column="department" />
                          </div>
                        </th>
                        <th className={`px-6 py-3 text-left text-xs font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`}>
                          Oversight
                        </th>
                        <th className={`px-6 py-3 text-left text-xs font-medium ${isDarkMode ? 'text-gray-300 hover:bg-gray-600' : 'text-gray-500 hover:bg-gray-100'} uppercase tracking-wider cursor-pointer`} onClick={() => handleSort('isActive')}>
                          <div className="flex items-center gap-1">
                            Status
                            <SortIcon column="isActive" />
                          </div>
                        </th>
                        <th className={`px-6 py-3 text-left text-xs font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`}>
                          Actions
                        </th>
                      </tr>
                  </thead>
                    <tbody className={`${isDarkMode ? 'bg-gray-800 divide-y divide-gray-700' : 'bg-white divide-y divide-gray-200'}`}>
                      {currentUsers.map((user, index) => (
                        <tr key={user.id} className={`${isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'}`}>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>
                            {(pagination.currentPage - 1) * pagination.limit + index + 1}
                          </td>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>
                            {user.name}
                          </td>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                            {user.email}
                          </td>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                            {user.project || 'Not Assigned'}
                          </td>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                            {user.location || 'Not Assigned'}
                          </td>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                            {user.department}
                          </td>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                            {user.oversightPermissions?.hasFullAccess ? (
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                isDarkMode ? 'bg-blue-900 text-blue-200' : 'bg-blue-100 text-blue-800'
                              }`}>
                                Full Access
                              </span>
                            ) : user.oversightPermissions?.departments.length > 0 ||
                               user.oversightPermissions?.projects.length > 0 ||
                               user.oversightPermissions?.locations.length > 0 ? (
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                isDarkMode ? 'bg-green-900 text-green-200' : 'bg-green-100 text-green-800'
                              }`}>
                                Custom Access
                              </span>
                            ) : (
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-800'
                              }`}>
                                No Access
                              </span>
                            )}
                          </td>
                          <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-500'}`}>
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              user.isActive
                                ? isDarkMode ? 'bg-green-900 text-green-200' : 'bg-green-100 text-green-800'
                                : isDarkMode ? 'bg-red-900 text-red-200' : 'bg-red-100 text-red-800'
                            }`}>
                              {user.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <div className="relative inline-block text-left">
                              <UserActionsMenu
                                user={user}
                                onEdit={() => handleEditClick(user)}
                                onToggleStatus={() => handleToggleUserStatus(user.id, user.isActive)}
                                onDelete={() => handleDeleteUser(user.id)}
                                isDeleting={isDeleting}
                                isOpen={activeDropdownUserId === user.id}
                                onToggle={() => handleDropdownToggle(user.id)}
                                isDarkMode={isDarkMode}
                              />
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Pagination Controls */}
              <div className={`${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} px-4 py-3 flex items-center justify-between border-t sm:px-6`}>
                <div className="flex flex-1 justify-between sm:hidden">
                  <button
                    onClick={() => handlePageChange(pagination.currentPage - 1)}
                    disabled={pagination.currentPage === 1}
                      className={`relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg ${
                      pagination.currentPage === 1
                        ? 'text-gray-400 bg-gray-100 cursor-not-allowed'
                          : 'text-gray-700 bg-white hover:bg-gray-50 border border-gray-300'
                      }`}
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => handlePageChange(pagination.currentPage + 1)}
                    disabled={pagination.currentPage === pagination.totalPages}
                      className={`relative inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg ${
                      pagination.currentPage === pagination.totalPages
                        ? 'text-gray-400 bg-gray-100 cursor-not-allowed'
                          : 'text-gray-700 bg-white hover:bg-gray-50 border border-gray-300'
                      }`}
                  >
                    Next
                  </button>
                </div>
                <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                  <div>
                    <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      Showing{' '}
                      <span className="font-medium">
                        {((pagination.currentPage - 1) * pagination.limit) + 1}
                      </span>
                      {' '}-{' '}
                      <span className="font-medium">
                        {Math.min(pagination.currentPage * pagination.limit, pagination.total)}
                      </span>
                      {' '}of{' '}
                      <span className="font-medium">{pagination.total}</span>
                      {' '}users
                    </p>
                  </div>
                  <div className="flex items-center gap-4">
                    <select
                      value={pagination.limit}
                      onChange={(e) => handlePageSizeChange(Number(e.target.value))}
                        className="rounded-lg border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-blue-500 text-sm"
                    >
                      {[5, 10, 15, 20, 30, 50].map(pageSize => (
                        <option key={pageSize} value={pageSize}>
                          Show {pageSize}
                        </option>
                      ))}
                    </select>
                      <nav className="isolate inline-flex -space-x-px rounded-lg shadow-sm overflow-hidden" aria-label="Pagination">
                      <button
                        onClick={() => handlePageChange(pagination.currentPage - 1)}
                        disabled={pagination.currentPage === 1}
                          className={`relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-500 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:bg-gray-100 disabled:text-gray-400`}
                      >
                        <span className="sr-only">Previous</span>
                        <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                      </button>
                      {Array.from({ length: pagination.totalPages }, (_, i) => i + 1).map((pageNumber) => (
                        <button
                          key={pageNumber}
                          onClick={() => handlePageChange(pageNumber)}
                          className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ${
                            pagination.currentPage === pageNumber
                                ? 'z-10 bg-blue-600 text-white focus-visible:outline-blue-600'
                              : 'text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0'
                          }`}
                        >
                          {pageNumber}
                        </button>
                      ))}
                      <button
                        onClick={() => handlePageChange(pagination.currentPage + 1)}
                        disabled={pagination.currentPage === pagination.totalPages}
                        className={`relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-500 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:bg-gray-100 disabled:text-gray-400`}
                      >
                        <span className="sr-only">Next</span>
                        <ChevronRight className="h-5 w-5" aria-hidden="true" />
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 