import { AppDataSource } from '../../config/database';
import { User } from '../../entities/User';
import bcrypt from 'bcryptjs';

async function updateAdminPassword() {
  try {
    // Initialize the database connection
    await AppDataSource.initialize();
    console.log('Database connection initialized');

    const userRepository = AppDataSource.getRepository(User);

    // Find the admin user
    const admin = await userRepository.findOne({
      where: { email: '<EMAIL>' }
    });

    if (!admin) {
      console.error('Admin user not found');
      return;
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash('admin123', 10);
    
    // Update the password
    admin.password = hashedPassword;
    
    // Save the changes
    await userRepository.save(admin);
    
    console.log('Admin password updated successfully');
  } catch (error) {
    console.error('Error updating admin password:', error);
  } finally {
    // Close the database connection
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('Database connection closed');
    }
  }
}

// Run the function
updateAdminPassword(); 