import { AppDataSource } from '../config/database';
import { User, UserRole, UserPermissions } from '../entities/User';

async function createAdminUser() {
  let connection = null;
  try {
    // Use the existing AppDataSource
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }
    connection = AppDataSource;
    console.log("Database connection initialized");

    const userRepository = connection.getRepository(User);
    console.log("User repository created");

    // Delete existing admin if exists
    const existingAdmin = await userRepository.findOne({ where: { email: '<EMAIL>' } });
    console.log("Checked for existing admin:", existingAdmin ? "found" : "not found");
    
    if (existingAdmin) {
      await userRepository.remove(existingAdmin);
      console.log('Existing admin user deleted');
    }

    // Create admin user permissions
    const adminPermissions: UserPermissions = {
      canCreateTickets: true,
      canCreateTicketsForOthers: true,
      canEditTickets: true,
      canDeleteTickets: true,
      canCloseTickets: true,
      canLockTickets: true,
      canAssignTickets: true,
      canEscalateTickets: true,
      canViewAllTickets: true
    };

    // Create admin user
    const adminUser = new User();
    adminUser.name = 'Admin User';
    adminUser.email = '<EMAIL>';
    adminUser.role = UserRole.IT_ADMIN;
    adminUser.department = 'IT Department';
    adminUser.isActive = true;
    adminUser.permissions = adminPermissions;
    adminUser.setPassword('admin123');

    console.log("Admin user object created:", { 
      ...adminUser,
      password: '[HIDDEN]',
      permissions: JSON.stringify(adminPermissions)
    });

    // Save the user
    const savedUser = await userRepository.save(adminUser);
    console.log('Admin user saved successfully:', { 
      id: savedUser.id,
      name: savedUser.name,
      email: savedUser.email,
      role: savedUser.role,
      permissions: JSON.stringify(savedUser.permissions)
    });

    // Verify the user was saved correctly
    const verifyUser = await userRepository.findOne({ where: { email: '<EMAIL>' } });
    if (verifyUser) {
      console.log('Verified user exists with ID:', verifyUser.id);
      
      // Test password validation
      const isValidPassword = await verifyUser.validatePassword('admin123');
      console.log('Password validation test:', isValidPassword ? 'PASSED' : 'FAILED');
    }

  } catch (error) {
    console.error('Error creating admin user:', error);
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Stack trace:', error.stack);
    }
  } finally {
    if (connection && connection.isInitialized) {
      await connection.destroy();
      console.log('Database connection closed');
    }
  }
}

createAdminUser(); 