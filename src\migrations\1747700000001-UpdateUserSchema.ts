import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateUserSchema1747700000001 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        console.log("Adding missing columns to users table...");

        // Add role column if it doesn't exist
        const hasRole = await queryRunner.hasColumn('users', 'role');
        if (!hasRole) {
            await queryRunner.query(`
                ALTER TABLE \`users\` 
                ADD COLUMN \`role\` VARCHAR(20) NOT NULL DEFAULT 'EMPLOYEE'
            `);
            console.log("Added role column");
        } else {
            console.log("role column already exists");
        }

        // Add oversightPermissions column if it doesn't exist
        const hasOversightPermissions = await queryRunner.hasColumn('users', 'oversightPermissions');
        if (!hasOversightPermissions) {
            await queryRunner.query(`
                ALTER TABLE \`users\` 
                ADD COLUMN \`oversightPermissions\` JSON NULL
            `);
            console.log("Added oversightPermissions column");
        } else {
            console.log("oversightPermissions column already exists");
        }

        // Add dashboardAccess column if it doesn't exist
        const hasDashboardAccess = await queryRunner.hasColumn('users', 'dashboardAccess');
        if (!hasDashboardAccess) {
            await queryRunner.query(`
                ALTER TABLE \`users\` 
                ADD COLUMN \`dashboardAccess\` TEXT NULL
            `);
            console.log("Added dashboardAccess column");
        } else {
            console.log("dashboardAccess column already exists");
        }

        console.log("User schema update completed successfully!");
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        console.log("Removing added columns from users table...");

        // Remove dashboardAccess column if it exists
        const hasDashboardAccess = await queryRunner.hasColumn('users', 'dashboardAccess');
        if (hasDashboardAccess) {
            await queryRunner.query(`
                ALTER TABLE \`users\` 
                DROP COLUMN \`dashboardAccess\`
            `);
            console.log("Removed dashboardAccess column");
        }

        // Remove oversightPermissions column if it exists
        const hasOversightPermissions = await queryRunner.hasColumn('users', 'oversightPermissions');
        if (hasOversightPermissions) {
            await queryRunner.query(`
                ALTER TABLE \`users\` 
                DROP COLUMN \`oversightPermissions\`
            `);
            console.log("Removed oversightPermissions column");
        }

        // Remove role column if it exists
        const hasRole = await queryRunner.hasColumn('users', 'role');
        if (hasRole) {
            await queryRunner.query(`
                ALTER TABLE \`users\` 
                DROP COLUMN \`role\`
            `);
            console.log("Removed role column");
        }

        console.log("User schema rollback completed successfully!");
    }
} 