import { Router } from 'express';
import { 
  getRoleTemplates, 
  getRoleTemplateById, 
  createRoleTemplate, 
  updateRoleTemplate, 
  deleteRoleTemplate
} from '../../controllers/roleTemplateController';
import { auth, authorize } from '../middleware/auth';
import { UserRole } from '../../types/common';
import rateLimit from 'express-rate-limit';

const router = Router();

// Configure rate limiting
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});

// Apply rate limiting to all role template routes
router.use(apiLimiter);

// Add a fallback route that works even if database is down
router.get('/fallback', (req, res) => {
  const mockTemplates = [
    {
      id: 'template-it-admin',
      name: 'IT Administrator',
      description: 'Full system access with all permissions across all modules',
      permissions: [
        'canAccessAllModules', 'canConfigureSystem', 'canManageRoles', 
        'canAddUsers', 'canEditUsers', 'canDeleteUsers', 'canViewReports', 
        'canExportData', 'canImportData', 'canViewAllDepartments',
        'canCreateTickets', 'canEditTickets', 'canDeleteTickets', 'canCloseTickets',
        'canCreateEmployee', 'canEditEmployee', 'canDeleteEmployee', 'canViewEmployees',
        'canViewDashboards', 'canViewAllDashboards', 'canCreateDashboards', 'canEditDashboards'
      ],
      category: 'predefined',
      iconName: 'Shield',
      iconColor: 'text-purple-600',
      createdAt: new Date().toISOString()
    },
    {
      id: 'template-admin',
      name: 'System Administrator',
      description: 'Core administrative capabilities for system management',
      permissions: [
        'canConfigureSystem', 'canManageRoles', 
        'canAddUsers', 'canEditUsers', 'canDeleteUsers', 'canViewReports', 
        'canExportData', 'canImportData', 'canViewAllDepartments'
      ],
      category: 'predefined',
      iconName: 'Settings',
      iconColor: 'text-blue-600',
      createdAt: new Date().toISOString()
    }
  ];
  
  res.json({ templates: mockTemplates });
});

// Get all role templates
router.get('/',
  auth,
  authorize([UserRole.IT_ADMIN]),
  getRoleTemplates
);

// Get role template by ID
router.get('/:id',
  auth,
  authorize([UserRole.IT_ADMIN]),
  getRoleTemplateById
);

// Create new role template
router.post('/',
  auth,
  authorize([UserRole.IT_ADMIN]),
  createRoleTemplate
);

// Update role template
router.put('/:id',
  auth,
  authorize([UserRole.IT_ADMIN]),
  updateRoleTemplate
);

// Delete role template
router.delete('/:id',
  auth,
  authorize([UserRole.IT_ADMIN]),
  deleteRoleTemplate
);

export default router; 