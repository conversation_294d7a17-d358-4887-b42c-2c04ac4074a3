interface EmailData {
  to: string;
  subject: string;
  template: string;
  data: Record<string, any>;
}

interface EmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

export async function sendEmail(emailData: EmailData): Promise<EmailResult> {
  try {
    // In a real implementation, you would integrate with services like:
    // - SendGrid
    // - AWS SES
    // - NodeMailer with SMTP
    // - Mailgun
    // etc.

    console.log('Sending email:', {
      to: emailData.to,
      subject: emailData.subject,
      template: emailData.template,
      data: emailData.data
    });

    // Simulate email sending
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Mock successful response
    return {
      success: true,
      messageId: `mock-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    };

  } catch (error) {
    console.error('Email sending failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

export function getLeaveRequestEmailTemplate(data: any): string {
  return `
    <h2>New Leave Request Submitted</h2>
    <p><strong>Employee:</strong> ${data.employeeName}</p>
    <p><strong>Leave Type:</strong> ${data.leaveType}</p>
    <p><strong>Start Date:</strong> ${data.startDate}</p>
    <p><strong>End Date:</strong> ${data.endDate}</p>
    <p><strong>Days Requested:</strong> ${data.daysRequested}</p>
    <p><strong>Reason:</strong> ${data.reason}</p>
    <p>Please review and approve/reject this request in the HR management system.</p>
  `;
}

export function getLeaveApprovalEmailTemplate(data: any): string {
  const action = data.action === 'approved' ? 'Approved' : 'Rejected';
  const color = data.action === 'approved' ? '#28a745' : '#dc3545';

  return `
    <h2 style="color: ${color}">Leave Request ${action}</h2>
    <p>Dear ${data.employeeName},</p>
    <p>Your leave request has been <strong>${data.action}</strong>.</p>
    
    <h3>Request Details:</h3>
    <p><strong>Leave Type:</strong> ${data.leaveType}</p>
    <p><strong>Start Date:</strong> ${data.startDate}</p>
    <p><strong>End Date:</strong> ${data.endDate}</p>
    
    <h3>Approval Details:</h3>
    <p><strong>${action} by:</strong> ${data.approverName}</p>
    ${data.comments ? `<p><strong>Comments:</strong> ${data.comments}</p>` : ''}
    
    <p>If you have any questions, please contact HR or your manager.</p>
  `;
} 