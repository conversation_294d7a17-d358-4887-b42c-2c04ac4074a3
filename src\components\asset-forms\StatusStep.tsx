import React from 'react';
import { Asset } from '../../types/asset';

interface StatusStepProps {
  formData: Partial<Asset>;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  errors: Record<string, string>;
}

export const StatusStep: React.FC<StatusStepProps> = ({
  formData,
  onChange,
  errors,
}) => {
  const inputClassName = "w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors";
  const labelClassName = "block text-sm font-medium text-gray-700 mb-1";

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <label htmlFor="status" className={labelClassName}>Status</label>
        <select
          id="status"
          name="status"
          value={formData.status || ''}
          onChange={onChange}
          className={inputClassName}
        >
          <option value="Active">Active</option>
          <option value="Reserved">Reserved</option>
          <option value="Under Repair">Under Repair</option>
          <option value="Retired">Retired</option>
          <option value="Lost/Stolen">Lost/Stolen</option>
          <option value="Decommissioned">Decommissioned</option>
          <option value="In Transit">In Transit</option>
        </select>
        {errors.status && (
          <p className="mt-1 text-sm text-red-500">{errors.status}</p>
        )}
      </div>

      <div>
        <label htmlFor="assignedTo" className={labelClassName}>Assigned To</label>
        <input
          type="text"
          id="assignedTo"
          name="assignedTo"
          value={formData.assignedTo || ''}
          onChange={onChange}
          className={inputClassName}
        />
      </div>
    </div>
  );
}; 