import { AppDataSource } from '../config/database';
import { Employee } from '../server/entities/Employee';
import { EmployeeContact } from '../server/entities/EmployeeContact';
import { EmployeeJob } from '../server/entities/EmployeeJob';
import { EmployeeBenefit } from '../server/entities/EmployeeBenefit';

// Initialize the database connection
const initializeDB = async () => {
  try {
    await AppDataSource.initialize();
    console.log('Database connection established');
    return true;
  } catch (error) {
    console.error('Error connecting to database:', error);
    return false;
  }
};

const repairEmployeeData = async () => {
  console.log('Starting employee data repair');
  
  try {
    // Get all employees
    const employeeRepository = AppDataSource.getRepository(Employee);
    const employees = await employeeRepository.find({
      relations: ['contact', 'job', 'benefit']
    });
    
    console.log(`Found ${employees.length} employees to check for repairs`);
    
    const results = {
      totalEmployees: employees.length,
      contactsCreated: 0,
      jobsCreated: 0,
      benefitsCreated: 0,
      errors: [] as string[]
    };
    
    // Process each employee
    for (const employee of employees) {
      try {
        // Check if contact exists
        if (!employee.contact) {
          console.log(`Creating missing contact for employee ${employee.id} (${employee.firstName} ${employee.lastName})`);
          
          // Create contact entity
          const contactRepository = AppDataSource.getRepository(EmployeeContact);
          const contact = contactRepository.create({
            mobileNumber: '0000000000', // Default placeholder
            employee
          });
          await contactRepository.save(contact);
          results.contactsCreated++;
        }
        
        // Check if job exists
        if (!employee.job) {
          console.log(`Creating missing job for employee ${employee.id} (${employee.firstName} ${employee.lastName})`);
          
          // Create job entity with default values
          const jobRepository = AppDataSource.getRepository(EmployeeJob);
          const job = jobRepository.create({
            designation: 'Not specified',
            department: 'Not specified',
            joinDate: new Date().toISOString().split('T')[0], // Today's date
            employee
          });
          await jobRepository.save(job);
          results.jobsCreated++;
        }
        
        // Check if benefit exists
        if (!employee.benefit) {
          console.log(`Creating missing benefit for employee ${employee.id} (${employee.firstName} ${employee.lastName})`);
          
          // Create benefit entity with default values
          const benefitRepository = AppDataSource.getRepository(EmployeeBenefit);
          const benefit = benefitRepository.create({
            totalSalary: '0', // Default placeholder
            employee
          });
          await benefitRepository.save(benefit);
          results.benefitsCreated++;
        }
      } catch (error) {
        const errorMsg = `Error repairing data for employee ${employee.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        console.error(errorMsg);
        results.errors.push(errorMsg);
      }
    }
    
    console.log('Employee data repair completed:');
    console.log(`- Total employees: ${results.totalEmployees}`);
    console.log(`- Contacts created: ${results.contactsCreated}`);
    console.log(`- Jobs created: ${results.jobsCreated}`);
    console.log(`- Benefits created: ${results.benefitsCreated}`);
    
    if (results.errors.length > 0) {
      console.log(`- Errors encountered: ${results.errors.length}`);
      results.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
    
    return results;
  } catch (error) {
    console.error('Error repairing employee data:', error);
    throw error;
  }
};

// Main function
const main = async () => {
  try {
    const dbInitialized = await initializeDB();
    if (!dbInitialized) {
      console.error('Failed to initialize database. Exiting...');
      process.exit(1);
    }
    
    await repairEmployeeData();
    
    // Close DB connection when done
    await AppDataSource.destroy();
    console.log('Database connection closed');
    process.exit(0);
  } catch (error) {
    console.error('Script execution failed:', error);
    process.exit(1);
  }
};

// Run the main function
main(); 