import React, { useState, useEffect, useRef } from 'react';
import { 
  Fingerprint, 
  Camera, 
  Scan, 
  User<PERSON>heck, 
  MapPin, 
  CheckCircle, 
  Clock, 
  RefreshCw, 
  AlertCircle,
  Loader,
  CheckSquare,
  XCircle,
  Save
} from 'lucide-react';
import { AttendanceStatus, BiometricVerificationType } from '../../../types/timeAttendance';
import { 
  hrPrimaryButtonStyle, 
  hrSecondaryButtonStyle, 
  hrCardStyle,
  hrErrorAlertStyle,
  hrSectionTitleStyle,
  hrSubsectionTitleStyle
} from '../../../styles/hrWorkflow';

interface BiometricClockInProps {
  employeeId: number;
  onClockInSuccess: (data: {
    employeeId: number;
    time: string;
    verificationType: BiometricVerificationType;
    location?: { latitude: number; longitude: number; address?: string };
  }) => void;
  onClockInFail: (error: string) => void;
  mode: 'clock-in' | 'clock-out';
  verificationTypes?: BiometricVerificationType[];
  requireLocation?: boolean;
  saveToAttendanceSystem?: boolean;
  employeeName?: string;
  department?: string;
  position?: string;
}

const BiometricClockIn: React.FC<BiometricClockInProps> = ({
  employeeId,
  onClockInSuccess,
  onClockInFail,
  mode = 'clock-in',
  verificationTypes = [BiometricVerificationType.FINGERPRINT, BiometricVerificationType.FACE],
  requireLocation = true,
  saveToAttendanceSystem = false,
  employeeName = '',
  department = '',
  position = ''
}) => {
  const [selectedVerificationType, setSelectedVerificationType] = useState<BiometricVerificationType>(
    verificationTypes[0] || BiometricVerificationType.FINGERPRINT
  );
  const [verificationStatus, setVerificationStatus] = useState<'idle' | 'scanning' | 'success' | 'failed'>('idle');
  const [error, setError] = useState<string | null>(null);
  const [location, setLocation] = useState<{ latitude: number; longitude: number; address?: string } | null>(null);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [verificationProgress, setVerificationProgress] = useState(0);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  
  const [savedToAttendance, setSavedToAttendance] = useState(false);

  // Get current time formatted as HH:MM
  const getCurrentTime = () => {
    const now = new Date();
    return `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
  };

  // Get user's location
  const getLocation = () => {
    if (!navigator.geolocation) {
      setError('Geolocation is not supported by your browser');
      return;
    }

    setIsGettingLocation(true);
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          const { latitude, longitude } = position.coords;
          
          // Try to get address using reverse geocoding
          let address;
          try {
            const response = await fetch(
              `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=18&addressdetails=1`
            );
            const data = await response.json();
            address = data.display_name;
          } catch (error) {
            console.error('Error fetching address:', error);
            address = 'Unknown location';
          }

          setLocation({ latitude, longitude, address });
          setIsGettingLocation(false);
        } catch (error) {
          setError('Failed to get location details');
          setIsGettingLocation(false);
        }
      },
      (error) => {
        setError(`Failed to get location: ${error.message}`);
        setIsGettingLocation(false);
      },
      { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
    );
  };

  // Initialize facial recognition
  const startFacialRecognition = async () => {
    if (!videoRef.current || !canvasRef.current) return;
    
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      videoRef.current.srcObject = stream;
      
      setVerificationStatus('scanning');
      
      // Simulating progress for demo purposes
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        setVerificationProgress(progress);
        if (progress >= 100) {
          clearInterval(interval);
          captureAndVerifyFace();
        }
      }, 500);
    } catch (error) {
      setError('Failed to access camera: ' + (error as Error).message);
      setVerificationStatus('failed');
    }
  };

  // Capture face and verify
  const captureAndVerifyFace = () => {
    if (!videoRef.current || !canvasRef.current) return;
    
    try {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');
      
      if (!context) return;
      
      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      
      // Draw video frame to canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height);
      
      // In a real implementation, we would:
      // 1. Convert canvas to image data
      // 2. Send to server for facial recognition
      // 3. Get verification result
      
      // Simulate server verification (80% success rate for demo)
      const isVerified = Math.random() > 0.2;
      
      // Stop video stream
      const stream = video.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      video.srcObject = null;
      
      if (isVerified) {
        setVerificationStatus('success');
        const clockData = {
          employeeId,
          time: getCurrentTime(),
          verificationType: BiometricVerificationType.FACE,
          location: location || undefined
        };
        
        // Save to attendance system if enabled
        if (saveToAttendanceSystem) {
          saveAttendanceRecord(clockData);
        }
        
        if (onClockInSuccess) {
          onClockInSuccess(clockData);
        }
      } else {
        setVerificationStatus('failed');
        setError('Facial verification failed. Please try again or use another method.');
        if (onClockInFail) {
          onClockInFail('Facial verification failed');
        }
      }
    } catch (error) {
      setVerificationStatus('failed');
      setError('Error during facial verification: ' + (error as Error).message);
      if (onClockInFail) {
        onClockInFail('Error during facial verification');
      }
    }
  };

  // Simulate fingerprint verification
  const startFingerprintVerification = () => {
    setVerificationStatus('scanning');
    
    // Simulating progress for demo purposes
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      setVerificationProgress(progress);
      if (progress >= 100) {
        clearInterval(interval);
        
        // Simulate verification result (90% success rate for demo)
        const isVerified = Math.random() > 0.1;
        
        if (isVerified) {
          setVerificationStatus('success');
          const clockData = {
            employeeId,
            time: getCurrentTime(),
            verificationType: BiometricVerificationType.FINGERPRINT,
            location: location || undefined
          };
          
          // Save to attendance system if enabled
          if (saveToAttendanceSystem) {
            saveAttendanceRecord(clockData);
          }
          
          if (onClockInSuccess) {
            onClockInSuccess(clockData);
          }
        } else {
          setVerificationStatus('failed');
          setError('Fingerprint verification failed. Please try again or use another method.');
          if (onClockInFail) {
            onClockInFail('Fingerprint verification failed');
          }
        }
      }
    }, 300);
  };

  // New function to save attendance record to the system
  const saveAttendanceRecord = (clockData: {
    employeeId: number;
    time: string;
    verificationType: BiometricVerificationType;
    location?: { latitude: number; longitude: number; address?: string };
  }) => {
    try {
      const today = new Date().toISOString().split('T')[0];
      const time = clockData.time;
      
      // In a real implementation, this would:
      // 1. Check if there's an existing record for today
      // 2. Update it with clock-in or clock-out time
      // 3. Save to database
      
      // For now, just simulate saving to localStorage
      const attendanceKey = `attendance_${employeeId}_${today}`;
      const existingRecord = localStorage.getItem(attendanceKey);
      
      if (mode === 'clock-in') {
        // Create or update record with clock-in time
        const attendanceRecord = {
          id: existingRecord ? JSON.parse(existingRecord).id : Date.now(),
          employeeId: clockData.employeeId,
          employeeName: employeeName || `Employee ${employeeId}`,
          department: department || 'Unknown',
          position: position || 'Unknown',
          date: today,
          checkInTime: time,
          checkOutTime: existingRecord ? JSON.parse(existingRecord).checkOutTime : null,
          status: AttendanceStatus.PRESENT,
          notes: `Verified with ${clockData.verificationType}`,
          location: clockData.location?.address || null,
          coordinates: clockData.location ? { lat: clockData.location.latitude, lng: clockData.location.longitude } : null,
          isRemote: false,
          workHours: null,
          biometricVerification: true,
          verificationType: clockData.verificationType,
          shift: 1
        };
        
        localStorage.setItem(attendanceKey, JSON.stringify(attendanceRecord));
      } else if (mode === 'clock-out' && existingRecord) {
        // Update existing record with clock-out time
        const record = JSON.parse(existingRecord);
        const checkInTime = record.checkInTime;
        
        // Calculate work hours
        const checkIn = new Date(`1970-01-01T${checkInTime}`);
        const checkOut = new Date(`1970-01-01T${time}`);
        let diffHours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
        
        // Handle overnight shifts
        if (diffHours < 0) {
          diffHours += 24;
        }
        
        const updatedRecord = {
          ...record,
          checkOutTime: time,
          workHours: parseFloat(diffHours.toFixed(2)),
          notes: record.notes + `, Clock-out verified with ${clockData.verificationType}`
        };
        
        localStorage.setItem(attendanceKey, JSON.stringify(updatedRecord));
      }
      
      setSavedToAttendance(true);
      console.log(`Successfully ${mode === 'clock-in' ? 'clocked in' : 'clocked out'} and saved to attendance system`);
    } catch (error) {
      console.error('Error saving to attendance system:', error);
      setError('Failed to save attendance record');
    }
  };

  // Get location when component mounts
  useEffect(() => {
    if (requireLocation) {
      getLocation();
    }
  }, [requireLocation]);

  // Start verification process
  const startVerification = () => {
    setError(null);
    setVerificationStatus('idle');
    setVerificationProgress(0);
    setSavedToAttendance(false);
    
    if (selectedVerificationType === BiometricVerificationType.FACE) {
      startFacialRecognition();
    } else if (selectedVerificationType === BiometricVerificationType.FINGERPRINT) {
      startFingerprintVerification();
    }
  };

  return (
    <div className={hrCardStyle}>
      <div className="flex justify-between items-center mb-6">
        <h2 className={hrSectionTitleStyle}>
          {mode === 'clock-in' ? 'Clock In' : 'Clock Out'} Verification
        </h2>
        <div className="flex items-center space-x-2">
          <Clock className="h-5 w-5 text-blue-600" />
          <span className="text-lg font-semibold">
            {getCurrentTime()}
          </span>
        </div>
      </div>

      {/* Verification type selector */}
      <div className="mb-6">
        <label className={hrSubsectionTitleStyle}>
          Verification Method
        </label>
        <div className="grid grid-cols-2 gap-4">
          {verificationTypes.includes(BiometricVerificationType.FINGERPRINT) && (
            <button
              type="button"
              className={`flex items-center justify-center p-4 border rounded-lg ${
                selectedVerificationType === BiometricVerificationType.FINGERPRINT
                  ? 'bg-blue-50 border-blue-500 text-blue-700'
                  : 'border-gray-300 text-gray-500 hover:bg-gray-50'
              } transition-colors duration-200`}
              onClick={() => setSelectedVerificationType(BiometricVerificationType.FINGERPRINT)}
              disabled={verificationStatus === 'scanning'}
            >
              <Fingerprint className="h-6 w-6 mr-2" />
              <span>Fingerprint</span>
            </button>
          )}
          
          {verificationTypes.includes(BiometricVerificationType.FACE) && (
            <button
              type="button"
              className={`flex items-center justify-center p-4 border rounded-lg ${
                selectedVerificationType === BiometricVerificationType.FACE
                  ? 'bg-blue-50 border-blue-500 text-blue-700'
                  : 'border-gray-300 text-gray-500 hover:bg-gray-50'
              } transition-colors duration-200`}
              onClick={() => setSelectedVerificationType(BiometricVerificationType.FACE)}
              disabled={verificationStatus === 'scanning'}
            >
              <Camera className="h-6 w-6 mr-2" />
              <span>Face Recognition</span>
            </button>
          )}
        </div>
      </div>

      {/* Location display */}
      {requireLocation && (
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center mb-2">
            <MapPin className="h-5 w-5 text-gray-600 mr-2" />
            <h3 className="text-md font-medium text-gray-700">Location</h3>
          </div>
          
          {isGettingLocation ? (
            <div className="flex items-center text-gray-500">
              <Loader className="h-4 w-4 mr-2 animate-spin" />
              <span>Getting your location...</span>
            </div>
          ) : location ? (
            <div className="text-sm text-gray-600">
              <div className="flex items-center mb-1">
                <CheckCircle className="h-4 w-4 mr-1 text-green-600" />
                <span>Location verified</span>
              </div>
              <p className="text-xs ml-5">{location.address}</p>
            </div>
          ) : (
            <div className="flex items-center text-red-500">
              <AlertCircle className="h-4 w-4 mr-2" />
              <span>Location required for verification</span>
            </div>
          )}
        </div>
      )}

      {/* Verification area */}
      <div className="mb-6">
        {selectedVerificationType === BiometricVerificationType.FACE && (
          <div className="relative">
            <video
              ref={videoRef}
              className={`w-full h-64 bg-gray-100 rounded-lg object-cover ${
                verificationStatus !== 'scanning' ? 'hidden' : ''
              }`}
              autoPlay
              playsInline
              muted
            />
            
            <canvas 
              ref={canvasRef} 
              className="hidden" 
            />
            
            {verificationStatus !== 'scanning' && (
              <div className="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                {verificationStatus === 'success' ? (
                  <div className="text-center text-green-600">
                    <CheckCircle className="h-16 w-16 mx-auto mb-2" />
                    <p className="font-medium">Verification Successful</p>
                    {savedToAttendance && (
                      <p className="text-sm mt-1 text-green-600">Saved to attendance system</p>
                    )}
                  </div>
                ) : verificationStatus === 'failed' ? (
                  <div className="text-center text-red-600">
                    <XCircle className="h-16 w-16 mx-auto mb-2" />
                    <p className="font-medium">Verification Failed</p>
                  </div>
                ) : (
                  <div className="text-center text-gray-500">
                    <Camera className="h-16 w-16 mx-auto mb-2" />
                    <p>Camera will activate when you start verification</p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
        
        {selectedVerificationType === BiometricVerificationType.FINGERPRINT && (
          <div className="relative">
            {verificationStatus === 'scanning' ? (
              <div className="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <div className="relative mb-4">
                    <Fingerprint className="h-16 w-16 text-blue-600 mx-auto animate-pulse" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-24 h-24 rounded-full border-2 border-blue-500 animate-ping opacity-30"></div>
                    </div>
                  </div>
                  <p className="text-gray-700">Place your finger on the scanner</p>
                  <div className="w-full h-2 bg-gray-200 rounded-full mt-4">
                    <div 
                      className="h-2 bg-blue-600 rounded-full transition-all duration-300"
                      style={{ width: `${verificationProgress}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="w-full h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                {verificationStatus === 'success' ? (
                  <div className="text-center text-green-600">
                    <CheckCircle className="h-16 w-16 mx-auto mb-2" />
                    <p className="font-medium">Verification Successful</p>
                    {savedToAttendance && (
                      <p className="text-sm mt-1 text-green-600">Saved to attendance system</p>
                    )}
                  </div>
                ) : verificationStatus === 'failed' ? (
                  <div className="text-center text-red-600">
                    <XCircle className="h-16 w-16 mx-auto mb-2" />
                    <p className="font-medium">Verification Failed</p>
                  </div>
                ) : (
                  <div className="text-center text-gray-500">
                    <Fingerprint className="h-16 w-16 mx-auto mb-2" />
                    <p>Ready for fingerprint scanning</p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Error display */}
      {error && (
        <div className={hrErrorAlertStyle}>
          <div className="flex items-start">
            <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5 text-red-500" />
            <p>{error}</p>
          </div>
        </div>
      )}

      {/* Action buttons */}
      <div className="flex space-x-4">
        <button
          type="button"
          className={
            verificationStatus === 'scanning'
              ? "flex-1 flex items-center justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-400 cursor-not-allowed"
              : verificationStatus === 'success'
              ? "flex-1 flex items-center justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200"
              : hrPrimaryButtonStyle + " flex-1"
          }
          onClick={startVerification}
          disabled={verificationStatus === 'scanning' || (!requireLocation ? false : !location)}
        >
          {verificationStatus === 'scanning' ? (
            <>
              <Loader className="h-5 w-5 mr-2 animate-spin" />
              <span>Verifying...</span>
            </>
          ) : verificationStatus === 'success' ? (
            <>
              <CheckSquare className="h-5 w-5 mr-2" />
              <span>{mode === 'clock-in' ? 'Clocked In' : 'Clocked Out'}</span>
            </>
          ) : (
            <>
              {mode === 'clock-in' ? (
                <UserCheck className="h-5 w-5 mr-2" />
              ) : (
                <Clock className="h-5 w-5 mr-2" />
              )}
              <span>Start Verification</span>
            </>
          )}
        </button>
        
        {verificationStatus !== 'idle' && (
          <button
            type="button"
            className={hrSecondaryButtonStyle}
            onClick={() => {
              if (selectedVerificationType === BiometricVerificationType.FACE && videoRef.current?.srcObject) {
                const stream = videoRef.current.srcObject as MediaStream;
                stream.getTracks().forEach(track => track.stop());
                videoRef.current.srcObject = null;
              }
              setVerificationStatus('idle');
              setError(null);
              setVerificationProgress(0);
              setSavedToAttendance(false);
            }}
          >
            <RefreshCw className="h-5 w-5 mr-2" />
            <span>Reset</span>
          </button>
        )}
      </div>
    </div>
  );
};

export default BiometricClockIn; 