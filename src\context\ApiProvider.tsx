import React, { createContext, useContext, useState, ReactNode } from 'react';
import { config } from '../config/environment';

interface ApiContextType {
  isOnline: boolean;
  useMockData: boolean;
  apiBaseUrl: string;
  toggleMockData: () => void;
  setOnlineStatus: (online: boolean) => void;
}

const ApiContext = createContext<ApiContextType | undefined>(undefined);

interface ApiProviderProps {
  children: ReactNode;
}

export const ApiProvider: React.FC<ApiProviderProps> = ({ children }) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [useMockData, setUseMockData] = useState(config.enableMockData);

  const toggleMockData = () => {
    setUseMockData(prev => !prev);
  };

  const setOnlineStatus = (online: boolean) => {
    setIsOnline(online);
  };

  // Listen to online/offline events
  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const value: ApiContextType = {
    isOnline,
    useMockData: useMockData || !isOnline, // Use mock data if offline
    apiBaseUrl: config.apiBaseUrl,
    toggleMockData,
    setOnlineStatus,
  };

  return (
    <ApiContext.Provider value={value}>
      {children}
    </ApiContext.Provider>
  );
};

export const useApi = (): ApiContextType => {
  const context = useContext(ApiContext);
  if (context === undefined) {
    throw new Error('useApi must be used within an ApiProvider');
  }
  return context;
};

// HOC for components that need API context
export const withApiProvider = <P extends object>(
  Component: React.ComponentType<P>
): React.FC<P> => {
  return (props: P) => (
    <ApiProvider>
      <Component {...props} />
    </ApiProvider>
  );
};

export default ApiProvider; 