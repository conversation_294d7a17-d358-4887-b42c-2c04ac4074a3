import { AppDataSource } from '../config/database';
import { AddPaymentDetailsToInvoice1741000000000 } from '../migrations/AddPaymentDetailsToInvoice';

async function runMigration() {
  try {
    // Initialize connection
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }

    console.log('Running migration to add payment details to invoices...');
    
    // Create a query runner
    const queryRunner = AppDataSource.createQueryRunner();
    
    // Run the migration
    const migration = new AddPaymentDetailsToInvoice1741000000000();
    await migration.up(queryRunner);
    
    console.log('Migration completed successfully');
    
    // Close the connection
    await AppDataSource.destroy();
  } catch (error) {
    console.error('Error during migration:', error);
    // Close the connection even if there's an error
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
    }
    process.exit(1);
  }
}

// Run the migration
runMigration(); 