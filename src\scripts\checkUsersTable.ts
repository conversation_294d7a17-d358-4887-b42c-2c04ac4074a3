import { DataSource } from 'typeorm';

// Database configuration
const AppDataSource = new DataSource({
  type: "mysql",
  host: process.env.DB_HOST || "localhost",
  port: parseInt(process.env.DB_PORT || "3306"),
  username: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "root",
  database: process.env.DB_NAME || "ims_db",
  synchronize: false,
  logging: false, // Disable SQL logging for cleaner output
  entities: [],
  subscribers: [],
  migrations: []
});

const checkUsersTable = async () => {
  console.log("Checking users table schema...");

  try {
    // Initialize the data source
    await AppDataSource.initialize();

    // Get query runner
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    
    console.log("\nCurrent users table schema:");
    const columns = await queryRunner.query(`DESCRIBE \`users\``);
    
    columns.forEach((column: any, index: number) => {
      console.log(`${index + 1}. ${column.Field} (${column.Type}) ${column.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${column.Default ? `DEFAULT ${column.Default}` : ''}`);
    });
    
    await queryRunner.release();
    
  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    try {
      await AppDataSource.destroy();
    } catch (error) {
      console.error("Error closing database connection:", error);
    }
  }
};

// Run the check
checkUsersTable(); 