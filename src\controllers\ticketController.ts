import { Request, Response } from 'express';
import { AppDataSource } from '../config/database';
import { Ticket } from '../entities/Ticket';
import { TicketStatus, TicketPriority } from '../types/common';
import { AuthRequest } from '../server/middleware/authMiddleware';
import { Like, MoreThanOrEqual, FindOptionsWhere } from 'typeorm';
import { getRepository } from 'typeorm';
import { User } from '../entities/User';
import { Comment } from '../entities/Comment';
import { isITStaff, isITAdmin } from '../utils/roleChecks';
import { 
  sendTicketCreatedNotification, 
  sendTicketAssignedNotification, 
  sendTicketEscalatedNotification,
  sendTicketInProgressNotification 
} from '../server/utils/notificationUtils';
import { io } from '../server/socket';

type UserRole = 'IT_ADMIN' | 'IT_STAFF' | 'EMPLOYEE' | 'CEO' | 'FINANCE_MANAGER' | 'DEPT_HEAD' | 'VIEW';

const ticketRepository = AppDataSource.getRepository(Ticket);

// Define type-safe where clause creators
const createTicketWhere = (id: string): FindOptionsWhere<Ticket> => ({
  id: parseInt(id)
});

const createUserWhere = (id: string): FindOptionsWhere<User> => ({
  id
});

// Define allowed status transitions with type safety
const ALLOWED_TRANSITIONS: Record<TicketStatus, TicketStatus[]> = {
  [TicketStatus.OPEN]: [TicketStatus.IN_PROGRESS],
  [TicketStatus.IN_PROGRESS]: [TicketStatus.RESOLVED],
  [TicketStatus.RESOLVED]: []
};

// Helper function to check if status transition is allowed
const isStatusTransitionAllowed = (from: TicketStatus, to: TicketStatus): boolean => {
  const allowedTransitions = ALLOWED_TRANSITIONS[from] || [];
  return allowedTransitions.includes(to);
};

export const ticketController = {
  // Create new ticket
  async createTicket(req: AuthRequest, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({ error: 'User not authenticated' });
      }

      // Start a transaction
      const queryRunner = AppDataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // Get the department prefix (first 2 letters)
        const deptPrefix = (req.user.department || 'GN').substring(0, 2).toUpperCase();
        
        // Get current year's last 2 digits
        const yearSuffix = new Date().getFullYear().toString().slice(-2);
        
        // Get count of tickets for this department this year within transaction
        const startOfYear = new Date(new Date().getFullYear(), 0, 1);
        const ticketCount = await queryRunner.manager.count(Ticket, {
          where: {
            ticketNumber: Like(`${deptPrefix}${yearSuffix}%`),
            createdAt: MoreThanOrEqual(startOfYear)
          }
        });
        
        // Generate sequential number (3 digits, zero-padded)
        const sequentialNumber = (ticketCount + 1).toString().padStart(3, '0');
        
        // Create final ticket number format: XXYYNNN (e.g., IT24001)
        const ticketNumber = `${deptPrefix}${yearSuffix}${sequentialNumber}`;

        // Create the ticket
        const ticket = queryRunner.manager.create(Ticket, {
          ticketNumber,
          title: req.body.title,
          description: req.body.description,
          status: TicketStatus.OPEN,
          priority: req.body.priority,
          category: req.body.category,
          createdById: req.user.id,
          visibleTo: [
            req.user.role,
            req.user.department,
            'IT_ADMIN',
            'IT_STAFF',
            'DEPT_HEAD'
          ],
          departmentChain: [req.user.department, 'IT']
        });

        await queryRunner.manager.save(ticket);
        await queryRunner.commitTransaction();
        
        res.status(201).json(ticket);
      } catch (error) {
        await queryRunner.rollbackTransaction();
        throw error;
      } finally {
        await queryRunner.release();
      }
    } catch (error) {
      console.error('Create ticket error:', error);
      res.status(500).json({ error: 'Failed to create ticket' });
    }
  },

  // Add comment to ticket
  async addComment(req: AuthRequest, res: Response) {
    try {
      const user = req.user as User;
      if (!user) {
        return res.status(401).json({ message: 'Unauthorized' });
      }

      const { ticketId } = req.params;
      const { content } = req.body;

      const ticketRepo = getRepository(Ticket);
      const ticket = await ticketRepo.findOne({
        where: { id: parseInt(ticketId) }
      });

      if (!ticket) {
        return res.status(404).json({ message: 'Ticket not found' });
      }

      if (!['OPEN', 'IN_PROGRESS'].includes(ticket.status)) {
        return res.status(400).json({ 
          message: 'Can only add comments to open or in-progress tickets' 
        });
      }

      const commentRepo = getRepository(Comment);
      const comment = commentRepo.create({
        content,
        ticket,
        createdBy: user,
        createdById: user.id,
        ticketId: ticket.id
      });

      await commentRepo.save(comment);

      return res.json({ 
        message: 'Comment added successfully',
        comment
      });

    } catch (error) {
      console.error('Error adding comment:', error);
      return res.status(500).json({ message: 'Error adding comment' });
    }
  },

  // Delete ticket (IT Admin only)
  async deleteTicket(req: AuthRequest, res: Response) {
    try {
      const user = req.user as User;
      if (!user || !isITAdmin(user)) {
        return res.status(403).json({ 
          message: 'Only IT admins can delete tickets' 
        });
      }

      const { ticketId } = req.params;
      const ticketRepo = getRepository(Ticket);
      const ticket = await ticketRepo.findOne({
        where: { id: parseInt(ticketId) }
      });

      if (!ticket) {
        return res.status(404).json({ message: 'Ticket not found' });
      }

      if (!['RESOLVED', 'CLOSED'].includes(ticket.status)) {
        return res.status(400).json({ 
          message: 'Can only delete resolved or closed tickets' 
        });
      }

      await ticketRepo.remove(ticket);
      return res.json({ message: 'Ticket deleted successfully' });

    } catch (error) {
      console.error('Error deleting ticket:', error);
      return res.status(500).json({ message: 'Error deleting ticket' });
    }
  },

  // Get tickets based on user role and permissions
  async getTickets(req: AuthRequest, res: Response) {
    try {
      const { role, department, id } = req.user || {};

      const tickets = await ticketRepository.find({
        where: [
          { visibleTo: Like(`%${role}%`) },
          { visibleTo: Like(`%${department}%`) },
          { createdById: id }
        ],
        relations: ['comments', 'comments.author', 'createdBy', 'assignedTo'],
        order: { createdAt: 'DESC' }
      });

      res.json(tickets);
    } catch (error) {
      console.error('Get tickets error:', error);
      res.status(500).json({ error: 'Failed to fetch tickets' });
    }
  },

  // Update ticket status
  async updateStatus(req: Request, res: Response) {
    try {
      const user = req.user as User;
      if (!user) {
        return res.status(401).json({ message: 'Unauthorized' });
      }

      const { ticketId } = req.params;
      const { status, comment } = req.body;

      const ticketRepo = getRepository(Ticket);
      const ticket = await ticketRepo.findOne({
        where: { id: parseInt(ticketId) },
        relations: ['createdBy', 'assignedTo']
      });

      if (!ticket) {
        return res.status(404).json({ message: 'Ticket not found' });
      }

      if (!isStatusTransitionAllowed(ticket.status, status as TicketStatus)) {
        return res.status(400).json({ 
          message: `Cannot transition ticket from ${ticket.status} to ${status}` 
        });
      }

      // Check permissions for resolving
      if (status === 'RESOLVED' && !isITStaff(user)) {
        return res.status(403).json({ 
          message: 'Only IT staff can resolve tickets' 
        });
      }

      // Save the old status for notification purposes
      const oldStatus = ticket.status;
      
      // Update the ticket status
      ticket.status = status as TicketStatus;

      await ticketRepo.save(ticket);

      // Add system comment
      const commentRepo = getRepository(Comment);
      const systemComment = commentRepo.create({
        content: `Ticket status changed to ${status}${comment ? ` - ${comment}` : ''}`,
        ticket,
        createdBy: user,
        createdById: user.id,
        ticketId: ticket.id,
        isSystemComment: true
      });

      await commentRepo.save(systemComment);
      
      // Send notification to ticket creator when status changes to IN_PROGRESS
      if (status === TicketStatus.IN_PROGRESS && oldStatus !== TicketStatus.IN_PROGRESS) {
        // Make sure we have the ticket creator's ID and the ticket has a creator
        if (ticket.createdBy && ticket.createdBy.id) {
          sendTicketInProgressNotification(
            io,
            ticket.createdBy.id,
            ticket.id.toString(),
            ticket.title,
            ticket.ticketNumber || `#${ticket.id}`,
            user.name || 'An agent'
          );
        }
      }

      return res.json({ 
        message: 'Ticket status updated successfully',
        ticket,
        comment: systemComment
      });

    } catch (error) {
      console.error('Error updating ticket status:', error);
      return res.status(500).json({ 
        message: 'Error updating ticket status' 
      });
    }
  },

  // Refer ticket
  async referTicket(req: Request, res: Response) {
    try {
      const user = req.user as User;
      if (!user || !isITStaff(user)) {
        return res.status(403).json({ 
          message: 'Only IT staff can refer tickets' 
        });
      }

      const { ticketId } = req.params;
      const { department, comment } = req.body;

      const ticketRepo = getRepository(Ticket);
      const ticket = await ticketRepo.findOne({
        where: { id: parseInt(ticketId) }
      });

      if (!ticket) {
        return res.status(404).json({ message: 'Ticket not found' });
      }

      if (!['OPEN', 'IN_PROGRESS'].includes(ticket.status)) {
        return res.status(400).json({ 
          message: 'Can only refer open or in-progress tickets' 
        });
      }

      ticket.department = department;
      await ticketRepo.save(ticket);

      // Add system comment for referral
      const commentRepo = getRepository(Comment);
      const systemComment = commentRepo.create({
        content: `Ticket referred to ${department} department`,
        ticket,
        createdBy: user,
        createdById: user.id,
        ticketId: ticket.id,
        isSystemComment: true
      });

      if (comment) {
        systemComment.content += ` - Note: ${comment}`;
      }

      // Ensure ticketId is not null before saving
      if (!systemComment.ticketId) {
        systemComment.ticketId = ticket.id;
      }

      await commentRepo.save(systemComment);

      return res.json({ 
        message: 'Ticket referred successfully',
        ticket,
        comment: systemComment
      });

    } catch (error) {
      console.error('Error referring ticket:', error);
      return res.status(500).json({ message: 'Error referring ticket' });
    }
  }
};
