import { config } from "dotenv";
import mysql from 'mysql2/promise';

// Load environment variables
config();

// Database configuration
const DB_HOST = process.env.DB_HOST || 'localhost';
const DB_PORT = parseInt(process.env.DB_PORT || '3306');
const DB_USER = process.env.DB_USER || 'root';
const DB_PASSWORD = process.env.DB_PASSWORD || 'root';
const DB_NAME = process.env.DB_NAME || 'ims_db';

async function runDirectRemoval() {
  console.log('Starting direct removal of previousEmployeeId field...');
  let connection;

  try {
    // Create connection
    connection = await mysql.createConnection({
      host: DB_HOST,
      port: DB_PORT,
      user: DB_USER,
      password: DB_PASSWORD,
      database: DB_NAME
    });
    
    console.log('Connected to database');

    // Check if the column exists
    const [columns] = await connection.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? 
      AND TABLE_NAME = 'employee_jobs' 
      AND COLUMN_NAME = 'previousEmployeeId'
    `, [DB_NAME]);

    if (Array.isArray(columns) && columns.length > 0) {
      console.log('previousEmployeeId column found, removing it...');
      
      // Drop the column
      await connection.query(`
        ALTER TABLE employee_jobs DROP COLUMN previousEmployeeId
      `);
      
      console.log('previousEmployeeId column removed successfully!');
    } else {
      console.log('previousEmployeeId column does not exist in employee_jobs table');
    }
    
    console.log('Operation completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error removing previousEmployeeId field:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

// Run the function
runDirectRemoval(); 