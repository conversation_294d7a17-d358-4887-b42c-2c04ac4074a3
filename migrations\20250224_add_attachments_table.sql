-- CreateTable
CREATE TABLE `attachments` (
  `id` VARCHAR(36) NOT NULL,
  `fileName` VARCHAR(255) NOT NULL,
  `fileUrl` VARCHAR(255) NOT NULL,
  `fileType` VARCHAR(100) NOT NULL,
  `ticketId` INT NOT NULL,
  `commentId` INT NULL,
  `uploadedById` VARCHAR(36) NOT NULL,
  `role` ENUM('IT_ADMIN', 'IT_STAFF', 'EMPLOY<PERSON>', 'CEO', 'FINANCE_MANAGER', 'DEPT_HEAD', 'VIEW') NOT NULL DEFAULT 'EMPLOYEE',
  `uploadedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

  PRIMARY KEY (`id`),
  INDEX `idx_ticket_attachments` (`ticketId`),
  INDEX `idx_comment_attachments` (`commentId`),
  INDEX `idx_uploader_attachments` (`uploadedById`),
  CONSTRAINT `fk_attachment_ticket` FOR<PERSON><PERSON><PERSON> KEY (`ticketId`) REFERENCES `tickets` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_attachment_comment` FOREIGN KEY (`commentId`) REFERENCES `comments` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_attachment_user` FOREIGN KEY (`uploadedById`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Migrate existing attachments
INSERT INTO `attachments` (
  `id`,
  `fileName`,
  `fileUrl`,
  `fileType`,
  `ticketId`,
  `uploadedById`,
  `role`,
  `uploadedAt`
)
SELECT 
  COALESCE(JSON_UNQUOTE(JSON_EXTRACT(a.attachment, '$.id')), UUID()),
  JSON_UNQUOTE(JSON_EXTRACT(a.attachment, '$.fileName')),
  JSON_UNQUOTE(JSON_EXTRACT(a.attachment, '$.fileUrl')),
  JSON_UNQUOTE(JSON_EXTRACT(a.attachment, '$.fileType')),
  t.id,
  JSON_UNQUOTE(JSON_EXTRACT(a.attachment, '$.uploadedBy.id')),
  COALESCE(JSON_UNQUOTE(JSON_EXTRACT(a.attachment, '$.uploadedBy.role')), 'EMPLOYEE'),
  COALESCE(
    STR_TO_DATE(JSON_UNQUOTE(JSON_EXTRACT(a.attachment, '$.uploadedAt')), '%Y-%m-%dT%H:%i:%s.%fZ'),
    NOW()
  )
FROM 
  `tickets` t
CROSS JOIN 
  JSON_TABLE(
    COALESCE(t.attachments, '[]'),
    '$[*]' COLUMNS (
      attachment JSON PATH '$'
    )
  ) a
WHERE 
  t.attachments IS NOT NULL;

-- Remove the old attachments column
ALTER TABLE `tickets` DROP COLUMN `attachments`; 