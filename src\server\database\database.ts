import { DataSource } from 'typeorm';
import { User } from '../../entities/User';
import { Ticket } from '../../entities/Ticket';
import { Comment } from '../../entities/Comment';
import { Attachment } from '../../entities/Attachment';
import { KnowledgeBase } from '../../entities/KnowledgeBase';
import { KnowledgeCategory } from '../../entities/KnowledgeCategory';
import { KnowledgeTag } from '../../entities/KnowledgeTag';
import { Vendor } from '../../entities/Vendor';
import { Asset } from '../../entities/Asset';
import { PrinterMaintenance } from '../../entities/PrinterMaintenance';
import { SoftwareLicense } from '../../entities/SoftwareLicense';
import { EmailAccount } from '../../entities/EmailAccount';
import { AssetMaintenance } from '../../entities/AssetMaintenance';
import { BillingInvoice } from '../../entities/BillingInvoice';
import { ITOperationLog } from '../../entities/ITOperationLog';
import { Role } from '../../entities/Role';
import { RoleTemplate } from '../../entities/RoleTemplate';
import { PermissionGroup } from '../../entities/PermissionGroup';
import { UserRoleAssignment } from '../../entities/UserRoleAssignment';
import { Holiday } from '../../entities/Holiday';
import { HolidayConfiguration } from '../../entities/HolidayConfiguration';

export const AppDataSource = new DataSource({
  type: 'mysql',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  username: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_NAME || 'ims_db',
  synchronize: false,
  logging: process.env.NODE_ENV !== 'production',
  entities: [
    User,
    Ticket,
    Comment,
    Attachment,
    KnowledgeBase,
    KnowledgeCategory,
    KnowledgeTag,
    Vendor,
    Asset,
    PrinterMaintenance,
    SoftwareLicense,
    EmailAccount,
    AssetMaintenance,
    BillingInvoice,
    ITOperationLog,
    Role,
    RoleTemplate,
    PermissionGroup,
    UserRoleAssignment,
    Holiday,
    HolidayConfiguration
  ],
}); 