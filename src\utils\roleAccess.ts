import { UserRole } from '../types/common';

// Role capabilities map - DEPRECATED: Use dynamic permission system instead
export const ROLE_CAPABILITIES: Record<UserRole, string[]> = {
  // System Administration
  [UserRole.SYSTEM_ADMIN]: ['*'], // All permissions

  // HR Management Roles
  [UserRole.HR_ADMIN]: [
    'manage_employees',
    'manage_attendance',
    'manage_payroll',
    'manage_hr_documents',
    'view_hr_reports'
  ],
  [UserRole.HR_STAFF]: [
    'manage_employees',
    'manage_attendance',
    'view_hr_reports'
  ],
  [UserRole.HR_SPECIALIST]: [
    'manage_employees',
    'view_hr_reports'
  ],

  // IT Management Roles
  [UserRole.IT_ADMIN]: [
    'manage_users',
    'manage_roles',
    'manage_it_assets',
    'manage_network',
    'manage_security',
    'view_all_tickets',
    'manage_tickets'
  ],
  [UserRole.IT_STAFF]: [
    'manage_it_assets',
    'view_all_tickets',
    'manage_tickets'
  ],
  [UserRole.IT_SUPPORT]: [
    'view_all_tickets',
    'manage_tickets'
  ],
  [UserRole.IT_TECHNICIAN]: [
    'manage_it_assets',
    'manage_tickets'
  ],

  // Management Roles
  [UserRole.DEPT_HEAD]: [
    'view_department_reports',
    'manage_department_tickets'
  ],
  [UserRole.PROJECT_MANAGER]: [
    'view_project_reports',
    'manage_project_tickets'
  ],
  [UserRole.FINANCE_MANAGER]: [
    'view_finance_reports',
    'manage_finance_data'
  ],

  // General Staff
  [UserRole.SENIOR_EMPLOYEE]: [
    'create_tickets',
    'view_own_tickets'
  ],
  [UserRole.EMPLOYEE]: [
    'create_tickets',
    'view_own_tickets'
  ],

  // Specialized Roles
  [UserRole.SECURITY_OFFICER]: [
    'manage_security',
    'view_security_reports'
  ],

  // Dashboard and Analytics
  [UserRole.DASHBOARD_MANAGER]: [
    'manage_dashboards',
    'view_all_dashboards'
  ],
  [UserRole.ANALYTICS_VIEWER]: [
    'view_analytics',
    'view_reports'
  ],

  // Legacy support
  [UserRole.CEO]: ['*'], // All permissions
  [UserRole.VIEW]: [
    'view_own_tickets'
  ],
  [UserRole.ADMIN]: ['*'] // All permissions
};

// Module access control - DEPRECATED: Use dynamic permission system instead
export const MODULE_ACCESS: Record<string, UserRole[]> = {
  'system-admin': [
    UserRole.SYSTEM_ADMIN,
    UserRole.IT_ADMIN,
    UserRole.CEO,
    UserRole.ADMIN
  ],
  'user-management': [
    UserRole.SYSTEM_ADMIN,
    UserRole.IT_ADMIN,
    UserRole.HR_ADMIN,
    UserRole.CEO,
    UserRole.ADMIN
  ],
  'asset-management': [
    UserRole.SYSTEM_ADMIN,
    UserRole.IT_ADMIN,
    UserRole.IT_STAFF,
    UserRole.IT_TECHNICIAN,
    UserRole.CEO,
    UserRole.ADMIN
  ],
  'ticket-management': [
    UserRole.SYSTEM_ADMIN,
    UserRole.IT_ADMIN,
    UserRole.IT_STAFF,
    UserRole.IT_SUPPORT,
    UserRole.IT_TECHNICIAN,
    UserRole.HR_ADMIN,
    UserRole.HR_STAFF,
    UserRole.DEPT_HEAD,
    UserRole.PROJECT_MANAGER,
    UserRole.SENIOR_EMPLOYEE,
    UserRole.EMPLOYEE,
    UserRole.CEO,
    UserRole.ADMIN
  ],
  'hr-management': [
    UserRole.SYSTEM_ADMIN,
    UserRole.HR_ADMIN,
    UserRole.HR_STAFF,
    UserRole.HR_SPECIALIST,
    UserRole.CEO,
    UserRole.ADMIN
  ],
  'dashboard-management': [
    UserRole.SYSTEM_ADMIN,
    UserRole.DASHBOARD_MANAGER,
    UserRole.ANALYTICS_VIEWER,
    UserRole.IT_ADMIN,
    UserRole.HR_ADMIN,
    UserRole.DEPT_HEAD,
    UserRole.PROJECT_MANAGER,
    UserRole.FINANCE_MANAGER,
    UserRole.CEO,
    UserRole.ADMIN
  ]
};

// Helper functions
export const hasCapability = (userRole: UserRole, capability: string): boolean => {
  if (userRole === UserRole.SYSTEM_ADMIN) return true;
  return ROLE_CAPABILITIES[userRole]?.includes(capability) || false;
};

export const canAccessModule = (userRole: UserRole, moduleName: string): boolean => {
  if (userRole === UserRole.SYSTEM_ADMIN) return true;
  return MODULE_ACCESS[moduleName]?.includes(userRole) || false;
};
