export interface Attendance {
  id: number;
  employeeId: number;
  employeeCode?: string; // String-based employee code (e.g., "GC-0403")
  employeeName: string;
  employeePhoto?: string;
  employeeDesignation?: string;
  employeeDepartment?: string;
  department?: string;
  position?: string;
  shift: number;
  date: string;
  checkInTime: string;
  checkOutTime: string | null;
  status: AttendanceStatus;
  notes: string | null;
  location: string | null;
  coordinates?: { lat: number; lng: number } | null;
  ipAddress?: string | null;
  deviceInfo?: string | null;
  workHours: number | null;
  overtime?: number | null;
  isRemote: boolean;
  isRegularized?: boolean;
  regularizedBy?: number | null;
  regularizationReason?: string | null;
  regularizationDate?: string | null;
  approvalStatus?: ApprovalStatus;
  shiftId?: number | null;
  shiftName?: string | null;
  shiftTiming?: { start: string; end: string } | null;
  breakTime?: number | null;
  createdAt?: string;
  updatedAt?: string;
  isImported?: boolean;
}

// Interface for importing attendance data that supports both string employee codes and numeric IDs
export interface AttendanceImportData {
  id?: number;
  employeeId?: number | string; // Support both numeric ID and string code
  employeeCode?: string; // String-based employee code (e.g., "GC-0100")
  employeeName: string;
  date: string;
  checkInTime?: string;
  checkOutTime?: string | null;
  status?: AttendanceStatus | string;
  notes?: string | null;
  location?: string | null;
  workHours?: number | null;
  overtime?: number | null;
  isRemote?: boolean;
  department?: string;
  position?: string;
  shift?: number;
  isImported?: boolean;
}

export enum AttendanceStatus {
  PRESENT = 'present',
  ABSENT = 'absent',
  HALF_DAY = 'half_day',
  LATE = 'late',
  LEAVE = 'leave',
  ON_LEAVE = 'on_leave',
  HOLIDAY = 'holiday',
  WEEKEND = 'weekend',
  WORK_FROM_HOME = 'work_from_home',
  ON_DUTY = 'on_duty',
  PAID_TIME_OFF = 'paid_time_off',
  UNPAID_LEAVE = 'unpaid_leave',
  COMP_OFF = 'comp_off',
  SICK_LEAVE = 'sick_leave',
  ANNUAL_LEAVE = 'annual_leave',
  MATERNITY_LEAVE = 'maternity_leave',
  PATERNITY_LEAVE = 'paternity_leave',
}

export enum ApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
}

export interface AttendanceFilter {
  status: AttendanceStatus[];
  department: string[];
  location: string[];
  position: string[];
  shift: string[];
  search: string;
  dateRange: {
    start: string;
    end: string;
  };
}

export interface AttendanceSummary {
  totalPresent: number;
  totalAbsent: number;
  totalLate: number;
  totalHalfDay: number;
  totalLeave: number;
  totalWorkingDays: number;
  requiredWorkingDays: number;
  presentPercentage: number;
  totalWorkHours: number;
  averageWorkHours: number;
  totalOvertime: number;
  lateArrivalCount: number;
  earlyDepartureCount: number;
  consecutiveAbsences: number;
  longestStreak: number;
}

export interface EmployeeAttendance {
  employee: {
    id: number;
    name: string;
    photo?: string;
    designation: string;
    department: string;
    employeeCode: string;
    joiningDate?: string;
    shift?: {
      id: number;
      name: string;
      timing: { start: string; end: string };
    };
  };
  attendanceRecords: Attendance[];
  summary: AttendanceSummary;
}

export interface Shift {
  id: number;
  name: string;
  description?: string;
  startTime: string;
  endTime: string;
  breakDuration: number;
  workingDays: number[];
  isFlexible: boolean;
  graceTimeInMinutes?: number;
  requiredWorkHours: number;
  color?: string;
  isActive: boolean;
}

export interface ShiftAssignment {
  id?: number;
  employeeId: number;
  shiftId: number;
  startDate: string;
  endDate?: string;
  isPermanent: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface AttendanceRegularizationRequest {
  id?: number;
  employeeId: number;
  employeeName: string;
  date: string;
  type: string;
  requestedTime: string;
  reason: string;
  status: string;
  approverId?: number;
  approverName?: string;
  approverComments?: string;
  createdAt: string;
  updatedAt?: string;
}

export interface AttendanceReport {
  title: string;
  description: string;
  timeRange: {
    start: string;
    end: string;
  };
  generatedBy: {
    id: number;
    name: string;
  };
  generatedAt: string;
  departments?: string[];
  employees?: number[];
  format: 'pdf' | 'excel' | 'csv';
  reportType: 'daily' | 'monthly' | 'custom' | 'annual';
  id: number;
  url?: string;
  isScheduled?: boolean;
  scheduleFrequency?: 'daily' | 'weekly' | 'monthly' | 'quarterly';
}

export interface AttendanceSettings {
  workingHoursPerDay: number;
  workingDays: number[];
  graceTimeInMinutes: number;
  allowRegularization: boolean;
  regularizationWindow: number;
  requireApproval: boolean;
  autoApprove: boolean;
  allowWFH: boolean;
  requireLocationCapture: boolean;
  captureIP: boolean;
  captureDevice: boolean;
  allowOvertime: boolean;
  overtimeCalculationStart: number;
  overtimeRate: number;
  shiftRotationEnabled: boolean;
  allowEmployeesToViewTeamAttendance: boolean;
  automaticCheckout: boolean;
  automaticCheckoutTime: string;
  attendanceNotifications: boolean;
  trackBreakTime: boolean;
}

// Leave types are now completely dynamic - managed through database

export enum LeaveStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled'
}

export interface LeaveBalance {
  employeeId: number;
  leaveType: string; // Dynamic leave type
  total: number;
  used: number;
  pending: number;
  remaining: number;
  carryForward?: number;
  expiryDate?: string;
}

export interface LeaveRequest {
  id?: number;
  employeeId: number;
  employeeName: string;
  employeeCode?: string; // String-based employee code (e.g., "EMP001")
  leaveType: string; // Dynamic leave type
  startDate: string;
  endDate: string;
  reason: string;
  status: LeaveStatus;
  approverId?: number;
  approverName?: string;
  approverComments?: string;
  isHalfDay?: boolean;
  documents?: string[];
  emergencyContact?: string; // Emergency contact information
  createdAt: string;
  updatedAt?: string;
  // Additional properties for employee information
  department?: string;
  position?: string;
  managerName?: string;
  contact?: string;
  days?: number;
  daysRequested?: number;
  appliedOn?: string;
  approvedOn?: string;
  rejectedOn?: string;
  rejectionReason?: string;
  rejectedBy?: string;
  rejectedByName?: string;
  approvedBy?: string;
  approvedByName?: string;
  createdBy?: string;
  createdByName?: string;
  source?: 'EMPLOYEE' | 'HR_ADMIN' | 'MANAGER';
  originalStartDate?: string;
  originalEndDate?: string;
  originalReason?: string;
  modificationHistory?: string;
  currentStage?: string;
  isUrgent?: boolean;
  createdById?: number;
  approvals?: Array<{
    id: number;
    level: 'MANAGER' | 'HR' | 'DIRECTOR';
    approverId?: string;
    approverName?: string;
    status: 'pending' | 'approved' | 'rejected' | 'cancelled';
    comments?: string;
    decisionAt?: string;
    sequence: number;
  }>;
}

export interface LeaveAccrualRule {
  id?: number;
  leaveType: string; // Dynamic leave type
  accrualFrequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually';
  accrualAmount: number;
  maxAccumulation: number;
  carryOverLimit: number;
  carryOverExpiry: number; // days until carried over leave expires
  effectiveDate: string;
  isProrated: boolean;
  waitingPeriod: number; // days from joining before leave is accrued
  description?: string;
  applicableRoles?: string[];
  applicableDepartments?: string[];
}

export interface LeaveCalculation {
  leaveType: string; // Dynamic leave type
  openingBalance: number;
  accrued: number;
  used: number;
  pending: number;
  adjustments: number;
  carryForward: number;
  expired: number;
  currentBalance: number;
  comment?: string;
  lastCalculationDate: string;
}

export interface HolidayCalendar {
  id?: number;
  name: string;
  year: number;
  holidays: Holiday[];
  applicableDepartments?: string[];
  applicableLocations?: string[];
}

export interface Holiday {
  id?: number;
  name: string;
  date: string;
  description?: string;
  isRecurring: boolean;
  isOptional: boolean;
  applicableRegions?: string[];
}

export interface CompensatoryTimeTracking {
  id?: number;
  employeeId: number;
  employeeName: string;
  dateWorked: string;
  hoursWorked: number;
  reason: string;
  expiryDate: string;
  status: 'pending' | 'approved' | 'rejected' | 'used' | 'expired';
  approvedBy?: number;
  approverName?: string;
  dateUsed?: string;
  leaveRequestId?: number;
}

export interface LeavePolicySettings {
  minDaysNotice: number;
  allowLeaveModification: boolean;
}

// New types for enhanced leave management features
export interface LeaveAdjustment {
  id?: number;
  employeeId: number;
  leaveType: string; // Dynamic leave type
  adjustmentType: 'credit' | 'debit';
  amount: number;
  reason: string;
  adjustmentDate: string;
  effectiveDate: string;
  approvedBy?: number;
  approvedAt?: string;
  notes?: string;
  isActive: boolean;
  createdAt: string;
  createdBy: number;
}

export interface LeavePolicyVersion {
  id?: number;
  policyId: number;
  version: string;
  changeDescription: string;
  effectiveDate: string;
  createdBy: number;
  createdAt: string;
  isActive: boolean;
  policyData: string; // JSON string of the policy configuration
  approvedBy?: number;
  approvedAt?: string;
}

export interface LeavePeriodLock {
  id?: number;
  year: number;
  month: number;
  lockType: 'requests' | 'adjustments' | 'all';
  isLocked: boolean;
  lockedBy: number;
  lockedAt: string;
  reason?: string;
  unlockDate?: string;
}

export interface LeaveResetConfiguration {
  id?: number;
  leaveType: string; // Dynamic leave type
  resetFrequency: 'annually' | 'monthly' | 'quarterly';
  resetDate: string; // Format: MM-DD for annual, DD for monthly
  carryForwardEnabled: boolean;
  carryForwardLimit: number;
  carryForwardExpiryMonths: number;
  autoResetEnabled: boolean;
  notificationDaysBefore: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LeaveQuotaAssignment {
  id?: number;
  employeeId: number;
  leaveType: string; // Dynamic leave type
  year: number;
  assignmentType: 'auto' | 'manual';
  baseAllocation: number;
  adjustments: number;
  finalAllocation: number;
  effectiveDate: string;
  expiryDate?: string;
  assignedBy: number;
  assignedAt: string;
  notes?: string;
  isActive: boolean;
} 