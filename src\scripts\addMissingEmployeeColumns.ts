import { DataSource } from 'typeorm';

// Database configuration
const AppDataSource = new DataSource({
  type: "mysql",
  host: process.env.DB_HOST || "localhost",
  port: parseInt(process.env.DB_PORT || "3306"),
  username: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "root",
  database: process.env.DB_NAME || "ims_db",
  synchronize: false,
  logging: true,
  entities: [],
  subscribers: [],
  migrations: []
});

const addMissingColumns = async () => {
  console.log("Adding missing columns to employees table...\n");

  try {
    // Initialize the data source
    await AppDataSource.initialize();

    // Get query runner
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    
    // Check existing columns
    const columns = await queryRunner.query("DESCRIBE employees");
    const existingColumns = columns.map((col: any) => col.Field);
    
    console.log("Checking for missing columns...");
    
    // Add fatherName column if it doesn't exist
    if (!existingColumns.includes('fatherName')) {
      console.log("Adding fatherName column...");
      await queryRunner.query(`
        ALTER TABLE \`employees\` 
        ADD COLUMN \`fatherName\` VARCHAR(255) NULL
      `);
      console.log("✅ fatherName column added successfully");
    } else {
      console.log("✅ fatherName column already exists");
    }
    
    // Add status_date column if it doesn't exist
    if (!existingColumns.includes('status_date')) {
      console.log("Adding status_date column...");
      await queryRunner.query(`
        ALTER TABLE \`employees\` 
        ADD COLUMN \`status_date\` DATETIME NULL
      `);
      console.log("✅ status_date column added successfully");
    } else {
      console.log("✅ status_date column already exists");
    }
    
    // Verify the changes
    console.log("\n📋 Updated table structure:");
    const updatedColumns = await queryRunner.query("DESCRIBE employees");
    
    const fatherNameColumn = updatedColumns.find((col: any) => col.Field === 'fatherName');
    const statusDateColumn = updatedColumns.find((col: any) => col.Field === 'status_date');
    
    if (fatherNameColumn) {
      console.log("✅ fatherName column confirmed:");
      console.log(`   Field: ${fatherNameColumn.Field}, Type: ${fatherNameColumn.Type}, Null: ${fatherNameColumn.Null}`);
    }
    
    if (statusDateColumn) {
      console.log("✅ status_date column confirmed:");
      console.log(`   Field: ${statusDateColumn.Field}, Type: ${statusDateColumn.Type}, Null: ${statusDateColumn.Null}`);
    }
    
    await queryRunner.release();
    await AppDataSource.destroy();
    
    console.log("\n🎉 Database update completed successfully!");
    
  } catch (error) {
    console.error("❌ Error updating database:", error);
  }
};

addMissingColumns().catch(console.error); 