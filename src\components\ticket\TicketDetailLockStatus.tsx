import React from 'react';
import { Lock, User, Clock } from 'lucide-react';
import { Ticket } from '../../types/common';

interface TicketDetailLockStatusProps {
  ticket: Ticket;
  currentUserId: string;
}

export const TicketDetailLockStatus: React.FC<TicketDetailLockStatusProps> = ({
  ticket,
  currentUserId
}) => {
  if (!ticket.lockedById) {
    return null;
  }

  const isLockedByCurrentUser = ticket.lockedById === currentUserId;
  const lockedAt = ticket.lockedAt ? new Date(ticket.lockedAt) : null;
  
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  };
  
  return (
    <div className={`flex items-center gap-2 p-3 rounded-lg border ${
      isLockedByCurrentUser ? 'bg-amber-50 border-amber-200 text-amber-700' : 'bg-gray-50 border-gray-200 text-gray-700'
    }`}>
      {isLockedByCurrentUser ? (
        <>
          <Lock className="h-5 w-5 text-amber-500" />
          <div>
            <p className="font-medium">You are currently working on this ticket</p>
            {lockedAt && (
              <div className="flex items-center gap-1 text-xs mt-1">
                <Clock className="h-3 w-3" />
                <span>Started at {formatDate(lockedAt)}</span>
              </div>
            )}
          </div>
        </>
      ) : (
        <>
          <User className="h-5 w-5 text-gray-500" />
          <div>
            <p className="font-medium">
              This ticket is being worked on by {ticket.lockedBy?.name}
            </p>
            {lockedAt && (
              <div className="flex items-center gap-1 text-xs mt-1">
                <Clock className="h-3 w-3" />
                <span>Started at {formatDate(lockedAt)}</span>
              </div>
            )}
            <p className="text-xs mt-1">
              {ticket.createdBy.id === currentUserId ? 
                'As the ticket creator, you can still add comments.' : 
                'You cannot make changes to this ticket while it is being worked on.'}
            </p>
          </div>
        </>
      )}
    </div>
  );
}; 