import { EntityRepository, Repository, Between, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { PrinterMaintenance } from '../entities/PrinterMaintenance';

@EntityRepository(PrinterMaintenance)
export class PrinterMaintenanceRepository extends Repository<PrinterMaintenance> {
  
  async findByDateRange(startDate: Date, endDate: Date): Promise<PrinterMaintenance[]> {
    return this.find({
      where: {
        serviceDate: Between(startDate, endDate)
      },
      relations: ['asset', 'vendor', 'assignee', 'approvedBy']
    });
  }

  async findPendingApprovals(): Promise<PrinterMaintenance[]> {
    return this.find({
      where: {
        approvalStatus: 'Pending'
      },
      relations: ['asset', 'vendor', 'assignee']
    });
  }

  async findApproved(): Promise<PrinterMaintenance[]> {
    return this.find({
      where: {
        approvalStatus: 'Approved',
        submittedToFinance: false
      },
      relations: ['asset', 'vendor', 'assignee', 'approvedBy']
    });
  }

  async findByDepartment(department: string): Promise<PrinterMaintenance[]> {
    return this.createQueryBuilder('maintenance')
      .innerJoinAndSelect('maintenance.asset', 'asset')
      .innerJoinAndSelect('maintenance.vendor', 'vendor')
      .innerJoinAndSelect('maintenance.assignee', 'assignee')
      .leftJoinAndSelect('maintenance.approvedBy', 'approvedBy')
      .where('asset.department = :department', { department })
      .getMany();
  }

  async findByVendor(vendorId: string): Promise<PrinterMaintenance[]> {
    return this.find({
      where: {
        vendorId
      },
      relations: ['asset', 'vendor', 'assignee', 'approvedBy']
    });
  }

  async generateFinanceReport(startDate: Date, endDate: Date): Promise<any[]> {
    const maintenanceRecords = await this.createQueryBuilder('maintenance')
      .innerJoinAndSelect('maintenance.asset', 'asset')
      .innerJoinAndSelect('maintenance.vendor', 'vendor')
      .innerJoinAndSelect('maintenance.assignee', 'assignee')
      .leftJoinAndSelect('maintenance.approvedBy', 'approvedBy')
      .where('maintenance.serviceDate BETWEEN :startDate AND :endDate', { 
        startDate,
        endDate
      })
      .andWhere('maintenance.approvalStatus = :status', { status: 'Approved' })
      .orderBy('maintenance.serviceDate', 'ASC')
      .getMany();

    const report = maintenanceRecords.map(record => {
      return {
        serviceDate: record.serviceDate,
        assetTag: record.asset.assetTag,
        assetModel: record.asset.model,
        department: record.asset.department,
        location: record.asset.location,
        assignee: record.assignee.id,
        assigneeName: record.assignee.name,
        vendor: record.vendor.vendorName,
        invoiceNumber: record.invoiceNumber,
        invoiceAmount: record.invoiceAmount,
        approvedBy: record.approvedBy ? record.approvedBy.name : 'Pending',
        approvalDate: record.approvalDate,
        submittedToFinance: record.submittedToFinance,
        submittedToFinanceDate: record.submittedToFinanceDate
      };
    });

    return report;
  }

  async markAsSubmittedToFinance(ids: string[]): Promise<void> {
    await this.createQueryBuilder()
      .update(PrinterMaintenance)
      .set({ 
        submittedToFinance: true,
        submittedToFinanceDate: new Date()
      })
      .whereInIds(ids)
      .execute();
  }
} 