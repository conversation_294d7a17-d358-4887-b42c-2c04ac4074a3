import { Request, Response, NextFunction } from 'express';
import { UserRole, ROLE_HIERARCHY } from '../../types/roles';
import { User } from '../../entities/User';
import { RoleManagementService } from '../services/roleManagementService';

const roleService = new RoleManagementService();

export const checkRole = (roles: UserRole[] | string[]) => {
  return async (req: Request & { user?: User & { role?: UserRole } }, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Unauthorized - User not authenticated' });
      }

      const userRole = req.user.role;
      
      if (!userRole) {
        return res.status(403).json({ 
          message: 'Forbidden - No role assigned' 
        });
      }

      // Check if user has any of the required roles or inherits from them
      const hasAccess = await hasRequiredRole(req.user.id, roles);
      
      if (!hasAccess) {
        return res.status(403).json({ 
          message: 'Forbidden - You do not have permission to access this resource' 
        });
      }
      
      next();
    } catch (error) {
      console.error('Role check error:', error);
      return res.status(500).json({ message: 'Internal server error during role verification' });
    }
  };
};

/**
 * Check if user has required role or inherits from it
 */
async function hasRequiredRole(userId: string, requiredRoles: UserRole[] | string[]): Promise<boolean> {
  // First check direct role assignments
  const assignments = await roleService.getUserRoles(userId);
  
  for (const assignment of assignments) {
    if (requiredRoles.includes(assignment.role.name)) {
      return true;
    }

    // Check role hierarchy
    let currentRole = assignment.role.name;
    while (ROLE_HIERARCHY[currentRole]) {
      currentRole = ROLE_HIERARCHY[currentRole];
      if (requiredRoles.includes(currentRole)) {
        return true;
      }
    }
  }

  return false;
} 