import React from 'react';
import { Routes, Route } from 'react-router-dom';
import {
  RecruitmentDashboard,
  JobPostingsList,
  JobPostingForm,
  JobPostingDetails,
  JobApplicationsList,
  ApplicationDetails,
  JobApplicationForm
} from './index';
import RecruitmentTest from './RecruitmentTest';
import SimpleTest from './SimpleTest';
import {
  JobPostingsPlaceholder,
  ApplicationsPlaceholder,
  JobFormPlaceholder,
  GenericPlaceholder
} from './PlaceholderComponents';

/**
 * Recruitment Module Routes
 * 
 * This component defines all the routes for the recruitment module.
 * Add this to your main router configuration.
 * 
 * Example usage in your main App.tsx or router:
 * 
 * <Route path="/hr/recruitment/*" element={<RecruitmentRoutes />} />
 */
const RecruitmentRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Main Recruitment Dashboard - Using simple test component for debugging */}
      <Route index element={<SimpleTest />} />

      {/* Full Dashboard (when API is ready) */}
      <Route path="dashboard" element={<RecruitmentDashboard />} />
      
      {/* Job Postings Routes - Using placeholders for now */}
      <Route path="job-postings" element={<JobPostingsPlaceholder />} />
      <Route path="job-postings/new" element={<JobFormPlaceholder />} />
      <Route path="job-postings/:id" element={<GenericPlaceholder title="Job Posting Details" description="Detailed job posting view will be available once the backend API is implemented." />} />
      <Route path="job-postings/:id/edit" element={<JobFormPlaceholder />} />

      {/* Applications Routes - Using placeholders for now */}
      <Route path="applications" element={<ApplicationsPlaceholder />} />
      <Route path="applications/:id" element={<GenericPlaceholder title="Application Details" description="Detailed candidate application view will be available once the backend API is implemented." />} />

      {/* Full Components (when API is ready) */}
      <Route path="full/job-postings" element={<JobPostingsList />} />
      <Route path="full/job-postings/new" element={<JobPostingForm />} />
      <Route path="full/job-postings/:id" element={<JobPostingDetails />} />
      <Route path="full/job-postings/:id/edit" element={<JobPostingForm />} />
      <Route path="full/applications" element={<JobApplicationsList />} />
      <Route path="full/applications/:id" element={<ApplicationDetails />} />

      {/* Public Routes (for external job seekers) */}
      <Route path="apply/:jobId" element={<JobApplicationForm />} />
    </Routes>
  );
};

export default RecruitmentRoutes;
