import { DataSource } from 'typeorm';
import dotenv from 'dotenv';
import { User } from '../entities/User';

// Load environment variables
dotenv.config();

// Database configuration
const AppDataSource = new DataSource({
  type: "mysql",
  host: process.env.DB_HOST || "localhost",
  port: parseInt(process.env.DB_PORT || "3306"),
  username: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "root",
  database: process.env.DB_NAME || "ims_db",
  synchronize: false,
  logging: true,
  entities: [User],
  subscribers: [],
  migrations: []
});

const updateAdminPermissions = async () => {
  console.log("Starting update of IT_ADMIN permissions...");

  try {
    // Initialize the data source
    await AppDataSource.initialize();
    console.log("Database connection established");

    // Get user repository
    const userRepository = AppDataSource.getRepository(User);
    
    // Find all IT_ADMIN users
    const adminUsers = await userRepository.find({
      where: { role: 'IT_ADMIN' }
    });
    
    console.log(`Found ${adminUsers.length} IT_ADMIN users to update`);
    
    // Update each admin's permissions to include all HR permissions
    for (const admin of adminUsers) {
      const currentPermissions = admin.permissions || {};
      
      // Ensure all HR permissions are set to true
      const updatedPermissions = {
        ...currentPermissions,
        // HR permissions
        canCreateEmployee: true,
        canEditEmployee: true,
        canDeleteEmployee: true,
        canViewEmployees: true,
        canManageAttendance: true,
        canManageLeave: true,
        canManagePayroll: true,
        canManagePerformance: true
      };
      
      // Update the user
      admin.permissions = updatedPermissions;
      await userRepository.save(admin);
      
      console.log(`Updated permissions for admin user: ${admin.name} (${admin.id})`);
    }
    
    console.log("Update completed successfully!");
    
    // Close the connection
    await AppDataSource.destroy();
    console.log("Database connection closed");
    
    process.exit(0);
  } catch (error) {
    console.error("Error updating permissions:", error);
    process.exit(1);
  }
};

// Run the update
updateAdminPermissions(); 