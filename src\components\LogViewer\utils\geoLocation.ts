interface GeoLocation {
  country: string;
  countryCode: string;
  region: string;
  city: string;
  latitude: number;
  longitude: number;
  timezone: string;
  isp: string;
  threat_score: number;
}

interface GeoLocationCache {
  [ip: string]: {
    data: GeoLocation;
    timestamp: number;
  };
}

// Pakistan cities data for rotation
const PAKISTAN_CITIES = [
  {
    city: 'Islamabad',
    region: 'Islamabad Capital Territory',
    latitude: 33.6844,
    longitude: 73.0479
  },
  {
    city: 'Lahore',
    region: 'Punjab',
    latitude: 31.5204, 
    longitude: 74.3587
  },
  {
    city: 'Karachi',
    region: 'Sindh',
    latitude: 24.8607,
    longitude: 67.0011
  },
  {
    city: 'Peshawar',
    region: 'Khyber Pakhtunkhwa',
    latitude: 34.0151,
    longitude: 71.5249
  },
  {
    city: 'Quetta',
    region: 'Balochistan',
    latitude: 30.1798,
    longitude: 66.9750
  }
];

// Cache geolocation data to avoid unnecessary API calls
const geoLocationCache: GeoLocationCache = {};
const CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours

// Default Pakistan location as fallback
const DEFAULT_PAKISTAN_LOCATION: GeoLocation = {
  country: 'Pakistan',
  countryCode: 'PK',
  region: 'Islamabad Capital Territory',
  city: 'Islamabad',
  latitude: 33.6844,
  longitude: 73.0479,
  timezone: 'Asia/Karachi',
  isp: 'PTCL',
  threat_score: 0
};

/**
 * Extract IP address from log details or action
 */
export const extractIpAddress = (text: string): string | null => {
  // IPv4 regex pattern
  const ipv4Pattern = /\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b/;
  // IPv6 regex pattern (simplified)
  const ipv6Pattern = /\b(?:[A-F0-9]{1,4}:){7}[A-F0-9]{1,4}\b/i;
  
  if (!text) return null;
  
  // Try to find IPv4 first as it's more common
  const ipv4Match = text.match(ipv4Pattern);
  if (ipv4Match) return ipv4Match[0];
  
  // Try IPv6 if no IPv4 found
  const ipv6Match = text.match(ipv6Pattern);
  if (ipv6Match) return ipv6Match[0];
  
  return null;
};

/**
 * Get a Pakistan location based on IP
 * This ensures we always show locations in Pakistan
 */
const getPakistanLocation = (ip: string): GeoLocation => {
  // Use a consistent algorithm to pick a Pakistan city based on IP
  // This ensures the same IP always maps to the same city
  const octets = ip.split('.').map(Number);
  const sum = octets.reduce((total, num) => total + num, 0);
  const index = sum % PAKISTAN_CITIES.length;
  
  // Get the selected city data
  const cityData = PAKISTAN_CITIES[index];
  
  // Create and return the location data
  return {
    country: 'Pakistan',
    countryCode: 'PK',
    region: cityData.region,
    city: cityData.city,
    latitude: cityData.latitude,
    longitude: cityData.longitude,
    timezone: 'Asia/Karachi',
    isp: 'PTCL',
    threat_score: 0
  };
};

/**
 * Get geolocation data for an IP address
 * ALWAYS returns a location in Pakistan
 */
export const getIPGeolocation = async (ip: string): Promise<GeoLocation> => {
  try {
    // Check if we have a valid cache entry
    if (
      geoLocationCache[ip] &&
      Date.now() - geoLocationCache[ip].timestamp < CACHE_EXPIRY
    ) {
      return geoLocationCache[ip].data;
    }

    // ALWAYS use Pakistan location
    const geoData = getPakistanLocation(ip);
    
    // Special case for 136.* IPs - always use Islamabad
    if (ip.startsWith('136.')) {
      geoData.city = 'Islamabad';
      geoData.region = 'Islamabad Capital Territory';
      geoData.latitude = 33.6844;
      geoData.longitude = 73.0479;
    }
    
    // Cache the data
    geoLocationCache[ip] = {
      data: geoData,
      timestamp: Date.now()
    };
    
    return geoData;
  } catch (error) {
    console.error('Error in getIPGeolocation:', error);
    // Return default Pakistan location as last resort
    return DEFAULT_PAKISTAN_LOCATION;
  }
};
