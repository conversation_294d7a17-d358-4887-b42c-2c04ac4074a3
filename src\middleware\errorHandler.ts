import { Request, Response, NextFunction } from 'express';
import { QueryFailedError, EntityNotFoundError } from 'typeorm';
import logger from '../utils/logger';

export class AppError extends Error {
  constructor(
    public statusCode: number,
    public code: string,
    message: string
  ) {
    super(message);
    this.name = 'AppError';
    Error.captureStackTrace(this, this.constructor);
  }
}

export const errorHandler = (
  err: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.error('Error:', {
    name: err.name,
    message: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method
  });

  // Handle known error types
  if (err instanceof AppError) {
    return res.status(err.statusCode).json({
      code: err.code,
      message: err.message
    });
  }

  if (err instanceof EntityNotFoundError) {
    return res.status(404).json({
      code: 'RESOURCE_NOT_FOUND',
      message: 'The requested resource was not found'
    });
  }

  if (err instanceof QueryFailedError) {
    // Handle unique constraint violations
    if ((err as any).code === '23505') {
      return res.status(409).json({
        code: 'DUPLICATE_ENTRY',
        message: 'A resource with these unique fields already exists'
      });
    }

    return res.status(400).json({
      code: 'DATABASE_ERROR',
      message: 'An error occurred while processing your request'
    });
  }

  // Handle validation errors from class-validator
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      code: 'VALIDATION_ERROR',
      message: 'Invalid input data',
      errors: (err as any).errors
    });
  }

  // Handle JWT errors
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      code: 'INVALID_TOKEN',
      message: 'Invalid authentication token'
    });
  }

  if (err.name === 'TokenExpiredError') {
    return res.status(401).json({
      code: 'TOKEN_EXPIRED',
      message: 'Authentication token has expired'
    });
  }

  // Default error response for unhandled errors
  return res.status(500).json({
    code: 'INTERNAL_SERVER_ERROR',
    message: 'An unexpected error occurred'
  });
}; 