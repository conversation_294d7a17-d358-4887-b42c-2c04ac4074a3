import React, { useState, useEffect } from 'react';
import { X, Download, AlertCircle, FileIcon, Video, ImageIcon, ExternalLink } from 'lucide-react';
import { fileService } from '../services/fileService';

interface FileViewerProps {
  fileUrl: string;
  fileName: string;
  fileType: string;
  onClose: () => void;
}

// Function to get a sanitized path for blob URL creation
const getSanitizedFilePath = (fileUrl: string) => {
  // Extract just the filename from the path to avoid exposing backend structure
  const pathParts = fileUrl.split('/');
  return pathParts[pathParts.length - 1];
};

export const FileViewer: React.FC<FileViewerProps> = ({ fileUrl, fileName, fileType, onClose }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [secureFileUrl, setSecureFileUrl] = useState<string>('');
  
  // Fetch secure URL when component mounts
  useEffect(() => {
    const fetchSecureUrl = async () => {
      try {
        const result = await fileService.getSecureFileUrl(fileUrl);
        if (result.success) {
          setSecureFileUrl(result.fileUrl);
        } else {
          setError(result.message || 'Failed to access file securely');
        }
      } catch (err) {
        setError('Error accessing file');
        console.error('File access error:', err);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchSecureUrl();
  }, [fileUrl]);

  const getFileIcon = () => {
    if (fileType.startsWith('image/')) return <ImageIcon className="w-12 h-12 text-blue-500" />;
    if (fileType.startsWith('video/')) return <Video className="w-12 h-12 text-purple-500" />;
    if (fileType === 'application/pdf') return <FileIcon className="w-12 h-12 text-red-500" />;
    return <FileIcon className="w-12 h-12 text-gray-500" />;
  };

  const handleImageError = () => {
    if (retryCount < 3) {
      setRetryCount(prev => prev + 1);
      const newUrl = `${secureFileUrl}?t=${Date.now()}`;
      const img = document.querySelector<HTMLImageElement>('.viewer-image');
      if (img) {
        img.src = newUrl;
      }
    } else {
      setError('Failed to load image');
      setIsLoading(false);
    }
  };

  // Handler to open file in a new tab
  const handleOpenInNewTab = (e: React.MouseEvent) => {
    e.preventDefault();
    window.open(secureFileUrl, '_blank', 'noopener,noreferrer');
  };
  
  // Handler for secure downloads
  const handleDownload = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    fileService.downloadFile(fileUrl, fileName);
  };

  const getFileContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-64 bg-gray-900">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
        </div>
      );
    }
    
    if (error) {
      return (
        <div className="flex flex-col items-center justify-center p-8 bg-white rounded-lg">
          <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
          <p className="text-lg font-medium text-red-500">{error}</p>
        </div>
      );
    }

    // Images - display in viewer
    if (fileType.startsWith('image/')) {
      return (
        <div className="relative max-h-[80vh] flex items-center justify-center bg-gray-900 rounded-lg overflow-hidden">
          <img
            src={secureFileUrl}
            alt={fileName}
            className="max-w-full max-h-[80vh] object-contain viewer-image"
            onLoad={() => setIsLoading(false)}
            onError={handleImageError}
          />
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-900">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
            </div>
          )}
        </div>
      );
    }

    // Videos - display in viewer
    if (fileType.startsWith('video/')) {
      return (
        <div className="relative max-h-[80vh] bg-black rounded-lg overflow-hidden">
          <video
            controls
            className="max-w-full max-h-[80vh]"
            src={secureFileUrl}
            onLoadStart={() => setIsLoading(true)}
            onLoadedData={() => setIsLoading(false)}
            onError={() => {
              setError('Failed to load video');
              setIsLoading(false);
            }}
          >
            Your browser does not support the video tag.
          </video>
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
            </div>
          )}
        </div>
      );
    }

    // For all other file types - show file info and open in new tab button
    return (
      <div className="flex flex-col items-center justify-center p-8 bg-white rounded-lg">
        {getFileIcon()}
        <p className="text-lg font-medium mt-4 mb-2">{fileName}</p>
        <p className="text-sm text-gray-500 mb-4">{fileType}</p>
        <div className="flex gap-3">
          <button
            onClick={handleOpenInNewTab}
          className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            <ExternalLink className="w-4 h-4" />
            Open File
          </button>
          <button
            onClick={handleDownload}
            className="flex items-center gap-2 px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-800 transition-colors"
        >
          <Download className="w-4 h-4" />
            Download
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className="relative max-w-4xl w-full bg-white rounded-lg overflow-hidden">
        <div className="absolute top-4 right-4 z-10">
          <button
            onClick={onClose}
            className="p-2 bg-black bg-opacity-50 hover:bg-opacity-75 rounded-full text-white transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <div className="p-4 bg-gray-900">
          {getFileContent()}
        </div>
        
        <div className="flex items-center justify-between p-4 bg-white border-t">
          <p className="font-medium text-gray-900 truncate max-w-[60%]">{fileName}</p>
        </div>
      </div>
    </div>
  );
}; 