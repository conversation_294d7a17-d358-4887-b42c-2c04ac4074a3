import { AppDataSource } from '../config/database';
import { KnowledgeCategory } from '../entities/KnowledgeCategory';

const defaultCategories = [
  {
    name: 'Hardware',
    description: 'Information about computer hardware, peripherals, and physical components',
    slug: 'hardware',
    displayOrder: 1
  },
  {
    name: 'Software',
    description: 'Information about software applications, operating systems, and programs',
    slug: 'software',
    displayOrder: 2
  },
  {
    name: 'Network',
    description: 'Information about networking, connectivity, and network infrastructure',
    slug: 'network',
    displayOrder: 3
  },
  {
    name: 'Security',
    description: 'Information about cybersecurity, data protection, and security best practices',
    slug: 'security',
    displayOrder: 4
  },
  {
    name: 'Database',
    description: 'Information about database systems, data management, and storage',
    slug: 'database',
    displayOrder: 5
  },
  {
    name: 'Mobile',
    description: 'Information about mobile devices, apps, and mobile computing',
    slug: 'mobile',
    displayOrder: 6
  },
  {
    name: 'Desktop',
    description: 'Information about desktop computers, workstations, and desktop environments',
    slug: 'desktop',
    displayOrder: 7
  },
  {
    name: 'System',
    description: 'Information about system administration, maintenance, and configuration',
    slug: 'system',
    displayOrder: 8
  },
  {
    name: 'CPU & Processor',
    description: 'Information about processors, CPU architecture, and processing units',
    slug: 'cpu-processor',
    displayOrder: 9
  }
];

async function createDefaultCategories() {
  try {
    // Initialize the data source
    await AppDataSource.initialize();
    console.log('Database connection initialized');

    const categoryRepository = AppDataSource.getRepository(KnowledgeCategory);

    // Create categories
    for (const categoryData of defaultCategories) {
      // Check if category already exists
      const existingCategory = await categoryRepository.findOne({
        where: { slug: categoryData.slug }
      });

      if (!existingCategory) {
        const category = new KnowledgeCategory();
        category.name = categoryData.name;
        category.description = categoryData.description;
        category.slug = categoryData.slug;
        category.displayOrder = categoryData.displayOrder;

        await categoryRepository.save(category);
        console.log(`Created category: ${category.name}`);
      } else {
        console.log(`Category ${categoryData.name} already exists`);
      }
    }

    console.log('Default categories created successfully');

  } catch (error) {
    console.error('Error creating default categories:', error);
  } finally {
    // Close the connection
    await AppDataSource.destroy();
    console.log('Database connection closed');
  }
}

// Run the script
createDefaultCategories(); 