import React from 'react';
import { FileText, AlertCircle, Filter, Search, RefreshCw } from 'lucide-react';

export const LogViewerEmpty: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center py-16 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
      <div className="relative mb-6">
        <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full opacity-20 blur-lg animate-pulse"></div>
        <div className="relative bg-white dark:bg-gray-800 rounded-full p-4 shadow-lg">
          <FileText className="h-12 w-12 text-blue-500 dark:text-blue-400" />
        </div>
      </div>
      
      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">No Logs Found</h3>
      
      <p className="text-gray-600 dark:text-gray-300 text-center max-w-md mb-6">
        There are no logs matching your current filters or the system logs are empty.
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full max-w-2xl">
        <div className="bg-white dark:bg-gray-750 rounded-lg p-4 border border-gray-200 dark:border-gray-700 flex items-center gap-3 shadow-sm">
          <div className="rounded-full bg-blue-100 dark:bg-blue-900/30 p-2">
            <Search className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white text-sm">Try a different search</h4>
            <p className="text-gray-500 dark:text-gray-400 text-xs">Adjust your search terms or clear filters</p>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-750 rounded-lg p-4 border border-gray-200 dark:border-gray-700 flex items-center gap-3 shadow-sm">
          <div className="rounded-full bg-purple-100 dark:bg-purple-900/30 p-2">
            <Filter className="h-5 w-5 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white text-sm">Reset all filters</h4>
            <p className="text-gray-500 dark:text-gray-400 text-xs">Clear filters to see all available logs</p>
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-750 rounded-lg p-4 border border-gray-200 dark:border-gray-700 flex items-center gap-3 shadow-sm">
          <div className="rounded-full bg-green-100 dark:bg-green-900/30 p-2">
            <RefreshCw className="h-5 w-5 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white text-sm">Refresh logs</h4>
            <p className="text-gray-500 dark:text-gray-400 text-xs">Update to see the most recent activities</p>
          </div>
        </div>
      </div>
      
      <div className="mt-8 flex items-center gap-2 text-sm text-yellow-700 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20 px-4 py-3 rounded-lg border border-yellow-100 dark:border-yellow-800/30 shadow-sm">
        <AlertCircle className="h-5 w-5 text-yellow-500 dark:text-yellow-400 flex-shrink-0" />
        <span>System activity will be recorded here once actions are performed.</span>
      </div>
    </div>
  );
}; 