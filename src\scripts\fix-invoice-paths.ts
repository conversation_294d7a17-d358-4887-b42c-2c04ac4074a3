import { AppDataSource } from '../config/database';
import { PrinterMaintenance } from '../entities/PrinterMaintenance';
import { IsNull, Not } from 'typeorm';
import * as path from 'path';
import * as fs from 'fs';

async function fixInvoicePaths() {
  try {
    // Initialize database connection
    await AppDataSource.initialize();
    console.log('Database connection initialized');

    // Get all maintenance records with invoice files
    const maintenanceRepo = AppDataSource.getRepository(PrinterMaintenance);
    const records = await maintenanceRepo.find({
      where: {
        invoiceFilePath: Not(IsNull())
      }
    });

    console.log(`Found ${records.length} records with invoice files`);

    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'uploads');
    const invoicesDir = path.join(uploadsDir, 'invoices');
    
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
      console.log('Created uploads directory');
    }
    if (!fs.existsSync(invoicesDir)) {
      fs.mkdirSync(invoicesDir, { recursive: true });
      console.log('Created uploads/invoices directory');
    }

    // Process each record
    for (const record of records) {
      if (!record.invoiceFilePath) continue;

      console.log(`Processing record ${record.id}...`);
      console.log('Current invoice path:', record.invoiceFilePath);

      // Extract just the filename from the path
      const filename = path.basename(record.invoiceFilePath);
      const newPath = path.join('uploads', 'invoices', filename);
      
      console.log('New path will be:', newPath);

      // If the file exists in the old location, try to move it
      if (fs.existsSync(record.invoiceFilePath)) {
        try {
          // Copy file to new location
          fs.copyFileSync(record.invoiceFilePath, path.join(process.cwd(), newPath));
          console.log('File copied to new location');

          // Update database record
          record.invoiceFilePath = filename; // Store only the filename
          await maintenanceRepo.save(record);
          console.log('Database record updated');

          // Try to delete the old file
          try {
            fs.unlinkSync(record.invoiceFilePath);
            console.log('Old file deleted');
          } catch (deleteError: any) {
            console.log('Could not delete old file:', deleteError.message);
          }
        } catch (moveError: any) {
          console.error('Error moving file:', moveError.message);
        }
      } else {
        // If file doesn't exist in old location, just update the database
        console.log('File not found in old location, updating database only');
        record.invoiceFilePath = filename;
        await maintenanceRepo.save(record);
        console.log('Database record updated');
      }
    }

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    // Close database connection
    await AppDataSource.destroy();
    console.log('Database connection closed');
  }
}

// Run the migration
fixInvoicePaths().then(() => {
  console.log('Script finished');
  process.exit(0);
}).catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
}); 