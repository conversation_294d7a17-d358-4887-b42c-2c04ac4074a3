import express from 'express';
import {
  createEmployee,
  getAllEmployees,
  getEmployeesForListing,
  getEmployeeById,
  updateEmployee,
  deleteEmployee,
  uploadEmployeeDocuments,
  getEmployeeDocuments,
  downloadEmployeeDocument,
  deleteEmployeeDocument,
  repairEmployeeData
} from '../controllers/employeeController';
import path from 'path';

const router = express.Router();

// Debug route
router.get('/debug', (req, res) => {
  console.log('Employee Debug route hit');
  return res.json({
    success: true,
    message: 'Employee API is working',
    timestamp: new Date().toISOString()
  });
});

// Create a new employee
router.post('/', createEmployee);

// Get all employees for listing (with minimal data)
router.get('/listing', getEmployeesForListing);

// Get all employees (with full data)
router.get('/', getAllEmployees);

// Get employee by ID
router.get('/:id', getEmployeeById);

// Update employee
router.put('/:id', updateEmployee);

// Delete employee
router.delete('/:id', deleteEmployee);

// Upload employee documents
router.post('/:id/documents', uploadEmployeeDocuments);

// Get employee documents
router.get('/:id/documents', getEmployeeDocuments);

// Download a specific employee document
router.get('/:id/documents/:documentId', downloadEmployeeDocument);

// Delete a specific employee document
router.delete('/:id/documents/:documentId', deleteEmployeeDocument);

// Secure document access endpoint
router.post('/documents/secure-access', (req, res) => {
  try {
    const { filePath } = req.body;
    
    if (!filePath) {
      return res.status(400).json({
        success: false,
        message: 'File path is required'
      });
    }
    
    // For security, don't allow paths with ".." to prevent directory traversal
    if (filePath.includes('..')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid file path'
      });
    }
    
    // Handle relative paths
    let secureUrl;
    if (filePath.startsWith('/')) {
      // If it's already a server path, use it directly
      secureUrl = `${req.protocol}://${req.get('host')}${filePath}`;
    } else if (filePath.startsWith('uploads/')) {
      // For paths starting with uploads/, just add the leading slash
      secureUrl = `${req.protocol}://${req.get('host')}/${filePath}`;
    } else {
      // Otherwise, assume it's a relative path within uploads
      secureUrl = `${req.protocol}://${req.get('host')}/uploads/${filePath}`;
    }
    
    console.log(`Generated secure URL for document: ${secureUrl}`);
    
    return res.json({
      success: true,
      secureUrl,
      expiresIn: 3600 // Token valid for 1 hour
    });
  } catch (error) {
    console.error('Error generating secure document URL:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to generate secure access URL'
    });
  }
});

// Employee payroll data endpoint
router.get('/:id/payroll', (req, res) => {
  try {
    const { id } = req.params;
    
    // For now, return structured placeholder data
    // TODO: Integrate with actual payroll system
    const payrollData = {
      lastPayment: {
        date: new Date().toISOString().split('T')[0],
        amount: '0.00',
        currency: 'PKR'
      },
      ytdEarnings: '0.00',
      payslips: [],
      bankDetails: {
        bankName: 'Not specified',
        accountNumber: 'Not specified'
      }
    };
    
    res.json({
      success: true,
      data: payrollData
    });
  } catch (error) {
    console.error('Error fetching employee payroll data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch payroll data'
    });
  }
});

// Employee performance data endpoint
router.get('/:id/performance', (req, res) => {
  try {
    const { id } = req.params;
    
    // For now, return structured placeholder data
    // TODO: Integrate with actual performance management system
    const performanceData = {
      currentRating: null,
      lastReviewDate: null,
      nextReviewDate: null,
      goals: [],
      achievements: []
    };
    
    res.json({
      success: true,
      data: performanceData
    });
  } catch (error) {
    console.error('Error fetching employee performance data:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch performance data'
    });
  }
});

// Employee attendance summary endpoint
router.get('/:id/attendance-summary', (req, res) => {
  try {
    const { id } = req.params;
    const { month, year } = req.query;
    
    // For now, return structured placeholder data
    // TODO: Integrate with actual attendance system
    const attendanceSummary = {
      totalDays: 30,
      presentDays: 22,
      absentDays: 0,
      leaveDays: 2,
      holidayDays: 6,
      workingDays: 24,
      punctualityScore: 95
    };
    
    res.json({
      success: true,
      data: attendanceSummary
    });
  } catch (error) {
    console.error('Error fetching employee attendance summary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch attendance summary'
    });
  }
});

// Repair employee data - creates missing related entities
router.post('/repair-data', repairEmployeeData);

export default router; 