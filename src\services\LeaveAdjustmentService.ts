import { LeaveAdjustment } from '../types/attendance';

export class LeaveAdjustmentService {
  private static instance: LeaveAdjustmentService;

  public static getInstance(): LeaveAdjustmentService {
    if (!LeaveAdjustmentService.instance) {
      LeaveAdjustmentService.instance = new LeaveAdjustmentService();
    }
    return LeaveAdjustmentService.instance;
  }

  /**
   * Create a new leave adjustment
   */
  async createAdjustment(adjustment: Omit<LeaveAdjustment, 'id' | 'createdAt'>): Promise<LeaveAdjustment> {
    // Validate adjustment
    this.validateAdjustment(adjustment);

    const newAdjustment: LeaveAdjustment = {
      ...adjustment,
      id: Date.now(), // Mock ID generation
      createdAt: new Date().toISOString()
    };

    // In a real implementation, this would save to database
    console.log('Creating leave adjustment:', newAdjustment);

    // Update employee's leave balance
    await this.updateLeaveBalance(newAdjustment);

    return newAdjustment;
  }

  /**
   * Get adjustments for an employee
   */
  async getEmployeeAdjustments(employeeId: number, year?: number): Promise<LeaveAdjustment[]> {
    // Mock data - in real implementation, fetch from database
    const mockAdjustments: LeaveAdjustment[] = [
      {
        id: 1,
        employeeId,
        leaveType: 'ANNUAL',
        adjustmentType: 'credit',
        amount: 2,
        reason: 'Additional leave for overtime work',
        adjustmentDate: '2024-01-15',
        effectiveDate: '2024-01-15',
        approvedBy: 1,
        approvedAt: '2024-01-15T10:00:00Z',
        notes: 'Approved by HR Manager',
        isActive: true,
        createdAt: '2024-01-15T09:00:00Z',
        createdBy: 1
      }
    ];

    return year 
      ? mockAdjustments.filter(adj => new Date(adj.effectiveDate).getFullYear() === year)
      : mockAdjustments;
  }

  /**
   * Get all adjustments with filters
   */
  async getAdjustments(filters: {
    employeeId?: number;
    leaveType?: string;
    adjustmentType?: 'credit' | 'debit';
    startDate?: string;
    endDate?: string;
    approvedBy?: number;
  }): Promise<LeaveAdjustment[]> {
    // Mock implementation - in real app, this would query database with filters
    const allAdjustments = await this.getEmployeeAdjustments(filters.employeeId || 0);
    
    return allAdjustments.filter(adj => {
      if (filters.leaveType && adj.leaveType !== filters.leaveType) return false;
      if (filters.adjustmentType && adj.adjustmentType !== filters.adjustmentType) return false;
      if (filters.startDate && adj.effectiveDate < filters.startDate) return false;
      if (filters.endDate && adj.effectiveDate > filters.endDate) return false;
      if (filters.approvedBy && adj.approvedBy !== filters.approvedBy) return false;
      return true;
    });
  }

  /**
   * Approve an adjustment
   */
  async approveAdjustment(adjustmentId: number, approvedBy: number): Promise<LeaveAdjustment> {
    // Mock implementation
    const adjustment: LeaveAdjustment = {
      id: adjustmentId,
      employeeId: 1,
      leaveType: 'ANNUAL',
      adjustmentType: 'credit',
      amount: 2,
      reason: 'Additional leave',
      adjustmentDate: new Date().toISOString().split('T')[0],
      effectiveDate: new Date().toISOString().split('T')[0],
      approvedBy,
      approvedAt: new Date().toISOString(),
      isActive: true,
      createdAt: new Date().toISOString(),
      createdBy: 1
    };

    await this.updateLeaveBalance(adjustment);
    return adjustment;
  }

  /**
   * Validate adjustment data
   */
  private validateAdjustment(adjustment: Omit<LeaveAdjustment, 'id' | 'createdAt'>): void {
    if (!adjustment.employeeId) {
      throw new Error('Employee ID is required');
    }

    if (!adjustment.leaveType) {
      throw new Error('Leave type is required');
    }

    if (!adjustment.amount || adjustment.amount <= 0) {
      throw new Error('Adjustment amount must be greater than 0');
    }

    if (!adjustment.reason?.trim()) {
      throw new Error('Reason is required for leave adjustment');
    }

    if (!adjustment.effectiveDate) {
      throw new Error('Effective date is required');
    }

    // Validate effective date is not in the future beyond reasonable limit
    const effectiveDate = new Date(adjustment.effectiveDate);
    const maxFutureDate = new Date();
    maxFutureDate.setFullYear(maxFutureDate.getFullYear() + 1);

    if (effectiveDate > maxFutureDate) {
      throw new Error('Effective date cannot be more than 1 year in the future');
    }
  }

  /**
   * Update employee's leave balance after adjustment
   */
  private async updateLeaveBalance(adjustment: LeaveAdjustment): Promise<void> {
    // In real implementation, this would update the LeaveBalance entity
    console.log(`Updating leave balance for employee ${adjustment.employeeId}:`, {
      leaveType: adjustment.leaveType,
      adjustmentType: adjustment.adjustmentType,
      amount: adjustment.amount,
      effectiveDate: adjustment.effectiveDate
    });

    // Mock balance update logic
    const balanceChange = adjustment.adjustmentType === 'credit' ? adjustment.amount : -adjustment.amount;
    console.log(`Balance change: ${balanceChange} days`);
  }

  /**
   * Get adjustment summary for reporting
   */
  async getAdjustmentSummary(filters: {
    startDate: string;
    endDate: string;
    departmentId?: number;
    leaveType?: string;
  }): Promise<{
    totalCredits: number;
    totalDebits: number;
    netAdjustment: number;
    adjustmentCount: number;
    byLeaveType: Record<string, { credits: number; debits: number; net: number }>;
  }> {
    const adjustments = await this.getAdjustments({
      startDate: filters.startDate,
      endDate: filters.endDate,
      leaveType: filters.leaveType
    });

    const summary = {
      totalCredits: 0,
      totalDebits: 0,
      netAdjustment: 0,
      adjustmentCount: adjustments.length,
      byLeaveType: {} as Record<string, { credits: number; debits: number; net: number }>
    };

    adjustments.forEach(adj => {
      const amount = adj.amount;
      
      if (adj.adjustmentType === 'credit') {
        summary.totalCredits += amount;
      } else {
        summary.totalDebits += amount;
      }

      // By leave type
      if (!summary.byLeaveType[adj.leaveType]) {
        summary.byLeaveType[adj.leaveType] = { credits: 0, debits: 0, net: 0 };
      }

      if (adj.adjustmentType === 'credit') {
        summary.byLeaveType[adj.leaveType].credits += amount;
      } else {
        summary.byLeaveType[adj.leaveType].debits += amount;
      }

      summary.byLeaveType[adj.leaveType].net = 
        summary.byLeaveType[adj.leaveType].credits - summary.byLeaveType[adj.leaveType].debits;
    });

    summary.netAdjustment = summary.totalCredits - summary.totalDebits;

    return summary;
  }
}

export const leaveAdjustmentService = LeaveAdjustmentService.getInstance(); 