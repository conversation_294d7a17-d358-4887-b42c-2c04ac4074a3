import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

// Import the main LeaveManagement component
import LeaveManagement from '../components/HR/leave/LeaveManagement';

const LeaveManagementRoutes: React.FC = () => {
  // Set default title
  useEffect(() => {
    document.title = 'Leave Management - HR Management | IT-MS';
  }, []);

  return (
    <Routes>
      <Route index element={<Navigate to="/hr/leave-management/overview" replace />} />
      <Route path="*" element={
        <LeaveManagement
          userRole="admin"
          currentUserId={1}
          currentUserName="Admin"
        />
      } />
    </Routes>
  );
};

export default LeaveManagementRoutes; 