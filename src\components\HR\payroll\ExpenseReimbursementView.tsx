import React from 'react';
import { DollarSign, File, Search, Filter, Calendar } from 'lucide-react';

const ExpenseReimbursementView: React.FC = () => {
  return (
    <div className="p-4">
      <h2 className="text-xl font-semibold text-gray-900 mb-6">Expense Reimbursement</h2>
      
      {/* Simple Placeholder */}
      <div className="bg-white rounded-lg shadow p-6">
        <p className="text-gray-600 mb-4">
          This is a simplified placeholder for the Expense Reimbursement system. The full functionality is being implemented.
        </p>
        
        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-sm font-medium text-blue-800 mb-2">Features coming soon:</h3>
          <ul className="list-disc pl-5 text-sm text-blue-600 space-y-1">
            <li>Submit expense claims with receipts</li>
            <li>Track reimbursement status</li>
            <li>Corporate card management</li>
            <li>Expense reports and analytics</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ExpenseReimbursementView; 