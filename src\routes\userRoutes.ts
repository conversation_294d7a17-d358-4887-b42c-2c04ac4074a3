import { Router } from 'express';
import { getUsers, getUserById, createUser, updateUser, deleteUser } from '../controllers/userController';
import { validateUserCreation, validateUserUpdate, validatePagination, validate } from '../middleware/validation';
import { apiLimiter } from '../middleware/rateLimit';
import { authenticate } from '../middleware/auth';
import { authorize } from '../middleware/authorize';
import { UserRole } from '../entities/User';

const router = Router();

// Apply rate limiting to all user routes
router.use(apiLimiter);

// Get users with pagination and filtering
router.get('/',
  authenticate,
  authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF]),
  validatePagination,
  validate,
  getUsers
);

// Get user by ID
router.get('/:id',
  authenticate,
  authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF]),
  getUserById
);

// Create new user
router.post('/',
  authenticate,
  authorize([UserRole.IT_ADMIN]),
  validateUserCreation,
  validate,
  createUser
);

// Update user
router.put('/:id',
  authenticate,
  authorize([UserRole.IT_ADMIN]),
  validateUserUpdate,
  validate,
  updateUser
);

// Delete user
router.delete('/:id',
  authenticate,
  authorize([UserRole.IT_ADMIN]),
  deleteUser
);

export default router; 