import React, { useState, useEffect } from 'react';
import { 
  FileText, 
  Filter, 
  Download, 
  Plus, 
  Calendar, 
  Search,
  BarChart2,
  Pie<PERSON>hart,
  TrendingUp,
  FileSpreadsheet,
  File,
  ArrowUpRight,
  ChevronRight,
  AlertCircle,
  X
} from 'lucide-react';
import { PayrollReport } from '../../../types/payroll';

interface PayrollReportsProps {
  isAdmin?: boolean;
}

const PayrollReports: React.FC<PayrollReportsProps> = ({ isAdmin = false }) => {
  const [loading, setLoading] = useState(true);
  const [reports, setReports] = useState<PayrollReport[]>([]);
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    reportType: '',
    dateRange: { start: '', end: '' }
  });
  
  // Mock report generation
  const [generatingReport, setGeneratingReport] = useState(false);
  const [reportForm, setReportForm] = useState({
    title: '',
    startDate: '',
    endDate: '',
    reportType: 'summary',
    format: 'pdf',
    departments: [] as string[]
  });

  // Fetch reports (simulated)
  useEffect(() => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      const mockReports: PayrollReport[] = [
        {
          id: 1,
          title: 'January 2023 - Payroll Summary',
          period: {
            startDate: '2023-01-01',
            endDate: '2023-01-31'
          },
          generatedBy: {
            id: 1,
            name: 'Admin User'
          },
          generatedAt: '2023-02-02T10:15:30Z',
          format: 'pdf',
          reportType: 'summary',
          totalEmployees: 48,
          totalSalaryPaid: 1920000,
          totalTaxPaid: 192000,
          totalDeductions: 96000,
          url: '#'
        },
        {
          id: 2,
          title: 'February 2023 - Payroll Summary',
          period: {
            startDate: '2023-02-01',
            endDate: '2023-02-28'
          },
          generatedBy: {
            id: 1,
            name: 'Admin User'
          },
          generatedAt: '2023-03-02T09:30:15Z',
          format: 'excel',
          reportType: 'detailed',
          totalEmployees: 50,
          totalSalaryPaid: 2050000,
          totalTaxPaid: 205000,
          totalDeductions: 102500,
          url: '#'
        },
        {
          id: 3,
          title: 'Q1 2023 - Tax Deduction Report',
          period: {
            startDate: '2023-01-01',
            endDate: '2023-03-31'
          },
          generatedBy: {
            id: 2,
            name: 'Finance Manager'
          },
          generatedAt: '2023-04-05T14:20:00Z',
          format: 'pdf',
          reportType: 'tax',
          departments: ['Engineering', 'HR', 'Finance'],
          totalEmployees: 52,
          totalSalaryPaid: 6300000,
          totalTaxPaid: 630000,
          totalDeductions: 315000,
          url: '#'
        },
        {
          id: 4,
          title: 'March 2023 - Department-wise Salary Breakdown',
          period: {
            startDate: '2023-03-01',
            endDate: '2023-03-31'
          },
          generatedBy: {
            id: 1,
            name: 'Admin User'
          },
          generatedAt: '2023-04-02T11:45:10Z',
          format: 'excel',
          reportType: 'custom',
          departments: ['Engineering', 'Product', 'Marketing', 'Sales'],
          totalEmployees: 52,
          totalSalaryPaid: 2150000,
          totalTaxPaid: 215000,
          totalDeductions: 107500,
          url: '#'
        }
      ];
      
      setReports(mockReports);
      setLoading(false);
    }, 1000);
  }, []);

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFilters({
        ...filters,
        [parent]: {
          ...filters[parent as keyof typeof filters] as any,
          [child]: value
        }
      });
    } else {
      setFilters({
        ...filters,
        [name]: value
      });
    }
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      reportType: '',
      dateRange: { start: '', end: '' }
    });
  };

  const handleReportFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setReportForm({
      ...reportForm,
      [name]: value
    });
  };

  const handleGenerateReport = () => {
    setGeneratingReport(true);
    
    // Simulate report generation
    setTimeout(() => {
      const newReport: PayrollReport = {
        id: Math.max(...reports.map(r => r.id)) + 1,
        title: reportForm.title,
        period: {
          startDate: reportForm.startDate,
          endDate: reportForm.endDate
        },
        generatedBy: {
          id: 1,
          name: 'Admin User'
        },
        generatedAt: new Date().toISOString(),
        format: reportForm.format as 'pdf' | 'excel' | 'csv',
        reportType: reportForm.reportType as 'summary' | 'detailed' | 'tax' | 'custom',
        departments: reportForm.departments,
        totalEmployees: 53,
        totalSalaryPaid: 2200000,
        totalTaxPaid: 220000,
        totalDeductions: 110000,
        url: '#'
      };
      
      setReports([newReport, ...reports]);
      setGeneratingReport(false);
      setShowGenerateModal(false);
      
      // Reset form
      setReportForm({
        title: '',
        startDate: '',
        endDate: '',
        reportType: 'summary',
        format: 'pdf',
        departments: []
      });
    }, 2000);
  };

  const filteredReports = reports.filter(report => {
    // Filter by search term
    if (filters.search && 
        !report.title.toLowerCase().includes(filters.search.toLowerCase())) {
      return false;
    }
    
    // Filter by report type
    if (filters.reportType && report.reportType !== filters.reportType) {
      return false;
    }
    
    // Filter by date range - start
    if (filters.dateRange.start && report.period.startDate < filters.dateRange.start) {
      return false;
    }
    
    // Filter by date range - end
    if (filters.dateRange.end && report.period.endDate > filters.dateRange.end) {
      return false;
    }
    
    return true;
  });

  const formatCurrency = (amount: number = 0) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'PKR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getReportTypeIcon = (type: string) => {
    switch (type) {
      case 'summary':
        return <BarChart2 className="h-5 w-5 text-blue-500" />;
      case 'detailed':
        return <FileSpreadsheet className="h-5 w-5 text-green-500" />;
      case 'tax':
        return <PieChart className="h-5 w-5 text-red-500" />;
      case 'custom':
        return <TrendingUp className="h-5 w-5 text-purple-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'pdf':
        return <FileText className="h-4 w-4 text-red-500" />;
      case 'excel':
        return <FileSpreadsheet className="h-4 w-4 text-green-500" />;
      case 'csv':
        return <FileText className="h-4 w-4 text-blue-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Payroll Reports</h3>
          <p className="text-sm text-gray-500">Generate and view payroll reports</p>
        </div>
        
        {/* Actions */}
        <div className="flex flex-wrap gap-2">
          {isAdmin && (
            <button
              onClick={() => setShowGenerateModal(true)}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Plus className="h-4 w-4 mr-2" />
              Generate Report
            </button>
          )}
        </div>
      </div>
      
      {/* Filters */}
      <div className="mb-6 p-4 bg-gray-50 rounded-md border border-gray-200">
        <div className="flex flex-wrap gap-4 items-end">
          <div>
            <label htmlFor="search" className="block text-xs font-medium text-gray-700 mb-1">Search</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                id="search"
                name="search"
                value={filters.search}
                onChange={handleFilterChange}
                placeholder="Search reports"
                className="pl-10 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="reportType" className="block text-xs font-medium text-gray-700 mb-1">Report Type</label>
            <select
              id="reportType"
              name="reportType"
              value={filters.reportType}
              onChange={handleFilterChange}
              className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
            >
              <option value="">All Types</option>
              <option value="summary">Summary</option>
              <option value="detailed">Detailed</option>
              <option value="tax">Tax</option>
              <option value="custom">Custom</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="dateRange.start" className="block text-xs font-medium text-gray-700 mb-1">From Date</label>
            <input
              type="date"
              id="dateRange.start"
              name="dateRange.start"
              value={filters.dateRange.start}
              onChange={handleFilterChange}
              className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <label htmlFor="dateRange.end" className="block text-xs font-medium text-gray-700 mb-1">To Date</label>
            <input
              type="date"
              id="dateRange.end"
              name="dateRange.end"
              value={filters.dateRange.end}
              onChange={handleFilterChange}
              className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <button 
              onClick={clearFilters}
              className="px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>
      
      {/* Reports Dashboard Summary */}
      <div className="mb-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-blue-100 rounded-md p-3">
                <BarChart2 className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Reports</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">{reports.length}</div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-green-100 rounded-md p-3">
                <FileText className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Salary Processed</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {formatCurrency(reports.reduce((sum, report) => sum + report.totalSalaryPaid, 0))}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-yellow-100 rounded-md p-3">
                <PieChart className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Tax Deducted</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {formatCurrency(reports.reduce((sum, report) => sum + report.totalTaxPaid, 0))}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-red-100 rounded-md p-3">
                <TrendingUp className="h-6 w-6 text-red-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Employees Paid</dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {Math.max(...reports.map(report => report.totalEmployees || 0))}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Reports List */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <>
          {filteredReports.length === 0 ? (
            <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
              <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No reports found</h3>
              <p className="text-gray-500 max-w-md mx-auto mb-6">
                {filters.search || filters.reportType || filters.dateRange.start || filters.dateRange.end ? 
                  'Try adjusting your filters to see more results.' : 
                  'Generate your first payroll report to see it here.'}
              </p>
              {isAdmin && (
                <button
                  onClick={() => setShowGenerateModal(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Generate Report
                </button>
              )}
            </div>
          ) : (
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <ul className="divide-y divide-gray-200">
                {filteredReports.map((report) => (
                  <li key={report.id}>
                    <a href={report.url} className="block hover:bg-gray-50">
                      <div className="px-4 py-4 sm:px-6">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            {getReportTypeIcon(report.reportType)}
                            <p className="ml-3 text-sm font-medium text-blue-600 truncate">{report.title}</p>
                          </div>
                          <div className="ml-2 flex-shrink-0 flex">
                            <p className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                              {report.reportType.charAt(0).toUpperCase() + report.reportType.slice(1)}
                            </p>
                          </div>
                        </div>
                        <div className="mt-2 sm:flex sm:justify-between">
                          <div className="sm:flex">
                            <p className="flex items-center text-sm text-gray-500">
                              <Calendar className="flex-shrink-0 mr-1.5 h-4 w-4 text-gray-400" />
                              {new Date(report.period.startDate).toLocaleDateString()} - {new Date(report.period.endDate).toLocaleDateString()}
                            </p>
                            <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                              {getFormatIcon(report.format)}
                              <span className="ml-1 capitalize">{report.format}</span>
                            </p>
                          </div>
                          <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                            <p className="text-sm text-gray-500">
                              {formatCurrency(report.totalSalaryPaid)}
                            </p>
                            <ChevronRight className="flex-shrink-0 ml-2 h-4 w-4 text-gray-400" />
                          </div>
                        </div>
                      </div>
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </>
      )}
      
      {/* Generate Report Modal */}
      {showGenerateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Generate Payroll Report
              </h3>
              <button
                onClick={() => setShowGenerateModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                  Report Title
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={reportForm.title}
                  onChange={handleReportFormChange}
                  placeholder="e.g., June 2023 - Payroll Summary"
                  className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
                    Start Date
                  </label>
                  <input
                    type="date"
                    id="startDate"
                    name="startDate"
                    value={reportForm.startDate}
                    onChange={handleReportFormChange}
                    className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  />
                </div>
                
                <div>
                  <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
                    End Date
                  </label>
                  <input
                    type="date"
                    id="endDate"
                    name="endDate"
                    value={reportForm.endDate}
                    onChange={handleReportFormChange}
                    className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="reportType" className="block text-sm font-medium text-gray-700 mb-1">
                  Report Type
                </label>
                <select
                  id="reportType"
                  name="reportType"
                  value={reportForm.reportType}
                  onChange={handleReportFormChange}
                  className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                >
                  <option value="summary">Summary Report</option>
                  <option value="detailed">Detailed Report</option>
                  <option value="tax">Tax Deduction Report</option>
                  <option value="custom">Custom Report</option>
                </select>
              </div>
              
              <div>
                <label htmlFor="format" className="block text-sm font-medium text-gray-700 mb-1">
                  Export Format
                </label>
                <select
                  id="format"
                  name="format"
                  value={reportForm.format}
                  onChange={handleReportFormChange}
                  className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                >
                  <option value="pdf">PDF</option>
                  <option value="excel">Excel</option>
                  <option value="csv">CSV</option>
                </select>
              </div>
              
              <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <AlertCircle className="h-5 w-5 text-yellow-400" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-yellow-700">
                      This will generate a report based on the payroll data for the selected period.
                      Make sure all payroll calculations are finalized before generating this report.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-5 flex justify-end">
              <button
                type="button"
                onClick={() => setShowGenerateModal(false)}
                className="mr-3 inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleGenerateReport}
                disabled={!reportForm.title || !reportForm.startDate || !reportForm.endDate || generatingReport}
                className={`inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white ${
                  !reportForm.title || !reportForm.startDate || !reportForm.endDate || generatingReport 
                    ? 'bg-gray-300 cursor-not-allowed' 
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                {generatingReport ? (
                  <>
                    <div className="mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Generating...
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-4 w-4" />
                    Generate Report
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PayrollReports; 