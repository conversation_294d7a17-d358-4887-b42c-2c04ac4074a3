import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>onte<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Ta<PERSON>Trigger 
} from '../../ui/Tabs';
import { 
  Clock, 
  Calendar, 
  Users, 
  BarChart4, 
  Settings, 
  Moon, 
  Fingerprint,
  CircleDollarSign,
  ClipboardList,
  ArrowLeft
} from 'lucide-react';
import BiometricClockIn from './BiometricClockIn';
import OvertimeCalculator from './OvertimeCalculator';
import { BiometricVerificationType, OvertimeType, ShiftType } from '../../../types/timeAttendance';
import { PayrollCurrency } from '../../../types/payroll';

// Mock employee data for demo
const DEMO_EMPLOYEE = {
  id: 101,
  name: '<PERSON>',
  department: 'Engineering',
  position: 'Senior Developer',
  baseSalary: 95000,
  hourlyRate: 45.67,
  currency: PayrollCurrency.USD,
  shift: ShiftType.REGULAR
};

interface AdvancedTimeAttendanceProps {
  onReturnToStandard?: () => void;
  employeeData?: {
    id: number;
    name: string;
    department?: string;
    position?: string;
    baseSalary?: number;
    hourlyRate?: number;
    currency?: PayrollCurrency;
    shift?: ShiftType;
  };
}

/**
 * Advanced Time & Attendance Component
 * 
 * This component integrates biometric verification, 
 * overtime calculations, and shift differential management.
 */
const AdvancedTimeAttendance: React.FC<AdvancedTimeAttendanceProps> = ({
  onReturnToStandard,
  employeeData
}) => {
  // Use provided employee data or fall back to demo data
  const employee = {
    ...DEMO_EMPLOYEE,
    ...(employeeData || {}),
    // Ensure these properties exist with defaults
    baseSalary: employeeData?.baseSalary || DEMO_EMPLOYEE.baseSalary,
    hourlyRate: employeeData?.hourlyRate || DEMO_EMPLOYEE.hourlyRate,
    currency: employeeData?.currency || DEMO_EMPLOYEE.currency,
    shift: employeeData?.shift || DEMO_EMPLOYEE.shift
  };
  
  const [activeTab, setActiveTab] = useState('clock-in');
  const [clockInHistory, setClockInHistory] = useState<Array<{
    employeeId: number;
    time: string;
    date: string;
    action: 'clock-in' | 'clock-out';
    verificationType: BiometricVerificationType;
    location?: { latitude: number; longitude: number; address?: string };
  }>>([]);
  const [shiftDifferentials, setShiftDifferentials] = useState<{
    [key in ShiftType]: { name: string; rate: number; description: string }
  }>({
    [ShiftType.REGULAR]: { 
      name: 'Standard Shift', 
      rate: 1.0, 
      description: 'Regular working hours' 
    },
    [ShiftType.NIGHT]: { 
      name: 'Night Hours', 
      rate: 1.15, 
      description: 'Overnight/late hours' 
    },
    [ShiftType.MORNING]: { 
      name: 'Early Hours', 
      rate: 1.0, 
      description: 'Early morning hours' 
    },
    [ShiftType.EVENING]: { 
      name: 'Extended Hours', 
      rate: 1.1, 
      description: 'Evening/extended hours' 
    },
    [ShiftType.WEEKEND]: { 
      name: 'Weekend Hours', 
      rate: 1.25, 
      description: 'Weekend work' 
    },
    [ShiftType.CUSTOM]: { 
      name: 'Custom Hours', 
      rate: 1.0, 
      description: 'Custom work schedule' 
    },
    [ShiftType.ROTATING]: { 
      name: 'Variable Hours', 
      rate: 1.1, 
      description: 'Rotating schedule' 
    }
  });
  const [overtimeHistory, setOvertimeHistory] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [notification, setNotification] = useState<{
    show: boolean;
    message: string;
    type: 'success' | 'error' | 'info';
  }>({ show: false, message: '', type: 'info' });
  
  // Load attendance data from local storage
  useEffect(() => {
    // Load clock history from local storage
    const loadAttendanceData = () => {
      const employeeId = employee.id;
      const today = new Date().toISOString().split('T')[0];
      const keys = [];
      
      // Find all attendance records in localStorage
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('attendance_')) {
          keys.push(key);
        }
      }
      
      // Process the records into clock history format
      const newClockHistory = [];
      for (const key of keys) {
        try {
          const record = JSON.parse(localStorage.getItem(key) || '{}');
          
          // Add clock-in record if it exists
          if (record.checkInTime) {
            newClockHistory.push({
              employeeId: record.employeeId,
              time: record.checkInTime,
              date: record.date,
              action: 'clock-in' as const,
              verificationType: record.verificationType || BiometricVerificationType.MANUAL,
              location: record.coordinates ? {
                latitude: record.coordinates.lat,
                longitude: record.coordinates.lng,
                address: record.location
              } : undefined
            });
          }
          
          // Add clock-out record if it exists
          if (record.checkOutTime) {
            newClockHistory.push({
              employeeId: record.employeeId,
              time: record.checkOutTime,
              date: record.date,
              action: 'clock-out' as const,
              verificationType: record.verificationType || BiometricVerificationType.MANUAL,
              location: record.coordinates ? {
                latitude: record.coordinates.lat,
                longitude: record.coordinates.lng,
                address: record.location
              } : undefined
            });
          }
        } catch (error) {
          console.error('Error parsing attendance record:', error);
        }
      }
      
      // Sort by date and time (newest first)
      newClockHistory.sort((a, b) => {
        const dateA = new Date(`${a.date}T${a.time}`);
        const dateB = new Date(`${b.date}T${b.time}`);
        return dateB.getTime() - dateA.getTime();
      });
      
      setClockInHistory(newClockHistory);
    };
    
    loadAttendanceData();
  }, [employee.id]);
  
  // Handle clock in/out success
  const handleClockInSuccess = (data: {
    employeeId: number;
    time: string;
    verificationType: BiometricVerificationType;
    location?: { latitude: number; longitude: number; address?: string };
  }) => {
    const newRecord = {
      ...data,
      date: new Date().toISOString().split('T')[0],
      action: activeTab === 'clock-in' ? 'clock-in' as const : 'clock-out' as const
    };
    
    setClockInHistory(prev => [newRecord, ...prev]);
    
    setNotification({
      show: true,
      message: `Successfully ${activeTab === 'clock-in' ? 'clocked in' : 'clocked out'} at ${data.time}`,
      type: 'success'
    });
    
    // Hide notification after 5 seconds
    setTimeout(() => {
      setNotification(prev => ({ ...prev, show: false }));
    }, 5000);
  };

  // Handle clock in/out failure
  const handleClockInFail = (error: string) => {
    setNotification({
      show: true,
      message: `Verification failed: ${error}`,
      type: 'error'
    });
    
    // Hide notification after 5 seconds
    setTimeout(() => {
      setNotification(prev => ({ ...prev, show: false }));
    }, 5000);
  };

  // Handle overtime calculation save
  const handleOvertimeSave = (overtimeData: any) => {
    setOvertimeHistory(prev => [overtimeData, ...prev]);
    
    setNotification({
      show: true,
      message: `Overtime calculation saved and ${overtimeData.status === 'approved' ? 'approved' : 'submitted for approval'}`,
      type: 'success'
    });
    
    // Hide notification after 5 seconds
    setTimeout(() => {
      setNotification(prev => ({ ...prev, show: false }));
    }, 5000);
  };

  // Handle shift differential change
  const handleShiftDifferentialChange = (shiftType: ShiftType, newRate: number) => {
    setShiftDifferentials(prev => ({
      ...prev,
      [shiftType]: {
        ...prev[shiftType],
        rate: newRate
      }
    }));
  };

  return (
    <div className="w-full p-6 pt-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Advanced Time & Attendance</h1>
        
        <div className="flex space-x-2">
          {onReturnToStandard && (
            <button
              onClick={onReturnToStandard}
              className="flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 border border-blue-300 rounded-md hover:bg-blue-50"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Return to Standard View
            </button>
          )}
          
          <a 
            href="/dashboard"
            className="flex items-center px-3 py-1.5 text-sm font-medium text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Return to Dashboard
          </a>
        </div>
      </div>
      
      {/* Notification */}
      {notification.show && (
        <div className={`mb-6 p-4 rounded-lg ${
          notification.type === 'success' ? 'bg-green-50 text-green-800 border border-green-200' :
          notification.type === 'error' ? 'bg-red-50 text-red-800 border border-red-200' :
          'bg-blue-50 text-blue-800 border border-blue-200'
        }`}>
          <p>{notification.message}</p>
        </div>
      )}
      
      <Tabs defaultValue="clock-in" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-6 w-full flex justify-center">
          <TabsTrigger value="clock-in" className="flex items-center">
            <Fingerprint className="h-4 w-4 mr-2" />
            Clock In
          </TabsTrigger>
          <TabsTrigger value="clock-out" className="flex items-center">
            <Clock className="h-4 w-4 mr-2" />
            Clock Out
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center">
            <ClipboardList className="h-4 w-4 mr-2" />
            Attendance History
          </TabsTrigger>
          <TabsTrigger value="overtime" className="flex items-center">
            <CircleDollarSign className="h-4 w-4 mr-2" />
            Overtime
          </TabsTrigger>
          <TabsTrigger value="shift-differentials" className="flex items-center">
            <Moon className="h-4 w-4 mr-2" />
            Shift Differentials
          </TabsTrigger>
        </TabsList>
        
        {/* Clock In Tab */}
        <TabsContent value="clock-in">
          <BiometricClockIn
            employeeId={employee.id}
            employeeName={employee.name}
            department={employee.department}
            position={employee.position}
            onClockInSuccess={handleClockInSuccess}
            onClockInFail={handleClockInFail}
            mode="clock-in"
            verificationTypes={[BiometricVerificationType.FINGERPRINT, BiometricVerificationType.FACE]}
            requireLocation={true}
            saveToAttendanceSystem={true}
          />
        </TabsContent>
        
        {/* Clock Out Tab */}
        <TabsContent value="clock-out">
          <BiometricClockIn
            employeeId={employee.id}
            employeeName={employee.name}
            department={employee.department}
            position={employee.position}
            onClockInSuccess={handleClockInSuccess}
            onClockInFail={handleClockInFail}
            mode="clock-out"
            verificationTypes={[BiometricVerificationType.FINGERPRINT, BiometricVerificationType.FACE]}
            requireLocation={true}
            saveToAttendanceSystem={true}
          />
        </TabsContent>
        
        {/* Attendance History Tab */}
        <TabsContent value="history">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
              <ClipboardList className="h-5 w-5 mr-2 text-blue-500" />
              Attendance Records
            </h2>
            
            {clockInHistory.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p>No attendance records yet</p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Time
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Action
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Verification
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Location
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {clockInHistory.map((record, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {record.date}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {record.time}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            record.action === 'clock-in' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                          }`}>
                            {record.action === 'clock-in' ? 'Clock In' : 'Clock Out'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {record.verificationType === BiometricVerificationType.FINGERPRINT ? 'Fingerprint' : 
                           record.verificationType === BiometricVerificationType.FACE ? 'Face Recognition' :
                           'Manual'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 max-w-xs truncate">
                          {record.location?.address || 'Not available'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </TabsContent>
        
        {/* Overtime Tab */}
        <TabsContent value="overtime">
          <OvertimeCalculator
            employeeId={employee.id}
            employeeName={employee.name}
            baseSalary={employee.baseSalary}
            hourlyRate={employee.hourlyRate}
            currency={employee.currency}
            onSave={handleOvertimeSave}
            departmentPolicies={{
              maximumOvertimeHours: 40,
              requiresApproval: true,
              overtimeRates: {
                [OvertimeType.REGULAR]: 1.5,
                [OvertimeType.DOUBLE]: 2.0,
                [OvertimeType.HOLIDAY]: 2.5,
                [OvertimeType.WEEKEND]: 1.75,
                [OvertimeType.NIGHT_SHIFT]: 1.7
              }
            }}
          />
          
          {overtimeHistory.length > 0 && (
            <div className="mt-8 bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Overtime History</h3>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Period
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total Hours
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {overtimeHistory.map((record, index) => (
                      <tr key={index}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {record.periodStart} to {record.periodEnd}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {typeof record.overtimeHours === 'object' && record.overtimeHours !== null
                            ? Object.values(record.overtimeHours)
                                .filter((hours): hours is number => typeof hours === 'number')
                                .reduce((sum, hours) => sum + hours, 0)
                            : 0}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Intl.NumberFormat('en-US', {
                            style: 'currency',
                            currency: record.calculations?.currency || 'USD',
                          }).format(record.calculations?.totalAmount || 0)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            record.status === 'approved' ? 'bg-green-100 text-green-800' :
                            record.status === 'pending_approval' ? 'bg-yellow-100 text-yellow-800' :
                            record.status === 'rejected' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {typeof record.status === 'string' 
                              ? record.status.replace('_', ' ').split(' ')
                                  .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
                                  .join(' ')
                              : 'Unknown'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </TabsContent>
        
        {/* Shift Differentials Tab */}
        <TabsContent value="shift-differentials">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
              <Moon className="h-5 w-5 mr-2 text-blue-500" />
              Shift Differential Management
            </h2>
            
            <div className="mb-6">
              <p className="text-sm text-gray-600 mb-4">
                Configure premium pay rates for different shift types. These rates multiply the base hourly rate when calculating pay for each shift type.
              </p>
              
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Shift Type
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Differential Rate
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {Object.entries(shiftDifferentials).map(([shiftType, data]) => (
                      <tr key={shiftType}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {data.name}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 max-w-xs truncate">
                          {data.description}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <input
                              type="number"
                              value={data.rate}
                              onChange={(e) => handleShiftDifferentialChange(
                                shiftType as ShiftType,
                                parseFloat(e.target.value) || 1.0
                              )}
                              min="1"
                              max="3"
                              step="0.05"
                              className="block w-20 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                            />
                            <span className="ml-2 text-gray-500">× base rate</span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <button
                            type="button"
                            className="text-blue-600 hover:text-blue-900"
                            onClick={() => {
                              // Reset to default (1.0) if not Regular shift
                              if (shiftType !== ShiftType.REGULAR) {
                                handleShiftDifferentialChange(shiftType as ShiftType, 1.0);
                              }
                            }}
                            disabled={shiftType === ShiftType.REGULAR}
                          >
                            Reset
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
            
            <div className="mt-8">
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                Sample Hourly Rate Calculation
              </h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Base Hourly Rate:</p>
                    <p className="font-medium">{new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: employee.currency,
                    }).format(employee.hourlyRate)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Current Shift:</p>
                    <p className="font-medium">{shiftDifferentials[employee.shift].name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Shift Differential:</p>
                    <p className="font-medium">{shiftDifferentials[employee.shift].rate}×</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Effective Hourly Rate:</p>
                    <p className="font-medium text-blue-600">{new Intl.NumberFormat('en-US', {
                      style: 'currency',
                      currency: employee.currency,
                    }).format(employee.hourlyRate * shiftDifferentials[employee.shift].rate)}</p>
                  </div>
                </div>
              </div>
              
              <div className="mt-4 text-sm text-gray-500">
                <p>This calculated rate is used as the base for regular time and overtime calculations.</p>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdvancedTimeAttendance; 