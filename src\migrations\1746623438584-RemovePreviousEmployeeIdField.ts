import { MigrationInterface, QueryRunner } from "typeorm";

export class RemovePreviousEmployeeIdField1746623438584 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Remove the previousEmployeeId column from employee_jobs table
        await queryRunner.query(`ALTER TABLE \`employee_jobs\` DROP COLUMN \`previousEmployeeId\``);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Add the column back if needed to rollback
        await queryRunner.query(`ALTER TABLE \`employee_jobs\` ADD \`previousEmployeeId\` varchar(255) NULL`);
    }

}
