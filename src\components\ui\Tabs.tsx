import React from "react";

interface TabsProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
  defaultValue?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  children: React.ReactNode;
}

interface TabsListProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
  children: React.ReactNode;
}

interface TabsTriggerProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  className?: string;
  value: string;
  children: React.ReactNode;
}

interface TabsContentProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
  value: string;
  children: React.ReactNode;
}

export const Tabs = ({
  className = "",
  defaultValue,
  value,
  onValueChange,
  children,
  ...props
}: TabsProps) => {
  const [activeTab, setActiveTab] = React.useState<string>(value || defaultValue || "");
  
  React.useEffect(() => {
    if (value !== undefined) {
      setActiveTab(value);
    }
  }, [value]);
  
  const handleValueChange = (newValue: string) => {
    if (onValueChange) {
      onValueChange(newValue);
    } else if (value === undefined) {
      setActiveTab(newValue);
    }
  };
  
  return (
    <div 
      className={`${className}`} 
      {...props}
      data-state={activeTab ? "active" : "inactive"}
      data-value={activeTab}
    >
      {React.Children.map(children, (child) => {
        if (!React.isValidElement(child)) return child;
        
        if (child.type === TabsList || child.type === TabsTrigger || child.type === TabsContent) {
          return React.cloneElement(child as React.ReactElement<any>, {
            activeTab, 
            onSelect: handleValueChange
          });
        }
        
        return child;
      })}
    </div>
  );
};

export const TabsList = ({
  className = "",
  children,
  activeTab,
  onSelect,
  ...props
}: TabsListProps & { activeTab?: string; onSelect?: (value: string) => void }) => {
  return (
    <div 
      className={`inline-flex items-center justify-center rounded-lg bg-gray-100 p-1 ${className}`} 
      role="tablist" 
      {...props}
    >
      {React.Children.map(children, (child) => {
        if (!React.isValidElement(child)) return child;
        
        if (child.type === TabsTrigger) {
          return React.cloneElement(child as React.ReactElement<any>, {
            activeTab, 
            onSelect
          });
        }
        
        return child;
      })}
    </div>
  );
};

export const TabsTrigger = ({
  className = "",
  value,
  children,
  activeTab,
  onSelect,
  ...props
}: TabsTriggerProps & { activeTab?: string; onSelect?: (value: string) => void }) => {
  const isActive = activeTab === value;
  
  return (
    <button 
      className={`inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 disabled:pointer-events-none disabled:opacity-50 ${
        isActive 
          ? "bg-white text-blue-700 shadow-sm" 
          : "text-gray-600 hover:text-blue-600"
      } ${className}`} 
      role="tab"
      type="button"
      aria-selected={isActive}
      data-state={isActive ? "active" : "inactive"}
      data-value={value}
      tabIndex={isActive ? 0 : -1}
      onClick={() => onSelect && onSelect(value)}
      {...props}
    >
      {children}
    </button>
  );
};

export const TabsContent = ({
  className = "",
  value,
  children,
  activeTab,
  ...props
}: TabsContentProps & { activeTab?: string }) => {
  const isActive = activeTab === value;
  
  if (!isActive) return null;
  
  return (
    <div 
      className={`mt-2 ${className}`} 
      role="tabpanel"
      data-state={isActive ? "active" : "inactive"}
      data-value={value}
      tabIndex={0}
      {...props}
    >
      {children}
    </div>
  );
}; 