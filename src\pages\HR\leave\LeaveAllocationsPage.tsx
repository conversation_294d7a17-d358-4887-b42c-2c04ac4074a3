import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import LeaveAllocationsTab from '../../../components/HR/leave/LeaveAllocationsTab';
import { employeeApi } from '../../../services/employeeApi';

const LeaveAllocationsPage: React.FC = () => {
  const { user } = useAuth();
  const [departments, setDepartments] = useState<string[]>([]);
  const [notification, setNotification] = useState<{type: 'success' | 'error' | 'warning', message: string} | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const employeesResponse = await employeeApi.getAll();
      if (employeesResponse.success) {
        const employees = employeesResponse.data || [];
        const depts = Array.from(new Set(employees.map((emp: any) => emp.department).filter(Boolean)));
        setDepartments(depts);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Notification */}
      {notification && (
        <div className={`p-4 border-l-4 ${
          notification.type === 'success' ? 'bg-green-50 border-green-400 text-green-700' :
          notification.type === 'error' ? 'bg-red-50 border-red-400 text-red-700' :
          'bg-yellow-50 border-yellow-400 text-yellow-700'
        }`}>
          <div className="flex items-center">
            <span>{notification.message}</span>
            <button
              onClick={() => setNotification(null)}
              className="ml-auto text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>
        </div>
      )}

      <LeaveAllocationsTab
        departments={departments}
        onNotification={setNotification}
      />
    </div>
  );
};

export default LeaveAllocationsPage; 