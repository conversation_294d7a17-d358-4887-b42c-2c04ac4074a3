import { AppDataSource } from '../config/database';
import { ITOperationLog } from '../entities/ITOperationLog';

export const ITOperationLogRepository = {
  // Get all operation logs
  getAllLogs: async (options?: {
    skip?: number;
    take?: number;
    order?: { [key: string]: 'ASC' | 'DESC' };
    filters?: { [key: string]: any };
  }) => {
    const repository = AppDataSource.getRepository(ITOperationLog);
    
    const query = repository.createQueryBuilder('log')
      .leftJoinAndSelect('log.createdBy', 'createdBy')
      .leftJoinAndSelect('log.lastModifiedBy', 'lastModifiedBy');
    
    // Apply filters if provided
    if (options?.filters) {
      Object.entries(options.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (key === 'search') {
            query.andWhere(
              '(log.title LIKE :search OR log.description LIKE :search OR log.loggedBy LIKE :search OR log.assignedTo LIKE :search)',
              { search: `%${value}%` }
            );
          } else if (key === 'dateFrom') {
            query.andWhere('log.dateOccurred >= :dateFrom', { dateFrom: value });
          } else if (key === 'dateTo') {
            query.andWhere('log.dateOccurred <= :dateTo', { dateTo: value });
          } else if (key === 'tags') {
            // Search for any of the specified tags
            const tags = Array.isArray(value) ? value : [value];
            const conditions = tags.map((_, index) => `log.tags LIKE :tag${index}`);
            const params = tags.reduce((acc, tag, index) => {
              acc[`tag${index}`] = `%${tag}%`;
              return acc;
            }, {});
            
            query.andWhere(`(${conditions.join(' OR ')})`, params);
          } else {
            query.andWhere(`log.${key} = :${key}`, { [key]: value });
          }
        }
      });
    }
    
    // Apply pagination and ordering
    if (options?.order) {
      Object.entries(options.order).forEach(([key, direction]) => {
        query.addOrderBy(`log.${key}`, direction);
      });
    } else {
      // Default order: most recent first
      query.orderBy('log.createdAt', 'DESC');
    }
    
    if (options?.skip !== undefined) {
      query.skip(options.skip);
    }
    
    if (options?.take !== undefined) {
      query.take(options.take);
    }
    
    // Execute the query
    const [logs, count] = await query.getManyAndCount();
    
    return { logs, count };
  },
  
  // Get a single operation log by ID
  getLogById: async (id: string) => {
    const repository = AppDataSource.getRepository(ITOperationLog);
    
    const log = await repository.findOne({
      where: { id },
      relations: ['createdBy', 'lastModifiedBy'],
    });
    
    if (!log) {
      throw new Error(`Operation log with ID ${id} not found`);
    }
    
    return log;
  },
  
  // Create a new operation log
  createLog: async (logData: Partial<ITOperationLog>) => {
    const repository = AppDataSource.getRepository(ITOperationLog);
    
    const newLog = repository.create(logData);
    
    return await repository.save(newLog);
  },
  
  // Update an existing operation log
  updateLog: async (id: string, logData: Partial<ITOperationLog>) => {
    const repository = AppDataSource.getRepository(ITOperationLog);
    
    const existingLog = await repository.findOne({
      where: { id }
    });
    
    if (!existingLog) {
      throw new Error(`Operation log with ID ${id} not found`);
    }
    
    // Update the log fields
    repository.merge(existingLog, logData);
    
    return await repository.save(existingLog);
  },
  
  // Delete an operation log
  deleteLog: async (id: string) => {
    const repository = AppDataSource.getRepository(ITOperationLog);
    
    const existingLog = await repository.findOne({
      where: { id }
    });
    
    if (!existingLog) {
      throw new Error(`Operation log with ID ${id} not found`);
    }
    
    await repository.remove(existingLog);
    
    return true;
  },
  
  // Get statistics for operation logs
  getStatistics: async () => {
    const repository = AppDataSource.getRepository(ITOperationLog);
    
    // Get counts by category
    const categoryCounts = await repository
      .createQueryBuilder('log')
      .select('log.issueCategory, COUNT(log.id) as count')
      .groupBy('log.issueCategory')
      .getRawMany();
    
    // Get counts by impact level
    const impactCounts = await repository
      .createQueryBuilder('log')
      .select('log.impactLevel, COUNT(log.id) as count')
      .groupBy('log.impactLevel')
      .getRawMany();
    
    // Get counts by status
    const statusCounts = await repository
      .createQueryBuilder('log')
      .select('log.status, COUNT(log.id) as count')
      .groupBy('log.status')
      .getRawMany();
    
    // Get recent activity
    const recentActivity = await repository
      .createQueryBuilder('log')
      .leftJoinAndSelect('log.createdBy', 'createdBy')
      .orderBy('log.createdAt', 'DESC')
      .take(5)
      .getMany();
    
    return {
      categoryCounts,
      impactCounts,
      statusCounts,
      recentActivity
    };
  }
};

export default ITOperationLogRepository; 