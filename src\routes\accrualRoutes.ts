import { Router } from 'express';
import { LeaveAccrualJobService } from '../services/LeaveAccrualJobService';
import { leaveAccrualService } from '../services/LeaveAccrualService';
import { requireAuth, requireAdmin } from '../middleware/auth';

const router = Router();

/**
 * @route GET /api/accruals/status
 * @desc Get accrual processing status and schedule information
 * @access HR Admin only
 */
router.get('/status', 
  requireAdmin, 
  async (req, res) => {
    try {
      const status = await LeaveAccrualJobService.getAccrualStatus();
      
      res.json({
        success: true,
        data: status
      });
    } catch (error: any) {
      console.error('Error getting accrual status:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get accrual status',
        error: error.message
      });
    }
  }
);

/**
 * @route POST /api/accruals/process
 * @desc Manually trigger accrual processing
 * @access HR Admin only
 * @body { frequency?: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually' }
 */
router.post('/process', 
  requireAdmin, 
  async (req, res) => {
    try {
      const { frequency } = req.body;
      
      const result = await LeaveAccrualJobService.triggerManualAccrualProcessing(frequency);
      
      if (result.success) {
        res.json({
          success: true,
          message: result.message,
          data: result.result
        });
      } else {
        res.status(400).json({
          success: false,
          message: result.message,
          error: result.error
        });
      }
    } catch (error: any) {
      console.error('Error processing manual accrual:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to process manual accrual',
        error: error.message
      });
    }
  }
);

/**
 * @route POST /api/accruals/manual
 * @desc Process manual accrual for a specific employee
 * @access HR Admin only
 * @body { employeeId: string, leaveType: string, amount: number, reason: string }
 */
router.post('/manual', 
  authenticateToken, 
  authorizeRoles(['HR_ADMIN', 'IT_ADMIN']), 
  async (req, res) => {
    try {
      const { employeeId, leaveType, amount, reason } = req.body;
      
      // Validate required fields
      if (!employeeId || !leaveType || !amount || !reason) {
        return res.status(400).json({
          success: false,
          message: 'Missing required fields: employeeId, leaveType, amount, reason'
        });
      }

      // Validate leave type (dynamic validation - could be enhanced with DB check)
      if (!leaveType || typeof leaveType !== 'string') {
        return res.status(400).json({
          success: false,
          message: 'Invalid leave type'
        });
      }

      // Validate amount
      if (isNaN(amount) || amount <= 0) {
        return res.status(400).json({
          success: false,
          message: 'Amount must be a positive number'
        });
      }
      
      await leaveAccrualService.processManualAccrual(
        employeeId,
        leaveType,
        parseFloat(amount),
        reason
      );
      
      res.json({
        success: true,
        message: `Manual accrual processed: ${amount} days of ${leaveType} leave added for employee ${employeeId}`
      });
    } catch (error: any) {
      console.error('Error processing manual accrual:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to process manual accrual',
        error: error.message
      });
    }
  }
);

/**
 * @route POST /api/accruals/restart
 * @desc Restart all accrual jobs
 * @access IT Admin only
 */
router.post('/restart', 
  authenticateToken, 
  authorizeRoles(['IT_ADMIN']), 
  async (req, res) => {
    try {
      await LeaveAccrualJobService.restartAllJobs();
      
      res.json({
        success: true,
        message: 'All accrual jobs restarted successfully'
      });
    } catch (error: any) {
      console.error('Error restarting accrual jobs:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to restart accrual jobs',
        error: error.message
      });
    }
  }
);

/**
 * @route POST /api/accruals/stop
 * @desc Stop all accrual jobs
 * @access IT Admin only
 */
router.post('/stop', 
  authenticateToken, 
  authorizeRoles(['IT_ADMIN']), 
  async (req, res) => {
    try {
      LeaveAccrualJobService.stopAllJobs();
      
      res.json({
        success: true,
        message: 'All accrual jobs stopped successfully'
      });
    } catch (error: any) {
      console.error('Error stopping accrual jobs:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to stop accrual jobs',
        error: error.message
      });
    }
  }
);

/**
 * @route GET /api/accruals/preview
 * @desc Preview accrual processing results without actually processing
 * @access HR Admin only
 * @query frequency?: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annually'
 */
router.get('/preview', 
  authenticateToken, 
  authorizeRoles(['HR_ADMIN', 'IT_ADMIN']), 
  async (req, res) => {
    try {
      const { frequency } = req.query;
      
      // This would be a preview-only version of the accrual processing
      // For now, just return information about what would be processed
      
      res.json({
        success: true,
        message: 'Accrual preview (not yet implemented)',
        data: {
          note: 'This endpoint will show preview of accrual processing without actually updating balances',
          frequency: frequency || 'all',
          timestamp: new Date().toISOString()
        }
      });
    } catch (error: any) {
      console.error('Error generating accrual preview:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to generate accrual preview',
        error: error.message
      });
    }
  }
);

export default router; 