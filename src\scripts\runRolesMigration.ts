import { DataSource } from 'typeorm';
import { CreateRolesTables1747700000000 } from '../migrations/1747700000000-CreateRolesTables';

// Database configuration
const AppDataSource = new DataSource({
  type: "mysql",
  host: process.env.DB_HOST || "localhost",
  port: parseInt(process.env.DB_PORT || "3306"),
  username: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "root",
  database: process.env.DB_NAME || "ims_db",
  synchronize: false,
  logging: true,
  entities: [],
  subscribers: [],
  migrations: []
});

const runMigration = async () => {
  console.log("Starting migration to create roles and permissions tables...");

  try {
    // Initialize the data source
    await AppDataSource.initialize();
    console.log("Database connection established");

    // Create migration instance
    const migration = new CreateRolesTables1747700000000();
    
    // Run the migration
    await migration.up(AppDataSource.createQueryRunner());
    console.log("Migration completed successfully!");
    
    // Close the connection
    await AppDataSource.destroy();
    console.log("Database connection closed");
    
    process.exit(0);
  } catch (error) {
    console.error("Error running migration:", error);
    process.exit(1);
  }
};

runMigration(); 