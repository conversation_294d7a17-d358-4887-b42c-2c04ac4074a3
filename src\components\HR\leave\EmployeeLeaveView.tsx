import React, { useState, useEffect } from 'react';
import { 
  Calendar, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  FileText, 
  PlusCircle, 
  Edit, 
  Check, 
  Search, 
  ArrowLeft,
  ArrowRight,
  Filter,
  TrendingUp,
  Award,
  Info,
  Download,
  ChevronRight,
  History,
  X,
  XCircle,
  Plus,
  Upload,
  File,
  Trash2,
  Phone
} from 'lucide-react';
import { 
  hrPrimaryButtonStyle, 
  hrSecondaryButtonStyle, 
  hrCardStyle,
  hrInputStyle,
  hrSectionTitleStyle,
  hrSubsectionTitleStyle,
  hrInfoAlertStyle,
  hrSuccessAlertStyle
} from '../../../styles/hrWorkflow';
import { useAuth } from '../../../contexts/AuthContext';
import { EmployeeLeaveData, employeePortalService } from '../../../services/EmployeePortalService';

// Add approval interfaces
interface ApprovalRecord {
  id: number;
  level: 'MANAGER' | 'HR' | 'DIRECTOR';
  approverId?: string;
  approverName?: string;
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  comments?: string;
  decisionAt?: string;
  sequence: number;
}

// Interface for leave request data
interface LeaveRequest {
  id: number;
  type: string;
  startDate: string;
  endDate: string;
  days: number;
  status: string;
  reason: string;
  approvedBy: string | null;
  approvedById?: number;
  approvedByName?: string;
  approvedOn?: string;
  appliedOn: string;
  rejectionReason?: string;
  rejectedBy?: string | null;
  rejectedById?: number;
  rejectedByName?: string;
  rejectedOn?: string;
  emergencyContact?: string; // Emergency contact information
  attachments?: string | any[]; // Attachments (JSON string or array)
  // Workflow fields
  createdBy?: string;
  source?: 'EMPLOYEE' | 'HR_ADMIN' | 'MANAGER';
  originalStartDate?: string;
  originalEndDate?: string;
  originalReason?: string;
  modificationHistory?: string;
  currentStage?: string;
  isUrgent?: boolean;
  createdById?: number;
  createdByName?: string;
  employeeName?: string;
  // Add approvals array to LeaveRequest interface
  approvals?: ApprovalRecord[];
}

// Interface for leave balance data
interface LeaveBalance {
  leaveType: string;
  total: number;
  used: number;
  pending: number;
  remaining: number;
  carryForward?: number;
  expiryDate?: string;
}

interface EmployeeLeaveViewProps {
  leaveData?: EmployeeLeaveData | null;
  employeeId?: number;
  employeeName?: string;
}

// Add this type above the component
type ModificationHistoryEntry = {
  action: string;
  actor: string;
  timestamp: string;
  reason?: string;
};

const EmployeeLeaveView: React.FC<EmployeeLeaveViewProps> = ({ 
  leaveData: propLeaveData, 
  employeeId: propEmployeeId,
  employeeName: propEmployeeName 
}) => {
  const { user } = useAuth();
  const [currentView, setCurrentView] = useState<'summary' | 'details' | 'apply'>('summary');
  const [selectedLeave, setSelectedLeave] = useState<LeaveRequest | null>(null);
  const [leaveBalances, setLeaveBalances] = useState<LeaveBalance[]>([]);
  const [leaveRecords, setLeaveRecords] = useState<LeaveRequest[]>([]);
  const [filterYear, setFilterYear] = useState<string>(new Date().getFullYear().toString());
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Updated leave form to match HR admin structure
  const [leaveForm, setLeaveForm] = useState({
    leaveType: '',
    startDate: '',
    endDate: '',
    reason: '',
    emergencyContact: '',
    attachments: [] as File[]
  });

  const [showAuditTrail, setShowAuditTrail] = useState(false);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);
  const [editingLeaveId, setEditingLeaveId] = useState<number | null>(null);
  const [selectedLeaveApprovals, setSelectedLeaveApprovals] = useState<ApprovalRecord[]>([]);
  const [isCancelling, setIsCancelling] = useState(false);

  // Use props if provided, otherwise fall back to user data
  const employeeId = propEmployeeId || (user?.id && !isNaN(Number(user.id)) ? Number(user.id) : undefined);
  const employeeName = propEmployeeName || user?.name || 'Employee';

  // Load leave data on component mount
  useEffect(() => {
    loadLeaveData();
  }, [propLeaveData, employeeId]);

  // Add a function to force refresh with cache busting
  const forceRefreshLeaveData = async () => {
    console.log('🔄 Force refreshing leave data with cache busting...');
    setIsRefreshing(true);
    
    try {
      // Add timestamp to bust cache
      const timestamp = Date.now();
      
      if (!employeeId) {
        console.warn('No employee ID available for leave data');
        setIsRefreshing(false);
        return;
      }
      
      // Always fetch fresh data from server, regardless of propLeaveData
      console.log('📡 Fetching fresh leave data from server...');
      const leaveData = await employeePortalService.getEmployeeLeaveData(employeeId, timestamp);
      
      if (leaveData) {
        setLeaveBalances(leaveData.balances);
        
        // Transform recent requests to match the expected format
        const transformedRecords: LeaveRequest[] = leaveData.recentRequests.map(req => ({
          id: req.id,
          type: req.type,
          startDate: req.from,
          endDate: req.to,
          days: req.days,
          status: req.status,
          reason: req.reason || '',
          approvedBy: (req as any).approvedBy || null,
          approvedById: (req as any).approvedById || null,
          approvedByName: (req as any).approvedByName || null,
          approvedOn: (req as any).approvedOn || null,
          appliedOn: (req as any).appliedOn || new Date().toISOString(),
          rejectionReason: (req as any).rejectionReason || null,
          rejectedBy: (req as any).rejectedBy || null,
          rejectedById: (req as any).rejectedById || null,
          rejectedByName: (req as any).rejectedByName || null,
          rejectedOn: (req as any).rejectedOn || null,
          emergencyContact: (req as any).emergencyContact || null,
          attachments: (req as any).attachments || null,
          // Include workflow fields if available
          createdBy: (req as any).createdBy,
          source: (req as any).source,
          originalStartDate: (req as any).originalStartDate,
          originalEndDate: (req as any).originalEndDate,
          originalReason: (req as any).originalReason,
          modificationHistory: (req as any).modificationHistory,
          currentStage: (req as any).currentStage,
          isUrgent: (req as any).isUrgent,
          createdById: (req as any).createdById,
          createdByName: (req as any).createdByName,
          employeeName: (req as any).employeeName
        }));
        
        setLeaveRecords(transformedRecords);
        console.log('✅ Force refresh completed successfully with records:', 
          transformedRecords.map(r => ({ id: r.id, status: r.status, currentStage: r.currentStage }))
        );
        // Show success message for manual refresh
        if (timestamp) {
          setSuccessMessage('Leave data refreshed successfully!');
        }
      }
    } catch (error) {
      console.error('Error force refreshing leave data:', error);
      setErrorMessage('Failed to refresh leave data. Please try again.');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Add immediate refresh after successful cancellation
  const refreshAfterCancellation = async () => {
    console.log('🔄 Refreshing data after cancellation...');
    await forceRefreshLeaveData();
    // Also refresh the current view
    setCurrentView('summary');
  };

  // Fetch approval timeline when selectedLeave changes
  useEffect(() => {
    if (selectedLeave) {
      fetchApprovalTimeline(selectedLeave.id)
        .then(setSelectedLeaveApprovals)
        .catch(error => {
          console.error('Failed to fetch approval timeline:', error);
          setSelectedLeaveApprovals([]);
        });
    } else {
      setSelectedLeaveApprovals([]);
    }
  }, [selectedLeave]);

  // Function to fetch approval timeline for a specific request
  const fetchApprovalTimeline = async (requestId: number): Promise<ApprovalRecord[]> => {
    try {
      const token = localStorage.getItem('authToken');
      if (!token) {
        console.warn('No authentication token found');
        return [];
      }

      // Validate token format (basic check)
      if (!token.includes('.') || token.split('.').length !== 3) {
        console.warn('Invalid JWT token format, clearing token');
        localStorage.removeItem('authToken');
        return [];
      }

      const response = await fetch(`/api/leave-approvals/${requestId}/timeline`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('Approval timeline response:', data);
        return data.data?.approvals || [];
      } else if (response.status === 401) {
        console.warn('Authentication failed, clearing token');
        localStorage.removeItem('authToken');
        return [];
      } else {
        console.error('Failed to fetch approval timeline:', response.status, response.statusText);
        return [];
      }
    } catch (error) {
      console.error('Error fetching approval timeline:', error);
      return [];
    }
  };

  const loadLeaveData = async (forceRefresh: boolean = false) => {
    // If leave data is provided as props and we're not forcing refresh, use it
    if (propLeaveData && !forceRefresh) {
      setLeaveBalances(propLeaveData.balances);
      
      // Transform recent requests to match the expected format
      const transformedRecords: LeaveRequest[] = propLeaveData.recentRequests.map(req => ({
        id: req.id,
        type: req.type,
        startDate: req.from,
        endDate: req.to,
        days: req.days,
        status: req.status,
        reason: req.reason || '',
        approvedBy: (req as any).approvedBy || null,
        approvedById: (req as any).approvedById || null,
        approvedByName: (req as any).approvedByName || null,
        approvedOn: (req as any).approvedOn || null,
        appliedOn: (req as any).appliedOn || new Date().toISOString(),
        rejectionReason: (req as any).rejectionReason || null,
        rejectedBy: (req as any).rejectedBy || null,
        rejectedById: (req as any).rejectedById || null,
        rejectedByName: (req as any).rejectedByName || null,
        rejectedOn: (req as any).rejectedOn || null,
        emergencyContact: (req as any).emergencyContact || null,
        attachments: (req as any).attachments || null,
        // Include workflow fields if available
        createdBy: (req as any).createdBy,
        source: (req as any).source,
        originalStartDate: (req as any).originalStartDate,
        originalEndDate: (req as any).originalEndDate,
        originalReason: (req as any).originalReason,
        modificationHistory: (req as any).modificationHistory,
        currentStage: (req as any).currentStage,
        isUrgent: (req as any).isUrgent,
        createdById: (req as any).createdById,
        createdByName: (req as any).createdByName,
        employeeName: (req as any).employeeName
      }));
      
      setLeaveRecords(transformedRecords);
      setLoading(false);
      return;
    }
    
    // Fall back to fetching data if no props provided or force refresh requested
    if (!employeeId) {
      console.warn('No employee ID available for leave data');
      setLoading(false);
      return;
    }
    
    try {
      setLoading(true);
      const leaveData = await employeePortalService.getEmployeeLeaveData(employeeId);
      
      if (leaveData) {
        setLeaveBalances(leaveData.balances);
        
        // Transform recent requests to match the expected format
        const transformedRecords: LeaveRequest[] = leaveData.recentRequests.map(req => ({
          id: req.id,
          type: req.type,
          startDate: req.from,
          endDate: req.to,
          days: req.days,
          status: req.status,
          reason: req.reason || '',
          approvedBy: (req as any).approvedBy || null,
          approvedById: (req as any).approvedById || null,
          approvedByName: (req as any).approvedByName || null,
          approvedOn: (req as any).approvedOn || null,
          appliedOn: (req as any).appliedOn || new Date().toISOString(),
          rejectionReason: (req as any).rejectionReason || null,
          rejectedBy: (req as any).rejectedBy || null,
          rejectedById: (req as any).rejectedById || null,
          rejectedByName: (req as any).rejectedByName || null,
          rejectedOn: (req as any).rejectedOn || null,
          emergencyContact: (req as any).emergencyContact || null,
          attachments: (req as any).attachments || null,
          // Include workflow fields if available
          createdBy: (req as any).createdBy,
          source: (req as any).source,
          originalStartDate: (req as any).originalStartDate,
          originalEndDate: (req as any).originalEndDate,
          originalReason: (req as any).originalReason,
          modificationHistory: (req as any).modificationHistory,
          currentStage: (req as any).currentStage,
          isUrgent: (req as any).isUrgent,
          createdById: (req as any).createdById,
          createdByName: (req as any).createdByName,
          employeeName: (req as any).employeeName
        }));
        
        setLeaveRecords(transformedRecords);
      }
    } catch (error) {
      console.error('Error loading leave data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to get leave balance by type
  const getLeaveBalance = (leaveType: string) => {
    // Map common leave type names to actual database values
    const typeMapping: { [key: string]: string } = {
      'annual': 'ANNUAL_LEAVE',
      'sick': 'SICK_LEAVE', 
      'casual': 'CASUAL_LEAVE'
    };
    
    const actualType = typeMapping[leaveType.toLowerCase()] || leaveType;
    const balance = leaveBalances.find(b => 
      b.leaveType === actualType || b.leaveType.toLowerCase().includes(leaveType.toLowerCase())
    );
    
    return balance || { 
      leaveType, 
      total: 0, 
      used: 0, 
      pending: 0, 
      remaining: 0 
    };
  };

  // File upload handlers
  const handleFileUpload = (files: FileList) => {
    const newFiles = Array.from(files).filter(file => {
      // Check file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        setErrorMessage(`File "${file.name}" is too large. Maximum size is 10MB.`);
        return false;
      }
      
      // Check file type
      const allowedTypes = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png', '.txt'];
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      if (!allowedTypes.includes(fileExtension)) {
        setErrorMessage(`File "${file.name}" has unsupported format.`);
        return false;
      }
      
      // Check if file already exists
      if (leaveForm.attachments.some(existing => existing.name === file.name && existing.size === file.size)) {
        setErrorMessage(`File "${file.name}" is already selected.`);
        return false;
      }
      
      return true;
    });
    
    setLeaveForm(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...newFiles]
    }));
  };

  // Handle drag and drop
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.dataTransfer.files) {
      handleFileUpload(e.dataTransfer.files);
    }
  };

  // Remove file
  const removeFile = (index: number) => {
    setLeaveForm(prev => ({
      ...prev,
      attachments: prev.attachments.filter((_, i) => i !== index)
    }));
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Calculate days between dates
  const calculateDays = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = end.getTime() - start.getTime();
    return Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1;
  };

  // Reset form
  const resetForm = () => {
    setLeaveForm({
      leaveType: '',
      startDate: '',
      endDate: '',
      reason: '',
      emergencyContact: '',
      attachments: []
    });
    setErrorMessage(null);
  };

  // Filter leave records by year
  const filteredRecords = leaveRecords.filter((record: LeaveRequest) => 
    record.startDate.startsWith(filterYear)
  );

  // Format date for display
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      weekday: 'short'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Helper function to get source badge
  const getSourceBadge = (request: LeaveRequest) => {
    if (request.source === 'HR_ADMIN') {
      return (
        <div className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full border border-blue-200">
          <span className="mr-1">🏢</span>
          Created by: {request.createdBy || 'HR/Admin'}
        </div>
      );
    }

    // Default: self-applied
    return (
      <div className="inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full border border-gray-200">
        <span className="mr-1">🧑‍💼</span>
        Self-Applied
      </div>
    );
  };

  // Helper function to check if request has modifications
  const hasModifications = (request: LeaveRequest) => {
    return request.modificationHistory && JSON.parse(request.modificationHistory).length > 0;
  };

  // Helper function to get modification count
  const getModificationCount = (request: LeaveRequest) => {
    if (!request.modificationHistory) return 0;
    return JSON.parse(request.modificationHistory).length;
  };

  // Get status badge with enhanced styling
  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold tracking-wide uppercase transition-all duration-200";
    
    switch (status.toLowerCase()) {
      case 'approved':
        return (
          <span className={`${baseClasses} bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-200 border border-emerald-200 dark:border-emerald-700`}>
            <CheckCircle className="h-3 w-3 mr-1.5" />
            Approved
          </span>
        );
      case 'pending':
        return (
          <span className={`${baseClasses} bg-blue-100 dark:bg-blue-600 text-blue-800 dark:text-white border border-blue-200 dark:border-blue-500`}>
            <Clock className="h-3 w-3 mr-1.5" />
            Pending
          </span>
        );
      case 'rejected':
        return (
          <span className={`${baseClasses} bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 border border-red-200 dark:border-red-700`}>
            <AlertCircle className="h-3 w-3 mr-1.5" />
            Rejected
          </span>
        );
      default:
        return (
          <span className={`${baseClasses} bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 border border-gray-200 dark:border-gray-600`}>
            <Info className="h-3 w-3 mr-1.5" />
            {status}
          </span>
        );
    }
  };

  // Calculate usage percentage for progress bars
  const getUsagePercentage = (used: number, total: number) => {
    return total > 0 ? Math.round((used / total) * 100) : 0;
  };

  // Professional loading component
  const LoadingSpinner = () => (
    <div className="min-h-[400px] flex items-center justify-center">
      <div className="text-center">
        <div className="relative">
          <div className="w-16 h-16 border-4 border-blue-100 border-t-blue-600 rounded-full animate-spin mx-auto"></div>
          <div className="absolute inset-0 w-16 h-16 border-2 border-blue-200 rounded-full mx-auto opacity-30"></div>
        </div>
                 <p className="mt-4 text-sm text-gray-600 font-medium">Loading your leave information...</p>
         <p className="text-xs text-gray-500 mt-1">Please wait while we fetch your data</p>
      </div>
    </div>
  );

  // Approval Timeline Component
  const ApprovalTimeline = ({ approvals }: { approvals: ApprovalRecord[] }) => {
    // If no approvals from new system, show fallback with basic status
    if (!approvals || approvals.length === 0) {
      // Show basic approval status based on leave request status
      const status = selectedLeave?.status || 'PENDING';
      const statusColor = status === 'APPROVED' ? 'green' : status === 'REJECTED' ? 'red' : 'blue';
      
      return (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden mb-4">
          <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
            <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100">Approval Timeline</h3>
            <p className="text-blue-700 dark:text-blue-300 text-sm">Track the approval process</p>
          </div>
          
          <div className="p-4">
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-full border-2 ${
                  status === 'APPROVED' ? 'bg-green-100 text-green-600 border-green-200' :
                  status === 'REJECTED' ? 'bg-red-100 text-red-600 border-red-200' :
                  'bg-blue-100 text-blue-600 border-blue-200'
                }`}>
                  {status === 'APPROVED' ? <CheckCircle className="h-4 w-4" /> :
                   status === 'REJECTED' ? <AlertCircle className="h-4 w-4" /> :
                   <Clock className="h-4 w-4" />}
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    Request Status: {status}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {status === 'PENDING' ? 'Awaiting approval' : 
                     status === 'APPROVED' ? 'Request has been approved' :
                     status === 'REJECTED' ? 'Request has been rejected' : 'Status unknown'}
                  </p>
                </div>
              </div>
              
              <div className="text-center text-gray-500 dark:text-gray-400 pt-2 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-center space-x-2">
                  <Info className="h-4 w-4" />
                  <span className="text-sm">Detailed approval timeline will be available once the multi-level approval system is active</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    const getStatusColor = (status: string) => {
      switch (status) {
        case 'approved': return 'text-green-600 bg-green-100 border-green-200';
        case 'rejected': return 'text-red-600 bg-red-100 border-red-200';
        case 'cancelled': return 'text-gray-600 bg-gray-100 border-gray-200';
        case 'pending': return 'text-blue-600 bg-blue-100 border-blue-200';
        default: return 'text-gray-600 bg-gray-100 border-gray-200';
      }
    };

    const getStatusIcon = (status: string) => {
      switch (status) {
        case 'approved': return <CheckCircle className="h-4 w-4" />;
        case 'rejected': return <AlertCircle className="h-4 w-4" />;
        case 'cancelled': return <XCircle className="h-4 w-4" />;
        case 'pending': return <Clock className="h-4 w-4" />;
        default: return <Info className="h-4 w-4" />;
      }
    };

    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden mb-4">
        <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
          <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100">Approval Timeline</h3>
          <p className="text-blue-700 dark:text-blue-300 text-sm">Track the approval process</p>
        </div>
        
        <div className="p-4">
          <div className="space-y-4">
            {approvals.sort((a, b) => a.sequence - b.sequence).map((approval, index) => (
              <div key={approval.id} className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className={`p-2 rounded-full border-2 ${getStatusColor(approval.status)}`}>
                    {getStatusIcon(approval.status)}
                  </div>
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {approval.level.replace('_', ' ')} Approval
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {approval.approverName || 'Pending assignment'}
                      </p>
                    </div>
                    <div className="text-right">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(approval.status)}`}>
                        {approval.status.charAt(0).toUpperCase() + approval.status.slice(1)}
                      </span>
                      {approval.decisionAt && (
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {new Date(approval.decisionAt as string).toLocaleString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  {approval.comments && (
                    <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-700 rounded-md">
                      <p className="text-sm text-gray-700 dark:text-gray-300">{approval.comments}</p>
                    </div>
                  )}
                </div>
                
                {index < approvals.length - 1 && (
                  <div className="absolute left-6 mt-8 h-6 w-0.5 bg-gray-200 dark:bg-gray-600"></div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // Enhanced summary stats component
  const SummaryStats = () => {
    const totalAllocated = leaveBalances.reduce((sum, b) => sum + b.total, 0);
    const totalUsed = leaveBalances.reduce((sum, b) => sum + b.used, 0);
    const totalRemaining = leaveBalances.reduce((sum, b) => sum + b.remaining, 0);
    const usagePercentage = totalAllocated > 0 ? Math.round((totalUsed / totalAllocated) * 100) : 0;

         return (
       <div className="grid grid-cols-1 md:grid-cols-4 gap-3 mb-4">
         <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border border-blue-200 dark:border-blue-700 rounded-lg p-3 hover:shadow-lg transition-all duration-300">
           <div className="flex items-center justify-between">
             <div>
               <p className="text-xs font-medium text-blue-600 dark:text-blue-400 uppercase tracking-wide">Total Allocated</p>
               <p className="text-lg font-bold text-blue-900 dark:text-blue-100 mt-1">{totalAllocated}</p>
               <p className="text-xs text-blue-700 dark:text-blue-300">days this year</p>
             </div>
             <div className="p-2 bg-blue-600 dark:bg-blue-700 rounded-lg">
               <Calendar className="h-4 w-4 text-white" />
             </div>
           </div>
         </div>

                 <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 border border-emerald-200 dark:border-emerald-700 rounded-lg p-3 hover:shadow-lg transition-all duration-300">
           <div className="flex items-center justify-between">
             <div>
               <p className="text-xs font-medium text-emerald-600 dark:text-emerald-400 uppercase tracking-wide">Remaining</p>
               <p className="text-lg font-bold text-emerald-900 dark:text-emerald-100 mt-1">{totalRemaining}</p>
               <p className="text-xs text-emerald-700 dark:text-emerald-300">days available</p>
             </div>
             <div className="p-2 bg-emerald-600 dark:bg-emerald-700 rounded-lg">
               <TrendingUp className="h-4 w-4 text-white" />
             </div>
           </div>
         </div>

         <div className="bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20 border border-amber-200 dark:border-amber-700 rounded-lg p-3 hover:shadow-lg transition-all duration-300">
           <div className="flex items-center justify-between">
             <div>
               <p className="text-xs font-medium text-amber-600 dark:text-amber-400 uppercase tracking-wide">Used</p>
               <p className="text-lg font-bold text-amber-900 dark:text-amber-100 mt-1">{totalUsed}</p>
               <p className="text-xs text-amber-700 dark:text-amber-300">days consumed</p>
             </div>
             <div className="p-2 bg-amber-600 dark:bg-amber-700 rounded-lg">
               <CheckCircle className="h-4 w-4 text-white" />
             </div>
           </div>
         </div>

         <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border border-purple-200 dark:border-purple-700 rounded-lg p-3 hover:shadow-lg transition-all duration-300">
           <div className="flex items-center justify-between">
             <div>
               <p className="text-xs font-medium text-purple-600 dark:text-purple-400 uppercase tracking-wide">Usage Rate</p>
               <p className="text-lg font-bold text-purple-900 dark:text-purple-100 mt-1">{usagePercentage}%</p>
               <p className="text-xs text-purple-700 dark:text-purple-300">utilization</p>
             </div>
             <div className="p-2 bg-purple-600 dark:bg-purple-700 rounded-lg">
               <Award className="h-4 w-4 text-white" />
             </div>
           </div>
         </div>
      </div>
    );
  };

  // Professional header component
     const HeaderSection = () => (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
          <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                  <Calendar className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Leave Management</h2>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Manage your leave requests and balances</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <div className="flex items-center space-x-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-3 py-1.5 shadow-sm">
                  <Filter className="h-3 w-3 text-gray-500 dark:text-gray-400" />
                  <select
                    className="border-0 bg-transparent text-xs font-medium text-gray-700 dark:text-gray-300 focus:outline-none"
                    value={filterYear}
                    onChange={(e) => setFilterYear(e.target.value)}
                  >
                    <option value="2025">2025</option>
                    <option value="2024">2024</option>
                    <option value="2023">2023</option>
                  </select>
                </div>
                
                <button
                  onClick={forceRefreshLeaveData}
                  disabled={isRefreshing}
                  className="flex items-center space-x-1 px-3 py-1.5 text-xs font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Refresh Leave Data"
                >
                  {isRefreshing ? (
                    <>
                      <svg className="animate-spin h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>Refreshing...</span>
                    </>
                  ) : (
                    <>
                      <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                      <span>Refresh</span>
                    </>
                  )}
                </button>
                
                <button
                  onClick={() => setCurrentView('apply')}
                  className="flex items-center space-x-1 px-3 py-1.5 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white text-xs font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <Plus className="h-3 w-3" />
                  <span>Apply Leave</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      );

  // Enhanced leave balance cards
     const LeaveBalanceCards = () => (
     <div className="mb-4">
       <div className="flex items-center justify-between mb-3">
         <h2 className="text-base font-semibold text-gray-900 dark:text-gray-100">Leave Balances</h2>
         <div className="text-xs text-gray-500 dark:text-gray-400">Updated {new Date().toLocaleDateString()}</div>
       </div>
       
       {leaveBalances.length === 0 ? (
         <div className="text-center py-8 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
           <Calendar className="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
           <h3 className="text-base font-semibold text-gray-900 dark:text-gray-100 mb-2">No Leave Balances Available</h3>
           <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
             Your leave balances have not been configured yet. Please contact your HR department for assistance with setting up your leave entitlements.
           </p>
         </div>
       ) : (
         <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-3">
          {leaveBalances.map((balance, index) => {
            const getLeaveColor = (leaveType: string) => {
              if (leaveType.includes('ANNUAL')) return { 
                gradient: 'from-blue-500 to-blue-600', 
                bg: 'bg-blue-50 dark:bg-blue-900/20', 
                border: 'border-blue-200 dark:border-blue-700',
                text: 'text-blue-900 dark:text-blue-100',
                progress: 'bg-blue-500'
              };
              if (leaveType.includes('SICK')) return { 
                gradient: 'from-emerald-500 to-emerald-600', 
                bg: 'bg-emerald-50 dark:bg-emerald-900/20', 
                border: 'border-emerald-200 dark:border-emerald-700',
                text: 'text-emerald-900 dark:text-emerald-100',
                progress: 'bg-emerald-500'
              };
              if (leaveType.includes('CASUAL')) return { 
                gradient: 'from-purple-500 to-purple-600', 
                bg: 'bg-purple-50 dark:bg-purple-900/20', 
                border: 'border-purple-200 dark:border-purple-700',
                text: 'text-purple-900 dark:text-purple-100',
                progress: 'bg-purple-500'
              };
              return { 
                gradient: 'from-gray-500 to-gray-600', 
                bg: 'bg-gray-50 dark:bg-gray-900/20', 
                border: 'border-gray-200 dark:border-gray-700',
                text: 'text-gray-900 dark:text-gray-100',
                progress: 'bg-gray-500'
              };
            };
            
            const colors = getLeaveColor(balance.leaveType);
            const displayName = balance.leaveType.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
            const usagePercentage = getUsagePercentage(balance.used, balance.total);
            
                         return (
               <div key={index} className={`bg-white dark:bg-gray-800 ${colors.border} border rounded-lg p-3 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1`}>
                 <div className="flex items-center justify-between mb-2">
                   <div className={`px-2 py-1 ${colors.bg} rounded-lg`}>
                     <h3 className={`font-semibold ${colors.text} text-xs uppercase tracking-wide`}>{displayName}</h3>
                   </div>
                   <div className={`p-1 bg-gradient-to-r ${colors.gradient} rounded-lg shadow-sm`}>
                     <Calendar className="h-3 w-3 text-white" />
                   </div>
                 </div>
                 
                 <div className="space-y-2">
                   <div className="text-center">
                     <div className={`text-lg font-bold ${colors.text} mb-1`}>{balance.remaining}</div>
                     <div className="text-xs text-gray-600 dark:text-gray-400">days remaining</div>
                   </div>
                   
                   <div className="space-y-2">
                     <div className="flex justify-between text-xs">
                       <span className="text-gray-600 dark:text-gray-400">Used</span>
                       <span className="font-semibold text-gray-900 dark:text-gray-100">{balance.used} / {balance.total}</span>
                     </div>
                     
                     <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                       <div 
                         className={`${colors.progress} h-1 rounded-full transition-all duration-500`}
                         style={{ width: `${usagePercentage}%` }}
                       ></div>
                     </div>
                     
                     {balance.pending > 0 && (
                       <div className="flex justify-between text-xs">
                         <span className="text-amber-600 dark:text-amber-400">Pending</span>
                         <span className="font-semibold text-amber-700 dark:text-amber-300">{balance.pending} days</span>
                       </div>
                     )}
                   </div>
                 </div>
               </div>
             );
          })}
        </div>
      )}
    </div>
  );

  // Handle leave application form submission - Updated to use API service
  const handleLeaveSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    

    
    // Clear previous messages
    setErrorMessage(null);
    setSuccessMessage(null);
    
    // Validate form
    if (!leaveForm.leaveType) {
      setErrorMessage('Please select a leave type');
      return;
    }
    
    if (!leaveForm.reason.trim()) {
      setErrorMessage('Please provide a reason for your leave');
      return;
    }
    
    if (!leaveForm.startDate || !leaveForm.endDate) {
      setErrorMessage('Please select start and end dates');
      return;
    }

    // Validate dates
    const startDate = new Date(leaveForm.startDate);
    const endDate = new Date(leaveForm.endDate);
    if (startDate > endDate) {
      setErrorMessage('End date must be after start date');
      return;
    }

    // Check leave balance only for new requests, not modifications
    if (!editingLeaveId) {
      const availableDays = getLeaveBalance(leaveForm.leaveType).remaining;
      const requestedDays = calculateDays(leaveForm.startDate, leaveForm.endDate);
      
      console.log('📊 Leave balance check for new request:', {
        leaveType: leaveForm.leaveType,
        availableDays,
        requestedDays,
        balance: getLeaveBalance(leaveForm.leaveType)
      });
      
      if (availableDays < requestedDays) {
        // Show detailed alert with balance information
        const balance = getLeaveBalance(leaveForm.leaveType);
        const alertMessage = `❌ **Insufficient Leave Balance!**

**Leave Type:** ${leaveForm.leaveType.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
**Available Days:** ${availableDays}
**Requested Days:** ${requestedDays}
**Total Quota:** ${balance.total}
**Used Days:** ${balance.used}
**Pending Days:** ${balance.pending}

You cannot submit this leave request as you don't have enough balance. Please contact HR for assistance.`;
        
        setErrorMessage(alertMessage);
        return;
      }
    } else {
      console.log('📝 Skipping balance check for modification of existing request:', editingLeaveId);
    }

    if (!employeeId) {
      console.error('❌ Employee ID not found:', { employeeId, propEmployeeId, user });
      setErrorMessage('Employee ID not found');
      return;
    }

    console.log('✅ Validation passed, proceeding with submission');

    setIsSubmitting(true);
    
    try {
      // Calculate requested days for API call
      const requestedDays = calculateDays(leaveForm.startDate, leaveForm.endDate);
      
      // Use the API service to submit the leave request
      const leaveData = {
        leaveType: leaveForm.leaveType,
        startDate: leaveForm.startDate,
        endDate: leaveForm.endDate,
        reason: leaveForm.reason,
        totalDays: requestedDays,
        emergencyContact: leaveForm.emergencyContact, // Include emergency contact
        attachments: leaveForm.attachments
      };



      let success;
      if (editingLeaveId) {
        // Update existing request using the update method
        success = await employeePortalService.updateLeaveRequest(editingLeaveId, {
          leaveType: leaveData.leaveType,
          startDate: leaveData.startDate,
          endDate: leaveData.endDate,
          reason: leaveData.reason,
          emergencyContact: leaveForm.emergencyContact,
          attachments: leaveForm.attachments
        });
        console.log('📝 Updated existing leave request:', editingLeaveId);
      } else {
        // Create new request
        success = await employeePortalService.submitLeaveRequest(employeeId, leaveData);

      }
      

      
      if (success) {
        // Clear any existing messages first
        setErrorMessage(null);
        setSuccessMessage(editingLeaveId ? 
          'Your leave request has been updated successfully.' : 
          'Your leave application has been submitted successfully and is now pending approval.'
        );
        setCurrentView('summary');
        resetForm();
        setEditingLeaveId(null);
        
        // Immediately update local state with the new leave request
        if (!editingLeaveId) {
          // Add new leave request to local state immediately
          const newLeaveRequest: LeaveRequest = {
            id: Date.now(), // Temporary ID until server responds
            type: leaveForm.leaveType,
            startDate: leaveForm.startDate,
            endDate: leaveForm.endDate,
            days: calculateDays(leaveForm.startDate, leaveForm.endDate),
            status: 'pending',
            reason: leaveForm.reason,
            approvedBy: null,
            appliedOn: new Date().toISOString(),
            emergencyContact: leaveForm.emergencyContact || undefined,
            attachments: leaveForm.attachments.length > 0 ? leaveForm.attachments.map(f => f.name) : undefined,
            source: 'EMPLOYEE',
            createdBy: employeeName
          };
          
          // Add to beginning of the list (most recent first)
          setLeaveRecords(prevRecords => [newLeaveRequest, ...prevRecords]);
          
          console.log('✅ New leave request added to local state immediately');
        } else {
          // Update existing leave request in local state
          setLeaveRecords(prevRecords => 
            prevRecords.map(record => 
              record.id === editingLeaveId 
                ? {
                    ...record,
                    type: leaveForm.leaveType,
                    startDate: leaveForm.startDate,
                    endDate: leaveForm.endDate,
                    days: calculateDays(leaveForm.startDate, leaveForm.endDate),
                    reason: leaveForm.reason,
                    emergencyContact: leaveForm.emergencyContact || undefined
                  }
                : record
            )
          );
          
          console.log('✅ Existing leave request updated in local state');
        }
        
        // Also refresh from server in background to ensure data consistency
        setTimeout(async () => {
          try {
            await forceRefreshLeaveData();
            console.log('✅ Background server refresh completed');
            
            // Dispatch custom event to notify HR admin's table to refresh
            const eventName = editingLeaveId ? 'leave-request-updated' : 'leave-request-submitted';
            const leaveRequestEvent = new CustomEvent(eventName, {
              detail: {
                employeeId: employeeId,
                leaveType: leaveForm.leaveType,
                startDate: leaveForm.startDate,
                endDate: leaveForm.endDate,
                timestamp: new Date().toISOString(),
                isUpdate: !!editingLeaveId
              }
            });
            window.dispatchEvent(leaveRequestEvent);
            console.log(`📢 Dispatched ${eventName} event for HR admin notification`);
          } catch (error) {
            console.warn('⚠️ Background refresh failed, but local state is already updated');
          }
        }, 1000);
      } else {
        setErrorMessage(editingLeaveId ? 
          'Failed to update leave request. Please try again.' : 
          'Failed to submit leave request. Please try again.'
        );
      }
    } catch (error: any) {
      console.error('❌ Error submitting leave request:', error);
      setErrorMessage(error.message || 'Failed to submit leave request. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };
    
  // Auto-dismiss messages
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => setSuccessMessage(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  useEffect(() => {
    if (errorMessage) {
      const timer = setTimeout(() => setErrorMessage(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [errorMessage]);

  // Render summary view with enhanced design
     const renderSummaryView = () => (
     <div className="space-y-4">
       <HeaderSection />
       
       {/* Success message */}
       {successMessage && (
         <div className="bg-emerald-50 dark:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-700 rounded-lg p-3 shadow-sm">
           <div className="flex items-center">
             <CheckCircle className="h-4 w-4 text-emerald-600 dark:text-emerald-400 mr-2 flex-shrink-0" />
             <div>
               <h4 className="text-emerald-900 dark:text-emerald-100 font-semibold text-sm">Success!</h4>
               <p className="text-emerald-800 dark:text-emerald-300 text-xs mt-1">{successMessage}</p>
             </div>
           </div>
         </div>
       )}

      {/* Error message */}
      {errorMessage && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-3 shadow-sm">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400 mr-2 flex-shrink-0" />
            <div>
              <h4 className="text-red-900 dark:text-red-100 font-semibold text-sm">Error!</h4>
              <p className="text-red-800 dark:text-red-300 text-xs mt-1">{errorMessage}</p>
            </div>
          </div>
        </div>
      )}

       <SummaryStats />
       <LeaveBalanceCards />

       {/* Leave History */}
       <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
         <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
           <div className="flex items-center justify-between">
             <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">Leave History ({filterYear})</h3>
             <div className="text-xs text-gray-500 dark:text-gray-400">{filteredRecords.length} records</div>
           </div>
         </div>
        
                 <div className="overflow-x-auto">
           <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
             <thead className="bg-gray-50 dark:bg-gray-900">
               <tr>
                 <th className="px-3 py-2 text-left text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider">Leave Type</th>
                 <th className="px-3 py-2 text-left text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider">Duration</th>
                 <th className="px-3 py-2 text-left text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider">Days</th>
                 <th className="px-3 py-2 text-left text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider">Applied</th>
                 <th className="px-3 py-2 text-left text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider">Source</th>
                 <th className="px-3 py-2 text-left text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider">Status</th>
                 <th className="px-3 py-2 text-right text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wider">Actions</th>
               </tr>
             </thead>
             <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredRecords.length === 0 ? (
                                 <tr>
                   <td colSpan={7} className="px-6 py-8 text-center">
                     <div className="flex flex-col items-center">
                       <FileText className="h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" />
                       <h4 className="text-base font-semibold text-gray-900 dark:text-gray-100 mb-2">No Leave Records Found</h4>
                       <p className="text-gray-600 dark:text-gray-400 max-w-md">
                         You haven't applied for any leave in {filterYear}. Click "Apply Leave" to submit your first request.
                       </p>
                     </div>
                   </td>
                 </tr>
              ) : (
                filteredRecords.map(record => (
                                     <tr 
                     key={record.id}
                     className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 cursor-pointer"
                     onClick={() => {
                       setSelectedLeave(record);
                       setCurrentView('details');
                     }}
                   >
                     <td className="px-3 py-2 whitespace-nowrap">
                       <div className="flex items-center">
                         <div className="p-1 bg-blue-100 dark:bg-blue-900/30 rounded-lg mr-2">
                           <Calendar className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                         </div>
                         <div>
                           <div className="text-xs font-semibold text-gray-900 dark:text-gray-100">
                             {record.type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                           </div>
                         </div>
                       </div>
                     </td>
                     <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-700 dark:text-gray-300">
                       <div>
                         <div className="font-medium">{formatDate(record.startDate)}</div>
                         <div className="text-gray-500 dark:text-gray-400">to {formatDate(record.endDate)}</div>
                       </div>
                     </td>
                     <td className="px-3 py-2 whitespace-nowrap">
                       <span className="inline-flex items-center px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full text-xs font-semibold text-gray-800 dark:text-gray-200">
                         {record.days} {record.days === 1 ? 'day' : 'days'}
                       </span>
                     </td>
                     <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-700 dark:text-gray-300">
                       {new Date(record.appliedOn).toLocaleDateString()}
                     </td>
                     <td className="px-3 py-2 whitespace-nowrap">
                       {getSourceBadge(record)}
                     </td>
                     <td className="px-3 py-2 whitespace-nowrap">
                       <div className="flex items-center space-x-2">
                         {getStatusBadge(record.status)}
                         {hasModifications(record) && (
                           <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-amber-100 text-amber-800 rounded-full border border-amber-200">
                             <Edit className="h-3 w-3 mr-1" />
                             {getModificationCount(record)} changes
                           </span>
                         )}
                       </div>
                     </td>
                     <td className="px-3 py-2 whitespace-nowrap text-right">
                       <div className="flex items-center justify-end space-x-2">
                         {(record.status === 'PENDING' || record.status === 'pending') && (
                           <button
                             onClick={(e) => {
                               e.stopPropagation();
                               // Pre-fill form with existing data for editing
                               setLeaveForm({
                                 leaveType: record.type,
                                 startDate: record.startDate,
                                 endDate: record.endDate,
                                 reason: record.reason || '',
                                 emergencyContact: record.emergencyContact || '',
                                 attachments: []
                               });
                               setEditingLeaveId(record.id);
                               setCurrentView('apply');
                             }}
                             className="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300 transition-colors duration-200"
                             title="Edit Request"
                           >
                             <Edit className="h-4 w-4" />
                           </button>
                         )}
                         <button 
                           onClick={(e) => {
                             e.stopPropagation();
                             setSelectedLeave(record);
                             setCurrentView('details');
                           }}
                           className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 transition-colors duration-200"
                           title="View Details"
                         >
                           <ChevronRight className="h-4 w-4" />
                         </button>
                         {hasModifications(record) && (
                           <button
                             onClick={(e) => {
                               e.stopPropagation();
                               setSelectedLeave(record);
                               setShowAuditTrail(true);
                             }}
                             className="text-amber-600 dark:text-amber-400 hover:text-amber-900 dark:hover:text-amber-300 transition-colors duration-200"
                             title="View Audit Trail"
                           >
                             <History className="h-4 w-4" />
                           </button>
                         )}
                       </div>
                     </td>
                   </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderDetailsView = () => {
    if (!selectedLeave) return null;



     // Parse modificationHistory and extract approvals
      const history: ModificationHistoryEntry[] = selectedLeave.modificationHistory 
        ? (typeof selectedLeave.modificationHistory === 'string' 
            ? JSON.parse(selectedLeave.modificationHistory || '[]') 
            : selectedLeave.modificationHistory as ModificationHistoryEntry[])
        : [];
      const approvals = history.filter((h: ModificationHistoryEntry) => h.action === 'approve');
      const rejections = history.filter((h: ModificationHistoryEntry) => h.action === 'reject');
      
      // Get approval info from either direct fields or modification history
      const approverName = selectedLeave.approvedByName || 
                          (approvals.length > 0 ? approvals[0].actor : null);
      
      const approvalDate = selectedLeave.approvedOn || 
                          (approvals.length > 0 ? approvals[0].timestamp : null);
                          
      // Get rejection info from either direct fields or modification history
      const rejectorName = selectedLeave.rejectedByName || 
                          (rejections.length > 0 ? rejections[0].actor : null);
      
      const rejectionDate = selectedLeave.rejectedOn || 
                          (rejections.length > 0 ? rejections[0].timestamp : null);
                          
      const rejectionReason = selectedLeave.rejectionReason || 
                             (rejections.length > 0 ? rejections[0].reason : null);

    return (
      <div className="space-y-4">
         {/* Header */}
          <div className="flex items-center justify-between">
            <button
              onClick={() => setCurrentView('summary')}
             className="flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-all duration-200"
            >
             <ArrowLeft className="h-4 w-4" />
             <span className="text-sm font-medium">Back to Leave History</span>
            </button>
            
           <div className="flex items-center space-x-3">
             <span className="text-sm text-gray-500 dark:text-gray-400">Request ID: #{selectedLeave.id}</span>
             <div className="flex items-center space-x-2">
               {getSourceBadge(selectedLeave)}
               {getStatusBadge(selectedLeave.status)}
             </div>
           </div>
          </div>
  
         {/* Approval Timeline */}
         {/* Removed - now moved to right column above Quick Actions */}

         {/* Main Content */}
         <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
           {/* Left Column - Request Details */}
           <div className="lg:col-span-2 space-y-4">
             {/* Request Overview Card */}
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
               <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
                  <div className="flex items-center justify-between">
                    <div>
                     <h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100">
                        {selectedLeave.type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                      </h2>
                     <p className="text-blue-700 dark:text-blue-300 text-sm">Leave Request Details</p>
                    </div>
                   <div className="text-right">
                     <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">{selectedLeave.days}</div>
                     <div className="text-sm text-blue-700 dark:text-blue-300">{selectedLeave.days === 1 ? 'Day' : 'Days'}</div>
                    </div>
                  </div>
                </div>
                
               <div className="p-6 space-y-6">
                 {/* Duration Section */}
                 <div>
                   <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide mb-3">Duration</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                     <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                       <div className="flex items-center space-x-3">
                         <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                           <Calendar className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                         </div>
                          <div>
                           <p className="text-xs text-gray-500 dark:text-gray-400">Start Date</p>
                           <p className="font-semibold text-gray-900 dark:text-gray-100">{formatDate(selectedLeave.startDate)}</p>
                          </div>
                        </div>
                      </div>
                      
                     <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                       <div className="flex items-center space-x-3">
                         <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                           <Calendar className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                         </div>
                         <div>
                           <p className="text-xs text-gray-500 dark:text-gray-400">End Date</p>
                           <p className="font-semibold text-gray-900 dark:text-gray-100">{formatDate(selectedLeave.endDate)}</p>
                         </div>
                        </div>
                      </div>
                    </div>
                  </div>
  
                 {/* Application Details */}
                  <div>
                   <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide mb-3">Application Details</h3>
                   <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                      <div className="space-y-3">
                       <div className="flex justify-between items-center">
                          <span className="text-gray-600 dark:text-gray-400">Applied On:</span>
                         <span className="font-semibold text-gray-900 dark:text-gray-100">
                           {new Date(selectedLeave.appliedOn).toLocaleDateString('en-US', {
                             year: 'numeric',
                             month: 'long',
                             day: 'numeric',
                             hour: '2-digit',
                             minute: '2-digit'
                           })}
                         </span>
                        </div>
                                               {selectedLeave.approvedByName && (
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600 dark:text-gray-400">Approved By:</span>
                             <span className="font-semibold text-gray-900 dark:text-gray-100">{selectedLeave.approvedByName}</span>
                            </div>
                          )}
                         {selectedLeave.rejectedByName && (
                           <div className="flex justify-between items-center">
                             <span className="text-gray-600 dark:text-gray-400">Rejected By:</span>
                             <span className="font-semibold text-gray-900 dark:text-gray-100">{selectedLeave.rejectedByName}</span>
                           </div>
                         )}
                         {selectedLeave.createdByName && selectedLeave.createdByName !== selectedLeave.approvedByName && selectedLeave.createdByName !== selectedLeave.rejectedByName && (
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600 dark:text-gray-400">Created By:</span>
                              <span className="font-semibold text-gray-900 dark:text-gray-100">{selectedLeave.createdByName}</span>
                            </div>
                         )}
                      </div>
                    </div>
                  </div>
  
                 {/* Reason for Leave */}
                  <div>
                   <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide mb-3">Reason for Leave</h3>
                   <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                      <p className="text-gray-900 dark:text-gray-100 leading-relaxed">{selectedLeave.reason}</p>
                    </div>
                  </div>

                 {/* Emergency Contact */}
                  <div>
                   <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide mb-3">Emergency Contact</h3>
                   <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
                     <div className="flex items-start space-x-3">
                       <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                         <Phone className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                       </div>
                       <div>
                         <p className="text-sm font-medium text-blue-900 dark:text-blue-100">Contact Information</p>
                         {selectedLeave.emergencyContact ? (
                           <p className="text-blue-800 dark:text-blue-300">{selectedLeave.emergencyContact}</p>
                         ) : (
                           <p className="text-gray-500 dark:text-gray-400 italic">No emergency contact provided</p>
                         )}
                       </div>
                     </div>
                    </div>
                  </div>

                                  {/* Attachments */}
                  <div>
                   <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300 uppercase tracking-wide mb-3">Attachments</h3>
                   <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                     <div className="flex items-start space-x-3">
                       <div className="p-2 bg-gray-100 dark:bg-gray-600 rounded-lg">
                         <File className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                       </div>
                       <div className="flex-1">
                         <p className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Supporting Documents</p>
                         <div className="space-y-2">
                           {(() => {
                             try {
                               const attachments = typeof selectedLeave.attachments === 'string' 
                                 ? JSON.parse(selectedLeave.attachments) 
                                 : selectedLeave.attachments;
                               
                               if (Array.isArray(attachments) && attachments.length > 0) {
                                 return attachments.map((attachment: any, index: number) => (
                                   <div key={index} className="flex items-center justify-between p-2 bg-white dark:bg-gray-800 rounded border border-gray-200 dark:border-gray-600">
                                     <div className="flex items-center space-x-2">
                                       <File className="h-4 w-4 text-blue-500" />
                                       <span className="text-sm text-gray-700 dark:text-gray-300">
                                         {typeof attachment === 'string' ? attachment : attachment.name || `Document ${index + 1}`}
                                       </span>
                                     </div>
                                     <button
                                       onClick={() => {
                                         // Handle download if needed
                                         console.log('Download attachment:', attachment);
                                       }}
                                       className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                                     >
                                       View
                                     </button>
                                   </div>
                                 ));
                               } else {
                                 return (
                                   <p className="text-sm text-gray-500 dark:text-gray-400 italic">
                                     No attachments uploaded
                                   </p>
                                 );
                               }
                             } catch (error) {
                               return (
                                 <p className="text-sm text-gray-500 dark:text-gray-400 italic">
                                   No attachments uploaded
                                 </p>
                               );
                             }
                           })()}
                         </div>
                       </div>
                     </div>
                    </div>
                  </div>
  
                 {/* Rejection Reason */}
                  {rejectionReason && (
                    <div>
                     <h3 className="text-sm font-semibold text-red-600 dark:text-red-400 uppercase tracking-wide mb-3">Rejection Reason</h3>
                     <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4">
                       <div className="flex items-start space-x-3">
                         <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                         <p className="text-red-800 dark:text-red-300">{rejectionReason}</p>
                       </div>
                      </div>
                    </div>
                  )}
               </div>
              </div>
  
             {/* Approval Timeline (Manager, then HR) */}
             {approvals.length > 0 && (
               <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
                 <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20">
                   <h3 className="text-lg font-semibold text-green-900 dark:text-green-100">Approval Timeline</h3>
                 </div>
                 <div className="p-6 space-y-4">
                   {approvals.slice(0,2).map((step: ModificationHistoryEntry, idx: number) => (
                     <div key={idx} className="flex flex-col md:flex-row md:items-center md:space-x-4 mb-2">
                       <div className="font-semibold text-gray-900 dark:text-gray-100">
                         {idx === 0 ? 'Manager Approved By:' : 'HR Approved By:'}
                       </div>
                       <div className="text-blue-700 dark:text-blue-300 font-medium">{step.actor}</div>
                       <div className="text-xs text-gray-500 dark:text-gray-400">
                         {new Date(step.timestamp).toLocaleString()}
                       </div>
                       {step.reason && (
                         <div className="text-xs text-gray-700 dark:text-gray-300 italic ml-2">Comment: {step.reason}</div>
                       )}
                     </div>
                   ))}
                 </div>
               </div>
             )}
            </div>
  
           {/* Right Column - Quick Actions */}
           <div className="space-y-4">
            {/* Approval Timeline - Moved to right column above Quick Actions */}
            <ApprovalTimeline approvals={selectedLeaveApprovals} />
            
            {/* Quick Actions */}
            {(selectedLeave.status.toLowerCase() === 'pending' || selectedLeave.status.toLowerCase() === 'approved') && (
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
                  <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100">Quick Actions</h3>
                  <p className="text-blue-700 dark:text-blue-300 text-sm">
                    {selectedLeave.status.toLowerCase() === 'pending' 
                      ? 'You can edit or cancel your pending request' 
                      : 'You can cancel your approved request'}
                  </p>
                </div>
                <div className="p-6 space-y-3">
                  {selectedLeave.status.toLowerCase() === 'pending' && (
                    <button
                      onClick={() => {
                        setLeaveForm({
                          leaveType: selectedLeave.type,
                          startDate: selectedLeave.startDate,
                          endDate: selectedLeave.endDate,
                          reason: selectedLeave.reason || '',
                          emergencyContact: selectedLeave.emergencyContact || '',
                          attachments: []
                        });
                        setEditingLeaveId(selectedLeave.id);
                        setCurrentView('apply');
                      }}
                      className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-800 transition-colors duration-200"
                    >
                      <Edit className="h-4 w-4" />
                      <span>Edit Request</span>
                    </button>
                  )}
                  
                  <button
                    onClick={() => setShowCancelConfirm(true)}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-2 border border-red-300 dark:border-red-600 text-red-700 dark:text-red-400 bg-white dark:bg-gray-800 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200"
                  >
                    <X className="h-4 w-4" />
                    <span>Cancel Request</span>
                  </button>
                </div>
              </div>
            )}
            

            

           </div>
         </div>
      </div>
    );
  };

  // Enhanced apply view with professional form design
     const renderApplyView = () => (
     <div className="space-y-3">
       <div className="flex items-center justify-between">
         <button
           onClick={() => setCurrentView('summary')}
           className="flex items-center space-x-1 px-3 py-1 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-all duration-200"
         >
           <ArrowLeft className="h-3 w-3" />
           <span className="text-sm font-medium">Back to Dashboard</span>
         </button>
         
         <h1 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Apply for Leave</h1>
       </div>
       
              <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden">
         <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
           <h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100">New Leave Request</h2>
           <p className="text-blue-700 dark:text-blue-300 text-sm">Please fill out all required information for your leave request</p>
         </div>
         
         <form onSubmit={handleLeaveSubmit} className="p-4 space-y-4">
           <div>
             <label htmlFor="type" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 uppercase tracking-wide">
               Leave Type <span className="text-red-500">*</span>
             </label>
             <select
               id="type"
               className="w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white dark:bg-gray-700 text-sm text-gray-900 dark:text-gray-100"
              value={leaveForm.leaveType}
              onChange={(e) => setLeaveForm({...leaveForm, leaveType: e.target.value})}
               required
             >
               <option value="">Select Leave Type</option>
               {leaveBalances.map((balance) => (
                 <option key={balance.leaveType} value={balance.leaveType}>
                   {balance.leaveType.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                   {balance.remaining > 0 ? ` (${balance.remaining} days available)` : ' (No days available)'}
                 </option>
               ))}
             </select>
           </div>
          
          <div>
            <label htmlFor="daysRequested" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 uppercase tracking-wide">
              Days Requested
            </label>
            <div className="w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-sm text-gray-900 dark:text-gray-100 font-medium">
              {leaveForm.startDate && leaveForm.endDate 
                ? `${calculateDays(leaveForm.startDate, leaveForm.endDate)} ${calculateDays(leaveForm.startDate, leaveForm.endDate) === 1 ? 'day' : 'days'}`
                : 'Select dates'
              }
            </div>
          </div>
          
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
             <div>
               <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 uppercase tracking-wide">
                 Start Date <span className="text-red-500">*</span>
               </label>
               <input
                 type="date"
                 id="startDate"
                 className="w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                 value={leaveForm.startDate}
                 onChange={(e) => setLeaveForm({...leaveForm, startDate: e.target.value})}
                 min={new Date().toISOString().split('T')[0]}
                 required
               />
             </div>
             
             <div>
               <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 uppercase tracking-wide">
                 End Date <span className="text-red-500">*</span>
               </label>
               <input
                 type="date"
                 id="endDate"
                 className="w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                 value={leaveForm.endDate}
                 onChange={(e) => setLeaveForm({...leaveForm, endDate: e.target.value})}
                 min={leaveForm.startDate || new Date().toISOString().split('T')[0]}
                 required
               />
             </div>
           </div>
          
          {/* Days Count Display */}

           
           <div>
             <label htmlFor="reason" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 uppercase tracking-wide">
               Reason for Leave <span className="text-red-500">*</span>
             </label>
             <textarea
               id="reason"
               rows={3}
               className="w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
               value={leaveForm.reason}
               onChange={(e) => setLeaveForm({...leaveForm, reason: e.target.value})}
               placeholder="Please provide a detailed reason for your leave request..."
               required
             />
           </div>
           
           <div>
             <label htmlFor="contactInfo" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 uppercase tracking-wide">
               Emergency Contact Information
             </label>
             <input
               type="text"
               id="contactInfo"
               className="w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              value={leaveForm.emergencyContact}
              onChange={(e) => setLeaveForm({...leaveForm, emergencyContact: e.target.value})}
               placeholder="Phone number or email where you can be reached during leave..."
             />
           </div>



          {/* Remove the emergency leave checkbox UI */}
          
          <div>
            <label htmlFor="attachments" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 uppercase tracking-wide">
              Attachments
            </label>
            <div
              className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 text-center hover:border-blue-400 transition-colors cursor-pointer bg-gray-50 dark:bg-gray-700"
              onDragOver={handleDragOver}
              onDrop={handleDrop}
            >
              <input
                type="file"
                id="attachments"
                multiple
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt"
                className="hidden"
                onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
                disabled={isSubmitting}
              />
              <label htmlFor="attachments" className="cursor-pointer">
                <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  <span className="font-medium text-blue-600">Click to upload</span> or drag and drop
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">PDF, DOC, JPG, PNG (Max 10MB per file)</p>
              </label>
            </div>
            {leaveForm.attachments.length > 0 && (
              <div className="mt-3 space-y-2">
                {leaveForm.attachments.map((file, index) => (
                  <div key={index} className="flex items-center justify-between bg-white dark:bg-gray-800 p-3 rounded-lg border border-gray-200 dark:border-gray-600">
                    <div className="flex items-center gap-3">
                      <File className="h-5 w-5 text-blue-500" />
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{file.name}</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">{formatFileSize(file.size)}</p>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => removeFile(index)}
                      className="text-red-500 hover:text-red-700 p-1 rounded-full hover:bg-red-50 dark:hover:bg-red-900/20"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
          
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
              <div className="space-y-2">
                                 <p className="text-sm text-blue-800 dark:text-blue-100 font-medium">
                   Important Information
                 </p>
                 <p className="text-sm text-blue-700 dark:text-blue-300">
                   Your request will be sent to your manager for approval. Please ensure all details are accurate as changes may require resubmission.
                 </p>
                {leaveForm.leaveType && (
                   <p className="text-sm text-blue-700 dark:text-blue-300 font-medium">
                    Available Balance: {leaveBalances.find(b => b.leaveType === leaveForm.leaveType)?.remaining || 0} days of {leaveForm.leaveType.replace(/_/g, ' ').toLowerCase()}
                   </p>
                 )}
              </div>
            </div>
          </div>
          
                     <div className="flex justify-end space-x-2 pt-3 border-t border-gray-200 dark:border-gray-700">
             <button
               type="button"
               onClick={() => setCurrentView('summary')}
               className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200 text-sm font-medium"
             >
               Cancel
             </button>
             <button
               type="submit"
               className="flex items-center space-x-1 px-5 py-2 bg-gradient-to-r from-blue-600 to-blue-700 dark:from-blue-700 dark:to-blue-800 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 dark:hover:from-blue-800 dark:hover:to-blue-900 transition-all duration-200 shadow-lg hover:shadow-xl text-sm font-semibold"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Submitting...</span>
                </>
              ) : (
                <>
               <Check className="h-4 w-4" />
               <span>{editingLeaveId ? "Update" : "Submit"} Request</span>
                </>
              )}
             </button>
           </div>
        </form>
      </div>
    </div>
  );

  // Cancel Confirmation Modal Component
  const CancelConfirmModal = () => {
    if (!showCancelConfirm || !selectedLeave) return null;

    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div className="absolute inset-0 bg-gray-900/40 backdrop-blur-sm" onClick={() => setShowCancelConfirm(false)} />
        <div className="relative w-full max-w-md bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Cancel Leave Request</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {selectedLeave.status.toLowerCase() === 'approved' 
                ? 'Are you sure you want to cancel your approved leave request? This will free up your leave balance.'
                : 'Are you sure you want to cancel this leave request? This will mark it as cancelled.'
              }
            </p>
          </div>

          <div className="px-6 py-4">
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-3 mb-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">Warning</p>
                  <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                    {selectedLeave.status.toLowerCase() === 'approved' 
                      ? 'Cancelling this approved request will return the leave days to your balance and mark it as cancelled.'
                      : 'Cancelling this request will mark it as cancelled and it cannot be reactivated.'
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
            <button
              onClick={() => setShowCancelConfirm(false)}
              disabled={isCancelling}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Keep Request
            </button>
            <button
              onClick={async () => {
                setIsCancelling(true);
                try {
                  const result = await employeePortalService.cancelLeaveRequest(selectedLeave.id);
                  if (result.success) {
                    setSuccessMessage('Your leave request has been cancelled successfully.');
                    setShowCancelConfirm(false);
                    
                    // Immediately refresh the data
                    await refreshAfterCancellation();
                    
                    // Also remove from local state for immediate UI update
                    setLeaveRecords(prev => prev.filter(record => record.id !== selectedLeave.id));
                    
                    console.log('✅ Leave request cancelled and data refreshed successfully');
                  } else {
                    setErrorMessage(result.message || 'Failed to cancel leave request');
                  }
                } catch (error) {
                  console.error('Error cancelling leave request:', error);
                  setErrorMessage('Failed to cancel leave request. Please try again.');
                } finally {
                  setIsCancelling(false);
                }
              }}
              disabled={isCancelling}
              className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-red-600 rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isCancelling ? (
                <>
                  <svg className="animate-spin h-4 w-4 text-white inline mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Cancelling...
                </>
              ) : (
                'Cancel Request'
              )}
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Audit Trail Modal Component
  const AuditTrailModal = () => {
    if (!showAuditTrail || !selectedLeave) return null;

    const auditData = selectedLeave.modificationHistory 
      ? JSON.parse(selectedLeave.modificationHistory) 
      : [];

    const getActionIcon = (action: string) => {
      switch (action.toLowerCase()) {
        case 'approve':
          return <CheckCircle className="h-4 w-4 text-green-600" />;
        case 'reject':
          return <AlertCircle className="h-4 w-4 text-red-600" />;
        case 'modify':
          return <Edit className="h-4 w-4 text-orange-600" />;
        case 'cancel':
          return <X className="h-4 w-4 text-gray-600" />;
        case 'create':
          return <Plus className="h-4 w-4 text-blue-600" />;
        default:
          return <Clock className="h-4 w-4 text-blue-600" />;
      }
    };

    const getActionColor = (action: string) => {
      switch (action.toLowerCase()) {
        case 'approve':
          return 'bg-green-100 text-green-800 border-green-200';
        case 'reject':
          return 'bg-red-100 text-red-800 border-red-200';
        case 'modify':
          return 'bg-orange-100 text-orange-800 border-orange-200';
        case 'cancel':
          return 'bg-gray-100 text-gray-800 border-gray-200';
        case 'create':
          return 'bg-blue-100 text-blue-800 border-blue-200';
        default:
          return 'bg-blue-100 text-blue-800 border-blue-200';
      }
    };

    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div className="absolute inset-0 bg-gray-900/40 backdrop-blur-sm" onClick={() => setShowAuditTrail(false)} />
        <div className="relative w-full max-w-2xl bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Audit Trail</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {selectedLeave.type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())} - 
              {selectedLeave.startDate} to {selectedLeave.endDate}
            </p>
          </div>

          <div className="p-6 max-h-96 overflow-y-auto">
            {auditData.length === 0 ? (
              <div className="text-center py-8">
                <History className="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400">No audit trail available for this request.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {auditData.map((entry: any, index: number) => (
                  <div key={index} className="flex items-start space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex-shrink-0">
                      {getActionIcon(entry.action)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getActionColor(entry.action)}`}>
                            {entry.action}
                          </span>
                          <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {entry.actor}
                          </span>
                        </div>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {new Date(entry.timestamp).toLocaleString()}
                        </span>
                      </div>
                      
                      {entry.reason && (
                        <p className="text-sm text-gray-700 dark:text-gray-300 mb-2">{entry.reason}</p>
                      )}
                      
                      {entry.changes && Object.keys(entry.changes).length > 0 && (
                        <div className="mt-2">
                          <h4 className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Changes:</h4>
                          <div className="space-y-1">
                            {Object.entries(entry.changes).map(([key, value]) => (
                              <div key={key} className="text-xs text-gray-600 dark:text-gray-400">
                                <span className="font-medium">{key}:</span> {String(value)}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end">
            <button
              onClick={() => setShowAuditTrail(false)}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Render the appropriate view based on current state
  if (loading) {
    return <LoadingSpinner />;
  }

      return (
      <div className="p-2 bg-gray-50 dark:bg-gray-900 min-h-screen">
        <div className="w-full">
          {currentView === 'summary' && renderSummaryView()}
          {currentView === 'details' && renderDetailsView()}
          {currentView === 'apply' && renderApplyView()}
          {showAuditTrail && <AuditTrailModal />}
          {showCancelConfirm && <CancelConfirmModal />}
        </div>
      </div>
    );
};

export default EmployeeLeaveView; 