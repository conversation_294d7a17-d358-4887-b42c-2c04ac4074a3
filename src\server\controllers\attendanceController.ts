import { Request, Response } from 'express';
import { AppDataSource } from '../../config/database';
import { Attendance, AttendanceStatus, ApprovalStatus } from '../entities/Attendance';
import { Employee } from '../entities/Employee';
import { logger } from '../utils/logger';
import { validate } from 'class-validator';
import { AttendanceValidationService } from '../services/AttendanceValidationService';
import { Shift } from '../entities/Shift';
import { ShiftAssignment } from '../entities/ShiftAssignment';

/**
 * Get all attendance records with filtering and proper shift information
 * FIXED: Performance issues, missing shift info, incorrect policy enforcement
 */
export const getAttendances = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { 
      startDate, 
      endDate, 
      employeeId, 
      status, 
      department, 
      limit = '100', 
      offset = '0' 
    } = req.query;

    console.log('📋 Fetching attendance records with filters:', req.query);

    const attendanceRepository = AppDataSource.getRepository(Attendance);
    const shiftAssignmentRepository = AppDataSource.getRepository(ShiftAssignment);
    
    // OPTIMIZED: Single query with proper joins for better performance
    let query = attendanceRepository.createQueryBuilder('attendance')
      .leftJoinAndSelect('attendance.employee', 'employee')
      .leftJoinAndSelect('employee.job', 'job')
      .leftJoinAndSelect('attendance.shiftDetails', 'directShift'); // Direct shift relationship

    // Apply filters
    if (startDate) {
      query = query.andWhere('attendance.date >= :startDate', { startDate });
    }

    if (endDate) {
      query = query.andWhere('attendance.date <= :endDate', { endDate });
    }

    if (employeeId) {
      query = query.andWhere('attendance.employeeId = :employeeId', { employeeId: parseInt(employeeId as string) });
    }

    if (status) {
      query = query.andWhere('attendance.status = :status', { status });
    }

    if (department) {
      query = query.andWhere('(employee.department = :department OR job.department = :department)', { department });
    }

    // Order by date and ID for consistent results
    query = query.orderBy('attendance.date', 'DESC')
                 .addOrderBy('attendance.id', 'DESC')
                 .limit(parseInt(limit as string))
                 .offset(parseInt(offset as string));

    const attendances = await query.getMany();

    if (attendances.length === 0) {
      return res.status(200).json({
        success: true,
        data: [],
        count: 0
      });
    }

    // OPTIMIZED: Batch fetch all shift assignments to avoid N+1 queries
    const employeeIds = [...new Set(attendances.map(a => a.employeeId))];
    const dateRange = {
      start: startDate as string || attendances[attendances.length - 1].date,
      end: endDate as string || attendances[0].date
    };

    console.log(`🔍 Batch fetching shift assignments for ${employeeIds.length} employees between ${dateRange.start} and ${dateRange.end}`);

    // Fetch all relevant shift assignments in one query
    const shiftAssignments = await shiftAssignmentRepository
      .createQueryBuilder('assignment')
      .leftJoinAndSelect('assignment.shift', 'shift')
      .where('assignment.employeeId IN (:...employeeIds)', { employeeIds })
      .andWhere('assignment.isActive = true')
      .andWhere('assignment.startDate <= :endDate', { endDate: dateRange.end })
      .andWhere('(assignment.endDate IS NULL OR assignment.endDate >= :startDate)', { startDate: dateRange.start })
      .orderBy('assignment.employeeId', 'ASC')
      .addOrderBy('assignment.startDate', 'DESC')
      .getMany();

    console.log(`✅ Found ${shiftAssignments.length} active shift assignments`);

    // Create a map for faster lookup
    const employeeShiftMap = new Map<number, { assignment: ShiftAssignment; shift: Shift }[]>();
    shiftAssignments.forEach(assignment => {
      if (assignment.shift) {
        const employeeId = assignment.employeeId;
        if (!employeeShiftMap.has(employeeId)) {
          employeeShiftMap.set(employeeId, []);
        }
        employeeShiftMap.get(employeeId)!.push({
          assignment,
          shift: assignment.shift
        });
      }
    });

    // Helper function to find the correct shift for an employee on a specific date
    const getEmployeeShiftForDate = (employeeId: number, date: string): Shift | null => {
      const employeeShifts = employeeShiftMap.get(employeeId);
      if (!employeeShifts || employeeShifts.length === 0) {
        return null;
      }

      // Find the shift assignment that covers this date
      for (const { assignment, shift } of employeeShifts) {
        const startDate = assignment.startDate;
        const endDate = assignment.endDate;
        
        // Check if the date falls within this assignment period
        if (date >= startDate && (!endDate || date <= endDate)) {
          return shift;
        }
      }

      return null;
    };

    // Helper function for accurate policy enforcement
    const calculatePolicyViolations = (attendance: Attendance, shift: Shift) => {
      const policyInfo = {
        expectedStartTime: shift.startTime,
        expectedEndTime: shift.endTime,
        graceTimeInMinutes: shift.graceTimeInMinutes || 0,
        isLate: false,
        isEarlyOut: false,
        allowsOvertime: true,
        isFlexible: shift.isFlexible || false,
        requiredWorkHours: shift.requiredWorkHours || 8,
        breakDuration: shift.breakDuration || 0,
        isNightShift: shift.isNightShift // Use computed property from entity
      };

      // FIXED: Accurate late arrival calculation
      if (attendance.checkInTime && !policyInfo.isFlexible) {
        try {
          const checkInTime = new Date(`1970-01-01T${attendance.checkInTime}:00`);
          const expectedStart = new Date(`1970-01-01T${shift.startTime}:00`);
          const graceEndTime = new Date(expectedStart.getTime() + (shift.graceTimeInMinutes || 0) * 60000);
          
          policyInfo.isLate = checkInTime > graceEndTime;
          
          console.log(`⏰ Late check for employee ${attendance.employeeId}: CheckIn=${attendance.checkInTime}, Expected=${shift.startTime}, Grace=${shift.graceTimeInMinutes}min, IsLate=${policyInfo.isLate}`);
        } catch (error) {
          console.warn(`⚠️ Error calculating late arrival for employee ${attendance.employeeId}:`, error);
        }
      }

      // FIXED: Accurate early departure calculation
      if (attendance.checkOutTime && !policyInfo.isFlexible) {
        try {
          const checkOutTime = new Date(`1970-01-01T${attendance.checkOutTime}:00`);
          const expectedEnd = new Date(`1970-01-01T${shift.endTime}:00`);
          
          // For night shifts that cross midnight, handle day crossing
          if (shift.crossesMidnight) {
            expectedEnd.setDate(expectedEnd.getDate() + 1);
          }
          
          const graceStartTime = new Date(expectedEnd.getTime() - (shift.graceTimeInMinutes || 0) * 60000);
          
          policyInfo.isEarlyOut = checkOutTime < graceStartTime;
          
          console.log(`🏃 Early out check for employee ${attendance.employeeId}: CheckOut=${attendance.checkOutTime}, Expected=${shift.endTime}, Grace=${shift.graceTimeInMinutes}min, IsEarlyOut=${policyInfo.isEarlyOut}`);
        } catch (error) {
          console.warn(`⚠️ Error calculating early departure for employee ${attendance.employeeId}:`, error);
        }
      }

      return policyInfo;
    };

    // ENHANCED: Process attendance records with complete shift information
    const enhancedAttendances = attendances.map((attendance) => {
      let shiftInfo: Shift | null = null;
      let policyInfo = null;

      // Priority 1: Direct shift relationship (shiftDetails)
      if (attendance.shiftDetails) {
        shiftInfo = attendance.shiftDetails;
        console.log(`🔗 Using direct shift for employee ${attendance.employeeId}: ${shiftInfo.name}`);
      } 
      // Priority 2: Find from shift assignments
      else {
        shiftInfo = getEmployeeShiftForDate(attendance.employeeId, attendance.date);
        if (shiftInfo) {
          console.log(`📋 Found assigned shift for employee ${attendance.employeeId} on ${attendance.date}: ${shiftInfo.name}`);
        } else {
          console.log(`❌ No shift found for employee ${attendance.employeeId} on ${attendance.date}`);
        }
      }

      // Calculate policy enforcement if shift is available
      if (shiftInfo) {
        policyInfo = calculatePolicyViolations(attendance, shiftInfo);
      }

      // Enhanced attendance object with consistent shift information
      const enhancedAttendance = {
        ...attendance,
        // Employee information
        employeeName: attendance.employeeName || 
                     (attendance.employee ? `${attendance.employee.firstName} ${attendance.employee.lastName}` : 'Unknown'),
        department: attendance.department || 
                   attendance.employee?.job?.department || 
                   'N/A',
        position: attendance.position || 
                 attendance.employee?.job?.designation || 
                 'N/A',
        
        // FIXED: Consistent shift information
        shiftId: shiftInfo?.id || null,
        shiftName: shiftInfo?.name || 'No Shift Assigned',
        shiftTiming: shiftInfo ? { start: shiftInfo.startTime, end: shiftInfo.endTime } : null,
        shiftDetails: shiftInfo ? {
          id: shiftInfo.id,
          name: shiftInfo.name,
          startTime: shiftInfo.startTime,
          endTime: shiftInfo.endTime,
          requiredWorkHours: shiftInfo.requiredWorkHours || 8,
          breakDuration: shiftInfo.breakDuration || 0,
          isFlexible: shiftInfo.isFlexible || false,
          graceTimeInMinutes: shiftInfo.graceTimeInMinutes || 0,
          isNightShift: shiftInfo.isNightShift,
          crossesMidnight: shiftInfo.crossesMidnight,
          color: shiftInfo.color || '#3B82F6'
        } : null,
        
        // FIXED: Accurate policy enforcement information
        policyInfo: policyInfo
      };

      // Remove internal properties that shouldn't be sent to frontend
      const { employee, ...cleanedAttendance } = enhancedAttendance;
      
      return cleanedAttendance;
    });

    // Log summary statistics
    const shiftsFound = enhancedAttendances.filter(a => a.shiftDetails !== null).length;
    const lateArrivals = enhancedAttendances.filter(a => a.policyInfo?.isLate).length;
    const earlyDepartures = enhancedAttendances.filter(a => a.policyInfo?.isEarlyOut).length;

    console.log(`✅ Enhanced ${enhancedAttendances.length} attendance records:`, {
      totalRecords: enhancedAttendances.length,
      withShiftInfo: shiftsFound,
      withoutShiftInfo: enhancedAttendances.length - shiftsFound,
      lateArrivals,
      earlyDepartures,
      performanceImprovement: 'Single batch query vs N+1 queries'
    });

    return res.status(200).json({
      success: true,
      data: enhancedAttendances,
      count: enhancedAttendances.length,
      summary: {
        total: enhancedAttendances.length,
        withShiftInfo: shiftsFound,
        policyViolations: {
          late: lateArrivals,
          earlyOut: earlyDepartures
        }
      }
    });
  } catch (error) {
    logger.error('Error fetching attendance records:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch attendance records',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

/**
 * Get attendance for a specific employee
 */
export const getEmployeeAttendance = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { employeeId } = req.params;
    const { startDate, endDate } = req.query;

    // Validate employee exists
    const employee = await AppDataSource.getRepository(Employee).findOne({
      where: { id: parseInt(employeeId) }
    });

    if (!employee) {
      return res.status(404).json({ success: false, message: 'Employee not found' });
    }

    // Create query for attendance records
    let queryBuilder = AppDataSource.getRepository(Attendance)
      .createQueryBuilder('attendance')
      .where('attendance.employeeId = :employeeId', { employeeId: parseInt(employeeId) });

    if (startDate && endDate) {
      queryBuilder = queryBuilder.andWhere('attendance.date BETWEEN :startDate AND :endDate', { 
        startDate: startDate as string, 
        endDate: endDate as string 
      });
    }

    const attendances = await queryBuilder.orderBy('attendance.date', 'DESC').getMany();

    return res.status(200).json({ success: true, data: attendances });
  } catch (error) {
    logger.error(`Error fetching attendance for employee ${req.params.employeeId}:`, error);
    return res.status(500).json({ success: false, message: 'Failed to fetch employee attendance' });
  }
};

/**
 * Create a new attendance record or update existing one for the same date
 * Supports multiple time entries per day - first entry = check-in, last entry = check-out
 */
export const createAttendance = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { 
      employeeId, 
      date, 
      checkInTime, 
      checkOutTime, 
      status,
      location,
      notes,
      isRemote,
      timeEntry // New field for handling multiple time entries
    } = req.body;

    // Basic validation only (skip duplicate check as we handle updates)
    if (!employeeId) {
      return res.status(400).json({ 
        success: false, 
        message: 'Employee ID is required'
      });
    }

    if (!date) {
      return res.status(400).json({ 
        success: false, 
        message: 'Date is required'
      });
    }

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(date)) {
      return res.status(400).json({ 
        success: false, 
        message: 'Date must be in YYYY-MM-DD format'
      });
    }

    // Validate time formats if provided
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (checkInTime && !timeRegex.test(checkInTime)) {
      return res.status(400).json({ 
        success: false, 
        message: 'Check-in time must be in HH:MM format'
      });
    }

    if (checkOutTime && !timeRegex.test(checkOutTime)) {
      return res.status(400).json({ 
        success: false, 
        message: 'Check-out time must be in HH:MM format'
      });
    }

    if (timeEntry && !timeRegex.test(timeEntry)) {
      return res.status(400).json({ 
        success: false, 
        message: 'Time entry must be in HH:MM format'
      });
    }

    // Get employee info and validate employee exists
    const employee = await AppDataSource.getRepository(Employee)
      .findOne({
        where: { id: employeeId },
        relations: ['job']
      });

    if (!employee) {
      return res.status(404).json({ success: false, message: 'Employee not found' });
    }

    // Check if employee is active
    if (employee.status && employee.status.toLowerCase() !== 'active') {
      return res.status(400).json({ 
        success: false, 
        message: `Cannot create attendance for inactive employee (Status: ${employee.status})`
      });
    }

    // Always check for existing attendance record for this employee on this date
    const existingAttendance = await AppDataSource.getRepository(Attendance)
      .findOne({
        where: { 
          employeeId: employeeId,
          date: date
        }
      });

    let attendance: Attendance;
    let isUpdate = false;
    let timeEntries: string[] = [];

    if (existingAttendance) {
      // Update existing record - this ensures we never create duplicates
      attendance = existingAttendance;
      isUpdate = true;
      
      console.log(`🔄 Updating existing attendance record for employee ${employeeId} on ${date}`);
      
      // Get existing time entries from notes or create new array
      try {
        const existingNotes = attendance.notes || '';
        const timeEntriesMatch = existingNotes.match(/TIME_ENTRIES:\[(.*?)\]/);
        if (timeEntriesMatch) {
          timeEntries = timeEntriesMatch[1].split(',').filter(t => t.trim());
        }
      } catch (error) {
        timeEntries = [];
      }

      // Add new time entry if provided
      if (timeEntry) {
        if (!timeEntries.includes(timeEntry)) {
          timeEntries.push(timeEntry);
          timeEntries.sort(); // Keep times in chronological order
        }
      }

      // Add individual check-in/check-out times to entries if provided
      if (checkInTime && !timeEntries.includes(checkInTime)) {
        timeEntries.push(checkInTime);
      }
      if (checkOutTime && !timeEntries.includes(checkOutTime)) {
        timeEntries.push(checkOutTime);
      }

      // Sort all time entries chronologically
      timeEntries.sort();

      // Determine actual check-in and check-out times from all entries
      if (timeEntries.length > 0) {
        attendance.checkInTime = timeEntries[0]; // First/earliest time
        attendance.checkOutTime = timeEntries[timeEntries.length - 1]; // Last/latest time
        
        console.log(`⏰ ERP Logic Applied: Check-in: ${attendance.checkInTime}, Check-out: ${attendance.checkOutTime}`);
        console.log(`📝 All time entries: ${timeEntries.join(', ')}`);
      } else {
        // Fallback to individual times if no time entries
        if (checkInTime !== undefined && checkInTime !== null && checkInTime !== '') {
          attendance.checkInTime = checkInTime;
        }
        
        if (checkOutTime !== undefined && checkOutTime !== null && checkOutTime !== '') {
          attendance.checkOutTime = checkOutTime;
        }
      }
      
      // Update other fields intelligently
      if (status !== undefined && status !== null) {
        attendance.status = status;
      }
      
      if (location !== undefined && location !== null) {
        attendance.location = location;
      }
      
      // Handle notes with time entries tracking
      let baseNotes = '';
      if (notes) {
        baseNotes = notes;
      } else if (attendance.notes) {
        // Extract existing notes without time entries
        baseNotes = attendance.notes.replace(/TIME_ENTRIES:\[.*?\]/g, '').trim();
      }

      // Combine notes with time entries
      const timeEntriesStr = timeEntries.length > 0 ? `TIME_ENTRIES:[${timeEntries.join(',')}]` : '';
      attendance.notes = baseNotes && timeEntriesStr ? `${baseNotes} ${timeEntriesStr}` : (baseNotes || timeEntriesStr);
      
      if (isRemote !== undefined) {
        attendance.isRemote = isRemote;
      }
      
    } else {
      // Create new attendance record only if none exists
      console.log(`➕ Creating new attendance record for employee ${employeeId} on ${date}`);
      
      attendance = new Attendance();
      attendance.employeeId = employeeId;
      attendance.employeeName = `${employee.firstName} ${employee.lastName}`;
      attendance.date = date;
      attendance.status = status || AttendanceStatus.PRESENT;
      attendance.location = location || undefined;
      attendance.isRemote = isRemote || false;
      attendance.department = employee.job?.department;
      attendance.position = employee.job?.designation;

      // Handle initial time entries
      if (timeEntry) {
        timeEntries = [timeEntry];
        attendance.checkInTime = timeEntry;
        attendance.checkOutTime = timeEntry; // Initially same as check-in
      } else {
        // Use provided check-in/check-out times
        attendance.checkInTime = checkInTime || undefined;
        attendance.checkOutTime = checkOutTime || undefined;
        
        // Build time entries array
        if (checkInTime) timeEntries.push(checkInTime);
        if (checkOutTime && checkOutTime !== checkInTime) timeEntries.push(checkOutTime);
        timeEntries.sort();
      }

      // Set notes with time entries
      const timeEntriesStr = timeEntries.length > 0 ? `TIME_ENTRIES:[${timeEntries.join(',')}]` : '';
      attendance.notes = notes && timeEntriesStr ? `${notes} ${timeEntriesStr}` : (notes || timeEntriesStr);
    }

    // Always recalculate work hours based on first and last time entries (ERP logic)
    if (attendance.checkInTime && attendance.checkOutTime && attendance.checkInTime !== attendance.checkOutTime) {
      const checkIn = new Date(`1970-01-01T${attendance.checkInTime}`);
      const checkOut = new Date(`1970-01-01T${attendance.checkOutTime}`);
      let diffHours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
      
      // Handle overnight shifts
      if (diffHours < 0) {
        diffHours += 24;
      }
      
      attendance.workHours = parseFloat(diffHours.toFixed(2));
      
      // Calculate break time (if there are gaps between entries)
      let totalBreakTime = 0;
      if (timeEntries.length > 2) {
        // Calculate potential break times between consecutive entries
        for (let i = 1; i < timeEntries.length - 1; i += 2) {
          if (i + 1 < timeEntries.length) {
            const breakStart = new Date(`1970-01-01T${timeEntries[i]}`);
            const breakEnd = new Date(`1970-01-01T${timeEntries[i + 1]}`);
            const breakDuration = (breakEnd.getTime() - breakStart.getTime()) / (1000 * 60 * 60);
            if (breakDuration > 0) {
              totalBreakTime += breakDuration;
            }
          }
        }
      }
      
      attendance.breakTime = totalBreakTime > 0 ? parseFloat(totalBreakTime.toFixed(2)) : undefined;
      
      console.log(`⏱️ ERP Calculation: Work hours: ${attendance.workHours}, Break time: ${attendance.breakTime || 0}, Time entries: ${timeEntries.length}`);
    } else {
      // Set work hours to 0 if times are incomplete
      attendance.workHours = 0;
      attendance.breakTime = undefined;
    }

    // Validate the entity
    const errors = await validate(attendance);
    if (errors.length > 0) {
      return res.status(400).json({ success: false, message: 'Validation error', errors });
    }

    // Save the attendance record
    const savedAttendance = await AppDataSource.getRepository(Attendance).save(attendance);

    console.log(`✅ ${isUpdate ? 'Updated' : 'Created'} attendance record with ERP logic:`, {
      id: savedAttendance.id,
      employeeId: savedAttendance.employeeId,
      date: savedAttendance.date,
      checkInTime: savedAttendance.checkInTime,
      checkOutTime: savedAttendance.checkOutTime,
      workHours: savedAttendance.workHours,
      breakTime: savedAttendance.breakTime,
      timeEntriesCount: timeEntries.length
    });

    return res.status(isUpdate ? 200 : 201).json({ 
      success: true, 
      data: {
        ...savedAttendance,
        timeEntries: timeEntries,
        summary: {
          totalTimeEntries: timeEntries.length,
          firstEntry: timeEntries[0] || null,
          lastEntry: timeEntries[timeEntries.length - 1] || null,
          workHours: savedAttendance.workHours,
          breakTime: savedAttendance.breakTime || 0
        }
      },
      message: isUpdate ? 'Attendance record updated with ERP logic' : 'Attendance record created with ERP logic'
    });
  } catch (error) {
    logger.error('Error creating/updating attendance record:', error);
    return res.status(500).json({ success: false, message: 'Failed to create/update attendance record' });
  }
};

/**
 * Update an existing attendance record
 */
export const updateAttendance = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Find the attendance record
    const attendance = await AppDataSource.getRepository(Attendance).findOne({
      where: { id: parseInt(id) }
    });

    if (!attendance) {
      return res.status(404).json({ success: false, message: 'Attendance record not found' });
    }

    // Update the allowed fields
    const allowedFields = [
      'checkInTime', 'checkOutTime', 'status', 'location', 'notes', 
      'isRemote', 'isRegularized', 'regularizationReason', 'approvalStatus'
    ];

    for (const field of allowedFields) {
      if (field in updates) {
        (attendance as any)[field] = updates[field];
      }
    }

    // Recalculate work hours if check-in or check-out times were updated
    if (('checkInTime' in updates || 'checkOutTime' in updates) && attendance.checkInTime && attendance.checkOutTime) {
      const checkIn = new Date(`1970-01-01T${attendance.checkInTime}`);
      const checkOut = new Date(`1970-01-01T${attendance.checkOutTime}`);
      let diffHours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
      
      // Handle overnight shifts
      if (diffHours < 0) {
        diffHours += 24;
      }
      
      attendance.workHours = parseFloat(diffHours.toFixed(2));
    }

    // Set regularization info if being regularized
    if (updates.isRegularized && !attendance.isRegularized) {
      attendance.regularizedBy = req.body.userId || null;
      attendance.regularizationDate = new Date().toISOString().split('T')[0];
    }

    // Validate the updated entity
    const errors = await validate(attendance);
    if (errors.length > 0) {
      return res.status(400).json({ success: false, message: 'Validation error', errors });
    }

    // Save the updated attendance record
    const updatedAttendance = await AppDataSource.getRepository(Attendance).save(attendance);

    return res.status(200).json({ success: true, data: updatedAttendance });
  } catch (error) {
    logger.error(`Error updating attendance record ${req.params.id}:`, error);
    return res.status(500).json({ success: false, message: 'Failed to update attendance record' });
  }
};

/**
 * Delete an attendance record
 */
export const deleteAttendance = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;

    // Find the attendance record
    const attendance = await AppDataSource.getRepository(Attendance).findOne({
      where: { id: parseInt(id) }
    });

    if (!attendance) {
      return res.status(404).json({ success: false, message: 'Attendance record not found' });
    }

    // Delete the attendance record
    await AppDataSource.getRepository(Attendance).remove(attendance);

    return res.status(200).json({ success: true, message: 'Attendance record deleted successfully' });
  } catch (error) {
    logger.error(`Error deleting attendance record ${req.params.id}:`, error);
    return res.status(500).json({ success: false, message: 'Failed to delete attendance record' });
  }
};

/**
 * Import multiple attendance records with strict employee validation
 * Only saves records for employees that exist in the database
 */
export const importAttendances = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { attendances } = req.body;

    if (!attendances || !Array.isArray(attendances) || attendances.length === 0) {
      return res.status(400).json({ success: false, message: 'No attendance records provided for import' });
    }

    const attendanceRepository = AppDataSource.getRepository(Attendance);
    const employeeRepository = AppDataSource.getRepository(Employee);
    
    interface ImportResult {
      success: number;
      failed: number;
      skipped: number;
      errors: Array<{record: any; error: string; reason: string}>;
      skippedEmployees: Array<{employeeId: number; employeeName?: string; recordCount: number}>;
    }

    const results: ImportResult = {
      success: 0,
      failed: 0,
      skipped: 0,
      errors: [],
      skippedEmployees: []
    };

    logger.info(`Starting professional import validation for ${attendances.length} records`);

    // Step 1: Extract all unique employee codes from import data
    const employeeCodes = new Set<string>();
    const employeeRecordCount = new Map<string, {name?: string, count: number}>();
    
    attendances.forEach(record => {
      if (record.employeeCode || record.employeeId) {
        // Support both employeeCode and employeeId fields for flexibility
        const empCode = record.employeeCode || record.employeeId;
        const empCodeStr = String(empCode); // Ensure it's a string
        
        employeeCodes.add(empCodeStr);
        
        if (!employeeRecordCount.has(empCodeStr)) {
          employeeRecordCount.set(empCodeStr, {
            name: record.employeeName,
            count: 1
          });
        } else {
          employeeRecordCount.get(empCodeStr)!.count++;
        }
      }
    });

    // Step 2: Batch check which employees exist in database by employeeId string field
    // Handle case-insensitive matching and different formats (with/without -1 suffix)
    const allEmployees = await employeeRepository
      .createQueryBuilder('employee')
      .leftJoinAndSelect('employee.job', 'job')
      .getMany();

    // Create flexible mapping for employee codes
    const existingEmployeeCodes = new Set<string>();
    const employeeCodeToDbId = new Map<string, number>();
    const csvCodeToDbCode = new Map<string, string>(); // Maps CSV code to actual DB code
    
    // For each CSV employee code, try to find a match in the database
    Array.from(employeeCodes).forEach(csvCode => {
      const csvCodeLower = csvCode.toLowerCase();
      
      // Try exact match first
      let dbEmployee = allEmployees.find(emp => emp.employeeId === csvCode);
      
      // Try case-insensitive match
      if (!dbEmployee) {
        dbEmployee = allEmployees.find(emp => emp.employeeId.toLowerCase() === csvCodeLower);
      }
      
      // Try with -1 suffix
      if (!dbEmployee) {
        dbEmployee = allEmployees.find(emp => emp.employeeId.toLowerCase() === csvCodeLower + '-1');
      }
      
      // Try removing -1 suffix from DB code
      if (!dbEmployee) {
        dbEmployee = allEmployees.find(emp => emp.employeeId.toLowerCase().replace('-1', '') === csvCodeLower);
      }
      
      // Try partial match (remove any suffix from both)
      if (!dbEmployee) {
        const csvCodeBase = csvCodeLower.replace(/-\d+$/, ''); // Remove any numeric suffix
        dbEmployee = allEmployees.find(emp => {
          const dbCodeBase = emp.employeeId.toLowerCase().replace(/-\d+$/, '');
          return dbCodeBase === csvCodeBase;
        });
      }
      
      if (dbEmployee) {
        existingEmployeeCodes.add(csvCode); // Keep original CSV code as key
        employeeCodeToDbId.set(csvCode, dbEmployee.id);
        csvCodeToDbCode.set(csvCode, dbEmployee.employeeId);
        console.log(`✅ Matched CSV code "${csvCode}" to DB code "${dbEmployee.employeeId}" (ID: ${dbEmployee.id})`);
      } else {
        console.log(`❌ No match found for CSV code "${csvCode}"`);
      }
    });
    
    logger.info(`Found ${existingEmployeeCodes.size} existing employees out of ${employeeCodes.size} unique employee codes in import`);

    // Step 3: Identify employees that don't exist
    const nonExistentEmployeeCodes = Array.from(employeeCodes).filter(code => !existingEmployeeCodes.has(code));
    
    // Track skipped employees
    nonExistentEmployeeCodes.forEach(employeeCode => {
      const empData = employeeRecordCount.get(employeeCode);
      results.skippedEmployees.push({
        employeeId: employeeCode as any, // Keep as string for display
        employeeName: empData?.name,
        recordCount: empData?.count || 0
      });
      results.skipped += empData?.count || 0;
    });

    // Step 4: Process only records for existing employees
    for (const record of attendances) {
      try {
        // Get employee code from record (support both fields)
        const employeeCode = String(record.employeeCode || record.employeeId || '');
        
        // Skip records for non-existent employees
        if (!employeeCode || !existingEmployeeCodes.has(employeeCode)) {
          results.errors.push({
            record,
            error: `Employee code ${employeeCode} does not exist in database`,
            reason: 'EMPLOYEE_NOT_FOUND'
          });
          continue; // Skip this record
        }

        // Validate required fields
        if (!record.date) {
          results.failed++;
          results.errors.push({
            record,
            error: 'Date is required',
            reason: 'MISSING_REQUIRED_FIELD'
          });
          continue;
        }

        // Get the database ID for this employee code
        const dbEmployeeId = employeeCodeToDbId.get(employeeCode);
        if (!dbEmployeeId) {
          results.errors.push({
            record,
            error: `Could not find database ID for employee code ${employeeCode}`,
            reason: 'EMPLOYEE_NOT_FOUND'
          });
          continue;
        }

        // Check for duplicate attendance record
        const existingRecord = await attendanceRepository.findOne({
          where: { 
            employeeId: dbEmployeeId, 
            date: record.date 
          }
        });

        if (existingRecord) {
          results.failed++;
          results.errors.push({
            record,
            error: `Attendance already exists for employee ${employeeCode} on ${record.date}`,
            reason: 'DUPLICATE_RECORD'
          });
          continue;
        }

        // Get employee details for the record
        const actualDbCode = csvCodeToDbCode.get(employeeCode);
        const employee = allEmployees.find(emp => emp.employeeId === actualDbCode);
        if (!employee) {
          results.errors.push({
            record,
            error: `Employee details not found for code ${employeeCode}`,
            reason: 'EMPLOYEE_NOT_FOUND'
          });
          continue;
        }

        // Create and save attendance record (we know employee exists)
        const attendance = new Attendance();
        attendance.employeeId = dbEmployeeId;
        attendance.employeeName = record.employeeName || `${employee.firstName} ${employee.lastName}`;
        attendance.date = record.date;
        attendance.checkInTime = record.checkInTime || undefined;
        attendance.checkOutTime = record.checkOutTime || undefined;
        attendance.status = record.status || AttendanceStatus.PRESENT;
        attendance.location = record.location || undefined;
        attendance.notes = record.notes || undefined;
        attendance.isRemote = record.isRemote || false;
        attendance.department = record.department || employee.job?.department;
        attendance.position = record.position || employee.job?.designation;
        attendance.isImported = true;

        // Calculate work hours if both times are provided
        let calculatedWorkHours = 0;
        if (record.checkInTime && record.checkOutTime) {
          const checkIn = new Date(`1970-01-01T${record.checkInTime}`);
          const checkOut = new Date(`1970-01-01T${record.checkOutTime}`);
          let diffHours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
          
          if (diffHours < 0) {
            diffHours += 24; // Handle overnight shifts
          }
          
          calculatedWorkHours = parseFloat(diffHours.toFixed(2));
        }
        
        attendance.workHours = record.workHours || calculatedWorkHours;

        // Validate the entity before saving
        const errors = await validate(attendance);
        if (errors.length > 0) {
          results.failed++;
          results.errors.push({
            record,
            error: `Validation failed: ${errors.map(e => Object.values(e.constraints || {}).join(', ')).join('; ')}`,
            reason: 'VALIDATION_ERROR'
          });
          continue;
        }

        // Save to database
        await attendanceRepository.save(attendance);
        results.success++;
        
        logger.info(`Successfully saved attendance for employee ${employeeCode} (DB ID: ${dbEmployeeId}) on ${record.date}`);

      } catch (error) {
        results.failed++;
        results.errors.push({
          record,
          error: (error as Error).message,
          reason: 'SAVE_ERROR'
        });
        logger.error(`Error saving attendance record:`, error);
      }
    }

    // Generate comprehensive response
    const totalProcessed = results.success + results.failed + results.skipped;
    let message = `Import completed. Successfully saved ${results.success} records to database.`;
    
    if (results.failed > 0) {
      message += ` ${results.failed} records failed validation.`;
    }
    
    if (results.skipped > 0) {
      message += ` ${results.skipped} records skipped (employees not found in database).`;
    }

    logger.info(`Import summary: ${results.success} saved, ${results.failed} failed, ${results.skipped} skipped`);

    return res.status(200).json({
      success: true,
      message,
      results: {
        ...results,
        totalProcessed,
        summary: {
          total: attendances.length,
          saved: results.success,
          failed: results.failed,
          skipped: results.skipped,
          employeesNotFound: results.skippedEmployees.length
        }
      }
    });
  } catch (error) {
    logger.error('Error importing attendance records:', error);
    return res.status(500).json({ success: false, message: 'Failed to import attendance records' });
  }
};

/**
 * Get attendance summary
 */
export const getAttendanceSummary = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { employeeId, startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return res.status(400).json({ success: false, message: 'Start date and end date are required' });
    }

    // Build query
    let queryBuilder = AppDataSource.getRepository(Attendance)
      .createQueryBuilder('attendance')
      .where('attendance.date BETWEEN :startDate AND :endDate', { 
        startDate: startDate as string, 
        endDate: endDate as string 
      });

    if (employeeId) {
      queryBuilder = queryBuilder.andWhere('attendance.employeeId = :employeeId', { 
        employeeId: parseInt(employeeId as string) 
      });
    }

    const attendances = await queryBuilder.getMany();

    // Calculate summary statistics
    const summary = {
      totalPresent: attendances.filter(a => a.status === AttendanceStatus.PRESENT).length,
      totalAbsent: attendances.filter(a => a.status === AttendanceStatus.ABSENT).length,
      totalLate: attendances.filter(a => a.status === AttendanceStatus.LATE).length,
      totalHalfDay: attendances.filter(a => a.status === AttendanceStatus.HALF_DAY).length,
      totalLeave: attendances.filter(a => a.status === AttendanceStatus.LEAVE).length,
      totalWorkingDays: attendances.length,
      presentPercentage: 0,
      totalWorkHours: 0,
      averageWorkHours: 0
    };

    // Calculate additional statistics
    if (summary.totalWorkingDays > 0) {
      summary.presentPercentage = (summary.totalPresent / summary.totalWorkingDays) * 100;
    }

    const totalWorkHours = attendances.reduce((sum, a) => sum + (a.workHours || 0), 0);
    summary.totalWorkHours = parseFloat(totalWorkHours.toFixed(2));
    
    if (summary.totalPresent > 0) {
      summary.averageWorkHours = parseFloat((totalWorkHours / summary.totalPresent).toFixed(2));
    }

    return res.status(200).json({ success: true, data: summary });
  } catch (error) {
    logger.error('Error generating attendance summary:', error);
    return res.status(500).json({ success: false, message: 'Failed to generate attendance summary' });
  }
};

/**
 * Clear all imported attendance data
 */
export const clearImportedAttendances = async (req: Request, res: Response): Promise<Response> => {
  try {
    const attendanceRepository = AppDataSource.getRepository(Attendance);
    
    // Delete all records that were imported
    const deleteResult = await attendanceRepository
      .createQueryBuilder()
      .delete()
      .from(Attendance)
      .where('isImported = :isImported', { isImported: true })
      .execute();

    const deletedCount = deleteResult.affected || 0;
    
    logger.info(`Cleared ${deletedCount} imported attendance records from database`);

    return res.status(200).json({
      success: true,
      message: `Successfully cleared ${deletedCount} imported attendance records`,
      deletedCount
    });
  } catch (error) {
    logger.error('Error clearing imported attendance records:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to clear imported attendance records' 
    });
  }
};

/**
 * Clear all attendance data (imported and manual)
 */
export const clearAllAttendances = async (req: Request, res: Response): Promise<Response> => {
  try {
    const attendanceRepository = AppDataSource.getRepository(Attendance);
    
    // Delete all attendance records
    const deleteResult = await attendanceRepository
      .createQueryBuilder()
      .delete()
      .from(Attendance)
      .execute();

    const deletedCount = deleteResult.affected || 0;
    
    logger.info(`Cleared all ${deletedCount} attendance records from database`);

    return res.status(200).json({
      success: true,
      message: `Successfully cleared all ${deletedCount} attendance records`,
      deletedCount
    });
  } catch (error) {
    logger.error('Error clearing all attendance records:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to clear all attendance records' 
    });
  }
};

/**
 * Clock in/out for an employee - handles multiple time entries with ERP logic
 * First entry = check-in, Last entry = check-out, calculates breaks automatically
 */
export const clockInOut = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { 
      employeeId, 
      time, // Current time entry (HH:MM format)
      location,
      notes,
      isRemote 
    } = req.body;

    if (!employeeId || !time) {
      return res.status(400).json({ 
        success: false, 
        message: 'Employee ID and time are required' 
      });
    }

    // Validate time format
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(time)) {
      return res.status(400).json({ 
        success: false, 
        message: 'Time must be in HH:MM format'
      });
    }

    const today = new Date().toISOString().split('T')[0];

    // Get employee info
    const employee = await AppDataSource.getRepository(Employee)
      .findOne({
        where: { id: employeeId },
        relations: ['job']
      });

    if (!employee) {
      return res.status(404).json({ success: false, message: 'Employee not found' });
    }

    // Find or create attendance record for today
    let attendance = await AppDataSource.getRepository(Attendance)
      .findOne({
        where: { 
          employeeId: employeeId,
          date: today
        }
      });

    let timeEntries: string[] = [];
    let isNewRecord = false;

    if (!attendance) {
      // Create new record for first time entry of the day
      isNewRecord = true;
      attendance = new Attendance();
      attendance.employeeId = employeeId;
      attendance.employeeName = `${employee.firstName} ${employee.lastName}`;
      attendance.date = today;
      attendance.status = AttendanceStatus.PRESENT;
      attendance.department = employee.job?.department;
      attendance.position = employee.job?.designation;
      attendance.isRemote = isRemote || false;
      attendance.location = location;
      
      // First entry of the day
      timeEntries = [time];
      attendance.checkInTime = time;
      attendance.checkOutTime = time; // Initially same as check-in
      
      console.log(`🌅 First clock entry for employee ${employeeId} at ${time}`);
    } else {
      // Update existing record with new time entry
      
      // Extract existing time entries from notes
      try {
        const existingNotes = attendance.notes || '';
        const timeEntriesMatch = existingNotes.match(/TIME_ENTRIES:\[(.*?)\]/);
        if (timeEntriesMatch) {
          timeEntries = timeEntriesMatch[1].split(',').filter(t => t.trim());
        }
      } catch (error) {
        timeEntries = [];
      }

      // Add new time entry if not already present
      if (!timeEntries.includes(time)) {
        timeEntries.push(time);
        timeEntries.sort(); // Keep chronological order
        
        console.log(`⏰ Additional clock entry for employee ${employeeId} at ${time}`);
      } else {
        return res.status(400).json({ 
          success: false, 
          message: `Time entry ${time} already exists for today` 
        });
      }

      // Apply ERP logic: First entry = check-in, Last entry = check-out
      attendance.checkInTime = timeEntries[0]; // Earliest time
      attendance.checkOutTime = timeEntries[timeEntries.length - 1]; // Latest time
      
      // Update location if provided
      if (location && !attendance.location) {
        attendance.location = location;
      }
    }

    // Calculate work hours and break time using ERP logic
    if (timeEntries.length >= 2) {
      const checkIn = new Date(`1970-01-01T${attendance.checkInTime}`);
      const checkOut = new Date(`1970-01-01T${attendance.checkOutTime}`);
      let totalWorkHours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
      
      if (totalWorkHours < 0) {
        totalWorkHours += 24; // Handle overnight shifts
      }
      
      attendance.workHours = parseFloat(totalWorkHours.toFixed(2));

      // Calculate break time (gaps between consecutive entries)
      let totalBreakTime = 0;
      if (timeEntries.length > 2) {
        for (let i = 1; i < timeEntries.length - 1; i += 2) {
          if (i + 1 < timeEntries.length) {
            const breakStart = new Date(`1970-01-01T${timeEntries[i]}`);
            const breakEnd = new Date(`1970-01-01T${timeEntries[i + 1]}`);
            const breakDuration = (breakEnd.getTime() - breakStart.getTime()) / (1000 * 60 * 60);
            if (breakDuration > 0) {
              totalBreakTime += breakDuration;
            }
          }
        }
      }
      
      attendance.breakTime = totalBreakTime > 0 ? parseFloat(totalBreakTime.toFixed(2)) : 0;
    } else {
      attendance.workHours = 0;
      attendance.breakTime = 0;
    }

    // Update notes with time entries and additional notes
    let baseNotes = '';
    if (notes) {
      baseNotes = notes;
    } else if (attendance.notes) {
      // Extract existing notes without time entries
      baseNotes = attendance.notes.replace(/TIME_ENTRIES:\[.*?\]/g, '').trim();
    }

    const timeEntriesStr = `TIME_ENTRIES:[${timeEntries.join(',')}]`;
    attendance.notes = baseNotes && timeEntriesStr ? `${baseNotes} ${timeEntriesStr}` : timeEntriesStr;

    // Save the record
    const savedAttendance = await AppDataSource.getRepository(Attendance).save(attendance);

    // Determine the action type for response
    let actionType = '';
    let nextAction = '';
    
    if (timeEntries.length === 1) {
      actionType = 'Clock In';
      nextAction = 'Clock Out or Break';
    } else if (timeEntries.length === 2) {
      actionType = 'Clock Out';
      nextAction = 'Day Complete';
    } else {
      // Multiple entries - determine if this is a break or return
      const isEvenEntry = timeEntries.length % 2 === 0;
      actionType = isEvenEntry ? 'Break/Clock Out' : 'Return/Clock In';
      nextAction = isEvenEntry ? 'Day Complete or Return from Break' : 'Break or Final Clock Out';
    }

    console.log(`✅ ${isNewRecord ? 'Created' : 'Updated'} attendance with ERP clock logic:`, {
      id: savedAttendance.id,
      employeeId: savedAttendance.employeeId,
      date: savedAttendance.date,
      actionType,
      timeEntry: time,
      totalEntries: timeEntries.length,
      checkIn: savedAttendance.checkInTime,
      checkOut: savedAttendance.checkOutTime,
      workHours: savedAttendance.workHours,
      breakTime: savedAttendance.breakTime
    });

    return res.status(200).json({ 
      success: true, 
      data: {
        ...savedAttendance,
        timeEntries: timeEntries,
        clockSummary: {
          actionType,
          nextAction,
          totalTimeEntries: timeEntries.length,
          workHours: savedAttendance.workHours,
          breakTime: savedAttendance.breakTime || 0,
          firstEntry: timeEntries[0],
          lastEntry: timeEntries[timeEntries.length - 1],
          allEntries: timeEntries.join(' → ')
        }
      },
      message: `${actionType} successful at ${time}. ${nextAction}.`
    });

  } catch (error) {
    logger.error('Error in ERP clock in/out:', error);
    return res.status(500).json({ success: false, message: 'Failed to process clock entry' });
  }
};

/**
 * Remove duplicate attendance records - keeps the most recent one for each employee/date combination
 */
export const cleanupDuplicateAttendances = async (req: Request, res: Response): Promise<Response> => {
  try {
    const attendanceRepository = AppDataSource.getRepository(Attendance);
    
    // Find all duplicate records (same employeeId and date)
    const duplicatesQuery = `
      SELECT employeeId, date, COUNT(*) as count, GROUP_CONCAT(id ORDER BY id DESC) as ids
      FROM attendances 
      GROUP BY employeeId, date 
      HAVING COUNT(*) > 1
    `;
    
    const duplicates = await AppDataSource.query(duplicatesQuery);
    
    if (duplicates.length === 0) {
      return res.status(200).json({
        success: true,
        message: 'No duplicate attendance records found',
        cleaned: 0
      });
    }
    
    let totalCleaned = 0;
    const cleanupResults = [];
    
    for (const duplicate of duplicates) {
      const { employeeId, date, count, ids } = duplicate;
      const idArray = ids.split(',').map((id: string) => parseInt(id));
      
      // Keep the first ID (most recent due to ORDER BY id DESC) and delete the rest
      const [keepId, ...deleteIds] = idArray;
      
      if (deleteIds.length > 0) {
        // Delete the duplicate records
        const deleteResult = await attendanceRepository
          .createQueryBuilder()
          .delete()
          .from(Attendance)
          .where('id IN (:...ids)', { ids: deleteIds })
          .execute();
        
        const deletedCount = deleteResult.affected || 0;
        totalCleaned += deletedCount;
        
        cleanupResults.push({
          employeeId,
          date,
          totalRecords: count,
          keptRecordId: keepId,
          deletedRecords: deletedCount,
          deletedIds: deleteIds
        });
        
        console.log(`🧹 Cleaned ${deletedCount} duplicate records for employee ${employeeId} on ${date}, kept record ID ${keepId}`);
      }
    }
    
    console.log(`✅ Cleanup completed: removed ${totalCleaned} duplicate attendance records`);
    
    return res.status(200).json({
      success: true,
      message: `Successfully cleaned up ${totalCleaned} duplicate attendance records`,
      cleaned: totalCleaned,
      details: cleanupResults
    });
    
  } catch (error) {
    logger.error('Error cleaning up duplicate attendance records:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to cleanup duplicate attendance records' 
    });
  }
}; 