import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { KnowledgeStatus } from '../entities/KnowledgeBase';
import DOMPurify from 'dompurify';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'react-hot-toast';
import api from '../services/api';
import { 
  BookOpen, Star, Tag, ArrowLeft, Edit, Trash, 
  Clock, Eye, Folder, HardDrive, Layers, Server, 
  Shield, Database, Smartphone, Monitor, Settings, Cpu
} from 'lucide-react';

interface RelatedArticle {
  id: string;
  title: string;
  category: {
    name: string;
  };
}

interface KnowledgeArticle {
  id: string;
  title: string;
  content: string;
  summary?: string;
  status: KnowledgeStatus;
  viewCount: number;
  isFeatured: boolean;
  category: {
    id: string;
    name: string;
    slug: string;
  };
  createdBy: {
    id: string;
    name: string;
  };
  lastUpdatedBy?: {
    id: string;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
  tags: Array<{
    id: string;
    name: string;
  }>;
}

// Helper function to strip HTML tags
const stripHtmlTags = (html: string): string => {
  const tmp = document.createElement('div');
  tmp.innerHTML = html;
  return tmp.textContent || tmp.innerText || '';
};

const KnowledgeArticleView: React.FC = () => {
  const { id: paramId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  // Extract ID from URL if useParams doesn't work
  const getArticleId = () => {
    if (paramId) return paramId;
    
    // Fallback: extract from URL path
    const pathParts = window.location.pathname.split('/');
    const articleId = pathParts[pathParts.length - 1];
    console.log('🔧 ID TRACE: Extracted from URL:', articleId);
    return articleId;
  };
  
  const id = getArticleId();
  console.log('🔧 ID TRACE: Final article ID:', id);
  console.log('🔧 ID TRACE: Param ID:', paramId);
  console.log('🔧 ID TRACE: Current URL:', window.location.pathname);
  
  const [article, setArticle] = useState<KnowledgeArticle | null>(null);
  const [relatedArticles, setRelatedArticles] = useState<RelatedArticle[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [renderedContent, setRenderedContent] = useState<string>('');
  const [userRole, setUserRole] = useState<string | null>(null);

  useEffect(() => {
    const fetchArticle = async () => {
      // Extract article ID from URL if not found in params
      let articleId = id;
      
      // If no ID from router params, try to extract from URL path
      if (!articleId) {
        const pathParts = window.location.pathname.split('/');
        articleId = pathParts[pathParts.length - 1];
      }
      
      if (!articleId) {
        setError('Article ID not found');
        setIsLoading(false);
        return;
      }
      
      if (!user) {
        toast.error('Please log in to view articles');
        navigate('/login');
        return;
      }

      setIsLoading(true);
      try {
        // Get the article data
        const response = await api.get(`/knowledge-base/articles/${articleId}`);
        setArticle(response.data);
        
        // Sanitize and process the content for rendering
        const sanitizedContent = DOMPurify.sanitize(response.data.content, {
          ADD_TAGS: ['iframe'],
          ADD_ATTR: ['allow', 'allowfullscreen', 'frameborder', 'scrolling']
        });
        
        setRenderedContent(sanitizedContent);
        
        try {
          await api.post(`/knowledge-base/articles/${articleId}/view`);
        } catch (viewErr) {
          console.error('Error incrementing view count:', viewErr);
        }
        
        try {
          const relatedResponse = await api.get(`/knowledge-base/articles/${articleId}/related`);
          setRelatedArticles(relatedResponse.data);
        } catch (relatedErr) {
          console.error('Error fetching related articles:', relatedErr);
          setRelatedArticles([]);
        }
        
        setError(null);
      } catch (err: any) {
        console.error('Error fetching article:', err);
        
        if (err.response?.status === 401) {
          setError('Please log in to view this article');
          toast.error('Please log in to view this article');
          navigate('/login');
        } else if (err.response?.status === 404) {
          setError('Article not found');
          toast.error('Article not found');
        } else {
          setError('Error loading article. Please try again.');
          toast.error('Error loading article');
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchArticle();
  }, [id, navigate, user]);

  // Function to check user permissions
  useEffect(() => {
    // First try to get role from user context
    if (user?.role) {
      setUserRole(user.role);
    } else {
      // Fallback to API call if role not in context
      const checkUserRole = async () => {
        try {
          const response = await api.get('/auth/me');
          if (response.data && response.data.role) {
            setUserRole(response.data.role);
          }
        } catch (err) {
          console.error('Error checking user role:', err);
        }
      };
      
      if (user) {
        checkUserRole();
      }
    }
  }, [user]);

  const handleBackClick = () => {
    // Check if we're in the service-desk section
    const currentPath = window.location.pathname;
    const knowledgeBasePath = currentPath.includes('/service-desk') 
      ? '/service-desk/knowledge'
      : '/knowledge';
    
    // Use direct navigation for reliability
    window.location.href = knowledgeBasePath;
  };

  const handleEditArticle = (e: React.MouseEvent) => {
    console.log('🔧 EDIT TRACE: Button clicked');
    console.log('🔧 EDIT TRACE: Event:', e);
    
    e.preventDefault();
    e.stopPropagation();
    
    console.log('🔧 EDIT TRACE: Article ID:', id);
    
    if (!id) {
      console.error('🔧 EDIT TRACE: No article ID found for editing');
      return;
    }
    
    // Check if we're in the service-desk section
    const currentPath = window.location.pathname;
    console.log('🔧 EDIT TRACE: Current path:', currentPath);
    
    const editPath = currentPath.includes('/service-desk') 
      ? `/service-desk/knowledge/edit/${id}`
      : `/knowledge/edit/${id}`;
    
    console.log('🔧 EDIT TRACE: Edit path:', editPath);
    console.log('🔧 EDIT TRACE: About to navigate...');
    
    try {
      // Use direct navigation for reliability
      window.location.href = editPath;
      console.log('🔧 EDIT TRACE: Navigation command executed');
    } catch (error) {
      console.error('🔧 EDIT TRACE: Navigation error:', error);
    }
  };

  const handleDeleteArticle = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!id || !article) {
      console.error('Missing ID or article data');
      return;
    }
    
    if (window.confirm('Are you sure you want to delete this article? This action cannot be undone.')) {
      try {
        toast.loading('Deleting article...');
        
        const token = localStorage.getItem('authToken');
        const config = {
          headers: {
            Authorization: token ? `Bearer ${token}` : '',
          },
        };
        
        await api.delete(`/knowledge-base/articles/${id}`, config);
        toast.dismiss();
        toast.success('Article deleted successfully');
        
        // Navigate back to the correct knowledge base path
        const currentPath = window.location.pathname;
        const knowledgeBasePath = currentPath.includes('/service-desk') 
          ? '/service-desk/knowledge'
          : '/knowledge';
        
        // Use direct navigation for reliability
        window.location.href = knowledgeBasePath;
      } catch (err: any) {
        console.error('Error deleting article:', err);
        toast.dismiss();
        toast.error(err.response?.data?.message || 'Error deleting article');
      }
    }
  };

  const getCategoryIcon = (categoryName: string, iconSize = "h-12 w-12 text-blue-400") => {
    const categoryNameLower = categoryName.toLowerCase();
    
    if (categoryNameLower.includes('hardware')) {
      return <HardDrive className={iconSize} />;
    } else if (categoryNameLower.includes('software')) {
      return <Layers className={iconSize} />;
    } else if (categoryNameLower.includes('network')) {
      return <Server className={iconSize} />;
    } else if (categoryNameLower.includes('security')) {
      return <Shield className={iconSize} />;
    } else if (categoryNameLower.includes('database')) {
      return <Database className={iconSize} />;
    } else if (categoryNameLower.includes('mobile')) {
      return <Smartphone className={iconSize} />;
    } else if (categoryNameLower.includes('desktop')) {
      return <Monitor className={iconSize} />;
    } else if (categoryNameLower.includes('system')) {
      return <Settings className={iconSize} />;
    } else if (categoryNameLower.includes('cpu') || categoryNameLower.includes('processor')) {
      return <Cpu className={iconSize} />;
    } else {
      return <BookOpen className={iconSize} />;
    }
  };

  if (isLoading) {
    return (
      <div className="bg-gray-50 min-h-screen py-8">
        <div className="container mx-auto px-4">
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !article) {
    return (
      <div className="bg-gray-50 min-h-screen py-8">
        <div className="container mx-auto px-4">
          <button 
            onClick={handleBackClick}
            className="flex items-center mb-4 text-blue-600 hover:text-blue-800"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to Knowledge Base
          </button>
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold text-red-600 mb-2">Error</h2>
            <p className="text-gray-700">{error || 'Article not found.'}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen pb-8">
      {/* Header section */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white py-8">
        <div className="container mx-auto px-4">
          <button 
            onClick={handleBackClick}
            className="flex items-center text-white hover:text-blue-100 mb-4 transition-colors"
          >
            <ArrowLeft className="h-5 w-5 mr-1" />
            Back to Knowledge Base
          </button>
          
          <div className="flex flex-col md:flex-row md:items-center justify-between">
            <h1 className="text-2xl md:text-3xl font-bold text-white mb-4 md:mb-0">
              {article.title}
            </h1>
            
            {article.isFeatured && (
              <div className="px-2.5 py-1 bg-yellow-500/20 rounded-full">
                <span className="text-yellow-300 text-xs font-medium flex items-center">
                  <Star className="h-3.5 w-3.5 mr-1 fill-yellow-300" />
                  Featured Article
                </span>
              </div>
            )}
          </div>
          
          <div className="flex items-center mt-4">
            <div className="w-6 h-6 rounded-full bg-gray-700 flex items-center justify-center text-white font-medium text-xs">
              {article.createdBy.name.charAt(0)}
            </div>
            <div className="ml-2">
              <span className="text-white text-sm">{article.createdBy.name}</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Article content */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main content column */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="p-6 border-b border-gray-200">
                <div className="flex justify-between items-center mb-6">
                  <div className="flex items-center text-gray-500 text-sm space-x-4">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1.5" />
                      <span>
                        Updated {new Date(article.updatedAt).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <Eye className="h-4 w-4 mr-1.5" />
                      <span>
                        {article.viewCount} {article.viewCount === 1 ? 'view' : 'views'}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <Folder className="h-4 w-4 mr-1.5" />
                      <span>{article.category.name}</span>
                    </div>
                  </div>
                  
                  {/* Admin/Editor Actions */}
                  {(userRole === 'IT_ADMIN' || userRole === 'IT_STAFF' || user?.role === 'IT_ADMIN' || user?.role === 'IT_STAFF') && (
                    <div className="flex space-x-2 z-10 relative">
                      <button
                        type="button"
                        onClick={handleEditArticle}
                        className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <Edit className="h-4 w-4 mr-1.5" />
                        Edit
                      </button>
                      <button
                        type="button"
                        onClick={handleDeleteArticle}
                        className="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-red-500"
                      >
                        <Trash className="h-4 w-4 mr-1.5" />
                        Delete
                      </button>
                    </div>
                  )}
                </div>
                
                {article.summary && (
                  <div className="mb-6 text-gray-700 italic border-l-4 border-blue-500 pl-4 py-2 bg-blue-50 rounded-r">
                    {article.summary}
                  </div>
                )}
                
                <div className="flex flex-wrap gap-2 mb-4">
                  {article.tags.map(tag => (
                    <span 
                      key={tag.id}
                      className="px-3 py-1 bg-gray-100 rounded-full text-sm text-gray-600 hover:bg-gray-200 transition-colors"
                    >
                      {tag.name}
                    </span>
                  ))}
                </div>
              </div>
              
              {/* Article content */}
              <div className="p-6 prose max-w-none">
                <div 
                  className="article-content" 
                  dangerouslySetInnerHTML={{ __html: renderedContent }}
                />
              </div>
            </div>
          </div>
          
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-200">Related Articles</h3>
              <div className="space-y-4">
                {relatedArticles.map(relatedArticle => (
                  <div 
                    key={relatedArticle.id}
                    className="group"
                  >
                    <h4 
                      onClick={() => {
                        // Use React Router's navigate for related articles
                        const currentPath = window.location.pathname;
                        const articlePath = currentPath.includes('/service-desk') 
                          ? `/service-desk/knowledge/articles/${relatedArticle.id}`
                          : `/knowledge/articles/${relatedArticle.id}`;
                          
                        window.location.href = articlePath;
                      }}
                      className="font-medium text-gray-700 group-hover:text-gray-900 transition-colors cursor-pointer"
                    >
                      {relatedArticle.title}
                    </h4>
                    <div className="flex items-center mt-1 text-sm text-gray-500">
                      <Folder className="h-3.5 w-3.5 mr-1" />
                      <span>{relatedArticle.category.name}</span>
                    </div>
                  </div>
                ))}
                
                {relatedArticles.length === 0 && (
                  <p className="text-sm text-gray-500">No related articles found.</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default KnowledgeArticleView; 