import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Search,
  Filter,
  Plus,
  Building2,
  Mail,
  Phone,
  MapPin,
  Calendar,
  DollarSign,
  FileText,
  Users,
  Star,
  Clock,
  CheckCircle,
  AlertCircle,
  Globe,
  BarChart2,
  TrendingUp,
  MoreVertical,
  ChevronDown,
  ExternalLink,
  Briefcase,
  Shield,
  X,
  User,
  Tag,
  Activity,
  Settings,
  FileCheck,
  Database,
  Layers,
  Cloud,
  Package,
  Gauge,
  Zap,
  Link,
  Trash2,
  Save
} from 'lucide-react';

// Import VendorForm component from VendorForm.tsx
import { VendorForm } from './VendorForm';

// Add export for Vendor type at the top of the file
export interface Vendor {
  id: string;
  vendorName: string;
  companyName: string;
  status: VendorStatus;
  vendorType: VendorType;
  contactPerson: ContactPerson;
  alternativeContacts?: ContactPerson[];
  address: {
    street: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
    telephone?: string;
  };
  website?: string;
  contract: {
    startDate: string;
    expiryDate: string;
    renewalReminder?: boolean;
    autoRenewal?: boolean;
    terminationNotice: number;
  };
  performance: {
    rating: number;
    responseTime: number;
    deliveryScore: number;
    qualityScore: number;
  };
  financial: {
    spending: {
      current: {
        currency: string;
        value: number;
      };
      trend: number;
    };
  };
}

export type VendorStatus = 'ACTIVE' | 'INACTIVE' | 'BLACKLISTED' | 'PENDING_REVIEW';
export type VendorType = 
  // IT Hardware & Infrastructure
  | 'HARDWARE_SUPPLIER'
  | 'PRINTER_PHOTOCOPIER_SUPPLIER'
  | 'SERVER_STORAGE_SUPPLIER'
  | 'CCTV_SURVEILLANCE_SUPPLIER'
  | 'POWER_UPS_SUPPLIER'
  | 'IT_ACCESSORIES_SUPPLIER'
  // Internet & Networking
  | 'ISP'
  | 'NETWORKING_EQUIPMENT_SUPPLIER'
  | 'FIREWALL_SECURITY_VENDOR'
  | 'VPN_SECURE_ACCESS_PROVIDER'
  // Cloud & Hosting
  | 'CLOUD_SERVICE_PROVIDER'
  | 'DOMAIN_HOSTING_PROVIDER'
  | 'BACKUP_DISASTER_RECOVERY'
  // Business Software & IT Solutions
  | 'ERP_VENDOR'
  | 'CRM_VENDOR'
  | 'HRMS_PAYROLL_VENDOR'
  | 'POS_RETAIL_SOFTWARE'
  | 'FINANCIAL_ACCOUNTING_SOFTWARE'
  // Call Center & Communication
  | 'PBX_VOIP_PROVIDER'
  | 'CALL_CENTER_SOFTWARE'
  | 'TELECOM_SERVICES'
  // IT Security & Cybersecurity
  | 'CYBERSECURITY_VENDOR'
  | 'ANTIVIRUS_ENDPOINT_PROTECTION'
  | 'IDENTITY_ACCESS_MANAGEMENT'
  // IT Consultancy & Development
  | 'IT_CONSULTANCY'
  | 'SOFTWARE_DEVELOPMENT'
  | 'BLOCKCHAIN_WEB3';

export interface ContactPerson {
  name: string;
  title: string;
  department?: string;
  email: string;
  phone: string;
}

export const VendorContacts = () => {
  // State for vendors
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // Get saved filters from localStorage
  const [searchQuery, setSearchQuery] = useState(() => {
    return localStorage.getItem('vendorSearchQuery') || '';
  });
  
  const [selectedStatus, setSelectedStatus] = useState(() => {
    return localStorage.getItem('vendorSelectedStatus') || 'all';
  });
  
  const [selectedType, setSelectedType] = useState(() => {
    return localStorage.getItem('vendorSelectedType') || 'all';
  });
  
  const [showAddForm, setShowAddForm] = useState(false);
  const [expandedVendor, setExpandedVendor] = useState<string | null>(null);
  const [editingVendor, setEditingVendor] = useState<Vendor | null>(null);

  // Fetch vendors from API with retry capability
  const fetchVendors = async (retryCount = 0) => {
    try {
      setLoading(true);
      const params: any = {};
      
      if (searchQuery) {
        params.search = searchQuery;
      }
      
      if (selectedStatus !== 'all') {
        params.status = selectedStatus;
      }
      
      if (selectedType !== 'all') {
        params.vendorType = selectedType;
      }
      
      const response = await axios.get('/api/vendors', { params });
      setVendors(response.data);
      setError(null);
    } catch (err: any) {
      console.error('Error fetching vendors:', err);
      
      // Try to extract a more detailed error message
      const errorMessage = err.response?.data?.message || 
                         err.response?.data?.error || 
                         err.message || 
                         'Failed to load vendors. Please try again later.';
      
      setError(errorMessage);
      
      // Retry the request up to 2 times if it's a server error (500)
      if (retryCount < 2 && err.response?.status === 500) {
        setTimeout(() => {
          fetchVendors(retryCount + 1);
        }, 1000); // Wait 1 second before retrying
      }
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchVendors();
  }, []);
  
  // Refetch when filters change
  useEffect(() => {
    fetchVendors();
  }, [searchQuery, selectedStatus, selectedType]);
  
  // Save filters to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('vendorSearchQuery', searchQuery);
  }, [searchQuery]);
  
  useEffect(() => {
    localStorage.setItem('vendorSelectedStatus', selectedStatus);
  }, [selectedStatus]);
  
  useEffect(() => {
    localStorage.setItem('vendorSelectedType', selectedType);
  }, [selectedType]);
  
  // Utility functions
  const getStatusColor = (status: VendorStatus) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'INACTIVE':
        return 'bg-gray-100 text-gray-800';
      case 'BLACKLISTED':
        return 'bg-red-100 text-red-800';
      case 'PENDING_REVIEW':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const toggleVendorExpansion = (vendorId: string) => {
    setExpandedVendor(expandedVendor === vendorId ? null : vendorId);
  };

  // CRUD Operations
  const handleAddVendor = async (newVendor: Vendor) => {
    try {
      setLoading(true);
      const response = await axios.post('/api/vendors', newVendor);
      setVendors([...vendors, response.data]);
      setShowAddForm(false);
      setError(null);
    } catch (err: any) {
      console.error('Error adding vendor:', err);
      const errorMessage = err.response?.data?.message || 
                         err.response?.data?.error || 
                         err.message || 
                         'Failed to add vendor. Please try again.';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleEditVendor = (vendor: Vendor) => {
    setEditingVendor(vendor);
    setShowAddForm(true);
  };

  const handleUpdateVendor = async (updatedVendor: Vendor) => {
    try {
      setLoading(true);
      const response = await axios.put(`/api/vendors/${updatedVendor.id}`, updatedVendor);
      setVendors(vendors.map(vendor => 
        vendor.id === updatedVendor.id ? response.data : vendor
      ));
      setShowAddForm(false);
      setEditingVendor(null);
      setError(null);
    } catch (err: any) {
      console.error('Error updating vendor:', err);
      const errorMessage = err.response?.data?.message || 
                         err.response?.data?.error || 
                         err.message || 
                         'Failed to update vendor. Please try again.';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteVendor = async (vendorId: string) => {
    if (window.confirm('Are you sure you want to delete this vendor?')) {
      try {
        setLoading(true);
        await axios.delete(`/api/vendors/${vendorId}`);
        setVendors(vendors.filter(vendor => vendor.id !== vendorId));
        setExpandedVendor(null);
        setError(null);
      } catch (err: any) {
        console.error('Error deleting vendor:', err);
        const errorMessage = err.response?.data?.message || 
                           err.response?.data?.error || 
                           err.message || 
                           'Failed to delete vendor. Please try again.';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleFormSubmit = (vendor: Vendor) => {
    if (editingVendor) {
      handleUpdateVendor(vendor);
    } else {
      handleAddVendor(vendor);
    }
  };

  const handleFormClose = () => {
    setShowAddForm(false);
    setEditingVendor(null);
  };

  //-----------------------------------------------------------------------------
  // Render Methods
  //-----------------------------------------------------------------------------
  const filteredVendors = vendors;
  
  return (
    <div className="flex-1">
      <div className="bg-white rounded-lg shadow p-4 space-y-3">
        <div className="space-y-1">
          <h2 className="text-xl font-medium text-gray-900">Vendor Directory</h2>
          <p className="text-sm text-gray-600">Manage and view detailed information about all vendors</p>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
            <strong className="font-bold">Error: </strong>
            <span className="block sm:inline">{error}</span>
            <button 
              className="absolute top-0 bottom-0 right-0 px-4 py-3"
              onClick={() => setError(null)}
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        )}

        <div className="flex items-center gap-3">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search vendors, contacts, or companies..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
          <select
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
          >
            <option value="all">All Status</option>
            <option value="ACTIVE">Active</option>
            <option value="INACTIVE">Inactive</option>
            <option value="BLACKLISTED">Blacklisted</option>
            <option value="PENDING_REVIEW">Pending Review</option>
          </select>
          <select
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
          >
            <option value="all">All Types</option>
            <option value="HARDWARE_SUPPLIER">Hardware Supplier</option>
            <option value="CLOUD_SERVICE_PROVIDER">Cloud Service Provider</option>
            <option value="SOFTWARE_DEVELOPMENT">Software Development</option>
            <option value="ERP_VENDOR">ERP Vendor</option>
            <option value="CRM_VENDOR">CRM Vendor</option>
            <option value="CYBERSECURITY_VENDOR">Cybersecurity Vendor</option>
            <option value="ISP">Internet Service Provider</option>
          </select>
          
          {/* Reset Filters Button - show only if filters are active */}
          {(searchQuery || selectedStatus !== 'all' || selectedType !== 'all') && (
            <button
              onClick={() => {
                setSearchQuery('');
                setSelectedStatus('all');
                setSelectedType('all');
              }}
              className="px-3 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-100"
            >
              <X className="h-5 w-5" />
            </button>
          )}
          
          <button
            onClick={() => setShowAddForm(true)}
            className="inline-flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            <Plus className="h-5 w-5 mr-1" />
            Add Vendor
          </button>
        </div>

        {/* Show filter count */}
        <div className="mt-2 mb-3 flex justify-between items-center">
          <p className="text-sm text-gray-600">
            {loading ? 'Loading vendors...' : (
              <>
                Showing {filteredVendors.length} vendors
                {(searchQuery || selectedStatus !== 'all' || selectedType !== 'all') && ' (filtered)'}
              </>
            )}
          </p>
        </div>
        
        {loading ? (
          <div className="py-8 flex justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredVendors.map((vendor) => (
              <div
                key={vendor.id}
                className="bg-white rounded-lg shadow-sm overflow-hidden"
              >
                <div 
                  className="p-4 cursor-pointer hover:bg-gray-50"
                  onClick={() => toggleVendorExpansion(vendor.id)}
                >
                  <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="p-1.5 bg-blue-100 rounded-lg">
                          <Building2 className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{vendor.vendorName}</h3>
                          <p className="text-sm text-gray-500">{vendor.companyName}</p>
                        </div>
                        <span className={`ml-2 px-2 py-0.5 text-xs font-medium rounded-full ${getStatusColor(vendor.status)}`}>
                          {vendor.status}
                        </span>
                      </div>

                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 mt-2">
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600">{vendor.contactPerson.email}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600">{vendor.contactPerson.phone}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600">{vendor.address.city}, {vendor.address.country}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Star className="h-4 w-4 text-yellow-400" />
                          <span className="text-sm text-gray-600">{vendor.performance.rating}/5 Rating</span>
                        </div>
                      </div>
                    </div>
                    <ChevronDown 
                      className={`h-5 w-5 text-gray-400 transform transition-transform ${expandedVendor === vendor.id ? 'rotate-180' : ''}`}
                    />
                  </div>
                </div>

                {expandedVendor === vendor.id && (
                  <div className="border-t border-gray-200 p-4">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                      {/* Contact Information */}
                      <div className="space-y-4">
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <h4 className="text-lg font-medium text-gray-900 mb-3">Primary Contact</h4>
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Users className="h-5 w-5 text-gray-400" />
                              <div>
                                <p className="text-sm font-medium">{vendor.contactPerson.name}</p>
                                <p className="text-sm text-gray-500">{vendor.contactPerson.title}</p>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Briefcase className="h-5 w-5 text-gray-400" />
                              <p className="text-sm text-gray-600">{vendor.contactPerson.department}</p>
                            </div>
                            <div className="flex items-center gap-2">
                              <Mail className="h-5 w-5 text-gray-400" />
                              <p className="text-sm text-gray-600">{vendor.contactPerson.email}</p>
                            </div>
                            <div className="flex items-center gap-2">
                              <Phone className="h-5 w-5 text-gray-400" />
                              <p className="text-sm text-gray-600">{vendor.contactPerson.phone}</p>
                            </div>
                          </div>
                        </div>

                        {vendor.alternativeContacts && vendor.alternativeContacts.length > 0 && (
                          <div className="bg-gray-50 p-3 rounded-lg">
                            <h4 className="text-lg font-medium text-gray-900 mb-3">Alternative Contacts</h4>
                            {vendor.alternativeContacts.map((contact: ContactPerson, index: number) => (
                              <div key={index} className="space-y-2 mt-2">
                                <div className="flex items-center gap-2">
                                  <Users className="h-5 w-5 text-gray-400" />
                                  <div>
                                    <p className="text-sm font-medium">{contact.name}</p>
                                    <p className="text-sm text-gray-500">{contact.title}</p>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Mail className="h-5 w-5 text-gray-400" />
                                  <p className="text-sm text-gray-600">{contact.email}</p>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Phone className="h-5 w-5 text-gray-400" />
                                  <p className="text-sm text-gray-600">{contact.phone}</p>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>

                      {/* Company & Contract Details */}
                      <div className="space-y-4">
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <h4 className="text-lg font-medium text-gray-900 mb-3">Company Details</h4>
                          <div className="space-y-2">
                            <div className="flex items-center gap-3">
                              <Building2 className="h-5 w-5 text-gray-400" />
                              <div>
                                <p className="text-sm font-medium">Company Type</p>
                                <p className="text-sm text-gray-600">{vendor.vendorType.replace('_', ' ')}</p>
                              </div>
                            </div>
                            {vendor.website && (
                              <div className="flex items-center gap-3">
                                <Globe className="h-5 w-5 text-gray-400" />
                                <div>
                                  <p className="text-sm font-medium">Website</p>
                                  <a 
                                    href={vendor.website} 
                                    target="_blank" 
                                    rel="noopener noreferrer" 
                                    className="text-sm text-blue-600 hover:text-blue-700"
                                  >
                                    {vendor.website}
                                  </a>
                                </div>
                              </div>
                            )}
                            <div className="flex items-center gap-3">
                              <MapPin className="h-5 w-5 text-gray-400" />
                              <div>
                                <p className="text-sm font-medium">Address</p>
                                <p className="text-sm text-gray-600">{vendor.address.street}</p>
                                <p className="text-sm text-gray-600">
                                  {vendor.address.city}, {vendor.address.state}
                                </p>
                                <p className="text-sm text-gray-600">
                                  {vendor.address.country} - {vendor.address.postalCode}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="bg-gray-50 p-3 rounded-lg">
                          <h4 className="text-lg font-medium text-gray-900 mb-3">Contract Information</h4>
                          <div className="space-y-2">
                            <div className="flex items-center gap-3">
                              <Calendar className="h-5 w-5 text-gray-400" />
                              <div>
                                <p className="text-sm text-gray-600">Start: {vendor.contract.startDate}</p>
                                <p className="text-sm text-gray-600">End: {vendor.contract.expiryDate}</p>
                              </div>
                            </div>
                            <div className="flex items-center gap-3">
                              <Clock className="h-5 w-5 text-gray-400" />
                              <p className="text-sm text-gray-600">
                                {vendor.contract.terminationNotice} days notice required
                              </p>
                            </div>
                            <div className="flex items-center gap-3">
                              <AlertCircle className="h-5 w-5 text-gray-400" />
                              <p className="text-sm text-gray-600">
                                {vendor.contract.renewalReminder ? 'Renewal reminder enabled' : 'No renewal reminder'}
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Performance & Financial */}
                      <div className="space-y-4">
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <h4 className="text-lg font-medium text-gray-900 mb-3">Performance Metrics</h4>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-gray-600">Rating</span>
                              <span className="text-sm font-medium">{vendor.performance.rating}/5</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-gray-600">Response Time</span>
                              <span className="text-sm font-medium">{vendor.performance.responseTime}h</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-gray-600">Delivery Score</span>
                              <span className="text-sm font-medium">{vendor.performance.deliveryScore}%</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-gray-600">Quality Score</span>
                              <span className="text-sm font-medium">{vendor.performance.qualityScore}%</span>
                            </div>
                          </div>
                        </div>

                        <div className="bg-gray-50 p-3 rounded-lg">
                          <h4 className="text-lg font-medium text-gray-900 mb-3">Financial Overview</h4>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-gray-600">Current Spending</span>
                              <span className="text-sm font-medium">
                                {vendor.financial.spending.current.currency} {vendor.financial.spending.current.value.toLocaleString()}
                              </span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-sm text-gray-600">Spending Trend</span>
                              <span className={`text-sm font-medium ${vendor.financial.spending.trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {vendor.financial.spending.trend > 0 ? '+' : ''}{vendor.financial.spending.trend}%
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* CRUD Actions */}
                      <div className="lg:col-span-3 mt-4 flex justify-end space-x-3">
                        <button 
                          onClick={(e) => {
                            e.stopPropagation();
                            handleEditVendor(vendor);
                          }}
                          className="inline-flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                        >
                          <Settings className="h-5 w-5 mr-1" />
                          Edit
                        </button>
                        <button 
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteVendor(vendor.id);
                          }}
                          className="inline-flex items-center px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                        >
                          <Trash2 className="h-5 w-5 mr-1" />
                          Delete
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {showAddForm && (
          <VendorForm
            onClose={handleFormClose}
            onSubmit={handleFormSubmit}
            vendors={vendors}
            vendor={editingVendor}
          />
        )}
      </div>
    </div>
  );
}

export default VendorContacts;