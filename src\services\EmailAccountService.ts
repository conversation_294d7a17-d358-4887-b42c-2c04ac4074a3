import api, { safelyHandleResponse } from './api';

export interface EmailAccountData {
  emailAddress: string;
  assignedToType: 'User' | 'Department';
  assignedToName: string;
  assignedToId?: string;
  designation?: string;
  employmentStatus?: string;
  department: string;
  project?: string;
  location?: string;
  accountType: string;
  platform: string;
  status: string;
  loginUrl?: string;
  hostingProvider?: string;
  recoveryEmail?: string;
  recoveryPhone?: string;
  twoFactorEnabled: boolean;
  primaryUser: string;
  createdBy: string;
  notes?: string;
  subscriptionType?: string;
  software?: string;
  asset?: string;
  password?: string;
  secretQuestion?: string;
  secretAnswer?: string;
  creationDate?: Date | string;
  lastAccessDate?: Date | string;
  ownershipChangeLog?: string;
  passwordAge?: number;
  ticketId?: string;
  licenseRecord?: string;
  userOffboardingProcess?: string;
}

// Connect to the real backend API endpoints
class EmailAccountService {
  private apiEndpoint = '/email-accounts';

  // Get all email accounts
  async getAllAccounts() {
    console.log('Fetching all email accounts from API');
    try {
      const response = await safelyHandleResponse(api.get(this.apiEndpoint));
      console.log('Email accounts API response:', response);
      return response;
    } catch (error) {
      console.error('Error fetching email accounts:', error);
      return { data: [], error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  // Create a new email account
  async createAccount(accountData: EmailAccountData) {
    console.log('Creating email account via API:', accountData);
    // Convert date strings to Date objects if they exist
    const processedData = { ...accountData };
    
    // Clean up date fields
    if (typeof processedData.creationDate === 'string' && processedData.creationDate) {
      // If it's a valid date string, convert to Date
      const parsedDate = new Date(processedData.creationDate);
      if (!isNaN(parsedDate.getTime())) {
        processedData.creationDate = parsedDate;
      } else {
        // If invalid date string, remove the field
        delete processedData.creationDate;
      }
    } else if (processedData.creationDate === '') {
      // Remove empty string dates
      delete processedData.creationDate;
    }
    
    if (typeof processedData.lastAccessDate === 'string' && processedData.lastAccessDate) {
      // If it's a valid date string, convert to Date
      const parsedDate = new Date(processedData.lastAccessDate);
      if (!isNaN(parsedDate.getTime())) {
        processedData.lastAccessDate = parsedDate;
      } else {
        // If invalid date string, remove the field
        delete processedData.lastAccessDate;
      }
    } else if (processedData.lastAccessDate === '') {
      // Remove empty string dates
      delete processedData.lastAccessDate;
    }
    
    return safelyHandleResponse(api.post(this.apiEndpoint, processedData));
  }

  // Update an existing email account
  async updateAccount(id: string, accountData: EmailAccountData) {
    console.log(`Updating email account ${id} via API:`, accountData);
    
    // Convert date strings to Date objects if they exist
    const processedData = { ...accountData };
    
    // Clean up date fields
    if (typeof processedData.creationDate === 'string' && processedData.creationDate) {
      // If it's a valid date string, convert to Date
      const parsedDate = new Date(processedData.creationDate);
      if (!isNaN(parsedDate.getTime())) {
        processedData.creationDate = parsedDate;
      } else {
        // If invalid date string, remove the field
        delete processedData.creationDate;
      }
    } else if (processedData.creationDate === '') {
      // Remove empty string dates
      delete processedData.creationDate;
    }
    
    if (typeof processedData.lastAccessDate === 'string' && processedData.lastAccessDate) {
      // If it's a valid date string, convert to Date
      const parsedDate = new Date(processedData.lastAccessDate);
      if (!isNaN(parsedDate.getTime())) {
        processedData.lastAccessDate = parsedDate;
      } else {
        // If invalid date string, remove the field
        delete processedData.lastAccessDate;
      }
    } else if (processedData.lastAccessDate === '') {
      // Remove empty string dates
      delete processedData.lastAccessDate;
    }
    
    return safelyHandleResponse(api.put(`${this.apiEndpoint}/${id}`, processedData));
  }

  // Delete an email account
  async deleteAccount(id: string) {
    console.log(`Deleting email account ${id} via API`);
    return safelyHandleResponse(api.delete(`${this.apiEndpoint}/${id}`));
  }

  // Get a single email account by ID
  async getAccountById(id: string) {
    console.log(`Fetching email account ${id} from API`);
    return safelyHandleResponse(api.get(`${this.apiEndpoint}/${id}`));
  }
}

export default new EmailAccountService(); 