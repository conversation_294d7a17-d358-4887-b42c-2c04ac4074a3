import { Request, Response } from 'express';
import { getRepository } from 'typeorm';
import { RoleTemplate, TemplateCategory } from '../entities/RoleTemplate';
import { AppDataSource } from '../config/database';

// Get all role templates
export const getRoleTemplates = async (req: Request, res: Response) => {
  try {
    // Check if database is connected
    if (!AppDataSource.isInitialized) {
      console.error('Database connection not initialized');
      // Return fallback templates
      return res.json({ 
        templates: getDefaultTemplates() 
      });
    }
    
    try {
      const templateRepository = AppDataSource.getRepository(RoleTemplate);
      const templates = await templateRepository.find();
      return res.json({ templates });
    } catch (dbError: any) {
      console.error('Database query error:', dbError);
      // Return fallback templates
      return res.json({ 
        templates: getDefaultTemplates() 
      });
    }
  } catch (error: any) {
    console.error('Error fetching role templates:', error);
    // Return fallback templates
    return res.json({ 
      templates: getDefaultTemplates() 
    });
  }
};

// Helper function to get default templates
function getDefaultTemplates() {
  return [
    {
      id: 'template-it-admin',
      name: 'IT Administrator',
      description: 'Complete system access with all permissions',
      permissions: [
        'canAccessAllModules', 'canConfigureSystem', 'canManageRoles', 
        'canAddUsers', 'canEditUsers', 'canDeleteUsers', 'canViewReports'
      ],
      category: 'predefined',
      iconName: 'Shield',
      iconColor: 'text-red-600'
    },
    {
      id: 'template-helpdesk',
      name: 'Help Desk Agent',
      description: 'Permissions for help desk staff',
      permissions: [
        'canCreateTickets', 'canEditTickets', 'canCloseTickets', 
        'canViewAllTickets', 'canAssignTickets'
      ],
      category: 'predefined',
      iconName: 'Users',
      iconColor: 'text-blue-600'
    },
    {
      id: 'template-dashboard',
      name: 'Dashboard User',
      description: 'Access to view and interact with dashboards',
      permissions: [
        'canViewDashboards', 'canExportDashboardData'
      ],
      category: 'predefined',
      iconName: 'BarChart2',
      iconColor: 'text-purple-600'
    }
  ];
}

// Get role template by ID
export const getRoleTemplateById = async (req: Request, res: Response) => {
  try {
    const templateRepository = getRepository(RoleTemplate);
    const template = await templateRepository.findOne({ where: { id: req.params.id } });
    
    if (!template) {
      return res.status(404).json({ message: 'Role template not found' });
    }
    
    return res.json(template);
  } catch (error) {
    console.error('Error fetching role template:', error);
    return res.status(500).json({ message: 'Server error while fetching role template' });
  }
};

// Create a new role template
export const createRoleTemplate = async (req: Request, res: Response) => {
  try {
    const templateRepository = getRepository(RoleTemplate);
    const { name, description, permissions, category, iconName, iconColor } = req.body;
    
    // Check if template with same name already exists
    const existingTemplate = await templateRepository.findOne({ where: { name } });
    if (existingTemplate) {
      return res.status(400).json({ message: 'Template with this name already exists' });
    }
    
    const template = new RoleTemplate();
    template.name = name;
    template.description = description;
    template.permissions = permissions || [];
    template.category = category || TemplateCategory.CUSTOM;
    template.iconName = iconName;
    template.iconColor = iconColor;
    template.createdBy = req.user?.id || '';
    
    const savedTemplate = await templateRepository.save(template);
    
    return res.status(201).json(savedTemplate);
  } catch (error) {
    console.error('Error creating role template:', error);
    return res.status(500).json({ message: 'Server error while creating role template' });
  }
};

// Update an existing role template
export const updateRoleTemplate = async (req: Request, res: Response) => {
  try {
    const templateRepository = getRepository(RoleTemplate);
    const { name, description, permissions, category, iconName, iconColor } = req.body;
    
    const template = await templateRepository.findOne({ where: { id: req.params.id } });
    if (!template) {
      return res.status(404).json({ message: 'Role template not found' });
    }
    
    // Only allow updating custom templates, not predefined ones
    if (template.category === TemplateCategory.PREDEFINED) {
      return res.status(403).json({ message: 'Predefined templates cannot be modified' });
    }
    
    // Check for name conflict only if name is being changed
    if (name !== template.name) {
      const existingTemplate = await templateRepository.findOne({ where: { name } });
      if (existingTemplate) {
        return res.status(400).json({ message: 'Template with this name already exists' });
      }
    }
    
    template.name = name || template.name;
    template.description = description || template.description;
    template.permissions = permissions || template.permissions;
    template.iconName = iconName || template.iconName;
    template.iconColor = iconColor || template.iconColor;
    
    const updatedTemplate = await templateRepository.save(template);
    
    return res.json(updatedTemplate);
  } catch (error) {
    console.error('Error updating role template:', error);
    return res.status(500).json({ message: 'Server error while updating role template' });
  }
};

// Delete a role template
export const deleteRoleTemplate = async (req: Request, res: Response) => {
  try {
    const templateRepository = getRepository(RoleTemplate);
    
    const template = await templateRepository.findOne({ where: { id: req.params.id } });
    if (!template) {
      return res.status(404).json({ message: 'Role template not found' });
    }
    
    // Only allow deleting custom templates, not predefined ones
    if (template.category === TemplateCategory.PREDEFINED) {
      return res.status(403).json({ message: 'Predefined templates cannot be deleted' });
    }
    
    await templateRepository.remove(template);
    
    return res.json({ message: 'Role template deleted successfully' });
  } catch (error) {
    console.error('Error deleting role template:', error);
    return res.status(500).json({ message: 'Server error while deleting role template' });
  }
}; 