import React, { useEffect, useState } from 'react';
import ReactDOM from 'react-dom';

interface PortalProps {
  children: React.ReactNode;
}

const Portal: React.FC<PortalProps> = ({ children }) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Check if we're in the browser environment
  if (typeof window === 'undefined') return null;

  // Return null on first render, this avoids SSR issues
  if (!mounted) return null;

  // Create a div that we'll render the modal into
  return ReactDOM.createPortal(
    children,
    document.body
  );
};

export default Portal; 