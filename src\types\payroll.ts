export enum PayrollStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  PROCESSING = 'processing',
  APPROVED = 'approved',
  COMPLETED = 'completed',
  REJECTED = 'rejected',
  ON_HOLD = 'on_hold'
}

export enum PaymentMethod {
  BANK_TRANSFER = 'bank_transfer',
  CASH = 'cash',
  CHECK = 'check',
  MOBILE_PAYMENT = 'mobile_payment',
  SPLIT_PAYMENT = 'split_payment'
}

export enum PayrollFrequency {
  MONTHLY = 'monthly',
  BIWEEKLY = 'biweekly',
  WEEKLY = 'weekly',
  DAILY = 'daily',
  CUSTOM = 'custom'
}

export enum TaxCalculationType {
  FIXED = 'fixed',
  PERCENTAGE = 'percentage', 
  SLAB_BASED = 'slab_based',
  EXEMPT = 'exempt'
}

export enum TaxPeriod {
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  SEMI_ANNUAL = 'semi_annual',
  ANNUAL = 'annual'
}

export enum PayrollCurrency {
  USD = 'USD',
  EUR = 'EUR',
  GBP = 'GBP',
  PKR = 'PKR',
  JPY = 'JPY',
  CAD = 'CAD',
  AUD = 'AUD',
  INR = 'INR'
}

export interface PayrollPeriod {
  id: number;
  name: string;
  startDate: string;
  endDate: string;
  paymentDate: string;
  frequency: PayrollFrequency;
  status: PayrollStatus;
  totalEmployees?: number;
  totalAmount?: number;
  currency?: PayrollCurrency;
  exchangeRate?: number;
  notes?: string;
  createdAt?: string;
  createdBy?: number;
  updatedAt?: string;
  updatedBy?: number;
}

export interface SalaryComponent {
  id: number;
  name: string;
  type: 'earning' | 'deduction' | 'tax' | 'contribution';
  calculationType: 'fixed' | 'percentage' | 'formula';
  value: number;
  formula?: string;
  taxable: boolean;
  description?: string;
  isDefault: boolean;
  isActive: boolean;
  currency?: PayrollCurrency;
  applicableCountries?: string[];
}

export interface TaxSlab {
  id: number;
  minAmount: number;
  maxAmount: number | null;
  rate: number;
  additionalAmount: number;
  description?: string;
}

export interface PayrollEmployee {
  id: number;
  employeeId: string;
  firstName: string;
  lastName: string;
  department: string;
  designation: string;
  joinDate: string;
  baseSalary: number;
  currency?: PayrollCurrency;
  status: 'active' | 'on_leave' | 'terminated' | 'suspended' | string;
  paymentMethod: PaymentMethod | string;
  bankName?: string;
  accountNumber?: string;
  taxIdentificationNumber?: string;
  shiftType?: string;
  overtimeEligible?: boolean;
}

export interface PayrollEmployeeEarning {
  id?: number;
  employeeId: number;
  payrollId: number;
  componentId: number;
  componentName: string;
  amount: number;
  calculationType: 'fixed' | 'percentage' | 'formula' | 'manual';
  calculationBase?: number;
  remarks?: string;
}

export interface PayrollEmployeeDeduction {
  id?: number;
  employeeId: number;
  payrollId: number;
  componentId: number;
  componentName: string;
  amount: number;
  calculationType: 'fixed' | 'percentage' | 'formula' | 'manual';
  calculationBase?: number;
  remarks?: string;
}

export interface PayrollTax {
  id?: number;
  employeeId: number;
  payrollId: number;
  taxName: string;
  taxAmount: number;
  calculationType: TaxCalculationType;
  calculationBase?: number;
  remarks?: string;
}

export interface EmployeePayrollEntry {
  id?: number;
  payrollPeriodId: number;
  employeeId: number;
  employeeName: string;
  department: string;
  designation: string;
  baseSalary: number;
  grossSalary: number;
  totalEarnings: number;
  totalDeductions: number;
  totalTax: number;
  netSalary: number;
  paymentMethod: PaymentMethod | string;
  paymentStatus: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | string;
  paymentDate?: string;
  paymentReference?: string;
  bankName?: string;
  accountNumber?: string;
  currency?: PayrollCurrency;
  remarks?: string;
  earnings: PayrollEmployeeEarning[];
  deductions: PayrollEmployeeDeduction[];
  taxes: PayrollTax[];
  benefits?: EmployeeBenefitPayment[];
  expenseReimbursements?: EmployeeExpenseReimbursement[];
  attendance?: {
    totalDays: number;
    presentDays: number;
    absentDays: number;
    leaveDays: number;
    holidayDays: number;
    workingDays: number;
    overtimeHours?: number;
    lateDays?: number;
  };
  processedBy?: string;
  processedDate?: string;
  comments?: string;
  exchangeRate?: number;
}

export interface PayrollSettings {
  payrollFrequency: PayrollFrequency;
  payrollStartDay: number;
  payrollEndDay: number;
  paymentDay: number;
  defaultPaymentMethod: PaymentMethod;
  taxSettings: {
    enableTaxCalculation: boolean;
    taxCalculationType: TaxCalculationType;
    standardTaxRate?: number;
    taxSlabs?: TaxSlab[];
  };
  allowanceSettings: {
    housingAllowancePercentage: number;
    transportAllowancePercentage: number;
    medicalAllowancePercentage: number;
    otherAllowancePercentage: number;
  };
  deductionSettings: {
    providentFundPercentage: number;
    insurancePercentage: number;
    enableProvidentFund: boolean;
    enableInsurance: boolean;
  };
  approvalSettings: {
    requireApproval: boolean;
    approvalWorkflow: 'single' | 'multi';
    approvers: number[];
  };
  emailSettings: {
    sendPayslipEmails: boolean;
    emailTemplate: string;
    ccEmails?: string[];
  };
}

export interface PayrollReport {
  id: number;
  title: string;
  period: {
    startDate: string;
    endDate: string;
  };
  generatedBy: {
    id: number;
    name: string;
  };
  generatedAt: string;
  format: 'pdf' | 'excel' | 'csv';
  departments?: string[];
  reportType: 'summary' | 'detailed' | 'tax' | 'custom';
  url?: string;
  totalEmployees: number;
  totalSalaryPaid: number;
  totalTaxPaid: number;
  totalDeductions: number;
}

export interface PayslipTemplate {
  id: number;
  name: string;
  isDefault: boolean;
  content: string;
  variables: string[];
  showCompanyLogo: boolean;
  logoPosition: 'left' | 'center' | 'right';
  headerColor: string;
  footerText?: string;
}

export enum LoanStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  DEFAULTED = 'defaulted',
  CANCELED = 'canceled'
}

export enum AdvanceStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  PAID = 'paid',
  RECOVERED = 'recovered',
  CANCELED = 'canceled'
}

export enum LoanType {
  PERSONAL = 'personal',
  EDUCATION = 'education',
  HOUSING = 'housing',
  MEDICAL = 'medical',
  VEHICLE = 'vehicle',
  EMERGENCY = 'emergency',
  OTHER = 'other'
}

export interface Loan {
  id: number;
  employeeId: number;
  employeeName: string;
  loanType: LoanType;
  amount: number;
  interestRate: number;
  termMonths: number;
  startDate: string;
  endDate: string;
  installmentAmount: number;
  remainingAmount: number;
  totalPaid: number;
  totalInterestPaid: number;
  status: LoanStatus;
  purpose?: string;
  approvedBy?: number;
  approvedDate?: string;
  rejectionReason?: string;
  attachments?: string[];
  createdAt: string;
  updatedAt?: string;
  notes?: string;
}

export interface LoanInstallment {
  id: number;
  loanId: number;
  payrollPeriodId: number;
  paymentDate: string;
  amount: number;
  interestAmount: number;
  principalAmount: number;
  remainingBalance: number;
  status: 'pending' | 'paid' | 'missed' | 'partial';
  notes?: string;
}

export interface SalaryAdvance {
  id: number;
  employeeId: number;
  employeeName: string;
  amount: number;
  requestDate: string;
  approvalDate?: string;
  paymentDate?: string;
  recoveryDate?: string;
  recoveryMethod: 'one_time' | 'installments';
  recoveryMonths?: number;
  installmentAmount?: number;
  recoveredAmount: number;
  remainingAmount: number;
  status: AdvanceStatus;
  purpose?: string;
  approvedBy?: number;
  rejectionReason?: string;
  attachments?: string[];
  payrollPeriodId?: number;
  createdAt: string;
  updatedAt?: string;
  notes?: string;
}

export interface AdvanceInstallment {
  id: number;
  advanceId: number;
  payrollPeriodId: number;
  recoveryDate: string;
  amount: number;
  remainingBalance: number;
  status: 'pending' | 'recovered' | 'partial';
  notes?: string;
}

export interface TaxConfig {
  id: number;
  name: string;
  description?: string;
  effectiveDate: string;
  expiryDate?: string;
  isActive: boolean;
  country: string;
  region?: string;
  taxType: 'income' | 'sales' | 'property' | 'other';
  taxSlabs: TaxSlab[];
  exemptions?: TaxExemption[];
  deductions?: TaxDeduction[];
  createdAt: string;
  updatedAt?: string;
}

export interface TaxExemption {
  id: number;
  name: string;
  description?: string;
  maxAmount?: number;
  applicableFor?: string[];
  documentRequired: boolean;
  isActive: boolean;
}

export interface TaxDeduction {
  id: number;
  name: string;
  description?: string;
  maxAmount?: number;
  percentageLimit?: number;
  applicableFor?: string[];
  documentRequired: boolean;
  isActive: boolean;
}

export interface EmployeeTaxInfo {
  id: number;
  employeeId: number;
  employeeName: string;
  taxIdentificationNumber: string;
  taxFilingStatus: 'single' | 'married' | 'head_of_household' | 'other';
  additionalWithholding?: number;
  exemptions?: {
    exemptionId: number;
    exemptionName: string;
    amount: number;
    documentReference?: string;
  }[];
  deductions?: {
    deductionId: number;
    deductionName: string;
    amount: number;
    documentReference?: string;
  }[];
  customTaxRate?: number;
  isExemptFromTax: boolean;
  effectiveDate: string;
  expiryDate?: string;
  notes?: string;
}

export interface TaxStatement {
  id: number;
  employeeId: number;
  employeeName: string;
  taxYear: number;
  statementType: 'W2' | 'Form16' | 'T4' | 'other';
  generatedDate: string;
  totalEarnings: number;
  taxableEarnings: number;
  totalTaxWithheld: number;
  federalTax: number;
  stateTax?: number;
  socialSecurity?: number;
  medicare?: number;
  otherTaxes?: {
    name: string;
    amount: number;
  }[];
  status: 'draft' | 'final' | 'amended';
  documentUrl?: string;
  sentToEmployee: boolean;
  sentDate?: string;
}

export interface TaxComplianceReport {
  id: number;
  reportType: 'monthly' | 'quarterly' | 'annual';
  period: {
    startDate: string;
    endDate: string;
  };
  dueDate: string;
  submissionDate?: string;
  totalTaxableAmount: number;
  totalTaxCollected: number;
  status: 'pending' | 'submitted' | 'accepted' | 'rejected';
  filingReference?: string;
  notes?: string;
  generatedBy?: number;
  documentUrl?: string;
}

export interface TaxBracket {
  id: number;
  minAmount: number;
  maxAmount: number;
  rate: number;
  fixedAmount?: number;
  currency: PayrollCurrency;
}

export interface TaxConfiguration {
  id: number;
  name: string;
  country: string;
  description: string;
  effectiveDate: string;
  expiryDate?: string;
  isActive: boolean;
  brackets: TaxBracket[];
  standardDeduction?: number;
  personalAllowance?: number;
}

export interface LoanApplication {
  id: number;
  employeeId: number;
  employeeName: string;
  loanType: string;
  amount: number;
  currency: PayrollCurrency;
  interestRate: number;
  term: number;
  startDate: string;
  endDate: string;
  installmentAmount: number;
  remainingAmount: number;
  status: LoanStatus;
  approvedBy?: string;
  approvalDate?: string;
  purpose?: string;
}

// Expense Reimbursement Types
export enum ExpenseType {
  TRAVEL = 'travel',
  ACCOMMODATION = 'accommodation',
  MEALS = 'meals',
  OFFICE_SUPPLIES = 'office_supplies',
  COMMUNICATION = 'communication',
  TRANSPORTATION = 'transportation',
  TRAINING = 'training',
  MEDICAL = 'medical',
  OTHER = 'other'
}

export enum ExpenseStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  IN_REVIEW = 'in_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  REIMBURSED = 'reimbursed',
  PARTIALLY_REIMBURSED = 'partially_reimbursed',
  CANCELLED = 'cancelled'
}

export enum BenefitType {
  HEALTH_INSURANCE = 'health_insurance',
  LIFE_INSURANCE = 'life_insurance',
  DENTAL_INSURANCE = 'dental_insurance',
  VISION_INSURANCE = 'vision_insurance',
  RETIREMENT_PLAN = 'retirement_plan',
  PENSION = 'pension',
  EDUCATION_ASSISTANCE = 'education_assistance',
  CHILD_CARE = 'child_care',
  WELLNESS_PROGRAM = 'wellness_program',
  OTHER = 'other'
}

export enum BenefitCalculationType {
  FIXED = 'fixed',
  PERCENTAGE = 'percentage',
  TIERED = 'tiered',
  AGE_BASED = 'age_based',
  SALARY_BASED = 'salary_based'
}

export interface ExpenseItem {
  id: number;
  expenseReportId: number;
  expenseType: ExpenseType;
  description: string;
  amount: number;
  currency: PayrollCurrency;
  expenseDate: string;
  receipt?: string;
  notes?: string;
  isApproved?: boolean;
  rejectionReason?: string;
}

export interface ExpenseReport {
  id: number;
  employeeId: number;
  employeeName: string;
  title: string;
  description?: string;
  submissionDate: string;
  totalAmount: number;
  currency: PayrollCurrency;
  status: ExpenseStatus;
  items: ExpenseItem[];
  approvedBy?: number;
  approvedDate?: string;
  rejectionReason?: string;
  payrollPeriodId?: number;
  isIncludedInPayroll: boolean;
  reimbursementDate?: string;
  reimbursementMethod?: PaymentMethod;
  createdAt: string;
  updatedAt?: string;
}

export interface BenefitPlan {
  id: number;
  name: string;
  benefitType: BenefitType;
  description: string;
  provider: string;
  planNumber?: string;
  startDate: string;
  endDate?: string;
  calculationType: BenefitCalculationType;
  employerContribution: number; // fixed amount or percentage
  employeeContribution: number; // fixed amount or percentage
  isActive: boolean;
  currency: PayrollCurrency;
  calculationBase?: 'gross_salary' | 'base_salary' | 'fixed';
  eligibilityCriteria?: {
    minServiceMonths?: number;
    employmentTypes?: string[];
    departments?: string[];
    minimumAge?: number;
  };
  documents?: string[];
  createdAt: string;
  updatedAt?: string;
}

export interface EmployeeBenefit {
  id: number;
  employeeId: number;
  employeeName: string;
  benefitPlanId: number;
  benefitPlanName: string;
  enrollmentDate: string;
  effectiveDate: string;
  terminationDate?: string;
  coverageLevel?: 'employee_only' | 'employee_spouse' | 'employee_family' | 'custom';
  dependents?: number;
  employerContribution: number;
  employeeContribution: number;
  additionalCoverage?: number;
  beneficiaries?: {
    name: string;
    relationship: string;
    percentage: number;
  }[];
  status: 'active' | 'inactive' | 'pending';
  notes?: string;
  documents?: string[];
}

export interface EmployeeBenefitPayment {
  id: number;
  employeeId: number;
  benefitPlanId: number;
  benefitPlanName: string;
  payrollPeriodId: number;
  employerContribution: number;
  employeeContribution: number;
  paymentDate: string;
  currency: PayrollCurrency;
  notes?: string;
}

export interface EmployeeExpenseReimbursement {
  id: number;
  employeeId: number;
  expenseReportId: number;
  expenseReportTitle: string;
  payrollPeriodId: number;
  amount: number;
  currency: PayrollCurrency;
  reimbursementDate: string;
  notes?: string;
} 