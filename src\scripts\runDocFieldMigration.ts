import { AppDataSource } from '../config/database';
import { RemoveDocumentFields1700000000018 } from '../server/migrations/1700000000018-RemoveDocumentFields';

async function runDocumentFieldsMigration() {
  console.log('Initializing data source...');
  
  try {
    await AppDataSource.initialize();
    console.log('Data source initialized');
    
    console.log('Executing document fields migration directly...');
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    
    // Start transaction
    await queryRunner.startTransaction();
    
    try {
      const migration = new RemoveDocumentFields1700000000018();
      console.log('Running migration up method...');
      await migration.up(queryRunner);
      
      // Commit transaction
      await queryRunner.commitTransaction();
      console.log('Migration completed successfully!');
    } catch (error) {
      // Rollback transaction in case of error
      await queryRunner.rollbackTransaction();
      console.error('Migration failed, transaction rolled back:', error);
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }
    
    await AppDataSource.destroy();
    console.log('Connection closed');
    
    process.exit(0);
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  }
}

runDocumentFieldsMigration(); 