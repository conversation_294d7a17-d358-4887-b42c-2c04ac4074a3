import React, { useState, useEffect } from 'react';
import { getCompanyProfile, updateCompanyProfile } from '../services/companyService';
import { toast } from 'react-hot-toast';

interface CompanyProfile {
  companyName: string;
  companyLogo: File | null | string;
  companyEmail: string;
  companyPhone: string;
  companyAddress: string;
  mainLocationName: string;
  subLocations: string[];
  departments: string[];
  projects: string[];
  adminUser: {
    fullName: string;
    email: string;
  };
  advancedSettings: {
    fiscalStartMonth: string;
    timezone: string;
    currency: string;
    defaultLanguage: string;
  };
}

interface CompanyProfileEditProps {
  onCancel: () => void;
  onSave: () => void;
}

const CompanyProfileEdit: React.FC<CompanyProfileEditProps> = ({ onCancel, onSave }) => {
  const [companyData, setCompanyData] = useState<CompanyProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [newLogo, setNewLogo] = useState<File | null>(null);
  const [previewLogo, setPreviewLogo] = useState<string | null>(null);
  
  // Form state for arrays
  const [newLocation, setNewLocation] = useState('');
  const [newDepartment, setNewDepartment] = useState('');
  const [newProject, setNewProject] = useState('');

  useEffect(() => {
    const fetchCompanyData = async () => {
      try {
        setLoading(true);
        const data = await getCompanyProfile();
        setCompanyData(data);
        setError(null);
      } catch (err) {
        console.error('Failed to fetch company data:', err);
        setError('Failed to load company profile. Please try again later.');
        toast.error('Could not load company profile');
      } finally {
        setLoading(false);
      }
    };

    fetchCompanyData();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    if (!companyData) return;
    
    // Handle nested properties
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setCompanyData({
        ...companyData,
        [parent]: {
          ...companyData[parent as keyof CompanyProfile],
          [child]: value
        }
      });
    } else {
      setCompanyData({
        ...companyData,
        [name]: value
      });
    }
  };

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setNewLogo(file);
      
      // Create preview URL
      const reader = new FileReader();
      reader.onload = () => {
        setPreviewLogo(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAddLocation = () => {
    if (!newLocation.trim() || !companyData) return;
    
    setCompanyData({
      ...companyData,
      subLocations: [...companyData.subLocations, newLocation.trim()]
    });
    setNewLocation('');
  };

  const handleRemoveLocation = (index: number) => {
    if (!companyData) return;
    
    const updatedLocations = [...companyData.subLocations];
    updatedLocations.splice(index, 1);
    
    setCompanyData({
      ...companyData,
      subLocations: updatedLocations
    });
  };

  const handleAddDepartment = () => {
    if (!newDepartment.trim() || !companyData) return;
    
    setCompanyData({
      ...companyData,
      departments: [...companyData.departments, newDepartment.trim()]
    });
    setNewDepartment('');
  };

  const handleRemoveDepartment = (index: number) => {
    if (!companyData) return;
    
    const updatedDepartments = [...companyData.departments];
    updatedDepartments.splice(index, 1);
    
    setCompanyData({
      ...companyData,
      departments: updatedDepartments
    });
  };

  const handleAddProject = () => {
    if (!newProject.trim() || !companyData) return;
    
    setCompanyData({
      ...companyData,
      projects: [...companyData.projects, newProject.trim()]
    });
    setNewProject('');
  };

  const handleRemoveProject = (index: number) => {
    if (!companyData) return;
    
    const updatedProjects = [...companyData.projects];
    updatedProjects.splice(index, 1);
    
    setCompanyData({
      ...companyData,
      projects: updatedProjects
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!companyData) return;
    
    try {
      setSaving(true);
      
      // Prepare data for submission
      const updatedData: any = {
        ...companyData
      };
      
      // Only add logo if a new one was selected
      if (newLogo) {
        updatedData.companyLogo = newLogo;
      } else {
        // Remove the logo property to avoid sending the URL string which API can't handle
        delete updatedData.companyLogo;
      }
      
      await updateCompanyProfile(updatedData);
      toast.success('Company profile updated successfully');
      onSave();
    } catch (err) {
      console.error('Error updating company profile:', err);
      toast.error('Failed to update company profile');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error || !companyData) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
        {error || 'No company profile data found.'}
      </div>
    );
  }

  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CNY', 'INR', 'PKR', 'AUD', 'CAD'];
  
  const languages = ['English', 'Spanish', 'French', 'German', 'Chinese', 'Japanese', 'Arabic', 'Urdu', 'Hindi'];
  
  const timezones = [
    'UTC', 'UTC+1', 'UTC+2', 'UTC+3', 'UTC+4', 'UTC+5', 'UTC+5:30', 'UTC+6', 'UTC+7', 
    'UTC+8', 'UTC+9', 'UTC+10', 'UTC+11', 'UTC+12', 'UTC-1', 'UTC-2', 'UTC-3', 'UTC-4', 
    'UTC-5', 'UTC-6', 'UTC-7', 'UTC-8', 'UTC-9', 'UTC-10', 'UTC-11', 'UTC-12'
  ];

  return (
    <div className="bg-white shadow-md rounded-lg overflow-hidden">
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-6">
        <h1 className="text-2xl font-bold mb-2">Edit Company Profile</h1>
        <p>Update your organization's information</p>
      </div>

      <form onSubmit={handleSubmit} className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Company Information */}
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-4">Company Information</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Company Name*
                </label>
                <input
                  type="text"
                  name="companyName"
                  value={companyData.companyName}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Company Logo
                </label>
                <div className="flex items-center space-x-4">
                  {(previewLogo || companyData.companyLogo) && (
                    <div className="h-16 w-16 overflow-hidden bg-gray-100 border rounded-md">
                      <img 
                        src={previewLogo || (typeof companyData.companyLogo === 'string' ? companyData.companyLogo : '')} 
                        alt="Company logo preview" 
                        className="h-full w-full object-contain"
                      />
                    </div>
                  )}
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleLogoChange}
                    className="block w-full text-sm text-gray-500
                    file:mr-4 file:py-2 file:px-4
                    file:rounded-md file:border-0
                    file:text-sm file:font-semibold
                    file:bg-blue-50 file:text-blue-700
                    hover:file:bg-blue-100"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Company Email*
                </label>
                <input
                  type="email"
                  name="companyEmail"
                  value={companyData.companyEmail}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Company Phone
                </label>
                <input
                  type="tel"
                  name="companyPhone"
                  value={companyData.companyPhone}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Company Address
                </label>
                <textarea
                  name="companyAddress"
                  value={companyData.companyAddress}
                  onChange={handleChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            
            {/* Locations Section */}
            <div className="mt-6">
              <h3 className="text-md font-medium text-gray-800 mb-3">Locations</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Main Location*
                  </label>
                  <input
                    type="text"
                    name="mainLocationName"
                    value={companyData.mainLocationName}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Additional Locations
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={newLocation}
                      onChange={(e) => setNewLocation(e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Add a location"
                    />
                    <button
                      type="button"
                      onClick={handleAddLocation}
                      className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      Add
                    </button>
                  </div>
                  
                  {companyData.subLocations.length > 0 && (
                    <div className="mt-2 space-y-1">
                      {companyData.subLocations.map((location, index) => (
                        <div key={index} className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded-md">
                          <span>{location}</span>
                          <button
                            type="button"
                            onClick={() => handleRemoveLocation(index)}
                            className="text-red-600 hover:text-red-800"
                          >
                            Remove
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          
          {/* Organization Structure and Settings */}
          <div>
            {/* Departments Section */}
            <div className="mb-6">
              <h3 className="text-md font-medium text-gray-800 mb-3">Departments</h3>
              
              <div>
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={newDepartment}
                    onChange={(e) => setNewDepartment(e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Add a department"
                  />
                  <button
                    type="button"
                    onClick={handleAddDepartment}
                    className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    Add
                  </button>
                </div>
                
                {companyData.departments.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-2">
                    {companyData.departments.map((dept, index) => (
                      <div key={index} className="flex items-center space-x-1 bg-blue-100 px-2 py-1 rounded-full">
                        <span className="text-sm text-blue-800">{dept}</span>
                        <button
                          type="button"
                          onClick={() => handleRemoveDepartment(index)}
                          className="text-blue-600 hover:text-blue-800 text-sm"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
            
            {/* Projects Section */}
            <div className="mb-6">
              <h3 className="text-md font-medium text-gray-800 mb-3">Projects</h3>
              
              <div>
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={newProject}
                    onChange={(e) => setNewProject(e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Add a project"
                  />
                  <button
                    type="button"
                    onClick={handleAddProject}
                    className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    Add
                  </button>
                </div>
                
                {companyData.projects.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-2">
                    {companyData.projects.map((project, index) => (
                      <div key={index} className="flex items-center space-x-1 bg-green-100 px-2 py-1 rounded-full">
                        <span className="text-sm text-green-800">{project}</span>
                        <button
                          type="button"
                          onClick={() => handleRemoveProject(index)}
                          className="text-green-600 hover:text-green-800 text-sm"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
            
            {/* Admin User Section */}
            <div className="mb-6">
              <h3 className="text-md font-medium text-gray-800 mb-3">Administrator</h3>
              
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Full Name
                  </label>
                  <input
                    type="text"
                    name="adminUser.fullName"
                    value={companyData.adminUser.fullName}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    name="adminUser.email"
                    value={companyData.adminUser.email}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>
            
            {/* Advanced Settings Section */}
            <div>
              <h3 className="text-md font-medium text-gray-800 mb-3">Advanced Settings</h3>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Fiscal Start Month
                  </label>
                  <select
                    name="advancedSettings.fiscalStartMonth"
                    value={companyData.advancedSettings.fiscalStartMonth}
                    onChange={(e) => handleChange(e as any)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {months.map(month => (
                      <option key={month} value={month}>{month}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Timezone
                  </label>
                  <select
                    name="advancedSettings.timezone"
                    value={companyData.advancedSettings.timezone}
                    onChange={(e) => handleChange(e as any)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {timezones.map(tz => (
                      <option key={tz} value={tz}>{tz}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Currency
                  </label>
                  <select
                    name="advancedSettings.currency"
                    value={companyData.advancedSettings.currency}
                    onChange={(e) => handleChange(e as any)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {currencies.map(currency => (
                      <option key={currency} value={currency}>{currency}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Default Language
                  </label>
                  <select
                    name="advancedSettings.defaultLanguage"
                    value={companyData.advancedSettings.defaultLanguage}
                    onChange={(e) => handleChange(e as any)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {languages.map(lang => (
                      <option key={lang} value={lang}>{lang}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Form Actions */}
        <div className="mt-8 flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={saving}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-400 disabled:cursor-not-allowed"
          >
            {saving ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CompanyProfileEdit; 