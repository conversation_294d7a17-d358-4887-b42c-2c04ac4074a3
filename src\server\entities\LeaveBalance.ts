import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, CreateDateColumn, UpdateDateColumn, Unique } from 'typeorm';
import { User } from './User';

@Entity('leave_balances')
@Unique(['employeeId', 'leaveType', 'year'])
export class LeaveBalance {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  employeeId: number;

  // Remove the relation for now since we're using Employee entity IDs, not User entity IDs
  // @ManyToOne(() => User, { eager: true })
  // @JoinColumn({ name: 'employeeId' })
  // employee: User;

  @Column({ type: 'varchar', length: 50 })
  leaveType: string; // Dynamic leave type

  @Column({ type: 'int' })
  year: number;

  @Column({ type: 'int', default: 0 })
  totalAllocated: number;

  @Column({ type: 'int', default: 0 })
  used: number;

  @Column({ type: 'int', default: 0 })
  pending: number;

  @Column({ type: 'int', default: 0 })
  carriedForward: number;

  @Column({ type: 'int', default: 0 })
  lapsed: number;

  @Column({ type: 'date', nullable: true })
  expiryDate?: string;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual properties for calculated fields
  get remaining(): number {
    return this.totalAllocated + this.carriedForward - this.used - this.pending;
  }

  get available(): number {
    return Math.max(0, this.remaining);
  }

  get utilizedPercentage(): number {
    const total = this.totalAllocated + this.carriedForward;
    return total > 0 ? (this.used / total) * 100 : 0;
  }

  get isExpired(): boolean {
    if (!this.expiryDate) return false;
    return new Date() > new Date(this.expiryDate);
  }

  // Methods for balance operations
  allocate(days: number, notes?: string): void {
    this.totalAllocated += days;
    if (notes) this.notes = notes;
    this.updatedAt = new Date();
  }

  useLeave(days: number): void {
    if (this.available < days) {
      throw new Error('Insufficient leave balance');
    }
    this.used += days;
    this.updatedAt = new Date();
  }

  addPending(days: number): void {
    this.pending += days;
    this.updatedAt = new Date();
  }

  removePending(days: number): void {
    this.pending = Math.max(0, this.pending - days);
    this.updatedAt = new Date();
  }

  carryForward(days: number): void {
    this.carriedForward += days;
    this.updatedAt = new Date();
  }

  lapseLeave(days: number): void {
    this.lapsed += days;
    this.updatedAt = new Date();
  }
} 