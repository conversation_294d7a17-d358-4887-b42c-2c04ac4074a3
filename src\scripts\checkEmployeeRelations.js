const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkEmployeeRelations() {
  const {
    DB_HOST = 'localhost',
    DB_PORT = '3306',
    DB_USER = 'root',
    DB_PASSWORD = 'root',
    DB_NAME = 'ims_db'
  } = process.env;

  console.log('Connecting to database...');

  try {
    // Create connection
    const connection = await mysql.createConnection({
      host: DB_HOST,
      port: parseInt(DB_PORT, 10),
      user: DB_USER,
      password: DB_PASSWORD,
      database: DB_NAME
    });

    console.log('Connected to database successfully');

    // Check related tables
    const relatedTables = [
      'employee_contacts',
      'employee_jobs',
      'employee_education',
      'employee_experience',
      'employee_family',
      'employee_documents',
      'employee_benefits',
      'employee_devices'
    ];

    console.log('\nChecking related employee tables:');
    
    for (const table of relatedTables) {
      const [result] = await connection.query(
        `SELECT TABLE_NAME FROM information_schema.TABLES 
         WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?`,
        [DB_NAME, table]
      );
      
      console.log(`- ${table}: ${result.length > 0 ? 'EXISTS' : 'DOES NOT EXIST'}`);
      
      if (result.length > 0) {
        // Check if there are any rows in this table related to our employee
        const [rowCount] = await connection.query(
          `SELECT COUNT(*) as count FROM ${table}`
        );
        
        console.log(`  Rows: ${rowCount[0].count}`);
        
        if (rowCount[0].count > 0) {
          // Check if there are any rows linked to employee ID 1
          const [employeeRows] = await connection.query(
            `SELECT COUNT(*) as count FROM ${table} WHERE employee_id = 1`
          );
          
          console.log(`  Rows for employee ID 1: ${employeeRows[0].count}`);
          
          if (employeeRows[0].count > 0) {
            // Get one sample row
            const [sampleRow] = await connection.query(
              `SELECT * FROM ${table} WHERE employee_id = 1 LIMIT 1`
            );
            
            console.log(`  Sample data: ${JSON.stringify(sampleRow[0])}`);
          }
        }
      }
    }

    // Check if there's a direct relation in the employees table
    console.log('\nChecking employees table for missing fields:');
    const expectedFields = [
      'officialEmail', 'personalEmail', 'department', 'designation', 
      'joinDate', 'employmentStatus', 'mobileNumber'
    ];
    
    const [columns] = await connection.query(
      `SELECT COLUMN_NAME FROM information_schema.COLUMNS 
       WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'employees'`,
      [DB_NAME]
    );
    
    const columnNames = columns.map(col => col.COLUMN_NAME);
    
    for (const field of expectedFields) {
      console.log(`- ${field}: ${columnNames.includes(field) ? 'EXISTS' : 'MISSING'}`);
    }

    await connection.end();
    console.log('\nConnection closed');
  } catch (error) {
    console.error('Error:', error);
  }
}

checkEmployeeRelations(); 