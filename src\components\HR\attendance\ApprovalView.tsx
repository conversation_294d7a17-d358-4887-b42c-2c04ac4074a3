import React, { useState } from 'react';
import { 
  <PERSON><PERSON><PERSON>cle, 
  XCircle, 
  Clock, 
  AlertTriangle,
  MessageSquare,
  User,
  Calendar,
  ArrowRight,
  Search
} from 'lucide-react';
import { 
  hrPrimaryButtonStyle, 
  hrSecondaryButtonStyle, 
  hrDangerButtonStyle,
  hrSuccessButtonStyle,
  hrSectionTitleStyle,
  hrSubsectionTitleStyle,
  hrInputStyle,
  hrCardStyle,
  hrSuccessAlertStyle,
  hrInfoAlertStyle
} from '../../../styles/hrWorkflow';

// Sample data for demonstration
const SAMPLE_REQUESTS = [
  {
    id: 1,
    employeeId: 101,
    employeeName: '<PERSON>',
    department: 'Marketing',
    type: 'Missing Clock-in',
    date: '2023-06-15',
    requestedTime: '09:00',
    reason: 'Forgot to clock in due to urgent client meeting',
    status: 'pending',
    requestDate: '2023-06-15T16:30:00',
    managerNotes: ''
  },
  {
    id: 2,
    employeeId: 203,
    employeeName: '<PERSON>',
    department: 'Engineering',
    type: 'Early Departure',
    date: '2023-06-14',
    requestedTime: '16:30',
    reason: 'Family emergency required me to leave early',
    status: 'pending',
    requestDate: '2023-06-14T18:20:00',
    managerNotes: ''
  }
];

const ApprovalView: React.FC = () => {
  const [requests, setRequests] = useState(SAMPLE_REQUESTS);
  const [selectedRequest, setSelectedRequest] = useState<typeof SAMPLE_REQUESTS[0] | null>(null);
  const [managerNotes, setManagerNotes] = useState('');
  const [filter, setFilter] = useState('pending');
  const [searchQuery, setSearchQuery] = useState('');
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Filter requests based on status and search query
  const filteredRequests = requests.filter(request => {
    const matchesFilter = filter === 'all' || request.status === filter;
    const matchesSearch = searchQuery === '' || 
      request.employeeName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.department.toLowerCase().includes(searchQuery.toLowerCase()) ||
      request.type.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesFilter && matchesSearch;
  });

  // Handle request selection
  const handleSelectRequest = (request: typeof SAMPLE_REQUESTS[0]) => {
    setSelectedRequest(request);
    setManagerNotes(request.managerNotes);
  };

  // Handle request approval
  const handleApprove = () => {
    if (!selectedRequest) return;
    
    // Update the request status
    const updatedRequests = requests.map(req => {
      if (req.id === selectedRequest.id) {
        return {
          ...req,
          status: 'approved',
          managerNotes: managerNotes
        };
      }
      return req;
    });
    
    setRequests(updatedRequests);
    setSuccessMessage(`Regularization request for ${selectedRequest.employeeName} has been approved.`);
    setSelectedRequest(null);
    
    // Clear success message after 5 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 5000);
  };

  // Handle request rejection
  const handleReject = () => {
    if (!selectedRequest) return;
    
    // Validate manager notes are provided for rejections
    if (!managerNotes.trim()) {
      alert('Please provide reason for rejection in the manager notes.');
      return;
    }
    
    // Update the request status
    const updatedRequests = requests.map(req => {
      if (req.id === selectedRequest.id) {
        return {
          ...req,
          status: 'rejected',
          managerNotes: managerNotes
        };
      }
      return req;
    });
    
    setRequests(updatedRequests);
    setSuccessMessage(`Regularization request for ${selectedRequest.employeeName} has been rejected.`);
    setSelectedRequest(null);
    
    // Clear success message after 5 seconds
    setTimeout(() => {
      setSuccessMessage(null);
    }, 5000);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  return (
    <div className="p-4">
      <h2 className={hrSectionTitleStyle}>
        <Clock className="h-6 w-6 mr-2 text-blue-500" />
        Attendance Regularization Approvals
      </h2>
      
      {/* Success message */}
      {successMessage && (
        <div className={hrSuccessAlertStyle}>
          <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
          <p>{successMessage}</p>
        </div>
      )}
      
      {/* Info message when no pending requests */}
      {filteredRequests.length === 0 && filter === 'pending' && (
        <div className={hrInfoAlertStyle}>
          <CheckCircle className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5" />
          <p>No pending regularization requests require your attention.</p>
        </div>
      )}
      
      <div className="flex mt-6">
        {/* Requests list */}
        <div className="w-1/2 pr-4">
          {/* Filter and search */}
          <div className="flex justify-between mb-4">
            <div className="flex space-x-2">
              <button
                className={`px-3 py-1 rounded-md text-sm ${
                  filter === 'pending' ? 'bg-blue-100 text-blue-800 font-medium' : 'hover:bg-gray-100'
                }`}
                onClick={() => setFilter('pending')}
              >
                Pending
              </button>
              <button
                className={`px-3 py-1 rounded-md text-sm ${
                  filter === 'approved' ? 'bg-green-100 text-green-800 font-medium' : 'hover:bg-gray-100'
                }`}
                onClick={() => setFilter('approved')}
              >
                Approved
              </button>
              <button
                className={`px-3 py-1 rounded-md text-sm ${
                  filter === 'rejected' ? 'bg-red-100 text-red-800 font-medium' : 'hover:bg-gray-100'
                }`}
                onClick={() => setFilter('rejected')}
              >
                Rejected
              </button>
              <button
                className={`px-3 py-1 rounded-md text-sm ${
                  filter === 'all' ? 'bg-gray-200 text-gray-800 font-medium' : 'hover:bg-gray-100'
                }`}
                onClick={() => setFilter('all')}
              >
                All
              </button>
            </div>
            
            <div className="relative">
              <input
                type="text"
                placeholder="Search requests..."
                className={hrInputStyle + " pl-8"}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <Search className="h-4 w-4 text-gray-400 absolute left-2 top-2.5" />
            </div>
          </div>
          
          {/* Request list */}
          <div className="border rounded-md divide-y overflow-hidden">
            {filteredRequests.length === 0 ? (
              <div className="p-4 text-center text-gray-500">No matching requests found</div>
            ) : (
              filteredRequests.map(request => (
                <button
                  key={request.id}
                  className={`w-full text-left p-4 hover:bg-gray-50 focus:outline-none focus:bg-gray-50 ${
                    selectedRequest?.id === request.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                  }`}
                  onClick={() => handleSelectRequest(request)}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="font-medium text-gray-900">{request.employeeName}</div>
                      <div className="text-sm text-gray-500">{request.department}</div>
                    </div>
                    <div className="flex items-center">
                      {request.status === 'pending' && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          <Clock className="h-3 w-3 mr-1" />
                          Pending
                        </span>
                      )}
                      {request.status === 'approved' && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Approved
                        </span>
                      )}
                      {request.status === 'rejected' && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          <XCircle className="h-3 w-3 mr-1" />
                          Rejected
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="mt-2 flex items-center justify-between">
                    <div>
                      <span className="inline-flex items-center text-xs text-gray-500">
                        <Calendar className="h-3 w-3 mr-1" />
                        {formatDate(request.date)}
                      </span>
                      <span className="mx-2 text-gray-300">|</span>
                      <span className="text-xs text-gray-500">{request.type}</span>
                    </div>
                    <span className="text-xs text-gray-400">{new Date(request.requestDate).toLocaleString()}</span>
                  </div>
                </button>
              ))
            )}
          </div>
        </div>
        
        {/* Request details */}
        <div className="w-1/2 pl-4">
          {selectedRequest ? (
            <div className={hrCardStyle}>
              <h3 className={hrSubsectionTitleStyle}>Request Details</h3>
              
              <div className="mb-6 border-b pb-4">
                <div className="flex items-center mb-2">
                  <User className="h-5 w-5 text-gray-400 mr-2" />
                  <div>
                    <div className="font-medium">{selectedRequest.employeeName}</div>
                    <div className="text-sm text-gray-500">{selectedRequest.department}</div>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 mt-4">
                  <div>
                    <div className="text-sm text-gray-500">Request Type</div>
                    <div className="font-medium">{selectedRequest.type}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Date</div>
                    <div className="font-medium">{formatDate(selectedRequest.date)}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Requested Time</div>
                    <div className="font-medium">{selectedRequest.requestedTime}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Submitted On</div>
                    <div className="font-medium">{new Date(selectedRequest.requestDate).toLocaleString()}</div>
                  </div>
                </div>
              </div>
              
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Employee's Reason</h4>
                <div className="bg-gray-50 p-3 rounded-md text-gray-700 text-sm">
                  {selectedRequest.reason}
                </div>
              </div>
              
              <div className="mb-6">
                <label htmlFor="managerNotes" className="block text-sm font-medium text-gray-700 mb-2">
                  Manager Notes {selectedRequest.status === 'pending' && <span className="text-red-500">*</span>}
                </label>
                <textarea
                  id="managerNotes"
                  rows={3}
                  className={hrInputStyle}
                  placeholder="Add your notes or reason for approval/rejection..."
                  value={managerNotes}
                  onChange={(e) => setManagerNotes(e.target.value)}
                  disabled={selectedRequest.status !== 'pending'}
                />
                {selectedRequest.status !== 'pending' && (
                  <p className="mt-1 text-xs text-gray-500">
                    This request has already been {selectedRequest.status}. Notes cannot be modified.
                  </p>
                )}
              </div>
              
              {selectedRequest.status === 'pending' ? (
                <div className="flex justify-end space-x-3">
                  <button 
                    className={hrDangerButtonStyle}
                    onClick={handleReject}
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Reject Request
                  </button>
                  <button 
                    className={hrSuccessButtonStyle}
                    onClick={handleApprove}
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Approve Request
                  </button>
                </div>
              ) : (
                <div className={`p-3 rounded-md text-sm flex items-start ${
                  selectedRequest.status === 'approved' 
                    ? 'bg-green-50 text-green-800' 
                    : 'bg-red-50 text-red-800'
                }`}>
                  {selectedRequest.status === 'approved' ? (
                    <CheckCircle className="h-4 w-4 mr-2 mt-0.5" />
                  ) : (
                    <XCircle className="h-4 w-4 mr-2 mt-0.5" />
                  )}
                  <div>
                    <p className="font-medium">
                      This request has been {selectedRequest.status}.
                    </p>
                    {selectedRequest.managerNotes && (
                      <p className="mt-1">
                        <span className="font-medium">Manager notes:</span> {selectedRequest.managerNotes}
                      </p>
                    )}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className={hrCardStyle + " text-center py-16 flex flex-col items-center"}>
              <div className="h-20 w-20 rounded-full bg-gray-100 flex items-center justify-center text-gray-400 mb-4">
                <Clock className="h-10 w-10" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-1">No Request Selected</h3>
              <p className="text-gray-500 mb-4">Select a regularization request from the list to view details</p>
              <ArrowRight className="h-5 w-5 text-gray-300 animate-pulse" />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ApprovalView; 