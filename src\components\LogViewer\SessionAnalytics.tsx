import React, { useState, useEffect } from 'react';
import { User, Clock, Shield, AlertTriangle, AlertOctagon, Users, Activity } from 'lucide-react';
import { SystemLog } from './types';
import { extractSessions, getSecurityInsights, SessionInfo, Anomaly } from './utils/securityAnalysis';

interface SessionAnalyticsProps {
  logs: SystemLog[];
  onLogSelect?: (logId: string) => void;
}

export const SessionAnalytics: React.FC<SessionAnalyticsProps> = ({
  logs,
  onLogSelect
}) => {
  const [sessions, setSessions] = useState<SessionInfo[]>([]);
  const [insights, setInsights] = useState<any>(null);
  const [selectedSession, setSelectedSession] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const processSessions = async () => {
      setLoading(true);
      const extractedSessions = extractSessions(logs);
      const securityInsights = getSecurityInsights(logs, extractedSessions);
      
      setSessions(extractedSessions);
      setInsights(securityInsights);
      setLoading(false);
    };
    
    processSessions();
  }, [logs]);

  const handleSessionClick = (sessionId: string) => {
    setSelectedSession(sessionId === selectedSession ? null : sessionId);
  };

  const handleLogClick = (logId: string) => {
    if (onLogSelect) {
      onLogSelect(logId);
    }
  };

  const getAnomalySeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'text-red-600 dark:text-red-400';
      case 'high':
        return 'text-orange-600 dark:text-orange-400';
      case 'medium':
        return 'text-yellow-600 dark:text-yellow-400';
      default:
        return 'text-blue-600 dark:text-blue-400';
    }
  };

  const getAnomalySeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <AlertOctagon className="h-5 w-5 text-red-600 dark:text-red-400" />;
      case 'high':
        return <AlertTriangle className="h-5 w-5 text-orange-600 dark:text-orange-400" />;
      case 'medium':
        return <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />;
      default:
        return <Activity className="h-5 w-5 text-blue-600 dark:text-blue-400" />;
    }
  };

  const getThreatScoreColor = (score: number) => {
    if (score >= 70) return 'text-red-600 dark:text-red-400';
    if (score >= 40) return 'text-orange-600 dark:text-orange-400';
    if (score >= 20) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-green-600 dark:text-green-400';
  };

  if (loading) {
    return (
      <div className="h-60 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col items-center text-gray-500 dark:text-gray-400">
          <Shield className="h-8 w-8 mb-2 animate-pulse" />
          <p>Analyzing security patterns...</p>
        </div>
      </div>
    );
  }

  if (sessions.length === 0) {
    return (
      <div className="h-60 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col items-center text-gray-500 dark:text-gray-400">
          <Users className="h-8 w-8 mb-2" />
          <p>No session data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      {/* Summary stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4">
        <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-3">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-xs text-blue-500 dark:text-blue-300 font-medium uppercase">Sessions</p>
              <h4 className="text-2xl font-semibold text-blue-700 dark:text-blue-200 mt-1">
                {insights?.totalSessions || 0}
              </h4>
            </div>
            <Users className="h-6 w-6 text-blue-500 dark:text-blue-300" />
          </div>
          <p className="text-xs text-blue-600 dark:text-blue-300 mt-1">
            {insights?.activeSessions || 0} active now
          </p>
        </div>
        
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-xs text-gray-500 dark:text-gray-300 font-medium uppercase">Anomalies</p>
              <h4 className="text-2xl font-semibold text-gray-700 dark:text-gray-200 mt-1">
                {insights?.totalAnomalies || 0}
              </h4>
            </div>
            <AlertTriangle className="h-6 w-6 text-gray-500 dark:text-gray-300" />
          </div>
          <p className="text-xs text-gray-600 dark:text-gray-300 mt-1">
            {insights?.highSeverityAnomalies || 0} high severity
          </p>
        </div>
        
        <div className="bg-red-50 dark:bg-red-900/30 rounded-lg p-3">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-xs text-red-500 dark:text-red-300 font-medium uppercase">Top Threat</p>
              <h4 className="text-2xl font-semibold text-red-700 dark:text-red-200 mt-1">
                {insights?.topThreatenedUsers?.[0]?.user || 'None'}
              </h4>
            </div>
            <AlertOctagon className="h-6 w-6 text-red-500 dark:text-red-300" />
          </div>
          <p className="text-xs text-red-600 dark:text-red-300 mt-1">
            Score: {insights?.topThreatenedUsers?.[0]?.threatScore.toFixed(0) || 0}
          </p>
        </div>
        
        <div className="bg-orange-50 dark:bg-orange-900/30 rounded-lg p-3">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-xs text-orange-500 dark:text-orange-300 font-medium uppercase">Top Suspicious IP</p>
              <h4 className="text-md font-semibold text-orange-700 dark:text-orange-200 mt-1 truncate">
                {insights?.topSuspiciousIPs?.[0]?.ip || 'None'}
              </h4>
            </div>
            <Shield className="h-6 w-6 text-orange-500 dark:text-orange-300" />
          </div>
          <p className="text-xs text-orange-600 dark:text-orange-300 mt-1">
            Score: {insights?.topSuspiciousIPs?.[0]?.threatScore.toFixed(0) || 0}
          </p>
        </div>
      </div>
      
      {/* Sessions list */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">User Sessions</h3>
        
        <div className="space-y-3 max-h-96 overflow-y-auto pr-2">
          {sessions
            .sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime())
            .map(session => (
              <div key={session.id} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                <div 
                  className={`p-3 flex justify-between items-center cursor-pointer ${
                    session.anomalies.length > 0 
                      ? 'bg-red-50 dark:bg-red-900/20' 
                      : 'bg-gray-50 dark:bg-gray-700'
                  }`}
                  onClick={() => handleSessionClick(session.id)}
                >
                  <div className="flex items-center gap-2">
                    <User className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">{session.user}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(session.startTime).toLocaleString()} - 
                        {session.endTime ? new Date(session.endTime).toLocaleString() : 'Active'}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    {session.anomalies.length > 0 && (
                      <div className="bg-red-100 dark:bg-red-900/50 px-2 py-1 rounded-full text-xs font-medium text-red-800 dark:text-red-200">
                        {session.anomalies.length} {session.anomalies.length === 1 ? 'anomaly' : 'anomalies'}
                      </div>
                    )}
                    
                    <div className={`font-medium ${getThreatScoreColor(session.threatScore)}`}>
                      {session.threatScore}
                    </div>
                  </div>
                </div>
                
                {selectedSession === session.id && (
                  <div className="p-3 bg-white dark:bg-gray-800">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Session Details</p>
                        <div className="mt-2 text-sm">
                          <p><span className="font-medium">IP:</span> {session.ip || 'Unknown'}</p>
                          <p><span className="font-medium">Duration:</span> {
                            (() => {
                              const start = new Date(session.startTime).getTime();
                              const end = session.endTime ? new Date(session.endTime).getTime() : Date.now();
                              const duration = end - start;
                              const minutes = Math.floor(duration / 60000);
                              const seconds = Math.floor((duration % 60000) / 1000);
                              return `${minutes}m ${seconds}s`;
                            })()
                          }</p>
                          <p><span className="font-medium">Actions:</span> {session.logIds.length}</p>
                        </div>
                      </div>
                      
                      <div>
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Threat Analysis</p>
                        <div 
                          className={`mt-2 px-3 py-2 rounded-md text-sm font-medium ${getThreatScoreColor(session.threatScore)}`}
                        >
                          {session.threatScore >= 70 && 'High threat detected!'}
                          {session.threatScore >= 40 && session.threatScore < 70 && 'Medium threat detected'}
                          {session.threatScore >= 20 && session.threatScore < 40 && 'Low threat detected'}
                          {session.threatScore < 20 && 'No significant threats'}
                        </div>
                      </div>
                    </div>
                    
                    {/* Anomalies */}
                    {session.anomalies.length > 0 && (
                      <div className="mb-4">
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Detected Anomalies</p>
                        <div className="space-y-2">
                          {session.anomalies.map((anomaly, index) => (
                            <div 
                              key={index} 
                              className="flex items-start gap-2 p-2 bg-gray-50 dark:bg-gray-700 rounded-md"
                            >
                              {getAnomalySeverityIcon(anomaly.severity)}
                              <div>
                                <p className={`font-medium ${getAnomalySeverityColor(anomaly.severity)}`}>
                                  {anomaly.type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                </p>
                                <p className="text-sm text-gray-600 dark:text-gray-300">{anomaly.description}</p>
                                {anomaly.relatedLogIds.length > 0 && (
                                  <div className="mt-1">
                                    <button
                                      className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                                      onClick={() => handleLogClick(anomaly.relatedLogIds[0])}
                                    >
                                      View related log
                                    </button>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {/* Recent actions */}
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">Recent Actions</p>
                      <div className="space-y-1 max-h-40 overflow-y-auto">
                        {logs
                          .filter(log => session.logIds.includes(log.id))
                          .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
                          .slice(0, 5)
                          .map(log => (
                            <div 
                              key={log.id} 
                              className="flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-md cursor-pointer"
                              onClick={() => handleLogClick(log.id)}
                            >
                              <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4 text-gray-400" />
                                <span className="text-sm text-gray-800 dark:text-gray-200">
                                  {log.action}
                                </span>
                              </div>
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                {new Date(log.timestamp).toLocaleTimeString()}
                              </span>
                            </div>
                          ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
        </div>
      </div>
    </div>
  );
}; 