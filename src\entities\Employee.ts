import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  OneToOne
} from 'typeorm';
import { EmployeeContact } from './EmployeeContact';
import { EmployeeJob } from './EmployeeJob';
import { EmployeeEducation } from './EmployeeEducation';
import { EmployeeExperience } from './EmployeeExperience';
import { EmployeeFamily } from './EmployeeFamily';
import { EmployeeDocument } from './EmployeeDocument';
import { EmployeeBenefit } from './EmployeeBenefit';
import { EmployeeDevice } from './EmployeeDevice';
import { EmployeeProject } from './EmployeeProject';
import { EmployeeSkill } from './EmployeeSkill';
import { EmployeeVehicle } from './EmployeeVehicle';
import { EmployeeHealth } from './EmployeeHealth';

// These would typically be in separate entity files with relationships
// For this simplified example, we'll include them here as JSON columns
@Entity('employees')
export class Employee {
  @PrimaryGeneratedColumn()
  id: number;

  // Core Personal Info
  @Column({ nullable: false })
  firstName: string;

  @Column({ nullable: true })
  middleName: string;

  @Column({ nullable: false })
  lastName: string;

  @Column({ nullable: false })
  gender: string;

  @Column({ nullable: false })
  dateOfBirth: string;

  @Column({ nullable: true })
  religion: string;

  @Column({ nullable: false })
  cnicNumber: string;

  @Column({ nullable: true })
  cnicExpiryDate: string;

  @Column({ nullable: true })
  nationality: string;

  @Column({ nullable: true })
  maritalStatus: string;

  @Column({ nullable: true })
  bloodType: string;

  @Column({ type: 'text', nullable: true })
  profileImagePath: string;
  
  @Column({ nullable: true })
  fatherName: string;
  
  // Employee ID - kept in main table as it's a core identifier
  @Column({ nullable: false, unique: true })
  employeeId: string;
  
  // System Status
  @Column({ nullable: true, default: 'active' })
  status: string;
  
  // Status Date
  @Column({ nullable: true, name: 'status_date' })
  statusDate: Date;
  
  // Notes and special instructions
  @Column({ type: 'text', nullable: true })
  notes: string;
  
  @Column({ type: 'text', nullable: true, name: 'special_instructions' })
  specialInstructions: string;

  // Relations to other tables
  @OneToOne(() => EmployeeContact, contact => contact.employee)
  contact: EmployeeContact;
  
  @OneToOne(() => EmployeeJob, job => job.employee)
  job: EmployeeJob;
  
  @OneToMany(() => EmployeeEducation, education => education.employee)
  education: EmployeeEducation[];
  
  @OneToMany(() => EmployeeExperience, experience => experience.employee)
  experience: EmployeeExperience[];
  
  @OneToMany(() => EmployeeFamily, family => family.employee)
  family: EmployeeFamily[];
  
  @OneToMany(() => EmployeeDocument, (document: EmployeeDocument) => document.employee)
  documents: EmployeeDocument[];
  
  @OneToOne(() => EmployeeBenefit, benefit => benefit.employee)
  benefit: EmployeeBenefit;
  
  @OneToMany(() => EmployeeDevice, (device: EmployeeDevice) => device.employee)
  devices: EmployeeDevice[];

  @OneToMany(() => EmployeeProject, project => project.employee)
  projects: EmployeeProject[];

  @OneToMany(() => EmployeeSkill, skill => skill.employee)
  skills: EmployeeSkill[];

  @OneToMany(() => EmployeeVehicle, vehicle => vehicle.employee)
  vehicles: EmployeeVehicle[];

  @OneToMany(() => EmployeeHealth, health => health.employee)
  healthRecords: EmployeeHealth[];

  // System columns
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 