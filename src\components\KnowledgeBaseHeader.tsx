import React from 'react';
import { BookOpen, Search } from 'lucide-react';

interface KnowledgeBaseHeaderProps {
  searchQuery: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSearchSubmit?: (e: React.FormEvent) => void;
}

const KnowledgeBaseHeader: React.FC<KnowledgeBaseHeaderProps> = ({
  searchQuery,
  onSearchChange,
  onSearchSubmit
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSearchSubmit) {
      onSearchSubmit(e);
    }
  };

  return (
    <div className="bg-white border-b border-gray-200">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center">
          <div className="flex items-center mr-4">
            <BookOpen className="h-7 w-7 text-blue-600 mr-3" />
            <h1 className="text-2xl md:text-3xl font-semibold text-gray-900">Enterprise Knowledge Hub</h1>
          </div>
          
          {/* Search bar - positioned between title and buttons */}
          <div className="hidden md:block relative w-64 lg:w-80 mx-4 flex-grow">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-4 w-4 text-gray-400" />
            </div>
            <form onSubmit={handleSubmit}>
              <input
                type="text"
                placeholder="Search resources..."
                value={searchQuery}
                onChange={onSearchChange}
                className="block w-full pl-9 pr-4 py-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </form>
          </div>
          
          <div className="flex space-x-3">
            <button className="px-4 py-2 text-sm font-medium text-blue-600 bg-white rounded-md hover:bg-blue-50 border border-gray-200">
              All Resources
            </button>
            <button className="px-4 py-2 text-sm font-medium text-gray-600 bg-white rounded-md hover:bg-gray-50 border border-gray-200">
              Featured
            </button>
          </div>
        </div>
        
        {/* Mobile search bar - only visible on small screens */}
        <div className="md:hidden relative w-full mt-3">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <form onSubmit={handleSubmit}>
            <input
              type="text"
              placeholder="Search resources..."
              value={searchQuery}
              onChange={onSearchChange}
              className="block w-full pl-9 pr-4 py-2 text-sm border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </form>
        </div>
      </div>
    </div>
  );
};

export default KnowledgeBaseHeader; 