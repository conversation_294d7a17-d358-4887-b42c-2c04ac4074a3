import express, { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { SystemLogController } from '../controllers/SystemLogController';
import { authMiddleware } from '../middleware/authMiddleware';

const router = express.Router();

// Apply auth middleware to all routes
router.use(authMiddleware.verify as RequestHandler);

// GET /api/system-logs/statistics - Get statistics about logs
router.get('/statistics', SystemLogController.getStatistics as RequestHandler);

// GET /api/system-logs/export - Export logs as CSV
router.get('/export', SystemLogController.exportLogs as RequestHandler);

// GET /api/system-logs - Get all system logs with optional filtering
router.get('/', SystemLogController.getAllLogs as RequestHandler);

// GET /api/system-logs/:id - Get a single system log by ID
router.get('/:id', SystemLogController.getLogById as RequestHandler);

// POST /api/system-logs - Create a new system log
router.post('/', SystemLogController.createLog as RequestHandler);

// PUT /api/system-logs/:id - Update an existing system log
router.put('/:id', SystemLogController.updateLog as RequestHandler);

// DELETE /api/system-logs/:id - Delete a system log
router.delete('/:id', SystemLogController.deleteLog as RequestHandler);

// DELETE /api/system-logs - Clear all system logs
router.delete('/', SystemLogController.clearLogs as RequestHandler);

export default router; 