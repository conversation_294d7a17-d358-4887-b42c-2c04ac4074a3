-- Create database if not exists
CREATE DATABASE IF NOT EXISTS ims_db;

-- Use the database
USE ims_db;

-- Create users table if not exists
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  email VARCHAR(255) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  role ENUM('EMPLOYEE', 'DEPT_HEAD', 'IT_STAFF', 'IT_ADMIN', 'IT_SUPPORT') NOT NULL DEFAULT 'EMPLOYEE',
  department VARCHAR(255) NOT NULL,
  project TEXT NULL,
  location TEXT NULL,
  isActive TINYINT(1) NOT NULL DEFAULT 1,
  permissions JSON NULL,
  createdAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  updatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
);

-- Insert admin user if not exists
INSERT INTO users (id, name, email, password, role, department, isActive, permissions)
VALUES (
  1,
  'Administrator',
  '<EMAIL>',
  '$2b$10$EjaI3g8bI/L0t4Z4Xg/FgeE/ZxRdT0YaGU.Fg0V9yA9UNqDJy6Pcy', -- hash for 'admin123'
  'IT_ADMIN',
  'IT',
  1,
  '{"canCreateTickets":true,"canCreateTicketsForOthers":true,"canEditTickets":true,"canDeleteTickets":true,"canCloseTickets":true,"canLockTickets":true,"canAssignTickets":true,"canEscalateTickets":true,"canViewAllTickets":true}'
)
ON DUPLICATE KEY UPDATE
  role = 'IT_ADMIN',
  isActive = 1,
  permissions = '{"canCreateTickets":true,"canCreateTicketsForOthers":true,"canEditTickets":true,"canDeleteTickets":true,"canCloseTickets":true,"canLockTickets":true,"canAssignTickets":true,"canEscalateTickets":true,"canViewAllTickets":true}';

-- Display the user
SELECT * FROM users WHERE email = '<EMAIL>'; 