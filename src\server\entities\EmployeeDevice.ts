import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { Employee } from './Employee';

@Entity('employee_devices')
export class EmployeeDevice {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 100,  nullable: false })
  deviceName: string;

  @Column({ type: 'varchar', length: 255,  nullable: true })
  makeModel: string;

  @Column({ type: 'varchar', length: 20,  nullable: true })
  serialNumber: string;

  @Column({ type: 'date',  nullable: true })
  handoverDate: string;

  @Column({ type: 'date',  nullable: true })
  returnDate: string;

  @Column({ type: 'varchar', length: 255,  nullable: true })
  condition: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  // Vehicle Details
  @Column({ type: 'varchar', length: 255,  nullable: true })
  isVehicle: boolean;

  @Column({ type: 'varchar', length: 50,  nullable: true })
  vehicleType: string;
  
  @Column({ type: 'varchar', length: 20,  nullable: true })
  registrationNumber: string;
  
  @Column({ type: 'boolean',  nullable: true, default: false })
  providedByCompany: boolean;
  
  @Column({ type: 'text', nullable: true })
  vehicleMakeModel: string;
  
  @Column({ type: 'text', nullable: true })
  vehicleColor: string;
  
  @Column({ type: 'text', nullable: true })
  mileageAtIssuance: string;

  // Relation to Employee
  @ManyToOne(() => Employee, employee => employee.devices, { onDelete: 'CASCADE' })
  @JoinColumn()
  employee: Employee;

  // System columns
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 