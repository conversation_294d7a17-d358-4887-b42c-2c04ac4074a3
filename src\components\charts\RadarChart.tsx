import React from 'react';
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend
} from 'chart.js';
import { Radar } from 'react-chartjs-2';

ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend
);

interface RadarChartProps {
  data: {
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      backgroundColor: string;
      borderColor: string;
      pointBackgroundColor: string;
      pointBorderColor: string;
      pointHoverBackgroundColor: string;
      pointHoverBorderColor: string;
    }[];
  };
  options?: any;
}

export const RadarChart: React.FC<RadarChartProps> = ({ data, options }) => {
  return (
    <div style={{ height: '300px' }}>
      <Radar 
        data={data} 
        options={{
          responsive: true,
          maintainAspectRatio: false,
          ...options
        }} 
      />
    </div>
  );
}; 