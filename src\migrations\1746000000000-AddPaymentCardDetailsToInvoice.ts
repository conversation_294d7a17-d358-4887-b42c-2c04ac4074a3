import { MigrationInterface, QueryRunner } from "typeorm";

export class AddPaymentCardDetailsToInvoice1741100000000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if the billing_invoices table exists
        const tableExists = await queryRunner.hasTable('billing_invoices');
        if (!tableExists) {
            console.log('billing_invoices table does not exist, skipping migration');
            return;
        }

        // Get the existing columns in the billing_invoices table
        const columns = await queryRunner.query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'billing_invoices'
        `);
        
        const columnNames = columns.map((column: { COLUMN_NAME: string }) => column.COLUMN_NAME);
        
        // Add columns that don't exist
        if (!columnNames.includes('accountNumber')) {
            await queryRunner.query(`ALTER TABLE billing_invoices ADD COLUMN accountNumber VARCHAR(255) NULL`);
        }
        
        if (!columnNames.includes('cardNumber')) {
            await queryRunner.query(`ALTER TABLE billing_invoices ADD COLUMN cardNumber VARCHAR(255) NULL`);
        }
        
        if (!columnNames.includes('expiryDate')) {
            await queryRunner.query(`ALTER TABLE billing_invoices ADD COLUMN expiryDate VARCHAR(255) NULL`);
        }
        
        if (!columnNames.includes('cvv')) {
            await queryRunner.query(`ALTER TABLE billing_invoices ADD COLUMN cvv VARCHAR(255) NULL`);
        }

        console.log('Successfully added payment card detail fields to billing_invoices table');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Check if the billing_invoices table exists
        const tableExists = await queryRunner.hasTable('billing_invoices');
        if (!tableExists) {
            console.log('billing_invoices table does not exist, skipping reversion');
            return;
        }

        // Remove the payment card detail columns
        await queryRunner.query(`
            ALTER TABLE billing_invoices
            DROP COLUMN IF EXISTS accountNumber,
            DROP COLUMN IF EXISTS cardNumber,
            DROP COLUMN IF EXISTS expiryDate,
            DROP COLUMN IF EXISTS cvv
        `);

        console.log('Successfully removed payment card detail fields from billing_invoices table');
    }
} 