import React, { useState, useEffect } from 'react';
import { X, Search } from 'lucide-react';
import { Employee } from '../../../types/employee';
import { AttendanceRegularizationRequest } from '../../../types/attendance';
import RegularizationManager from './RegularizationManager';

interface RegularizationFormProps {
  onClose: () => void;
  onSubmit: (request: Omit<AttendanceRegularizationRequest, 'id'>) => void;
  isSubmitting?: boolean;
  employees: Employee[];
}

const RegularizationForm: React.FC<RegularizationFormProps> = ({
  onClose,
  onSubmit,
  isSubmitting = false,
  employees
}) => {
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  // Filter employees based on search query
  const filteredEmployees = employees.filter(emp => 
    emp.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    emp.employeeId?.toString().includes(searchQuery) ||
    emp.department?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-2xl relative">
        {/* Header */}
        <div className="border-b border-gray-200 dark:border-gray-700 p-4">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                Submit Attendance Regularization (HR/Admin)
              </h2>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Submit attendance regularization request on behalf of an employee
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
              aria-label="Close"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        <div className="p-6">
          {/* Employee Search */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Select Employee
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search by name, ID, or department..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Employee List */}
            <div className="mt-2 max-h-40 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-md">
              {filteredEmployees.length > 0 ? (
                <div className="divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredEmployees.map((employee) => (
                    <button
                      key={employee.id}
                      onClick={() => setSelectedEmployee(employee)}
                      className={`w-full text-left px-4 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150 ${
                        selectedEmployee?.id === employee.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                      }`}
                    >
                      <div className="flex items-center">
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white">
                            {employee.name}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            ID: {employee.employeeId} • {employee.department}
                          </div>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              ) : (
                <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                  No employees found matching your search
                </div>
              )}
            </div>
          </div>

          {/* Regularization Form */}
          {selectedEmployee && (
            <div className="mt-6">
              <RegularizationManager
                mode="form"
                showFormButton={false}
                requests={[]}
                employeeId={selectedEmployee.id}
                employeeName={selectedEmployee.name}
                onSubmit={onSubmit}
                onCancel={onClose}
                isSubmitting={isSubmitting}
                compact={false}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RegularizationForm; 