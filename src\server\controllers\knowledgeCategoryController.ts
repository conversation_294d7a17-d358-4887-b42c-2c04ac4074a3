import { Request, Response } from 'express';
import { KnowledgeCategory } from '../../entities/KnowledgeCategory';
import { KnowledgeBase } from '../../entities/KnowledgeBase';
import { AppDataSource } from '../../config/database';
import logger from '../utils/logger';

// Get all categories
export const getAllCategories = async (req: Request, res: Response) => {
  try {
    // Ensure database is initialized
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }
    
    const categoryRepository = AppDataSource.getRepository(KnowledgeCategory);
    
    const categories = await categoryRepository.find({
      order: {
        displayOrder: 'ASC',
        name: 'ASC'
      }
    });
    
    return res.status(200).json(categories);
  } catch (error) {
    logger.error('Error fetching knowledge categories:', error);
    return res.status(500).json({ message: 'Error fetching knowledge categories' });
  }
};

// Get a single category by ID
export const getCategoryById = async (req: Request, res: Response) => {
  try {
    // Ensure database is initialized
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }
    
    const { id } = req.params;
    
    const categoryRepository = AppDataSource.getRepository(KnowledgeCategory);
    
    const category = await categoryRepository.findOne({
      where: { id },
      relations: ['articles']
    });
    
    if (!category) {
      return res.status(404).json({ message: 'Category not found' });
    }
    
    return res.status(200).json(category);
  } catch (error) {
    logger.error(`Error fetching category with ID ${req.params.id}:`, error);
    return res.status(500).json({ message: 'Error fetching category' });
  }
};

// Create a new category
export const createCategory = async (req: Request, res: Response) => {
  try {
    // Ensure database is initialized
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }
    
    const { name, description, slug, displayOrder, parentId } = req.body;
    
    // Validate required fields
    if (!name || !slug) {
      return res.status(400).json({ message: 'Name and slug are required' });
    }
    
    const categoryRepository = AppDataSource.getRepository(KnowledgeCategory);
    
    // Check if slug is already in use
    const existingCategory = await categoryRepository.findOne({ where: { slug } });
    if (existingCategory) {
      return res.status(400).json({ message: 'Slug is already in use' });
    }
    
    // Check if parent category exists if provided
    if (parentId) {
      const parentCategory = await categoryRepository.findOne({ where: { id: parentId } });
      if (!parentCategory) {
        return res.status(404).json({ message: 'Parent category not found' });
      }
    }
    
    // Create new category
    const newCategory = new KnowledgeCategory();
    newCategory.name = name;
    newCategory.description = description;
    newCategory.slug = slug;
    newCategory.displayOrder = displayOrder || 0;
    newCategory.parentId = parentId;
    
    const savedCategory = await categoryRepository.save(newCategory);
    
    return res.status(201).json(savedCategory);
  } catch (error) {
    logger.error('Error creating category:', error);
    return res.status(500).json({ message: 'Error creating category' });
  }
};

// Update a category
export const updateCategory = async (req: Request, res: Response) => {
  try {
    // Ensure database is initialized
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }
    
    const { id } = req.params;
    const { name, description, slug, displayOrder, parentId } = req.body;
    
    const categoryRepository = AppDataSource.getRepository(KnowledgeCategory);
    
    // Check if category exists
    const category = await categoryRepository.findOne({ where: { id } });
    
    if (!category) {
      return res.status(404).json({ message: 'Category not found' });
    }
    
    // Check if slug is already in use by another category
    if (slug && slug !== category.slug) {
      const existingCategory = await categoryRepository.findOne({ where: { slug } });
      if (existingCategory && existingCategory.id !== id) {
        return res.status(400).json({ message: 'Slug is already in use' });
      }
    }
    
    // Check if parent category exists if provided
    if (parentId && parentId !== category.parentId) {
      // Prevent circular reference (category can't be its own parent)
      if (parentId === id) {
        return res.status(400).json({ message: 'Category cannot be its own parent' });
      }
      
      const parentCategory = await categoryRepository.findOne({ where: { id: parentId } });
      if (!parentCategory) {
        return res.status(404).json({ message: 'Parent category not found' });
      }
    }
    
    // Update category fields
    if (name) category.name = name;
    if (description !== undefined) category.description = description;
    if (slug) category.slug = slug;
    if (displayOrder !== undefined) category.displayOrder = displayOrder;
    if (parentId !== undefined) category.parentId = parentId;
    
    const updatedCategory = await categoryRepository.save(category);
    
    return res.status(200).json(updatedCategory);
  } catch (error) {
    logger.error(`Error updating category with ID ${req.params.id}:`, error);
    return res.status(500).json({ message: 'Error updating category' });
  }
};

// Delete a category
export const deleteCategory = async (req: Request, res: Response) => {
  try {
    // Ensure database is initialized
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }
    
    const { id } = req.params;
    
    const categoryRepository = AppDataSource.getRepository(KnowledgeCategory);
    const knowledgeBaseRepository = AppDataSource.getRepository(KnowledgeBase);
    
    // Check if category exists
    const category = await categoryRepository.findOne({
      where: { id },
      relations: ['articles']
    });
    
    if (!category) {
      return res.status(404).json({ message: 'Category not found' });
    }
    
    // Check if category has articles
    if (category.articles && category.articles.length > 0) {
      return res.status(400).json({
        message: 'Cannot delete category with articles. Please move or delete the articles first.'
      });
    }
    
    // Check if category has child categories
    const childCategories = await categoryRepository.find({ where: { parentId: id } });
    if (childCategories.length > 0) {
      return res.status(400).json({
        message: 'Cannot delete category with child categories. Please move or delete the child categories first.'
      });
    }
    
    await categoryRepository.remove(category);
    
    return res.status(200).json({ message: 'Category deleted successfully' });
  } catch (error) {
    logger.error(`Error deleting category with ID ${req.params.id}:`, error);
    return res.status(500).json({ message: 'Error deleting category' });
  }
}; 