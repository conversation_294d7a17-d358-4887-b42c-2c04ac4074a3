-- Fix existing leave request duration calculation
-- This script will recalculate the daysRequested for all leave requests

UPDATE leave_requests 
SET daysRequested = FLOOR(DATEDIFF(endDate, startDate)) + 1
WHERE daysRequested != FLOOR(DATEDIFF(endDate, startDate)) + 1;

-- For your specific request (Oct 12-14, 2025), this should update it to 3 days
-- You can also run this specific query to check:
-- SELECT id, startDate, endDate, daysRequested, 
--        FLOOR(DATEDIFF(endDate, startDate)) + 1 as correct_days
-- FROM leave_requests 
-- WHERE startDate = '2025-10-12' AND endDate = '2025-10-14';