import { AttendanceRegularizationRequest } from '../types/attendance';
import api from './api';

class RegularizationService {
  private static instance: RegularizationService;

  private constructor() {
    // No initialization needed for real API service
  }

  public static getInstance(): RegularizationService {
    if (!RegularizationService.instance) {
      RegularizationService.instance = new RegularizationService();
    }
    return RegularizationService.instance;
  }

  /**
   * Get all regularization requests
   */
  public async getAllRequests(): Promise<AttendanceRegularizationRequest[]> {
    try {
      const response = await api.get('/attendance/regularization');
      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching regularization requests:', error);
      throw new Error('Failed to fetch regularization requests');
    }
  }

  /**
   * Get regularization requests by status
   */
  public async getRequestsByStatus(status: string): Promise<AttendanceRegularizationRequest[]> {
    try {
      const params = status === 'all' ? {} : { status };
      const response = await api.get('/attendance/regularization', { params });
      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching regularization requests by status:', error);
      throw new Error('Failed to fetch regularization requests by status');
    }
  }

  /**
   * Get regularization requests by employee ID
   */
  public async getRequestsByEmployee(employeeId: number): Promise<AttendanceRegularizationRequest[]> {
    try {
      const response = await api.get(`/attendance/regularization/employee/${employeeId}`);
      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching employee regularization requests:', error);
      throw new Error('Failed to fetch employee regularization requests');
    }
  }

  /**
   * Submit a new regularization request
   */
  public async submitRequest(request: Omit<AttendanceRegularizationRequest, 'id'>): Promise<AttendanceRegularizationRequest> {
    try {
      const response = await api.post('/attendance/regularization', request);
      return response.data.data;
    } catch (error) {
      console.error('Error submitting regularization request:', error);
      throw new Error('Failed to submit regularization request');
    }
  }

  /**
   * Approve a regularization request
   */
  public async approveRequest(id: number, approverComments?: string): Promise<AttendanceRegularizationRequest | null> {
    try {
      const response = await api.patch(`/attendance/regularization/${id}/approve`, {
        approverComments
      });
      return response.data.data;
    } catch (error) {
      console.error('Error approving regularization request:', error);
      throw new Error('Failed to approve regularization request');
    }
  }

  /**
   * Reject a regularization request
   */
  public async rejectRequest(id: number, approverComments: string): Promise<AttendanceRegularizationRequest | null> {
    try {
      if (!approverComments.trim()) {
        throw new Error('Approver comments are required for rejection');
      }

      const response = await api.patch(`/attendance/regularization/${id}/reject`, {
        approverComments: approverComments.trim()
      });
      return response.data.data;
    } catch (error) {
      console.error('Error rejecting regularization request:', error);
      throw new Error('Failed to reject regularization request');
    }
  }

  /**
   * Get a specific regularization request by ID
   */
  public async getRequestById(id: number): Promise<AttendanceRegularizationRequest | null> {
    try {
      const response = await api.get(`/attendance/regularization/${id}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching regularization request by ID:', error);
      throw new Error('Failed to fetch regularization request');
    }
  }

  /**
   * Update a regularization request
   */
  public async updateRequest(id: number, updates: Partial<AttendanceRegularizationRequest>): Promise<AttendanceRegularizationRequest | null> {
    try {
      const response = await api.put(`/attendance/regularization/${id}`, updates);
      return response.data.data;
    } catch (error) {
      console.error('Error updating regularization request:', error);
      throw new Error('Failed to update regularization request');
    }
  }

  /**
   * Delete a regularization request
   */
  public async deleteRequest(id: number): Promise<boolean> {
    try {
      await api.delete(`/attendance/regularization/${id}`);
      return true;
    } catch (error) {
      console.error('Error deleting regularization request:', error);
      throw new Error('Failed to delete regularization request');
    }
  }

  /**
   * Get statistics about regularization requests
   */
  public async getStatistics(): Promise<{
    total: number;
    pending: number;
    approved: number;
    rejected: number;
  }> {
    try {
      const response = await api.get('/attendance/regularization/stats');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching regularization statistics:', error);
      throw new Error('Failed to fetch regularization statistics');
    }
  }

  /**
   * Get regularization requests with advanced filtering
   */
  public async getRequestsWithFilters(filters: {
    employeeId?: number;
    status?: string;
    startDate?: string;
    endDate?: string;
    limit?: number;
    offset?: number;
  }): Promise<{
    data: AttendanceRegularizationRequest[];
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  }> {
    try {
      const response = await api.get('/attendance/regularization', { params: filters });
      return {
        data: response.data.data || [],
        pagination: response.data.pagination || {
          total: 0,
          limit: 100,
          offset: 0,
          hasMore: false
        }
      };
    } catch (error) {
      console.error('Error fetching regularization requests with filters:', error);
      throw new Error('Failed to fetch regularization requests with filters');
    }
  }
}

export default RegularizationService; 