import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index
} from 'typeorm';
import { IsNotEmpty, IsOptional, IsString, IsArray, IsNumber, IsBoolean } from 'class-validator';

@Entity('holiday_configurations')
@Index(['organizationId'])
@Index(['isActive'])
export class HolidayConfiguration {
  @PrimaryGeneratedColumn({ type: 'int' })
  id: number;

  @Column({ type: 'varchar', length: 100, default: 'default' })
  @IsNotEmpty({ message: 'Organization ID is required' })
  @IsString({ message: 'Organization ID must be a string' })
  organizationId: string;

  @Column({ type: 'varchar', length: 100, default: 'Global Configuration' })
  @IsNotEmpty({ message: 'Configuration name is required' })
  @IsString({ message: 'Configuration name must be a string' })
  name: string;

  @Column({ type: 'json' })
  @IsArray({ message: 'Weekend days must be an array' })
  @IsNumber({}, { each: true, message: 'Weekend days must be numbers (0-6)' })
  weekendDays: number[];

  @Column({ type: 'varchar', length: 50, default: 'UTC' })
  @IsOptional()
  @IsString({ message: 'Timezone must be a string' })
  timezone?: string;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  workingHours?: {
    start: string;
    end: string;
    breakDuration?: number;
  };

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  holidaySettings?: {
    allowOptionalHolidays?: boolean;
    maxOptionalHolidaysPerYear?: number;
    requireApprovalForOptional?: boolean;
    carryOverUnusedOptionalHolidays?: boolean;
    substituteHolidayPolicy?: string;
  };

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  applicableRegions?: string[];

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  applicableDepartments?: string[];

  @Column({ type: 'boolean', default: true })
  @IsBoolean({ message: 'isActive must be a boolean' })
  isActive: boolean;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString({ message: 'Created by must be a string' })
  createdBy?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString({ message: 'Updated by must be a string' })
  updatedBy?: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString({ message: 'Notes must be a string' })
  notes?: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;
} 