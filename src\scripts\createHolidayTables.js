const mysql = require('mysql2/promise');
require('dotenv').config();

async function createHolidayTables() {
  let connection;
  
  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USERNAME || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_DATABASE || 'ims_db',
      port: process.env.DB_PORT || 3306
    });

    console.log('✅ Connected to database successfully');

    // Create holidays table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS holidays (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name VARCHAR(100) NOT NULL,
        date DATE NOT NULL,
        type ENUM('public', 'company', 'optional') DEFAULT 'public',
        description TEXT,
        isActive BOOLEAN DEFAULT true,
        metadata JSON,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_holiday_date (date),
        INDEX idx_holiday_type (type),
        INDEX idx_holiday_active (isActive)
      )
    `);
    
    console.log('✅ Holidays table created/verified');

    // Create holiday_configurations table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS holiday_configurations (
        id INT PRIMARY KEY AUTO_INCREMENT,
        organizationId INT DEFAULT 1,
        weekendDays VARCHAR(20) DEFAULT '[0,6]',
        timezone VARCHAR(50) DEFAULT 'UTC',
        workingHours JSON,
        holidaySettings JSON,
        applicableRegions JSON,
        applicableDepartments JSON,
        createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    
    console.log('✅ Holiday configurations table created/verified');

    // Insert sample religious holidays
    await connection.execute(`
      INSERT IGNORE INTO holidays (name, date, type, description, metadata) VALUES
      ('Eid al-Fitr', '2024-04-10', 'optional', 'Islamic festival celebrating the end of Ramadan', '{"religion": "Islam", "duration": 3, "culturalSignificance": "Major Islamic holiday", "observanceLevel": "major", "fastingRequired": false}'),
      ('Eid al-Adha', '2024-06-16', 'optional', 'Islamic festival of sacrifice', '{"religion": "Islam", "duration": 4, "culturalSignificance": "Major Islamic holiday", "observanceLevel": "major", "fastingRequired": false}'),
      ('Mawlid al-Nabi', '2024-09-15', 'optional', 'Birthday of Prophet Muhammad', '{"religion": "Islam", "duration": 1, "culturalSignificance": "Religious commemoration", "observanceLevel": "major"}'),
      ('Diwali', '2024-11-01', 'optional', 'Hindu festival of lights', '{"religion": "Hinduism", "duration": 5, "culturalSignificance": "Major Hindu holiday", "observanceLevel": "major"}'),
      ('Holi', '2024-03-13', 'optional', 'Hindu festival of colors', '{"religion": "Hinduism", "duration": 2, "culturalSignificance": "Spring festival", "observanceLevel": "major"}'),
      ('Christmas Day', '2024-12-25', 'public', 'Christian holiday celebrating the birth of Jesus Christ', '{"religion": "Christianity", "duration": 1, "culturalSignificance": "Major Christian holiday", "observanceLevel": "major"}'),
      ('Good Friday', '2024-03-29', 'optional', 'Christian commemoration of the crucifixion of Jesus', '{"religion": "Christianity", "duration": 1, "culturalSignificance": "Religious observance", "observanceLevel": "major"}'),
      ('Chinese New Year', '2024-02-10', 'optional', 'Traditional Chinese New Year celebration', '{"culturalSignificance": "Major Chinese cultural holiday", "duration": 3, "observanceLevel": "major"}'),
      ('Vesak Day', '2024-05-23', 'optional', 'Buddhist celebration of Buddha\\\'s birth, enlightenment, and death', '{"religion": "Buddhism", "duration": 1, "culturalSignificance": "Sacred Buddhist holiday", "observanceLevel": "major"}'),
      ('Vaisakhi', '2024-04-13', 'optional', 'Sikh New Year and harvest festival', '{"religion": "Sikhism", "duration": 1, "culturalSignificance": "Sikh religious celebration", "observanceLevel": "major"}')
    `);
    
    console.log('✅ Sample religious holidays inserted');

    // Insert default configuration
    await connection.execute(`
      INSERT INTO holiday_configurations (organizationId, weekendDays, holidaySettings) VALUES
      (1, '[0,6]', '{"allowOptionalHolidays": true, "maxOptionalHolidaysPerEmployee": 5, "requireApproval": false}')
      ON DUPLICATE KEY UPDATE
      holidaySettings = '{"allowOptionalHolidays": true, "maxOptionalHolidaysPerEmployee": 5, "requireApproval": false}'
    `);
    
    console.log('✅ Default holiday configuration created');

    // Check what we created
    const [holidays] = await connection.execute('SELECT * FROM holidays ORDER BY date');
    const [configs] = await connection.execute('SELECT * FROM holiday_configurations');
    
    console.log(`\n📊 Summary:`);
    console.log(`   • ${holidays.length} holidays in database`);
    console.log(`   • ${configs.length} holiday configuration(s)`);
    
    console.log('\n🎉 Holiday system setup completed successfully!');
    console.log('\n📋 Religious holidays included:');
    
    const religiousHolidays = holidays.filter(h => h.type === 'optional');
    religiousHolidays.forEach(holiday => {
      const metadata = JSON.parse(holiday.metadata || '{}');
      console.log(`   • ${holiday.name} (${holiday.date}) - ${metadata.religion || 'Cultural'}`);
    });
    
  } catch (error) {
    console.error('❌ Error creating holiday tables:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔐 Database connection closed');
    }
  }
}

// Run the script
createHolidayTables(); 