import { AppDataSource } from '../config/database';

async function checkLeaveTypes() {
  try {
    console.log('🔍 Initializing database connection...');
    await AppDataSource.initialize();
    
    const queryRunner = AppDataSource.createQueryRunner();
    
    try {
      console.log('📋 Checking current leave types in the system...\n');
      
      // Check leave type policies
      console.log('1️⃣ LEAVE TYPE POLICIES:');
      const policies = await queryRunner.query(`
        SELECT id, leaveType, displayName, enabled, isActive, maxDaysPerYear 
        FROM leave_type_policies 
        ORDER BY leaveType
      `);
      
      if (policies.length === 0) {
        console.log('   ❌ No leave type policies found!');
      } else {
        console.log(`   ✅ Found ${policies.length} leave type policies:`);
        policies.forEach((policy: any) => {
          console.log(`      • ${policy.leaveType} (${policy.displayName}) - ${policy.maxDaysPerYear} days - Active: ${policy.isActive} - Enabled: ${policy.enabled}`);
        });
      }
      
      // Check what leave types are being used in allocations
      console.log('\n2️⃣ LEAVE TYPES IN ALLOCATIONS:');
      const allocationTypes = await queryRunner.query(`
        SELECT DISTINCT leaveType, COUNT(*) as count 
        FROM leave_allocations 
        GROUP BY leaveType 
        ORDER BY leaveType
      `);
      
      if (allocationTypes.length === 0) {
        console.log('   ❌ No leave allocations found!');
      } else {
        console.log(`   📊 Found ${allocationTypes.length} distinct leave types in allocations:`);
        allocationTypes.forEach((type: any) => {
          console.log(`      • ${type.leaveType} - ${type.count} allocation records`);
        });
      }
      
      // Check what leave types are being used in balances
      console.log('\n3️⃣ LEAVE TYPES IN BALANCES:');
      const balanceTypes = await queryRunner.query(`
        SELECT DISTINCT leaveType, COUNT(*) as count 
        FROM leave_balances 
        GROUP BY leaveType 
        ORDER BY leaveType
      `);
      
      if (balanceTypes.length === 0) {
        console.log('   ❌ No leave balances found!');
      } else {
        console.log(`   📊 Found ${balanceTypes.length} distinct leave types in balances:`);
        balanceTypes.forEach((type: any) => {
          console.log(`      • ${type.leaveType} - ${type.count} balance records`);
        });
      }
      
      // Find orphaned leave types (in allocations/balances but not in policies)
      console.log('\n4️⃣ ORPHANED LEAVE TYPES:');
      const policyLeaveTypes = policies.map((p: any) => p.leaveType);
      const allLeaveTypes = [...new Set([
        ...allocationTypes.map((t: any) => t.leaveType),
        ...balanceTypes.map((t: any) => t.leaveType)
      ])];
      
      const orphanedTypes = allLeaveTypes.filter(type => !policyLeaveTypes.includes(type));
      
      if (orphanedTypes.length === 0) {
        console.log('   ✅ No orphaned leave types found!');
      } else {
        console.log(`   ⚠️  Found ${orphanedTypes.length} orphaned leave types (in data but no policy):`);
        orphanedTypes.forEach(type => {
          console.log(`      • ${type} - This leave type has data but no policy configuration!`);
        });
      }
      
      console.log('\n📝 RECOMMENDATIONS:');
      if (orphanedTypes.length > 0) {
        console.log('   🔧 You need to either:');
        console.log('      1. Create leave type policies for the orphaned types, OR');
        console.log('      2. Clean up/convert the orphaned data to existing leave types');
      }
      
      if (policies.length === 0) {
        console.log('   🔧 You need to create leave type policies first in the Leave Policy Configuration tab');
      }
      
    } finally {
      await queryRunner.release();
    }
    
  } catch (error) {
    console.error('❌ Error checking leave types:', error);
    throw error;
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('\n📪 Database connection closed');
    }
  }
}

// Run the check
if (require.main === module) {
  checkLeaveTypes()
    .then(() => {
      console.log('✅ Leave types check completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Leave types check failed:', error);
      process.exit(1);
    });
}

export { checkLeaveTypes }; 