import { AppDataSource } from '../config/database';
import { LeaveTypePolicy } from '../entities/LeaveTypePolicy';
import { LeavePolicyConfiguration } from '../entities/LeavePolicyConfiguration';

async function testLeaveTypeCreation() {
  try {
    console.log('🔍 Initializing database connection...');
    await AppDataSource.initialize();
    
    const leaveTypePolicyRepository = AppDataSource.getRepository(LeaveTypePolicy);
    const leavePolicyConfigRepository = AppDataSource.getRepository(LeavePolicyConfiguration);
    
    // Find active policy
    console.log('\n📋 Finding active policy configuration...');
    const activePolicyConfig = await leavePolicyConfigRepository.findOne({
      where: { isActive: true }
    });
    
    if (!activePolicyConfig) {
      console.log('❌ No active policy configuration found');
      return;
    }
    
    console.log(`✅ Found active policy: ID ${activePolicyConfig.id}`);
    
    // Test creating a leave type
    console.log('\n🧪 Testing leave type creation...');
    
    const testLeaveTypeData = {
      leaveType: 'SICK_LEAVE',
      enabled: true,
      displayName: 'Sick Leave',
      description: 'Medical leave for illness',
      settings: {
        minDaysNotice: 0,
        allowLeaveModification: false,
        enableProratedLeave: false
      },
      applicableRoles: [],
      applicableDepartments: [],
      maxDaysPerYear: 15,
      minServicePeriod: 0,
      allowCarryForward: false,
      carryForwardLimit: 0,
      encashmentAllowed: false,
      documentRequired: false,
      color: '',
      sortOrder: 0,
      isActive: true,
      category: 'sick',
      genderEligibility: 'all',
      effectiveFrom: undefined,
      validUntil: undefined,
      policyConfigurationId: activePolicyConfig.id
    };
    
    console.log('Test data:', JSON.stringify(testLeaveTypeData, null, 2));
    
    // Check if it already exists
    const existingLeaveType = await leaveTypePolicyRepository.findOne({
      where: { leaveType: testLeaveTypeData.leaveType }
    });
    
    if (existingLeaveType) {
      console.log('❌ Leave type already exists:', existingLeaveType);
      return;
    }
    
    console.log('✅ No existing leave type found, proceeding with creation...');
    
    // Try to create
    const newLeaveType = leaveTypePolicyRepository.create(testLeaveTypeData);
    console.log('✅ Entity created in memory');
    
    const savedLeaveType = await leaveTypePolicyRepository.save(newLeaveType);
    console.log('✅ Successfully saved leave type:', savedLeaveType);
    
  } catch (error) {
    console.error('❌ Error during test:', error);
    
    // Check if it's a specific database error
    if (error instanceof Error) {
      console.error('Error name:', error.name);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }
    
    // If it's a query error, let's see more details
    if ((error as any).code) {
      console.error('Database error code:', (error as any).code);
      console.error('Database error sqlMessage:', (error as any).sqlMessage);
      console.error('Database error sql:', (error as any).sql);
    }
  } finally {
    await AppDataSource.destroy();
  }
}

testLeaveTypeCreation().catch(console.error); 