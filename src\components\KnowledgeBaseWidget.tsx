import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Link } from 'react-router-dom';
import { FileText } from 'lucide-react';

interface KnowledgeArticle {
  id: string;
  title: string;
  summary?: string;
  content: string;
  isFeatured: boolean;
  category: {
    name: string;
  };
  createdAt: string;
}

interface KnowledgeBaseWidgetProps {
  limit?: number;
  featuredOnly?: boolean;
  categoryId?: string;
  showTitle?: boolean;
  title?: string;
}

// Add a helper function to extract the first image URL from content
const extractFirstImageUrl = (content: string): string | null => {
  const imgRegex = /<img[^>]+src="([^">]+)"/;
  const match = content.match(imgRegex);
  return match ? match[1] : null;
};

// Add this helper function near the top of the file
const stripHtmlTags = (html: string): string => {
  const tmp = document.createElement('div');
  tmp.innerHTML = html;
  return tmp.textContent || tmp.innerText || '';
};

const KnowledgeBaseWidget: React.FC<KnowledgeBaseWidgetProps> = ({
  limit = 5,
  featuredOnly = false,
  categoryId,
  showTitle = true,
  title = 'Knowledge Base'
}) => {
  const [articles, setArticles] = useState<KnowledgeArticle[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchArticles = async () => {
      setIsLoading(true);
      try {
        const params: Record<string, any> = {
          status: 'PUBLISHED'
        };

        if (featuredOnly) {
          params.featured = true;
        }

        if (categoryId) {
          params.category = categoryId;
        }

        const response = await axios.get('/api/knowledge/articles', { params });
        
        // Limit the number of articles
        const limitedArticles = response.data.slice(0, limit);
        setArticles(limitedArticles);
        setError(null);
      } catch (err) {
        console.error('Error fetching knowledge base articles:', err);
        setError('Failed to load knowledge base articles');
      } finally {
        setIsLoading(false);
      }
    };

    fetchArticles();
  }, [limit, featuredOnly, categoryId]);

  if (isLoading) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <div className="animate-pulse flex space-x-4">
          <div className="flex-1 space-y-4 py-1">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <p className="text-red-500">{error}</p>
      </div>
    );
  }

  if (articles.length === 0) {
    return (
      <div className="p-4 bg-white rounded-lg shadow-md">
        <p className="text-gray-500">No knowledge base articles found.</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {showTitle && (
        <div className="px-6 py-4 bg-blue-50 border-b border-blue-100">
          <h2 className="text-xl font-semibold text-blue-800">{title}</h2>
        </div>
      )}
      <ul className="divide-y divide-gray-200">
        {articles.map(article => (
          <li key={article.id} className="hover:bg-gray-50">
            <Link to={`/knowledge/${article.id}`} className="block px-6 py-4">
              <div className="flex items-start space-x-4">
                {article.content && extractFirstImageUrl(article.content) ? (
                  <div className="flex-shrink-0 w-24 h-24">
                    <img
                      src={extractFirstImageUrl(article.content)!}
                      alt={article.title}
                      className="w-full h-full object-cover rounded-md"
                    />
                  </div>
                ) : (
                  <div className="flex-shrink-0 w-24 h-24 bg-gray-100 rounded-md flex items-center justify-center">
                    <FileText className="h-8 w-8 text-gray-400" />
                  </div>
                )}
                <div className="flex-1">
                  <h3 className="text-lg font-medium text-gray-900">{stripHtmlTags(article.title)}</h3>
                  {article.summary && (
                    <p className="mt-1 text-sm text-gray-600 line-clamp-2">{article.summary}</p>
                  )}
                  <div className="mt-2 flex items-center text-xs text-gray-500">
                    <span>{article.category.name}</span>
                    <span className="mx-2">•</span>
                    <span>{new Date(article.createdAt).toLocaleDateString()}</span>
                    {article.isFeatured && (
                      <>
                        <span className="mx-2">•</span>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          Featured
                        </span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </Link>
          </li>
        ))}
      </ul>
      <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
        <Link to="/knowledge" className="text-sm font-medium text-blue-600 hover:text-blue-500">
          View all articles
          <span aria-hidden="true"> &rarr;</span>
        </Link>
      </div>
    </div>
  );
};

export default KnowledgeBaseWidget; 