import React from 'react';

interface StepsProps {
  currentStep: number;
}

export const Steps: React.FC<StepsProps> = ({ currentStep }) => {
  const steps = [
    { number: 1, title: 'Basic Info' },
    { number: 2, title: 'Attributes' },
    { number: 3, title: 'Status' },
    { number: 4, title: 'Maintenance' },
  ];

  return (
    <div className="flex justify-between">
      {steps.map((step) => (
        <div
          key={step.number}
          className={`flex flex-col items-center ${
            currentStep >= step.number ? 'text-blue-500' : 'text-gray-400'
          }`}
        >
          <div
            className={`w-8 h-8 rounded-full flex items-center justify-center mb-2 ${
              currentStep >= step.number
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 text-gray-500'
            }`}
          >
            {step.number}
          </div>
          <span className="text-sm">{step.title}</span>
        </div>
      ))}
    </div>
  );
}; 