import React, { useState, useRef, useEffect } from 'react';
import { 
  Bold, Italic, List, ListOrdered, Heading1, Heading2, Image as ImageIcon, 
  Link, Code, Quote, Undo, Redo, AlignLeft, AlignCenter, AlignRight,
  Type, Palette
} from 'lucide-react';
import axios from 'axios';
import { toast } from 'react-hot-toast';

interface KnowledgeRichTextEditorProps {
  initialValue: string;
  onChange: (content: string) => void;
  onImageUpload?: (file: File) => Promise<string>;
}

interface ToolbarButtonProps {
  icon: React.ElementType;
  title: string;
  onClick: () => void;
  isActive?: boolean;
}

const ToolbarButton: React.FC<ToolbarButtonProps> = ({ icon: Icon, title, onClick, isActive }) => (
  <button 
    type="button" 
    onClick={onClick}
    className={`p-2 rounded hover:bg-gray-200 ${isActive ? 'bg-gray-200 text-blue-600' : ''}`}
    title={title}
  >
    <Icon size={18} />
  </button>
);

const fontImport = `
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Fira+Code&display=swap');
`;

// Color picker dropdown component
const ColorPicker: React.FC<{ onColorSelect: (color: string) => void }> = ({ onColorSelect }) => {
  const [isOpen, setIsOpen] = useState(false);
  
  const colors = [
    { name: 'Default', value: '#374151' },
    { name: 'Blue', value: '#2563eb' },
    { name: 'Red', value: '#dc2626' },
    { name: 'Green', value: '#16a34a' },
    { name: 'Purple', value: '#7c3aed' },
    { name: 'Orange', value: '#ea580c' },
    { name: 'Gray', value: '#6b7280' },
  ];
  
  return (
    <div className="relative">
      <button 
        type="button" 
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 rounded hover:bg-gray-200"
        title="Text Color"
      >
        <Palette size={18} />
      </button>
      
      {isOpen && (
        <div className="absolute z-10 mt-1 w-40 bg-white rounded-md shadow-lg border border-gray-200">
          <div className="p-2 grid grid-cols-4 gap-1">
            {colors.map((color) => (
              <button
                key={color.value}
                onClick={() => {
                  onColorSelect(color.value);
                  setIsOpen(false);
                }}
                className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center"
                style={{ backgroundColor: color.value }}
                title={color.name}
              >
                {color.name === 'Default' && <Type size={14} className="text-white" />}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

const KnowledgeRichTextEditor: React.FC<KnowledgeRichTextEditorProps> = ({ 
  initialValue, 
  onChange,
  onImageUpload
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const [content, setContent] = useState(initialValue);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isRTL, setIsRTL] = useState(false);

  useEffect(() => {
    if (editorRef.current) {
      // Set content only on initial load or when initialValue changes significantly
      if (!editorRef.current.innerHTML || editorRef.current.innerHTML !== initialValue) {
        editorRef.current.innerHTML = initialValue;
      }
      
      // Fix for text direction
      editorRef.current.dir = "ltr";
      
      // Add event listeners for better cursor handling
      editorRef.current.addEventListener('keydown', handleKeyDown);
      
      // Fix for bullet points disappearing
      fixBulletStyles();
    }
    
    return () => {
      // Clean up event listeners
      if (editorRef.current) {
        editorRef.current.removeEventListener('keydown', handleKeyDown);
      }
    };
  }, [initialValue]);
  
  // Function to fix bullet styles
  const fixBulletStyles = () => {
    // Add CSS to ensure bullets are always visible
    const style = document.createElement('style');
    style.textContent = `
      ul.editor-bullets {
        list-style-type: disc !important;
        padding-left: 2rem !important;
        margin: 1.25rem 0 !important;
      }
      
      ul.editor-bullets li {
        display: list-item !important;
        margin: 0.75rem 0 !important;
      }
      
      ol.editor-numbers {
        list-style-type: decimal !important;
        padding-left: 2rem !important;
        margin: 1.25rem 0 !important;
      }
      
      ol.editor-numbers li {
        display: list-item !important;
        margin: 0.75rem 0 !important;
      }
    `;
    document.head.appendChild(style);
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    // Special handling for backspace near images
    if (e.key === 'Backspace') {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const startContainer = range.startContainer;
        
        // Check if we're near an image
        if (startContainer.nodeType === Node.TEXT_NODE && 
            startContainer.textContent === '' && 
            startContainer.previousSibling && 
            startContainer.previousSibling.nodeName === 'IMG') {
          
          // Prevent default behavior
          e.preventDefault();
          
          // Remove the image
          startContainer.previousSibling.remove();
          
          // Trigger content change
          handleContentChange();
        }
      }
    }
  };

  // Track the last selection position
  const [lastSelection, setLastSelection] = useState<{
    node: Node | null;
    offset: number;
  }>({ node: null, offset: 0 });

  const handleContentChange = () => {
    if (editorRef.current) {
      // Save current selection before updating content
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        setLastSelection({
          node: range.startContainer,
          offset: range.startOffset
        });
      }
      
      // Ensure bullet points have proper classes and inline styles
      ensureBulletClasses();
      
      // Update content
      const newContent = editorRef.current.innerHTML;
      
      // Process content to ensure bullets are preserved
      const processedContent = preserveBulletPoints(newContent);
      
      setContent(processedContent);
      onChange(processedContent);
    }
  };
  
  // Function to ensure bullet points are preserved in the HTML
  const preserveBulletPoints = (html: string): string => {
    // Create a temporary div to manipulate the HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    
    // Process headings to control spacing
    const headings = tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach(heading => {
      heading.setAttribute('style', 'margin-top: 1rem; margin-bottom: 0.5rem; line-height: 1.3;');
    });
    
    // Process paragraphs to control spacing
    const paragraphs = tempDiv.querySelectorAll('p');
    paragraphs.forEach(p => {
      p.setAttribute('style', 'margin-bottom: 0.5rem; margin-top: 0.5rem; line-height: 1.5;');
    });
    
    // Process unordered lists
    const uls = tempDiv.querySelectorAll('ul');
    uls.forEach(ul => {
      ul.setAttribute('style', 'list-style-type: disc !important; padding-left: 1.5rem !important; margin: 0.25rem 0 !important;');
      ul.setAttribute('class', 'editor-bullets');
      
      // Process list items
      const lis = ul.querySelectorAll('li');
      lis.forEach(li => {
        li.setAttribute('style', 'display: list-item !important; margin: 0.25rem 0 !important; padding-left: 0.25rem !important; line-height: 1.5 !important;');
      });
    });
    
    // Process ordered lists
    const ols = tempDiv.querySelectorAll('ol');
    ols.forEach(ol => {
      ol.setAttribute('style', 'list-style-type: decimal !important; padding-left: 1.5rem !important; margin: 0.25rem 0 !important;');
      ol.setAttribute('class', 'editor-numbers');
      
      // Process list items
      const lis = ol.querySelectorAll('li');
      lis.forEach(li => {
        li.setAttribute('style', 'display: list-item !important; margin: 0.25rem 0 !important; padding-left: 0.25rem !important; line-height: 1.5 !important;');
      });
    });
    
    // Fix spacing between sections
    const sections = tempDiv.querySelectorAll('div');
    sections.forEach(section => {
      if (section.innerHTML.trim().startsWith('<h') || section.innerHTML.trim().startsWith('<p>')) {
        section.setAttribute('style', 'margin-bottom: 0.25rem; margin-top: 0.25rem;');
      }
    });
    
    // Remove any empty paragraphs that might be causing extra space
    const emptyParagraphs = tempDiv.querySelectorAll('p:empty');
    emptyParagraphs.forEach(p => {
      p.remove();
    });
    
    // Fix any br tags that might be causing extra space
    const brs = tempDiv.querySelectorAll('br');
    brs.forEach(br => {
      const next = br.nextSibling;
      const prev = br.previousSibling;
      if ((next && next.nodeName === 'BR') || (prev && prev.nodeName === 'BR')) {
        br.remove();
      }
    });
    
    return tempDiv.innerHTML;
  };

  // Function to ensure bullet points have proper classes and inline styles
  const ensureBulletClasses = () => {
    if (editorRef.current) {
      // Add classes and inline styles to unordered lists
      const uls = editorRef.current.querySelectorAll('ul');
      uls.forEach(ul => {
        if (!ul.classList.contains('editor-bullets')) {
          ul.classList.add('editor-bullets');
        }
        // Add inline styles that will persist after publishing
        ul.setAttribute('style', 'list-style-type: disc !important; padding-left: 1.5rem !important; margin: 0.25rem 0 !important;');
        
        // Fix list items
        const lis = ul.querySelectorAll('li');
        lis.forEach(li => {
          li.setAttribute('style', 'display: list-item !important; margin: 0.25rem 0 !important; padding-left: 0.25rem !important; line-height: 1.5 !important;');
        });
      });
      
      // Add classes and inline styles to ordered lists
      const ols = editorRef.current.querySelectorAll('ol');
      ols.forEach(ol => {
        if (!ol.classList.contains('editor-numbers')) {
          ol.classList.add('editor-numbers');
        }
        // Add inline styles that will persist after publishing
        ol.setAttribute('style', 'list-style-type: decimal !important; padding-left: 1.5rem !important; margin: 0.25rem 0 !important;');
        
        // Fix list items
        const lis = ol.querySelectorAll('li');
        lis.forEach(li => {
          li.setAttribute('style', 'display: list-item !important; margin: 0.25rem 0 !important; padding-left: 0.25rem !important; line-height: 1.5 !important;');
        });
      });
      
      // Fix spacing for headings
      const headings = editorRef.current.querySelectorAll('h1, h2, h3, h4, h5, h6');
      headings.forEach(heading => {
        heading.setAttribute('style', 'margin-top: 1rem; margin-bottom: 0.5rem; line-height: 1.3;');
      });
      
      // Fix spacing for paragraphs
      const paragraphs = editorRef.current.querySelectorAll('p');
      paragraphs.forEach(p => {
        p.setAttribute('style', 'margin-bottom: 0.5rem; margin-top: 0.5rem; line-height: 1.5;');
      });
      
      // Remove any empty paragraphs that might be causing extra space
      const emptyParagraphs = editorRef.current.querySelectorAll('p:empty');
      emptyParagraphs.forEach(p => {
        p.remove();
      });
      
      // Fix any br tags that might be causing extra space
      const brs = editorRef.current.querySelectorAll('br');
      brs.forEach(br => {
        const next = br.nextSibling;
        const prev = br.previousSibling;
        if ((next && next.nodeName === 'BR') || (prev && prev.nodeName === 'BR')) {
          br.remove();
        }
      });
    }
  };

  // Function to restore cursor position
  const restoreCursor = () => {
    if (lastSelection.node && editorRef.current?.contains(lastSelection.node)) {
      try {
        const selection = window.getSelection();
        if (selection) {
          const range = document.createRange();
          range.setStart(lastSelection.node, lastSelection.offset);
          range.collapse(true);
          selection.removeAllRanges();
          selection.addRange(range);
          editorRef.current?.focus();
        }
      } catch (e) {
        console.error("Error restoring cursor:", e);
      }
    }
  };

  const execCommand = (command: string, value: string = '') => {
    // Focus the editor first
    editorRef.current?.focus();
    
    // Save selection
    const selection = window.getSelection();
    let savedRange = null;
    if (selection && selection.rangeCount > 0) {
      savedRange = selection.getRangeAt(0).cloneRange();
    }
    
    // Execute the command
    document.execCommand(command, false, value);
    
    // Special handling for lists to ensure they have proper classes
    if (command === 'insertUnorderedList') {
      const ul = getSelectedListElement();
      if (ul && ul.tagName === 'UL') {
        ul.classList.add('editor-bullets');
      }
    } else if (command === 'insertOrderedList') {
      const ol = getSelectedListElement();
      if (ol && ol.tagName === 'OL') {
        ol.classList.add('editor-numbers');
      }
    }
    
    // Update content
    handleContentChange();
    
    // Make sure editor keeps focus
    editorRef.current?.focus();
    
    // Try to restore selection if possible
    if (savedRange) {
      try {
        selection?.removeAllRanges();
        selection?.addRange(savedRange);
      } catch (e) {
        console.error("Error restoring selection after command:", e);
      }
    }
  };
  
  // Helper function to get the list element at current selection
  const getSelectedListElement = (): HTMLElement | null => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return null;
    
    let node = selection.anchorNode;
    while (node && node !== editorRef.current) {
      if (node.nodeName === 'UL' || node.nodeName === 'OL') {
        return node as HTMLElement;
      }
      node = node.parentNode;
    }
    return null;
  };

  const handleUnorderedList = () => {
    execCommand('insertUnorderedList');
    // Additional handling to ensure proper styling
    setTimeout(() => {
      ensureBulletClasses();
      handleContentChange();
    }, 10);
  };
  
  const handleOrderedList = () => {
    execCommand('insertOrderedList');
    // Additional handling to ensure proper styling
    setTimeout(() => {
      ensureBulletClasses();
      handleContentChange();
    }, 10);
  };

  const handleBold = () => execCommand('bold');
  const handleItalic = () => execCommand('italic');
  const handleH1 = () => execCommand('formatBlock', '<h1>');
  const handleH2 = () => execCommand('formatBlock', '<h2>');
  const handleH3 = () => execCommand('formatBlock', '<h3>');
  const handleParagraph = () => execCommand('formatBlock', '<p>');
  const handleQuote = () => execCommand('formatBlock', '<blockquote>');
  const handleCode = () => {
    const selection = window.getSelection();
    if (selection && selection.toString()) {
      execCommand('insertHTML', `<pre><code>${selection.toString()}</code></pre>`);
    } else {
      execCommand('insertHTML', '<pre><code></code></pre>');
    }
  };
  const handleUndo = () => execCommand('undo');
  const handleRedo = () => execCommand('redo');
  const handleAlignLeft = () => execCommand('justifyLeft');
  const handleAlignCenter = () => execCommand('justifyCenter');
  const handleAlignRight = () => execCommand('justifyRight');
  
  // Text direction handler
  const toggleTextDirection = () => {
    const newDirection = !isRTL;
    setIsRTL(newDirection);
    if (editorRef.current) {
      editorRef.current.dir = newDirection ? "rtl" : "ltr";
    }
  };

  const handleLink = () => {
    const url = prompt('Enter URL:');
    if (url) {
      execCommand('createLink', url);
    }
  };
  
  // Text color handler
  const handleTextColor = (color: string) => {
    execCommand('foreColor', color);
  };

  const handleImageClick = () => {
    fileInputRef.current?.click();
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    setIsUploading(true);
    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        
        // Check file size (limit to 5MB)
        if (file.size > 5 * 1024 * 1024) {
          toast.error(`File ${file.name} is too large. Maximum size is 5MB.`);
          continue;
        }
        
        // Check file type
        if (!['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)) {
          toast.error(`File ${file.name} is not a supported image type.`);
          continue;
        }
        
        // If custom upload handler is provided
        if (onImageUpload) {
          try {
            const imageUrl = await onImageUpload(file);
            execCommand('insertHTML', `<img src="${imageUrl}" alt="${file.name}" style="max-width: 100%; height: auto; margin: 10px 0;" />`);
            toast.success(`Image ${file.name} uploaded successfully`);
          } catch (error: any) {
            console.error('Error uploading image:', error);
            toast.error(typeof error.message === 'string' ? error.message : `Failed to upload image ${file.name}`);
          }
        } else {
          // Default upload using FormData
          const formData = new FormData();
          formData.append('image', file);
          
          try {
            const token = localStorage.getItem('authToken');
            const response = await axios.post('/api/knowledge/upload-images', formData, {
              headers: {
                'Content-Type': 'multipart/form-data',
                'Authorization': token ? `Bearer ${token}` : ''
              },
              withCredentials: true
            });
            
            const imageUrl = response.data.fileUrl || response.data.url;
            execCommand('insertHTML', `<img src="${imageUrl}" alt="${file.name}" style="max-width: 100%; height: auto; margin: 10px 0;" />`);
            toast.success(`Image ${file.name} uploaded successfully`);
          } catch (error: any) {
            console.error('Error uploading image:', error);
            toast.error(typeof error.message === 'string' ? error.message : `Failed to upload image ${file.name}`);
          }
        }
      }
    } catch (error: any) {
      console.error('Error processing images:', error);
      toast.error(typeof error.message === 'string' ? error.message : 'Failed to process images');
    } finally {
      setIsUploading(false);
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  return (
    <div className="border border-gray-300 rounded-md overflow-hidden">
      <style>{fontImport}</style>
      
      <div className="bg-gray-50 border-b border-gray-300 p-2 flex flex-wrap gap-1">
        <ToolbarButton icon={Bold} title="Bold" onClick={handleBold} />
        <ToolbarButton icon={Italic} title="Italic" onClick={handleItalic} />
        <div className="h-6 w-px bg-gray-300 mx-1"></div>
        <ToolbarButton icon={Heading1} title="Heading 1" onClick={handleH1} />
        <ToolbarButton icon={Heading2} title="Heading 2" onClick={handleH2} />
        <div className="h-6 w-px bg-gray-300 mx-1"></div>
        <ToolbarButton icon={List} title="Bullet List" onClick={handleUnorderedList} />
        <ToolbarButton icon={ListOrdered} title="Numbered List" onClick={handleOrderedList} />
        <ToolbarButton icon={Quote} title="Quote" onClick={handleQuote} />
        <ToolbarButton icon={Code} title="Code Block" onClick={handleCode} />
        <div className="h-6 w-px bg-gray-300 mx-1"></div>
        <ToolbarButton icon={Link} title="Insert Link" onClick={handleLink} />
        <ToolbarButton 
          icon={ImageIcon} 
          title="Insert Image" 
          onClick={handleImageClick} 
        />
        <div className="h-6 w-px bg-gray-300 mx-1"></div>
        <ToolbarButton icon={AlignLeft} title="Align Left" onClick={handleAlignLeft} />
        <ToolbarButton icon={AlignCenter} title="Align Center" onClick={handleAlignCenter} />
        <ToolbarButton icon={AlignRight} title="Align Right" onClick={handleAlignRight} />
        <div className="h-6 w-px bg-gray-300 mx-1"></div>
        <ColorPicker onColorSelect={handleTextColor} />
        <div className="h-6 w-px bg-gray-300 mx-1"></div>
        <ToolbarButton icon={Undo} title="Undo" onClick={handleUndo} />
        <ToolbarButton icon={Redo} title="Redo" onClick={handleRedo} />
      </div>
      
      <style>
      {`
        .editor-content {
          color: #374151;
          line-height: 1.5;
          font-size: 1.125rem;
          white-space: pre-wrap;
          word-wrap: break-word;
          font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }
        
        .editor-content p {
          margin-bottom: 0.5rem;
          margin-top: 0.5rem;
          color: #4b5563;
          min-height: 1.5em;
          line-height: 1.5;
          padding: 0;
        }
        
        .editor-content h1 {
          font-size: 2rem;
          font-weight: 700;
          margin-top: 1rem;
          margin-bottom: 0.5rem;
          color: #111827;
          line-height: 1.3;
          letter-spacing: -0.025em;
          border-bottom: 2px solid #e5e7eb;
          padding-bottom: 0.5rem;
          font-family: 'Inter', system-ui, sans-serif;
        }
        
        .editor-content h2 {
          font-size: 1.5rem;
          font-weight: 600;
          margin-top: 1rem;
          margin-bottom: 0.5rem;
          color: #1f2937;
          line-height: 1.3;
          letter-spacing: -0.025em;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 0.5rem;
          font-family: 'Inter', system-ui, sans-serif;
        }
        
        .editor-content h3 {
          font-size: 1.25rem;
          font-weight: 600;
          margin-top: 1rem;
          margin-bottom: 0.5rem;
          color: #1f2937;
          line-height: 1.3;
          letter-spacing: -0.025em;
          background-color: #f9fafb;
          padding: 0.5rem 0.75rem;
          border-radius: 0.5rem;
          border-left: 4px solid #3b82f6;
          font-family: 'Inter', system-ui, sans-serif;
        }
        
        .editor-content strong {
          color: #111827;
          font-weight: 700;
        }
        
        .editor-content a {
          color: #2563eb;
          text-decoration: none;
          border-bottom: 1px solid #2563eb;
          transition: all 0.2s ease;
        }
        
        .editor-content a:hover {
          color: #1d4ed8;
          border-bottom: 1px solid #1d4ed8;
        }
        
        .editor-content ul, .editor-content ol {
          margin: 0.25rem 0;
          padding-left: 1.5rem;
          color: #4b5563;
          list-style-position: outside;
        }
        
        .editor-content ul, .editor-bullets {
          list-style-type: disc !important;
        }
        
        .editor-content ol, .editor-numbers {
          list-style-type: decimal !important;
        }
        
        .editor-content li, .editor-bullets li, .editor-numbers li {
          margin: 0.25rem 0;
          padding-left: 0.25rem;
          line-height: 1.5;
          display: list-item !important;
        }
        
        .editor-content li p {
          margin-bottom: 0.25rem;
          margin-top: 0.25rem;
        }
        
        .editor-content blockquote {
          border-left: 4px solid #3b82f6;
          padding: 1rem 1.25rem;
          margin: 1rem 0;
          background: #f3f4f6;
          border-radius: 0.75rem;
          font-style: italic;
          color: #374151;
          line-height: 1.5;
        }
        
        .editor-content code {
          background: #f3f4f6;
          padding: 0.2rem 0.4rem;
          border-radius: 0.375rem;
          font-size: 0.9rem;
          color: #1f2937;
          font-family: 'Fira Code', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
          border: 1px solid #e5e7eb;
        }
        
        .editor-content pre {
          background: #f8fafc;
          padding: 1.25rem;
          border-radius: 0.75rem;
          overflow-x: auto;
          margin: 1rem 0;
          border: 1px solid #e5e7eb;
        }
        
        .editor-content pre code {
          background: transparent;
          padding: 0;
          border: none;
          font-size: 0.9rem;
          line-height: 1.5;
          color: #1f2937;
        }
        
        .editor-content img {
          max-width: 100%;
          height: auto;
          border-radius: 0.75rem;
          margin: 1rem 0;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        /* Fix for bullet points */
        .editor-content ul li::marker,
        .editor-bullets li::marker {
          color: #3b82f6;
          font-size: 1.2em;
        }
        
        .editor-content ol li::marker,
        .editor-numbers li::marker {
          color: #3b82f6;
          font-weight: 600;
        }
        
        /* Fix for empty paragraphs */
        .editor-content p:empty {
          display: none;
        }
        
        /* Fix for consecutive br tags */
        .editor-content br + br {
          display: none;
        }
      `}
      </style>
      
      <div
        ref={editorRef}
        contentEditable
        suppressContentEditableWarning={true}
        className="p-8 min-h-[400px] focus:outline-none prose prose-sm max-w-none whitespace-pre-wrap editor-content"
        style={{ 
          whiteSpace: 'pre-wrap', 
          wordWrap: 'break-word',
          maxWidth: '800px',
          margin: '0 auto',
          maxHeight: '600px',
          overflowY: 'auto',
          overflowX: 'hidden',
          fontFamily: "'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
          fontSize: '1.125rem',
          lineHeight: '1.5',
          direction: isRTL ? 'rtl' : 'ltr'
        }}
        onInput={(e) => {
          e.stopPropagation();
          handleContentChange();
        }}
        onBlur={handleContentChange}
        onFocus={() => {
          // If editor is empty, place cursor at beginning
          if (editorRef.current && editorRef.current.innerHTML === '') {
            const range = document.createRange();
            range.setStart(editorRef.current, 0);
            range.collapse(true);
            const selection = window.getSelection();
            if (selection) {
              selection.removeAllRanges();
              selection.addRange(range);
            }
          } else if (lastSelection.node) {
            // Otherwise try to restore last known position
            restoreCursor();
          }
        }}
        onClick={(e) => {
          // Update last selection on click
          const selection = window.getSelection();
          if (selection && selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            setLastSelection({
              node: range.startContainer,
              offset: range.startOffset
            });
          }
          
          // Prevent click from causing cursor to jump
          e.stopPropagation();
        }}
        onKeyDown={(e) => {
          // Update last selection on key press
          setTimeout(() => {
            const selection = window.getSelection();
            if (selection && selection.rangeCount > 0) {
              const range = selection.getRangeAt(0);
              setLastSelection({
                node: range.startContainer,
                offset: range.startOffset
              });
            }
          }, 0);
          
          // Fix for keyboard shortcuts
          if (e.key === 'Tab') {
            e.preventDefault();
            document.execCommand('insertHTML', false, '&nbsp;&nbsp;&nbsp;&nbsp;');
          }
          
          // Enhanced fix for bullet lists with keyboard shortcut
          if ((e.key === '*' || e.key === '-') && e.ctrlKey) {
            e.preventDefault();
            handleUnorderedList();
          }
          
          // Enhanced fix for numbered lists with keyboard shortcut
          if (e.key === '1' && e.ctrlKey) {
            e.preventDefault();
            handleOrderedList();
          }
        }}
      />
      
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleImageUpload}
        className="hidden"
        accept="image/jpeg,image/png,image/gif,image/webp"
        multiple
      />
      
      {isUploading && (
        <div className="p-2 bg-blue-50 text-blue-700 text-sm flex items-center">
          <svg className="animate-spin h-4 w-4 mr-2 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Uploading image...
        </div>
      )}
    </div>
  );
};

export default KnowledgeRichTextEditor; 