// User Permission Constants
// This file defines all permissions available in the system

export enum UserPermissions {
  // Ticket permissions
  canCreateTickets = 'canCreateTickets',
  canEditTickets = 'canEditTickets',
  canDeleteTickets = 'canDeleteTickets',
  canViewTickets = 'canViewTickets',
  canCloseTickets = 'canCloseTickets',
  canLockTickets = 'canLockTickets',
  canAssignTickets = 'canAssignTickets',
  canEscalateTickets = 'canEscalateTickets',
  canViewAllTickets = 'canViewAllTickets',
  canCreateTicketsForOthers = 'canCreateTicketsForOthers',
  
  // User permissions
  canCreateUser = 'canCreateUser',
  canEditUser = 'canEditUser',
  canDeleteUser = 'canDeleteUser',
  canViewUser = 'canViewUser',
  canAddUsers = 'canAddUsers',
  canEditUsers = 'canEditUsers',
  canDeleteUsers = 'canDeleteUsers',
  
  // Employee permissions
  canCreateEmployee = 'canCreateEmployee',
  canEditEmployee = 'canEditEmployee',
  canDeleteEmployee = 'canDeleteEmployee',
  canViewEmployees = 'canViewEmployees',
  
  // Attendance and HR permissions
  canManageAttendance = 'canManageAttendance',
  canManageLeave = 'canManageLeave',
  canManagePayroll = 'canManagePayroll',
  canManagePerformance = 'canManagePerformance',
  
  // Report permissions
  canViewReports = 'canViewReports',
  canExportData = 'canExportData',
  canImportData = 'canImportData',
  
  // Admin permissions
  isAdmin = 'isAdmin',
  canManageSettings = 'canManageSettings',
  canConfigureSystem = 'canConfigureSystem',
  canManageRoles = 'canManageRoles',
  canApproveRequests = 'canApproveRequests',
  canViewAllDepartments = 'canViewAllDepartments',
  canAccessAllModules = 'canAccessAllModules',
  canAccessServiceDesk = 'canAccessServiceDesk'
}

export default UserPermissions; 