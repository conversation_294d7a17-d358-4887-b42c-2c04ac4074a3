import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { Employee } from './Employee';

@Entity('employee_experience')
export class EmployeeExperience {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 100,  nullable: false })
  companyName: string;

  @Column({ type: 'varchar', length: 100,  nullable: false })
  jobTitle: string;

  @Column({ type: 'date',  nullable: false })
  startDate: string;

  @Column({ type: 'date',  nullable: true })
  endDate: string;

  @Column({ type: 'boolean',  nullable: false, default: false })
  currentlyWorking: boolean;

  @Column({ type: 'text', nullable: true })
  jobDescription: string;

  // Relation to Employee
  @ManyToOne(() => Employee, employee => employee.experience, { onDelete: 'CASCADE' })
  @JoinColumn()
  employee: Employee;

  // System columns
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 