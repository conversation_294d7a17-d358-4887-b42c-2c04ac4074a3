import React, { useState, useEffect } from 'react';
import { Attendance, AttendanceStatus, AttendanceSummary } from '../../../types/attendance';
import AttendanceService from '../../../services/AttendanceService';
import EmployeeService from '../../../services/EmployeeService';
import { 
  BarChart3, 
  FileDown, 
  Calendar as CalendarIcon, 
  Users, 
  Clock, 
  Download,
  FileText,
  PieChart,
  TrendingUp,
  Filter,
  Briefcase,
  Clock7,
  AlertTriangle,
  Activity,
  Target,
  CheckCircle2,
  XCircle,
  Timer,
  Building2,
  UserCheck,
  Calendar,
  ArrowUp,
  ArrowDown,
  Minus,
  RefreshCw,
  Eye,
  MoreHorizontal,
  Search,
  Settings,
  Plus
} from 'lucide-react';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface AttendanceReportProps {
  // Remove attendances prop - we'll fetch from database
}

interface AttendanceSummaryCardsProps {
  attendances: Attendance[];
  startDate?: string;
  endDate?: string;
  selectedDepartment?: string;
  selectedEmployee?: number | null;
}

// Exported component for summary cards that can be used independently
export const AttendanceSummaryCards: React.FC<AttendanceSummaryCardsProps> = ({ 
  attendances,
  startDate,
  endDate,
  selectedDepartment = 'All',
  selectedEmployee = null
}) => {
  const [summary, setSummary] = useState<AttendanceSummary>({
    totalPresent: 0,
    totalAbsent: 0,
    totalLate: 0,
    totalHalfDay: 0,
    totalLeave: 0,
    totalWorkingDays: 0,
    requiredWorkingDays: 0,
    presentPercentage: 0,
    totalWorkHours: 0,
    averageWorkHours: 0,
    totalOvertime: 0,
    lateArrivalCount: 0,
    earlyDepartureCount: 0,
    consecutiveAbsences: 0,
    longestStreak: 0
  });

  // Set default date range if not provided
  const defaultStartDate = startDate || new Date(new Date().setDate(1)).toISOString().split('T')[0];
  const defaultEndDate = endDate || new Date().toISOString().split('T')[0];

  // Get unique employees
  const employees = [...new Map(attendances.map(a => 
    [a.employeeId, { id: a.employeeId, name: a.employeeName }]
  )).values()];

  useEffect(() => {
    // Calculate summary based on filters
    let filteredAttendances = [...attendances];
    
    // Filter by date range
    filteredAttendances = filteredAttendances.filter(a => 
      a.date >= defaultStartDate && a.date <= defaultEndDate
    );
    
    // Filter by department if not "All"
    if (selectedDepartment !== 'All') {
      filteredAttendances = filteredAttendances.filter(a => 
        a.employeeDepartment === selectedDepartment || a.department === selectedDepartment
      );
    }
    
    // Filter by employee if selected
    if (selectedEmployee) {
      filteredAttendances = filteredAttendances.filter(a => 
        a.employeeId === selectedEmployee
      );
    }
    
    const totalPresent = filteredAttendances.filter(a => 
      a.status === AttendanceStatus.PRESENT || 
      a.status === AttendanceStatus.WORK_FROM_HOME
    ).length;
    
    const totalAbsent = filteredAttendances.filter(a => 
      a.status === AttendanceStatus.ABSENT
    ).length;
    
    const totalLate = filteredAttendances.filter(a => 
      a.status === AttendanceStatus.LATE
    ).length;
    
    const totalHalfDay = filteredAttendances.filter(a => 
      a.status === AttendanceStatus.HALF_DAY
    ).length;
    
    const totalLeave = filteredAttendances.filter(a => 
      a.status === AttendanceStatus.LEAVE ||
      a.status === AttendanceStatus.SICK_LEAVE ||
      a.status === AttendanceStatus.ANNUAL_LEAVE ||
      a.status === AttendanceStatus.PAID_TIME_OFF ||
      a.status === AttendanceStatus.UNPAID_LEAVE ||
      a.status === AttendanceStatus.MATERNITY_LEAVE ||
      a.status === AttendanceStatus.PATERNITY_LEAVE
    ).length;
    
    const totalWorkHours = filteredAttendances.reduce((sum, a) => 
      sum + (a.workHours || 0), 0
    );
    
    const totalOvertime = filteredAttendances.reduce((sum, a) => 
      sum + (a.overtime || 0), 0
    );
    
    // Calculate total work days in the period (excluding weekends)
    const startDateObj = new Date(defaultStartDate);
    const endDateObj = new Date(defaultEndDate);
    let totalWorkingDays = 0;
    let currentDate = new Date(startDateObj);
    
    while (currentDate <= endDateObj) {
      const dayOfWeek = currentDate.getDay();
      if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Not Sunday or Saturday
        totalWorkingDays++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    const presentPercentage = totalWorkingDays > 0 
      ? (totalPresent / (totalWorkingDays * employees.length)) * 100 
      : 0;
    
    // Update summary
    setSummary({
      totalPresent,
      totalAbsent,
      totalLate,
      totalHalfDay,
      totalLeave,
      totalWorkingDays,
      requiredWorkingDays: totalWorkingDays * employees.length,
      presentPercentage,
      totalWorkHours,
      averageWorkHours: totalPresent > 0 ? totalWorkHours / totalPresent : 0,
      totalOvertime,
      lateArrivalCount: totalLate,
      earlyDepartureCount: 0,
      consecutiveAbsences: 0,
      longestStreak: 0
    });
    
  }, [attendances, defaultStartDate, defaultEndDate, selectedDepartment, selectedEmployee, employees.length]);

  // Apply comprehensive filtering function
  const getFilteredAttendances = () => {
    return attendances.filter(a => {
      // Date range filter
      if (a.date < defaultStartDate || a.date > defaultEndDate) return false;
      
      // Department filter
      if (selectedDepartment !== 'All' && 
          a.employeeDepartment !== selectedDepartment && 
          a.department !== selectedDepartment) return false;
      
      // Employee filter
      if (selectedEmployee && a.employeeId !== selectedEmployee) return false;
      
      return true;
    });
  };

  // Check if we have any data
  const hasData = getFilteredAttendances().length > 0;

  if (!hasData) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-8 transition-colors duration-200">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gray-100 dark:bg-gray-700 mb-4">
            <BarChart3 className="h-8 w-8 text-gray-400 dark:text-gray-500" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Attendance Data</h3>
          <p className="text-gray-500 dark:text-gray-400">
            No attendance records available for the selected period and filters.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 hover:shadow-md transition-all duration-200">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center">
              <div className="p-2 bg-emerald-50 dark:bg-emerald-900/30 rounded-lg mr-3">
                <UserCheck className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Present</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{summary.totalPresent}</p>
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-sm font-medium text-emerald-600 dark:text-emerald-400 bg-emerald-50 dark:bg-emerald-900/30 px-2 py-1 rounded-full">
                {summary.presentPercentage.toFixed(1)}%
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">attendance rate</span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 hover:shadow-md transition-all duration-200">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center">
              <div className="p-2 bg-red-50 dark:bg-red-900/30 rounded-lg mr-3">
                <XCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Absences</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{summary.totalAbsent}</p>
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-sm font-medium text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/30 px-2 py-1 rounded-full">
                {summary.requiredWorkingDays > 0 ? ((summary.totalAbsent / summary.requiredWorkingDays) * 100).toFixed(1) : 0}%
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">absence rate</span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 hover:shadow-md transition-all duration-200">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center">
              <div className="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-lg mr-3">
                <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Work Hours</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{summary.totalWorkHours.toFixed(0)}</p>
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 px-2 py-1 rounded-full">
                {summary.averageWorkHours.toFixed(1)}h
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">avg per day</span>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 hover:shadow-md transition-all duration-200">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center">
              <div className="p-2 bg-purple-50 dark:bg-purple-900/30 rounded-lg mr-3">
                <Timer className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">Overtime</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{summary.totalOvertime.toFixed(0)}</p>
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-sm font-medium text-purple-600 dark:text-purple-400 bg-purple-50 dark:bg-purple-900/30 px-2 py-1 rounded-full">
                {summary.totalOvertime > 0 ? `${(summary.totalOvertime / (summary.totalPresent || 1)).toFixed(1)}h` : '0h'}
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">avg overtime</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const AttendanceReport: React.FC<AttendanceReportProps> = () => {
  const [reportType, setReportType] = useState<'summary' | 'trends' | 'department' | 'export'>('summary');
  const [startDate, setStartDate] = useState<string>(
    new Date(new Date().setDate(1)).toISOString().split('T')[0]
  );
  const [endDate, setEndDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );
  const [selectedDepartment, setSelectedDepartment] = useState<string>('All');
  const [selectedEmployee, setSelectedEmployee] = useState<number | null>(null);
  const [showFilters, setShowFilters] = useState(false);

  // Enhanced filter state to match AttendanceManagement
  const [selectedPosition, setSelectedPosition] = useState<string>('');
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [selectedShift, setSelectedShift] = useState<string>('');
  const [datePeriod, setDatePeriod] = useState<string>('This Month');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [userDefinedDates, setUserDefinedDates] = useState({
    start: startDate,
    end: endDate
  });

  // Add state for database data
  const [attendances, setAttendances] = useState<Attendance[]>([]);
  const [employees, setEmployees] = useState<Array<{ id: number; name: string; department: string }>>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Calculate unique values for filters from attendance data
  const uniqueDepartments = React.useMemo(() => {
    const departments = new Set<string>();
    attendances.forEach(attendance => {
      const dept = attendance.department || attendance.employeeDepartment;
      if (dept && dept.trim() !== '' && dept !== 'N/A') {
        const normalizedDept = dept.trim().replace(/\s+/g, ' ');
        departments.add(normalizedDept);
      }
    });
    return Array.from(departments).sort();
  }, [attendances]);

  const uniquePositions = React.useMemo(() => {
    const positions = new Set<string>();
    attendances.forEach(attendance => {
      const position = (attendance as any).designation || (attendance as any).employeeDesignation || attendance.position;
      if (position && position.trim() !== '' && position !== 'N/A') {
        const normalizedPosition = position.trim().replace(/\s+/g, ' ');
        positions.add(normalizedPosition);
      }
    });
    return Array.from(positions).sort();
  }, [attendances]);

  const uniqueLocations = React.useMemo(() => {
    const locations = new Set<string>();
    attendances.forEach(attendance => {
      let location = attendance.location;
      if (!location || location.trim() === '' || location === 'N/A') {
        location = attendance.isRemote ? 'Remote' : 'Office';
      }
      const normalizedLocation = location.trim().replace(/\s+/g, ' ');
      if (normalizedLocation !== '') {
        locations.add(normalizedLocation);
      }
    });
    return Array.from(locations).sort();
  }, [attendances]);

  const uniqueShifts = React.useMemo(() => {
    const shifts = new Set<string>();
    attendances.forEach(attendance => {
      if (attendance.shiftName && attendance.shiftName.trim() !== '') {
        shifts.add(attendance.shiftName);
      } else if (attendance.shift) {
        const shiftNames = {
          1: 'Morning Shift',
          2: 'Evening Shift', 
          3: 'Night Shift',
          4: 'Flexible Hours'
        };
        const shiftName = shiftNames[attendance.shift as keyof typeof shiftNames] || `Shift ${attendance.shift}`;
        shifts.add(shiftName);
      }
    });
    return Array.from(shifts).sort();
  }, [attendances]);

  const uniqueEmployees = React.useMemo(() => {
    const employees = new Map<number, string>();
    attendances.forEach(attendance => {
      if (attendance.employeeId && attendance.employeeName) {
        employees.set(attendance.employeeId, attendance.employeeName);
      }
    });
    return Array.from(employees.entries()).map(([id, name]) => ({ id, name })).sort((a, b) => a.name.localeCompare(b.name));
  }, [attendances]);

  // Helper function to format date as YYYY-MM-DD in local timezone
  const formatLocalDate = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Handle date period changes
  const handleDatePeriodChange = (period: string) => {
    setDatePeriod(period);
    
    const today = new Date();
    let newStartDate: string;
    let newEndDate: string;
    
    switch (period) {
      case "Today":
        newStartDate = newEndDate = formatLocalDate(today);
        break;
      case "Yesterday":
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        newStartDate = newEndDate = formatLocalDate(yesterday);
        break;
      case "This Week":
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay());
        newStartDate = formatLocalDate(startOfWeek);
        newEndDate = formatLocalDate(today);
        break;
      case "Last Week":
        const lastWeekStart = new Date(today);
        lastWeekStart.setDate(today.getDate() - today.getDay() - 7);
        const lastWeekEnd = new Date(lastWeekStart);
        lastWeekEnd.setDate(lastWeekStart.getDate() + 6);
        newStartDate = formatLocalDate(lastWeekStart);
        newEndDate = formatLocalDate(lastWeekEnd);
        break;
      case "This Month":
        newStartDate = formatLocalDate(new Date(today.getFullYear(), today.getMonth(), 1));
        newEndDate = formatLocalDate(today);
        break;
      case "Last Month":
        const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
        newStartDate = formatLocalDate(lastMonth);
        newEndDate = formatLocalDate(lastMonthEnd);
        break;
      case "Last Three Months":
        const threeMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 3, 1);
        newStartDate = formatLocalDate(threeMonthsAgo);
        newEndDate = formatLocalDate(new Date(today.getFullYear(), today.getMonth(), 0));
        break;
      case "Last Six Months":
        const sixMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 6, 1);
        newStartDate = formatLocalDate(sixMonthsAgo);
        newEndDate = formatLocalDate(new Date(today.getFullYear(), today.getMonth(), 0));
        break;
      case "This Year":
        newStartDate = formatLocalDate(new Date(today.getFullYear(), 0, 1));
        newEndDate = formatLocalDate(today);
        break;
      case "Last Year":
        newStartDate = formatLocalDate(new Date(today.getFullYear() - 1, 0, 1));
        newEndDate = formatLocalDate(new Date(today.getFullYear() - 1, 11, 31));
        break;
      case "User Defined":
        // Don't change dates for user defined
        return;
      default:
        return;
    }
    
    setStartDate(newStartDate);
    setEndDate(newEndDate);
  };

  // Reset all filters
  const resetFilters = () => {
    setSelectedDepartment('All');
    setSelectedEmployee(null);
    setSelectedPosition('');
    setSelectedLocation('');
    setSelectedStatus('');
    setSelectedShift('');
    setSearchQuery('');
    setDatePeriod('This Month');
    
    // Reset to current month
    const today = new Date();
    const startOfMonth = formatLocalDate(new Date(today.getFullYear(), today.getMonth(), 1));
    const endOfMonth = formatLocalDate(today);
    setStartDate(startOfMonth);
    setEndDate(endOfMonth);
  };

  // Fetch employees from database
  const fetchEmployees = async () => {
    try {
      const result = await EmployeeService.getEmployees();
      if (result.data?.employees) {
        const formattedEmployees = result.data.employees.map((emp: any) => ({
          id: emp.id,
          name: `${emp.firstName} ${emp.lastName}`,
          department: emp.department || 'Unknown'
        }));
        setEmployees(formattedEmployees);
      }
    } catch (err) {
      console.error('Error fetching employees:', err);
      setError('Failed to fetch employees');
    }
  };

  // Fetch attendance data from database
  const fetchAttendances = async () => {
    try {
      setLoading(true);
      const result = await AttendanceService.getAttendances({
        startDate,
        endDate,
        ...(selectedEmployee && { employeeId: selectedEmployee }),
        ...(selectedDepartment !== 'All' && { department: selectedDepartment })
      });
      
      if (result.data) {
        console.log('📊 AttendanceReport - Fetched attendance data:', {
          totalRecords: result.data.length,
          dateRange: { startDate, endDate },
          filters: { selectedEmployee, selectedDepartment },
          sampleData: result.data.slice(0, 3),
          uniqueDepartments: [...new Set(result.data.map(a => a.employeeDepartment || a.department))],
          uniqueEmployees: [...new Set(result.data.map(a => ({ id: a.employeeId, name: a.employeeName })))]
        });
        setAttendances(result.data);
      } else if (result.error) {
        setError(result.error);
      }
    } catch (err) {
      console.error('Error fetching attendances:', err);
      setError('Failed to fetch attendance data');
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchEmployees();
  }, []);

  // Fetch attendances when filters change
  useEffect(() => {
    fetchAttendances();
  }, [startDate, endDate, selectedEmployee, selectedDepartment]);

  // Comprehensive filtering function that applies all filters
  const getFilteredAttendances = () => {
    return attendances.filter((a: Attendance) => {
      // Date range filter
      if (a.date < startDate || a.date > endDate) return false;
      
      // Department filter
      if (selectedDepartment !== 'All' && 
          a.employeeDepartment !== selectedDepartment && 
          (a as any).department !== selectedDepartment) return false;
      
      // Employee filter
      if (selectedEmployee && a.employeeId !== selectedEmployee) return false;
      
      // Position filter
      if (selectedPosition) {
        const position = (a as any).designation || (a as any).employeeDesignation || (a as any).position;
        if (position !== selectedPosition) return false;
      }
      
      // Location filter
      if (selectedLocation) {
        let location = a.location;
        if (!location || location.trim() === '' || location === 'N/A') {
          location = a.isRemote ? 'Remote' : 'Office';
        }
        if (location !== selectedLocation) return false;
      }
      
      // Status filter
      if (selectedStatus && a.status !== selectedStatus) return false;
      
      // Shift filter
      if (selectedShift) {
        if (a.shiftName && a.shiftName !== selectedShift) return false;
        else if (a.shift) {
          const shiftNames = {
            1: 'Morning Shift',
            2: 'Evening Shift', 
            3: 'Night Shift',
            4: 'Flexible Hours'
          };
          const shiftName = shiftNames[a.shift as keyof typeof shiftNames] || `Shift ${a.shift}`;
          if (shiftName !== selectedShift) return false;
        }
      }
      
      // Search filter (searches across multiple fields)
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const searchFields = [
          a.employeeName,
          a.employeeDepartment,
          (a as any).department,
          (a as any).designation,
          (a as any).employeeDesignation,
          a.location,
          a.status,
          a.shiftName
        ].filter(Boolean).map(field => field?.toString().toLowerCase());
        
        if (!searchFields.some(field => field?.includes(query))) return false;
      }
      
      return true;
    });
  };

  // Get unique employees and departments from fetched data (combine both sources)
  const departments = [
    'All', 
    ...new Set([
      ...employees.map(emp => emp.department).filter(Boolean),
      ...attendances.map((a: Attendance) => a.employeeDepartment || (a as any).department).filter(Boolean)
    ])
  ];

  console.log('🏢 AttendanceReport - Department Analysis:', {
    employeeDepartments: [...new Set(employees.map(emp => emp.department))],
    attendanceDepartments: [...new Set(attendances.map((a: Attendance) => a.employeeDepartment || a.department))],
    combinedDepartmentsList: departments,
    totalEmployees: employees.length,
    totalAttendances: attendances.length,
    currentFilters: { startDate, endDate, selectedDepartment, selectedEmployee },
    sampleAttendanceRecord: attendances[0],
    sampleEmployeeRecord: employees[0]
  });

  const formatDateRange = () => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    return `${start.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${end.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
  };

  const handleExportPDF = () => {
    alert('Export to PDF functionality would be implemented here');
  };

  const handleExportExcel = () => {
    alert('Export to Excel functionality would be implemented here');
  };

  const handleGenerateReport = (type: string) => {
    alert(`Generating ${type} report...`);
  };

  // Show loading state
  if (loading && attendances.length === 0) {
    return (
      <div className="space-y-3">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-8 transition-colors duration-200">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 dark:bg-blue-900/30 mb-4">
              <RefreshCw className="h-8 w-8 text-blue-600 dark:text-blue-400 animate-spin" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Loading Reports</h3>
            <p className="text-gray-500 dark:text-gray-400">Fetching attendance data from database...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="space-y-3">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-8 transition-colors duration-200">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900/30 mb-4">
              <AlertTriangle className="h-8 w-8 text-red-600 dark:text-red-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Error Loading Data</h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">{error}</p>
            <button 
              onClick={fetchAttendances}
              className="px-4 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors duration-200"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Navigation Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-2 transition-colors duration-200">
        <div className="flex space-x-1">
          {[
            { id: 'summary', label: 'Executive Summary', icon: BarChart3 },
            { id: 'trends', label: 'Trend Analysis', icon: TrendingUp },
            { id: 'department', label: 'Department Breakdown', icon: Building2 },
            { id: 'export', label: 'Export & Reports', icon: FileDown }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setReportType(tab.id as any)}
              className={`flex items-center px-4 py-2.5 rounded-lg text-sm font-medium transition-colors duration-200 ${
                reportType === tab.id
                  ? 'bg-blue-600 dark:bg-blue-500 text-white'
                  : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
            >
              <tab.icon className="h-4 w-4 mr-2" />
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      {reportType === 'summary' && (
        <div className="space-y-3">
          {/* Compact Professional Filters */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-colors duration-200">
            {/* Filter Header */}
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-700 border-b border-gray-200 dark:border-gray-600">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                  <Filter className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-gray-900 dark:text-white">Smart Filters</h4>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {getFilteredAttendances().length} of {attendances.length} records
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {/* Quick Filter Presets */}
                <div className="hidden md:flex items-center space-x-1 mr-3">
                  <span className="text-xs text-gray-500 dark:text-gray-400">Quick:</span>
                  <button
                    onClick={() => {
                      setDatePeriod('Today');
                      handleDatePeriodChange('Today');
                    }}
                    className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors duration-200"
                  >
                    Today
                  </button>
                  <button
                    onClick={() => {
                      setDatePeriod('This Week');
                      handleDatePeriodChange('This Week');
                    }}
                    className="px-2 py-1 text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors duration-200"
                  >
                    This Week
                  </button>
                  <button
                    onClick={() => {
                      setDatePeriod('This Month');
                      handleDatePeriodChange('This Month');
                    }}
                    className="px-2 py-1 text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 rounded hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors duration-200"
                  >
                    This Month
                  </button>
                </div>
                
                {/* Active Filters Indicator */}
                {(selectedEmployee || selectedDepartment !== 'All' || selectedPosition || selectedLocation || selectedStatus || selectedShift || searchQuery) && (
                  <div className="flex items-center space-x-1 px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400 rounded text-xs">
                    <div className="w-1.5 h-1.5 bg-yellow-500 dark:bg-yellow-400 rounded-full animate-pulse"></div>
                    <span className="font-medium">
                      {[selectedEmployee ? 1 : 0, selectedDepartment !== 'All' ? 1 : 0, selectedPosition ? 1 : 0, selectedLocation ? 1 : 0, selectedStatus ? 1 : 0, selectedShift ? 1 : 0, searchQuery ? 1 : 0].filter(Boolean).length} Active
                    </span>
                  </div>
                )}
                
                <button 
                  onClick={() => setShowFilters(!showFilters)}
                  className="inline-flex items-center px-3 py-1.5 border border-blue-200 dark:border-blue-700 rounded-md text-xs font-medium text-blue-700 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors duration-200"
                >
                  <Filter className="h-3 w-3 mr-1" />
                  {showFilters ? 'Hide' : 'Show'} Advanced
                </button>
              </div>
            </div>

            <div className="p-4">
              {/* Primary Filters Row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
                {/* Period Selector with enhanced styling */}
                <div className="md:col-span-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <CalendarIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Time Period</label>
                    {datePeriod !== 'This Month' && (
                      <span className="text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 px-1.5 py-0.5 rounded">Custom</span>
                    )}
                  </div>
                  <select
                    value={datePeriod}
                    onChange={(e) => handleDatePeriodChange(e.target.value)}
                    className="w-full pl-3 pr-8 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500"
                  >
                    <option value="Today">📅 Today</option>
                    <option value="Yesterday">⏮️ Yesterday</option>
                    <option value="This Week">📊 This Week</option>
                    <option value="Last Week">📈 Last Week</option>
                    <option value="This Month">🗓️ This Month</option>
                    <option value="Last Month">📆 Last Month</option>
                    <option value="Last Three Months">🗂️ Last 3 Months</option>
                    <option value="Last Six Months">📋 Last 6 Months</option>
                    <option value="This Year">🗃️ This Year</option>
                    <option value="Last Year">📁 Last Year</option>
                    <option value="User Defined">⚙️ Custom Range</option>
                  </select>
                </div>

                {/* Enhanced Search Field */}
                <div className="md:col-span-2">
                  <div className="flex items-center space-x-2 mb-2">
                    <Search className="h-4 w-4 text-green-600 dark:text-green-400" />
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Global Search</label>
                    {searchQuery && (
                      <span className="text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 px-1.5 py-0.5 rounded">Active</span>
                    )}
                  </div>
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="🔍 Search employees, departments, positions, status..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-10 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-green-500 dark:focus:border-green-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500"
                    />
                    <Search className="h-4 w-4 text-gray-400 dark:text-gray-500 absolute left-3 top-1/2 transform -translate-y-1/2" />
                    {searchQuery && (
                      <button
                        onClick={() => setSearchQuery('')}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 hover:text-red-500 dark:hover:text-red-400 transition-colors duration-200"
                      >
                        <XCircle className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Enhanced Custom Date Range */}
              {datePeriod === "User Defined" && (
                <div className="mb-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 border border-blue-200 dark:border-blue-700 rounded-lg">
                  <div className="flex items-center space-x-2 mb-3">
                    <Calendar className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    <span className="text-sm font-medium text-blue-900 dark:text-blue-300">Custom Date Range</span>
                    <div className="flex-1 border-t border-blue-200 dark:border-blue-700"></div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
                    <div className="md:col-span-2">
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">📅 Start Date</label>
                      <input
                        type="date"
                        className="w-full py-2.5 px-3 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200" 
                        value={userDefinedDates.start}
                        onChange={(e) => {
                          setUserDefinedDates(prev => ({
                            ...prev,
                            start: e.target.value
                          }));
                        }}
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">📅 End Date</label>
                      <input
                        type="date"
                        className="w-full py-2.5 px-3 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200" 
                        value={userDefinedDates.end}
                        onChange={(e) => {
                          setUserDefinedDates(prev => ({
                            ...prev,
                            end: e.target.value
                          }));
                        }}
                      />
                    </div>
                    <div className="flex items-end">
                      <button 
                        className="w-full px-4 py-2.5 bg-blue-600 dark:bg-blue-500 text-white rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-600 disabled:bg-gray-300 dark:disabled:bg-gray-600 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow"
                        disabled={
                          !userDefinedDates.start ||
                          !userDefinedDates.end ||
                          userDefinedDates.start > userDefinedDates.end
                        }
                        onClick={() => {
                          setStartDate(userDefinedDates.start);
                          setEndDate(userDefinedDates.end);
                        }}
                      >
                        ✅ Apply Range
                      </button>
                    </div>
                  </div>
                </div>
              )}
              
              {showFilters && (
                <>
                  {/* Enhanced Filter Grid with better grouping */}
                  <div className="bg-gray-50 rounded-lg p-4 mb-4">
                    <div className="flex items-center space-x-2 mb-4">
                      <Settings className="h-4 w-4 text-gray-600" />
                      <h5 className="text-sm font-medium text-gray-700">Advanced Filters</h5>
                      <div className="flex-1 border-t border-gray-300"></div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {/* People Filters Group */}
                      <div className="space-y-3">
                        <div className="text-xs font-semibold text-gray-600 uppercase tracking-wide flex items-center">
                          <Users className="h-3 w-3 mr-1" />
                          People
                        </div>
                        
                        {/* Employee Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Users className="h-3 w-3 text-indigo-600" />
                            <label className="text-xs font-medium text-gray-700">Employee</label>
                            {selectedEmployee && <span className="text-xs bg-indigo-100 text-indigo-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedEmployee || ''}
                            onChange={(e) => setSelectedEmployee(e.target.value ? parseInt(e.target.value) : null)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">👥 All Employees</option>
                            {employees.map((employee) => (
                              <option key={employee.id} value={employee.id}>👤 {employee.name}</option>
                            ))}
                          </select>
                        </div>

                        {/* Department Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Building2 className="h-3 w-3 text-blue-600" />
                            <label className="text-xs font-medium text-gray-700">Department</label>
                            {selectedDepartment !== 'All' && <span className="text-xs bg-blue-100 text-blue-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedDepartment}
                            onChange={(e) => setSelectedDepartment(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            {departments.map((dept, i) => (
                              <option key={i} value={dept}>🏢 {dept}</option>
                            ))}
                          </select>
                        </div>
                      </div>

                      {/* Work Filters Group */}
                      <div className="space-y-3">
                        <div className="text-xs font-semibold text-gray-600 uppercase tracking-wide flex items-center">
                          <Briefcase className="h-3 w-3 mr-1" />
                          Work Details
                        </div>
                        
                        {/* Position Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Briefcase className="h-3 w-3 text-green-600" />
                            <label className="text-xs font-medium text-gray-700">Position</label>
                            {selectedPosition && <span className="text-xs bg-green-100 text-green-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedPosition}
                            onChange={(e) => setSelectedPosition(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">💼 All Positions</option>
                            {uniquePositions.map((position) => (
                              <option key={position} value={position}>🎯 {position}</option>
                            ))}
                          </select>
                        </div>

                        {/* Shift Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Clock7 className="h-3 w-3 text-teal-600" />
                            <label className="text-xs font-medium text-gray-700">Shift</label>
                            {selectedShift && <span className="text-xs bg-teal-100 text-teal-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedShift}
                            onChange={(e) => setSelectedShift(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">🕒 All Shifts</option>
                            {uniqueShifts.map((shift) => (
                              <option key={shift} value={shift}>⏰ {shift}</option>
                            ))}
                          </select>
                        </div>
                      </div>

                      {/* Status & Location Group */}
                      <div className="space-y-3">
                        <div className="text-xs font-semibold text-gray-600 uppercase tracking-wide flex items-center">
                          <Activity className="h-3 w-3 mr-1" />
                          Status & Location
                        </div>
                        
                        {/* Status Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Activity className="h-3 w-3 text-orange-600" />
                            <label className="text-xs font-medium text-gray-700">Status</label>
                            {selectedStatus && <span className="text-xs bg-orange-100 text-orange-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedStatus}
                            onChange={(e) => setSelectedStatus(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">📊 All Status</option>
                            <option value={AttendanceStatus.PRESENT}>✅ Present</option>
                            <option value={AttendanceStatus.ABSENT}>❌ Absent</option>
                            <option value={AttendanceStatus.LATE}>⏰ Late</option>
                            <option value={AttendanceStatus.WORK_FROM_HOME}>🏠 WFH</option>
                            <option value={AttendanceStatus.HALF_DAY}>🕐 Half Day</option>
                            <option value={AttendanceStatus.LEAVE}>🏖️ Leave</option>
                            <option value={AttendanceStatus.SICK_LEAVE}>🤒 Sick</option>
                            <option value={AttendanceStatus.ANNUAL_LEAVE}>🌴 Annual</option>
                          </select>
                        </div>

                        {/* Location Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <svg className="h-3 w-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <label className="text-xs font-medium text-gray-700">Location</label>
                            {selectedLocation && <span className="text-xs bg-purple-100 text-purple-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedLocation}
                            onChange={(e) => setSelectedLocation(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">📍 All Locations</option>
                            {uniqueLocations.map((location) => (
                              <option key={location} value={location}>🌍 {location}</option>
                            ))}
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* Enhanced Actions Bar */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-600">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                    <div className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full animate-pulse"></div>
                    <span className="font-semibold text-blue-700 dark:text-blue-400">{getFilteredAttendances().length}</span>
                    <span>of</span>
                    <span className="font-medium">{attendances.length}</span>
                    <span>records shown</span>
                  </div>
                  
                  {/* Data Quality Indicator */}
                  {attendances.length === 0 ? (
                    <div className="flex items-center space-x-1 text-xs text-amber-600 dark:text-amber-400">
                      <AlertTriangle className="h-3 w-3" />
                      <span>No data available</span>
                    </div>
                  ) : getFilteredAttendances().length === 0 ? (
                    <div className="flex items-center space-x-1 text-xs text-orange-600 dark:text-orange-400">
                      <AlertTriangle className="h-3 w-3" />
                      <span>No matches found</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-1 text-xs text-green-600 dark:text-green-400">
                      <CheckCircle2 className="h-3 w-3" />
                      <span>Data loaded</span>
                    </div>
                  )}
                </div>
                
                <div className="flex items-center space-x-2">
                  <button 
                    onClick={resetFilters}
                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md text-xs font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 shadow-sm hover:shadow"
                  >
                    <RefreshCw className="h-3 w-3 mr-1" />
                    Reset All
                  </button>
                  <button
                    onClick={() => setShowFilters(false)}
                    className="inline-flex items-center px-4 py-1.5 bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white rounded-md text-xs font-medium transition-all duration-200 shadow-sm hover:shadow"
                  >
                    <CheckCircle2 className="h-3 w-3 mr-1" />
                    Apply Filters
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          {/* Summary Cards */}
          <AttendanceSummaryCards 
            attendances={getFilteredAttendances()}
            startDate={startDate}
            endDate={endDate}
            selectedDepartment={selectedDepartment}
            selectedEmployee={selectedEmployee}
          />

          {(() => {
            // Check if we have any data for the selected filters
            const filteredAttendances = getFilteredAttendances();

            if (filteredAttendances.length === 0) {
              return (
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-8 transition-colors duration-200">
                  <div className="text-center">
                    <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gray-100 dark:bg-gray-700 mb-4">
                      <Calendar className="h-8 w-8 text-gray-400 dark:text-gray-500" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Data Available</h3>
                    <p className="text-gray-500 dark:text-gray-400 mb-4">
                      No attendance records found for the selected date range and filters.
                    </p>
                    <div className="text-sm text-gray-400 dark:text-gray-500">
                      Selected period: {new Date(startDate).toLocaleDateString()} - {new Date(endDate).toLocaleDateString()}
                      {selectedDepartment !== 'All' && (
                        <><br />Department: {selectedDepartment}</>
                      )}
                      {selectedEmployee && (
                        <><br />Employee: {employees.find(e => e.id === selectedEmployee)?.name}</>
                      )}
                    </div>
                  </div>
                </div>
              );
            }

            return (
              <>
                {/* Executive Insights & Alerts */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                  {/* Key Performance Indicators */}
                  <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-5 transition-colors duration-200">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Performance Overview</h3>
                      <Target className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="space-y-4">
                      {(() => {
                        // Calculate filtered attendances
                        const filteredAttendances = attendances.filter(a => 
                          a.date >= startDate && 
                          a.date <= endDate &&
                          (selectedDepartment === 'All' || a.employeeDepartment === selectedDepartment) &&
                          (!selectedEmployee || a.employeeId === selectedEmployee)
                        );
                        
                        const totalRecords = filteredAttendances.length;
                        const presentRecords = filteredAttendances.filter(a => 
                          a.status === AttendanceStatus.PRESENT || 
                          a.status === AttendanceStatus.WORK_FROM_HOME
                        ).length;
                        const lateRecords = filteredAttendances.filter(a => 
                          a.status === AttendanceStatus.LATE
                        ).length;
                        
                        const attendanceRate = totalRecords > 0 ? (presentRecords / totalRecords) * 100 : 0;
                        const avgWorkHours = presentRecords > 0 ? 
                          filteredAttendances.reduce((sum, a) => sum + (a.workHours || 0), 0) / presentRecords : 0;
                        
                        // Calculate current week late arrivals
                        const currentWeekStart = new Date();
                        currentWeekStart.setDate(currentWeekStart.getDate() - currentWeekStart.getDay());
                        const weekStartStr = currentWeekStart.toISOString().split('T')[0];
                        const currentWeekLate = attendances.filter(a => 
                          a.date >= weekStartStr && 
                          a.status === AttendanceStatus.LATE &&
                          (selectedDepartment === 'All' || a.employeeDepartment === selectedDepartment)
                        ).length;
                        
                        return (
                          <>
                            <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/30 rounded-lg">
                              <div className="flex items-center">
                                <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400 mr-2" />
                                <span className="text-sm font-medium text-green-800 dark:text-green-300">Attendance Target</span>
                              </div>
                              <div className="text-right">
                                <div className="text-lg font-bold text-green-900 dark:text-green-100">{attendanceRate.toFixed(1)}%</div>
                                <div className="text-xs text-green-600 dark:text-green-400">
                                  {attendanceRate >= 90 ? 'Above 90% target' : 'Below 90% target'}
                                </div>
                              </div>
                            </div>
                            
                            <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
                              <div className="flex items-center">
                                <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400 mr-2" />
                                <span className="text-sm font-medium text-blue-800 dark:text-blue-300">Productivity Hours</span>
                              </div>
                              <div className="text-right">
                                <div className="text-lg font-bold text-blue-900 dark:text-blue-100">{avgWorkHours.toFixed(1)}h</div>
                                <div className="text-xs text-blue-600 dark:text-blue-400">Daily average</div>
                              </div>
                            </div>
                            
                            <div className="flex items-center justify-between p-3 bg-amber-50 dark:bg-amber-900/30 rounded-lg">
                              <div className="flex items-center">
                                <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400 mr-2" />
                                <span className="text-sm font-medium text-amber-800 dark:text-amber-300">Late Arrivals</span>
                              </div>
                              <div className="text-right">
                                <div className="text-lg font-bold text-amber-900 dark:text-amber-100">{currentWeekLate}</div>
                                <div className="text-xs text-amber-600 dark:text-amber-400">This week</div>
                              </div>
                            </div>
                          </>
                        );
                      })()}
                    </div>
                  </div>

                  {/* Alerts & Action Items */}
                  <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-5 transition-colors duration-200">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Action Required</h3>
                      <AlertTriangle className="h-5 w-5 text-amber-600 dark:text-amber-400" />
                    </div>
                    <div className="space-y-3">
                      {(() => {
                        const alerts = [];
                        
                        // Calculate high absenteeism by department
                        const deptAbsenteeism = departments.filter(dept => dept !== 'All').map(dept => {
                          const deptAttendances = attendances.filter(a => 
                            a.employeeDepartment === dept &&
                            a.date >= startDate && 
                            a.date <= endDate
                          );
                          const absentCount = deptAttendances.filter(a => 
                            a.status === AttendanceStatus.ABSENT
                          ).length;
                          return { dept, absentCount, total: deptAttendances.length };
                        });
                        
                        const highAbsentDept = deptAbsenteeism.find(d => 
                          d.total > 0 && (d.absentCount / d.total) > 0.15
                        );
                        
                        // Calculate overtime violations
                        const overtimeViolations = attendances.filter(a => 
                          a.date >= startDate && 
                          a.date <= endDate &&
                          (a.overtime || 0) > 3 &&
                          (selectedDepartment === 'All' || a.employeeDepartment === selectedDepartment)
                        );
                        const uniqueOvertimeEmployees = new Set(overtimeViolations.map(a => a.employeeId)).size;
                        
                        // Calculate perfect attendance
                        const employeeStats = new Map<number, { present: number; total: number; name: string }>();
                        
                        attendances.filter(a => 
                          a.date >= startDate && 
                          a.date <= endDate &&
                          (selectedDepartment === 'All' || a.employeeDepartment === selectedDepartment)
                        ).forEach(a => {
                          const existing = employeeStats.get(a.employeeId) || { present: 0, total: 0, name: a.employeeName };
                          existing.total++;
                          if (a.status === AttendanceStatus.PRESENT || a.status === AttendanceStatus.WORK_FROM_HOME) {
                            existing.present++;
                          }
                          employeeStats.set(a.employeeId, existing);
                        });
                        
                        const perfectAttendanceCount = Array.from(employeeStats.values()).filter(emp => 
                          emp.total > 0 && emp.present === emp.total
                        ).length;
                        
                        // Generate alerts
                        if (highAbsentDept) {
                          alerts.push(
                            <div key="absenteeism" className="flex items-start space-x-3 p-3 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-lg">
                              <XCircle className="h-4 w-4 text-red-500 dark:text-red-400 mt-0.5" />
                              <div className="flex-1">
                                <div className="text-sm font-medium text-red-800 dark:text-red-300">High Absenteeism</div>
                                <div className="text-xs text-red-600 dark:text-red-400">
                                  {highAbsentDept.dept}: {((highAbsentDept.absentCount / highAbsentDept.total) * 100).toFixed(1)}% absence rate
                                </div>
                              </div>
                              <button className="text-xs text-red-700 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 font-medium">Review</button>
                            </div>
                          );
                        }
                        
                        if (uniqueOvertimeEmployees > 0) {
                          alerts.push(
                            <div key="overtime" className="flex items-start space-x-3 p-3 bg-amber-50 dark:bg-amber-900/30 border border-amber-200 dark:border-amber-700 rounded-lg">
                              <Clock className="h-4 w-4 text-amber-500 dark:text-amber-400 mt-0.5" />
                              <div className="flex-1">
                                <div className="text-sm font-medium text-amber-800 dark:text-amber-300">Overtime Alert</div>
                                <div className="text-xs text-amber-600 dark:text-amber-400">
                                  {uniqueOvertimeEmployees} employees with excessive overtime
                                </div>
                              </div>
                              <button className="text-xs text-amber-700 dark:text-amber-400 hover:text-amber-900 dark:hover:text-amber-300 font-medium">View</button>
                            </div>
                          );
                        }
                        
                        if (perfectAttendanceCount > 0) {
                          alerts.push(
                            <div key="perfect" className="flex items-start space-x-3 p-3 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg">
                              <UserCheck className="h-4 w-4 text-blue-500 dark:text-blue-400 mt-0.5" />
                              <div className="flex-1">
                                <div className="text-sm font-medium text-blue-800 dark:text-blue-300">Perfect Attendance</div>
                                <div className="text-xs text-blue-600 dark:text-blue-400">
                                  {perfectAttendanceCount} employees with 100% attendance
                                </div>
                              </div>
                              <button className="text-xs text-blue-700 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 font-medium">Recognize</button>
                            </div>
                          );
                        }
                        
                        if (alerts.length === 0) {
                          alerts.push(
                            <div key="no-alerts" className="flex items-center justify-center p-4 text-gray-500 dark:text-gray-400">
                              <CheckCircle2 className="h-5 w-5 mr-2" />
                              No critical alerts at this time
                            </div>
                          );
                        }
                        
                        return alerts;
                      })()}
                    </div>
                  </div>
                </div>

                {/* Trends & Comparisons */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-5 transition-colors duration-200">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Trend Analysis</h3>
                    <TrendingUp className="h-5 w-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {(() => {
                      // Calculate current period data
                      const currentAttendances = attendances.filter((a: Attendance) => 
                        a.date >= startDate && 
                        a.date <= endDate &&
                        (selectedDepartment === 'All' || a.employeeDepartment === selectedDepartment) &&
                        (!selectedEmployee || a.employeeId === selectedEmployee)
                      );
                      
                      // Calculate previous period (same duration before start date)
                      const periodDays = Math.ceil((new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 60 * 60 * 24));
                      const prevStartDate = new Date(startDate);
                      prevStartDate.setDate(prevStartDate.getDate() - periodDays);
                      const prevEndDate = new Date(startDate);
                      prevEndDate.setDate(prevEndDate.getDate() - 1);
                      
                      const previousAttendances = attendances.filter((a: Attendance) => 
                        a.date >= prevStartDate.toISOString().split('T')[0] && 
                        a.date <= prevEndDate.toISOString().split('T')[0] &&
                        (selectedDepartment === 'All' || a.employeeDepartment === selectedDepartment) &&
                        (!selectedEmployee || a.employeeId === selectedEmployee)
                      );
                      
                      // Calculate attendance rates
                      const currentRate = currentAttendances.length > 0 ? 
                        (currentAttendances.filter((a: Attendance) => a.status === AttendanceStatus.PRESENT || a.status === AttendanceStatus.WORK_FROM_HOME).length / currentAttendances.length) * 100 : 0;
                      const previousRate = previousAttendances.length > 0 ? 
                        (previousAttendances.filter((a: Attendance) => a.status === AttendanceStatus.PRESENT || a.status === AttendanceStatus.WORK_FROM_HOME).length / previousAttendances.length) * 100 : 0;
                      const attendanceChange = currentRate - previousRate;
                      
                      // Calculate work hours
                      const currentHours = currentAttendances.reduce((sum, a) => sum + (a.workHours || 0), 0) / (currentAttendances.length || 1);
                      const previousHours = previousAttendances.reduce((sum, a) => sum + (a.workHours || 0), 0) / (previousAttendances.length || 1);
                      const hoursChange = currentHours - previousHours;
                      
                      // Calculate late arrivals
                      const currentLate = currentAttendances.filter((a: Attendance) => a.status === AttendanceStatus.LATE).length;
                      const previousLate = previousAttendances.filter((a: Attendance) => a.status === AttendanceStatus.LATE).length;
                      const lateChange = previousLate > 0 ? ((currentLate - previousLate) / previousLate) * 100 : 0;
                      
                      return (
                        <>
                          <div className="text-center p-4 bg-green-50 dark:bg-green-900/30 rounded-lg">
                            <div className="flex items-center justify-center mb-2">
                              {attendanceChange >= 0 ? (
                                <ArrowUp className="h-4 w-4 text-green-600 dark:text-green-400 mr-1" />
                              ) : (
                                <ArrowDown className="h-4 w-4 text-red-600 dark:text-red-400 mr-1" />
                              )}
                              <span className="text-sm font-medium text-gray-800 dark:text-gray-200">Attendance Rate</span>
                            </div>
                            <div className={`text-2xl font-bold ${attendanceChange >= 0 ? 'text-green-900 dark:text-green-100' : 'text-red-900 dark:text-red-100'}`}>
                              {attendanceChange >= 0 ? '+' : ''}{attendanceChange.toFixed(1)}%
                            </div>
                            <div className="text-xs text-gray-600 dark:text-gray-400">vs previous period</div>
                          </div>
                          
                          <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
                            <div className="flex items-center justify-center mb-2">
                              {hoursChange >= 0 ? (
                                <ArrowUp className="h-4 w-4 text-blue-600 dark:text-blue-400 mr-1" />
                              ) : (
                                <ArrowDown className="h-4 w-4 text-blue-600 dark:text-blue-400 mr-1" />
                              )}
                              <span className="text-sm font-medium text-blue-800 dark:text-blue-200">Average Work Hours</span>
                            </div>
                            <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                              {hoursChange >= 0 ? '+' : ''}{hoursChange.toFixed(1)}h
                            </div>
                            <div className="text-xs text-blue-600 dark:text-blue-400">vs previous period</div>
                          </div>
                          
                          <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/30 rounded-lg">
                            <div className="flex items-center justify-center mb-2">
                              {lateChange <= 0 ? (
                                <ArrowDown className="h-4 w-4 text-purple-600 dark:text-purple-400 mr-1" />
                              ) : (
                                <ArrowUp className="h-4 w-4 text-red-600 dark:text-red-400 mr-1" />
                              )}
                              <span className="text-sm font-medium text-purple-800 dark:text-purple-200">Late Arrivals</span>
                            </div>
                            <div className={`text-2xl font-bold ${lateChange <= 0 ? 'text-purple-900 dark:text-purple-100' : 'text-red-900 dark:text-red-100'}`}>
                              {lateChange > 0 ? '+' : ''}{lateChange.toFixed(0)}%
                            </div>
                            <div className="text-xs text-purple-600 dark:text-purple-400">
                              {lateChange <= 0 ? 'Improvement' : 'Increase'}
                            </div>
                          </div>
                        </>
                      );
                    })()}
                  </div>
                </div>

                {/* Department Performance Summary */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-5 transition-colors duration-200">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Department Performance</h3>
                    <Building2 className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {departments.filter(dept => dept !== 'All').map((dept, index) => {
                      // Current period data
                      const currentDeptAttendances = attendances.filter(a => 
                        a.employeeDepartment === dept &&
                        a.date >= startDate && 
                        a.date <= endDate
                      );
                      
                      console.log(`🏢 Department ${dept} analysis:`, {
                        departmentName: dept,
                        totalAttendances: attendances.length,
                        matchingRecords: currentDeptAttendances.length,
                        sampleAttendanceRecord: attendances[0],
                        filterCriteria: { startDate, endDate },
                        uniqueAttendanceDepts: [...new Set(attendances.map((a: Attendance) => a.employeeDepartment || a.department))]
                      });
                      
                      const currentPresent = currentDeptAttendances.filter(a => 
                        a.status === AttendanceStatus.PRESENT || 
                        a.status === AttendanceStatus.WORK_FROM_HOME
                      ).length;
                      
                      const currentRate = currentDeptAttendances.length > 0 ? 
                        (currentPresent / currentDeptAttendances.length) * 100 : 0;
                      
                      // Previous period for trend
                      const periodDays = Math.ceil((new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 60 * 60 * 24));
                      const prevStartDate = new Date(startDate);
                      prevStartDate.setDate(prevStartDate.getDate() - periodDays);
                      const prevEndDate = new Date(startDate);
                      prevEndDate.setDate(prevEndDate.getDate() - 1);
                      
                      const previousDeptAttendances = attendances.filter(a => 
                        a.employeeDepartment === dept &&
                        a.date >= prevStartDate.toISOString().split('T')[0] && 
                        a.date <= prevEndDate.toISOString().split('T')[0]
                      );
                      
                      const previousPresent = previousDeptAttendances.filter(a => 
                        a.status === AttendanceStatus.PRESENT || 
                        a.status === AttendanceStatus.WORK_FROM_HOME
                      ).length;
                      
                      const previousRate = previousDeptAttendances.length > 0 ? 
                        (previousPresent / previousDeptAttendances.length) * 100 : 0;
                      
                      const trend = currentRate > previousRate + 1 ? 'up' : 
                                   currentRate < previousRate - 1 ? 'down' : 'stable';
                      const color = currentRate >= 90 ? 'green' : currentRate >= 75 ? 'blue' : 'red';
                      
                      return (
                        <div key={index} className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md dark:hover:shadow-gray-900/20 transition-shadow bg-white dark:bg-gray-700">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{dept}</span>
                            {trend === 'up' && <ArrowUp className="h-4 w-4 text-green-600 dark:text-green-400" />}
                            {trend === 'down' && <ArrowDown className="h-4 w-4 text-red-600 dark:text-red-400" />}
                            {trend === 'stable' && <Minus className="h-4 w-4 text-blue-600 dark:text-blue-400" />}
                          </div>
                          <div className="text-2xl font-bold text-gray-900 dark:text-white">{currentRate.toFixed(1)}%</div>
                          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
                            <div 
                              className={`h-2 rounded-full bg-${color}-500`}
                              style={{ width: `${Math.min(currentRate, 100)}%` }}
                            ></div>
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {new Set(currentDeptAttendances.map(a => a.employeeId)).size} employees
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-5 transition-colors duration-200">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
                    <Settings className="h-5 w-5 text-gray-600" />
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    <button className="flex flex-col items-center p-3 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors">
                      <FileDown className="h-5 w-5 text-blue-600 mb-2" />
                      <span className="text-xs font-medium text-gray-700">Export Report</span>
                    </button>
                    
                    <button className="flex flex-col items-center p-3 border border-gray-200 rounded-lg hover:bg-green-50 hover:border-green-300 transition-colors">
                      <UserCheck className="h-5 w-5 text-green-600 mb-2" />
                      <span className="text-xs font-medium text-gray-700">View Details</span>
                    </button>
                    
                    <button className="flex flex-col items-center p-3 border border-gray-200 rounded-lg hover:bg-purple-50 hover:border-purple-300 transition-colors">
                      <BarChart3 className="h-5 w-5 text-purple-600 mb-2" />
                      <span className="text-xs font-medium text-gray-700">Analytics</span>
                    </button>
                    
                    <button className="flex flex-col items-center p-3 border border-gray-200 rounded-lg hover:bg-amber-50 hover:border-amber-300 transition-colors">
                      <AlertTriangle className="h-5 w-5 text-amber-600 mb-2" />
                      <span className="text-xs font-medium text-gray-700">Alerts</span>
                    </button>
                  </div>
                </div>
              </>
            );
          })()}
        </div>
      )}

      {reportType === 'trends' && (
        <div className="space-y-3">
          {/* Enhanced Professional Filters */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-colors duration-200">
            {/* Filter Header */}
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-700 border-b border-gray-200 dark:border-gray-600">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                  <Filter className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-gray-900 dark:text-white">Smart Filters</h4>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {getFilteredAttendances().length} of {attendances.length} records
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {/* Quick Filter Presets */}
                <div className="hidden md:flex items-center space-x-1 mr-3">
                  <span className="text-xs text-gray-500 dark:text-gray-400">Quick:</span>
                  <button
                    onClick={() => {
                      setDatePeriod('Today');
                      handleDatePeriodChange('Today');
                    }}
                    className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors duration-200"
                  >
                    Today
                  </button>
                  <button
                    onClick={() => {
                      setDatePeriod('This Week');
                      handleDatePeriodChange('This Week');
                    }}
                    className="px-2 py-1 text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors duration-200"
                  >
                    This Week
                  </button>
                  <button
                    onClick={() => {
                      setDatePeriod('This Month');
                      handleDatePeriodChange('This Month');
                    }}
                    className="px-2 py-1 text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 rounded hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors duration-200"
                  >
                    This Month
                  </button>
                </div>
                
                {/* Active Filters Indicator */}
                {(selectedEmployee || selectedDepartment !== 'All' || selectedPosition || selectedLocation || selectedStatus || selectedShift || searchQuery) && (
                  <div className="flex items-center space-x-1 px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400 rounded text-xs">
                    <div className="w-1.5 h-1.5 bg-yellow-500 dark:bg-yellow-400 rounded-full animate-pulse"></div>
                    <span className="font-medium">
                      {[selectedEmployee ? 1 : 0, selectedDepartment !== 'All' ? 1 : 0, selectedPosition ? 1 : 0, selectedLocation ? 1 : 0, selectedStatus ? 1 : 0, selectedShift ? 1 : 0, searchQuery ? 1 : 0].filter(Boolean).length} Active
                    </span>
                  </div>
                )}
                
                <button 
                  onClick={() => setShowFilters(!showFilters)}
                  className="inline-flex items-center px-3 py-1.5 border border-blue-200 dark:border-blue-700 rounded-md text-xs font-medium text-blue-700 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors duration-200"
                >
                  <Filter className="h-3 w-3 mr-1" />
                  {showFilters ? 'Hide' : 'Show'} Advanced
                </button>
              </div>
            </div>

            <div className="p-4">
              {/* Primary Filters Row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
                {/* Period Selector with enhanced styling */}
                <div className="md:col-span-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <CalendarIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Time Period</label>
                    {datePeriod !== 'This Month' && (
                      <span className="text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 px-1.5 py-0.5 rounded">Custom</span>
                    )}
                  </div>
                  <select
                    value={datePeriod}
                    onChange={(e) => handleDatePeriodChange(e.target.value)}
                    className="w-full pl-3 pr-8 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500"
                  >
                    <option value="Today">📅 Today</option>
                    <option value="Yesterday">⏮️ Yesterday</option>
                    <option value="This Week">📊 This Week</option>
                    <option value="Last Week">📈 Last Week</option>
                    <option value="This Month">🗓️ This Month</option>
                    <option value="Last Month">📆 Last Month</option>
                    <option value="Last Three Months">🗂️ Last 3 Months</option>
                    <option value="Last Six Months">📋 Last 6 Months</option>
                    <option value="This Year">🗃️ This Year</option>
                    <option value="Last Year">📁 Last Year</option>
                    <option value="User Defined">⚙️ Custom Range</option>
                  </select>
                </div>

                {/* Enhanced Search Field */}
                <div className="md:col-span-2">
                  <div className="flex items-center space-x-2 mb-2">
                    <Search className="h-4 w-4 text-green-600 dark:text-green-400" />
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Global Search</label>
                    {searchQuery && (
                      <span className="text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 px-1.5 py-0.5 rounded">Active</span>
                    )}
                  </div>
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="🔍 Search employees, departments, positions, status..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-10 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-green-500 dark:focus:border-green-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500"
                    />
                    <Search className="h-4 w-4 text-gray-400 dark:text-gray-500 absolute left-3 top-1/2 transform -translate-y-1/2" />
                    {searchQuery && (
                      <button
                        onClick={() => setSearchQuery('')}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 hover:text-red-500 dark:hover:text-red-400 transition-colors duration-200"
                      >
                        <XCircle className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Enhanced Custom Date Range */}
              {datePeriod === "User Defined" && (
                <div className="mb-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 border border-blue-200 dark:border-blue-700 rounded-lg">
                  <div className="flex items-center space-x-2 mb-3">
                    <Calendar className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    <span className="text-sm font-medium text-blue-900 dark:text-blue-300">Custom Date Range</span>
                    <div className="flex-1 border-t border-blue-200 dark:border-blue-700"></div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
                    <div className="md:col-span-2">
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">📅 Start Date</label>
                      <input
                        type="date"
                        className="w-full py-2.5 px-3 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200" 
                        value={userDefinedDates.start}
                        onChange={(e) => {
                          setUserDefinedDates(prev => ({
                            ...prev,
                            start: e.target.value
                          }));
                        }}
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">📅 End Date</label>
                      <input
                        type="date"
                        className="w-full py-2.5 px-3 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200" 
                        value={userDefinedDates.end}
                        onChange={(e) => {
                          setUserDefinedDates(prev => ({
                            ...prev,
                            end: e.target.value
                          }));
                        }}
                      />
                    </div>
                    <div className="flex items-end">
                      <button 
                        className="w-full px-4 py-2.5 bg-blue-600 dark:bg-blue-500 text-white rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-600 disabled:bg-gray-300 dark:disabled:bg-gray-600 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow"
                        disabled={
                          !userDefinedDates.start ||
                          !userDefinedDates.end ||
                          userDefinedDates.start > userDefinedDates.end
                        }
                        onClick={() => {
                          setStartDate(userDefinedDates.start);
                          setEndDate(userDefinedDates.end);
                        }}
                      >
                        ✅ Apply Range
                      </button>
                    </div>
                  </div>
                </div>
              )}
              
              {showFilters && (
                <>
                  {/* Enhanced Filter Grid with better grouping */}
                  <div className="bg-gray-50 rounded-lg p-4 mb-4">
                    <div className="flex items-center space-x-2 mb-4">
                      <Settings className="h-4 w-4 text-gray-600" />
                      <h5 className="text-sm font-medium text-gray-700">Advanced Filters</h5>
                      <div className="flex-1 border-t border-gray-300"></div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {/* People Filters Group */}
                      <div className="space-y-3">
                        <div className="text-xs font-semibold text-gray-600 uppercase tracking-wide flex items-center">
                          <Users className="h-3 w-3 mr-1" />
                          People
                        </div>
                        
                        {/* Employee Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Users className="h-3 w-3 text-indigo-600" />
                            <label className="text-xs font-medium text-gray-700">Employee</label>
                            {selectedEmployee && <span className="text-xs bg-indigo-100 text-indigo-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedEmployee || ''}
                            onChange={(e) => setSelectedEmployee(e.target.value ? parseInt(e.target.value) : null)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">👥 All Employees</option>
                            {employees.map((employee) => (
                              <option key={employee.id} value={employee.id}>👤 {employee.name}</option>
                            ))}
                          </select>
                        </div>

                        {/* Department Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Building2 className="h-3 w-3 text-blue-600" />
                            <label className="text-xs font-medium text-gray-700">Department</label>
                            {selectedDepartment !== 'All' && <span className="text-xs bg-blue-100 text-blue-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedDepartment}
                            onChange={(e) => setSelectedDepartment(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            {departments.map((dept, i) => (
                              <option key={i} value={dept}>🏢 {dept}</option>
                            ))}
                          </select>
                        </div>
                      </div>

                      {/* Work Filters Group */}
                      <div className="space-y-3">
                        <div className="text-xs font-semibold text-gray-600 uppercase tracking-wide flex items-center">
                          <Briefcase className="h-3 w-3 mr-1" />
                          Work Details
                        </div>
                        
                        {/* Position Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Briefcase className="h-3 w-3 text-green-600" />
                            <label className="text-xs font-medium text-gray-700">Position</label>
                            {selectedPosition && <span className="text-xs bg-green-100 text-green-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedPosition}
                            onChange={(e) => setSelectedPosition(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">💼 All Positions</option>
                            {uniquePositions.map((position) => (
                              <option key={position} value={position}>🎯 {position}</option>
                            ))}
                          </select>
                        </div>

                        {/* Shift Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Clock7 className="h-3 w-3 text-teal-600" />
                            <label className="text-xs font-medium text-gray-700">Shift</label>
                            {selectedShift && <span className="text-xs bg-teal-100 text-teal-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedShift}
                            onChange={(e) => setSelectedShift(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">🕒 All Shifts</option>
                            {uniqueShifts.map((shift) => (
                              <option key={shift} value={shift}>⏰ {shift}</option>
                            ))}
                          </select>
                        </div>
                      </div>

                      {/* Status & Location Group */}
                      <div className="space-y-3">
                        <div className="text-xs font-semibold text-gray-600 uppercase tracking-wide flex items-center">
                          <Activity className="h-3 w-3 mr-1" />
                          Status & Location
                        </div>
                        
                        {/* Status Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Activity className="h-3 w-3 text-orange-600" />
                            <label className="text-xs font-medium text-gray-700">Status</label>
                            {selectedStatus && <span className="text-xs bg-orange-100 text-orange-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedStatus}
                            onChange={(e) => setSelectedStatus(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">📊 All Status</option>
                            <option value={AttendanceStatus.PRESENT}>✅ Present</option>
                            <option value={AttendanceStatus.ABSENT}>❌ Absent</option>
                            <option value={AttendanceStatus.LATE}>⏰ Late</option>
                            <option value={AttendanceStatus.WORK_FROM_HOME}>🏠 WFH</option>
                            <option value={AttendanceStatus.HALF_DAY}>🕐 Half Day</option>
                            <option value={AttendanceStatus.LEAVE}>🏖️ Leave</option>
                            <option value={AttendanceStatus.SICK_LEAVE}>🤒 Sick</option>
                            <option value={AttendanceStatus.ANNUAL_LEAVE}>🌴 Annual</option>
                          </select>
                        </div>

                        {/* Location Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <svg className="h-3 w-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <label className="text-xs font-medium text-gray-700">Location</label>
                            {selectedLocation && <span className="text-xs bg-purple-100 text-purple-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedLocation}
                            onChange={(e) => setSelectedLocation(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">📍 All Locations</option>
                            {uniqueLocations.map((location) => (
                              <option key={location} value={location}>🌍 {location}</option>
                            ))}
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* Enhanced Actions Bar */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-600">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                    <div className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full animate-pulse"></div>
                    <span className="font-semibold text-blue-700 dark:text-blue-400">{getFilteredAttendances().length}</span>
                    <span>of</span>
                    <span className="font-medium">{attendances.length}</span>
                    <span>records shown</span>
                  </div>
                  
                  {/* Data Quality Indicator */}
                  {attendances.length === 0 ? (
                    <div className="flex items-center space-x-1 text-xs text-amber-600 dark:text-amber-400">
                      <AlertTriangle className="h-3 w-3" />
                      <span>No data available</span>
                    </div>
                  ) : getFilteredAttendances().length === 0 ? (
                    <div className="flex items-center space-x-1 text-xs text-orange-600 dark:text-orange-400">
                      <AlertTriangle className="h-3 w-3" />
                      <span>No matches found</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-1 text-xs text-green-600 dark:text-green-400">
                      <CheckCircle2 className="h-3 w-3" />
                      <span>Data loaded</span>
                    </div>
                  )}
                </div>
                
                <div className="flex items-center space-x-2">
                  <button 
                    onClick={resetFilters}
                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md text-xs font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 shadow-sm hover:shadow"
                  >
                    <RefreshCw className="h-3 w-3 mr-1" />
                    Reset All
                  </button>
                  <button
                    onClick={() => setShowFilters(false)}
                    className="inline-flex items-center px-4 py-1.5 bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white rounded-md text-xs font-medium transition-all duration-200 shadow-sm hover:shadow"
                  >
                    <CheckCircle2 className="h-3 w-3 mr-1" />
                    Apply Filters
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          {/* Enhanced Attendance Trends with Real Data Analysis */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 transition-colors duration-200">
            {(() => {
              // Filter data based on current selection
              const filteredData = attendances.filter((a: Attendance) => 
                a.date >= startDate && 
                a.date <= endDate &&
                (selectedDepartment === 'All' || a.employeeDepartment === selectedDepartment) &&
                (!selectedEmployee || a.employeeId === selectedEmployee)
              );

              if (filteredData.length === 0) {
                return (
                  <div className="text-center py-8">
                    <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gray-100 dark:bg-gray-700 mb-4">
                      <TrendingUp className="h-8 w-8 text-gray-400 dark:text-gray-500" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Trend Data Available</h3>
                    <p className="text-gray-500 dark:text-gray-400">
                      No attendance data available for trend analysis with the current filters.
                    </p>
                  </div>
                );
              }

              // Calculate weekly patterns from real data
              const weeklyData = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'].map((dayName, dayIndex) => {
                const dayRecords = filteredData.filter((a: Attendance) => {
                  const date = new Date(a.date);
                  const dayOfWeek = date.getDay();
                  return dayOfWeek === (dayIndex + 1); // Monday = 1, Tuesday = 2, etc.
                });

                const totalRecords = dayRecords.length;
                const presentRecords = dayRecords.filter((a: Attendance) => 
                  a.status === AttendanceStatus.PRESENT || 
                  a.status === AttendanceStatus.WORK_FROM_HOME ||
                  a.status === AttendanceStatus.LATE
                ).length;

                const attendanceRate = totalRecords > 0 ? (presentRecords / totalRecords) * 100 : 0;
                return { day: dayName, rate: attendanceRate, total: totalRecords };
              });

              // Calculate daily trends for line chart
              const dailyTrends = (() => {
                const dailyData = new Map<string, { present: number; total: number }>();
                
                filteredData.forEach((a: Attendance) => {
                  const key = a.date;
                  if (!dailyData.has(key)) {
                    dailyData.set(key, { present: 0, total: 0 });
                  }
                  const data = dailyData.get(key)!;
                  data.total += 1;
                  if (a.status === AttendanceStatus.PRESENT || 
                      a.status === AttendanceStatus.WORK_FROM_HOME ||
                      a.status === AttendanceStatus.LATE) {
                    data.present += 1;
                  }
                });

                const sortedDates = Array.from(dailyData.keys()).sort();
                return sortedDates.map(date => ({
                  date,
                  rate: dailyData.get(date)!.total > 0 ? 
                    (dailyData.get(date)!.present / dailyData.get(date)!.total) * 100 : 0
                }));
              })();

              // Calculate department comparison
              const departmentData = departments.filter(dept => dept !== 'All').map(dept => {
                const deptRecords = filteredData.filter((a: Attendance) => a.employeeDepartment === dept);
                const presentRecords = deptRecords.filter((a: Attendance) => 
                  a.status === AttendanceStatus.PRESENT || 
                  a.status === AttendanceStatus.WORK_FROM_HOME ||
                  a.status === AttendanceStatus.LATE
                ).length;
                const rate = deptRecords.length > 0 ? (presentRecords / deptRecords.length) * 100 : 0;
                return { department: dept, rate, total: deptRecords.length };
              });

              // Status distribution
              const statusData = Object.values(AttendanceStatus).map(status => {
                const count = filteredData.filter((a: Attendance) => a.status === status).length;
                return { status, count, percentage: filteredData.length > 0 ? (count / filteredData.length) * 100 : 0 };
              }).filter(item => item.count > 0);

              // Monthly comparison (current vs previous periods)
              const currentPeriodDays = Math.ceil((new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 60 * 60 * 24));
              const previousStartDate = new Date(startDate);
              previousStartDate.setDate(previousStartDate.getDate() - currentPeriodDays);
              const previousEndDate = new Date(startDate);
              previousEndDate.setDate(previousEndDate.getDate() - 1);

              const previousData = attendances.filter((a: Attendance) => 
                a.date >= previousStartDate.toISOString().split('T')[0] && 
                a.date <= previousEndDate.toISOString().split('T')[0] &&
                (selectedDepartment === 'All' || a.employeeDepartment === selectedDepartment) &&
                (!selectedEmployee || a.employeeId === selectedEmployee)
              );

              const currentRate = filteredData.length > 0 ? 
                (filteredData.filter((a: Attendance) => 
                  a.status === AttendanceStatus.PRESENT || 
                  a.status === AttendanceStatus.WORK_FROM_HOME ||
                  a.status === AttendanceStatus.LATE
                ).length / filteredData.length) * 100 : 0;

              const previousRate = previousData.length > 0 ? 
                (previousData.filter((a: Attendance) => 
                  a.status === AttendanceStatus.PRESENT || 
                  a.status === AttendanceStatus.WORK_FROM_HOME ||
                  a.status === AttendanceStatus.LATE
                ).length / previousData.length) * 100 : 0;

              // Find best and worst performing days
              const bestDay = weeklyData.reduce((max, day) => day.rate > max.rate ? day : max, weeklyData[0]);
              const worstDay = weeklyData.reduce((min, day) => day.rate < min.rate ? day : min, weeklyData[0]);

              // Chart configurations
              const lineChartData = {
                labels: dailyTrends.slice(-14).map(d => new Date(d.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })),
                datasets: [
                  {
                    label: 'Attendance Rate %',
                    data: dailyTrends.slice(-14).map(d => d.rate),
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4,
                  },
                ],
              };

              const barChartData = {
                labels: weeklyData.map(d => d.day),
                datasets: [
                  {
                    label: 'Attendance Rate %',
                    data: weeklyData.map(d => d.rate),
                    backgroundColor: weeklyData.map(d => 
                      d.rate >= 90 ? 'rgba(34, 197, 94, 0.8)' :
                      d.rate >= 80 ? 'rgba(59, 130, 246, 0.8)' :
                      'rgba(251, 191, 36, 0.8)'
                    ),
                  },
                ],
              };

              const doughnutChartData = {
                labels: statusData.map(s => s.status.replace('_', ' ').toUpperCase()),
                datasets: [
                  {
                    data: statusData.map(s => s.count),
                    backgroundColor: [
                      '#10B981', '#3B82F6', '#F59E0B', '#EF4444', '#8B5CF6',
                      '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
                    ],
                  },
                ],
              };

              const chartOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'top' as const,
                  },
                },
                scales: {
                  y: {
                    beginAtZero: true,
                    max: 100,
                  },
                },
              };

              return (
                <>
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Advanced Attendance Trends</h3>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {filteredData.length} records analyzed
                    </div>
                  </div>

                  {/* Summary Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div className="bg-gradient-to-r from-blue-500 to-blue-600 dark:from-blue-600 dark:to-blue-700 p-4 rounded-lg text-white shadow-lg dark:shadow-gray-900/20">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-blue-100 dark:text-blue-200 text-sm">Current Period</p>
                          <p className="text-2xl font-bold">{currentRate.toFixed(1)}%</p>
                        </div>
                        <TrendingUp className="h-8 w-8 text-blue-200 dark:text-blue-300" />
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-green-500 to-green-600 p-4 rounded-lg text-white">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-green-100 text-sm">Trend vs Previous</p>
                          <p className="text-2xl font-bold flex items-center">
                            {currentRate > previousRate ? (
                              <>+{(currentRate - previousRate).toFixed(1)}% <ArrowUp className="h-4 w-4 ml-1" /></>
                            ) : currentRate < previousRate ? (
                              <>{(currentRate - previousRate).toFixed(1)}% <ArrowDown className="h-4 w-4 ml-1" /></>
                            ) : (
                              <>0% <Minus className="h-4 w-4 ml-1" /></>
                            )}
                          </p>
                        </div>
                        <Activity className="h-8 w-8 text-green-200" />
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-4 rounded-lg text-white">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-purple-100 text-sm">Best Day</p>
                          <p className="text-lg font-bold">{bestDay.day}</p>
                          <p className="text-sm text-purple-200">{bestDay.rate.toFixed(1)}%</p>
                        </div>
                        <CheckCircle2 className="h-8 w-8 text-purple-200" />
                      </div>
                    </div>

                    <div className="bg-gradient-to-r from-orange-500 to-orange-600 p-4 rounded-lg text-white">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-orange-100 text-sm">Focus Day</p>
                          <p className="text-lg font-bold">{worstDay.day}</p>
                          <p className="text-sm text-orange-200">{worstDay.rate.toFixed(1)}%</p>
                        </div>
                        <AlertTriangle className="h-8 w-8 text-orange-200" />
                      </div>
                    </div>
                  </div>

                  {/* Charts Section */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    {/* Daily Trend Line Chart */}
                    <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 transition-colors duration-200">
                      <h4 className="text-md font-semibold text-gray-800 dark:text-white mb-4">Daily Attendance Trend (Last 14 Days)</h4>
                      <div className="h-64">
                        <Line data={lineChartData} options={chartOptions} />
                      </div>
                    </div>

                    {/* Weekly Pattern Bar Chart */}
                    <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 transition-colors duration-200">
                      <h4 className="text-md font-semibold text-gray-800 dark:text-white mb-4">Weekly Attendance Pattern</h4>
                      <div className="h-64">
                        <Bar data={barChartData} options={chartOptions} />
                      </div>
                    </div>
                  </div>

                  {/* Status Distribution and Department Comparison */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
                    {/* Enhanced Professional Status Distribution */}
                    <div className="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-700 rounded-xl shadow-sm border border-gray-200 dark:border-gray-600 p-4 transition-colors duration-200">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-2">
                          <div className="p-1.5 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                            <PieChart className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div>
                            <h4 className="text-sm font-semibold text-gray-900 dark:text-white">Status Distribution</h4>
                            <p className="text-xs text-gray-500 dark:text-gray-400">Comprehensive attendance breakdown</p>
                          </div>
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                          {filteredData.length} Records
                        </div>
                      </div>
                      
                      {/* Enhanced Chart with Better Design */}
                      <div className="relative">
                        <div className="h-48 relative mb-4">
                          <Doughnut 
                            data={{
                              labels: statusData.map(s => {
                                const statusLabels: { [key: string]: string } = {
                                  'PRESENT': '✅ Present',
                                  'ABSENT': '❌ Absent',
                                  'LATE': '⏰ Late Arrival',
                                  'WORK_FROM_HOME': '🏠 Work From Home',
                                  'HALF_DAY': '🕐 Half Day',
                                  'LEAVE': '🏖️ Leave',
                                  'SICK_LEAVE': '🤒 Sick Leave',
                                  'ANNUAL_LEAVE': '🌴 Annual Leave',
                                  'PAID_TIME_OFF': '💼 Paid Time Off',
                                  'UNPAID_LEAVE': '📋 Unpaid Leave',
                                  'MATERNITY_LEAVE': '👶 Maternity Leave',
                                  'PATERNITY_LEAVE': '👨‍👶 Paternity Leave'
                                };
                                return statusLabels[s.status] || s.status.replace('_', ' ').toUpperCase();
                              }),
                              datasets: [{
                                data: statusData.map(s => s.count),
                                backgroundColor: [
                                  '#10B981', // Present - Green
                                  '#EF4444', // Absent - Red
                                  '#F59E0B', // Late - Amber
                                  '#3B82F6', // WFH - Blue
                                  '#8B5CF6', // Half Day - Purple
                                  '#06B6D4', // Leave - Cyan
                                  '#F97316', // Sick - Orange
                                  '#84CC16', // Annual - Lime
                                  '#EC4899', // PTO - Pink
                                  '#6366F1', // Unpaid - Indigo
                                  '#14B8A6', // Maternity - Teal
                                  '#A855F7'  // Paternity - Violet
                                ],
                                borderColor: '#ffffff',
                                borderWidth: 2,
                                hoverBorderWidth: 3,
                                hoverOffset: 4
                              }]
                            }} 
                            options={{
                              responsive: true,
                              maintainAspectRatio: false,
                              plugins: {
                                legend: {
                                  display: false // We'll create a custom legend
                                },
                                tooltip: {
                                  backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                  titleColor: '#ffffff',
                                  bodyColor: '#ffffff',
                                  borderColor: '#374151',
                                  borderWidth: 1,
                                  cornerRadius: 6,
                                  displayColors: true,
                                  callbacks: {
                                    label: function(context: any) {
                                      const total = statusData.reduce((sum, item) => sum + item.count, 0);
                                      const percentage = ((context.parsed / total) * 100).toFixed(1);
                                      return `${context.label}: ${context.parsed} (${percentage}%)`;
                                    }
                                  }
                                }
                              },
                              cutout: '65%', // Creates the doughnut hole
                              elements: {
                                arc: {
                                  borderRadius: 3
                                }
                              }
                            }} 
                          />
                          
                          {/* Center Statistics */}
                          <div className="absolute inset-0 flex items-center justify-center">
                            <div className="text-center">
                              <div className="text-xl font-bold text-gray-900 dark:text-white">
                                {filteredData.length}
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                                Total Records
                              </div>
                              <div className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                                {new Date(startDate).toLocaleDateString()} - {new Date(endDate).toLocaleDateString()}
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        {/* Compact Professional Legend */}
                        <div className="space-y-1.5 max-h-32 overflow-y-auto">
                          {statusData
                            .sort((a, b) => b.count - a.count) // Sort by count descending
                            .slice(0, 6) // Show only top 6 statuses to save space
                            .map((item, index) => {
                              const colors = [
                                '#10B981', '#EF4444', '#F59E0B', '#3B82F6', '#8B5CF6', '#06B6D4',
                                '#F97316', '#84CC16', '#EC4899', '#6366F1', '#14B8A6', '#A855F7'
                              ];
                              const total = statusData.reduce((sum, s) => sum + s.count, 0);
                              const percentage = total > 0 ? ((item.count / total) * 100) : 0;
                              
                              const statusLabels: { [key: string]: string } = {
                                'PRESENT': '✅ Present',
                                'ABSENT': '❌ Absent', 
                                'LATE': '⏰ Late Arrival',
                                'WORK_FROM_HOME': '🏠 Work From Home',
                                'HALF_DAY': '🕐 Half Day',
                                'LEAVE': '🏖️ Leave',
                                'SICK_LEAVE': '🤒 Sick Leave',
                                'ANNUAL_LEAVE': '🌴 Annual Leave',
                                'PAID_TIME_OFF': '💼 Paid Time Off',
                                'UNPAID_LEAVE': '📋 Unpaid Leave',
                                'MATERNITY_LEAVE': '👶 Maternity Leave',
                                'PATERNITY_LEAVE': '👨‍👶 Paternity Leave'
                              };
                              
                              return (
                                <div key={item.status} className="flex items-center justify-between p-1.5 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                  <div className="flex items-center space-x-2">
                                    <div 
                                      className="w-3 h-3 rounded-full border border-white dark:border-gray-600 shadow-sm" 
                                      style={{ backgroundColor: colors[index] || '#6B7280' }}
                                    ></div>
                                    <span className="text-xs font-medium text-gray-700 dark:text-gray-300 truncate">
                                      {statusLabels[item.status] || item.status.replace('_', ' ').toUpperCase()}
                                    </span>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <span className="text-xs font-semibold text-gray-900 dark:text-white">
                                      {item.count}
                                    </span>
                                    <div className="flex items-center space-x-1">
                                      <div className="w-12 h-1.5 bg-gray-200 dark:bg-gray-600 rounded-full overflow-hidden">
                                        <div 
                                          className="h-full rounded-full transition-all duration-500" 
                                          style={{ 
                                            width: `${percentage}%`,
                                            backgroundColor: colors[index] || '#6B7280'
                                          }}
                                        />
                                      </div>
                                      <span className="text-xs font-medium text-gray-500 dark:text-gray-400 min-w-[2.5rem]">
                                        {percentage.toFixed(1)}%
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              );
                            })}
                            {statusData.length > 6 && (
                              <div className="text-xs text-gray-500 dark:text-gray-400 text-center py-1">
                                +{statusData.length - 6} more statuses
                              </div>
                            )}
                        </div>
                        
                        {/* Compact Status Summary Cards */}
                        <div className="mt-4 grid grid-cols-2 gap-2">
                          <div className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700 rounded-lg p-2">
                            <div className="flex items-center space-x-1">
                              <div className="w-1.5 h-1.5 bg-green-500 dark:bg-green-400 rounded-full"></div>
                              <span className="text-xs font-medium text-green-800 dark:text-green-300">Active</span>
                            </div>
                            <div className="mt-1">
                              <div className="text-lg font-bold text-green-900">
                                {statusData
                                  .filter(s => ['PRESENT', 'WORK_FROM_HOME', 'LATE'].includes(s.status))
                                  .reduce((sum, s) => sum + s.count, 0)}
                              </div>
                              <div className="text-xs text-green-600">
                                Present + WFH + Late
                              </div>
                            </div>
                          </div>
                          
                          <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 rounded-lg p-2">
                            <div className="flex items-center space-x-1">
                              <div className="w-1.5 h-1.5 bg-red-500 dark:bg-red-400 rounded-full"></div>
                              <span className="text-xs font-medium text-red-800 dark:text-red-300">Inactive</span>
                            </div>
                            <div className="mt-1">
                              <div className="text-lg font-bold text-red-900 dark:text-red-200">
                                {statusData
                                  .filter(s => !['PRESENT', 'WORK_FROM_HOME', 'LATE'].includes(s.status))
                                  .reduce((sum, s) => sum + s.count, 0)}
                              </div>
                              <div className="text-xs text-red-600 dark:text-red-400">
                                Absent + Leaves
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Compact Department Performance */}
                    <div className="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-700 rounded-xl shadow-sm border border-gray-200 dark:border-gray-600 p-4 transition-colors duration-200">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-2">
                          <div className="p-1.5 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg">
                            <Building2 className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                          </div>
                          <div>
                            <h4 className="text-sm font-semibold text-gray-900 dark:text-white">Department Performance</h4>
                            <p className="text-xs text-gray-500 dark:text-gray-400">Attendance rates by department</p>
                          </div>
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                          {departmentData.length} Departments
                        </div>
                      </div>
                      
                      <div className="space-y-3 max-h-80 overflow-y-auto">
                        {departmentData.sort((a, b) => b.rate - a.rate).map((dept, index) => (
                          <div key={dept.department} className="relative">
                            {/* Compact Department Header */}
                            <div className="flex items-center justify-between mb-1.5">
                              <div className="flex items-center space-x-2">
                                <div className={`w-2.5 h-2.5 rounded-full ${
                                  dept.rate >= 95 ? 'bg-emerald-500 dark:bg-emerald-400' :
                                  dept.rate >= 90 ? 'bg-green-500 dark:bg-green-400' :
                                  dept.rate >= 80 ? 'bg-blue-500 dark:bg-blue-400' :
                                  dept.rate >= 70 ? 'bg-yellow-500 dark:bg-yellow-400' :
                                  dept.rate >= 60 ? 'bg-orange-500 dark:bg-orange-400' : 'bg-red-500 dark:bg-red-400'
                                }`}></div>
                                <span className="text-sm font-semibold text-gray-800 dark:text-gray-200 truncate">{dept.department}</span>
                                {index === 0 && (
                                  <span className="text-xs bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400 px-1.5 py-0.5 rounded-full font-medium">
                                    🏆 Top
                                  </span>
                                )}
                              </div>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm font-bold text-gray-900 dark:text-white">{dept.rate.toFixed(1)}%</span>
                                <span className="text-xs text-gray-500 dark:text-gray-400">({dept.total})</span>
                              </div>
                            </div>
                            
                            {/* Compact Progress Bar */}
                            <div className="w-full bg-gray-100 dark:bg-gray-600 rounded-full h-2 mb-2 overflow-hidden">
                              <div 
                                className={`h-full rounded-full transition-all duration-700 ease-out ${
                                  dept.rate >= 95 ? 'bg-gradient-to-r from-emerald-500 to-emerald-600 dark:from-emerald-400 dark:to-emerald-500' :
                                  dept.rate >= 90 ? 'bg-gradient-to-r from-green-500 to-green-600 dark:from-green-400 dark:to-green-500' :
                                  dept.rate >= 80 ? 'bg-gradient-to-r from-blue-500 to-blue-600 dark:from-blue-400 dark:to-blue-500' :
                                  dept.rate >= 70 ? 'bg-gradient-to-r from-yellow-500 to-yellow-600 dark:from-yellow-400 dark:to-yellow-500' :
                                  dept.rate >= 60 ? 'bg-gradient-to-r from-orange-500 to-orange-600 dark:from-orange-400 dark:to-orange-500' : 
                                  'bg-gradient-to-r from-red-500 to-red-600 dark:from-red-400 dark:to-red-500'
                                }`}
                                style={{ width: `${dept.rate}%` }}
                              ></div>
                            </div>
                            
                            {/* Compact Performance Indicators */}
                            <div className="flex items-center justify-between text-xs">
                              <div className="flex items-center space-x-1">
                                <div className={`flex items-center space-x-1 ${
                                  dept.rate >= 90 ? 'text-green-600 dark:text-green-400' :
                                  dept.rate >= 80 ? 'text-blue-600 dark:text-blue-400' :
                                  dept.rate >= 70 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400'
                                }`}>
                                  {dept.rate >= 90 ? (
                                    <CheckCircle2 className="h-3 w-3" />
                                  ) : dept.rate >= 80 ? (
                                    <Clock className="h-3 w-3" />
                                  ) : (
                                    <AlertTriangle className="h-3 w-3" />
                                  )}
                                  <span className="font-medium">
                                    {dept.rate >= 90 ? 'Excellent' :
                                     dept.rate >= 80 ? 'Good' :
                                     dept.rate >= 70 ? 'Average' : 'Attention'}
                                  </span>
                                </div>
                              </div>
                              <div className="text-gray-500 dark:text-gray-400">
                                Target: 90%
                              </div>
                            </div>
                          </div>
                        ))}
                        
                        {departmentData.length === 0 && (
                          <div className="text-center py-6">
                            <Building2 className="h-8 w-8 text-gray-300 dark:text-gray-600 mx-auto mb-2" />
                            <p className="text-gray-500 dark:text-gray-400 text-xs">No department data available</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Insights and Recommendations */}
                  <div className="bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-900/20 dark:to-blue-900/20 border border-indigo-200 dark:border-indigo-700 rounded-lg p-6 transition-colors duration-200">
                    <h4 className="text-md font-semibold text-indigo-900 dark:text-indigo-300 mb-4 flex items-center">
                      <Target className="h-5 w-5 mr-2" />
                      Key Insights & Recommendations
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-green-500 dark:bg-green-400 rounded-full mt-2"></div>
                          <div>
                            <p className="text-sm font-medium text-gray-800 dark:text-gray-200">Best Performance</p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">
                              {bestDay.day} shows highest attendance ({bestDay.rate.toFixed(1)}%)
                            </p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-orange-500 dark:bg-orange-400 rounded-full mt-2"></div>
                          <div>
                            <p className="text-sm font-medium text-gray-800 dark:text-gray-200">Improvement Opportunity</p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">
                              Focus on {worstDay.day} attendance ({worstDay.rate.toFixed(1)}%)
                            </p>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full mt-2"></div>
                          <div>
                            <p className="text-sm font-medium text-gray-800 dark:text-gray-200">Trend Analysis</p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">
                              {currentRate > previousRate ? 'Positive trend' : currentRate < previousRate ? 'Declining trend' : 'Stable pattern'} compared to previous period
                            </p>
                          </div>
                        </div>
                        <div className="flex items-start space-x-3">
                          <div className="w-2 h-2 bg-purple-500 dark:bg-purple-400 rounded-full mt-2"></div>
                          <div>
                            <p className="text-sm font-medium text-gray-800 dark:text-gray-200">Data Coverage</p>
                            <p className="text-xs text-gray-600 dark:text-gray-400">
                              Analysis based on {filteredData.length} attendance records
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              );
            })()}
          </div>
        </div>
      )}

      {reportType === 'department' && (
        <div className="space-y-3">
          {/* Enhanced Professional Filters */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-colors duration-200">
            {/* Filter Header */}
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-700 border-b border-gray-200 dark:border-gray-600">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                  <Filter className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-gray-900 dark:text-white">Smart Filters</h4>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {getFilteredAttendances().length} of {attendances.length} records
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {/* Quick Filter Presets */}
                <div className="hidden md:flex items-center space-x-1 mr-3">
                  <span className="text-xs text-gray-500 dark:text-gray-400">Quick:</span>
                  <button
                    onClick={() => {
                      setDatePeriod('Today');
                      handleDatePeriodChange('Today');
                    }}
                    className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors duration-200"
                  >
                    Today
                  </button>
                  <button
                    onClick={() => {
                      setDatePeriod('This Week');
                      handleDatePeriodChange('This Week');
                    }}
                    className="px-2 py-1 text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors duration-200"
                  >
                    This Week
                  </button>
                  <button
                    onClick={() => {
                      setDatePeriod('This Month');
                      handleDatePeriodChange('This Month');
                    }}
                    className="px-2 py-1 text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 rounded hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors duration-200"
                  >
                    This Month
                  </button>
                </div>
                
                {/* Active Filters Indicator */}
                {(selectedEmployee || selectedDepartment !== 'All' || selectedPosition || selectedLocation || selectedStatus || selectedShift || searchQuery) && (
                  <div className="flex items-center space-x-1 px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400 rounded text-xs">
                    <div className="w-1.5 h-1.5 bg-yellow-500 dark:bg-yellow-400 rounded-full animate-pulse"></div>
                    <span className="font-medium">
                      {[selectedEmployee ? 1 : 0, selectedDepartment !== 'All' ? 1 : 0, selectedPosition ? 1 : 0, selectedLocation ? 1 : 0, selectedStatus ? 1 : 0, selectedShift ? 1 : 0, searchQuery ? 1 : 0].filter(Boolean).length} Active
                    </span>
                  </div>
                )}
                
                <button 
                  onClick={() => setShowFilters(!showFilters)}
                  className="inline-flex items-center px-3 py-1.5 border border-blue-200 dark:border-blue-700 rounded-md text-xs font-medium text-blue-700 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors duration-200"
                >
                  <Filter className="h-3 w-3 mr-1" />
                  {showFilters ? 'Hide' : 'Show'} Advanced
                </button>
              </div>
            </div>

            <div className="p-4">
              {/* Primary Filters Row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
                {/* Period Selector with enhanced styling */}
                <div className="md:col-span-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <CalendarIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Time Period</label>
                    {datePeriod !== 'This Month' && (
                      <span className="text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 px-1.5 py-0.5 rounded">Custom</span>
                    )}
                  </div>
                  <select
                    value={datePeriod}
                    onChange={(e) => handleDatePeriodChange(e.target.value)}
                    className="w-full pl-3 pr-8 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500"
                  >
                    <option value="Today">📅 Today</option>
                    <option value="Yesterday">⏮️ Yesterday</option>
                    <option value="This Week">📊 This Week</option>
                    <option value="Last Week">📈 Last Week</option>
                    <option value="This Month">🗓️ This Month</option>
                    <option value="Last Month">📆 Last Month</option>
                    <option value="Last Three Months">🗂️ Last 3 Months</option>
                    <option value="Last Six Months">📋 Last 6 Months</option>
                    <option value="This Year">🗃️ This Year</option>
                    <option value="Last Year">📁 Last Year</option>
                    <option value="User Defined">⚙️ Custom Range</option>
                  </select>
                </div>

                {/* Enhanced Search Field */}
                <div className="md:col-span-2">
                  <div className="flex items-center space-x-2 mb-2">
                    <Search className="h-4 w-4 text-green-600 dark:text-green-400" />
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Global Search</label>
                    {searchQuery && (
                      <span className="text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 px-1.5 py-0.5 rounded">Active</span>
                    )}
                  </div>
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="🔍 Search employees, departments, positions, status..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-10 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-green-500 dark:focus:border-green-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500"
                    />
                    <Search className="h-4 w-4 text-gray-400 dark:text-gray-500 absolute left-3 top-1/2 transform -translate-y-1/2" />
                    {searchQuery && (
                      <button
                        onClick={() => setSearchQuery('')}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 hover:text-red-500 dark:hover:text-red-400 transition-colors duration-200"
                      >
                        <XCircle className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Enhanced Custom Date Range */}
              {datePeriod === "User Defined" && (
                <div className="mb-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 border border-blue-200 dark:border-blue-700 rounded-lg">
                  <div className="flex items-center space-x-2 mb-3">
                    <Calendar className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    <span className="text-sm font-medium text-blue-900 dark:text-blue-300">Custom Date Range</span>
                    <div className="flex-1 border-t border-blue-200 dark:border-blue-700"></div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
                    <div className="md:col-span-2">
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">📅 Start Date</label>
                      <input
                        type="date"
                        className="w-full py-2.5 px-3 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200" 
                        value={userDefinedDates.start}
                        onChange={(e) => {
                          setUserDefinedDates(prev => ({
                            ...prev,
                            start: e.target.value
                          }));
                        }}
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">📅 End Date</label>
                      <input
                        type="date"
                        className="w-full py-2.5 px-3 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white transition-all duration-200" 
                        value={userDefinedDates.end}
                        onChange={(e) => {
                          setUserDefinedDates(prev => ({
                            ...prev,
                            end: e.target.value
                          }));
                        }}
                      />
                    </div>
                    <div className="flex items-end">
                      <button 
                        className="w-full px-4 py-2.5 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow"
                        disabled={
                          !userDefinedDates.start ||
                          !userDefinedDates.end ||
                          userDefinedDates.start > userDefinedDates.end
                        }
                        onClick={() => {
                          setStartDate(userDefinedDates.start);
                          setEndDate(userDefinedDates.end);
                        }}
                      >
                        ✅ Apply Range
                      </button>
                    </div>
                  </div>
                </div>
              )}
              
              {showFilters && (
                <>
                  {/* Enhanced Filter Grid with better grouping */}
                  <div className="bg-gray-50 rounded-lg p-4 mb-4">
                    <div className="flex items-center space-x-2 mb-4">
                      <Settings className="h-4 w-4 text-gray-600" />
                      <h5 className="text-sm font-medium text-gray-700">Advanced Filters</h5>
                      <div className="flex-1 border-t border-gray-300"></div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {/* People Filters Group */}
                      <div className="space-y-3">
                        <div className="text-xs font-semibold text-gray-600 uppercase tracking-wide flex items-center">
                          <Users className="h-3 w-3 mr-1" />
                          People
                        </div>
                        
                        {/* Employee Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Users className="h-3 w-3 text-indigo-600" />
                            <label className="text-xs font-medium text-gray-700">Employee</label>
                            {selectedEmployee && <span className="text-xs bg-indigo-100 text-indigo-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedEmployee || ''}
                            onChange={(e) => setSelectedEmployee(e.target.value ? parseInt(e.target.value) : null)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">👥 All Employees</option>
                            {employees.map((employee) => (
                              <option key={employee.id} value={employee.id}>👤 {employee.name}</option>
                            ))}
                          </select>
                        </div>

                        {/* Department Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Building2 className="h-3 w-3 text-blue-600" />
                            <label className="text-xs font-medium text-gray-700">Department</label>
                            {selectedDepartment !== 'All' && <span className="text-xs bg-blue-100 text-blue-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedDepartment}
                            onChange={(e) => setSelectedDepartment(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            {departments.map((dept, i) => (
                              <option key={i} value={dept}>🏢 {dept}</option>
                            ))}
                          </select>
                        </div>
                      </div>

                      {/* Work Filters Group */}
                      <div className="space-y-3">
                        <div className="text-xs font-semibold text-gray-600 uppercase tracking-wide flex items-center">
                          <Briefcase className="h-3 w-3 mr-1" />
                          Work Details
                        </div>
                        
                        {/* Position Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Briefcase className="h-3 w-3 text-green-600" />
                            <label className="text-xs font-medium text-gray-700">Position</label>
                            {selectedPosition && <span className="text-xs bg-green-100 text-green-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedPosition}
                            onChange={(e) => setSelectedPosition(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">💼 All Positions</option>
                            {uniquePositions.map((position) => (
                              <option key={position} value={position}>🎯 {position}</option>
                            ))}
                          </select>
                        </div>

                        {/* Shift Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Clock7 className="h-3 w-3 text-teal-600" />
                            <label className="text-xs font-medium text-gray-700">Shift</label>
                            {selectedShift && <span className="text-xs bg-teal-100 text-teal-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedShift}
                            onChange={(e) => setSelectedShift(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">🕒 All Shifts</option>
                            {uniqueShifts.map((shift) => (
                              <option key={shift} value={shift}>⏰ {shift}</option>
                            ))}
                          </select>
                        </div>
                      </div>

                      {/* Status & Location Group */}
                      <div className="space-y-3">
                        <div className="text-xs font-semibold text-gray-600 uppercase tracking-wide flex items-center">
                          <Activity className="h-3 w-3 mr-1" />
                          Status & Location
                        </div>
                        
                        {/* Status Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Activity className="h-3 w-3 text-orange-600" />
                            <label className="text-xs font-medium text-gray-700">Status</label>
                            {selectedStatus && <span className="text-xs bg-orange-100 text-orange-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedStatus}
                            onChange={(e) => setSelectedStatus(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">📊 All Status</option>
                            <option value={AttendanceStatus.PRESENT}>✅ Present</option>
                            <option value={AttendanceStatus.ABSENT}>❌ Absent</option>
                            <option value={AttendanceStatus.LATE}>⏰ Late</option>
                            <option value={AttendanceStatus.WORK_FROM_HOME}>🏠 WFH</option>
                            <option value={AttendanceStatus.HALF_DAY}>🕐 Half Day</option>
                            <option value={AttendanceStatus.LEAVE}>🏖️ Leave</option>
                            <option value={AttendanceStatus.SICK_LEAVE}>🤒 Sick</option>
                            <option value={AttendanceStatus.ANNUAL_LEAVE}>🌴 Annual</option>
                          </select>
                        </div>

                        {/* Location Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <svg className="h-3 w-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <label className="text-xs font-medium text-gray-700">Location</label>
                            {selectedLocation && <span className="text-xs bg-purple-100 text-purple-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedLocation}
                            onChange={(e) => setSelectedLocation(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">📍 All Locations</option>
                            {uniqueLocations.map((location) => (
                              <option key={location} value={location}>🌍 {location}</option>
                            ))}
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* Enhanced Actions Bar */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-600">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                    <div className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full animate-pulse"></div>
                    <span className="font-semibold text-blue-700 dark:text-blue-400">{getFilteredAttendances().length}</span>
                    <span>of</span>
                    <span className="font-medium">{attendances.length}</span>
                    <span>records shown</span>
                  </div>
                  
                  {/* Data Quality Indicator */}
                  {attendances.length === 0 ? (
                    <div className="flex items-center space-x-1 text-xs text-amber-600 dark:text-amber-400">
                      <AlertTriangle className="h-3 w-3" />
                      <span>No data available</span>
                    </div>
                  ) : getFilteredAttendances().length === 0 ? (
                    <div className="flex items-center space-x-1 text-xs text-orange-600 dark:text-orange-400">
                      <AlertTriangle className="h-3 w-3" />
                      <span>No matches found</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-1 text-xs text-green-600 dark:text-green-400">
                      <CheckCircle2 className="h-3 w-3" />
                      <span>Data loaded</span>
                    </div>
                  )}
                </div>
                
                <div className="flex items-center space-x-2">
                  <button 
                    onClick={resetFilters}
                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md text-xs font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 shadow-sm hover:shadow"
                  >
                    <RefreshCw className="h-3 w-3 mr-1" />
                    Reset All
                  </button>
                  <button
                    onClick={() => setShowFilters(false)}
                    className="inline-flex items-center px-4 py-1.5 bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white rounded-md text-xs font-medium transition-all duration-200 shadow-sm hover:shadow"
                  >
                    <CheckCircle2 className="h-3 w-3 mr-1" />
                    Apply Filters
                  </button>
                </div>
              </div>
            </div>
          </div>
            
          {/* Department Analysis */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 transition-colors duration-200">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">Department Performance</h3>
            {(() => {
              // Check if we have any data for departments
              const availableDepartments = departments.filter(dept => dept !== 'All');
              const hasAnyDeptData = availableDepartments.some(dept => {
                const deptAttendances = attendances.filter(a => 
                  a.employeeDepartment === dept &&
                  a.date >= startDate && 
                  a.date <= endDate
                );
                return deptAttendances.length > 0;
              });

              if (!hasAnyDeptData || availableDepartments.length === 0) {
                return (
                  <div className="text-center py-8">
                    <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-gray-100 dark:bg-gray-700 mb-4">
                      <Building2 className="h-8 w-8 text-gray-400 dark:text-gray-500" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Department Data</h3>
                    <p className="text-gray-500 dark:text-gray-400">
                      No department attendance data available for the selected period.
                    </p>
                  </div>
                );
              }

              return (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {departments.filter(dept => dept !== 'All').map((dept, index) => {
                    const deptAttendances = attendances.filter(a => 
                      a.employeeDepartment === dept &&
                      a.date >= startDate && 
                      a.date <= endDate
                    );
                    
                    console.log(`🏢 Department ${dept} analysis:`, {
                      departmentName: dept,
                      totalAttendances: attendances.length,
                      matchingRecords: deptAttendances.length,
                      sampleAttendanceRecord: attendances[0],
                      filterCriteria: { startDate, endDate },
                      uniqueAttendanceDepts: [...new Set(attendances.map((a: Attendance) => a.employeeDepartment || a.department))]
                    });
                    
                    const deptPresent = deptAttendances.filter(a => 
                      a.status === AttendanceStatus.PRESENT || 
                      a.status === AttendanceStatus.WORK_FROM_HOME
                    ).length;
                    
                    const deptTotal = deptAttendances.length;
                    const deptRate = deptTotal > 0 ? (deptPresent / deptTotal) * 100 : 0;
                    
                    return (
                      <div key={index} className="border border-gray-100 dark:border-gray-600 rounded-lg p-5 hover:shadow-md dark:hover:shadow-gray-900/20 transition-shadow bg-white dark:bg-gray-700">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center">
                            <div className="p-2 bg-indigo-50 dark:bg-indigo-900/30 rounded-lg mr-3">
                              <Building2 className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                            </div>
                            <div>
                              <h4 className="text-sm font-semibold text-gray-900 dark:text-white">{dept}</h4>
                              <p className="text-xs text-gray-500 dark:text-gray-400">{new Set(deptAttendances.map(a => a.employeeId)).size} employees</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold text-gray-900 dark:text-white">{deptRate.toFixed(1)}%</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">attendance</div>
                          </div>
                        </div>
                        
                        <div className="w-full bg-gray-100 dark:bg-gray-600 rounded-full h-3">
                          <div 
                            className={`h-3 rounded-full transition-all duration-300 ${
                              deptRate >= 90 ? 'bg-emerald-500 dark:bg-emerald-400' :
                              deptRate >= 75 ? 'bg-amber-500 dark:bg-amber-400' : 'bg-red-500 dark:bg-red-400'
                            }`}
                            style={{ width: `${Math.min(deptRate, 100)}%` }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              );
            })()}
          </div>
        </div>
      )}

      {reportType === 'export' && (
        <div className="space-y-3">
          {/* Enhanced Professional Filters */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-colors duration-200">
            {/* Filter Header */}
            <div className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-700 border-b border-gray-200 dark:border-gray-600">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                  <Filter className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-gray-900 dark:text-white">Smart Filters</h4>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {getFilteredAttendances().length} of {attendances.length} records
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {/* Quick Filter Presets */}
                <div className="hidden md:flex items-center space-x-1 mr-3">
                  <span className="text-xs text-gray-500 dark:text-gray-400">Quick:</span>
                  <button
                    onClick={() => {
                      setDatePeriod('Today');
                      handleDatePeriodChange('Today');
                    }}
                    className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors duration-200"
                  >
                    Today
                  </button>
                  <button
                    onClick={() => {
                      setDatePeriod('This Week');
                      handleDatePeriodChange('This Week');
                    }}
                    className="px-2 py-1 text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors duration-200"
                  >
                    This Week
                  </button>
                  <button
                    onClick={() => {
                      setDatePeriod('This Month');
                      handleDatePeriodChange('This Month');
                    }}
                    className="px-2 py-1 text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 rounded hover:bg-purple-200 dark:hover:bg-purple-900/50 transition-colors duration-200"
                  >
                    This Month
                  </button>
                </div>
                
                {/* Active Filters Indicator */}
                {(selectedEmployee || selectedDepartment !== 'All' || selectedPosition || selectedLocation || selectedStatus || selectedShift || searchQuery) && (
                  <div className="flex items-center space-x-1 px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400 rounded text-xs">
                    <div className="w-1.5 h-1.5 bg-yellow-500 dark:bg-yellow-400 rounded-full animate-pulse"></div>
                    <span className="font-medium">
                      {[selectedEmployee ? 1 : 0, selectedDepartment !== 'All' ? 1 : 0, selectedPosition ? 1 : 0, selectedLocation ? 1 : 0, selectedStatus ? 1 : 0, selectedShift ? 1 : 0, searchQuery ? 1 : 0].filter(Boolean).length} Active
                    </span>
                  </div>
                )}
                
                <button 
                  onClick={() => setShowFilters(!showFilters)}
                  className="inline-flex items-center px-3 py-1.5 border border-blue-200 dark:border-blue-700 rounded-md text-xs font-medium text-blue-700 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors duration-200"
                >
                  <Filter className="h-3 w-3 mr-1" />
                  {showFilters ? 'Hide' : 'Show'} Advanced
                </button>
              </div>
            </div>

            <div className="p-4">
              {/* Primary Filters Row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
                {/* Period Selector with enhanced styling */}
                <div className="md:col-span-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <CalendarIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Time Period</label>
                    {datePeriod !== 'This Month' && (
                      <span className="text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 px-1.5 py-0.5 rounded">Custom</span>
                    )}
                  </div>
                  <select
                    value={datePeriod}
                    onChange={(e) => handleDatePeriodChange(e.target.value)}
                    className="w-full pl-3 pr-8 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500"
                  >
                    <option value="Today">📅 Today</option>
                    <option value="Yesterday">⏮️ Yesterday</option>
                    <option value="This Week">📊 This Week</option>
                    <option value="Last Week">📈 Last Week</option>
                    <option value="This Month">🗓️ This Month</option>
                    <option value="Last Month">📆 Last Month</option>
                    <option value="Last Three Months">🗂️ Last 3 Months</option>
                    <option value="Last Six Months">📋 Last 6 Months</option>
                    <option value="This Year">🗃️ This Year</option>
                    <option value="Last Year">📁 Last Year</option>
                    <option value="User Defined">⚙️ Custom Range</option>
                  </select>
                </div>

                {/* Enhanced Search Field */}
                <div className="md:col-span-2">
                  <div className="flex items-center space-x-2 mb-2">
                    <Search className="h-4 w-4 text-green-600 dark:text-green-400" />
                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Global Search</label>
                    {searchQuery && (
                      <span className="text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 px-1.5 py-0.5 rounded">Active</span>
                    )}
                  </div>
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="🔍 Search employees, departments, positions, status..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-10 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400 focus:border-green-500 dark:focus:border-green-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200 hover:border-gray-400 dark:hover:border-gray-500"
                    />
                    <Search className="h-4 w-4 text-gray-400 dark:text-gray-500 absolute left-3 top-1/2 transform -translate-y-1/2" />
                    {searchQuery && (
                      <button
                        onClick={() => setSearchQuery('')}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 hover:text-red-500 dark:hover:text-red-400 transition-colors duration-200"
                      >
                        <XCircle className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Enhanced Custom Date Range */}
              {datePeriod === "User Defined" && (
                <div className="mb-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center space-x-2 mb-3">
                    <Calendar className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-900">Custom Date Range</span>
                    <div className="flex-1 border-t border-blue-200"></div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
                    <div className="md:col-span-2">
                      <label className="block text-xs font-medium text-gray-700 mb-1">📅 Start Date</label>
                      <input
                        type="date"
                        className="w-full py-2.5 px-3 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white transition-all duration-200" 
                        value={userDefinedDates.start}
                        onChange={(e) => {
                          setUserDefinedDates(prev => ({
                            ...prev,
                            start: e.target.value
                          }));
                        }}
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-xs font-medium text-gray-700 mb-1">📅 End Date</label>
                      <input
                        type="date"
                        className="w-full py-2.5 px-3 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white transition-all duration-200" 
                        value={userDefinedDates.end}
                        onChange={(e) => {
                          setUserDefinedDates(prev => ({
                            ...prev,
                            end: e.target.value
                          }));
                        }}
                      />
                    </div>
                    <div className="flex items-end">
                      <button 
                        className="w-full px-4 py-2.5 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-all duration-200 shadow-sm hover:shadow"
                        disabled={
                          !userDefinedDates.start ||
                          !userDefinedDates.end ||
                          userDefinedDates.start > userDefinedDates.end
                        }
                        onClick={() => {
                          setStartDate(userDefinedDates.start);
                          setEndDate(userDefinedDates.end);
                        }}
                      >
                        ✅ Apply Range
                      </button>
                    </div>
                  </div>
                </div>
              )}
              
              {showFilters && (
                <>
                  {/* Enhanced Filter Grid with better grouping */}
                  <div className="bg-gray-50 rounded-lg p-4 mb-4">
                    <div className="flex items-center space-x-2 mb-4">
                      <Settings className="h-4 w-4 text-gray-600" />
                      <h5 className="text-sm font-medium text-gray-700">Advanced Filters</h5>
                      <div className="flex-1 border-t border-gray-300"></div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {/* People Filters Group */}
                      <div className="space-y-3">
                        <div className="text-xs font-semibold text-gray-600 uppercase tracking-wide flex items-center">
                          <Users className="h-3 w-3 mr-1" />
                          People
                        </div>
                        
                        {/* Employee Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Users className="h-3 w-3 text-indigo-600" />
                            <label className="text-xs font-medium text-gray-700">Employee</label>
                            {selectedEmployee && <span className="text-xs bg-indigo-100 text-indigo-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedEmployee || ''}
                            onChange={(e) => setSelectedEmployee(e.target.value ? parseInt(e.target.value) : null)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">👥 All Employees</option>
                            {employees.map((employee) => (
                              <option key={employee.id} value={employee.id}>👤 {employee.name}</option>
                            ))}
                          </select>
                        </div>

                        {/* Department Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Building2 className="h-3 w-3 text-blue-600" />
                            <label className="text-xs font-medium text-gray-700">Department</label>
                            {selectedDepartment !== 'All' && <span className="text-xs bg-blue-100 text-blue-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedDepartment}
                            onChange={(e) => setSelectedDepartment(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            {departments.map((dept, i) => (
                              <option key={i} value={dept}>🏢 {dept}</option>
                            ))}
                          </select>
                        </div>
                      </div>

                      {/* Work Filters Group */}
                      <div className="space-y-3">
                        <div className="text-xs font-semibold text-gray-600 uppercase tracking-wide flex items-center">
                          <Briefcase className="h-3 w-3 mr-1" />
                          Work Details
                        </div>
                        
                        {/* Position Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Briefcase className="h-3 w-3 text-green-600" />
                            <label className="text-xs font-medium text-gray-700">Position</label>
                            {selectedPosition && <span className="text-xs bg-green-100 text-green-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedPosition}
                            onChange={(e) => setSelectedPosition(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">💼 All Positions</option>
                            {uniquePositions.map((position) => (
                              <option key={position} value={position}>🎯 {position}</option>
                            ))}
                          </select>
                        </div>

                        {/* Shift Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Clock7 className="h-3 w-3 text-teal-600" />
                            <label className="text-xs font-medium text-gray-700">Shift</label>
                            {selectedShift && <span className="text-xs bg-teal-100 text-teal-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedShift}
                            onChange={(e) => setSelectedShift(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">🕒 All Shifts</option>
                            {uniqueShifts.map((shift) => (
                              <option key={shift} value={shift}>⏰ {shift}</option>
                            ))}
                          </select>
                        </div>
                      </div>

                      {/* Status & Location Group */}
                      <div className="space-y-3">
                        <div className="text-xs font-semibold text-gray-600 uppercase tracking-wide flex items-center">
                          <Activity className="h-3 w-3 mr-1" />
                          Status & Location
                        </div>
                        
                        {/* Status Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Activity className="h-3 w-3 text-orange-600" />
                            <label className="text-xs font-medium text-gray-700">Status</label>
                            {selectedStatus && <span className="text-xs bg-orange-100 text-orange-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedStatus}
                            onChange={(e) => setSelectedStatus(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">📊 All Status</option>
                            <option value={AttendanceStatus.PRESENT}>✅ Present</option>
                            <option value={AttendanceStatus.ABSENT}>❌ Absent</option>
                            <option value={AttendanceStatus.LATE}>⏰ Late</option>
                            <option value={AttendanceStatus.WORK_FROM_HOME}>🏠 WFH</option>
                            <option value={AttendanceStatus.HALF_DAY}>🕐 Half Day</option>
                            <option value={AttendanceStatus.LEAVE}>🏖️ Leave</option>
                            <option value={AttendanceStatus.SICK_LEAVE}>🤒 Sick</option>
                            <option value={AttendanceStatus.ANNUAL_LEAVE}>🌴 Annual</option>
                          </select>
                        </div>

                        {/* Location Filter */}
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <svg className="h-3 w-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            <label className="text-xs font-medium text-gray-700">Location</label>
                            {selectedLocation && <span className="text-xs bg-purple-100 text-purple-700 px-1 py-0.5 rounded">1</span>}
                          </div>
                          <select
                            value={selectedLocation}
                            onChange={(e) => setSelectedLocation(e.target.value)}
                            className="w-full py-2 px-3 border border-gray-300 rounded-md text-xs focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white transition-all duration-200 hover:border-gray-400"
                          >
                            <option value="">📍 All Locations</option>
                            {uniqueLocations.map((location) => (
                              <option key={location} value={location}>🌍 {location}</option>
                            ))}
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* Enhanced Actions Bar */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-600">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                    <div className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full animate-pulse"></div>
                    <span className="font-semibold text-blue-700 dark:text-blue-400">{getFilteredAttendances().length}</span>
                    <span>of</span>
                    <span className="font-medium">{attendances.length}</span>
                    <span>records shown</span>
                  </div>
                  
                  {/* Data Quality Indicator */}
                  {attendances.length === 0 ? (
                    <div className="flex items-center space-x-1 text-xs text-amber-600 dark:text-amber-400">
                      <AlertTriangle className="h-3 w-3" />
                      <span>No data available</span>
                    </div>
                  ) : getFilteredAttendances().length === 0 ? (
                    <div className="flex items-center space-x-1 text-xs text-orange-600 dark:text-orange-400">
                      <AlertTriangle className="h-3 w-3" />
                      <span>No matches found</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-1 text-xs text-green-600 dark:text-green-400">
                      <CheckCircle2 className="h-3 w-3" />
                      <span>Data loaded</span>
                    </div>
                  )}
                </div>
                
                <div className="flex items-center space-x-2">
                  <button 
                    onClick={resetFilters}
                    className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md text-xs font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 shadow-sm hover:shadow"
                  >
                    <RefreshCw className="h-3 w-3 mr-1" />
                    Reset All
                  </button>
                  <button
                    onClick={() => setShowFilters(false)}
                    className="inline-flex items-center px-4 py-1.5 bg-blue-600 dark:bg-blue-500 hover:bg-blue-700 dark:hover:bg-blue-600 text-white rounded-md text-xs font-medium transition-all duration-200 shadow-sm hover:shadow"
                  >
                    <CheckCircle2 className="h-3 w-3 mr-1" />
                    Apply Filters
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          {/* Export Options */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 transition-colors duration-200">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">Generate Reports</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[
                {
                  title: 'Executive Summary',
                  description: 'High-level attendance overview',
                  icon: BarChart3,
                  color: 'blue',
                  type: 'executive'
                },
                {
                  title: 'Detailed Analytics',
                  description: 'Comprehensive attendance analysis',
                  icon: TrendingUp,
                  color: 'green',
                  type: 'detailed'
                },
                {
                  title: 'Department Report',
                  description: 'Department-wise breakdown',
                  icon: Building2,
                  color: 'purple',
                  type: 'department'
                },
                {
                  title: 'Employee Report',
                  description: 'Individual employee metrics',
                  icon: Users,
                  color: 'amber',
                  type: 'employee'
                },
                {
                  title: 'Compliance Report',
                  description: 'Regulatory compliance data',
                  icon: CheckCircle2,
                  color: 'emerald',
                  type: 'compliance'
                },
                {
                  title: 'Custom Report',
                  description: 'Build your own report',
                  icon: Settings,
                  color: 'gray',
                  type: 'custom'
                }
              ].map((report, index) => (
                <div key={index} className="border border-gray-200 dark:border-gray-600 rounded-lg p-6 hover:shadow-md dark:hover:shadow-gray-900/20 transition-shadow bg-white dark:bg-gray-700">
                  <div className="flex items-start justify-between mb-4">
                    <div className={`p-3 bg-${report.color}-50 dark:bg-${report.color}-900/30 rounded-lg`}>
                      <report.icon className={`h-6 w-6 text-${report.color}-600 dark:text-${report.color}-400`} />
                    </div>
                    <button 
                      onClick={() => handleGenerateReport(report.type)}
                      className={`px-3 py-1.5 bg-${report.color}-600 dark:bg-${report.color}-500 text-white rounded-lg text-xs font-medium hover:bg-${report.color}-700 dark:hover:bg-${report.color}-600 transition-colors`}
                    >
                      Generate
                    </button>
                  </div>
                  <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-2">{report.title}</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">{report.description}</p>
                  <div className="flex items-center space-x-3">
                    <button 
                      onClick={handleExportPDF}
                      className="flex items-center text-xs text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
                    >
                      <FileText className="h-3 w-3 mr-1" />
                      PDF
                    </button>
                    <button 
                      onClick={handleExportExcel}
                      className="flex items-center text-xs text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
                    >
                      <Download className="h-3 w-3 mr-1" />
                      Excel
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
            
          {/* Generated Reports History */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 p-6 transition-colors duration-200">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Report History</h3>
              <div className="flex items-center space-x-2">
                <div className="relative">
                  <Search className="h-4 w-4 text-gray-400 dark:text-gray-500 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  <input
                    type="text"
                    placeholder="Search reports..."
                    className="pl-10 pr-4 py-2 border border-gray-200 dark:border-gray-600 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                  />
                </div>
              </div>
            </div>
            
            <div className="overflow-hidden border border-gray-100 dark:border-gray-600 rounded-lg">
              <table className="min-w-full divide-y divide-gray-100 dark:divide-gray-600">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Report</th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Period</th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Generated</th>
                    <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Format</th>
                    <th className="px-6 py-4 text-right text-xs font-semibold text-gray-600 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-50 dark:divide-gray-600">
                  {[
                    {
                      name: 'Monthly Summary',
                      period: 'Nov 2024',
                      generated: '2 hours ago',
                      format: 'PDF'
                    },
                    {
                      name: 'Department Analytics',
                      period: 'Nov 2024',
                      generated: '1 day ago',
                      format: 'Excel'
                    },
                    {
                      name: 'Employee Performance',
                      period: 'Oct 2024',
                      generated: '1 week ago',
                      format: 'PDF'
                    }
                  ].map((report, index) => (
                    <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-lg mr-3">
                            <FileText className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div>
                            <div className="text-sm font-semibold text-gray-900 dark:text-white">{report.name}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">{report.period}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400">{report.generated}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400">
                          {report.format}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm space-x-2">
                        <button className="inline-flex items-center px-3 py-1.5 border border-gray-200 dark:border-gray-600 rounded-lg text-xs font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                          <Eye className="h-3 w-3 mr-1" />
                          View
                        </button>
                        <button className="inline-flex items-center px-3 py-1.5 bg-blue-600 dark:bg-blue-500 border border-blue-600 dark:border-blue-500 rounded-lg text-xs font-medium text-white hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors">
                          <Download className="h-3 w-3 mr-1" />
                          Download
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AttendanceReport;