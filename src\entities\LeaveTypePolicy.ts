import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { LeavePolicyConfiguration } from './LeavePolicyConfiguration';

@Entity('leave_type_policies')
export class LeaveTypePolicy {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 100 })
  leaveType: string;

  @Column({ type: 'boolean', default: true })
  enabled: boolean;

  @Column({ type: 'varchar', length: 100 })
  displayName: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'json' })
  settings: any; // Store LeavePolicySettings as JSON

  @Column({ type: 'json', nullable: true })
  applicableRoles: string[];

  @Column({ type: 'json', nullable: true })
  applicableDepartments: string[];

  @Column({ type: 'int' })
  maxDaysPerYear: number;

  @Column({ type: 'int', default: 0 })
  minServicePeriod: number; // months

  @Column({ type: 'boolean', default: false })
  allowCarryForward: boolean;

  @Column({ type: 'int', default: 0 })
  carryForwardLimit: number;

  @Column({ type: 'boolean', default: false })
  encashmentAllowed: boolean;

  @Column({ type: 'boolean', default: false })
  documentRequired: boolean;

  @Column({ type: 'varchar', length: 7, nullable: true })
  color: string;

  @Column({ type: 'int', nullable: true })
  sortOrder: number;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'varchar', length: 50, nullable: true })
  category: string; // 'annual' | 'sick' | 'casual' | 'unpaid' | 'maternity' | 'paternity' | 'half_day'

  @Column({ type: 'varchar', length: 20, nullable: true, default: 'all' })
  genderEligibility: string; // 'all' | 'male' | 'female'

  @Column({ type: 'boolean', default: true })
  allowHalfDay: boolean;

  @Column({ type: 'int', default: 0 })
  minDaysNotice: number;

  @Column({ type: 'date', nullable: true })
  effectiveFrom: Date;

  @Column({ type: 'date', nullable: true })
  validUntil: Date;

  @Column({ type: 'int' })
  policyConfigurationId: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => LeavePolicyConfiguration, config => config.leaveTypes)
  @JoinColumn({ name: 'policyConfigurationId' })
  policyConfiguration: LeavePolicyConfiguration;
} 