// Transform the data to include employee codes
const transformedAttendances = attendances.map(attendance => ({
  ...attendance,
  employeeCode: attendance.employee?.employeeId || 'N/A', // Add the string employee code
  employeeName: attendance.employeeName || `${attendance.employee?.firstName || ''} ${attendance.employee?.lastName || ''}`.trim(),
  // Add department information from the employee's job relation
  department: attendance.department || attendance.employee?.job?.department || 'N/A',
  employeeDepartment: attendance.department || attendance.employee?.job?.department || 'N/A',
  position: attendance.position || attendance.employee?.job?.designation || 'N/A',
  employeePhoto: attendance.employee?.profileImagePath || null
})); 