import React, { useState } from 'react';
import {
  FileText,
  Clock,
  CheckCircle,
  AlertTriangle,
  Plus,
  Search,
  Filter,
  MoreVertical,
  DollarSign,
  Shield,
  BarChart2,
  X,
  Calendar,
  Settings,
  Users,
  Activity,
  Link,
  FileCheck,
  Bell,
  Tag,
  Zap,
  HelpCircle,
  TrendingUp
} from 'lucide-react';
import { ServiceForm } from './ServiceForm';

type Currency = 'PKR' | 'USD' | 'EUR';

interface PricingTier {
  name: string;
  price: number;
  billingCycle: 'monthly' | 'quarterly' | 'annually';
  features: string[];
}

interface PricingType {
  type: 'fixed' | 'hourly' | 'per_unit' | 'tiered';
  amount: number;
  currency: Currency;
  tiers?: PricingTier[];
  minimumCommitment?: number;
  discounts?: {
    type: 'volume' | 'early-payment' | 'long-term';
    percentage: number;
    conditions: string;
  }[];
}

interface SLAMetrics {
  responseTime: number;
  uptime: number;
  penalties: string;
  resolutionTime: number;
  availabilityHours: string;
  escalationMatrix: {
    level: number;
    contact: string;
    responseTime: number;
  }[];
  maintenanceWindows: {
    day: string;
    time: string;
    duration: number;
  }[];
}

interface ServiceMetrics {
  availability: number;
  performance: number;
  satisfaction: number;
  reliability: number;
  incidentCount: number;
  avgResolutionTime: number;
  costEfficiency: number;
  userAdoption: number;
  complianceScore: number;
}

interface Compliance {
  certifications: string[];
  standards: string[];
  lastAuditDate: string;
  nextAuditDue: string;
  findings: {
    type: 'critical' | 'major' | 'minor';
    description: string;
    status: 'open' | 'closed' | 'in-progress';
    dueDate: string;
  }[];
}

interface Integration {
  system: string;
  status: 'active' | 'pending' | 'failed';
  lastSync: string;
  apiVersion: string;
  endpoints: string[];
}

interface VendorService {
  id: string;
  name: string;
  description: string;
  hasSLA: boolean;
  category: string;
  subcategory?: string;
  tags: string[];
  status: 'active' | 'pending' | 'suspended' | 'terminated';
  criticality: 'high' | 'medium' | 'low';
  sla?: SLAMetrics;
  pricing: PricingType;
  metrics: ServiceMetrics;
  compliance: Compliance;
  integrations: Integration[];
  documentation: {
    type: string;
    url: string;
    lastUpdated: string;
  }[];
  supportChannels: {
    type: 'email' | 'phone' | 'portal' | 'chat';
    contact: string;
    availability: string;
  }[];
  dependencies: string[];
  backupProvider?: string;
  lastUpdated: string;
  nextReview: string;
  changeHistory: {
    date: string;
    type: 'configuration' | 'pricing' | 'sla' | 'status';
    description: string;
    user: string;
  }[];
  notifications: {
    id: string;
    type: 'warning' | 'info' | 'critical';
    message: string;
    date: string;
    isRead: boolean;
  }[];
}

interface VendorServicesProps {
  services: VendorService[];
  onUpdate: (services: VendorService[]) => void;
}

export function VendorServices({ services, onUpdate }: VendorServicesProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedCriticality, setSelectedCriticality] = useState('all');
  const [showMetricsModal, setShowMetricsModal] = useState(false);
  const [selectedService, setSelectedService] = useState<VendorService | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'metrics' | 'compliance' | 'integrations' | 'history'>('overview');
  const [dateRange, setDateRange] = useState<'7d' | '30d' | '90d' | 'custom'>('30d');

  const getStatusColor = (status: VendorService['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'suspended':
        return 'bg-orange-100 text-orange-800';
      case 'terminated':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCriticalityColor = (criticality: VendorService['criticality']) => {
    switch (criticality) {
      case 'high':
        return 'bg-red-50 text-red-700 border-red-200';
      case 'medium':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200';
      case 'low':
        return 'bg-green-50 text-green-700 border-green-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  const handleDeleteService = (serviceId: string) => {
    const updatedServices = services.filter(service => service.id !== serviceId);
    onUpdate(updatedServices);
  };

  const handleAddService = (serviceData: any) => {
    const newService = {
      id: `S${(services.length + 1).toString().padStart(3, '0')}`,
      status: 'active',
      lastUpdated: new Date().toISOString(),
      nextReview: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(),
      metrics: {
        availability: 100,
        performance: 100,
        satisfaction: 100,
        reliability: 100,
        incidentCount: 0,
        avgResolutionTime: 0,
        costEfficiency: 100,
        userAdoption: 0,
        complianceScore: 100
      },
      compliance: {
        certifications: [],
        standards: [],
        lastAuditDate: new Date().toISOString(),
        nextAuditDue: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000).toISOString(),
        findings: []
      },
      integrations: [],
      documentation: [],
      supportChannels: [],
      dependencies: [],
      changeHistory: [],
      notifications: [],
      ...serviceData
    };

    onUpdate([...services, newService]);
    setShowAddForm(false);
  };

  const filteredServices = services.filter(service => {
    const matchesSearch = 
      service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      service.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      service.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = selectedCategory === 'all' || service.category === selectedCategory;
    const matchesStatus = selectedStatus === 'all' || service.status === selectedStatus;
    const matchesCriticality = selectedCriticality === 'all' || service.criticality === selectedCriticality;
    return matchesSearch && matchesCategory && matchesStatus && matchesCriticality;
  });

  const renderServiceMetrics = (service: VendorService) => (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      <div className="bg-white p-4 rounded-lg shadow-sm">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-500">Availability</span>
          <Activity className="h-5 w-5 text-blue-500" />
        </div>
        <div className="flex items-end justify-between">
          <span className="text-2xl font-bold text-gray-900">{service.metrics.availability}%</span>
          <span className="text-sm text-gray-500">Last 30d</span>
        </div>
        <div className="mt-2 h-2 bg-gray-100 rounded-full overflow-hidden">
          <div 
            className="h-full bg-blue-500 rounded-full"
            style={{ width: `${service.metrics.availability}%` }}
          />
        </div>
      </div>

      <div className="bg-white p-4 rounded-lg shadow-sm">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-500">Performance</span>
          <Zap className="h-5 w-5 text-yellow-500" />
        </div>
        <div className="flex items-end justify-between">
          <span className="text-2xl font-bold text-gray-900">{service.metrics.performance}%</span>
          <span className="text-sm text-gray-500">Target: 95%</span>
        </div>
        <div className="mt-2 h-2 bg-gray-100 rounded-full overflow-hidden">
          <div 
            className="h-full bg-yellow-500 rounded-full"
            style={{ width: `${service.metrics.performance}%` }}
          />
        </div>
      </div>

      <div className="bg-white p-4 rounded-lg shadow-sm">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-500">User Adoption</span>
          <Users className="h-5 w-5 text-purple-500" />
        </div>
        <div className="flex items-end justify-between">
          <span className="text-2xl font-bold text-gray-900">{service.metrics.userAdoption}%</span>
          <span className="text-sm text-gray-500">↑ 12%</span>
        </div>
        <div className="mt-2 h-2 bg-gray-100 rounded-full overflow-hidden">
          <div 
            className="h-full bg-purple-500 rounded-full"
            style={{ width: `${service.metrics.userAdoption}%` }}
          />
        </div>
      </div>

      <div className="bg-white p-4 rounded-lg shadow-sm">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-500">Compliance</span>
          <Shield className="h-5 w-5 text-green-500" />
        </div>
        <div className="flex items-end justify-between">
          <span className="text-2xl font-bold text-gray-900">{service.metrics.complianceScore}%</span>
          <span className="text-sm text-gray-500">Required: 98%</span>
        </div>
        <div className="mt-2 h-2 bg-gray-100 rounded-full overflow-hidden">
          <div 
            className="h-full bg-green-500 rounded-full"
            style={{ width: `${service.metrics.complianceScore}%` }}
          />
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header with advanced filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-4">
          <div className="flex flex-1 gap-4">
            <div className="relative flex-1">
              <input
                type="text"
                placeholder="Search services, tags, or descriptions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            </div>
          </div>
          <button
            onClick={() => setShowAddForm(true)}
            className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            <Plus className="h-5 w-5 mr-2" />
            Add Service
          </button>
        </div>

        <div className="flex flex-wrap gap-4">
          <div className="relative">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="appearance-none pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Categories</option>
              <option value="infrastructure">Infrastructure</option>
              <option value="support">Support</option>
              <option value="security">Security</option>
              <option value="consulting">Consulting</option>
            </select>
            <Filter className="absolute right-3 top-2.5 h-5 w-5 text-gray-400 pointer-events-none" />
          </div>

          <div className="relative">
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="appearance-none pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="pending">Pending</option>
              <option value="suspended">Suspended</option>
              <option value="terminated">Terminated</option>
            </select>
            <Filter className="absolute right-3 top-2.5 h-5 w-5 text-gray-400 pointer-events-none" />
          </div>

          <div className="relative">
            <select
              value={selectedCriticality}
              onChange={(e) => setSelectedCriticality(e.target.value)}
              className="appearance-none pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Criticality</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
            <Filter className="absolute right-3 top-2.5 h-5 w-5 text-gray-400 pointer-events-none" />
          </div>

          <div className="relative">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value as any)}
              className="appearance-none pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="custom">Custom Range</option>
            </select>
            <Calendar className="absolute right-3 top-2.5 h-5 w-5 text-gray-400 pointer-events-none" />
          </div>
        </div>
      </div>

      {/* Services Grid */}
      <div className="grid gap-6">
        {filteredServices.map((service) => (
          <div
            key={service.id}
            className="bg-white rounded-lg shadow-sm overflow-hidden"
          >
            <div className="p-6">
              <div className="flex flex-col lg:flex-row lg:items-start justify-between gap-4">
                <div className="flex-1">
                  <div className="flex items-start gap-4">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <FileText className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h3 className="text-lg font-semibold text-gray-900">{service.name}</h3>
                        <span className={`px-2.5 py-0.5 text-xs font-medium rounded-full ${getStatusColor(service.status)}`}>
                          {service.status}
                        </span>
                        <span className={`px-2.5 py-0.5 text-xs font-medium rounded-full border ${getCriticalityColor(service.criticality)}`}>
                          {service.criticality} criticality
                        </span>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">{service.description}</p>
                      <div className="flex flex-wrap items-center gap-2 mt-2">
                        {service.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                          >
                            <Tag className="h-3 w-3 mr-1" />
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Service Metrics Overview */}
                  {renderServiceMetrics(service)}

                  {/* Additional Information */}
                  <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* SLA Information */}
                    {service.hasSLA && service.sla && (
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">SLA Details</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500">Response Time</span>
                            <span className="text-sm font-medium">{service.sla.responseTime}h</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500">Uptime</span>
                            <span className="text-sm font-medium">{service.sla.uptime}%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500">Resolution Time</span>
                            <span className="text-sm font-medium">{service.sla.resolutionTime}h</span>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Compliance Status */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Compliance</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Last Audit</span>
                          <span className="text-sm font-medium">{service.compliance.lastAuditDate}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Next Due</span>
                          <span className="text-sm font-medium">{service.compliance.nextAuditDue}</span>
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {service.compliance.certifications.map((cert, index) => (
                            <span
                              key={index}
                              className="px-2 py-0.5 text-xs bg-green-100 text-green-800 rounded-full"
                            >
                              {cert}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Integration Status */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Integrations</h4>
                      <div className="space-y-2">
                        {service.integrations.map((integration, index) => (
                          <div key={index} className="flex items-center justify-between">
                            <span className="text-sm text-gray-500">{integration.system}</span>
                            <span className={`text-xs px-2 py-0.5 rounded-full ${
                              integration.status === 'active' ? 'bg-green-100 text-green-800' :
                              integration.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {integration.status}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Recent Notifications */}
                  {service.notifications.length > 0 && (
                    <div className="mt-6">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Recent Notifications</h4>
                      <div className="space-y-2">
                        {service.notifications.slice(0, 3).map((notification) => (
                          <div
                            key={notification.id}
                            className={`flex items-start gap-2 p-2 rounded-lg ${
                              notification.type === 'critical' ? 'bg-red-50' :
                              notification.type === 'warning' ? 'bg-yellow-50' :
                              'bg-blue-50'
                            }`}
                          >
                            <Bell className={`h-5 w-5 ${
                              notification.type === 'critical' ? 'text-red-500' :
                              notification.type === 'warning' ? 'text-yellow-500' :
                              'text-blue-500'
                            }`} />
                            <div className="flex-1">
                              <p className="text-sm text-gray-900">{notification.message}</p>
                              <span className="text-xs text-gray-500">{notification.date}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col gap-2">
                  <button
                    onClick={() => {
                      setSelectedService(service);
                      setShowMetricsModal(true);
                    }}
                    className="p-2 text-gray-400 hover:text-blue-600 rounded-lg hover:bg-gray-100"
                    title="View Detailed Metrics"
                  >
                    <BarChart2 className="h-5 w-5" />
                  </button>
                  <button
                    onClick={() => {/* Handle settings */}}
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                    title="Service Settings"
                  >
                    <Settings className="h-5 w-5" />
                  </button>
                  <button
                    onClick={() => handleDeleteService(service.id)}
                    className="p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-gray-100"
                    title="Delete Service"
                  >
                    <X className="h-5 w-5" />
                  </button>
                  <button
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                    title="More Options"
                  >
                    <MoreVertical className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Add Service Form Modal */}
      {showAddForm && (
        <ServiceForm
          onClose={() => setShowAddForm(false)}
          onSubmit={handleAddService}
        />
      )}

      {/* Help Button */}
      <button
        className="fixed bottom-6 right-6 p-3 bg-white rounded-full shadow-lg hover:shadow-xl transition-shadow"
        title="Help & Documentation"
      >
        <HelpCircle className="h-6 w-6 text-blue-500" />
      </button>
    </div>
  );
} 