import React, { useState, useEffect } from 'react';
import {
  Users,
  Filter,
  Calendar,
  Search,
  Clock,
  Check,
  AlertCircle,
  FileCheck,
  DollarSign,
  Download,
  Edit,
  Play,
  RefreshCw,
  Loader,
  User,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import {
  PayrollPeriod,
  PayrollEmployee,
  PayrollStatus,
  PayrollCurrency,
  PayrollFrequency
} from '../../../types/payroll';

interface BulkPayrollProcessorProps {
  isAdmin?: boolean;
  selectedCurrency?: PayrollCurrency;
}

const BulkPayrollProcessor: React.FC<BulkPayrollProcessorProps> = ({ isAdmin = false, selectedCurrency = PayrollCurrency.USD }) => {
  const [selectedPeriod, setSelectedPeriod] = useState<PayrollPeriod | null>(null);
  const [periods, setPeriods] = useState<PayrollPeriod[]>([]);
  const [employees, setEmployees] = useState<PayrollEmployee[]>([]);
  const [selectedEmployees, setSelectedEmployees] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [loading, setLoading] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [processingComplete, setProcessingComplete] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [processingStatus, setProcessingStatus] = useState('');
  const [expandedDepartments, setExpandedDepartments] = useState<string[]>([]);
  const [filterDepartment, setFilterDepartment] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Currency symbols mapping
  const currencySymbols: Record<PayrollCurrency, string> = {
    USD: '$',
    EUR: '€',
    GBP: '£',
    PKR: '₨',
    JPY: '¥',
    CAD: 'C$',
    AUD: 'A$',
    INR: '₹'
  };

  // Format currency amount
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: selectedCurrency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount);
  };

  // Exchange rate conversion
  const getExchangeRate = (fromCurrency: PayrollCurrency, toCurrency: PayrollCurrency): number => {
    // This would typically come from an API or database
    // Mocked exchange rates for demonstration purposes
    const rates: Record<PayrollCurrency, Record<PayrollCurrency, number>> = {
      USD: { USD: 1, EUR: 0.85, GBP: 0.75, PKR: 278.5, JPY: 110.5, CAD: 1.25, AUD: 1.35, INR: 83.2 },
      EUR: { USD: 1.18, EUR: 1, GBP: 0.88, PKR: 328.2, JPY: 130.4, CAD: 1.47, AUD: 1.59, INR: 98.2 },
      GBP: { USD: 1.33, EUR: 1.13, GBP: 1, PKR: 371.7, JPY: 147.3, CAD: 1.67, AUD: 1.80, INR: 110.9 },
      PKR: { USD: 0.0036, EUR: 0.003, GBP: 0.0027, PKR: 1, JPY: 0.4, CAD: 0.0045, AUD: 0.0048, INR: 0.3 },
      JPY: { USD: 0.009, EUR: 0.0077, GBP: 0.0068, PKR: 2.52, JPY: 1, CAD: 0.011, AUD: 0.012, INR: 0.75 },
      CAD: { USD: 0.8, EUR: 0.68, GBP: 0.6, PKR: 222.8, JPY: 88.4, CAD: 1, AUD: 1.08, INR: 66.6 },
      AUD: { USD: 0.74, EUR: 0.63, GBP: 0.55, PKR: 206.3, JPY: 81.9, CAD: 0.93, AUD: 1, INR: 61.6 },
      INR: { USD: 0.012, EUR: 0.01, GBP: 0.009, PKR: 3.35, JPY: 1.33, CAD: 0.015, AUD: 0.016, INR: 1 }
    };
    
    return rates[fromCurrency][toCurrency];
  };

  // Convert amount between currencies
  const convertCurrency = (amount: number, fromCurrency: PayrollCurrency, toCurrency: PayrollCurrency): number => {
    const rate = getExchangeRate(fromCurrency, toCurrency);
    return amount * rate;
  };

  // Load sample data
  useEffect(() => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      // Sample employees
      const sampleEmployees: PayrollEmployee[] = [
        {
          id: 1,
          employeeId: 'EMP001',
          firstName: 'John',
          lastName: 'Smith',
          department: 'IT',
          designation: 'Senior Developer',
          joinDate: '2022-01-15',
          baseSalary: 85000,
          currency: PayrollCurrency.USD,
          status: 'active',
          paymentMethod: 'bank_transfer',
          bankName: 'City Bank',
          accountNumber: '**********',
          taxIdentificationNumber: 'TX123456'
        },
        {
          id: 2,
          employeeId: 'EMP002',
          firstName: 'Sarah',
          lastName: 'Johnson',
          department: 'HR',
          designation: 'HR Manager',
          joinDate: '2022-03-10',
          baseSalary: 75000,
          currency: PayrollCurrency.USD,
          status: 'active',
          paymentMethod: 'bank_transfer',
          bankName: 'Global Bank',
          accountNumber: '**********',
          taxIdentificationNumber: 'TX789012'
        },
        {
          id: 3,
          employeeId: 'EMP003',
          firstName: 'Michael',
          lastName: 'Wong',
          department: 'Finance',
          designation: 'Financial Analyst',
          joinDate: '2022-02-01',
          baseSalary: 70000,
          currency: PayrollCurrency.USD,
          status: 'active',
          paymentMethod: 'bank_transfer',
          bankName: 'City Bank',
          accountNumber: '**********',
          taxIdentificationNumber: 'TX345678'
        },
        {
          id: 4,
          employeeId: 'EMP004',
          firstName: 'Emma',
          lastName: 'Garcia',
          department: 'Marketing',
          designation: 'Marketing Director',
          joinDate: '2021-11-15',
          baseSalary: 95000,
          currency: PayrollCurrency.USD,
          status: 'active',
          paymentMethod: 'bank_transfer',
          bankName: 'Universal Bank',
          accountNumber: '**********',
          taxIdentificationNumber: 'TX901234'
        },
        {
          id: 5,
          employeeId: 'EMP005',
          firstName: 'David',
          lastName: 'Lee',
          department: 'IT',
          designation: 'DevOps Engineer',
          joinDate: '2022-04-20',
          baseSalary: 80000,
          currency: PayrollCurrency.USD,
          status: 'active',
          paymentMethod: 'bank_transfer',
          bankName: 'Global Bank',
          accountNumber: '**********',
          taxIdentificationNumber: 'TX567890'
        },
        // Add a Pakistani employee with PKR salary
        {
          id: 6,
          employeeId: 'EMP006',
          firstName: 'Jessica',
          lastName: 'Chen',
          department: 'HR',
          designation: 'HR Specialist',
          joinDate: '2023-02-10',
          baseSalary: 120000,
          currency: PayrollCurrency.PKR,
          status: 'active',
          paymentMethod: 'bank_transfer',
          bankName: 'City Bank',
          accountNumber: '**********',
          taxIdentificationNumber: 'TX112233'
        },
        {
          id: 7,
          employeeId: 'EMP007',
          firstName: 'Robert',
          lastName: 'Taylor',
          department: 'Finance',
          designation: 'Accountant',
          joinDate: '2023-01-05',
          baseSalary: 68000,
          currency: PayrollCurrency.USD,
          status: 'active',
          paymentMethod: 'bank_transfer',
          bankName: 'Global Bank',
          accountNumber: '**********',
          taxIdentificationNumber: 'TX445566'
        }
      ];
      
      // Sample periods
      const samplePeriods: PayrollPeriod[] = [
        {
          id: 1,
          name: 'May 2025',
          startDate: '2025-05-01',
          endDate: '2025-05-31',
          paymentDate: '2025-06-05',
          frequency: PayrollFrequency.MONTHLY,
          status: PayrollStatus.DRAFT,
          totalEmployees: 32,
          totalAmount: 0,
          currency: selectedCurrency
        },
        {
          id: 2,
          name: 'April 2025',
          startDate: '2025-04-01',
          endDate: '2025-04-30',
          paymentDate: '2025-05-05',
          frequency: PayrollFrequency.MONTHLY,
          status: PayrollStatus.COMPLETED,
          totalEmployees: 32,
          totalAmount: 1245300,
          currency: PayrollCurrency.USD
        }
      ];
      
      setEmployees(sampleEmployees);
      setPeriods(samplePeriods);
      setSelectedPeriod(samplePeriods[0]);
      setExpandedDepartments(['IT', 'HR', 'Finance', 'Marketing']);
      setLoading(false);
    }, 1000);
    }, [selectedCurrency]);

  // Toggle select all
  const toggleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);
    
    if (newSelectAll) {
      // Select all employees that match the current filter and search
      const filteredIds = filteredEmployees.map(emp => emp.id);
      setSelectedEmployees(filteredIds);
    } else {
      // Deselect all
      setSelectedEmployees([]);
    }
  };

  // Toggle single employee selection
  const toggleEmployeeSelection = (employeeId: number) => {
    if (selectedEmployees.includes(employeeId)) {
      setSelectedEmployees(selectedEmployees.filter(id => id !== employeeId));
      setSelectAll(false);
    } else {
      setSelectedEmployees([...selectedEmployees, employeeId]);
      
      // Check if all filtered employees are now selected
      const allFilteredIds = filteredEmployees.map(emp => emp.id);
      const isAllSelected = allFilteredIds.every(id => 
        selectedEmployees.includes(id) || id === employeeId
      );
      
      setSelectAll(isAllSelected);
    }
  };

  // Toggle department expansion
  const toggleDepartment = (department: string) => {
    if (expandedDepartments.includes(department)) {
      setExpandedDepartments(expandedDepartments.filter(d => d !== department));
    } else {
      setExpandedDepartments([...expandedDepartments, department]);
    }
  };

  // Filter employees based on department and search term
  const filteredEmployees = employees.filter(emp => {
    const departmentMatches = filterDepartment === 'all' || emp.department === filterDepartment;
    const searchMatches = 
      emp.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      emp.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      emp.employeeId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      emp.designation.toLowerCase().includes(searchTerm.toLowerCase());
      
    return departmentMatches && searchMatches;
  });

  // Group employees by department
  const employeesByDepartment = filteredEmployees.reduce((groups, employee) => {
    const dept = employee.department;
    if (!groups[dept]) {
      groups[dept] = [];
    }
    groups[dept].push(employee);
    return groups;
  }, {} as Record<string, PayrollEmployee[]>);

  // Process payroll for selected employees
  const processPayroll = () => {
    if (selectedEmployees.length === 0 || !selectedPeriod) return;
    
    setProcessing(true);
    setProcessingProgress(0);
    setProcessingStatus('Initializing...');
    
    // Simulated processing stages
    setTimeout(() => {
      setProcessingProgress(10);
      setProcessingStatus('Fetching employee data...');
      
      setTimeout(() => {
        setProcessingProgress(25);
        setProcessingStatus('Calculating earnings...');
        
        setTimeout(() => {
          setProcessingProgress(50);
          setProcessingStatus('Applying deductions...');
          
          setTimeout(() => {
            setProcessingProgress(75);
            setProcessingStatus('Calculating taxes...');
            
            setTimeout(() => {
              setProcessingProgress(90);
              setProcessingStatus('Finalizing calculations...');
              
              setTimeout(() => {
                setProcessingProgress(100);
                setProcessingStatus('Processing complete!');
                setProcessingComplete(true);
                
                // Update the period status to indicate processing
                if (selectedPeriod) {
                  const updatedPeriod = { ...selectedPeriod, status: 'processing' as PayrollStatus };
                  setPeriods(periods.map(p => 
                    p.id === updatedPeriod.id ? updatedPeriod : p
                  ));
                  setSelectedPeriod(updatedPeriod);
                }
              }, 500);
            }, 800);
          }, 1000);
        }, 800);
      }, 600);
    }, 500);
  };

  // Department filter options
  const departments = Array.from(new Set(employees.map(emp => emp.department)));

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left column: Pay Period */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="text-base font-medium text-gray-900 mb-4">Select Pay Period</h3>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Pay Period
              </label>
              <select 
                className="block w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 p-2"
                value={selectedPeriod?.id || ''}
                onChange={(e) => {
                  const periodId = parseInt(e.target.value);
                  const period = periods.find(p => p.id === periodId) || null;
                  setSelectedPeriod(period);
                }}
                disabled={processing}
              >
                <option value="">Select period</option>
                {periods.map((period) => (
                  <option 
                    key={period.id} 
                    value={period.id}
                    disabled={period.status !== 'draft' && period.status !== 'on_hold'}
                  >
                    {period.name} ({new Date(period.startDate).toLocaleDateString()} - {new Date(period.endDate).toLocaleDateString()})
                  </option>
                ))}
              </select>
            </div>
            
            {selectedPeriod && (
              <div className="bg-blue-50 p-3 rounded-md">
                <div className="flex items-center mb-2">
                  <Calendar className="h-4 w-4 text-blue-500 mr-2" />
                  <h4 className="text-sm font-medium text-gray-700">Period Details</h4>
                </div>
                <div className="text-sm">
                  <p><span className="text-gray-500">Start Date:</span> {new Date(selectedPeriod.startDate).toLocaleDateString()}</p>
                  <p><span className="text-gray-500">End Date:</span> {new Date(selectedPeriod.endDate).toLocaleDateString()}</p>
                  <p><span className="text-gray-500">Payment Date:</span> {new Date(selectedPeriod.paymentDate).toLocaleDateString()}</p>
                  <p><span className="text-gray-500">Currency:</span> {selectedPeriod.currency || selectedCurrency} ({currencySymbols[selectedPeriod.currency as PayrollCurrency || selectedCurrency]})</p>
                  <p className="mt-2">
                    <span className="text-gray-500">Status: </span>
                    <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${
                      selectedPeriod.status === 'draft' ? 'bg-gray-100 text-gray-800' :
                      selectedPeriod.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      selectedPeriod.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                      selectedPeriod.status === 'approved' ? 'bg-green-100 text-green-800' :
                      selectedPeriod.status === 'completed' ? 'bg-green-100 text-green-800' :
                      selectedPeriod.status === 'rejected' ? 'bg-red-100 text-red-800' :
                      'bg-purple-100 text-purple-800'
                    }`}>
                      {selectedPeriod.status.charAt(0).toUpperCase() + selectedPeriod.status.slice(1).replace('_', ' ')}
                    </span>
                  </p>
                </div>
              </div>
            )}
            
            {processing && (
              <div className="mt-4 bg-white border border-gray-200 rounded-md p-4">
                <div className="flex items-center mb-2">
                  <Loader className="h-4 w-4 text-blue-500 mr-2 animate-spin" />
                  <h4 className="text-sm font-medium text-gray-700">Processing Status</h4>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2.5 mb-2">
                  <div 
                    className="bg-blue-600 h-2.5 rounded-full" 
                    style={{ width: `${processingProgress}%` }}
                  ></div>
                </div>
                <p className="text-sm text-gray-500">{processingStatus}</p>
                <p className="text-sm mt-1">
                  <span className="text-gray-500">Progress:</span> {processingProgress}%
                </p>
                
                {processingComplete && (
                  <div className="mt-3 text-sm text-green-600 flex items-center">
                    <Check className="h-4 w-4 mr-1" />
                    Processing complete! Payroll has been submitted for approval.
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* Middle column: Employee Selection */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-base font-medium text-gray-900">Select Employees</h3>
              <span className="text-sm text-blue-600">
                {selectedEmployees.length} selected
              </span>
            </div>
            
            <div className="flex space-x-2 mb-4">
              <div className="relative flex-grow">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search employees..."
                  className="pl-10 block w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 p-2 text-sm"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  disabled={processing}
                />
              </div>
              
              <select
                className="border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 p-2 text-sm"
                value={filterDepartment}
                onChange={(e) => setFilterDepartment(e.target.value)}
                disabled={processing}
              >
                <option value="all">All Departments</option>
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
            </div>
            
            <div className="border border-gray-200 rounded-md overflow-hidden">
              <div className="bg-gray-50 p-2 border-b border-gray-200 flex items-center">
                <input
                  type="checkbox"
                  checked={selectAll}
                  onChange={toggleSelectAll}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  disabled={processing}
                />
                <span className="ml-2 text-sm font-medium text-gray-700">
                  Select All
                </span>
              </div>
              
              <div className="max-h-96 overflow-y-auto">
                {Object.entries(employeesByDepartment).map(([department, deptEmployees]) => (
                  <div key={department}>
                    <div 
                      className="flex items-center justify-between p-2 bg-gray-100 cursor-pointer"
                      onClick={() => toggleDepartment(department)}
                    >
                      <span className="font-medium text-gray-700">{department}</span>
                      {expandedDepartments.includes(department) ? (
                        <ChevronUp className="h-4 w-4 text-gray-500" />
                      ) : (
                        <ChevronDown className="h-4 w-4 text-gray-500" />
                      )}
                    </div>
                    
                    {expandedDepartments.includes(department) && (
                      <div>
                        {deptEmployees.map(employee => (
                          <div 
                            key={employee.id}
                            className="flex items-center p-2 hover:bg-gray-50 border-t border-gray-200"
                          >
                            <input
                              type="checkbox"
                              checked={selectedEmployees.includes(employee.id)}
                              onChange={() => toggleEmployeeSelection(employee.id)}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                              disabled={processing}
                            />
                            <div className="ml-2">
                              <div className="text-sm font-medium text-gray-900">
                                {employee.firstName} {employee.lastName}
                              </div>
                              <div className="text-xs text-gray-500">
                                {employee.employeeId} • {employee.designation}
                              </div>
                              <div className="text-xs text-gray-500">
                                {formatCurrency(employee.baseSalary)} • {employee.currency}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
                
                {filteredEmployees.length === 0 && (
                  <div className="p-4 text-center text-gray-500">
                    No employees found matching your criteria
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Right column: Processing Options */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="text-base font-medium text-gray-900 mb-4">Processing Options</h3>
            
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-800 mb-2">Currency Settings</h4>
              <div className="flex items-center p-3 bg-gray-50 rounded-md mb-4">
                <DollarSign className="h-4 w-4 text-gray-600 mr-2" />
                <div className="text-sm">
                  <p><span className="font-medium">Processing Currency:</span> {selectedCurrency} ({currencySymbols[selectedCurrency]})</p>
                  <p className="text-xs text-gray-500 mt-1">Salaries in other currencies will be converted at current exchange rates</p>
                </div>
              </div>
            </div>
            
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-800 mb-2">Include Components</h4>
              <div className="space-y-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="include-base-salary"
                    checked={true}
                    disabled
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="include-base-salary" className="ml-2 block text-sm text-gray-700">
                    Base Salary & Allowances
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="include-overtime"
                    checked={true}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    disabled={processing}
                  />
                  <label htmlFor="include-overtime" className="ml-2 block text-sm text-gray-700">
                    Overtime & Shift Differentials
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="include-attendance"
                    checked={true}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    disabled={processing}
                  />
                  <label htmlFor="include-attendance" className="ml-2 block text-sm text-gray-700">
                    Attendance & Leave Integration
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="include-taxes"
                    checked={true}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    disabled={processing}
                  />
                  <label htmlFor="include-taxes" className="ml-2 block text-sm text-gray-700">
                    Taxes & Deductions
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="include-loans"
                    checked={true}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    disabled={processing}
                  />
                  <label htmlFor="include-loans" className="ml-2 block text-sm text-gray-700">
                    Loan & Advance Deductions
                  </label>
                </div>
              </div>
            </div>
            
            <div className="mb-4 border-t border-gray-200 pt-4">
              <h4 className="text-sm font-medium text-gray-800 mb-2">Approval Workflow</h4>
              <select 
                className="block w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 p-2 text-sm"
                disabled={processing}
              >
                <option value="standard">Standard Approval Flow (Department Head → HR → Finance)</option>
                <option value="expedited">Expedited Approval (HR Director Only)</option>
                <option value="none">No Approval Required</option>
              </select>
            </div>
            
            <div className="mb-4 border-t border-gray-200 pt-4">
              <h4 className="text-sm font-medium text-gray-800 mb-2">Additional Options</h4>
              <div className="space-y-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="generate-payslips"
                    checked={true}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    disabled={processing}
                  />
                  <label htmlFor="generate-payslips" className="ml-2 block text-sm text-gray-700">
                    Generate Payslips
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="email-notification"
                    checked={false}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    disabled={processing}
                  />
                  <label htmlFor="email-notification" className="ml-2 block text-sm text-gray-700">
                    Send Email Notifications
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="save-template"
                    checked={false}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    disabled={processing}
                  />
                  <label htmlFor="save-template" className="ml-2 block text-sm text-gray-700">
                    Save as Processing Template
                  </label>
                </div>
              </div>
            </div>
            
            <div className="mt-auto pt-4 border-t border-gray-200">
              <button
                onClick={processPayroll}
                disabled={selectedEmployees.length === 0 || !selectedPeriod || processing || (selectedPeriod?.status !== 'draft')}
                className={`w-full flex items-center justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white
                  ${(selectedEmployees.length === 0 || !selectedPeriod || processing || (selectedPeriod?.status !== 'draft'))
                    ? 'bg-gray-300 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                  }`}
              >
                {processing ? (
                  <>
                    <Loader className="h-4 w-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Process Payroll
                  </>
                )}
              </button>
              
              {selectedPeriod && selectedPeriod.status !== 'draft' && !processing && (
                <div className="mt-2 text-sm text-red-600 flex items-center justify-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  Cannot process payroll for periods that are not in draft status
                </div>
              )}
              
              {selectedEmployees.length === 0 && !processing && (
                <div className="mt-2 text-sm text-yellow-600 flex items-center justify-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  Please select at least one employee
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BulkPayrollProcessor; 