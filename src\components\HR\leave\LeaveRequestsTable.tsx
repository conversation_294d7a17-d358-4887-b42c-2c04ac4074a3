import React, { useState, useEffect } from 'react';
import { 
  Check, 
  X, 
  Eye, 
  Edit, 
  Trash2, 
  Plus, 
  Download, 
  Upload, 
  Calendar,
  Clock,
  User,
  FileText,
  AlertTriangle,
  Shield,
  History,
  Search,
  File
} from 'lucide-react';
import { LeaveRequest, LeaveStatus } from '../../../types/attendance';
import { UserRole } from '../../../types/common';

// Types and Interfaces
interface EmployeeLeaveData {
  employeeId: number;
  employeeName: string;
  employeeCode: string;
  department: string;
  position: string;
  leaveBalances: Array<{
    leaveType: string;
    totalDays: number;
    usedDays: number;
    remainingDays: number;
    remaining?: number; // Add this to match employee portal
  }>;
  recentRequests: LeaveRequest[];
  totalDaysUsed: number;
  totalDaysRemaining: number;
}

interface LeaveType {
  value: string;
  label: string;
  maxDays?: number;
  requiresApproval?: boolean;
  color?: string;
}

interface LeaveRequestsTableProps {
  paginatedRequests: LeaveRequest[];
  employeeLeaveData: EmployeeLeaveData[];
  onApprove: (request: LeaveRequest, level: string) => void;
  onReject: (request: LeaveRequest, level: string) => void;
  onViewDetails: (request: LeaveRequest) => void;
  onCreateRequest?: (request: Omit<LeaveRequest, 'id'>) => Promise<void>;
  filtersBar: React.ReactNode;
  title?: string;
  showAddButton?: boolean;
  leaveTypes: LeaveType[];
  currentUser?: {
    id: number;
    role: UserRole;
    name: string;
  };
  workflowEnabled?: boolean;
  onWorkflowAction?: (requestId: string, action: any, reason?: string, modifications?: any) => Promise<void>;
  // Pagination props
  currentPage?: number;
  totalPages?: number;
  totalItems?: number;
  itemsPerPage?: number;
  onPageChange?: (page: number) => void;
  onItemsPerPageChange?: (itemsPerPage: number) => void;
}

const LeaveRequestsTable: React.FC<LeaveRequestsTableProps> = ({
  paginatedRequests,
  employeeLeaveData,
  onApprove,
  onReject,
  onViewDetails,
  onCreateRequest,
  filtersBar,
  title = "All Leave Requests",
  showAddButton = true,
  leaveTypes = [],
  currentUser,
  workflowEnabled = false,
  onWorkflowAction,
  // Pagination props
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onPageChange,
  onItemsPerPageChange
}) => {
  const [showAddLeaveModal, setShowAddLeaveModal] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState('');
  const [employeeSearch, setEmployeeSearch] = useState('');
  const [leaveType, setLeaveType] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [reason, setReason] = useState('');
  const [emergencyContact, setEmergencyContact] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [notification, setNotification] = useState<{type: 'success' | 'error', message: string} | null>(null);
  const [selectedEmployeeData, setSelectedEmployeeData] = useState<EmployeeLeaveData | null>(null);

  useEffect(() => {
    if (selectedEmployee) {
      const employeeData = employeeLeaveData.find(
        (emp) => emp.employeeId.toString() === selectedEmployee
      );
      setSelectedEmployeeData(employeeData ?? null);
    } else {
      setSelectedEmployeeData(null);
    }
  }, [selectedEmployee, employeeLeaveData]);

  // Filter employees based on search
  const filteredEmployees = employeeLeaveData.filter(employee =>
    employee.employeeName.toLowerCase().includes(employeeSearch.toLowerCase()) ||
    employee.employeeCode.toLowerCase().includes(employeeSearch.toLowerCase())
  );

  // Normalize leave types for the form with fallback
  const normalizedLeaveTypes = (leaveTypes && leaveTypes.length > 0) 
    ? leaveTypes.map(type => ({
        value: type.value,
        label: type.label
      }))
    : [
        { value: 'ANNUAL_LEAVE', label: 'Annual Leave' },
        { value: 'SICK_LEAVE', label: 'Sick Leave' },
        { value: 'CASUAL_LEAVE', label: 'Casual Leave' },
        { value: 'MATERNITY_LEAVE', label: 'Maternity Leave' },
        { value: 'PATERNITY_LEAVE', label: 'Paternity Leave' },
        { value: 'UNPAID_LEAVE', label: 'Unpaid Leave' }
      ];

  // Debug: Log the leave types being passed
  console.log('🔍 LeaveTypes passed to component:', leaveTypes);
  console.log('🔍 Normalized leave types:', normalizedLeaveTypes);
  console.log('🔍 Employee leave data:', employeeLeaveData);

  const getAvailableLeaveDays = () => {
    if (!selectedEmployeeData) return null;
    const selectedType = normalizedLeaveTypes.find(t => t.value === leaveType);
    if (!selectedType) return null;
    
    const balance = selectedEmployeeData.leaveBalances.find(b => b.leaveType === leaveType);
    return balance ? balance.remainingDays : 0;
  };

  const handleFileUpload = (files: FileList) => {
    const newFiles = Array.from(files);
    setAttachments(prev => [...prev, ...newFiles]);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files);
    }
  };

  const removeFile = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const calculateDays = (startDate: string, endDate: string) => {
    if (!startDate || !endDate) return 0;
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
  };

  const resetForm = () => {
    setSelectedEmployee('');
    setEmployeeSearch('');
    setLeaveType('');
    setStartDate('');
    setEndDate('');
    setReason('');
    setEmergencyContact('');
    setAttachments([]);
    setSelectedEmployeeData(null);
  };

  const handleSubmit = async () => {
    if (!selectedEmployee || !leaveType || !startDate || !endDate || !reason) {
      setNotification({ type: 'error', message: 'Please fill all required fields.' });
      return;
    }

    const start = new Date(startDate);
    const end = new Date(endDate);
    if (start > end) {
      setNotification({ type: 'error', message: 'End date must be after start date.' });
      return;
    }

    setIsSubmitting(true);
    try {
      const leaveRequest = {
        employeeId: parseInt(selectedEmployee),
        employeeName: selectedEmployeeData?.employeeName || '',
        leaveType,
        startDate,
        endDate,
        reason,
        status: LeaveStatus.PENDING,
        createdAt: new Date().toISOString(),
        emergencyContact: emergencyContact || undefined,
        attachments: attachments.length > 0 ? attachments.map(file => file.name) : []
      };

      if (onCreateRequest) {
        await onCreateRequest(leaveRequest);
      } else {
        // Fallback simulation
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      setNotification({ type: 'success', message: 'Leave request submitted successfully!' });
      setShowAddLeaveModal(false);
      resetForm();
      
    } catch (error: any) {
      setNotification({ type: 'error', message: error.message || 'Failed to create leave request.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Auto-dismiss notifications
  useEffect(() => {
    if (notification) {
      const timer = setTimeout(() => setNotification(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [notification]);

  // Get status badge
  const getStatusBadge = (status: LeaveStatus) => {
    const statusStyles = {
      [LeaveStatus.PENDING]: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      [LeaveStatus.APPROVED]: 'bg-green-100 text-green-800 border-green-200',
      [LeaveStatus.REJECTED]: 'bg-red-100 text-red-800 border-red-200',
      [LeaveStatus.CANCELLED]: 'bg-gray-100 text-gray-800 border-gray-200'
    };

    const statusLabels = {
      [LeaveStatus.PENDING]: '⏳ Pending',
      [LeaveStatus.APPROVED]: '✓ Approved',
      [LeaveStatus.REJECTED]: '✗ Rejected',
      [LeaveStatus.CANCELLED]: '❌ Cancelled'
    };

    return (
      <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border ${statusStyles[status]}`}>
        {statusLabels[status]}
      </span>
    );
  };

  const getLeaveTypeLabel = (leaveType: string) => {
    // First try to find in normalized types
    const type = normalizedLeaveTypes.find(t => t.value === leaveType);
    if (type) return type.label;
    
    // If not found, convert backend format to user-friendly format
    const leaveTypeMap: { [key: string]: string } = {
      'CAUSAL_LEAVES': 'Causal Leave',
      'SICK_LEAVE': 'Sick Leave',
      'ANNUAL_LEAVE': 'Annual Leave',
      'MATERNITY_LEAVE': 'Maternity Leave',
      'PATERNITY_LEAVE': 'Paternity Leave',
      'UNPAID_LEAVE': 'Unpaid Leave',
      'CASUAL_LEAVE': 'Casual Leave',
      'EMERGENCY_LEAVE': 'Emergency Leave',
      'COMPENSATORY_LEAVE': 'Compensatory Leave'
    };
    
    return leaveTypeMap[leaveType] || leaveType.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  const getLeaveTypeColor = (leaveType: string) => {
    const colorMap: { [key: string]: string } = {
      'SICK_LEAVE': 'bg-red-500 hover:bg-red-600',
      'CASUAL_LEAVE': 'bg-violet-500 hover:bg-violet-600',
      'ANNUAL_LEAVE': 'bg-green-500 hover:bg-green-600',
      'MATERNITY_LEAVE': 'bg-pink-500 hover:bg-pink-600',
      'PATERNITY_LEAVE': 'bg-purple-500 hover:bg-purple-600',
      'UNPAID_LEAVE': 'bg-gray-500 hover:bg-gray-600',
      'EMERGENCY_LEAVE': 'bg-orange-500 hover:bg-orange-600',
      'COMPENSATORY_LEAVE': 'bg-indigo-500 hover:bg-indigo-600',
      'CAUSAL_LEAVES': 'bg-violet-500 hover:bg-violet-600'
    };
    
    return colorMap[leaveType] || 'bg-gray-400 hover:bg-gray-500';
  };

  // Add a helper function to render approval actions for a given level
  const renderApprovalActions = (request: any, level: string) => {
    const approval = request.approvals?.find((a: any) => a.level === level);
    
    // If no approval record exists, show N/A
    if (!approval) {
      return <span className="text-xs text-gray-500">N/A</span>;
    }

    // If already approved or rejected, show status badge
    if (approval.status === 'approved') {
      return (
        <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border bg-green-100 text-green-800 border-green-200">
          ✓ Approved
        </span>
      );
    }

    if (approval.status === 'rejected') {
      return (
        <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border bg-red-100 text-red-800 border-red-200">
          ✗ Rejected
        </span>
      );
    }

    // If cancelled, show cancelled badge
    if (approval.status === 'cancelled') {
      return (
        <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border bg-gray-100 text-gray-800 border-gray-200">
          ❌ Cancelled
        </span>
      );
    }

    // If pending and request is still pending, show approve/reject buttons
    if (approval.status === 'pending' && request.status === 'PENDING') {
      return (
        <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border bg-yellow-100 text-yellow-800 border-yellow-200">
          ⏳ Pending
        </span>
      );
    }

    // Default pending state
    return (
      <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border bg-yellow-100 text-yellow-800 border-yellow-200">
        ⏳ Pending
      </span>
    );
  };

  // Add a helper function to render approval badge for a given level
  const renderApprovalBadge = (approvals: any[] | undefined, level: string) => {
    const approval = approvals?.find((a: any) => a.level === level);
    if (!approval) return <span className="text-xs text-gray-500">N/A</span>;
    let badgeColor = '';
    let statusText = '';
    switch (approval.status) {
      case 'approved':
        badgeColor = 'bg-green-100 text-green-800 border-green-200';
        statusText = 'Approved';
        break;
      case 'rejected':
        badgeColor = 'bg-red-100 text-red-800 border-red-200';
        statusText = 'Rejected';
        break;
      case 'cancelled':
        badgeColor = 'bg-gray-100 text-gray-800 border-gray-200';
        statusText = 'Cancelled';
        break;
      default:
        badgeColor = 'bg-yellow-100 text-yellow-800 border-yellow-200';
        statusText = 'Pending';
    }
    return (
      <span className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full border ${badgeColor}`} title={approval.approverName || ''}>
        {statusText}
        {approval.approverName && (
          <span className="ml-1 text-gray-500">({approval.approverName})</span>
        )}
      </span>
    );
  };

  // Empty state
  if (paginatedRequests.length === 0) {
    return (
      <>
        {/* Header with title and button */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
          {showAddButton && (
            <button
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm"
              onClick={() => setShowAddLeaveModal(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Leave Request
            </button>
          )}
        </div>

        <div className="mb-6">{filtersBar}</div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
          <Calendar className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No leave requests found</h3>
          <p className="text-gray-500 mb-6">
            No requests match your current filters. Try adjusting your search criteria.
          </p>
          {showAddButton && (
            <button
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              onClick={() => setShowAddLeaveModal(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Create First Request
            </button>
          )}
        </div>

        {/* Add Leave Modal */}
        {showAddLeaveModal && (
          <AddLeaveModal
            isOpen={showAddLeaveModal}
            onClose={() => {
              setShowAddLeaveModal(false);
              resetForm();
            }}
            employeeLeaveData={employeeLeaveData}
            filteredEmployees={filteredEmployees}
            leaveTypes={normalizedLeaveTypes}
            selectedEmployee={selectedEmployee}
            setSelectedEmployee={setSelectedEmployee}
            employeeSearch={employeeSearch}
            setEmployeeSearch={setEmployeeSearch}
            leaveType={leaveType}
            setLeaveType={setLeaveType}
            startDate={startDate}
            setStartDate={setStartDate}
            endDate={endDate}
            setEndDate={setEndDate}
            reason={reason}
            setReason={setReason}
            emergencyContact={emergencyContact}
            setEmergencyContact={setEmergencyContact}
            attachments={attachments}
            handleFileUpload={handleFileUpload}
            handleDragOver={handleDragOver}
            handleDrop={handleDrop}
            removeFile={removeFile}
            formatFileSize={formatFileSize}
            isSubmitting={isSubmitting}
            onSubmit={handleSubmit}
            notification={notification}
            calculateDays={calculateDays}
            selectedEmployeeData={selectedEmployeeData ?? null}
            getAvailableLeaveDays={getAvailableLeaveDays}
            resetForm={resetForm}
          />
        )}
      </>
    );
  }

  return (
    <>
      {/* Header with title and button */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-gray-900">{title}</h3>
        {showAddButton && (
          <button
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors shadow-sm"
            onClick={() => setShowAddLeaveModal(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Leave Request
          </button>
        )}
      </div>

      <div className="mb-6">{filtersBar}</div>
      
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
        <div className="overflow-x-auto shadow-sm border border-gray-200 rounded-lg">
          <table className="w-full divide-y divide-gray-200 bg-white">
            <thead className="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-bold text-black uppercase tracking-wider">
                  Employee
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-black uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider hidden md:table-cell">
                  DURATION
                </th>

                <th className="px-6 py-4 text-left text-xs font-bold text-black uppercase tracking-wider hidden lg:table-cell">
                  Reason
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-black uppercase tracking-wider hidden md:table-cell">
                  Applied
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-black uppercase tracking-wider">
                  Manager
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-black uppercase tracking-wider">
                  HR
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-black uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-black uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedRequests.map((request) => (
                <tr key={request.id} className="hover:bg-gray-50 transition-colors">
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-white">
                          {request.employeeName
                            ? request.employeeName.split(' ').map((n: string, index: number) => n[0]).join('').slice(0, 2)
                            : 'NA'}
                        </span>
                      </div>
                      <div className="ml-3 min-w-0 flex-1">
                        <div className="text-sm font-semibold text-gray-900 truncate">
                          {request.employeeName || 'Unknown Employee'}
                        </div>
                                                  <div className="text-xs text-gray-500 space-y-0.5">
                            <div className="flex items-center">
                              <span className="font-medium text-gray-600">ID:</span>
                              <span className="ml-1 text-gray-700">
                                {request.employeeCode || 'N/A'}
                              </span>
                            </div>
                            <div className="flex items-center">
                              <span className="font-medium text-gray-600">Dept:</span>
                              <span className="ml-1 text-gray-700">
                                {request.department || 'N/A'}
                              </span>
                            </div>
                          </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="inline-flex">
                      <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold text-white ${getLeaveTypeColor(request.leaveType)}`}>
                        {getLeaveTypeLabel(request.leaveType)}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 hidden md:table-cell">
                    <div className="flex flex-col">
                      <div className="text-sm font-semibold text-gray-900">
                        {(() => {
                          const startDate = new Date(request.startDate);
                          const endDate = new Date(request.endDate);
                          const currentYear = new Date().getFullYear();
                          
                          // Format date as YYYY-MM-DD
                          const formatDate = (date: Date) => {
                            const year = date.getFullYear();
                            const month = String(date.getMonth() + 1).padStart(2, '0');
                            const day = String(date.getDate()).padStart(2, '0');
                            return `${year}-${month}-${day}`;
                          };
                          
                          const startFormatted = formatDate(startDate);
                          const endFormatted = formatDate(endDate);
                          
                          return startFormatted;
                        })()}
                      </div>
                      <div className="text-xs text-gray-500">
                        to {(() => {
                          const endDate = new Date(request.endDate);
                          const currentYear = new Date().getFullYear();
                          
                          const formatDate = (date: Date) => {
                            const year = date.getFullYear();
                            const month = String(date.getMonth() + 1).padStart(2, '0');
                            const day = String(date.getDate()).padStart(2, '0');
                            return `${year}-${month}-${day}`;
                          };
                          
                          return formatDate(endDate);
                        })()}
                      </div>
                      <div className="text-sm font-bold text-blue-600 mt-1">
                        {calculateDays(request.startDate, request.endDate)} day{calculateDays(request.startDate, request.endDate) !== 1 ? 's' : ''}
                      </div>
                    </div>
                  </td>

                  <td className="px-4 py-3 hidden lg:table-cell">
                    <div className="text-sm text-gray-900 max-w-xs truncate" title={request.reason}>
                      {request.reason}
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 hidden md:table-cell">
                    <div>
                      {(() => {
                        const appliedDate = request.appliedOn || request.createdAt || request.createdAt;
                        if (!appliedDate) return 'N/A';
                        
                        const date = new Date(appliedDate);
                        const currentYear = new Date().getFullYear();
                        
                        // Format: "Aug 10, 2:30 PM" or "Aug 10, 2024, 2:30 PM" if different year
                        const month = date.toLocaleDateString('en-US', { month: 'short' });
                        const day = date.getDate();
                        const year = date.getFullYear();
                        const time = date.toLocaleTimeString('en-US', { 
                          hour: 'numeric', 
                          minute: '2-digit',
                          hour12: true 
                        });
                        
                        const datePart = year !== currentYear 
                          ? `${month} ${day}, ${year}` 
                          : `${month} ${day}`;
                        
                        return `${datePart}, ${time}`;
                      })()}
                    </div>
                  </td>
                  {/* Manager Approval Column */}
                  <td className="px-4 py-3 whitespace-nowrap">
                    {renderApprovalActions(request, 'MANAGER')}
                  </td>
                  {/* HR Approval Column */}
                  <td className="px-4 py-3 whitespace-nowrap">
                    {renderApprovalActions(request, 'HR')}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    {getStatusBadge(request.status)}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <button 
                        onClick={() => onViewDetails(request)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded-full hover:bg-blue-50 transition-colors" 
                        title="View Details"
                        aria-label={`View details for ${request.employeeName}'s leave request`}
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      {request.status === LeaveStatus.PENDING && (
                        <>
                          {/* Smart logic to determine which level to approve */}
                          {(() => {
                            // Check which approval level is pending
                            const managerApproval = request.approvals?.find((a: any) => a.level === 'MANAGER');
                            const hrApproval = request.approvals?.find((a: any) => a.level === 'HR');
                            
                            // If manager approval is pending, show manager buttons
                            if (managerApproval?.status === 'pending') {
                              return (
                                <>
                                  <button 
                                    onClick={() => onApprove(request, 'MANAGER')}
                                    className="text-green-600 hover:text-green-900 p-1 rounded-full hover:bg-green-50 transition-colors"
                                    title="Approve as Manager"
                                    aria-label={`Approve leave request as Manager for ${request.employeeName}`}
                                  >
                                    <Check className="h-4 w-4" />
                                  </button>
                                  <button 
                                    onClick={() => onReject(request, 'MANAGER')}
                                    className="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-50 transition-colors"
                                    title="Reject as Manager"
                                    aria-label={`Reject leave request as Manager for ${request.employeeName}`}
                                  >
                                    <X className="h-4 w-4" />
                                  </button>
                                </>
                              );
                            }
                            
                            // If HR approval is pending, show HR buttons
                            if (hrApproval?.status === 'pending') {
                              return (
                                <>
                                  <button 
                                    onClick={() => onApprove(request, 'HR')}
                                    className="text-green-600 hover:text-green-900 p-1 rounded-full hover:bg-green-50 transition-colors"
                                    title="Approve as HR"
                                    aria-label={`Approve leave request as HR for ${request.employeeName}`}
                                  >
                                    <Check className="h-4 w-4" />
                                  </button>
                                  <button 
                                    onClick={() => onReject(request, 'HR')}
                                    className="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-50 transition-colors"
                                    title="Reject as HR"
                                    aria-label={`Reject leave request as HR for ${request.employeeName}`}
                                  >
                                    <X className="h-4 w-4" />
                                  </button>
                                </>
                              );
                            }
                            
                            // Fallback: if no specific pending level found, show general buttons
                            return (
                              <>
                                <button 
                                  onClick={() => onApprove(request, 'MANAGER')}
                                  className="text-green-600 hover:text-green-900 p-1 rounded-full hover:bg-green-50 transition-colors"
                                  title="Approve"
                                  aria-label={`Approve leave request for ${request.employeeName}`}
                                >
                                  <Check className="h-4 w-4" />
                                </button>
                                <button 
                                  onClick={() => onReject(request, 'MANAGER')}
                                  className="text-red-600 hover:text-red-900 p-1 rounded-full hover:bg-red-50 transition-colors"
                                  title="Reject"
                                  aria-label={`Reject leave request for ${request.employeeName}`}
                                >
                                  <X className="h-4 w-4" />
                                </button>
                              </>
                            );
                          })()}
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {currentPage && totalPages && totalItems && onPageChange && (
        <div className="bg-white px-6 py-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 text-sm text-gray-700">
              <span>
                Showing {((currentPage - 1) * (itemsPerPage || 10)) + 1} to{' '}
                {Math.min(currentPage * (itemsPerPage || 10), totalItems)} of {totalItems} results
              </span>
            </div>
            
            <div className="flex items-center space-x-2">
              {/* Items per page selector */}
              {onItemsPerPageChange && (
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-700">Show:</span>
                  <select
                    value={itemsPerPage || 10}
                    onChange={(e) => onItemsPerPageChange(Number(e.target.value))}
                    className="border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value={10}>10</option>
                    <option value={25}>25</option>
                    <option value={50}>50</option>
                    <option value={100}>100</option>
                  </select>
                  <span className="text-sm text-gray-700">per page</span>
                </div>
              )}
              
              {/* Pagination controls */}
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => onPageChange(currentPage - 1)}
                  disabled={currentPage <= 1}
                  className="px-3 py-1 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                
                {/* Page numbers */}
                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }
                    
                    return (
                      <button
                        key={pageNum}
                        onClick={() => onPageChange(pageNum)}
                        className={`px-3 py-1 text-sm font-medium rounded-md ${
                          pageNum === currentPage
                            ? 'bg-blue-600 text-white'
                            : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>
                
                <button
                  onClick={() => onPageChange(currentPage + 1)}
                  disabled={currentPage >= totalPages}
                  className="px-3 py-1 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Leave Modal */}
      {showAddLeaveModal && (
        <AddLeaveModal
          isOpen={showAddLeaveModal}
          onClose={() => {
            setShowAddLeaveModal(false);
            resetForm();
          }}
          employeeLeaveData={employeeLeaveData}
          filteredEmployees={filteredEmployees}
          leaveTypes={normalizedLeaveTypes}
          selectedEmployee={selectedEmployee}
          setSelectedEmployee={setSelectedEmployee}
          employeeSearch={employeeSearch}
          setEmployeeSearch={setEmployeeSearch}
          leaveType={leaveType}
          setLeaveType={setLeaveType}
          startDate={startDate}
          setStartDate={setStartDate}
          endDate={endDate}
          setEndDate={setEndDate}
          reason={reason}
          setReason={setReason}
          emergencyContact={emergencyContact}
          setEmergencyContact={setEmergencyContact}
                      attachments={attachments}
          handleFileUpload={handleFileUpload}
          handleDragOver={handleDragOver}
          handleDrop={handleDrop}
          removeFile={removeFile}
          formatFileSize={formatFileSize}
          isSubmitting={isSubmitting}
          onSubmit={handleSubmit}
          notification={notification}
          calculateDays={calculateDays}
          selectedEmployeeData={selectedEmployeeData ?? null}
          getAvailableLeaveDays={getAvailableLeaveDays}
          resetForm={resetForm}
        />
      )}

      {/* Global notification */}
      {notification && (
        <div className="fixed top-4 right-4 z-50 max-w-sm">
          <div className={`p-4 rounded-lg shadow-lg border ${
            notification.type === 'success' 
              ? 'bg-green-50 text-green-800 border-green-200' 
              : 'bg-red-50 text-red-800 border-red-200'
          }`}>
            <div className="flex">
              <div className="flex-shrink-0">
                {notification.type === 'success' ? (
                  <Check className="h-5 w-5 text-green-400" />
                ) : (
                  <X className="h-5 w-5 text-red-400" />
                )}
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium">{notification.message}</p>
              </div>
              <div className="ml-auto pl-3">
                <button
                  onClick={() => setNotification(null)}
                  className="inline-flex text-gray-400 hover:text-gray-600"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

// Modal Component
interface AddLeaveModalProps {
  isOpen: boolean;
  onClose: () => void;
  employeeLeaveData: EmployeeLeaveData[];
  filteredEmployees: EmployeeLeaveData[];
  leaveTypes: { value: string; label: string }[];
  selectedEmployee: string;
  setSelectedEmployee: (value: string) => void;
  employeeSearch: string;
  setEmployeeSearch: (value: string) => void;
  leaveType: string;
  setLeaveType: (value: string) => void;
  startDate: string;
  setStartDate: (value: string) => void;
  endDate: string;
  setEndDate: (value: string) => void;
  reason: string;
  setReason: (value: string) => void;
  emergencyContact: string;
  setEmergencyContact: (value: string) => void;
  attachments: File[];
  handleFileUpload: (files: FileList) => void;
  handleDragOver: (e: React.DragEvent) => void;
  handleDrop: (e: React.DragEvent) => void;
  removeFile: (index: number) => void;
  formatFileSize: (bytes: number) => string;
  isSubmitting: boolean;
  onSubmit: () => void;
  notification: {type: 'success' | 'error', message: string} | null;
  calculateDays: (start: string, end: string) => number;
  selectedEmployeeData: EmployeeLeaveData | null;
  getAvailableLeaveDays: () => number | null;
  resetForm: () => void;
}

const AddLeaveModal: React.FC<AddLeaveModalProps> = ({
  isOpen,
  onClose,
  employeeLeaveData,
  filteredEmployees,
  leaveTypes,
  selectedEmployee,
  setSelectedEmployee,
  employeeSearch,
  setEmployeeSearch,
  leaveType,
  setLeaveType,
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  reason,
  setReason,
  emergencyContact,
  setEmergencyContact,
  attachments,
  handleFileUpload,
  handleDragOver,
  handleDrop,
  removeFile,
  formatFileSize,
  isSubmitting,
  onSubmit,
  notification,
  calculateDays,
  selectedEmployeeData,
  getAvailableLeaveDays,
  resetForm
}) => {
  const [showEmployeeDropdown, setShowEmployeeDropdown] = useState(false);

  if (!isOpen) return null;

  const totalDays = startDate && endDate ? calculateDays(startDate, endDate) : 0;
  const availableDays = getAvailableLeaveDays();

  // Handle employee selection
  const handleEmployeeSelect = (employee: EmployeeLeaveData) => {
    setSelectedEmployee(employee.employeeId.toString());
    setEmployeeSearch(employee.employeeName);
    setShowEmployeeDropdown(false);
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEmployeeSearch(value);
    setShowEmployeeDropdown(true);
    
    // Clear selected employee if search is cleared
    if (!value) {
      setSelectedEmployee('');
    }
  };

  // Handle modal close with form reset
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // Get display value for employee input
  const getEmployeeDisplayValue = () => {
    if (selectedEmployee && selectedEmployeeData) {
      return selectedEmployeeData.employeeName;
    }
    return employeeSearch;
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 font-sans">
      {/* Modal Overlay */}
      <div className="absolute inset-0 bg-gray-900/40 backdrop-blur-[2px] transition-opacity duration-300 ease-in-out" onClick={onClose} />
      {/* Modal Card */}
      <div className="relative w-full max-w-3xl max-h-[90vh] bg-white rounded-lg shadow-xl border border-gray-200 flex flex-col">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Calendar className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">New Leave Request</h3>
              <p className="text-sm text-gray-500">Complete the form below to submit your request</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-gray-100"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto px-6 py-4">
          <form className="space-y-6">
            {/* Employee Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Employee <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    className="w-full pl-10 pr-4 py-2.5 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
                    placeholder="Search and select employee..."
                    value={getEmployeeDisplayValue()}
                    onChange={handleSearchChange}
                    onFocus={() => setShowEmployeeDropdown(true)}
                    onBlur={() => setTimeout(() => setShowEmployeeDropdown(false), 200)}
                  />
                  {selectedEmployee && (
                    <button
                      type="button"
                      onClick={() => {
                        setSelectedEmployee('');
                        setEmployeeSearch('');
                        setShowEmployeeDropdown(false);
                      }}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  )}
                </div>
                
                {showEmployeeDropdown && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                    {filteredEmployees.length > 0 ? (
                      filteredEmployees.map((employee, index) => (
                        <div
                          key={`${employee.employeeId}-${employee.employeeCode}-${index}`}
                          className="px-4 py-2.5 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                          onClick={() => handleEmployeeSelect(employee)}
                        >
                          <div className="font-medium text-gray-900">{employee.employeeName}</div>
                          <div className="text-sm text-gray-500">{employee.department} • {employee.employeeCode}</div>
                        </div>
                      ))
                    ) : (
                      <div className="px-4 py-2.5 text-gray-500 text-sm">No employees found</div>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Leave Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Leave Type <span className="text-red-500">*</span>
                </label>
                <select
                  className="w-full border border-gray-300 rounded-lg px-3 py-2.5 bg-white text-gray-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
                  value={leaveType}
                  onChange={e => setLeaveType(e.target.value)}
                >
                  <option key="placeholder" value="" disabled>
                    Select leave type
                  </option>
                  {selectedEmployeeData && selectedEmployeeData.leaveBalances && selectedEmployeeData.leaveBalances.length > 0
                    ? selectedEmployeeData.leaveBalances.map((balance, index) => (
                        <option key={`${balance.leaveType}-${index}`} value={balance.leaveType}>
                          {balance.leaveType.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())} ({(balance.remaining ?? balance.remainingDays ?? 0)} days available)
                        </option>
                      ))
                    : leaveTypes.map((type, index) => (
                        <option key={`${type.value}-${index}`} value={type.value}>
                          {type.label} (Balance not available)
                        </option>
                      ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Days Requested
                </label>
                <div className="px-3 py-2.5 border border-gray-300 rounded-lg bg-gray-50 text-gray-900 font-medium">
                  {totalDays > 0 ? `${totalDays} day${totalDays > 1 ? 's' : ''}` : 'Select dates'}
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Start Date <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2.5 bg-white text-gray-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
                  value={startDate}
                  onChange={e => setStartDate(e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  End Date <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2.5 bg-white text-gray-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
                  value={endDate}
                  onChange={e => setEndDate(e.target.value)}
                  min={startDate || new Date().toISOString().split('T')[0]}
                />
              </div>
            </div>

            {/* Reason */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reason <span className="text-red-500">*</span>
              </label>
              <textarea
                className="w-full border border-gray-300 rounded-lg px-3 py-2.5 bg-white text-gray-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all resize-none"
                value={reason}
                onChange={e => setReason(e.target.value)}
                rows={3}
                placeholder="Provide reason for leave request..."
              />
            </div>

            {/* Emergency Contact */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Emergency Contact
              </label>
              <input
                type="tel"
                className="w-full border border-gray-300 rounded-lg px-3 py-2.5 bg-white text-gray-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all"
                value={emergencyContact}
                onChange={e => setEmergencyContact(e.target.value)}
                placeholder="Enter emergency contact number"
              />
            </div>

            {/* Attachments */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Supporting Documents
              </label>
              <div 
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors cursor-pointer bg-gray-50"
                onDragOver={handleDragOver}
                onDrop={handleDrop}
              >
                <input
                  type="file"
                  multiple
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.txt"
                  onChange={e => e.target.files && handleFileUpload(e.target.files)}
                  className="hidden"
                  id="fileUpload"
                />
                <label htmlFor="fileUpload" className="cursor-pointer">
                  <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">
                    <span className="font-medium text-blue-600">Click to upload</span> or drag and drop
                  </p>
                  <p className="text-xs text-gray-500 mt-1">PDF, DOC, JPG, PNG (Max 10MB per file)</p>
                </label>
              </div>
              {attachments.length > 0 && (
                <div className="mt-3 space-y-2">
                  {attachments.map((file, index) => (
                    <div key={index} className="flex items-center justify-between bg-white p-3 rounded-lg border border-gray-200">
                      <div className="flex items-center gap-3">
                        <File className="h-5 w-5 text-blue-500" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">{file.name}</p>
                          <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                        </div>
                      </div>
                      <button
                        onClick={() => removeFile(index)}
                        className="text-red-500 hover:text-red-700 p-1 rounded-full hover:bg-red-50"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 px-6 py-4 flex items-center justify-end gap-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            onClick={onSubmit}
            disabled={isSubmitting || !selectedEmployee || !leaveType || !startDate || !endDate || !reason}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Request'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default LeaveRequestsTable;