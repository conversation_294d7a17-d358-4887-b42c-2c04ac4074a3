import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import cookieParser from 'cookie-parser';
import errorHandler from './middleware/errorHandler';
import authRoutes from './routes/authRoutes';
import ticketRoutes from './routes/ticketRoutes';
import userRoutes from './routes/userRoutes';
import dashboardRoutes from './routes/dashboardRoutes';
import knowledgeBaseRoutes from './routes/knowledgeBaseRoutes';
import vendorRoutes from './routes/vendorRoutes';
import softwareLicenseRoutes from './routes/softwareLicenseRoutes';
import emailAccountRoutes from './routes/emailAccountRoutes';
import roleRoutes from './routes/roleRoutes';
import roleTemplateRoutes from './routes/roleTemplateRoutes';
import permissionGroupRoutes from './routes/permissionGroupRoutes';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import path from 'path';
import assetRoutes from './routes/assetRoutes';
import assetMaintenanceRoutes from './routes/assetMaintenanceRoutes';
import morgan from 'morgan';
import 'reflect-metadata';
import logger from './utils/logger';
import { AppDataSource } from '../config/database';
import { User } from '../entities/User';
import fileUpload from 'express-fileupload';
import fs from 'fs';
import http from 'http';
import { initializeSocketServer } from './socket/socketHandler';
import { setSocketInstance } from './socket';
import multer from 'multer';
import { Asset } from '../entities/Asset';
import printerMaintenanceRoutes from './routes/printerMaintenanceRoutes';
import billingInvoiceRoutes from '../routes/billingInvoice.routes';
import uploadRoutes from '../routes/uploadRoutes';
import downloadRoutes from '../routes/downloadRoutes';
import itOperationLogRoutes from '../routes/itOperationLog.routes';
import employeeRoutes from './routes/employeeRoutes';
import documentsRoutes from '../routes/documentsRoutes';
import attendanceRoutes from './routes/attendanceRoutes';
import systemLogRoutes from './routes/systemLogRoutes';
import projectRoutes from '../routes/projectRoutes';
import taskRoutes from '../routes/taskRoutes';
import recruitmentRoutes from '../routes/recruitment';
import leavePolicyRoutes from '../routes/leavePolicyRoutes';
import leaveBalanceRoutes from '../routes/leaveBalanceRoutes';
import leaveAllocationRoutes from '../routes/leaveAllocationRoutes';
import leaveRequestRoutes from '../routes/leaveRequestRoutes';
import leaveApprovalRoutes from '../routes/leaveApprovalRoutes';
import expenseRoutes from '../routes/expenseRoutes';
import budgetRoutes from '../routes/budgetRoutes';
import { activityLogger } from './middleware/activityLogger';
import { SystemLogService } from './services/SystemLogService';
import { LogType } from '../entities/SystemLog';
import { createShiftRoutes } from '../routes/shiftRoutes';
import holidayRoutes from '../routes/holidays';
import { initializeServices, shutdownServices } from '../services';

// At the top of your file, declare all upload directories once
const UPLOAD_DIRS = {
  itLogs: path.join(process.cwd(), 'public', 'uploads', 'it-logs'),
  assets: path.join(process.cwd(), 'public', 'uploads', 'assets'),
  debug: path.join(process.cwd(), 'public', 'uploads', 'debug'),
  // Add any other upload directories here
};

// Ensure all upload directories exist
Object.values(UPLOAD_DIRS).forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log('Created upload directory:', dir);
  }
});

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'test');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  }
});

dotenv.config();

const app = express();
const PORT = process.env.PORT ? parseInt(process.env.PORT, 10) : 5000;

// Define CORS options
const corsOptions = {
  origin: 'http://localhost:3000',  // Set to the specific client origin
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With'],
  exposedHeaders: ['Authorization', 'Content-Type', 'Content-Disposition', 'Content-Length'],
  preflightContinue: false,
  optionsSuccessStatus: 204
};

// Middleware setup
app.use(cors(corsOptions));
// Special CORS settings for file downloads
app.use('/api/printer-maintenance/:id/download', (req, res, next) => {
  res.header('Access-Control-Allow-Origin', 'http://localhost:3000');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Expose-Headers', 'Content-Disposition, Content-Type, Content-Length');
  next();
});
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use(cookieParser());
app.use(helmet());
app.disable('x-powered-by');
app.use(morgan('dev'));

// Configure rate limiting
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 2000 // limit each IP to 2000 requests per windowMs
});

const uploadLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 file uploads per windowMs
});

// Apply rate limiting to all routes
app.use('/api/', apiLimiter);

// Apply specific rate limit to file upload routes
app.use('/api/assets', uploadLimiter);

// Apply activity logger middleware to track user actions
app.use('/api', activityLogger);

// Serve static files from public directory
app.use(express.static(path.join(__dirname, '../../public')));

// Add direct route for uploads with proper CORS and file serving
app.use('/uploads', (req, res, next) => {
  // Set CORS headers
  res.header('Access-Control-Allow-Origin', 'http://localhost:3000');
  res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  res.header('Access-Control-Allow-Credentials', 'true');
  
  // If it's a preflight OPTIONS request, respond immediately
  if (req.method === 'OPTIONS') {
    return res.status(204).end();
  }
  
  // Check for inline viewing parameter
  const shouldViewInline = req.query.view === 'inline';
  
  // Log access attempts to help debug
  console.log(`[Static Files] Accessing: ${req.url}, View Inline: ${shouldViewInline}`);
  
  // Continue to the static middleware
  next();
}, express.static(path.join(__dirname, '../../public/uploads'), {
  maxAge: '1d',
  etag: true,
  setHeaders: (res, filePath) => {
    // Check URL for inline viewing parameter
    const url = res.req?.url || '';
    const shouldViewInline = url.includes('view=inline');
    
    // Set proper MIME type for PDFs
    if (filePath.endsWith('.pdf')) {
      res.setHeader('Content-Type', 'application/pdf');
      // Set Content-Disposition to 'inline' for PDFs if viewing inline
      if (shouldViewInline) {
        res.setHeader('Content-Disposition', 'inline');
      }
    }
    
    // Set proper cache headers for images
    if (filePath.endsWith('.jpg') || filePath.endsWith('.jpeg') || filePath.endsWith('.png') || filePath.endsWith('.gif') || filePath.endsWith('.webp')) {
      res.setHeader('Cache-Control', 'public, max-age=86400');
      res.setHeader('Content-Disposition', 'inline');
    }
    
    // Set header for all document types to prefer inline viewing
    if (shouldViewInline) {
      res.setHeader('Content-Disposition', 'inline');
    }
    
    // Add CORS headers for all static files
    res.setHeader('Access-Control-Allow-Origin', '*');
  }
}));

// Add direct route for the root uploads directory with proper CORS and file serving
app.use('/uploads', (req, res, next) => {
  // Set CORS headers
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  
  // Log access attempts to help debug
  console.log(`[Root Uploads] Accessing: ${req.url}`);
  
  // Continue to the static middleware
  next();
}, express.static(path.join(__dirname, '../../uploads'), {
  maxAge: '1d',
  etag: true,
  setHeaders: (res, filePath) => {
    // Set proper MIME type for PDFs
    if (filePath.endsWith('.pdf')) {
      res.setHeader('Content-Type', 'application/pdf');
    }
    
    // Set proper MIME type for documents
    if (filePath.endsWith('.docx')) {
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    }
    
    // Set proper cache headers for images
    if (filePath.endsWith('.jpg') || filePath.endsWith('.jpeg') || filePath.endsWith('.png') || filePath.endsWith('.gif') || filePath.endsWith('.webp')) {
      res.setHeader('Cache-Control', 'public, max-age=86400');
    }
    
    // Add CORS headers for all static files
    res.setHeader('Access-Control-Allow-Origin', '*');
  }
}));

// Add debug route for file paths
app.get('/debug/paths', (req, res) => {
  const publicPath = path.join(__dirname, '../../public');
  const uploadsPath = path.join(__dirname, '../../public/uploads');
  const assetsPath = path.join(__dirname, '../../public/uploads/assets');
  
  // Check if directories exist
  const publicExists = fs.existsSync(publicPath);
  const uploadsExists = fs.existsSync(uploadsPath);
  const assetsExists = fs.existsSync(assetsPath);
  
  // List files in uploads directory
  let uploadFiles: string[] = [];
  if (uploadsExists) {
    try {
      uploadFiles = fs.readdirSync(uploadsPath);
    } catch (error) {
      console.error('Error reading uploads directory:', error);
    }
  }
  
  // List files in assets directory
  let assetFiles: string[] = [];
  if (assetsExists) {
    try {
      assetFiles = fs.readdirSync(assetsPath);
    } catch (error) {
      console.error('Error reading assets directory:', error);
    }
  }
  
  // Create directories if they don't exist
  if (!publicExists) {
    try {
      fs.mkdirSync(publicPath, { recursive: true });
      console.log('Created public directory');
    } catch (error) {
      console.error('Error creating public directory:', error);
    }
  }
  
  if (!uploadsExists) {
    try {
      fs.mkdirSync(uploadsPath, { recursive: true });
      console.log('Created uploads directory');
    } catch (error) {
      console.error('Error creating uploads directory:', error);
    }
  }
  
  if (!assetsExists) {
    try {
      fs.mkdirSync(assetsPath, { recursive: true });
      console.log('Created assets directory');
    } catch (error) {
      console.error('Error creating assets directory:', error);
    }
  }
  
  res.json({
    paths: {
      publicPath,
      uploadsPath,
      assetsPath,
      __dirname,
      cwd: process.cwd()
    },
    exists: {
      publicExists,
      uploadsExists,
      assetsExists
    },
    uploadFiles,
    assetFiles
  });
});

// Add a test route to upload a file
app.post('/debug/upload-test', upload.single('testImage'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }
    
    const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'test');
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    // Generate unique filename
    const fileExt = path.extname(req.file.originalname);
    const filename = `test-${Date.now()}${fileExt}`;
    const filepath = path.join(uploadDir, filename);
    
    // Write file
    fs.writeFileSync(filepath, req.file.buffer);
    
    res.json({
      success: true,
      file: {
        originalname: req.file.originalname,
        filename: filename,
        path: `/uploads/test/${filename}`,
        size: req.file.size,
        mimetype: req.file.mimetype
      }
    });
  } catch (error) {
    console.error('Error in test upload:', error);
    res.status(500).json({ 
      error: 'Failed to upload test file',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Add a test route to serve a specific file
app.get('/test-file', (req, res) => {
  res.sendFile(path.join(__dirname, '../../public/test.txt'));
});

// Add a test route to check image uploads
app.get('/test-image-upload', (req, res) => {
  res.send(`
    <html>
      <head>
        <title>Test Image Upload</title>
        <style>
          body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
          .form-group { margin-bottom: 15px; }
          label { display: block; margin-bottom: 5px; }
          button { padding: 10px 15px; background-color: #4CAF50; color: white; border: none; cursor: pointer; }
          #result { margin-top: 20px; padding: 10px; border: 1px solid #ddd; display: none; }
          .preview-image { max-width: 200px; max-height: 200px; margin-top: 10px; display: none; }
        </style>
      </head>
      <body>
        <h1>Test Image Upload</h1>
        <form id="uploadForm" enctype="multipart/form-data">
          <div class="form-group">
            <label for="testImage">Select Image:</label>
            <input type="file" id="testImage" name="testImage" accept="image/*">
            <img id="imagePreview" class="preview-image" alt="Image Preview">
          </div>
          <button type="submit">Upload Image</button>
        </form>
        <div id="result"></div>

        <script>
          // Preview image
          document.getElementById('testImage').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
              const reader = new FileReader();
              const preview = document.getElementById('imagePreview');
              
              reader.onload = function(e) {
                preview.src = e.target.result;
                preview.style.display = 'block';
              };
              
              reader.readAsDataURL(file);
            }
          });
          
          // Form submission
          document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Uploading...';
            
            try {
              const formData = new FormData();
              const imageFile = document.getElementById('testImage').files[0];
              
              if (!imageFile) {
                resultDiv.textContent = 'Please select an image file';
                return;
              }
              
              formData.append('testImage', imageFile);
              
              const response = await fetch('/debug/upload-test', {
                method: 'POST',
                body: formData
              });
              
              const responseData = await response.json();
              
              if (response.ok) {
                resultDiv.innerHTML = 
                  '<h3>Image Uploaded Successfully!</h3>' +
                  '<pre>' + JSON.stringify(responseData, null, 2) + '</pre>' +
                  (responseData.file?.path ? '<img src="' + responseData.file.path + '" style="max-width: 100%; max-height: 300px;">' : '');
              } else {
                resultDiv.innerHTML = 
                  '<h3>Upload Failed</h3>' +
                  '<pre>' + JSON.stringify(responseData, null, 2) + '</pre>';
              }
            } catch (error) {
              resultDiv.textContent = 'Error: ' + error.message;
            }
          });
        </script>
      </body>
    </html>
  `);
});

// Add a test route to check asset ID handling
app.get('/test-asset-id/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Testing asset ID lookup for:', id);
    
    // Import the asset repository
    const { assetRepository } = require('../repositories/assetRepository');
    
    // Try to find the asset
    const asset = await assetRepository.findById(id);
    
    if (asset) {
      res.json({
        success: true,
        message: 'Asset found',
        asset: {
          id: asset.id,
          assetTag: asset.assetTag,
          serialNumber: asset.serialNumber,
          model: asset.model,
          manufacturer: asset.manufacturer
        }
      });
    } else {
      res.status(404).json({
        success: false,
        message: 'Asset not found',
        id: id
      });
    }
  } catch (error) {
    console.error('Error in test-asset-id route:', error);
    res.status(500).json({
      success: false,
      message: 'Error testing asset ID',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Add a more comprehensive test route for asset ID handling
app.get('/api/test-asset-lookup/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Testing comprehensive asset lookup for:', id);
    
    // Import the asset repository
    const { assetRepository } = require('../repositories/assetRepository');
    
    // Try to find the asset by assetTag first
    let asset = await assetRepository.findByAssetTag(id);
    
    // If not found, try to find by ID
    if (!asset && !isNaN(parseInt(id, 10))) {
      const numericId = parseInt(id, 10);
      asset = await assetRepository.findById(numericId);
    }
    
    if (asset) {
      res.json({
        success: true,
        message: 'Asset found',
        asset: {
          id: asset.id,
          assetTag: asset.assetTag,
          serialNumber: asset.serialNumber,
          model: asset.model,
          manufacturer: asset.manufacturer
        },
        lookupMethod: typeof id === 'string' && id === asset.assetTag ? 'assetTag match' : 'ID match'
      });
    } else {
      // Try direct database query to see if asset exists at all
      const assetRepo = AppDataSource.getRepository(Asset);
      
      // Try by assetTag first
      const assetByTag = await assetRepo.findOne({ where: { assetTag: id } });
      
      // Try to convert ID to number if it's numeric
      let assetByUuid = null;
      if (!isNaN(parseInt(id, 10))) {
        // If it's a numeric string, try to find by numeric ID
        const numericId = parseInt(id, 10);
        assetByUuid = await assetRepo.findOne({ where: { id: numericId } });
      }
      
      res.status(404).json({
        success: false,
        message: 'Asset not found',
        id: id,
        directUuidLookup: assetByUuid ? 'Found' : 'Not found',
        directTagLookup: assetByTag ? 'Found' : 'Not found',
        suggestion: 'Check if the asset exists in the database and if the ID format is correct'
      });
    }
  } catch (error) {
    console.error('Error in test-asset-lookup route:', error);
    res.status(500).json({
      success: false,
      message: 'Error testing asset ID',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Add a debug route to check asset tag lookup
app.get('/api/debug/asset-tag/:tag', async (req, res) => {
  try {
    const { tag } = req.params;
    console.log('Debugging asset tag lookup for:', tag);
    
    // Import the asset repository
    const { assetRepository } = require('../repositories/assetRepository');
    
    // Try direct database query to see if asset exists by tag
    const assetRepo = AppDataSource.getRepository(Asset);
    const assetByTag = await assetRepo.findOne({ where: { assetTag: tag } });
    
    if (assetByTag) {
      res.json({
        success: true,
        message: 'Asset found by direct tag lookup',
        asset: {
          id: assetByTag.id,
          assetTag: assetByTag.assetTag,
          serialNumber: assetByTag.serialNumber,
          model: assetByTag.model,
          manufacturer: assetByTag.manufacturer
        }
      });
    } else {
      // Try using the repository method
      const asset = await assetRepository.findById(tag);
      
      if (asset) {
        res.json({
          success: true,
          message: 'Asset found by repository method',
          asset: {
            id: asset.id,
            assetTag: asset.assetTag,
            serialNumber: asset.serialNumber,
            model: asset.model,
            manufacturer: asset.manufacturer
          }
        });
      } else {
        // List all asset tags in the system
        const allAssets = await assetRepo.find({ select: ['id', 'assetTag'] });
        const assetTags = allAssets.map(a => a.assetTag);
        
        res.status(404).json({
          success: false,
          message: 'Asset not found by tag',
          tag: tag,
          allAssetTags: assetTags.slice(0, 20), // Show first 20 tags to avoid overwhelming response
          suggestion: 'Check if the asset tag format matches exactly what is in the database'
        });
      }
    }
  } catch (error) {
    console.error('Error in debug asset tag route:', error);
    res.status(500).json({
      success: false,
      message: 'Error debugging asset tag',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Configure file upload middleware - exclude routes where we use multer
app.use((req, res, next) => {
  // Skip express-fileupload for IT operation log routes
  if (req.path.startsWith('/api/it-logs')) {
    console.log('Skipping express-fileupload middleware for IT logs route:', req.path);
    return next();
  }
  
  // For all other routes, use express-fileupload
  return fileUpload({
    createParentPath: true,
    limits: {
      fileSize: 50 * 1024 * 1024 // 50MB max file size (increased from 10MB)
    },
    abortOnLimit: true,
    useTempFiles: false,
    debug: process.env.NODE_ENV !== 'production',
    uploadTimeout: 60000, // 60 seconds timeout
    safeFileNames: true, // Remove special characters
    preserveExtension: true // Preserve file extension
  })(req, res, next);
});

// More lenient rate limit for user routes
const userRoutesLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 2000, // Higher limit for user routes
  message: 'Too many user requests, please try again after a minute',
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for GET requests
    return req.method === 'GET';
  }
});

// Apply rate limiting to specific routes
app.use('/api/users', userRoutesLimiter);

// Debug request logging
app.use((req, res, next) => {
  // Only log non-GET requests to reduce noise
  if (req.method !== 'GET') {
    logger.debug(`Incoming ${req.method} request to ${req.url}`, {
      headers: req.headers,
      body: req.body,
      cookies: req.cookies
    });
  }
  next();
});

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/tickets', ticketRoutes);
app.use('/api/users', userRoutes);
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/knowledge-base', knowledgeBaseRoutes);
app.use('/api/vendors', vendorRoutes);
app.use('/api/software-licenses', softwareLicenseRoutes);
app.use('/api/email-accounts', emailAccountRoutes);
app.use('/api/assets', assetRoutes);
app.use('/api/asset-maintenance', assetMaintenanceRoutes);
app.use('/api/printer-maintenance', printerMaintenanceRoutes);
app.use('/api/billing-invoices', billingInvoiceRoutes);
app.use('/api/it-logs', itOperationLogRoutes);
app.use('/api/system-logs', systemLogRoutes);
app.use('/api/uploads', uploadRoutes);
app.use('/api/downloads', downloadRoutes);
app.use('/api/employees', employeeRoutes);
app.use('/api/documents', documentsRoutes);
app.use('/api/attendance', attendanceRoutes);
app.use('/api', createShiftRoutes(AppDataSource));
app.use('/api/holidays', holidayRoutes);
app.use('/api/roles', roleRoutes);
app.use('/api/role-templates', roleTemplateRoutes);
app.use('/api/permission-groups', permissionGroupRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api/tasks', taskRoutes);
app.use('/api/hr/recruitment', recruitmentRoutes);
app.use('/api/leave-policies', leavePolicyRoutes);
app.use('/api/leave-balances', leaveBalanceRoutes);
app.use('/api/leave-allocation', leaveAllocationRoutes);
app.use('/api/leave-requests', leaveRequestRoutes);
app.use('/api/leave-approvals', leaveApprovalRoutes);
app.use('/api/expenses', expenseRoutes);
app.use('/api/budget-items', budgetRoutes);

// Special CORS settings for billing routes to handle preflight requests
app.use('/api/billing-invoices', (req, res, next) => {
  // Special CORS settings for billing routes
  if (req.method === 'OPTIONS') {
    // Handle preflight request specially
    res.header('Access-Control-Allow-Origin', 'http://localhost:3000');  // Use specific origin
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.header('Access-Control-Max-Age', '86400');  // Cache preflight for 24 hours
    res.header('Access-Control-Expose-Headers', 'Content-Disposition, Content-Type, Content-Length');
    return res.status(204).end();
  }
  // For non-OPTIONS requests
  res.header('Access-Control-Allow-Origin', 'http://localhost:3000');
  res.header('Access-Control-Allow-Credentials', 'true');
  next();
});

// Add a direct file preview route to ensure files are viewed and not downloaded
app.get('/preview/:filePath(*)', (req, res) => {
  try {
    // Get the file path from the URL parameter
    let filePath = req.params.filePath;
    console.log(`[Preview Route] Request for: ${filePath}`);
    
    // Sanitize the path to prevent directory traversal
    if (filePath.includes('..')) {
      return res.status(400).send('Invalid file path');
    }
    
    // Determine the full file path
    let fullPath;
    if (filePath.startsWith('/')) {
      // If it's an absolute path, remove the leading slash
      filePath = filePath.substring(1);
    }
    
    // Check different possible locations
    const possiblePaths = [
      path.join(process.cwd(), 'public', 'uploads', filePath),
      path.join(process.cwd(), 'public', filePath),
      path.join(process.cwd(), 'uploads', filePath)
    ];
    
    // Find the first path that exists
    fullPath = possiblePaths.find(p => fs.existsSync(p));
    
    if (!fullPath) {
      console.error(`File not found: ${filePath}. Checked paths:`, possiblePaths);
      return res.status(404).send('File not found');
    }
    
    console.log(`[Preview Route] File found at: ${fullPath}`);
    
    // Get file stats
    const stats = fs.statSync(fullPath);
    
    // Determine the content type based on the file extension
    const ext = path.extname(fullPath).toLowerCase();
    let contentType = 'application/octet-stream'; // Default content type
    
    // Check for special disposition parameter
    const forceInlineDisposition = req.query.disposition === 'inline';
    
    // For PDF files, serve an HTML wrapper with an embedded viewer UNLESS disposition=inline is specified
    if (ext === '.pdf' && !forceInlineDisposition) {
      // Get the base URL for the file
      const baseUrl = `${req.protocol}://${req.get('host')}`;
      const fileUrl = `${baseUrl}/raw-file/${encodeURIComponent(filePath)}`;
      
      // Create an HTML page with an embedded PDF viewer
      const htmlContent = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>PDF Viewer</title>
            <style>
                body, html {
                    margin: 0;
                    padding: 0;
                    height: 100%;
                    overflow: hidden;
                }
                #pdf-container {
                    width: 100%;
                    height: 100%;
                    border: none;
                }
            </style>
        </head>
        <body>
            <iframe id="pdf-container" src="${fileUrl}" allowfullscreen></iframe>
        </body>
        </html>
      `;
      
      res.setHeader('Content-Type', 'text/html');
      res.setHeader('Content-Length', Buffer.byteLength(htmlContent));
      return res.send(htmlContent);
    }
    
    // Handle PDFs with direct serving when disposition=inline is specified
    if (ext === '.pdf' && forceInlineDisposition) {
      contentType = 'application/pdf';
      console.log('Direct serving PDF with inline disposition as requested');
    } else {
      // Handle other file types
      switch (ext) {
        case '.jpg':
        case '.jpeg':
          contentType = 'image/jpeg';
          break;
        case '.png':
          contentType = 'image/png';
          break;
        case '.gif':
          contentType = 'image/gif';
          break;
        case '.webp':
          contentType = 'image/webp';
          break;
        case '.doc':
          contentType = 'application/msword';
          break;
        case '.docx':
          contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
          break;
        case '.xls':
          contentType = 'application/vnd.ms-excel';
          break;
        case '.xlsx':
          contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
          break;
        case '.txt':
          contentType = 'text/plain';
          break;
      }
    }
    
    // Set aggressive headers to force inline viewing
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', 'inline; filename="' + path.basename(fullPath) + '"');
    res.setHeader('Content-Length', stats.size);
    
    // Add cache control and pragma headers
    res.setHeader('Cache-Control', 'public, max-age=0');
    res.setHeader('Pragma', 'public');
    
    // Add CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
    
    // For PDFs, force inline display with additional headers
    if (ext === '.pdf') {
      res.setHeader('X-Content-Type-Options', 'nosniff');
      // Add extra headers to discourage downloading
      if (forceInlineDisposition) {
        res.setHeader('Content-Disposition', 'inline; filename="' + path.basename(fullPath) + '"');
        res.setHeader('X-Content-Type-Options', 'nosniff');
      }
    }
    
    // Log what we're sending
    console.log(`[Preview Route] Serving file as ${contentType} with inline disposition and size ${stats.size}`);
    
    // Stream the file
    const fileStream = fs.createReadStream(fullPath);
    fileStream.pipe(res);
    
  } catch (error) {
    console.error('Error in preview route:', error);
    res.status(500).send('Error processing file');
  }
});

// Add a raw file access route for PDF embedding
app.get('/raw-file/:filePath(*)', (req, res) => {
  try {
    // Get the file path from the URL parameter
    let filePath = req.params.filePath;
    
    // Sanitize the path to prevent directory traversal
    if (filePath.includes('..')) {
      return res.status(400).send('Invalid file path');
    }
    
    // Determine the full file path
    let fullPath;
    if (filePath.startsWith('/')) {
      // If it's an absolute path, remove the leading slash
      filePath = filePath.substring(1);
    }
    
    // Check different possible locations
    const possiblePaths = [
      path.join(process.cwd(), 'public', 'uploads', filePath),
      path.join(process.cwd(), 'public', filePath),
      path.join(process.cwd(), 'uploads', filePath)
    ];
    
    // Find the first path that exists
    fullPath = possiblePaths.find(p => fs.existsSync(p));
    
    if (!fullPath) {
      return res.status(404).send('File not found');
    }
    
    // Get file stats
    const stats = fs.statSync(fullPath);
    
    // Determine the content type based on the file extension
    const ext = path.extname(fullPath).toLowerCase();
    let contentType = 'application/octet-stream';
    
    if (ext === '.pdf') {
      contentType = 'application/pdf';
    } else if (ext === '.jpg' || ext === '.jpeg') {
      contentType = 'image/jpeg';
    } else if (ext === '.png') {
      contentType = 'image/png';
    }
    
    // Set headers for raw file access
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', 'inline');
    res.setHeader('Content-Length', stats.size);
    res.setHeader('Cache-Control', 'public, max-age=3600');
    res.setHeader('Accept-Ranges', 'bytes');
    
    // Add CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    
    // Stream the file
    const fileStream = fs.createReadStream(fullPath);
    fileStream.pipe(res);
    
  } catch (error) {
    console.error('Error in raw file access route:', error);
    res.status(500).send('Error accessing file');
  }
});

// Add this before your routes
if (process.env.NODE_ENV !== 'production') {
  app.get('/api/debug/users', async (_req, res) => {
    try {
      const userRepository = AppDataSource.getRepository(User);
      const users = await userRepository.find();
      res.json({ count: users.length, users });
    } catch (error) {
      logger.error('Debug endpoint error:', error);
      res.status(500).json({ error: 'Failed to fetch users' });
    }
  });
}

// Health check endpoints
app.get('/', (_req, res) => {
  res.json({ message: 'API is running' });
});

app.get('/health', (_req, res) => {
  res.json({ 
    status: 'ok', 
    uptime: process.uptime(),
    timestamp: new Date().toISOString(),
    cors: {
      origin: corsOptions.origin,
      methods: corsOptions.methods
    }
  });
});

// Create a WebSocket endpoint for dashboard
app.get('/api/ws/dashboard', (req, res) => {
  res.set({
    'Upgrade': 'websocket',
    'Connection': 'Upgrade'
  }).status(101).end();
});

// Add more detailed error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Express error:', err);
  res.status(500).json({
    success: false,
    message: 'Server error',
    error: err.message,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
  });
});

// Error handling
app.use(errorHandler);

// Add debug routes
app.get('/api/debug/assets', async (req, res) => {
  try {
    logger.info('Debug endpoint: Listing all assets');
    const assetRepo = AppDataSource.getRepository(Asset);
    const assets = await assetRepo.find({ select: ['id', 'assetTag', 'assetType', 'manufacturer', 'model', 'serialNumber'] });
    
    logger.info(`Found ${assets.length} assets in database`);
    
    // Return a simplified list of assets for debugging
    res.json({
      count: assets.length,
      assets: assets.map(asset => ({
        id: asset.id,
        assetTag: asset.assetTag,
        type: asset.assetType,
        manufacturer: asset.manufacturer,
        model: asset.model,
        serialNumber: asset.serialNumber
      }))
    });
  } catch (error) {
    logger.error('Error in debug assets endpoint:', error);
    res.status(500).json({ error: 'Failed to fetch assets for debugging' });
  }
});

// Add debug endpoint to test the asset controller directly
app.get('/api/debug/test-asset-controller', async (req, res) => {
  console.log('Debug test-asset-controller endpoint called');
  
  try {
    // Import the asset controller
    const { assetController } = require('./controllers/assetController');
    
    // Check if the controller exists
    if (!assetController) {
      return res.status(500).json({ error: 'Asset controller not found' });
    }
    
    // Check if the getAssets method exists
    if (!assetController.getAssets) {
      return res.status(500).json({ error: 'getAssets method not found in asset controller' });
    }
    
    // Create a mock request and response
    const mockReq = { query: {} };
    let responseData: any = null;
    let responseStatus = 200;
    
    const mockRes = {
      json: (data: any) => {
        responseData = data;
        return mockRes;
      },
      status: (code: number) => {
        responseStatus = code;
        return mockRes;
      }
    };
    
    // Call the getAssets method directly
    await assetController.getAssets(mockReq, mockRes);
    
    // Return the result
    return res.status(responseStatus).json({
      status: responseStatus,
      directControllerResponse: responseData,
      message: 'Asset controller called directly'
    });
  } catch (error: any) {
    console.error('Error in debug assets endpoint:', error);
    return res.status(500).json({
      error: 'Error calling asset controller directly',
      message: error.message,
      stack: error.stack
    });
  }
});

// Add a specific asset lookup debug endpoint
app.get('/api/debug/asset/:id', async (req, res) => {
  try {
    const { id } = req.params;
    logger.info(`Debug endpoint: Looking up asset with ID: ${id}`);
    
    const assetRepo = AppDataSource.getRepository(Asset);
    
    // Try to find by asset tag first
    let asset = await assetRepo.findOne({ where: { assetTag: id } });
    
    // If not found and ID is numeric, try by numeric ID
    if (!asset && !isNaN(parseInt(id, 10))) {
      const numericId = parseInt(id, 10);
      asset = await assetRepo.findOne({ where: { id: numericId } });
    }
    
    if (asset) {
      logger.info(`Debug: Found asset with ID ${id}:`, { 
        id: asset.id, 
        assetTag: asset.assetTag,
        type: asset.assetType
      });
      res.json({
        found: true,
        asset: {
          id: asset.id,
          assetTag: asset.assetTag,
          type: asset.assetType,
          manufacturer: asset.manufacturer,
          model: asset.model,
          serialNumber: asset.serialNumber
        }
      });
    } else {
      // If asset not found, return all asset IDs to help diagnose
      const allAssets = await assetRepo.find({ select: ['id', 'assetTag'] });
      logger.info(`Debug: Asset with ID ${id} not found. Available assets: ${allAssets.length}`);
      
      res.status(404).json({
        found: false,
        message: `Asset with ID ${id} not found`,
        availableIds: allAssets.map(a => a.id).slice(0, 10), // First 10 IDs
        availableTags: allAssets.map(a => a.assetTag).slice(0, 10) // First 10 tags
      });
    }
  } catch (error) {
    logger.error('Error in debug asset lookup endpoint:', error);
    res.status(500).json({ error: 'Failed to lookup asset for debugging' });
  }
});

// Add debug endpoint to list all registered routes
app.get('/api/debug/routes', (req, res) => {
  console.log('Debug routes endpoint called');
  
  // Get all registered routes
  const routes: any[] = [];
  
  // Function to extract routes from a router
  function extractRoutes(router: any, basePath = '') {
    if (!router || !router.stack) return;
    
    router.stack.forEach((layer: any) => {
      if (layer.route) {
        // Routes registered directly on the app
        const path = basePath + (layer.route?.path || '');
        const methods = Object.keys(layer.route.methods).map(m => m.toUpperCase()).join(', ');
        routes.push({ path, methods });
      } else if (layer.name === 'router' && layer.handle.stack) {
        // Routes registered via router
        const path = basePath + (layer.regexp.toString().replace('/^', '').replace('/(?=\\/|$)/i', '').replace(/\\\//g, '/') || '');
        extractRoutes(layer.handle, path);
      } else if (layer.name === 'bound dispatch' && layer.handle.stack) {
        // Middleware with nested routers
        extractRoutes(layer.handle, basePath);
      }
    });
  }
  
  // Extract routes from the main app
  extractRoutes(app._router);
  
  // Sort routes by path
  routes.sort((a, b) => a.path.localeCompare(b.path));
  
  // Return the routes
  res.json({
    count: routes.length,
    routes
  });
});

// Add a debug endpoint to test file uploads
app.post('/api/debug/uploads', (req, res) => {
  try {
    console.log('Debug upload endpoint called');
    console.log('Request headers:', req.headers);
    
    // Check if files were uploaded
    if (!req.files || Object.keys(req.files).length === 0) {
      return res.status(400).json({ 
        success: false, 
        message: 'No files were uploaded',
        headers: req.headers['content-type']
      });
    }
    
    // Get file information
    interface FileInfo {
      fieldname: string;
      name: string;
      mimetype: string;
      size: number;
    }
    
    const fileInfo: FileInfo[] = [];
    const fileObj = req.files as any;
    
    for (const key in fileObj) {
      const file = fileObj[key];
      if (Array.isArray(file)) {
        // Multiple files with the same field name
        file.forEach(f => {
          fileInfo.push({
            fieldname: key,
            name: f.name,
            mimetype: f.mimetype,
            size: f.size
          });
        });
      } else {
        // Single file
        fileInfo.push({
          fieldname: key,
          name: file.name,
          mimetype: file.mimetype,
          size: file.size
        });
      }
    }
    
    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'debug');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }
    
    // Save the first file
    if (fileInfo.length > 0) {
      const firstFileKey = Object.keys(fileObj)[0];
      const firstFile = fileObj[firstFileKey];
      
      if (!Array.isArray(firstFile)) {
        const filename = `debug_${Date.now()}_${firstFile.name}`;
        const filePath = path.join(uploadsDir, filename);
        
        firstFile.mv(filePath, (err: Error) => {
          if (err) {
            console.error('Error saving file:', err);
            return res.status(500).json({
              success: false,
              message: 'Error saving file',
              error: err.message
            });
          }
          
          return res.status(200).json({
            success: true,
            message: 'Debug file upload successful',
            files: fileInfo,
            savedFile: {
              path: filePath,
              url: `/uploads/debug/${filename}`
            }
          });
        });
      } else {
        return res.status(200).json({
          success: true,
          message: 'Multiple files detected',
          files: fileInfo
        });
      }
    } else {
      return res.status(200).json({
        success: true,
        message: 'No files to save',
        files: fileInfo
      });
    }
  } catch (error) {
    console.error('Debug upload error:', error);
    return res.status(500).json({
      success: false,
      message: 'Error processing upload',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Add a route to check if a file exists - helps with debugging
app.get('/api/check-file', (req, res) => {
  try {
    const filePath = req.query.path as string;
    
    if (!filePath) {
      return res.status(400).json({
        success: false,
        message: 'No file path provided'
      });
    }
    
    // Clean up the path to prevent directory traversal
    const normalizedPath = path.normalize(filePath).replace(/^(\.\.(\/|\\|$))+/, '');
    
    // Check both with and without public prefix
    const possiblePaths = [
      // Try with direct path
      path.join(process.cwd(), normalizedPath),
      // Try with public prefix
      path.join(process.cwd(), 'public', normalizedPath),
      // Try with uploads suffix
      path.join(process.cwd(), 'public/uploads', normalizedPath),
      // Try with specific directories
      path.join(process.cwd(), 'public/uploads/invoices', path.basename(normalizedPath)),
      path.join(process.cwd(), 'public/uploads/it-invoices', path.basename(normalizedPath))
    ];
    
    // Check if any of the paths exist
    const results = possiblePaths.map(p => {
      const exists = fs.existsSync(p);
      return {
        path: p,
        exists,
        stats: exists ? fs.statSync(p) : null
      };
    });
    
    const fileExists = results.some(r => r.exists);
    
    return res.status(fileExists ? 200 : 404).json({
      success: fileExists,
      message: fileExists ? 'File exists' : 'File not found',
      queriedPath: filePath,
      results
    });
  } catch (error) {
    console.error('Error checking file:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error checking file',
      error
    });
  }
});

// Direct file serving route for PDFs to bypass CORS
app.get('/api/files/:directory/:filename', (req, res) => {
  try {
    const { directory, filename } = req.params;
    
    // Validate directory and filename to prevent directory traversal
    if (!directory || !filename || directory.includes('..') || filename.includes('..')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid directory or filename'
      });
    }
    
    // Check multiple possible locations
    const possiblePaths = [
      path.join(process.cwd(), 'public/uploads', directory, filename),
      path.join(process.cwd(), 'uploads', directory, filename)
    ];
    
    // Find the first path that exists
    const existingPath = possiblePaths.find(p => fs.existsSync(p));
    
    if (!existingPath) {
      return res.status(404).json({
        success: false,
        message: 'File not found',
        checkedPaths: possiblePaths
      });
    }
    
    // Set appropriate headers
    if (filename.endsWith('.pdf')) {
      res.setHeader('Content-Type', 'application/pdf');
    } else if (filename.endsWith('.jpg') || filename.endsWith('.jpeg')) {
      res.setHeader('Content-Type', 'image/jpeg');
    } else if (filename.endsWith('.png')) {
      res.setHeader('Content-Type', 'image/png');
    } else if (filename.endsWith('.docx')) {
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    }
    
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    
    // Serve the file
    console.log(`[Direct File] Serving file: ${existingPath}`);
    return res.sendFile(existingPath);
  } catch (error) {
    console.error('Error serving file:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error serving file',
      error
    });
  }
});

// Initialize database and start server
const startServer = async () => {
  try {
    // Initialize database connection
    await AppDataSource.initialize();
    console.log('Database connected successfully');

    // Initialize application services
    await initializeServices();
    
    // Create HTTP server
    const server = http.createServer(app);
    
    // Initialize Socket.IO
    const io = initializeSocketServer(server, corsOptions);
    
    // Set the socket instance for use throughout the application
    setSocketInstance(io);
    
    // Make io available to controllers via app.locals
    app.locals.io = io;
    
    // Start the server
    server.listen(PORT, async () => {
      logger.info(`Server is running on port ${PORT}`, { service: 'ims-api' });
      logger.info(`Socket.IO server is running on namespace /socket.io/`);
      
      // Create a test log entry
      try {
        const testLog = await SystemLogService.createLog({
          type: 'info',
          action: 'Server Started',
          user: 'System',
          details: `Server started successfully on port ${PORT} at ${new Date().toISOString()}`
        });
        console.log('Test log entry created successfully:', testLog);
      } catch (error) {
        console.error('Failed to create test log entry:', error);
      }
    });

    // Handle server errors
    server.on('error', (error: NodeJS.ErrnoException) => {
      if (error.code === 'EADDRINUSE') {
        logger.error(`Port ${PORT} is already in use. Please check if another instance is running or use a different port.`);
        
        // Try to close the current server
        server.close();
        
        // Try the next port as a fallback
        const nextPort = PORT + 1;
        logger.info(`Attempting to use port ${nextPort} instead...`);
        
        server.listen(nextPort, () => {
          logger.info(`Server is running on port ${nextPort} (fallback)`, { service: 'ims-api' });
          logger.info(`Please update your frontend configuration to use port ${nextPort} or set PORT=${nextPort} in your .env file`);
        });
      } else {
        logger.error('Server error:', error);
        process.exit(1);
      }
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

// At the start of your server code
const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'it-logs');
try {
  if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
    console.log('Created uploads directory:', uploadsDir);
  }
  
  // Test write access
  const testFile = path.join(uploadsDir, '.test-write-' + Date.now());
  fs.writeFileSync(testFile, 'test');
  fs.unlinkSync(testFile);
  console.log('Uploads directory is writable');
} catch (error) {
  console.error('Error with uploads directory:', error);
  console.error('This may cause file uploads to fail');
}

startServer();

// Graceful shutdown handling
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully...');
  await shutdownServices();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully...');
  await shutdownServices();
  process.exit(0);
});

export default app;