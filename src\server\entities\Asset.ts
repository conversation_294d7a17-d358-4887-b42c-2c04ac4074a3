import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('assets')
export class Asset {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'name', type: 'varchar', length: 100 })
  name: string;

  @Column({ name: 'type', type: 'varchar', length: 50 })
  type: string;

  @Column({ name: 'model', type: 'varchar', length: 100, nullable: true })
  model: string;

  @Column({ name: 'serial_number', type: 'varchar', length: 100, nullable: true })
  serial_number: string;

  @Column({ name: 'purchase_date', type: 'date', nullable: true })
  purchase_date: Date;

  @Column({ name: 'warranty_expiry', type: 'date', nullable: true })
  warranty_expiry: Date;

  @Column({ name: 'department', type: 'varchar', length: 100 })
  department: string;

  @Column({ name: 'location', type: 'varchar', length: 100, nullable: true })
  location: string;

  @Column({ name: 'assigned_to', type: 'varchar', length: 100, nullable: true })
  assigned_to: string;

  @Column({ name: 'status', type: 'varchar', length: 50, default: 'active' })
  status: string;

  @Column({ name: 'notes', type: 'text', nullable: true })
  notes: string;

  @CreateDateColumn({ name: 'created_at' })
  created_at: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updated_at: Date;
} 