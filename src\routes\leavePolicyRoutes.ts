import { Router } from 'express';
import { LeavePolicyController } from '../controllers/leavePolicyController';
import { requireAuth } from '../middleware/auth';
import { Request, Response } from 'express';

const router = Router();
const leavePolicyController = new LeavePolicyController();

// Apply authentication middleware to all routes
router.use(requireAuth);



// Policy Configuration Routes
router.get('/current', leavePolicyController.getCurrentPolicy.bind(leavePolicyController));
router.post('/', leavePolicyController.createPolicy.bind(leavePolicyController));
router.put('/:id', leavePolicyController.updatePolicy.bind(leavePolicyController));

// Auto-allocation from policies
router.post('/auto-allocate', leavePolicyController.autoAllocateFromPolicies.bind(leavePolicyController));

// Recalculate allocations for a specific leave type (useful when proration settings change)
router.post('/recalculate-allocations/:leaveType', async (req: Request, res: Response) => {
  try {
    const { leaveType } = req.params;
    const { year } = req.body;
    
    const { leaveAllocationService } = await import('../services/LeaveAllocationService');
    await leaveAllocationService.recalculateAllocationsForLeaveType(leaveType, year);
    
    res.json({
      success: true,
      message: `Successfully recalculated allocations for ${leaveType}`
    });
  } catch (error: any) {
    console.error('Error recalculating allocations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to recalculate allocations',
      error: error.message
    });
  }
});

// Leave Types Routes
router.get('/leave-types', leavePolicyController.getAllLeaveTypes.bind(leavePolicyController));
router.get('/leave-types/active', leavePolicyController.getActiveLeaveTypes.bind(leavePolicyController));
router.get('/leave-types/:id', leavePolicyController.getLeaveTypeById.bind(leavePolicyController));
router.post('/leave-types', leavePolicyController.createLeaveType.bind(leavePolicyController));
router.put('/leave-types/:id', leavePolicyController.updateLeaveType.bind(leavePolicyController));
router.delete('/leave-types/:id', leavePolicyController.deleteLeaveType.bind(leavePolicyController));



// Holiday Calendar Routes
router.get('/holiday-calendars', leavePolicyController.getAllHolidayCalendars.bind(leavePolicyController));
router.get('/holiday-calendars/:id', leavePolicyController.getHolidayCalendarById.bind(leavePolicyController));
router.post('/holiday-calendars', leavePolicyController.createHolidayCalendar.bind(leavePolicyController));
router.put('/holiday-calendars/:id', leavePolicyController.updateHolidayCalendar.bind(leavePolicyController));
router.delete('/holiday-calendars/:id', leavePolicyController.deleteHolidayCalendar.bind(leavePolicyController));

// Holiday Routes
router.get('/holiday-calendars/:calendarId/holidays', leavePolicyController.getHolidaysByCalendar.bind(leavePolicyController));
router.post('/holidays', leavePolicyController.createHoliday.bind(leavePolicyController));
router.put('/holidays/:id', leavePolicyController.updateHoliday.bind(leavePolicyController));
router.delete('/holidays/:id', leavePolicyController.deleteHoliday.bind(leavePolicyController));
router.put('/holidays/bulk', leavePolicyController.bulkUpdateHolidays.bind(leavePolicyController));

export default router; 