import { Request, Response, NextFunction, RequestHandler } from 'express';
import jwt from 'jsonwebtoken';
import { UserRole } from '../../types/common';
import { User } from '../../entities/User';
import { AppDataSource } from '../../config/database';

const userRepository = AppDataSource.getRepository(User);
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

export type AuthRequest = Request & {
  // Using 'any' here avoids conflicts between different User entity classes defined in shared and server directories.
  // We still maintain runtime safety by ensuring we attach the correct server User instance below.
  user?: any;
};

// Use Express's RequestHandler type so this middleware can be passed directly to router methods without type errors
export const auth: RequestHandler = async (req, res: Response, next: NextFunction) => {
  // Cast once so we can access strongly-typed user info inside the middleware body without affecting the external type signature
  const authReq = req as AuthRequest;
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      res.status(401).json({ error: 'Authorization token required' });
      return;
    }

    try {
      const decoded = jwt.verify(token, JWT_SECRET) as any;
      const userId = decoded.userId || decoded.id;
      
      if (!userId) {
        return res.status(401).json({ error: 'Invalid token: missing user ID' });
      }
      
      const user = await userRepository.findOne({ where: { id: userId } });
      
      if (!user) {
        return res.status(401).json({ error: 'User not found' });
      }
      
      if (!user.isActive) {
        return res.status(403).json({ error: 'Account is inactive' });
      }
      // Ensure user.role is of type UserRole
      user.role = user.role as UserRole;
      authReq.user = user;
      next();
    } catch (jwtError) {
      console.error('JWT verification failed:', jwtError);
      return res.status(401).json({ error: 'Invalid token' });
    }
  } catch (error) {
    console.error('Authentication middleware error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
};

// Optional: Add role-based authorization middleware
export const authorize = (roles: UserRole[]): RequestHandler =>
  async (req, res: Response, next: NextFunction) => {
    const authReq = req as AuthRequest;
    try {
      if (!authReq.user) {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      // IT_ADMIN always has access to everything
      if (authReq.user.role && (authReq.user.role === UserRole.IT_ADMIN || authReq.user.role === UserRole.ADMIN || authReq.user.role === UserRole.SYSTEM_ADMIN)) {
        next();
        return;
      }

      if (!authReq.user.role || !roles.includes(authReq.user.role as UserRole)) {
        // Check for fallback routes - bypass auth for fallback routes if database is down
        if (req.path.includes('/fallback')) {
          console.log('Bypassing authorization for fallback route:', req.path);
          next();
          return;
        }
        
        res.status(403).json({ error: 'Not authorized' });
        return;
      }

      next();
    } catch (error) {
      console.error('Authorization error:', error);
      // If there's any error during authorization, bypass for fallback routes
      if (req.path.includes('/fallback')) {
        console.log('Error during authorization, bypassing for fallback route:', req.path);
        next();
        return;
      }
      res.status(500).json({ error: 'Authorization error' });
      return;
    }
  };