import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddProratedLeaveSettings1703800000000 implements MigrationInterface {
  name = 'AddProratedLeaveSettings1703800000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if settings column exists, if not add it
    const table = await queryRunner.getTable('leave_type_policies');
    const settingsColumn = table?.findColumnByName('settings');
    
    if (!settingsColumn) {
      await queryRunner.addColumn('leave_type_policies', new TableColumn({
        name: 'settings',
        type: 'json',
        isNullable: true,
        default: "'{}'",
      }));
    }

    // Ensure all existing records have a valid settings JSON object
    await queryRunner.query(`
      UPDATE leave_type_policies 
      SET settings = JSON_OBJECT(
        'minDaysNotice', COALESCE(minDaysNotice, 0),
        'allowLeaveModification', false,
        'enableProratedLeave', false
      )
      WHERE settings IS NULL OR settings = '' OR JSON_VALID(settings) = 0
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // In rollback, we don't remove the settings column as it might contain other data
    // Just remove the prorated leave specific settings
    await queryRunner.query(`
      UPDATE leave_type_policies 
      SET settings = JSON_REMOVE(settings, '$.enableProratedLeave')
      WHERE JSON_EXTRACT(settings, '$.enableProratedLeave') IS NOT NULL
    `);
  }
} 