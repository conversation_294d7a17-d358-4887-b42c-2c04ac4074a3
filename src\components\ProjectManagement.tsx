import React from 'react';

interface ProjectManagementProps {
  currentView?: string;
}

function ProjectManagement({ currentView = 'dashboard' }: ProjectManagementProps) {
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">🎉 Project Management System</h1>
        <p className="text-gray-600 mb-6">The Project Management component is now working!</p>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-green-900 mb-2">✅ Success!</h3>
          <p className="text-green-700">The routing issue has been resolved and the component is rendering correctly.</p>
          <p className="text-green-700 mt-2">Current View: {currentView}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-900 mb-2">📊 Projects</h3>
            <p className="text-blue-700">Manage your projects</p>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h3 className="font-semibold text-green-900 mb-2">✅ Tasks</h3>
            <p className="text-green-700">Track your tasks</p>
          </div>

          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <h3 className="font-semibold text-purple-900 mb-2">👥 Team</h3>
            <p className="text-purple-700">Manage team members</p>
          </div>
        </div>

        <div className="mt-6">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            Create New Project
          </button>
        </div>
      </div>
    </div>
  );
}

export default ProjectManagement;
