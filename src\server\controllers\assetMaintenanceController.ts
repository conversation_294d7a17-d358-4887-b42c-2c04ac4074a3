import { Request, Response } from 'express';
import { assetMaintenanceRepository } from '../../repositories/assetMaintenanceRepository';
import { assetRepository } from '../../repositories/assetRepository';

export const assetMaintenanceController = {
  async getMaintenanceRecords(req: Request, res: Response) {
    try {
      const { 
        assetId, 
        maintenanceType, 
        status, 
        performedById, 
        startDate, 
        endDate,
        page, 
        limit 
      } = req.query;
      
      const result = await assetMaintenanceRepository.findAll({
        assetId: assetId ? Number(assetId) : undefined,
        maintenanceType: maintenanceType as string,
        status: status as string,
        performedById: performedById as string,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
        page: page ? parseInt(page as string) : undefined,
        limit: limit ? parseInt(limit as string) : undefined
      });
      
      res.json(result);
    } catch (error) {
      console.error('Error fetching maintenance records:', error);
      res.status(500).json({ error: 'Failed to fetch maintenance records' });
    }
  },
  
  async getMaintenanceById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const maintenance = await assetMaintenanceRepository.findById(id);
      
      if (!maintenance) {
        return res.status(404).json({ error: 'Maintenance record not found' });
      }
      
      res.json(maintenance);
    } catch (error) {
      console.error('Error fetching maintenance record:', error);
      res.status(500).json({ error: 'Failed to fetch maintenance record' });
    }
  },
  
  async getMaintenanceByAssetId(req: Request, res: Response) {
    try {
      const { assetId } = req.params;
      
      // Validate asset exists
      const asset = await assetRepository.findById(assetId);
      if (!asset) {
        return res.status(404).json({ error: 'Asset not found' });
      }
      
      // Get maintenance records for the asset
      const maintenanceRecords = await assetMaintenanceRepository.findByAssetId(Number(assetId));
      
      return res.status(200).json(maintenanceRecords);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  },
  
  async createMaintenance(req: Request, res: Response) {
    try {
      const {
        assetId,
        maintenanceType,
        maintenanceDate,
        description,
        cost,
        performedById,
        vendor,
        status,
        nextMaintenanceDate,
        notes,
        partsReplaced
      } = req.body;

      // Validate required fields
      const maintenanceData = {
        assetId: Number(assetId),
        maintenanceType,
        maintenanceDate,
        description,
        cost: parseFloat(cost),
        performedById,
        vendor,
        status,
        nextMaintenanceDate,
        notes,
        partsReplaced
      };
      
      // Check if asset exists
      const asset = await assetRepository.findById(String(maintenanceData.assetId));
      if (!asset) {
        return res.status(404).json({ error: 'Asset not found' });
      }
      
      // Create the maintenance record
      const maintenance = await assetMaintenanceRepository.create(maintenanceData);
      
      // Update the asset's last maintenance date and next maintenance date
      await assetRepository.update(String(maintenanceData.assetId), {
        lastMaintenance: maintenanceData.maintenanceDate ? new Date(maintenanceData.maintenanceDate) : undefined,
        nextMaintenance: maintenanceData.nextMaintenanceDate ? new Date(maintenanceData.nextMaintenanceDate) : undefined,
        maintenanceBy: maintenanceData.performedById || maintenanceData.vendor
      });
      
      res.status(201).json(maintenance);
    } catch (error) {
      console.error('Error creating maintenance record:', error);
      res.status(500).json({ error: 'Failed to create maintenance record' });
    }
  },
  
  async updateMaintenance(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const maintenanceData = req.body;
      
      // Check if maintenance record exists
      const existingMaintenance = await assetMaintenanceRepository.findById(id);
      if (!existingMaintenance) {
        return res.status(404).json({ error: 'Maintenance record not found' });
      }
      
      // Update the maintenance record
      const updatedMaintenance = await assetMaintenanceRepository.update(id, maintenanceData);
      
      // If this is the most recent maintenance for the asset, update the asset's maintenance info
      const assetId = existingMaintenance.assetId;
      const latestMaintenance = await assetMaintenanceRepository.findByAssetId(Number(assetId));
      
      if (latestMaintenance.length > 0 && latestMaintenance[0].id === id) {
        await assetRepository.update(String(assetId), {
          lastMaintenance: maintenanceData.maintenanceDate ? new Date(maintenanceData.maintenanceDate) : 
                           existingMaintenance.maintenanceDate ? new Date(existingMaintenance.maintenanceDate) : undefined,
          nextMaintenance: maintenanceData.nextMaintenanceDate ? new Date(maintenanceData.nextMaintenanceDate) : 
                           existingMaintenance.nextMaintenanceDate ? new Date(existingMaintenance.nextMaintenanceDate) : undefined,
          maintenanceBy: maintenanceData.performedById || maintenanceData.vendor || existingMaintenance.performedById || existingMaintenance.vendor
        });
      }
      
      res.json(updatedMaintenance);
    } catch (error) {
      console.error('Error updating maintenance record:', error);
      res.status(500).json({ error: 'Failed to update maintenance record' });
    }
  },
  
  async deleteMaintenance(req: Request, res: Response) {
    try {
      const { id } = req.params;
      
      // Check if maintenance record exists
      const existingMaintenance = await assetMaintenanceRepository.findById(id);
      if (!existingMaintenance) {
        return res.status(404).json({ error: 'Maintenance record not found' });
      }
      
      // Delete the maintenance record
      await assetMaintenanceRepository.delete(id);
      
      // If this was the most recent maintenance for the asset, update the asset's maintenance info
      const assetId = existingMaintenance.assetId;
      const remainingMaintenance = await assetMaintenanceRepository.findByAssetId(Number(assetId));
      
      if (remainingMaintenance.length > 0) {
        const latestMaintenance = remainingMaintenance[0];
        await assetRepository.update(String(assetId), {
          lastMaintenance: new Date(latestMaintenance.maintenanceDate),
          nextMaintenance: latestMaintenance.nextMaintenanceDate ? new Date(latestMaintenance.nextMaintenanceDate) : undefined,
          maintenanceBy: latestMaintenance.performedById || latestMaintenance.vendor
        });
      } else {
        // No maintenance records left, clear the asset's maintenance info
        await assetRepository.update(String(assetId), {
          lastMaintenance: undefined,
          nextMaintenance: undefined,
          maintenanceBy: undefined
        });
      }
      
      res.status(204).send();
    } catch (error) {
      console.error('Error deleting maintenance record:', error);
      res.status(500).json({ error: 'Failed to delete maintenance record' });
    }
  },
  
  async getMaintenanceStats(req: Request, res: Response) {
    try {
      const { assetId } = req.query;
      
      const stats = await assetMaintenanceRepository.getMaintenanceStats(assetId ? Number(assetId) : undefined);
      
      return res.status(200).json(stats);
    } catch (error: any) {
      return res.status(500).json({ error: error.message });
    }
  }
}; 