/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./public/index.html",
  ],
  darkMode: 'class', // or 'media' for OS preference based dark mode
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#1a2234',
          light: '#2a3446',
          dark: '#141b2d',
        },
        'primary-gradient-start': 'var(--primary-gradient-start)',
        'primary-gradient-end': 'var(--primary-gradient-end)',
        'secondary': 'var(--secondary-color)',
        'text-primary': 'var(--text-primary)',
        'text-secondary': 'var(--text-secondary)',
        'background-light': 'var(--background-light)',
        'card-background': 'var(--card-background)',
        'accent': 'var(--accent-color)',
        'tag-bg': 'var(--tag-background)',
        'tag-text': 'var(--tag-text)',
        'border': 'var(--border-color)',
      },
      animation: {
        'slide-down': 'slide-down 0.3s ease-out',
      },
      keyframes: {
        'slide-down': {
          '0%': { opacity: 0, transform: 'translateY(-10px)' },
          '100%': { opacity: 1, transform: 'translateY(0)' },
        },
      },
      backgroundImage: {
        'gradient-primary': 'linear-gradient(135deg, var(--primary-gradient-start), var(--primary-gradient-end))',
      },
    },
  },
  plugins: [],
};