import { AppDataSource } from '../config/database';

async function runMigrations() {
  console.log('Initializing data source...');
  
  try {
    await AppDataSource.initialize();
    console.log('Data source initialized');
    
    console.log('Running migrations...');
    const migrations = await AppDataSource.runMigrations();
    
    console.log(`Successfully ran ${migrations.length} migration(s):`);
    migrations.forEach(migration => {
      console.log(`- ${migration.name}`);
    });
    
    await AppDataSource.destroy();
    console.log('Connection closed');
    
    process.exit(0);
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  }
}

runMigrations(); 