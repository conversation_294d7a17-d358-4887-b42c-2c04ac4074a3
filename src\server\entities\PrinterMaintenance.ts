import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Asset } from './Asset';
import { Vendor } from './Vendor';
import { User } from './User';

@Entity('printer_maintenance')
export class PrinterMaintenance {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'date' })
  serviceDate: Date;

  @Column({ type: 'text' })
  serviceDescription: string;

  @Column({ type: 'json', nullable: true })
  partsReplaced: string | object;

  @Column({ type: 'varchar', length: 100, nullable: true })
  technician_name: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  invoiceAmount: number;

  @Column({ type: 'varchar', length: 50, nullable: true })
  invoiceNumber: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  invoiceFilePath: string;

  @Column({ type: 'varchar', length: 50, default: 'Pending' })
  approvalStatus: string;

  @Column({ type: 'varchar', length: 100 })
  department: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  location: string;

  @Column({ type: 'json', nullable: true })
  tonerReplaced: string | object;

  @Column({ type: 'tinyint', default: false })
  submittedToFinance: boolean;

  @Column({ type: 'date', nullable: true })
  submittedToFinanceDate: Date;

  @Column({ type: 'date', nullable: true })
  approvalDate: Date;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'varchar', length: 50, default: 'Completed' })
  serviceStatus: string;

  @Column({ type: 'date', nullable: true })
  nextServiceDate: Date;

  @ManyToOne(() => Asset, { eager: true, onDelete: 'CASCADE' })
  @JoinColumn({ name: 'assetId' })
  asset: Asset;

  @ManyToOne(() => Vendor, { eager: true })
  @JoinColumn({ name: 'vendorId' })
  vendor: Vendor;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'assigneeId' })
  assignee: User;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'approvedById' })
  approvedBy: User;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 