import React from 'react';
import {
  DollarSign,
  Edit,
  Trash2,
  ChevronRight,
  CheckCircle,
  AlertTriangle,
  Clock,
  AlertCircle,
  XCircle
} from 'lucide-react';
import { SalaryAdvance, AdvanceStatus } from '../../../types/payroll';

interface AdvancesTableProps {
  advances: SalaryAdvance[];
  isAdmin?: boolean;
  onEditAdvance?: (advance: SalaryAdvance) => void;
  onDeleteAdvance?: (advanceId: number) => void;
  onViewAdvance?: (advance: SalaryAdvance) => void;
}

const AdvancesTable: React.FC<AdvancesTableProps> = ({
  advances,
  isAdmin = false,
  onEditAdvance,
  onDeleteAdvance,
  onViewAdvance
}) => {
  // Function to format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'PKR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Function to format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Get status badge styles based on advance status
  const getStatusBadge = (status: AdvanceStatus) => {
    switch (status) {
      case AdvanceStatus.PENDING:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="mr-1 h-3 w-3" />
            Pending
          </span>
        );
      case AdvanceStatus.APPROVED:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="mr-1 h-3 w-3" />
            Approved
          </span>
        );
      case AdvanceStatus.PAID:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <DollarSign className="mr-1 h-3 w-3" />
            Paid
          </span>
        );
      case AdvanceStatus.RECOVERED:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
            <CheckCircle className="mr-1 h-3 w-3" />
            Recovered
          </span>
        );
      case AdvanceStatus.REJECTED:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircle className="mr-1 h-3 w-3" />
            Rejected
          </span>
        );
      case AdvanceStatus.CANCELED:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            <XCircle className="mr-1 h-3 w-3" />
            Canceled
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  // Get recovery method display text
  const getRecoveryMethodLabel = (method: string) => {
    switch (method) {
      case 'one_time':
        return 'One-time';
      case 'installments':
        return 'Installments';
      default:
        return method;
    }
  };

  if (!advances || advances.length === 0) {
    return (
      <div className="bg-white shadow overflow-hidden sm:rounded-md p-6 text-center">
        <DollarSign className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No salary advances</h3>
        <p className="mt-1 text-sm text-gray-500">
          There are no salary advances to display.
        </p>
        {isAdmin && (
          <div className="mt-6">
            <button
              type="button"
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              New Advance
            </button>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-md">
      <ul className="divide-y divide-gray-200">
        {advances.map((advance) => (
          <li key={advance.id}>
            <div 
              className="block hover:bg-gray-50 cursor-pointer"
              onClick={() => onViewAdvance && onViewAdvance(advance)}
            >
              <div className="px-4 py-4 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <p className="text-sm font-medium text-blue-600 truncate">
                      {advance.employeeName}
                    </p>
                    <p className="ml-2 text-sm text-gray-500">
                      ({getRecoveryMethodLabel(advance.recoveryMethod)})
                    </p>
                  </div>
                  <div className="ml-2 flex-shrink-0 flex">
                    {getStatusBadge(advance.status)}
                  </div>
                </div>
                <div className="mt-2 sm:flex sm:justify-between">
                  <div className="sm:flex">
                    <p className="flex items-center text-sm text-gray-500">
                      Amount: {formatCurrency(advance.amount)}
                    </p>
                    <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                      Remaining: {formatCurrency(advance.remainingAmount)}
                    </p>
                    <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                      Requested: {formatDate(advance.requestDate)}
                    </p>
                  </div>
                  <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                    {isAdmin && (
                      <div className="flex space-x-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onEditAdvance && onEditAdvance(advance);
                          }}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        {(advance.status === AdvanceStatus.PENDING || advance.status === AdvanceStatus.APPROVED) && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onDeleteAdvance && onDeleteAdvance(advance.id);
                            }}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    )}
                    <ChevronRight className="ml-2 h-5 w-5 text-gray-400" />
                  </div>
                </div>
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default AdvancesTable; 