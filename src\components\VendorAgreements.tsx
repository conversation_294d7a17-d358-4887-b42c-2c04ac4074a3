import React, { useState } from 'react';
import {
  FileText,
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock,
  Download,
  Plus,
  Search,
  Filter,
  MoreVertical
} from 'lucide-react';

interface Agreement {
  id: string;
  title: string;
  vendorName: string;
  type: string;
  status: 'active' | 'expired' | 'pending' | 'terminated';
  startDate: string;
  endDate: string;
  value: number;
  currency: string;
  complianceScore: number;
  documents: {
    name: string;
    type: string;
    url: string;
  }[];
  terms: string[];
  lastReviewed: string;
  nextReview: string;
}

export function VendorAgreements() {
  const [agreements] = useState<Agreement[]>([
    {
      id: 'AGR001',
      title: 'Annual IT Support Agreement',
      vendorName: 'Tech Solutions Inc',
      type: 'Service Level Agreement',
      status: 'active',
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      value: 120000,
      currency: 'PKR',
      complianceScore: 98,
      documents: [
        { name: 'SLA Document', type: 'PDF', url: '/documents/sla.pdf' },
        { name: 'Terms & Conditions', type: 'PDF', url: '/documents/terms.pdf' }
      ],
      terms: [
        '24/7 Technical Support',
        'Maximum 4-hour Response Time',
        '99.9% Uptime Guarantee',
        'Monthly Performance Reports'
      ],
      lastReviewed: '2024-02-15',
      nextReview: '2024-05-15'
    },
    {
      id: 'AGR002',
      title: 'Hardware Maintenance Contract',
      vendorName: 'Hardware Pro',
      type: 'Maintenance Agreement',
      status: 'active',
      startDate: '2024-02-01',
      endDate: '2025-01-31',
      value: 75000,
      currency: 'PKR',
      complianceScore: 95,
      documents: [
        { name: 'Maintenance Contract', type: 'PDF', url: '/documents/maintenance.pdf' },
        { name: 'Service Schedule', type: 'PDF', url: '/documents/schedule.pdf' }
      ],
      terms: [
        'Quarterly Maintenance Visits',
        'Parts Replacement Coverage',
        'Emergency Support within 24 hours',
        'Preventive Maintenance Schedule'
      ],
      lastReviewed: '2024-03-01',
      nextReview: '2024-06-01'
    }
  ]);

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [showAddForm, setShowAddForm] = useState(false);

  const getStatusColor = (status: Agreement['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'terminated':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with actions */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="flex flex-1 gap-4">
          <div className="relative flex-1 max-w-md">
            <input
              type="text"
              placeholder="Search agreements..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
          </div>
          <div className="relative">
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="appearance-none pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="expired">Expired</option>
              <option value="pending">Pending</option>
              <option value="terminated">Terminated</option>
            </select>
            <Filter className="absolute right-3 top-2.5 h-5 w-5 text-gray-400 pointer-events-none" />
          </div>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          <Plus className="h-5 w-5 mr-2" />
          Add Agreement
        </button>
      </div>

      {/* Agreements List */}
      <div className="grid gap-6">
        {agreements.map((agreement) => (
          <div
            key={agreement.id}
            className="bg-white rounded-lg shadow-sm overflow-hidden"
          >
            <div className="p-6">
              <div className="flex flex-col lg:flex-row lg:items-start justify-between gap-4">
                <div className="flex-1">
                  <div className="flex items-start gap-4">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <FileText className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{agreement.title}</h3>
                      <p className="text-sm text-gray-500">{agreement.vendorName}</p>
                      <div className="flex flex-wrap items-center gap-4 mt-2">
                        <span className={`px-2.5 py-0.5 text-xs font-medium rounded-full ${getStatusColor(agreement.status)}`}>
                          {agreement.status}
                        </span>
                        <div className="flex items-center gap-1 text-sm text-gray-500">
                          <Calendar className="h-4 w-4" />
                          <span>{agreement.startDate} - {agreement.endDate}</span>
                        </div>
                        <div className="flex items-center gap-1 text-sm text-gray-500">
                          <AlertCircle className="h-4 w-4" />
                          <span>Next Review: {agreement.nextReview}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm text-gray-500">Agreement Value</span>
                        <span className="text-sm font-medium text-gray-900">
                          {agreement.currency} {agreement.value.toLocaleString()}
                        </span>
                      </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm text-gray-500">Compliance Score</span>
                        <div className="flex items-center gap-1">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span className="text-sm font-medium text-gray-900">{agreement.complianceScore}%</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm text-gray-500">Last Review</span>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4 text-blue-500" />
                          <span className="text-sm font-medium text-gray-900">{agreement.lastReviewed}</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm text-gray-500">Documents</span>
                        <span className="text-sm font-medium text-gray-900">{agreement.documents.length} files</span>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Key Terms</h4>
                    <div className="flex flex-wrap gap-2">
                      {agreement.terms.map((term, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-gray-100 text-gray-600 text-sm rounded-full"
                        >
                          {term}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <button
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                    title="Download Documents"
                  >
                    <Download className="h-5 w-5" />
                  </button>
                  <button
                    className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                    title="More Options"
                  >
                    <MoreVertical className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 