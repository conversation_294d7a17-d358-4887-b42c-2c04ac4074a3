import React, { useState, useEffect } from 'react';
import { Mail, Search, Filter, Calendar, Users, FileText, Clock, ChevronDown, CheckCircle, XCircle, AlertCircle, Plus, Shield, LinkIcon, FileKey, Database, Building, User, Ticket, ArrowLeft, Trash2, Eye, MoreHorizontal, Info, MapPin, Laptop, EyeOff, ChevronUp, GitBranch, Share2, HardDrive, Download, Table } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { EmailForm } from './EmailForm';
import EmailAccountService from '../services/EmailAccountService';
import AssetSoftwareService from '../services/AssetSoftwareService';
import * as XLSX from 'xlsx';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import 'jspdf-autotable';
import { UserConfig } from 'jspdf-autotable';

// Add type declaration for jsPDF with autoTable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: typeof autoTable;
    internal: {
      pageSize: {
        width: number;
        height: number;
        getWidth: () => number;
        getHeight: () => number;
      };
      getNumberOfPages: () => number;
    };
  }
}

// Account type options
type AccountType = 'Personal' | 'Shared' | 'Group' | 'Alias' | 'System';

// Platform options
type EmailPlatform = 'Office365' | 'Gmail' | 'SiteGround' | 'Hostinger' | 'cPanel' | 'Exchange' | 'Other';

// Status options
type AccountStatus = 'Active' | 'Suspended' | 'Blocked' | 'Deleted' | 'Reassigned';

// View modes
type ViewMode = 'list' | 'create' | 'edit' | 'view';

interface EmailAccount {
  id: string;
  // Core Email Information
  emailAddress: string;
  assignedTo: {
    type: 'User' | 'Department';
    name: string;
    id?: string;
  };
  designation?: string;
  employmentStatus?: 'Active' | 'Terminated' | 'On Leave' | 'Contract';
  linkedSystems?: {
    system: 'CRM' | 'ERP' | 'Shared Inbox' | 'Other';
    details: string;
  }[];

  // Account Classification
  accountType: AccountType;
  platform: EmailPlatform;
  loginUrl?: string;
  hostingProvider?: string;

  // Security Information
  initialPassword?: string; // Encrypted
  lastPasswordUpdate?: string;
  recoveryEmail?: string;
  recoveryPhone?: string;
  twoFactorEnabled: boolean;
  secretQuestions?: {
    question: string;
    answer: string; // Encrypted
  }[];
  // Additional security fields
  password?: string;
  secretQuestion?: string;
  secretAnswer?: string;
  passwordAge?: number;

  // Lifecycle & Status Tracking
  createdAt: string;
  lastAccessDate?: string;
  status: AccountStatus;
  terminationReason?: string;
  changeHistory?: {
    date: string;
    changedBy: string;
    changes: {
      field: string;
      oldValue: string;
      newValue: string;
    }[];
  }[];
  creationDate?: string;
  ownershipChangeLog?: string;

  // Ownership & Responsibility
  primaryUser: string;
  createdBy: string;
  approvedBy?: string;
  ticketId?: string;
  alternateUsers?: string[];

  // Compliance & Documentation
  supportingDocs?: {
    name: string;
    url: string;
    type: string;
    uploadedAt: string;
  }[];
  remarks?: string;
  thirdPartyPlatforms?: {
    name: string;
    url?: string;
  }[];
  licenseId?: string;
  offboardingLinked: boolean;
  
  // Additional fields
  department: string;
  notes?: string;
  subscriptionType?: string;
  software?: string;
  asset?: string;
  licenseRecord?: string;
  userOffboardingProcess?: string;
  
  // Location information
  project?: string;
  location?: string;
}

export function EmailAccountManagement() {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedPlatform, setSelectedPlatform] = useState('all');
  const [selectedAccountType, setSelectedAccountType] = useState('all');
  const [selectedDateRange, setSelectedDateRange] = useState('7days');
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedAccount, setSelectedAccount] = useState<EmailAccount | null>(null);
  const [activeTab, setActiveTab] = useState('basic');
  const [emailAccounts, setEmailAccounts] = useState<EmailAccount[]>([]);
  const [formData, setFormData] = useState({
    emailAddress: '',
    assignedToType: 'Department',
    assignedToName: '',
    assignedToId: '',
    designation: '',
    employmentStatus: '',
    department: '',
    accountType: '',
    platform: '',
    status: '',
    loginUrl: '',
    hostingProvider: '',
    recoveryEmail: '',
    recoveryPhone: '',
    twoFactorEnabled: false,
    primaryUser: '',
    createdBy: 'IT Admin',
    notes: '',
    subscriptionType: '',
    software: '',
    asset: '',
    password: '',
    secretQuestion: '',
    secretAnswer: '',
    creationDate: '',
    lastAccessDate: '',
    ownershipChangeLog: '',
    passwordAge: 0,
    ticketId: '',
    licenseRecord: '',
    userOffboardingProcess: '',
    project: '',
    location: ''
  });
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [accountToDelete, setAccountToDelete] = useState<EmailAccount | null>(null);
  const [assetOptions, setAssetOptions] = useState<{ value: string; label: string }[]>([]);
  const [softwareOptions, setSoftwareOptions] = useState<{ value: string; label: string }[]>([]);
  const [expandedCardId, setExpandedCardId] = useState<string | null>(null);
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [downloadFormat, setDownloadFormat] = useState<'excel' | 'pdf'>('excel');
  const [downloadDateRange, setDownloadDateRange] = useState('all');
  const [downloadStatus, setDownloadStatus] = useState('all');
  const [downloadPlatform, setDownloadPlatform] = useState('all');
  const [downloadType, setDownloadType] = useState('all');
  const [downloadLocation, setDownloadLocation] = useState('all');
  const [downloadProject, setDownloadProject] = useState('all');
  const [isDownloading, setIsDownloading] = useState(false);

  // Load accounts and related options on component mount
  useEffect(() => {
    loadEmailAccounts();
    loadAssetOptions();
    loadSoftwareOptions();
  }, []);

  // Load email accounts from service
  const loadEmailAccounts = async () => {
    try {
      console.log('Loading email accounts');
      const response = await EmailAccountService.getAllAccounts();
      
      console.log('Email accounts response:', response);
      
      if (response.error) {
        console.error('Error loading email accounts:', response.error);
        // Display empty state instead of crashing
        setEmailAccounts([]);
        return;
      }
      
      if (Array.isArray(response.data)) {
        setEmailAccounts(response.data);
      } else {
        console.error('Invalid response format for email accounts:', response.data);
        setEmailAccounts([]);
      }
    } catch (error) {
      console.error('Error loading email accounts:', error);
      setEmailAccounts([]);
    }
  };

  // Function to load asset options
  const loadAssetOptions = async () => {
    try {
      // getAssetOptions returns the array directly
      const options = await AssetSoftwareService.getAssetOptions(); 
      if (!Array.isArray(options)) { // Add a check to ensure it's an array
         console.error('Received non-array data for asset options:', options);
         setAssetOptions([]); // Set empty array on unexpected format
         return;
      }
      setAssetOptions(options);
    } catch (error) {
      console.error('Failed to load asset options:', error);
      setAssetOptions([]); // Ensure state is an empty array on error
    }
  };

  // Function to load software options
  const loadSoftwareOptions = async () => {
    try {
      // getSoftwareOptions returns the array directly
      const options = await AssetSoftwareService.getSoftwareOptions(); 
       if (!Array.isArray(options)) { // Add a check to ensure it's an array
         console.error('Received non-array data for software options:', options);
         setSoftwareOptions([]); // Set empty array on unexpected format
         return;
      }
      setSoftwareOptions(options);
    } catch (error) {
      console.error('Failed to load software options:', error);
      setSoftwareOptions([]); // Ensure state is an empty array on error
    }
  };

  // Check URL to determine view mode on component mount and URL change
  useEffect(() => {
    const pathParts = location.pathname.split('/');
    const lastPath = pathParts[pathParts.length - 1];
    
    if (lastPath === 'create') {
      handleCreateAccount();
    } else if (lastPath === 'edit' && location.state?.accountId) {
      const accountToEdit = emailAccounts.find(acc => acc.id === location.state.accountId);
      if (accountToEdit) {
        handleEditAccount(accountToEdit);
      } else {
        navigate('/email-management/accounts');
      }
    } else {
      setViewMode('list');
    }
  }, [location.pathname]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  // Adjusted Status Color Logic for potentially better consistency
  const getStatusColorClasses = (status: string) => {
    switch (status) {
      case 'Active':
        return { 
          bar: 'bg-green-500', 
          dot: 'bg-green-500', 
          badgeText: 'text-green-700 dark:text-green-300', 
          badgeBg: 'bg-green-100 dark:bg-green-900/30' 
        };
      case 'Suspended':
        return { 
          bar: 'bg-amber-500', 
          dot: 'bg-amber-500', 
          badgeText: 'text-amber-700 dark:text-amber-300', 
          badgeBg: 'bg-amber-100 dark:bg-amber-900/30' 
        };
      case 'Blocked':
      case 'Deleted':
        return { 
          bar: 'bg-red-500', 
          dot: 'bg-red-500', 
          badgeText: 'text-red-700 dark:text-red-300', 
          badgeBg: 'bg-red-100 dark:bg-red-900/30' 
        };
      case 'Reassigned':
        return { 
          bar: 'bg-blue-500', 
          dot: 'bg-blue-500', 
          badgeText: 'text-blue-700 dark:text-blue-300', 
          badgeBg: 'bg-blue-100 dark:bg-blue-900/30' 
        };
      default:
        return { 
          bar: 'bg-gray-400', 
          dot: 'bg-gray-400', 
          badgeText: 'text-gray-700 dark:text-gray-300', 
          badgeBg: 'bg-gray-100 dark:bg-gray-700' 
        };
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Active':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'Suspended':
      case 'Blocked':
      case 'Deleted':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'Reassigned':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const handleCreateAccount = () => {
    setSelectedAccount(null);
    setFormData({
      emailAddress: '',
      assignedToType: 'Department',
      assignedToName: '',
      assignedToId: '',
      designation: '',
      employmentStatus: '',
      department: '',
      accountType: '',
      platform: '',
      status: '',
      loginUrl: '',
      hostingProvider: '',
      recoveryEmail: '',
      recoveryPhone: '',
      twoFactorEnabled: false,
      primaryUser: '',
      createdBy: 'IT Admin',
      notes: '',
      subscriptionType: '',
      software: '',
      asset: '',
      password: '',
      secretQuestion: '',
      secretAnswer: '',
      creationDate: '',
      lastAccessDate: '',
      ownershipChangeLog: '',
      passwordAge: 0,
      ticketId: '',
      licenseRecord: '',
      userOffboardingProcess: '',
      project: '',
      location: ''
    });
    setViewMode('create');
    setActiveTab('basic');
    // Update URL without triggering the useEffect again
    window.history.pushState(null, '', '/email-management/accounts/create');
  };

  const handleEditAccount = (account: EmailAccount) => {
    setSelectedAccount(account);
    
    // Log raw data to help with debugging
    console.log('Raw account data for edit:', account);
    
    // Create a complete form data object with all necessary fields and proper defaults
    const updatedFormData = {
      emailAddress: account.emailAddress || '',
      assignedToType: account.assignedTo?.type || 'Department',
      assignedToName: account.assignedTo?.name || '',
      assignedToId: account.assignedTo?.id || '',
      designation: account.designation || '',
      employmentStatus: account.employmentStatus || '',
      department: account.department || account.assignedTo?.name || '',
      accountType: account.accountType || 'Personal',
      platform: account.platform || 'Office365',
      status: account.status || 'Active',
      loginUrl: account.loginUrl || '',
      hostingProvider: account.hostingProvider || '',
      recoveryEmail: account.recoveryEmail || '',
      recoveryPhone: account.recoveryPhone || '',
      twoFactorEnabled: account.twoFactorEnabled || false,
      primaryUser: account.primaryUser || '',
      createdBy: account.createdBy || 'IT Admin',
      notes: account.notes || '',
      
      // Additional fields that may have been missed
      subscriptionType: account.subscriptionType || '',
      software: account.software || '',
      asset: account.asset || '',
      password: account.password || '',
      secretQuestion: account.secretQuestion || '',
      secretAnswer: account.secretAnswer || '',
      creationDate: account.creationDate || account.createdAt || '',
      lastAccessDate: account.lastAccessDate || '',
      ownershipChangeLog: account.ownershipChangeLog || '',
      passwordAge: account.passwordAge || 0,
      ticketId: account.ticketId || '',
      licenseRecord: account.licenseRecord || '',
      userOffboardingProcess: account.userOffboardingProcess || '',
      project: account.project || '',
      location: account.location || ''
    };
    
    console.log('Setting form data for edit:', updatedFormData);
    setFormData(updatedFormData);
    
    setViewMode('edit');
    setActiveTab('basic');
    // Update URL without triggering the useEffect again
    window.history.pushState({ accountId: account.id }, '', '/email-management/accounts/edit');
  };

  const handleBackToList = () => {
    setViewMode('list');
    navigate('/email-management/accounts');
    // Reload accounts from service when returning to list
    loadEmailAccounts();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle account creation/update logic here
    handleBackToList();
  };

  const handleToggleStatus = async (accountId: string, currentStatus: string) => {
    // Ensure the new status is properly typed as an AccountStatus
    const newStatus = currentStatus === 'Active' ? 'Suspended' : 'Active' as AccountStatus;
    
    // Find the account to update
    const accountToUpdate = emailAccounts.find(acc => acc.id === accountId);
    if (!accountToUpdate) {
      console.error('Account not found:', accountId);
      return;
    }
    
    try {
      console.log(`Toggling account ${accountId} status from ${currentStatus} to ${newStatus}`);
      
      // Create the updated account data
      const updatedAccountData = {
        id: accountId,
        emailAddress: accountToUpdate.emailAddress,
        assignedToType: accountToUpdate.assignedTo.type,
        assignedToName: accountToUpdate.assignedTo.name,
        assignedToId: accountToUpdate.assignedTo.id,
        designation: accountToUpdate.designation,
        employmentStatus: accountToUpdate.employmentStatus,
        department: accountToUpdate.department,
        accountType: accountToUpdate.accountType,
        platform: accountToUpdate.platform,
        status: newStatus, // Set the new status
        loginUrl: accountToUpdate.loginUrl,
        hostingProvider: accountToUpdate.hostingProvider,
        recoveryEmail: accountToUpdate.recoveryEmail,
        recoveryPhone: accountToUpdate.recoveryPhone,
        twoFactorEnabled: accountToUpdate.twoFactorEnabled,
        primaryUser: accountToUpdate.primaryUser,
        createdBy: accountToUpdate.createdBy,
        notes: accountToUpdate.notes,
        // Additional fields
        subscriptionType: accountToUpdate.subscriptionType,
        software: accountToUpdate.software,
        asset: accountToUpdate.asset,
        password: accountToUpdate.password,
        secretQuestion: accountToUpdate.secretQuestion,
        secretAnswer: accountToUpdate.secretAnswer,
        creationDate: accountToUpdate.creationDate,
        lastAccessDate: accountToUpdate.lastAccessDate,
        ownershipChangeLog: accountToUpdate.ownershipChangeLog,
        passwordAge: accountToUpdate.passwordAge,
        ticketId: accountToUpdate.ticketId,
        licenseRecord: accountToUpdate.licenseRecord,
        userOffboardingProcess: accountToUpdate.userOffboardingProcess
      };
      
      // Call the API to update the status
      const result = await EmailAccountService.updateAccount(accountId, updatedAccountData);
      
      if (result.error) {
        console.error('Error updating account status:', result.error);
        return;
      }
      
      console.log('Account status updated successfully');
      
      // Update the local state to reflect the change
      const updatedAccounts = emailAccounts.map(account => {
        if (account.id === accountId) {
          return {
            ...account,
            status: newStatus
          };
        }
        return account;
      });
      
      setEmailAccounts(updatedAccounts);
    } catch (error) {
      console.error('Error toggling account status:', error);
    }
  };

  // Handle opening the download modal
  const handleOpenDownloadModal = () => {
    setShowDownloadModal(true);
  };

  // Handle closing the download modal
  const handleCloseDownloadModal = () => {
    setShowDownloadModal(false);
  };

  // Handle downloading the email accounts
  const handleDownload = async () => {
    try {
      setIsDownloading(true);
      
      // Filter accounts based on selected options
      const filteredAccounts = emailAccounts.filter(account => {
        if (downloadStatus !== 'all' && account.status !== downloadStatus) return false;
        if (downloadPlatform !== 'all' && account.platform !== downloadPlatform) return false;
        if (downloadType !== 'all' && account.accountType !== downloadType) return false;
        if (downloadLocation !== 'all' && account.location !== downloadLocation) return false;
        if (downloadProject !== 'all' && account.project !== downloadProject) return false;
        
        // Apply date filtering if necessary
        if (downloadDateRange !== 'all') {
          const accountDate = new Date(account.createdAt);
          const now = new Date();
          
          switch (downloadDateRange) {
            case '7days':
              const sevenDaysAgo = new Date(now.setDate(now.getDate() - 7));
              return accountDate >= sevenDaysAgo;
            case '30days':
              const thirtyDaysAgo = new Date(now.setDate(now.getDate() - 30));
              return accountDate >= thirtyDaysAgo;
            case '90days': 
              const ninetyDaysAgo = new Date(now.setDate(now.getDate() - 90));
              return accountDate >= ninetyDaysAgo;
            default:
              return true;
          }
        }
        
        return true;
      });

      if (downloadFormat === 'excel') {
        // Prepare data for Excel export
        const excelData = filteredAccounts.map(account => ({
          'Email Address': account.emailAddress,
          'Status': account.status,
          'Account Type': account.accountType,
          'Platform': account.platform,
          'Assigned To': account.assignedTo.name,
          'Department': account.department,
          'Primary User': account.primaryUser || '-',
          'Created Date': new Date(account.createdAt).toLocaleDateString(),
          'Last Access': account.lastAccessDate ? new Date(account.lastAccessDate).toLocaleDateString() : '-',
          '2FA Enabled': account.twoFactorEnabled ? 'Yes' : 'No',
          'Location': account.location || '-',
          'Project': account.project || '-',
          'Recovery Email': account.recoveryEmail || '-',
          'Recovery Phone': account.recoveryPhone || '-',
          'Hosting Provider': account.hostingProvider || '-',
          'Login URL': account.loginUrl || '-',
          'Ticket ID': account.ticketId || '-',
          'Software': account.software || '-',
          'Asset': account.asset || '-',
          'Notes': account.notes || '-'
        }));

        // Create workbook and worksheet
        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(excelData);

        // Add column widths
        const columnWidths = Object.keys(excelData[0] || {}).map(() => ({ wch: 20 }));
        ws['!cols'] = columnWidths;

        // Add the worksheet to the workbook
        XLSX.utils.book_append_sheet(wb, ws, 'Email Accounts');

        // Generate Excel file
        const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
        const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        
        // Create download link and trigger download
        const fileName = `email_accounts_${new Date().toISOString().split('T')[0]}.xlsx`;
        if (window.navigator.msSaveBlob) {
          // For IE
          window.navigator.msSaveBlob(data, fileName);
        } else {
          // For other browsers
          const url = window.URL.createObjectURL(data);
          const link = document.createElement('a');
          link.href = url;
          link.download = fileName;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
        }
      } else if (downloadFormat === 'pdf') {
        try {
          // Create new PDF document in landscape mode
          const doc = new jsPDF({
            orientation: 'landscape',
            unit: 'mm',
            format: 'a4'
          });
          
          // Add title
          doc.setFontSize(24);
          doc.setTextColor(41, 128, 185);
          doc.text('Email Accounts Report', 15, 20);

          // Add metadata
          doc.setFontSize(10);
          doc.setTextColor(100);
          doc.text(`Generated: ${new Date().toLocaleString()}`, 15, 30);
          
          // Add filter summary
          let yPos = 35;
          const activeFilters = [];
          if (downloadStatus !== 'all') activeFilters.push(`Status: ${downloadStatus}`);
          if (downloadPlatform !== 'all') activeFilters.push(`Platform: ${downloadPlatform}`);
          if (downloadType !== 'all') activeFilters.push(`Type: ${downloadType}`);
          if (downloadLocation !== 'all') activeFilters.push(`Location: ${downloadLocation}`);
          if (downloadProject !== 'all') activeFilters.push(`Project: ${downloadProject}`);
          
          if (activeFilters.length > 0) {
            doc.text('Applied Filters:', 15, yPos);
            doc.setTextColor(120);
            doc.text(activeFilters.join(' | '), 15, yPos + 5);
            yPos += 10;
          }

          // Add summary statistics
          const totalAccounts = filteredAccounts.length;
          const activeAccounts = filteredAccounts.filter(acc => acc.status === 'Active').length;
          const suspendedAccounts = filteredAccounts.filter(acc => acc.status === 'Suspended' || acc.status === 'Blocked').length;
          
          doc.setFontSize(11);
          doc.setTextColor(70);
          doc.text(`Total Accounts: ${totalAccounts}`, 15, yPos + 5);
          doc.text(`Active: ${activeAccounts}`, 15, yPos + 10);
          doc.text(`Suspended/Blocked: ${suspendedAccounts}`, 15, yPos + 15);

          // Add table using autoTable
          (doc as any).autoTable({
            startY: yPos + 20,
            head: [[
              'Email Address',
              'Type',
              'Platform',
              'Department',
              'Primary User',
              'Project',
              'Location'
            ]],
            body: filteredAccounts.map(account => [
              account.emailAddress,
              account.accountType,
              account.platform,
              account.department || '-',
              account.primaryUser || '-',
              account.project || '-',
              account.location || '-'
            ]),
            theme: 'grid',
            styles: {
              fontSize: 9,
              cellPadding: 3
            },
            headStyles: {
              fillColor: [41, 128, 185],
              textColor: 255,
              fontStyle: 'bold'
            },
            alternateRowStyles: {
              fillColor: [249, 250, 251]
            }
          });

          // Save the PDF
          const fileName = `email_accounts_${new Date().toISOString().split('T')[0]}.pdf`;
          doc.save(fileName);

          // Show success message
          setTimeout(() => {
            setIsDownloading(false);
            setShowDownloadModal(false);
            alert(`Successfully downloaded PDF file with ${filteredAccounts.length} email accounts.`);
          }, 1000);
          
        } catch (error) {
          console.error('Error generating PDF:', error);
          setIsDownloading(false);
          alert('Failed to generate PDF. Please try again.');
        }
      }

      // Close modal and show success message
      setTimeout(() => {
        setIsDownloading(false);
        setShowDownloadModal(false);
        
        // Show success message
        alert(`Successfully downloaded ${downloadFormat.toUpperCase()} file with ${filteredAccounts.length} email accounts.`);
      }, 1000);
      
    } catch (error) {
      console.error('Error downloading email accounts:', error);
      setIsDownloading(false);
      alert('Failed to download email accounts. Please try again.');
    }
  };

  // Modified view handler to toggle expansion
  const handleToggleExpand = (accountId: string) => {
    setExpandedCardId(prevId => prevId === accountId ? null : accountId);
    // Prevent navigation if just toggling view within the list
    if (viewMode === 'list') {
       // Optional: could scroll to the card, but keep it simple for now
    } else {
      // If not in list view, perform the original view action
       const accountToView = emailAccounts.find(acc => acc.id === accountId);
       if (accountToView) {
          handleViewAccount(accountToView); // Call the original full view handler
       }
    }
  };

  // Original handleViewAccount for navigating to full page view (if needed later or from other contexts)
  const handleViewAccount = (account: EmailAccount) => {
    setSelectedAccount(account);
    // Ensure the correct form data structure is used for setting state
    const updatedFormData = {
      emailAddress: account.emailAddress || '',
      assignedToType: account.assignedTo?.type || 'Department',
      assignedToName: account.assignedTo?.name || '',
      assignedToId: account.assignedTo?.id || '',
      designation: account.designation || '',
      employmentStatus: account.employmentStatus || '',
      department: account.department || account.assignedTo?.name || '',
      accountType: account.accountType || 'Personal',
      platform: account.platform || 'Office365',
      status: account.status || 'Active',
      loginUrl: account.loginUrl || '',
      hostingProvider: account.hostingProvider || '',
      recoveryEmail: account.recoveryEmail || '',
      recoveryPhone: account.recoveryPhone || '',
      twoFactorEnabled: account.twoFactorEnabled || false,
      primaryUser: account.primaryUser || '',
      createdBy: account.createdBy || 'IT Admin',
      notes: account.notes || '',
      subscriptionType: account.subscriptionType || '',
      software: account.software || '',
      asset: account.asset || '',
      password: account.password || '',
      secretQuestion: account.secretQuestion || '',
      secretAnswer: account.secretAnswer || '',
      creationDate: account.creationDate || account.createdAt || '',
      lastAccessDate: account.lastAccessDate || '',
      ownershipChangeLog: account.ownershipChangeLog || '',
      passwordAge: account.passwordAge || 0,
      ticketId: account.ticketId || '',
      licenseRecord: account.licenseRecord || '',
      userOffboardingProcess: account.userOffboardingProcess || '',
      project: account.project || '',
      location: account.location || ''
    };
    setFormData(updatedFormData); // Correctly set the form data state
    setViewMode('view');
    navigate(`/email-management/accounts/view/${account.id}`); 
  };
  
  // Handle delete account
  const handleDeleteClick = (account: EmailAccount) => {
    setAccountToDelete(account);
    setShowDeleteModal(true);
  };
  
  const confirmDelete = async () => {
    if (!accountToDelete) return;
    
    try {
      const result = await EmailAccountService.deleteAccount(accountToDelete.id);
      
      if (result.error) {
        console.error('Error deleting account:', result.error);
        return;
      }
      
      console.log('Account deleted successfully');
      
      // Update the local state to remove the deleted account
      setEmailAccounts(emailAccounts.filter(account => account.id !== accountToDelete.id));
      setShowDeleteModal(false);
      setAccountToDelete(null);
    } catch (error) {
      console.error('Error deleting account:', error);
    }
  };
  
  const cancelDelete = () => {
    setShowDeleteModal(false);
    setAccountToDelete(null);
  };

  // Helper function to get the name from options based on ID
  const getOptionLabel = (id: string | undefined | null, options: { value: string; label: string }[]): string => {
    if (!id) return '-';
    const option = options.find(opt => opt.value === id);
    return option ? option.label : id; // Fallback to ID if not found
  };

  // Function to render the action buttons for a card
  const renderCardActions = (account: EmailAccount) => (
    <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-all duration-200">
      {/* Toggle Status Button */}
      <button
        onClick={() => handleToggleStatus(account.id, account.status)}
        className={`p-2 rounded-lg transition-all duration-200 transform hover:scale-110 ${
          account.status === 'Active' 
            ? 'text-red-600 bg-red-100 hover:bg-red-200 dark:text-red-400 dark:bg-red-900/40 dark:hover:bg-red-900/60' 
            : 'text-green-600 bg-green-100 hover:bg-green-200 dark:text-green-400 dark:bg-green-900/40 dark:hover:bg-green-900/60'
        }`}
        title={account.status === 'Active' ? 'Deactivate Account' : 'Activate Account'}
      >
        {account.status === 'Active' ? <XCircle className="h-5 w-5" /> : <CheckCircle className="h-5 w-5" />}
      </button>
      {/* View/Expand Button */}
      <button
        onClick={() => handleToggleExpand(account.id)} // Use the toggle handler
        className="p-2 rounded-lg text-blue-600 bg-blue-100 hover:bg-blue-200 dark:text-blue-400 dark:bg-blue-900/40 dark:hover:bg-blue-900/60 transition-all duration-200 transform hover:scale-110"
        title={expandedCardId === account.id ? "Collapse Details" : "View Details"}
      >
        {expandedCardId === account.id ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />} 
      </button>
      {/* Edit Button */}
      <button
        onClick={() => handleEditAccount(account)} // Navigate to edit page
        className="p-2 rounded-lg text-purple-600 bg-purple-100 hover:bg-purple-200 dark:text-purple-400 dark:bg-purple-900/40 dark:hover:bg-purple-900/60 transition-all duration-200 transform hover:scale-110"
        title="Edit Account"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
          <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
        </svg>
      </button>
      {/* Delete Button */}
      <button
        onClick={() => handleDeleteClick(account)} // Show delete modal
        className="p-2 rounded-lg text-red-600 bg-red-100 hover:bg-red-200 dark:text-red-400 dark:bg-red-900/40 dark:hover:bg-red-900/60 transition-all duration-200 transform hover:scale-110"
        title="Delete Account"
      >
        <Trash2 className="h-5 w-5" />
      </button>
    </div>
  );

  // Render the accounts list view
  const renderAccountsList = () => {
    // Calculate statistics
    const totalAccounts = emailAccounts.length;
    const activeAccounts = emailAccounts.filter(acc => acc.status === 'Active').length;
    const suspendedOrBlocked = emailAccounts.filter(acc => acc.status === 'Suspended' || acc.status === 'Blocked').length;
    const sharedOrGroup = emailAccounts.filter(acc => acc.accountType === 'Shared' || acc.accountType === 'Group').length;
    const office365Accounts = emailAccounts.filter(acc => acc.platform === 'Office365').length;
    const gmailAccounts = emailAccounts.filter(acc => acc.platform === 'Gmail').length;
    const linkedToAssets = emailAccounts.filter(acc => !!acc.asset && acc.asset !== 'None').length;

    return (
      <>
        {/* Statistics Summary - Updated with 6 cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-6">
          {/* Total Accounts */}
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 transition-all duration-200 hover:shadow-md hover:-translate-y-1 hover:border-blue-300 dark:hover:border-blue-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Total Accounts</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">{totalAccounts}</p>
              </div>
              <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30 transition-all duration-200 group-hover:bg-blue-200 dark:group-hover:bg-blue-800/50">
                <Mail className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </div>
          
          {/* Active Accounts */}
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 transition-all duration-200 hover:shadow-md hover:-translate-y-1 hover:border-green-300 dark:hover:border-green-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Active Accounts</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">{activeAccounts}</p>
              </div>
              <div className="p-2 rounded-full bg-green-100 dark:bg-green-900/30 transition-all duration-200 group-hover:bg-green-200 dark:group-hover:bg-green-800/50">
                 <CheckCircle className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </div>

          {/* Suspended/Blocked */}
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 transition-all duration-200 hover:shadow-md hover:-translate-y-1 hover:border-red-300 dark:hover:border-red-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Suspended/Blocked</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">{suspendedOrBlocked}</p>
              </div>
               <div className="p-2 rounded-full bg-red-100 dark:bg-red-900/30 transition-all duration-200 group-hover:bg-red-200 dark:group-hover:bg-red-800/50">
                 <XCircle className="h-6 w-6 text-red-500" />
              </div>
            </div>
          </div>

          {/* Shared/Group */}
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 transition-all duration-200 hover:shadow-md hover:-translate-y-1 hover:border-purple-300 dark:hover:border-purple-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Shared/Group</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">{sharedOrGroup}</p>
              </div>
              <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900/30 transition-all duration-200 group-hover:bg-purple-200 dark:group-hover:bg-purple-800/50">
                <Users className="h-6 w-6 text-purple-500" />
              </div>
            </div>
          </div>
          
          {/* Office 365 Accounts */}
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 transition-all duration-200 hover:shadow-md hover:-translate-y-1 hover:border-sky-300 dark:hover:border-sky-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Office 365</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">{office365Accounts}</p>
              </div>
               <div className="p-2 rounded-full bg-sky-100 dark:bg-sky-900/30 transition-all duration-200 group-hover:bg-sky-200 dark:group-hover:bg-sky-800/50">
                 <Database className="h-6 w-6 text-sky-500" />
              </div>
            </div>
          </div>

          {/* Gmail Accounts */}
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 transition-all duration-200 hover:shadow-md hover:-translate-y-1 hover:border-rose-300 dark:hover:border-rose-700">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Gmail</p>
                <p className="text-2xl font-semibold text-gray-900 dark:text-white">{gmailAccounts}</p>
              </div>
              <div className="p-2 rounded-full bg-rose-100 dark:bg-rose-900/30 transition-all duration-200 group-hover:bg-rose-200 dark:group-hover:bg-rose-800/50">
                 <Mail className="h-6 w-6 text-rose-500" /> 
              </div>
            </div>
          </div>       
        </div>

        <div className="mb-6 flex flex-col md:flex-row justify-between items-center">
          <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Email Account Management</h1>
        <p className="text-gray-600 dark:text-gray-400">Centralized system for tracking and managing organization-wide email accounts</p>
          </div>
          <div className="flex gap-3 mt-4 md:mt-0">
            <button
              onClick={handleOpenDownloadModal}
              className="flex items-center justify-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200"
              title="Download Email Accounts"
            >
              <Download className="h-5 w-5" />
              <span>Download</span>
            </button>
            <button
              onClick={handleCreateAccount}
              className="flex items-center justify-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200"
            >
              <Plus className="h-5 w-5" />
              <span>New Email Account</span>
            </button>
          </div>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex-1">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search email address, user, or department..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5 text-gray-400" />
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Status</option>
            <option value="Active">Active</option>
            <option value="Suspended">Suspended</option>
            <option value="Blocked">Blocked</option>
            <option value="Deleted">Deleted</option>
            <option value="Reassigned">Reassigned</option>
          </select>
        </div>
        <div className="flex items-center gap-2">
          <Database className="h-5 w-5 text-gray-400" />
          <select
            value={selectedPlatform}
            onChange={(e) => setSelectedPlatform(e.target.value)}
            className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Platforms</option>
            <option value="Office365">Office 365</option>
            <option value="Gmail">Gmail</option>
              <option value="SiteGround">SiteGround</option>
              <option value="Hostinger">Hostinger</option>
            <option value="cPanel">cPanel</option>
            <option value="Exchange">Exchange</option>
            <option value="Other">Other</option>
          </select>
        </div>
        <div className="flex items-center gap-2">
          <User className="h-5 w-5 text-gray-400" />
          <select
            value={selectedAccountType}
            onChange={(e) => setSelectedAccountType(e.target.value)}
            className="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Types</option>
            <option value="Personal">Personal</option>
            <option value="Shared">Shared</option>
            <option value="Group">Group</option>
            <option value="Alias">Alias</option>
            <option value="System">System</option>
          </select>
        </div>
      </div>

      {/* Email Account List */}
        <div className="bg-transparent dark:bg-gray-900 rounded-lg mb-6">
          <div className="space-y-4">
            {emailAccounts.length === 0 ? (
              <div className="text-center py-8">
                <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No email accounts found</h3>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  Get started by creating your first email account
                </p>
                <button
                  onClick={handleCreateAccount}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <Plus className="h-5 w-5 mr-2" />
                  New Email Account
                </button>
              </div>
            ) : (
              emailAccounts
                .filter(account => {
                  // Apply filters
                  if (selectedStatus !== 'all' && account.status !== selectedStatus) return false;
                  if (selectedPlatform !== 'all' && account.platform !== selectedPlatform) return false;
                  if (selectedAccountType !== 'all' && account.accountType !== selectedAccountType) return false;
                  
                  // Apply search
                  if (searchQuery) {
                    const query = searchQuery.toLowerCase();
                    return (
                      account.emailAddress.toLowerCase().includes(query) ||
                      account.assignedTo.name.toLowerCase().includes(query) ||
                      account.department?.toLowerCase().includes(query) ||
                      (account.primaryUser && account.primaryUser.toLowerCase().includes(query))
                    );
                  }
                  
                  return true;
                })
                .map((account) => {
                  const softwareName = getOptionLabel(account.software, softwareOptions);
                  const assetName = getOptionLabel(account.asset, assetOptions);
                  const statusColors = getStatusColorClasses(account.status);
                  
                  return (
                    <div key={account.id} className="bg-white dark:bg-gray-850 overflow-hidden rounded-xl shadow-sm border border-gray-200 dark:border-gray-700/50 transition-all duration-300 hover:shadow-md hover:border-gray-300 dark:hover:border-gray-500 group">
                      {/* Card Header */}
                      <div className="relative">
                        {/* Status Bar */}
                        <div className={`absolute top-0 left-0 w-1.5 h-full rounded-l-xl ${statusColors.bar} transition-all duration-300 group-hover:w-2`}></div>
                        
                        {/* Header Content */}
                        <div className="flex items-center justify-between border-b border-gray-100 dark:border-gray-700/50 bg-gradient-to-r from-gray-50 to-gray-100/50 dark:from-gray-800 dark:to-gray-800/50 p-3 pl-6 cursor-pointer transition-colors duration-200 hover:bg-gray-100/80 dark:hover:bg-gray-750/80" onClick={() => handleToggleExpand(account.id)}>
                          {/* Left Side Content */}
                          <div className="flex items-center gap-3 flex-1 min-w-0">
                            {/* Platform Icon and Status Dot */}
                            <div className="relative flex-shrink-0">
                              <div className={`p-2 rounded-full ${
                                account.platform === 'Office365' ? 'bg-blue-100 dark:bg-blue-900/40' :
                                account.platform === 'Gmail' ? 'bg-red-100 dark:bg-red-900/40' :
                                account.platform === 'Exchange' ? 'bg-indigo-100 dark:bg-indigo-900/40' :
                                'bg-gray-100 dark:bg-gray-700'
                              }`}>
                                <Mail className={`h-5 w-5 ${
                                  account.platform === 'Office365' ? 'text-blue-600 dark:text-blue-400' :
                                  account.platform === 'Gmail' ? 'text-red-600 dark:text-red-400' :
                                  account.platform === 'Exchange' ? 'text-indigo-600 dark:text-indigo-400' :
                                  'text-gray-600 dark:text-gray-400'
                                }`} />
                              </div>
                              <div className={`absolute -top-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white dark:border-gray-800 ${statusColors.dot}`}></div>
                            </div>

                            {/* Email Info */}
                            <div className="min-w-0">
                              <div className="flex items-center gap-2">
                                <h3 className="font-semibold text-gray-800 dark:text-white truncate" title={account.emailAddress}>
                                  {account.emailAddress}
                                </h3>
                                {account.twoFactorEnabled && (
                                  <span className="text-green-500 dark:text-green-400 flex-shrink-0" title="2FA Enabled">
                                    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M9 12L11 14L15 10M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                  </span>
                                )}
                              </div>
                              <div className="flex items-center flex-wrap gap-x-3 gap-y-1 mt-1 text-xs text-gray-500 dark:text-gray-400">
                                <span className="inline-flex items-center">
                                  <Database className="h-3 w-3 mr-1 text-gray-400" />
                                  {account.platform}
                                </span>
                                <span className="inline-flex items-center">
                                  <User className="h-3 w-3 mr-1 text-gray-400" />
                                  {account.accountType}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Right Side Actions */}
                          <div className="flex items-center">
                            <div className="flex-shrink-0 ml-4 hidden md:flex" onClick={(e) => e.stopPropagation()}>
                              {renderCardActions(account)}
                            </div>
                            <div className="flex-shrink-0 ml-2">
                              <button 
                                className="p-2 rounded-lg text-gray-500 hover:bg-gray-200 dark:text-gray-400 dark:hover:bg-gray-700" 
                                aria-expanded={expandedCardId === account.id}
                              >
                                {expandedCardId === account.id ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                      {/* Collapsible Card Body Wrapper */}
                      <div 
                        className={`transition-all duration-300 ease-in-out overflow-hidden ${expandedCardId === account.id ? 'max-h-[1000px] opacity-100 visible' : 'max-h-0 opacity-0 invisible'}`}
                        aria-hidden={expandedCardId !== account.id}
                      >
                        {/* Inner padding and border only when expanded */}
                        <div className="border-t border-gray-100 dark:border-gray-700/50">
                          {/* Card Body Grid */}
                          <div className="p-4 grid grid-cols-2 md:grid-cols-4 gap-y-4 gap-x-4">
                            {/* Assignment Section */}
                            <div className="col-span-2 md:col-span-1">
                              <div className="flex items-center">
                                <span className={`w-8 h-8 flex items-center justify-center rounded-lg mr-3 ${
                                  account.assignedTo.type === 'User' 
                                    ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400' 
                                    : 'bg-indigo-100 text-indigo-600 dark:bg-indigo-900/30 dark:text-indigo-400'
                                }`}>
                                  {account.assignedTo.type === 'User' ? <User className="h-4 w-4" /> : <Building className="h-4 w-4" />}
                                </span>
                                <div>
                                  <span className="text-xs text-gray-500 dark:text-gray-400 block leading-tight">Assigned To</span>
                                  <span className="text-sm font-medium text-gray-800 dark:text-gray-100">{account.assignedTo.name}</span>
                      </div>
                              </div>
                            </div>
                            {/* Primary User */}
                            <div className="col-span-2 md:col-span-1">
                              <div className="flex items-center">
                                <span className="w-8 h-8 flex items-center justify-center rounded-lg bg-emerald-100 text-emerald-600 dark:bg-emerald-900/30 dark:text-emerald-400 mr-3">
                                  <svg className="h-4 w-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16 7C16 9.20914 14.2091 11 12 11C9.79086 11 8 9.20914 8 7C8 4.79086 9.79086 3 12 3C14.2091 3 16 4.79086 16 7Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/><path d="M12 14C8.13401 14 5 17.134 5 21H19C19 17.134 15.866 14 12 14Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                                </span>
                                <div>
                                  <span className="text-xs text-gray-500 dark:text-gray-400 block leading-tight">Primary User</span>
                                  <span className="text-sm font-medium text-gray-800 dark:text-gray-100">{account.primaryUser || '-'}</span>
                                </div>
                              </div>
                            </div>
                            {/* Creation Date */}
                            <div>
                              <div className="flex items-center">
                                <span className="w-8 h-8 flex items-center justify-center rounded-lg bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400 mr-3">
                                  <Calendar className="h-4 w-4" />
                                </span>
                                <div>
                                  <span className="text-xs text-gray-500 dark:text-gray-400 block leading-tight">Created</span>
                                  <span className="text-sm font-medium text-gray-800 dark:text-gray-100">{new Date(account.createdAt).toLocaleDateString()}</span>
                                </div>
                              </div>
                            </div>
                            {/* Last Access */}
                            <div>
                              <div className="flex items-center">
                                <span className="w-8 h-8 flex items-center justify-center rounded-lg bg-amber-100 text-amber-600 dark:bg-amber-900/30 dark:text-amber-400 mr-3">
                          <Clock className="h-4 w-4" />
                                </span>
                                <div>
                                  <span className="text-xs text-gray-500 dark:text-gray-400 block leading-tight">Last Access</span>
                                  <span className="text-sm font-medium text-gray-800 dark:text-gray-100">{account.lastAccessDate ? new Date(account.lastAccessDate).toLocaleDateString() : '-'}</span>
                        </div>
                              </div>
                            </div>
                            {/* Security Status */}
                            <div className="col-span-2">
                              <div className="flex items-center">
                                 <span className={`w-8 h-8 flex items-center justify-center rounded-lg mr-3 ${
                                  account.twoFactorEnabled 
                                    ? 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400' 
                                    : 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400'
                                 }`}>
                                  <Shield className="h-4 w-4" />
                                </span>
                                <div>
                                  <span className="text-xs text-gray-500 dark:text-gray-400 block leading-tight">Security</span>
                                  <div className="flex items-center flex-wrap gap-2 mt-1">
                                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${account.twoFactorEnabled
                                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                                      : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' }`}>
                                      {account.twoFactorEnabled ? '2FA Enabled' : 'No 2FA'}
                                    </span>
                                    {account.passwordAge !== undefined && (
                                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                                        account.passwordAge > 90 ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                                        account.passwordAge > 60 ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300' :
                                        'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                                      }`}>
                                        Pass Age: {account.passwordAge}d
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                            {/* Location Information */}
                            {(account.project || account.location) && (
                              <div className="col-span-2">
                                <div className="flex items-center">
                                  <span className="w-8 h-8 flex items-center justify-center rounded-lg bg-cyan-100 text-cyan-600 dark:bg-cyan-900/30 dark:text-cyan-400 mr-3">
                                    <MapPin className="h-4 w-4" />
                                  </span>
                                  <div>
                                    <span className="text-xs text-gray-500 dark:text-gray-400 block leading-tight">Location</span>
                                    <span className="text-sm font-medium text-gray-800 dark:text-gray-100">
                                      {account.project && <span>{account.project}</span>}
                                      {account.project && account.location && <span className="mx-1 text-gray-400">•</span>}
                                      {account.location && <span>{account.location}</span>}
                                      {!account.project && !account.location && '-'}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                          
                          {/* References Section */}
                          {(account.ticketId || account.software || account.asset) && (
                            <div className="mt-4 pt-4 border-t border-gray-100 dark:border-gray-700/50 px-4 pb-3">
                              <span className="text-xs font-medium text-gray-500 dark:text-gray-400 block mb-2">References</span>
                              <div className="flex flex-wrap gap-2">
                      {account.ticketId && (
                                  <div className="inline-flex items-center px-2.5 py-1 rounded-full bg-purple-100 dark:bg-purple-900/30">
                                    <Ticket className="h-3.5 w-3.5 text-purple-500 mr-1.5" />
                                    <span className="text-xs font-medium text-purple-700 dark:text-purple-300">{account.ticketId}</span>
                        </div>
                      )}
                                {account.software && softwareName && (
                                  <div className="inline-flex items-center px-2.5 py-1 rounded-full bg-blue-100 dark:bg-blue-900/30">
                                    <svg className="h-3.5 w-3.5 text-blue-500 mr-1.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M15 2H9a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2Z"></path><path d="M9 22h6"></path><path d="M8 6h8"></path><path d="M8 10h8"></path><path d="M8 14h8"></path><path d="M8 18h8"></path></svg>
                                    <span className="text-xs font-medium text-blue-700 dark:text-blue-300">{softwareName}</span>
                      </div>
                                )}
                                {account.asset && assetName && (
                                  <div className="inline-flex items-center px-2.5 py-1 rounded-full bg-green-100 dark:bg-green-900/30">
                                    <Laptop className="h-3.5 w-3.5 text-green-500 mr-1.5" />
                                    <span className="text-xs font-medium text-green-700 dark:text-green-300">{assetName}</span>
                    </div>
                                )}
                  </div>
                            </div>
            )}
          </div>
                      </div>
                    </div>
                  )
                })
            )}
        </div>
      </div>
    </>
  );
  };

  // Render the account form
  const renderAccountForm = () => (
    <EmailForm
      initialData={selectedAccount ? {
        id: selectedAccount.id,
        emailAddress: formData.emailAddress,
        assignedToType: formData.assignedToType as 'User' | 'Department',
        assignedToName: formData.assignedToName,
        assignedToId: formData.assignedToId,
        designation: formData.designation,
        department: formData.department,
        accountType: formData.accountType,
        platform: formData.platform,
        status: formData.status,
        loginUrl: formData.loginUrl,
        hostingProvider: formData.hostingProvider,
        recoveryEmail: formData.recoveryEmail,
        recoveryPhone: formData.recoveryPhone,
        twoFactorEnabled: formData.twoFactorEnabled,
        primaryUser: formData.primaryUser,
        createdBy: formData.createdBy,
        notes: formData.notes,
        subscriptionType: formData.subscriptionType,
        software: formData.software,
        asset: formData.asset,
        password: formData.password,
        secretQuestion: formData.secretQuestion,
        secretAnswer: formData.secretAnswer,
        creationDate: formData.creationDate,
        lastAccessDate: formData.lastAccessDate,
        ownershipChangeLog: formData.ownershipChangeLog,
        passwordAge: formData.passwordAge,
        ticketId: formData.ticketId,
        licenseRecord: formData.licenseRecord,
        userOffboardingProcess: formData.userOffboardingProcess,
        project: formData.project,
        location: formData.location
      } : undefined}
      mode={viewMode === 'create' ? 'create' : viewMode === 'view' ? 'view' : 'edit'}
      onClose={handleBackToList}
    />
  );

  // Render the delete confirmation modal
  const renderDeleteModal = () => {
    if (!showDeleteModal || !accountToDelete) return null;
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full p-6">
          <div className="text-center mb-4">
            <div className="inline-flex items-center justify-center h-12 w-12 rounded-full bg-red-100 text-red-500 mb-4">
              <Trash2 className="h-6 w-6" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Delete Email Account</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
              Are you sure you want to delete <span className="font-semibold">{accountToDelete.emailAddress}</span>? 
              This action cannot be undone.
            </p>
          </div>
          
          <div className="flex justify-end gap-3 mt-6">
            <button
              onClick={cancelDelete}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              onClick={confirmDelete}
              className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
            >
              Delete
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Get unique locations and projects for dropdown options
  const getUniqueLocations = () => {
    const locations = emailAccounts
      .map(account => account.location)
      .filter((location): location is string => !!location);
    return [...new Set(locations)];
  };

  const getUniqueProjects = () => {
    const projects = emailAccounts
      .map(account => account.project)
      .filter((project): project is string => !!project);
    return [...new Set(projects)];
  };

  // Render the download modal
  const renderDownloadModal = () => {
    if (!showDownloadModal) return null;
    
    const uniqueLocations = getUniqueLocations();
    const uniqueProjects = getUniqueProjects();
    
  return (
      <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-3xl w-full p-0 overflow-hidden">
          {/* Header */}
          <div className="bg-gray-50 dark:bg-gray-750 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
            <div className="flex items-center">
              <div className="p-2 rounded-full bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400 mr-3">
                <Download className="h-6 w-6" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Download Email Accounts</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Export email accounts as Excel or PDF with custom filters
                </p>
              </div>
            </div>
          </div>
          
    <div className="p-6">
            {/* Format Selection */}
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Export Format</h4>
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => setDownloadFormat('excel')}
                  className={`flex items-center p-4 rounded-lg border-2 transition-all ${
                    downloadFormat === 'excel' 
                      ? 'border-green-500 bg-green-50 dark:bg-green-900/20' 
                      : 'border-gray-200 dark:border-gray-700 hover:border-green-300 dark:hover:border-green-700'
                  }`}
                >
                  <Table className={`h-7 w-7 mr-3 ${downloadFormat === 'excel' ? 'text-green-500' : 'text-gray-500 dark:text-gray-400'}`} />
                  <div className="text-left">
                    <span className={`block font-medium ${downloadFormat === 'excel' ? 'text-green-700 dark:text-green-400' : 'text-gray-700 dark:text-gray-300'}`}>Excel Format</span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">Detailed spreadsheet format</span>
                  </div>
                </button>
                
                <button
                  onClick={() => setDownloadFormat('pdf')}
                  className={`flex items-center p-4 rounded-lg border-2 transition-all ${
                    downloadFormat === 'pdf' 
                      ? 'border-green-500 bg-green-50 dark:bg-green-900/20' 
                      : 'border-gray-200 dark:border-gray-700 hover:border-green-300 dark:hover:border-green-700'
                  }`}
                >
                  <FileText className={`h-7 w-7 mr-3 ${downloadFormat === 'pdf' ? 'text-green-500' : 'text-gray-500 dark:text-gray-400'}`} />
                  <div className="text-left">
                    <span className={`block font-medium ${downloadFormat === 'pdf' ? 'text-green-700 dark:text-green-400' : 'text-gray-700 dark:text-gray-300'}`}>PDF Format</span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">Printable document format</span>
                  </div>
                </button>
              </div>
            </div>
            
            {/* Filter Options */}
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Filter Options</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Account Status</label>
                <select
                  value={downloadStatus}
                  onChange={(e) => setDownloadStatus(e.target.value)}
                  className="block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                >
                  <option value="all">All Statuses</option>
                  <option value="Active">Active</option>
                  <option value="Suspended">Suspended</option>
                  <option value="Blocked">Blocked</option>
                  <option value="Deleted">Deleted</option>
                  <option value="Reassigned">Reassigned</option>
                </select>
              </div>
              
              <div>
                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Platform</label>
                <select
                  value={downloadPlatform}
                  onChange={(e) => setDownloadPlatform(e.target.value)}
                  className="block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                >
                  <option value="all">All Platforms</option>
                  <option value="Office365">Office 365</option>
                  <option value="Gmail">Gmail</option>
                  <option value="SiteGround">SiteGround</option>
                  <option value="Hostinger">Hostinger</option>
                  <option value="cPanel">cPanel</option>
                  <option value="Exchange">Exchange</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              
              <div>
                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Account Type</label>
                <select
                  value={downloadType}
                  onChange={(e) => setDownloadType(e.target.value)}
                  className="block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                >
                  <option value="all">All Types</option>
                  <option value="Personal">Personal</option>
                  <option value="Shared">Shared</option>
                  <option value="Group">Group</option>
                  <option value="Alias">Alias</option>
                  <option value="System">System</option>
                </select>
              </div>
              
              <div>
                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Date Range</label>
                <select
                  value={downloadDateRange}
                  onChange={(e) => setDownloadDateRange(e.target.value)}
                  className="block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                >
                  <option value="all">All Time</option>
                  <option value="7days">Last 7 Days</option>
                  <option value="30days">Last 30 Days</option>
                  <option value="90days">Last 90 Days</option>
                </select>
              </div>
              
              <div>
                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Location</label>
                <select
                  value={downloadLocation}
                  onChange={(e) => setDownloadLocation(e.target.value)}
                  className="block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                >
                  <option value="all">All Locations</option>
                  {uniqueLocations.map(location => (
                    <option key={location} value={location}>{location}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Project</label>
                <select
                  value={downloadProject}
                  onChange={(e) => setDownloadProject(e.target.value)}
                  className="block w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                >
                  <option value="all">All Projects</option>
                  {uniqueProjects.map(project => (
                    <option key={project} value={project}>{project}</option>
                  ))}
                </select>
              </div>
            </div>
            
            {/* Summary */}
            <div className="bg-gray-50 dark:bg-gray-750/50 rounded-lg p-3 mb-6 border border-gray-200 dark:border-gray-700 text-sm">
              <div className="flex items-center text-gray-700 dark:text-gray-300">
                <Info className="h-4 w-4 mr-2 text-blue-500" />
                <span>Export will include all records matching your selected filters. Data will be masked according to your permission level.</span>
              </div>
            </div>
          </div>
          
          {/* Footer */}
          <div className="bg-gray-50 dark:bg-gray-750 border-t border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-end gap-3">
            <button
              onClick={handleCloseDownloadModal}
              className="px-4 py-2 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-650 transition-colors shadow-sm"
            >
              Cancel
            </button>
            <button
              onClick={handleDownload}
              disabled={isDownloading}
              className={`px-6 py-2 bg-green-500 text-white rounded-lg flex items-center gap-2 transition-colors shadow-sm ${isDownloading ? 'opacity-75 cursor-not-allowed' : 'hover:bg-green-600'}`}
            >
              {isDownloading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </>
              ) : (
                <>
                  <Download className="h-5 w-5" />
                  Download
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Main render function
  return (
    <div className="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      {viewMode === 'list' && renderAccountsList()}
      {(viewMode === 'create' || viewMode === 'edit' || viewMode === 'view') && renderAccountForm()}
      {renderDeleteModal()}
      {renderDownloadModal()}
    </div>
  );
} 