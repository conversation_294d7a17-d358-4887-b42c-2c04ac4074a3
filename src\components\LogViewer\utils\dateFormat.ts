/**
 * Utility functions for date formatting in the local time zone
 */

/**
 * Format a date string directly using browser's local timezone
 * 
 * @param dateString The date string to format
 * @returns The formatted date in local timezone
 */
export const formatSystemDate = (dateString: string): string => {
  try {
    // Parse the date string
    const date = new Date(dateString);
    
    // Check if valid date
    if (isNaN(date.getTime())) {
      return dateString;
    }
    
    // Format using browser's locale settings
    const options: Intl.DateTimeFormatOptions = { 
      year: 'numeric', 
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    };
    
    // Format the date with the browser's locale and timezone
    return new Intl.DateTimeFormat(undefined, options).format(date);
  } catch (e) {
    return dateString;
  }
};

/**
 * Format date for detailed display with timezone info
 */
export const formatDetailedSystemDate = (dateString: string): string => {
  try {
    // Parse the date string
    const date = new Date(dateString);
    
    // Check if valid date
    if (isNaN(date.getTime())) {
      return dateString;
    }
    
    // Format with timezone
    const options: Intl.DateTimeFormatOptions = { 
      year: 'numeric', 
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true,
      timeZoneName: 'short'
    };
    
    return new Intl.DateTimeFormat(undefined, options).format(date);
  } catch (e) {
    return dateString;
  }
};

/**
 * Format date string for UI display in short format (M/D/YYYY HH:MM:SS AM/PM)
 * without leading zeros on month/day and without timezone indicator
 */
export const formatShortDisplayDate = (dateString: string): { date: string, time: string } => {
  try {
    // Parse the date string
    const date = new Date(dateString);
    
    // Check if valid date
    if (isNaN(date.getTime())) {
      return { date: dateString, time: '' };
    }
    
    // Format date without leading zeros
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const year = date.getFullYear();
    const dateStr = `${month}/${day}/${year}`;
    
    // Format time with AM/PM
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    const period = hours >= 12 ? 'PM' : 'AM';
    const hours12 = hours % 12 || 12; // Convert 0 to 12 for 12 AM
    const timeStr = `${hours12.toString().padStart(2, '0')}:${minutes}:${seconds} ${period}`;
    
    return { date: dateStr, time: timeStr };
  } catch (e) {
    // On error, return original
    return { 
      date: dateString,
      time: ''
    };
  }
};

/**
 * Format date for a stacked vertical display with date on top and time below
 * Returns an object with top (date) and bottom (time) properties
 */
export const formatStackedDate = (dateString: string): { top: string, bottom: string } => {
  try {
    // Parse the date string
    const date = new Date(dateString);
    
    // Check if valid date
    if (isNaN(date.getTime())) {
      // Handle raw ISO format as fallback (like 2025-05-15T14:09:17.991Z)
      if (typeof dateString === 'string' && 
          dateString.includes('T') && 
          dateString.includes('Z')) {
        // Split the ISO string into date and time parts
        const parts = dateString.split('T');
        const datePart = parts[0];
        
        // Format time as HH:MM:SS without milliseconds
        let timePart = parts[1].replace('Z', '');
        if (timePart.includes('.')) {
          timePart = timePart.split('.')[0];
        }
        
        return {
          top: datePart,
          bottom: timePart
        };
      }
      
      return { top: dateString, bottom: '' };
    }
    
    // Format date
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const dateStr = `${year}-${month}-${day}`;
    
    // Format time
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    const timeStr = `${hours}:${minutes}:${seconds}`;
    
    return {
      top: dateStr,
      bottom: timeStr
    };
  } catch (e) {
    // Split the string if possible
    if (typeof dateString === 'string' && dateString.includes(' ')) {
      const parts = dateString.split(' ');
      return {
        top: parts[0],
        bottom: parts.slice(1).join(' ')
      };
    }
    
    return {
      top: dateString,
      bottom: ''
    };
  }
};

/**
 * Format a date for display with only date (no time)
 */
export const formatSystemDateOnly = (dateString: string): string => {
  try {
    // Parse the date string
    const date = new Date(dateString);
    
    // Check if valid date
    if (isNaN(date.getTime())) {
      return dateString;
    }
    
    // Format date only
    const options: Intl.DateTimeFormatOptions = { 
      year: 'numeric', 
      month: '2-digit',
      day: '2-digit'
    };
    
    return new Intl.DateTimeFormat(undefined, options).format(date);
  } catch (e) {
    return dateString;
  }
};

/**
 * Format a date for display with only time (no date)
 */
export const formatSystemTimeOnly = (dateString: string): string => {
  try {
    // Parse the date string
    const date = new Date(dateString);
    
    // Check if valid date
    if (isNaN(date.getTime())) {
      return dateString;
    }
    
    // Format time only
    const options: Intl.DateTimeFormatOptions = { 
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    };
    
    return new Intl.DateTimeFormat(undefined, options).format(date);
  } catch (e) {
    return dateString;
  }
};

/**
 * Get the system timezone name
 */
export const getSystemTimeZone = (): string => {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
};

/**
 * Get the system timezone offset as a formatted string
 */
export const getSystemTimeZoneOffset = (): string => {
  const date = new Date();
  const offset = -date.getTimezoneOffset() / 60;
  const sign = offset >= 0 ? '+' : '-';
  const hours = Math.abs(Math.floor(offset)).toString().padStart(2, '0');
  const minutes = (Math.abs(offset) % 1 * 60).toString().padStart(2, '0');
  return `${sign}${hours}:${minutes}`;
}; 