-- First drop the foreign key if it exists
SET @dbname = DATABASE();
SET @tablename = "attachments";
SET @constraintname = "fk_attachment_comment";

SELECT COUNT(*) INTO @exists FROM information_schema.table_constraints 
WHERE table_schema = DATABASE() 
AND table_name = 'attachments' 
AND constraint_name = 'fk_attachment_comment';

SET @sqlstmt = IF(@exists > 0,
    CONCAT('ALTER TABLE attachments DROP FOREIGN KEY ', @constraintname),
    'SELECT 1');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add commentId column if it doesn't exist
SET @columnname = "commentId";
SET @preparedStatement = (SELECT IF(
  (
    SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
    WHERE
      TABLE_SCHEMA = @dbname
      AND TABLE_NAME = @tablename
      AND COLUMN_NAME = @columnname
  ) > 0,
  "SELECT 1",
  "ALTER TABLE attachments ADD COLUMN commentId INT NULL"
));
PREPARE alterIfNotExists FROM @preparedStatement;
EXECUTE alterIfNotExists;
DEALLOCATE PREPARE alterIfNotExists;

-- Add index if it doesn't exist (after dropping the foreign key)
SELECT COUNT(*) INTO @exists FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name = 'attachments' 
AND index_name = 'idx_comment_attachments';

SET @sqlstmt = IF(@exists > 0, 'SELECT 1', 'CREATE INDEX idx_comment_attachments ON attachments(commentId)');
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add foreign key constraint
SET @sqlstmt = 'ALTER TABLE attachments ADD CONSTRAINT fk_attachment_comment FOREIGN KEY (commentId) REFERENCES comments(id) ON DELETE CASCADE ON UPDATE CASCADE';
PREPARE stmt FROM @sqlstmt;
EXECUTE stmt;
DEALLOCATE PREPARE stmt; 