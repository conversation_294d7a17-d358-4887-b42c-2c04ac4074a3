import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Grid,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Publish as PublishIcon,
  Close as CloseIcon,
  MoreVert as MoreVertIcon,
  Search as SearchIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { recruitmentAPI } from '../../../../services/recruitmentAPI';

interface JobPosting {
  id: number;
  title: string;
  department: string;
  location: string;
  jobType: string;
  experienceLevel: string;
  workLocation: string;
  status: string;
  isActive: boolean;
  isUrgent: boolean;
  isFeatured: boolean;
  applicationCount: number;
  viewCount: number;
  createdAt: string;
  applicationDeadline?: string;
  createdBy: {
    id: string;
    firstName: string;
    lastName: string;
  };
  hiringManager?: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

const JobPostingsList: React.FC = () => {
  const navigate = useNavigate();
  const [jobPostings, setJobPostings] = useState<JobPosting[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedPosting, setSelectedPosting] = useState<JobPosting | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    fetchJobPostings();
  }, [page, rowsPerPage, searchTerm, statusFilter, departmentFilter]);

  const fetchJobPostings = async () => {
    try {
      setLoading(true);
      const params = {
        page: page + 1,
        limit: rowsPerPage,
        search: searchTerm || undefined,
        status: statusFilter || undefined,
        department: departmentFilter || undefined,
        sortBy: 'createdAt',
        sortOrder: 'DESC'
      };

      const response = await recruitmentAPI.getJobPostings(params);
      setJobPostings(response.data.jobPostings);
      setTotal(response.data.pagination.total);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch job postings');
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, posting: JobPosting) => {
    setAnchorEl(event.currentTarget);
    setSelectedPosting(posting);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedPosting(null);
  };

  const handlePublish = async (posting: JobPosting) => {
    try {
      setActionLoading(true);
      await recruitmentAPI.publishJobPosting(posting.id);
      await fetchJobPostings();
      handleMenuClose();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to publish job posting');
    } finally {
      setActionLoading(false);
    }
  };

  const handleClose = async (posting: JobPosting) => {
    try {
      setActionLoading(true);
      await recruitmentAPI.closeJobPosting(posting.id);
      await fetchJobPostings();
      handleMenuClose();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to close job posting');
    } finally {
      setActionLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!selectedPosting) return;

    try {
      setActionLoading(true);
      await recruitmentAPI.deleteJobPosting(selectedPosting.id);
      await fetchJobPostings();
      setDeleteDialogOpen(false);
      handleMenuClose();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to delete job posting');
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    const statusColors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      draft: 'default',
      published: 'success',
      paused: 'warning',
      closed: 'error',
      cancelled: 'error'
    };
    return statusColors[status] || 'default';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const isExpired = (deadline?: string) => {
    if (!deadline) return false;
    return new Date(deadline) < new Date();
  };

  if (loading && jobPostings.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Job Postings
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => navigate('/hr/recruitment/job-postings/new')}
        >
          Create Job Posting
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  label="Status"
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <MenuItem value="">All Statuses</MenuItem>
                  <MenuItem value="draft">Draft</MenuItem>
                  <MenuItem value="published">Published</MenuItem>
                  <MenuItem value="paused">Paused</MenuItem>
                  <MenuItem value="closed">Closed</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Department</InputLabel>
                <Select
                  value={departmentFilter}
                  label="Department"
                  onChange={(e) => setDepartmentFilter(e.target.value)}
                >
                  <MenuItem value="">All Departments</MenuItem>
                  <MenuItem value="Engineering">Engineering</MenuItem>
                  <MenuItem value="Marketing">Marketing</MenuItem>
                  <MenuItem value="Sales">Sales</MenuItem>
                  <MenuItem value="HR">HR</MenuItem>
                  <MenuItem value="Finance">Finance</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<FilterIcon />}
                onClick={fetchJobPostings}
              >
                Apply Filters
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Job Postings Table */}
      <Card>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Title</TableCell>
                <TableCell>Department</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Applications</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Deadline</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {jobPostings.map((posting) => (
                <TableRow key={posting.id} hover>
                  <TableCell>
                    <Box>
                      <Typography variant="subtitle2">
                        {posting.title}
                        {posting.isUrgent && (
                          <Chip label="Urgent" color="error" size="small" sx={{ ml: 1 }} />
                        )}
                        {posting.isFeatured && (
                          <Chip label="Featured" color="primary" size="small" sx={{ ml: 1 }} />
                        )}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        {posting.location} • {posting.jobType.replace(/_/g, ' ')}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>{posting.department}</TableCell>
                  <TableCell>
                    <Chip
                      label={posting.status.replace(/_/g, ' ').toUpperCase()}
                      color={getStatusColor(posting.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {posting.applicationCount} applications
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      {posting.viewCount} views
                    </Typography>
                  </TableCell>
                  <TableCell>{formatDate(posting.createdAt)}</TableCell>
                  <TableCell>
                    {posting.applicationDeadline ? (
                      <Typography
                        variant="body2"
                        color={isExpired(posting.applicationDeadline) ? 'error' : 'textPrimary'}
                      >
                        {formatDate(posting.applicationDeadline)}
                        {isExpired(posting.applicationDeadline) && ' (Expired)'}
                      </Typography>
                    ) : (
                      <Typography variant="body2" color="textSecondary">
                        No deadline
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell align="right">
                    <Tooltip title="View Details">
                      <IconButton
                        size="small"
                        onClick={() => navigate(`/hr/recruitment/job-postings/${posting.id}`)}
                      >
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Edit">
                      <IconButton
                        size="small"
                        onClick={() => navigate(`/hr/recruitment/job-postings/${posting.id}/edit`)}
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <IconButton
                      size="small"
                      onClick={(e) => handleMenuClick(e, posting)}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={total}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {selectedPosting?.status === 'draft' && (
          <MenuItem onClick={() => handlePublish(selectedPosting)}>
            <PublishIcon sx={{ mr: 1 }} />
            Publish
          </MenuItem>
        )}
        {selectedPosting?.status === 'published' && (
          <MenuItem onClick={() => handleClose(selectedPosting)}>
            <CloseIcon sx={{ mr: 1 }} />
            Close
          </MenuItem>
        )}
        <MenuItem onClick={() => setDeleteDialogOpen(true)}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the job posting "{selectedPosting?.title}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleDelete}
            color="error"
            disabled={actionLoading}
          >
            {actionLoading ? <CircularProgress size={20} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default JobPostingsList;
