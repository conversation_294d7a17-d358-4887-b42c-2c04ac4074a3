import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateSoftwareLicenseTable1710000000003 implements MigrationInterface {
    name = 'CreateSoftwareLicenseTable1710000000003'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE software_license (
                id VARCHAR(36) PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                vendorId VARCHAR(36),
                category VARCHAR(255) NOT NULL,
                type ENUM('One-Time (Lifetime)', 'Monthly Subscription', 'Yearly Subscription', 'Trial', 'Freemium', 'Open Source', 'Free') NOT NULL,
                department VARCHAR(255) NOT NULL,
                status ENUM('Active', 'Inactive', 'Expired', 'Pending', 'Suspended') NOT NULL,
                licenseKey VARCHAR(255) NOT NULL,
                totalSeats INT NOT NULL,
                usedSeats INT NOT NULL,
                purchaseDate DATETIME NOT NULL,
                expiryDate DATETIME,
                paymentFrequency ENUM('Monthly', 'Quarterly', 'Yearly', 'One-time') NOT NULL,
                costPKR DECIMAL(10,2) NOT NULL,
                costUSD DECIMAL(10,2) NOT NULL,
                paidBy VARCHAR(255) NOT NULL,
                invoiceUrl VARCHAR(255),
                autoRenew BOOLEAN NOT NULL DEFAULT false,
                notes TEXT,
                currencyConverter BOOLEAN NOT NULL DEFAULT false,
                renewalReminder BOOLEAN NOT NULL DEFAULT false,
                multiLocationUse BOOLEAN NOT NULL DEFAULT false,
                loginSharingInfo TEXT,
                socialMediaLinks TEXT,
                createdAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updatedAt DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                createdBy VARCHAR(255),
                updatedBy VARCHAR(255),
                assignedToId VARCHAR(36),
                FOREIGN KEY (vendorId) REFERENCES vendor(id) ON DELETE SET NULL,
                FOREIGN KEY (assignedToId) REFERENCES user(id) ON DELETE SET NULL
            )
        `);

        // Create junction tables for many-to-many relationships
        await queryRunner.query(`
            CREATE TABLE software_license_assigned_users (
                softwareLicenseId VARCHAR(36),
                userId VARCHAR(36),
                PRIMARY KEY (softwareLicenseId, userId),
                FOREIGN KEY (softwareLicenseId) REFERENCES software_license(id) ON DELETE CASCADE,
                FOREIGN KEY (userId) REFERENCES user(id) ON DELETE CASCADE
            )
        `);

        await queryRunner.query(`
            CREATE TABLE software_license_linked_assets (
                softwareLicenseId VARCHAR(36),
                assetId VARCHAR(36),
                PRIMARY KEY (softwareLicenseId, assetId),
                FOREIGN KEY (softwareLicenseId) REFERENCES software_license(id) ON DELETE CASCADE,
                FOREIGN KEY (assetId) REFERENCES asset(id) ON DELETE CASCADE
            )
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE IF EXISTS software_license_linked_assets`);
        await queryRunner.query(`DROP TABLE IF EXISTS software_license_assigned_users`);
        await queryRunner.query(`DROP TABLE IF EXISTS software_license`);
    }
} 