import { Router } from 'express';
import { LeaveApprovalController } from '../controllers/LeaveApprovalController';
import { requireAuth } from '../middleware/auth';

const router = Router();
const leaveApprovalController = new LeaveApprovalController();

// Apply authentication middleware to all routes
router.use(requireAuth);

// Get leave request with approval timeline
router.get('/:id/timeline', leaveApprovalController.getLeaveRequestWithApprovals.bind(leaveApprovalController));

// Get approval history for a specific leave request
router.get('/:id/history', leaveApprovalController.getApprovalHistory.bind(leaveApprovalController));

// Manager approval endpoints
router.post('/:id/manager/approve', leaveApprovalController.managerApproval.bind(leaveApprovalController));
router.post('/:id/manager/reject', leaveApprovalController.managerApproval.bind(leaveApprovalController));

// HR approval endpoints
router.post('/:id/hr/approve', leaveApprovalController.hrApproval.bind(leaveApprovalController));
router.post('/:id/hr/reject', leaveApprovalController.hrApproval.bind(leaveApprovalController));

// Get pending approvals by role
router.get('/pending/manager', leaveApprovalController.getPendingManagerApprovals.bind(leaveApprovalController));
router.get('/pending/hr', leaveApprovalController.getPendingHRApprovals.bind(leaveApprovalController));

// Get approval statistics (for dashboard)
router.get('/statistics', leaveApprovalController.getApprovalStatistics.bind(leaveApprovalController));

export default router; 