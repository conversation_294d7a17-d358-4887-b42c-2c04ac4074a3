import React, { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  UserPlus,
  Calendar,
  Clock,
  DollarSign,
  ArrowRight,
  Save,
  Calculator,
  FileText,
  Plus,
  Minus,
  AlertCircle
} from 'lucide-react';
import {
  PayrollEmployee,
  PayrollPeriod,
  SalaryComponent,
  EmployeePayrollEntry,
  PayrollCurrency
} from '../../../types/payroll';

interface IndividualPayrollProcessorProps {
  isAdmin?: boolean;
  selectedCurrency?: PayrollCurrency;
}

const IndividualPayrollProcessor: React.FC<IndividualPayrollProcessorProps> = ({ isAdmin = false, selectedCurrency = 'USD' }) => {
  const [selectedEmployee, setSelectedEmployee] = useState<PayrollEmployee | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<PayrollPeriod | null>(null);
  const [employees, setEmployees] = useState<PayrollEmployee[]>([]);
  const [periods, setPeriods] = useState<PayrollPeriod[]>([]);
  const [salaryComponents, setSalaryComponents] = useState<SalaryComponent[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [calculatedPayroll, setCalculatedPayroll] = useState<EmployeePayrollEntry | null>(null);
  const [showCalculation, setShowCalculation] = useState(false);
  
  // Sample attendance data
  const [attendanceData, setAttendanceData] = useState({
    regularDays: 21,
    overtimeHours: 5,
    leaveDays: 1,
    unpaidLeaveDays: 0,
    holidayWorked: 0
  });

  // Sample adjustment data
  const [adjustments, setAdjustments] = useState([
    { id: 1, name: 'Performance Bonus', type: 'earning', amount: 2000, isDefault: false },
    { id: 2, name: 'Late Deduction', type: 'deduction', amount: 500, isDefault: false }
  ]);

  // Currency symbols mapping
  const currencySymbols: Record<PayrollCurrency, string> = {
    USD: '$',
    EUR: '€',
    GBP: '£',
    PKR: '₨',
    JPY: '¥',
    CAD: 'C$',
    AUD: 'A$',
    INR: '₹'
  };

  // Format currency amount
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: selectedCurrency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount);
  };

  // Exchange rate conversion
  const getExchangeRate = (fromCurrency: PayrollCurrency, toCurrency: PayrollCurrency): number => {
    // This would typically come from an API or database
    // Mocked exchange rates for demonstration purposes
    const rates: Record<PayrollCurrency, Record<PayrollCurrency, number>> = {
      USD: { USD: 1, EUR: 0.85, GBP: 0.75, PKR: 278.5, JPY: 110.5, CAD: 1.25, AUD: 1.35, INR: 83.2 },
      EUR: { USD: 1.18, EUR: 1, GBP: 0.88, PKR: 328.2, JPY: 130.4, CAD: 1.47, AUD: 1.59, INR: 98.2 },
      GBP: { USD: 1.33, EUR: 1.13, GBP: 1, PKR: 371.7, JPY: 147.3, CAD: 1.67, AUD: 1.80, INR: 110.9 },
      PKR: { USD: 0.0036, EUR: 0.003, GBP: 0.0027, PKR: 1, JPY: 0.4, CAD: 0.0045, AUD: 0.0048, INR: 0.3 },
      JPY: { USD: 0.009, EUR: 0.0077, GBP: 0.0068, PKR: 2.52, JPY: 1, CAD: 0.011, AUD: 0.012, INR: 0.75 },
      CAD: { USD: 0.8, EUR: 0.68, GBP: 0.6, PKR: 222.8, JPY: 88.4, CAD: 1, AUD: 1.08, INR: 66.6 },
      AUD: { USD: 0.74, EUR: 0.63, GBP: 0.55, PKR: 206.3, JPY: 81.9, CAD: 0.93, AUD: 1, INR: 61.6 },
      INR: { USD: 0.012, EUR: 0.01, GBP: 0.009, PKR: 3.35, JPY: 1.33, CAD: 0.015, AUD: 0.016, INR: 1 }
    };
    
    return rates[fromCurrency][toCurrency];
  };

  // Convert amount between currencies
  const convertCurrency = (amount: number, fromCurrency: PayrollCurrency, toCurrency: PayrollCurrency): number => {
    const rate = getExchangeRate(fromCurrency, toCurrency);
    return amount * rate;
  };

  // Load sample data
  useEffect(() => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      // Sample employees
      const sampleEmployees: PayrollEmployee[] = [
        {
          id: 1,
          employeeId: 'EMP001',
          firstName: 'John',
          lastName: 'Smith',
          department: 'IT',
          designation: 'Senior Developer',
          joinDate: '2022-01-15',
          baseSalary: 85000,
          status: 'active',
          paymentMethod: 'bank_transfer',
          bankName: 'City Bank',
          accountNumber: '**********',
          taxIdentificationNumber: 'TX123456'
        },
        {
          id: 2,
          employeeId: 'EMP002',
          firstName: 'Sarah',
          lastName: 'Johnson',
          department: 'HR',
          designation: 'HR Manager',
          joinDate: '2022-03-10',
          baseSalary: 75000,
          status: 'active',
          paymentMethod: 'bank_transfer',
          bankName: 'Global Bank',
          accountNumber: '**********',
          taxIdentificationNumber: 'TX789012'
        },
        {
          id: 3,
          employeeId: 'EMP003',
          firstName: 'Michael',
          lastName: 'Wong',
          department: 'Finance',
          designation: 'Financial Analyst',
          joinDate: '2022-02-01',
          baseSalary: 70000,
          status: 'active',
          paymentMethod: 'bank_transfer',
          bankName: 'City Bank',
          accountNumber: '**********',
          taxIdentificationNumber: 'TX345678'
        }
      ];
      
      // Sample periods
      const samplePeriods: PayrollPeriod[] = [
        {
          id: 1,
          name: 'May 2025',
          startDate: '2025-05-01',
          endDate: '2025-05-31',
          paymentDate: '2025-06-05',
          frequency: 'monthly',
          status: 'processing',
          totalEmployees: 32,
          totalAmount: 1250000
        },
        {
          id: 2,
          name: 'April 2025',
          startDate: '2025-04-01',
          endDate: '2025-04-30',
          paymentDate: '2025-05-05',
          frequency: 'monthly',
          status: 'completed',
          totalEmployees: 32,
          totalAmount: 1245300
        }
      ];
      
      // Sample salary components
      const sampleComponents: SalaryComponent[] = [
        {
          id: 1,
          name: 'Basic Salary',
          type: 'earning',
          calculationType: 'fixed',
          value: 0,
          taxable: true,
          description: 'Base salary component',
          isDefault: true,
          isActive: true
        },
        {
          id: 2,
          name: 'Housing Allowance',
          type: 'earning',
          calculationType: 'percentage',
          value: 25,
          taxable: true,
          description: '25% of basic salary',
          isDefault: true,
          isActive: true
        },
        {
          id: 3,
          name: 'Transport Allowance',
          type: 'earning',
          calculationType: 'fixed',
          value: 5000,
          taxable: true,
          description: 'Fixed transportation allowance',
          isDefault: true,
          isActive: true
        },
        {
          id: 4,
          name: 'Income Tax',
          type: 'tax',
          calculationType: 'percentage',
          value: 15,
          taxable: false,
          description: 'Standard income tax deduction',
          isDefault: true,
          isActive: true
        },
        {
          id: 5,
          name: 'Health Insurance',
          type: 'deduction',
          calculationType: 'fixed',
          value: 2000,
          taxable: false,
          description: 'Health insurance premium',
          isDefault: true,
          isActive: true
        }
      ];
      
      setEmployees(sampleEmployees);
      setPeriods(samplePeriods);
      setSalaryComponents(sampleComponents);
      setSelectedPeriod(samplePeriods[0]);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter employees based on search term
  const filteredEmployees = employees.filter(
    emp => emp.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
           emp.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
           emp.employeeId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Calculate payroll with currency support
  const calculatePayroll = () => {
    if (!selectedEmployee || !selectedPeriod) return;

    setLoading(true);
    
    // Simulate calculation delay
    setTimeout(() => {
      const employeeCurrency = selectedEmployee.currency || 'USD';
      let monthlyBaseSalary = selectedEmployee.baseSalary / 12;
      
      // Convert salary to selected currency if needed
      if (employeeCurrency !== selectedCurrency) {
        monthlyBaseSalary = convertCurrency(monthlyBaseSalary, employeeCurrency, selectedCurrency);
      }
      
      const dailyRate = monthlyBaseSalary / 22; // Assuming 22 working days in a month
      const hourlyRate = dailyRate / 8; // Assuming 8 working hours per day
      
      // Calculate earnings
      const regularSalary = dailyRate * attendanceData.regularDays;
      const overtimePay = hourlyRate * 1.5 * attendanceData.overtimeHours;
      const holidayPay = dailyRate * 2 * attendanceData.holidayWorked;
      
      // Calculate deductions
      const unpaidLeave = dailyRate * attendanceData.unpaidLeaveDays;
      const taxRate = 0.15; // 15% tax rate
      
      // Add standard earnings
      const totalEarnings = regularSalary + overtimePay + holidayPay;
      
      // Add adjustments
      const earningAdjustments = adjustments
        .filter(adj => adj.type === 'earning')
        .reduce((sum, adj) => sum + adj.amount, 0);
        
      const deductionAdjustments = adjustments
        .filter(adj => adj.type === 'deduction')
        .reduce((sum, adj) => sum + adj.amount, 0);
      
      const grossSalary = totalEarnings + earningAdjustments;
      const tax = grossSalary * taxRate;
      const totalDeductions = unpaidLeave + deductionAdjustments;
      const netSalary = grossSalary - tax - totalDeductions;
      
      const calculatedEntry: EmployeePayrollEntry = {
        id: Math.floor(Math.random() * 1000),
        payrollPeriodId: selectedPeriod.id,
        employeeId: selectedEmployee.id,
        employeeName: `${selectedEmployee.firstName} ${selectedEmployee.lastName}`,
        department: selectedEmployee.department,
        designation: selectedEmployee.designation,
        baseSalary: selectedEmployee.baseSalary,
        grossSalary: parseFloat(grossSalary.toFixed(2)),
        totalEarnings: parseFloat((totalEarnings + earningAdjustments).toFixed(2)),
        totalDeductions: parseFloat(totalDeductions.toFixed(2)),
        totalTax: parseFloat(tax.toFixed(2)),
        netSalary: parseFloat(netSalary.toFixed(2)),
        paymentMethod: selectedEmployee.paymentMethod,
        paymentStatus: 'pending',
        bankName: selectedEmployee.bankName,
        accountNumber: selectedEmployee.accountNumber,
        currency: selectedCurrency,
        exchangeRate: employeeCurrency !== selectedCurrency ? 
          getExchangeRate(employeeCurrency, selectedCurrency) : 1,
        earnings: [],
        deductions: [],
        taxes: [],
        attendance: {
          totalDays: 31,
          presentDays: attendanceData.regularDays,
          absentDays: attendanceData.unpaidLeaveDays,
          leaveDays: attendanceData.leaveDays,
          holidayDays: 9,
          workingDays: 22
        }
      };
      
      setCalculatedPayroll(calculatedEntry);
      setShowCalculation(true);
      setLoading(false);
    }, 1000);
  };

  // Handle adding a new adjustment
  const handleAddAdjustment = () => {
    const newId = Math.max(0, ...adjustments.map(a => a.id)) + 1;
    setAdjustments([
      ...adjustments,
      { id: newId, name: 'New Adjustment', type: 'earning', amount: 0, isDefault: false }
    ]);
  };

  // Handle removing an adjustment
  const handleRemoveAdjustment = (id: number) => {
    setAdjustments(adjustments.filter(adj => adj.id !== id));
  };
  
  // Handle adjustment changes
  const handleAdjustmentChange = (id: number, field: string, value: string | number) => {
    setAdjustments(adjustments.map(adj => 
      adj.id === id ? { ...adj, [field]: value } : adj
    ));
  };

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Employee Selection */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="text-base font-medium text-gray-900 mb-4">Select Employee</h3>
            
            <div className="relative mb-4">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search employees..."
                className="pl-10 block w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 p-2"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <div className="h-64 overflow-y-auto border border-gray-200 rounded-md">
              {loading ? (
                <div className="flex items-center justify-center h-full">
                  <div className="animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                </div>
              ) : (
                <ul className="divide-y divide-gray-200">
                  {filteredEmployees.map((employee) => (
                    <li 
                      key={employee.id}
                      className={`p-3 hover:bg-gray-50 cursor-pointer ${
                        selectedEmployee?.id === employee.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                      }`}
                      onClick={() => setSelectedEmployee(employee)}
                    >
                      <div className="font-medium text-gray-900">
                        {employee.firstName} {employee.lastName}
                      </div>
                      <div className="text-sm text-gray-500">
                        {employee.employeeId} • {employee.designation}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {employee.department}
                      </div>
                    </li>
                  ))}
                  
                  {filteredEmployees.length === 0 && (
                    <li className="p-3 text-center text-gray-500">
                      No employees found
                    </li>
                  )}
                </ul>
              )}
            </div>
          </div>
          
          {/* Payroll Period & Attendance */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="text-base font-medium text-gray-900 mb-4">Pay Period & Attendance</h3>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Select Pay Period
              </label>
              <select 
                className="block w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 p-2"
                value={selectedPeriod?.id || ''}
                onChange={(e) => {
                  const periodId = parseInt(e.target.value);
                  const period = periods.find(p => p.id === periodId) || null;
                  setSelectedPeriod(period);
                }}
              >
                <option value="">Select period</option>
                {periods.map((period) => (
                  <option key={period.id} value={period.id}>
                    {period.name} ({new Date(period.startDate).toLocaleDateString()} - {new Date(period.endDate).toLocaleDateString()})
                  </option>
                ))}
              </select>
            </div>
            
            <div className="border-t border-gray-200 pt-4">
              <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                <Calendar className="h-4 w-4 mr-1 text-blue-500" />
                Attendance Information
              </h4>
              
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Regular Days
                  </label>
                  <input
                    type="number"
                    className="block w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 p-1"
                    value={attendanceData.regularDays}
                    onChange={(e) => setAttendanceData({...attendanceData, regularDays: parseInt(e.target.value) || 0})}
                    min="0"
                    max="31"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Overtime Hours
                  </label>
                  <input
                    type="number"
                    className="block w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 p-1"
                    value={attendanceData.overtimeHours}
                    onChange={(e) => setAttendanceData({...attendanceData, overtimeHours: parseInt(e.target.value) || 0})}
                    min="0"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Paid Leave Days
                  </label>
                  <input
                    type="number"
                    className="block w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 p-1"
                    value={attendanceData.leaveDays}
                    onChange={(e) => setAttendanceData({...attendanceData, leaveDays: parseInt(e.target.value) || 0})}
                    min="0"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Unpaid Leave Days
                  </label>
                  <input
                    type="number"
                    className="block w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 p-1"
                    value={attendanceData.unpaidLeaveDays}
                    onChange={(e) => setAttendanceData({...attendanceData, unpaidLeaveDays: parseInt(e.target.value) || 0})}
                    min="0"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Holidays Worked
                  </label>
                  <input
                    type="number"
                    className="block w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 p-1"
                    value={attendanceData.holidayWorked}
                    onChange={(e) => setAttendanceData({...attendanceData, holidayWorked: parseInt(e.target.value) || 0})}
                    min="0"
                  />
                </div>
              </div>
            </div>
          </div>
          
          {/* Adjustments & Calculations */}
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="text-base font-medium text-gray-900 mb-4">Salary Adjustments</h3>
            
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-sm font-medium text-gray-800">Custom Adjustments</h4>
                <button
                  onClick={handleAddAdjustment}
                  className="inline-flex items-center text-xs text-blue-600 hover:text-blue-800"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add Adjustment
                </button>
              </div>
              
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {adjustments.map((adjustment) => (
                  <div key={adjustment.id} className="flex items-center space-x-2 p-2 bg-gray-50 rounded-md">
                    <div className="flex-grow">
                      <input
                        type="text"
                        className="block w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 p-1 text-sm"
                        value={adjustment.name}
                        onChange={(e) => handleAdjustmentChange(adjustment.id, 'name', e.target.value)}
                        placeholder="Adjustment Name"
                      />
                    </div>
                    <div className="w-24">
                      <select
                        className="block w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 p-1 text-sm"
                        value={adjustment.type}
                        onChange={(e) => handleAdjustmentChange(adjustment.id, 'type', e.target.value)}
                      >
                        <option value="earning">Add</option>
                        <option value="deduction">Deduct</option>
                      </select>
                    </div>
                    <div className="w-24">
                      <input
                        type="number"
                        className="block w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 p-1 text-sm"
                        value={adjustment.amount}
                        onChange={(e) => handleAdjustmentChange(adjustment.id, 'amount', parseFloat(e.target.value) || 0)}
                        placeholder="Amount"
                      />
                    </div>
                    <button
                      onClick={() => handleRemoveAdjustment(adjustment.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Minus className="h-4 w-4" />
                    </button>
                  </div>
                ))}
                
                {adjustments.length === 0 && (
                  <div className="text-center text-gray-500 text-sm py-2">
                    No adjustments added
                  </div>
                )}
              </div>
            </div>
            
            <div className="mt-auto pt-4 border-t border-gray-200">
              <button
                onClick={calculatePayroll}
                disabled={!selectedEmployee || !selectedPeriod || loading}
                className={`w-full flex items-center justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white
                  ${(!selectedEmployee || !selectedPeriod || loading)
                    ? 'bg-gray-300 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                  }`}
              >
                {loading ? (
                  <>
                    <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                    Calculating...
                  </>
                ) : (
                  <>
                    <Calculator className="h-4 w-4 mr-2" />
                    Calculate Payroll
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
        
        {/* Calculation Results */}
        {showCalculation && calculatedPayroll && (
          <div className="mt-6 border border-gray-200 rounded-lg bg-gray-50 p-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-base font-medium text-gray-900">
                Payroll Calculation Results
              </h3>
              <div className="flex space-x-2">
                <button
                  className="inline-flex items-center px-3 py-1 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                  <Save className="h-4 w-4 mr-1" />
                  Save
                </button>
                <button
                  className="inline-flex items-center px-3 py-1 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <FileText className="h-4 w-4 mr-1" />
                  View Payslip
                </button>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white rounded-md shadow-sm p-3">
                <div className="text-sm text-gray-500 mb-1">Employee</div>
                <div className="text-base font-medium">{calculatedPayroll.employeeName}</div>
                <div className="text-sm text-gray-500">{calculatedPayroll.designation} • {calculatedPayroll.department}</div>
              </div>
              
              <div className="bg-white rounded-md shadow-sm p-3">
                <div className="text-sm text-gray-500 mb-1">Period</div>
                <div className="text-base font-medium">{selectedPeriod?.name}</div>
                <div className="text-sm text-gray-500">
                  {new Date(selectedPeriod?.startDate || '').toLocaleDateString()} - 
                  {new Date(selectedPeriod?.endDate || '').toLocaleDateString()}
                </div>
              </div>
              
              <div className="bg-white rounded-md shadow-sm p-3">
                <div className="text-sm text-gray-500 mb-1">Currency</div>
                <div className="text-base font-medium flex items-center">
                  <DollarSign className="h-4 w-4 mr-1 text-green-600" />
                  {calculatedPayroll.currency || 'USD'}
                  {calculatedPayroll.exchangeRate !== 1 && (
                    <span className="ml-2 text-xs text-gray-500">
                      (Rate: {calculatedPayroll.exchangeRate?.toFixed(2)})
                    </span>
                  )}
                </div>
                <div className="text-sm text-gray-500">
                  <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                    Calculated
                  </span>
                </div>
              </div>
            </div>
            
            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white rounded-md shadow-sm p-4">
                <h4 className="text-sm font-medium text-gray-900 mb-3">Earnings</h4>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Regular Salary</span>
                    <span>{formatCurrency((calculatedPayroll.baseSalary / 12 / 22) * attendanceData.regularDays)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Overtime ({attendanceData.overtimeHours} hours)</span>
                    <span>{formatCurrency((calculatedPayroll.baseSalary / 12 / 22 / 8) * 1.5 * attendanceData.overtimeHours)}</span>
                  </div>
                  {adjustments.filter(a => a.type === 'earning').map((adj) => (
                    <div key={adj.id} className="flex justify-between text-sm">
                      <span>{adj.name}</span>
                      <span>{formatCurrency(adj.amount)}</span>
                    </div>
                  ))}
                  <div className="border-t border-gray-200 pt-2 flex justify-between font-medium">
                    <span>Total Earnings</span>
                    <span>{formatCurrency(calculatedPayroll.totalEarnings)}</span>
                  </div>
                </div>
              </div>
              
              <div className="bg-white rounded-md shadow-sm p-4">
                <h4 className="text-sm font-medium text-gray-900 mb-3">Deductions & Taxes</h4>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Income Tax (15%)</span>
                    <span>{formatCurrency(calculatedPayroll.totalTax)}</span>
                  </div>
                  {attendanceData.unpaidLeaveDays > 0 && (
                    <div className="flex justify-between text-sm">
                      <span>Unpaid Leave ({attendanceData.unpaidLeaveDays} days)</span>
                      <span>{formatCurrency((calculatedPayroll.baseSalary / 12 / 22) * attendanceData.unpaidLeaveDays)}</span>
                    </div>
                  )}
                  {adjustments.filter(a => a.type === 'deduction').map((adj) => (
                    <div key={adj.id} className="flex justify-between text-sm">
                      <span>{adj.name}</span>
                      <span>{formatCurrency(adj.amount)}</span>
                    </div>
                  ))}
                  <div className="border-t border-gray-200 pt-2 flex justify-between font-medium">
                    <span>Total Deductions</span>
                    <span>{formatCurrency(calculatedPayroll.totalDeductions + calculatedPayroll.totalTax)}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-4 bg-blue-50 rounded-md p-4 flex justify-between items-center">
              <div>
                <div className="text-sm text-gray-700">Net Salary</div>
                <div className="text-xl font-bold text-blue-800">{formatCurrency(calculatedPayroll.netSalary)}</div>
              </div>
              <div className="text-sm text-gray-500">
                To be paid via {calculatedPayroll.paymentMethod.replace('_', ' ')}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default IndividualPayrollProcessor; 