import React, { useState } from 'react';
import {
  <PERSON>,
  Clock,
  CheckCircle,
  TrendingUp,
  BarChart2,
  DollarSign,
  AlertTriangle,
  Building2,
  Calendar
} from 'lucide-react';

interface VendorPerformanceData {
  id: string;
  name: string;
  metrics: {
    rating: number;
    responseTime: number;
    deliveryScore: number;
    qualityScore: number;
    spending: {
      current: number;
      previous: number;
      trend: number;
    };
    contracts: number;
    incidents: number;
    compliance: number;
  };
  history: {
    month: string;
    rating: number;
    spending: number;
    incidents: number;
  }[];
  lastEvaluation: string;
  nextEvaluation: string;
}

export function VendorPerformance() {
  const [performanceData] = useState<VendorPerformanceData[]>([
    {
      id: 'V001',
      name: 'Tech Solutions Inc',
      metrics: {
        rating: 4.5,
        responseTime: 24,
        deliveryScore: 98,
        qualityScore: 95,
        spending: {
          current: 450000,
          previous: 380000,
          trend: 18.4
        },
        contracts: 3,
        incidents: 2,
        compliance: 100
      },
      history: [
        { month: 'Jan 2024', rating: 4.5, spending: 150000, incidents: 1 },
        { month: 'Dec 2023', rating: 4.3, spending: 145000, incidents: 0 },
        { month: 'Nov 2023', rating: 4.4, spending: 140000, incidents: 1 }
      ],
      lastEvaluation: '2024-01-15',
      nextEvaluation: '2024-04-15'
    },
    {
      id: 'V002',
      name: 'Hardware Pro',
      metrics: {
        rating: 4.8,
        responseTime: 12,
        deliveryScore: 100,
        qualityScore: 98,
        spending: {
          current: 750000,
          previous: 620000,
          trend: 21.0
        },
        contracts: 5,
        incidents: 0,
        compliance: 100
      },
      history: [
        { month: 'Jan 2024', rating: 4.8, spending: 250000, incidents: 0 },
        { month: 'Dec 2023', rating: 4.7, spending: 240000, incidents: 0 },
        { month: 'Nov 2023', rating: 4.8, spending: 235000, incidents: 0 }
      ],
      lastEvaluation: '2024-01-20',
      nextEvaluation: '2024-04-20'
    }
  ]);

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getMetricCard = (
    icon: React.ReactNode,
    label: string,
    value: string | number,
    subValue?: string,
    trend?: number
  ) => (
    <div className="bg-white p-4 rounded-lg shadow-sm">
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm text-gray-500">{label}</span>
        {icon}
      </div>
      <div className="flex items-center justify-between">
        <span className="text-lg font-semibold">{value}</span>
        {subValue && <span className="text-sm text-gray-500">{subValue}</span>}
        {trend !== undefined && (
          <span className={`text-sm ${trend >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {trend >= 0 ? '+' : ''}{trend}%
          </span>
        )}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {performanceData.map((vendor) => (
        <div
          key={vendor.id}
          className="bg-white rounded-lg shadow-sm p-6"
        >
          <div className="flex items-center gap-3 mb-6">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Building2 className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">{vendor.name}</h3>
              <div className="flex items-center gap-4 text-sm text-gray-500">
                <span>ID: {vendor.id}</span>
                <span>•</span>
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>Last evaluated: {vendor.lastEvaluation}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {getMetricCard(
              <Star className="h-5 w-5 text-yellow-500" />,
              'Overall Rating',
              `${vendor.metrics.rating}/5`,
              `${vendor.metrics.contracts} contracts`
            )}
            {getMetricCard(
              <Clock className="h-5 w-5 text-blue-500" />,
              'Response Time',
              `${vendor.metrics.responseTime}h`,
              'average'
            )}
            {getMetricCard(
              <DollarSign className="h-5 w-5 text-purple-500" />,
              'Total Spending',
              `₨${vendor.metrics.spending.current.toLocaleString()}`,
              undefined,
              vendor.metrics.spending.trend
            )}
            {getMetricCard(
              <AlertTriangle className="h-5 w-5 text-orange-500" />,
              'Incidents',
              vendor.metrics.incidents,
              'this month'
            )}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-gray-700">Performance Metrics</h4>
              <div className="space-y-3">
                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm text-gray-600">Delivery Score</span>
                    <span className={`text-sm font-medium ${getScoreColor(vendor.metrics.deliveryScore)}`}>
                      {vendor.metrics.deliveryScore}%
                    </span>
                  </div>
                  <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                    <div
                      className={`h-full ${getScoreColor(vendor.metrics.deliveryScore)} bg-current`}
                      style={{ width: `${vendor.metrics.deliveryScore}%` }}
                    />
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm text-gray-600">Quality Score</span>
                    <span className={`text-sm font-medium ${getScoreColor(vendor.metrics.qualityScore)}`}>
                      {vendor.metrics.qualityScore}%
                    </span>
                  </div>
                  <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                    <div
                      className={`h-full ${getScoreColor(vendor.metrics.qualityScore)} bg-current`}
                      style={{ width: `${vendor.metrics.qualityScore}%` }}
                    />
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm text-gray-600">Compliance Score</span>
                    <span className={`text-sm font-medium ${getScoreColor(vendor.metrics.compliance)}`}>
                      {vendor.metrics.compliance}%
                    </span>
                  </div>
                  <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                    <div
                      className={`h-full ${getScoreColor(vendor.metrics.compliance)} bg-current`}
                      style={{ width: `${vendor.metrics.compliance}%` }}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-4">Performance History</h4>
              <div className="overflow-x-auto">
                <table className="min-w-full">
                  <thead>
                    <tr>
                      <th className="text-left text-sm font-medium text-gray-500 pb-3">Month</th>
                      <th className="text-left text-sm font-medium text-gray-500 pb-3">Rating</th>
                      <th className="text-left text-sm font-medium text-gray-500 pb-3">Spending</th>
                      <th className="text-left text-sm font-medium text-gray-500 pb-3">Incidents</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-100">
                    {vendor.history.map((record, index) => (
                      <tr key={index}>
                        <td className="py-2 text-sm text-gray-600">{record.month}</td>
                        <td className="py-2 text-sm text-gray-600">{record.rating}/5</td>
                        <td className="py-2 text-sm text-gray-600">₨{record.spending.toLocaleString()}</td>
                        <td className="py-2 text-sm text-gray-600">{record.incidents}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
} 