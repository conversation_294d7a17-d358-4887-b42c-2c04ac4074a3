import React from 'react';
import { <PERSON>, Typo<PERSON>, Card, CardContent, Button, Alert } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { ArrowBack as ArrowBackIcon, Construction as ConstructionIcon } from '@mui/icons-material';

/**
 * Placeholder components for recruitment routes
 * These will be replaced with full components once the backend is ready
 */

export const JobPostingsPlaceholder: React.FC = () => {
  const navigate = useNavigate();
  
  return (
    <Box sx={{ p: 3 }}>
      <Box display="flex" alignItems="center" mb={3}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/hr/recruitment')}
          sx={{ mr: 2 }}
        >
          Back to Recruitment
        </Button>
        <Typography variant="h4">Job Postings</Typography>
      </Box>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          🚧 Job Postings Management
        </Typography>
        This section will allow you to:
        • Create and manage job postings
        • Publish jobs to external boards
        • Track application metrics
        • Set application deadlines
      </Alert>
      
      <Card>
        <CardContent sx={{ textAlign: 'center', py: 6 }}>
          <ConstructionIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            Coming Soon
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Job postings management interface will be available once the backend API is implemented.
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export const ApplicationsPlaceholder: React.FC = () => {
  const navigate = useNavigate();
  
  return (
    <Box sx={{ p: 3 }}>
      <Box display="flex" alignItems="center" mb={3}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/hr/recruitment')}
          sx={{ mr: 2 }}
        >
          Back to Recruitment
        </Button>
        <Typography variant="h4">Job Applications</Typography>
      </Box>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          🚧 Applications Management
        </Typography>
        This section will allow you to:
        • Review candidate applications
        • Rate and star candidates
        • Schedule interviews
        • Track application status
        • Manage hiring pipeline
      </Alert>
      
      <Card>
        <CardContent sx={{ textAlign: 'center', py: 6 }}>
          <ConstructionIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            Coming Soon
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Applications management interface will be available once the backend API is implemented.
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export const JobFormPlaceholder: React.FC = () => {
  const navigate = useNavigate();
  
  return (
    <Box sx={{ p: 3 }}>
      <Box display="flex" alignItems="center" mb={3}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/hr/recruitment/job-postings')}
          sx={{ mr: 2 }}
        >
          Back to Job Postings
        </Button>
        <Typography variant="h4">Create Job Posting</Typography>
      </Box>
      
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          🚧 Job Posting Form
        </Typography>
        This form will allow you to:
        • Create detailed job descriptions
        • Set requirements and qualifications
        • Configure salary ranges
        • Set application deadlines
        • Publish to multiple job boards
      </Alert>
      
      <Card>
        <CardContent sx={{ textAlign: 'center', py: 6 }}>
          <ConstructionIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            Coming Soon
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Job posting creation form will be available once the backend API is implemented.
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export const GenericPlaceholder: React.FC<{ title: string; description: string }> = ({ title, description }) => {
  const navigate = useNavigate();
  
  return (
    <Box sx={{ p: 3 }}>
      <Box display="flex" alignItems="center" mb={3}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/hr/recruitment')}
          sx={{ mr: 2 }}
        >
          Back to Recruitment
        </Button>
        <Typography variant="h4">{title}</Typography>
      </Box>
      
      <Card>
        <CardContent sx={{ textAlign: 'center', py: 6 }}>
          <ConstructionIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h5" gutterBottom>
            Coming Soon
          </Typography>
          <Typography variant="body1" color="textSecondary">
            {description}
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};
