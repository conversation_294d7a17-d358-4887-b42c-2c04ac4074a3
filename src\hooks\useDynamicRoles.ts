import { useState, useEffect, useCallback } from 'react';
import api from '../services/api';
import { toast } from 'react-hot-toast';

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  parentId?: string;
  category: string;
  dashboardAccess?: string[];
  isDynamic?: boolean;
  dynamicId?: string;
  hasRules?: boolean;
  userCount?: number;
}

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
  critical?: boolean;
  impacts?: string[];
}

interface DynamicFeatures {
  enabled: boolean;
  available: boolean;
  version: string;
}

export const useDynamicRoles = () => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dynamicFeatures, setDynamicFeatures] = useState<DynamicFeatures>({
    enabled: false,
    available: false,
    version: '1.0.0'
  });

  // Check if dynamic features are available
  const checkDynamicFeatures = useCallback(async () => {
    try {
      const response = await api.get('/dynamic-roles/roles');
      setDynamicFeatures({
        enabled: true,
        available: true,
        version: '1.0.0'
      });
      return true;
    } catch (error) {
      // Dynamic features not available, fall back to static
      setDynamicFeatures({
        enabled: false,
        available: false,
        version: '1.0.0'
      });
      return false;
    }
  }, []);

  // Fetch roles with dynamic enhancement
  const fetchRoles = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Try enhanced endpoint first
      let response;
      const isDynamicAvailable = await checkDynamicFeatures();
      
      if (isDynamicAvailable) {
        try {
          response = await api.get('/roles/enhanced');
        } catch (enhancedError) {
          // Fall back to regular endpoint
          response = await api.get('/roles');
        }
      } else {
        response = await api.get('/roles');
      }
      
      if (response.data) {
        setRoles(Array.isArray(response.data) ? response.data : []);
      }
    } catch (error) {
      console.error('Error fetching roles:', error);
      setError('Failed to fetch roles');
      
      // Try fallback endpoint
      try {
        const fallbackResponse = await api.get('/roles/fallback');
        if (fallbackResponse.data) {
          setRoles(Array.isArray(fallbackResponse.data) ? fallbackResponse.data : []);
          toast.warning('Using fallback role data');
        }
      } catch (fallbackError) {
        console.error('Fallback also failed:', fallbackError);
        setRoles([]);
      }
    } finally {
      setLoading(false);
    }
  }, [checkDynamicFeatures]);

  // Create role with dynamic features
  const createRole = useCallback(async (roleData: any) => {
    try {
      let response;
      
      if (dynamicFeatures.enabled && roleData.dynamicRules) {
        // Use enhanced endpoint for dynamic roles
        response = await api.post('/roles/enhanced', roleData);
      } else {
        // Use regular endpoint
        response = await api.post('/roles', roleData);
      }
      
      if (response.status === 201 && response.data) {
        const newRole = response.data;
        setRoles(prev => [...prev, newRole]);
        toast.success('Role created successfully');
        return newRole;
      } else {
        throw new Error('Failed to create role');
      }
    } catch (error) {
      console.error('Error creating role:', error);
      toast.error('Failed to create role');
      throw error;
    }
  }, [dynamicFeatures.enabled]);

  // Update role with dynamic features
  const updateRole = useCallback(async (roleId: string, roleData: any) => {
    try {
      const response = await api.put(`/roles/${roleId}`, roleData);
      
      if (response.status === 200 || response.status === 204) {
        setRoles(prev => prev.map(role => 
          role.id === roleId ? { ...role, ...roleData } : role
        ));
        toast.success('Role updated successfully');
        return response.data;
      } else {
        throw new Error('Failed to update role');
      }
    } catch (error) {
      console.error('Error updating role:', error);
      toast.error('Failed to update role');
      throw error;
    }
  }, []);

  // Delete role
  const deleteRole = useCallback(async (roleId: string) => {
    try {
      await api.delete(`/roles/${roleId}`);
      setRoles(prev => prev.filter(role => role.id !== roleId));
      toast.success('Role deleted successfully');
    } catch (error) {
      console.error('Error deleting role:', error);
      toast.error('Failed to delete role');
      throw error;
    }
  }, []);

  // Check user permissions
  const checkUserPermissions = useCallback(async (userId: string, actions: string[], resource?: any) => {
    if (!dynamicFeatures.enabled) {
      // Fall back to static permission checking
      return {};
    }
    
    try {
      const response = await api.post('/roles/check-permissions', {
        userId,
        actions,
        resource
      });
      
      return response.data.permissions || {};
    } catch (error) {
      console.error('Error checking permissions:', error);
      return {};
    }
  }, [dynamicFeatures.enabled]);

  // Get role with enhanced details
  const getRoleDetails = useCallback(async (roleId: string) => {
    try {
      let response;
      
      if (dynamicFeatures.enabled) {
        try {
          response = await api.get(`/roles/${roleId}/enhanced`);
        } catch (enhancedError) {
          response = await api.get(`/roles/${roleId}`);
        }
      } else {
        response = await api.get(`/roles/${roleId}`);
      }
      
      return response.data;
    } catch (error) {
      console.error('Error fetching role details:', error);
      throw error;
    }
  }, [dynamicFeatures.enabled]);

  // Clone role with dynamic features
  const cloneRole = useCallback(async (sourceRoleId: string, newName: string, newDescription?: string) => {
    try {
      let response;
      
      if (dynamicFeatures.enabled) {
        try {
          response = await api.post(`/dynamic-roles/roles/${sourceRoleId}/clone`, {
            newName,
            newDescription
          });
        } catch (dynamicError) {
          // Fall back to manual cloning
          const sourceRole = roles.find(r => r.id === sourceRoleId);
          if (sourceRole) {
            const clonedRole = {
              ...sourceRole,
              name: newName,
              description: newDescription || `Cloned from ${sourceRole.name}`
            };
            delete clonedRole.id;
            response = await api.post('/roles', clonedRole);
          } else {
            throw new Error('Source role not found');
          }
        }
      } else {
        // Manual cloning for static system
        const sourceRole = roles.find(r => r.id === sourceRoleId);
        if (sourceRole) {
          const clonedRole = {
            ...sourceRole,
            name: newName,
            description: newDescription || `Cloned from ${sourceRole.name}`
          };
          delete clonedRole.id;
          response = await api.post('/roles', clonedRole);
        } else {
          throw new Error('Source role not found');
        }
      }
      
      if (response.data) {
        setRoles(prev => [...prev, response.data]);
        toast.success('Role cloned successfully');
        return response.data;
      }
    } catch (error) {
      console.error('Error cloning role:', error);
      toast.error('Failed to clone role');
      throw error;
    }
  }, [dynamicFeatures.enabled, roles]);

  // Migrate role to dynamic system
  const migrateToDynamic = useCallback(async (roleId: string) => {
    if (!dynamicFeatures.available) {
      toast.error('Dynamic features not available');
      return;
    }
    
    try {
      const role = roles.find(r => r.id === roleId);
      if (!role) {
        throw new Error('Role not found');
      }
      
      // Create dynamic version
      await api.post('/dynamic-roles/roles', {
        name: role.name,
        description: role.description,
        category: role.category || 'operational',
        permissions: role.permissions || []
      });
      
      // Update local state
      setRoles(prev => prev.map(r => 
        r.id === roleId ? { ...r, isDynamic: true } : r
      ));
      
      toast.success('Role migrated to dynamic system');
    } catch (error) {
      console.error('Error migrating role:', error);
      toast.error('Failed to migrate role');
    }
  }, [dynamicFeatures.available, roles]);

  // Initialize
  useEffect(() => {
    fetchRoles();
  }, [fetchRoles]);

  return {
    // Data
    roles,
    permissions,
    loading,
    error,
    dynamicFeatures,
    
    // Actions
    fetchRoles,
    createRole,
    updateRole,
    deleteRole,
    cloneRole,
    getRoleDetails,
    checkUserPermissions,
    migrateToDynamic,
    
    // Utilities
    isRoleDynamic: (roleId: string) => {
      const role = roles.find(r => r.id === roleId);
      return role?.isDynamic || false;
    },
    
    canUseDynamicFeatures: dynamicFeatures.enabled,
    
    getDynamicRoleCount: () => roles.filter(r => r.isDynamic).length,
    
    getStaticRoleCount: () => roles.filter(r => !r.isDynamic).length
  };
};
