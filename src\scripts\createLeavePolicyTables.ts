import { AppDataSource } from '../config/database';

async function createLeavePolicyTables() {
  try {
    console.log('🚀 Creating Leave Policy Tables...');
    
    // Initialize database connection
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ Database connection established');
    }

    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();

    // Create leave_policy_configurations table
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS leave_policy_configurations (
        id int AUTO_INCREMENT PRIMARY KEY,
        companyId int,
        generalSettings json NOT NULL,
        version varchar(50) NOT NULL,
        effectiveDate timestamp NOT NULL,
        isActive boolean DEFAULT true,
        createdBy int NOT NULL,
        createdAt timestamp DEFAULT CURRENT_TIMESTAMP,
        updatedAt timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Create leave_type_policies table
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS leave_type_policies (
        id int AUTO_INCREMENT PRIMARY KEY,
        leaveType varchar(50) NOT NULL,
        enabled boolean DEFAULT true,
        displayName varchar(100) NOT NULL,
        description text,
        settings json NOT NULL,
        applicableRoles json,
        applicableDepartments json,
        maxDaysPerYear int NOT NULL,
        minServicePeriod int DEFAULT 0,
        allowCarryForward boolean DEFAULT false,
        carryForwardLimit int DEFAULT 0,
        encashmentAllowed boolean DEFAULT false,
        documentRequired boolean DEFAULT false,
        color varchar(7),
        sortOrder int,
        isActive boolean DEFAULT true,
        category varchar(50),
        allowHalfDay boolean DEFAULT true,
        minDaysNotice int DEFAULT 0,
        policyConfigurationId int NOT NULL,
        createdAt timestamp DEFAULT CURRENT_TIMESTAMP,
        updatedAt timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (policyConfigurationId) REFERENCES leave_policy_configurations(id) ON DELETE CASCADE
      )
    `);

    // Create leave_accrual_rules table
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS leave_accrual_rules (
        id int AUTO_INCREMENT PRIMARY KEY,
        leaveType varchar(50) NOT NULL,
        accrualFrequency enum('daily', 'weekly', 'monthly', 'quarterly', 'annually') NOT NULL,
        accrualAmount decimal(10,2) NOT NULL,
        maxAccumulation decimal(10,2) NOT NULL,
        carryOverLimit decimal(10,2) NOT NULL,
        carryOverExpiry int NOT NULL,
        effectiveDate timestamp NOT NULL,
        isProrated boolean DEFAULT false,
        waitingPeriod int DEFAULT 0,
        description text,
        applicableRoles json,
        applicableDepartments json,
        policyConfigurationId int NOT NULL,
        createdAt timestamp DEFAULT CURRENT_TIMESTAMP,
        updatedAt timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (policyConfigurationId) REFERENCES leave_policy_configurations(id) ON DELETE CASCADE
      )
    `);

    // Create holiday_calendars table
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS holiday_calendars (
        id int AUTO_INCREMENT PRIMARY KEY,
        name varchar(100) NOT NULL,
        year int NOT NULL,
        applicableDepartments json,
        applicableLocations json,
        policyConfigurationId int NOT NULL,
        createdAt timestamp DEFAULT CURRENT_TIMESTAMP,
        updatedAt timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (policyConfigurationId) REFERENCES leave_policy_configurations(id) ON DELETE CASCADE
      )
    `);

    // Create holidays table
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS holidays (
        id int AUTO_INCREMENT PRIMARY KEY,
        name varchar(100) NOT NULL,
        date date NOT NULL,
        type enum('fixed', 'floating', 'optional') DEFAULT 'fixed',
        description text,
        calendarId int NOT NULL,
        createdAt timestamp DEFAULT CURRENT_TIMESTAMP,
        updatedAt timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (calendarId) REFERENCES holiday_calendars(id) ON DELETE CASCADE
      )
    `);

    // Create leave_policy_settings table if it doesn't exist
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS leave_policy_settings (
        id int AUTO_INCREMENT PRIMARY KEY,
        minDaysNotice int DEFAULT 1,
        allowLeaveModification boolean DEFAULT true,
        isActive boolean DEFAULT true,
        createdBy int DEFAULT 1,
        updatedBy int DEFAULT 1,
        createdAt timestamp DEFAULT CURRENT_TIMESTAMP,
        updatedAt timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    await queryRunner.release();

    console.log('✅ Leave Policy Tables Created Successfully:');
    console.log('   - leave_policy_configurations');
    console.log('   - leave_type_policies');
    console.log('   - leave_accrual_rules');
    console.log('   - holiday_calendars');
    console.log('   - holidays');
    console.log('   - leave_policy_settings');
    
  } catch (error) {
    console.error('❌ Failed to create tables:', error);
    process.exit(1);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the script if executed directly
if (require.main === module) {
  createLeavePolicyTables();
}

export { createLeavePolicyTables }; 