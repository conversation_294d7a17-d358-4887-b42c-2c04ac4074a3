import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Unique } from 'typeorm';
import { IsNotEmpty, IsNumber, IsDateString, IsOptional, IsEnum, IsBoolean } from 'class-validator';
import { Employee } from './Employee';
import { Shift } from './Shift';

export enum AttendanceStatus {
  PRESENT = 'present',
  ABSENT = 'absent',
  LATE = 'late',
  HALF_DAY = 'half_day',
  LEAVE = 'leave',
  HOLIDAY = 'holiday',
  WEEKEND = 'weekend'
}

export enum ApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected'
}

@Entity('attendances')
@Unique('unique_employee_date', ['employeeId', 'date'])
export class Attendance {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  @IsNotEmpty({ message: 'Employee ID is required' })
  @IsNumber({}, { message: 'Employee ID must be a valid number' })
  employeeId: number;

  @ManyToOne(() => Employee, { nullable: false })
  @JoinColumn({ name: 'employeeId' })
  employee: Employee;

  @Column({ nullable: true })
  @IsOptional()
  employeeName?: string;

  @Column()
  @IsNotEmpty({ message: 'Date is required' })
  @IsDateString({}, { message: 'Date must be a valid date string' })
  date: string;

  @Column({ nullable: true })
  @IsOptional()
  checkInTime?: string;

  @Column({ nullable: true })
  @IsOptional()
  checkOutTime?: string;

  @Column({
    type: 'enum',
    enum: AttendanceStatus,
    default: AttendanceStatus.PRESENT
  })
  @IsEnum(AttendanceStatus, { message: 'Invalid attendance status' })
  status: AttendanceStatus;

  @Column({ nullable: true })
  notes?: string;

  @Column({ nullable: true })
  location?: string;

  @Column({ type: 'json', nullable: true })
  coordinates: { lat: number; lng: number } | null;

  @Column({ nullable: true })
  ipAddress?: string;

  @Column({ nullable: true })
  deviceInfo?: string;

  @Column({ type: 'float', nullable: true })
  workHours?: number;

  @Column({ type: 'float', nullable: true })
  overtime?: number;

  @Column({ default: false })
  isRemote: boolean;

  @Column({ default: false })
  isRegularized: boolean;

  @Column({ nullable: true })
  regularizedBy?: number;

  @Column({ nullable: true })
  regularizationReason?: string;

  @Column({ nullable: true })
  regularizationDate?: string;

  @Column({
    type: 'enum',
    enum: ApprovalStatus,
    default: ApprovalStatus.PENDING,
    nullable: true
  })
  approvalStatus: ApprovalStatus;

  @Column({ nullable: true })
  shiftId?: number;

  @ManyToOne(() => Shift, { nullable: true })
  @JoinColumn({ name: 'shiftId' })
  shiftDetails?: Shift;

  @Column({ nullable: true })
  shiftName?: string;

  @Column({ type: 'json', nullable: true })
  shiftTiming: { start: string; end: string } | null;

  @Column({ nullable: true })
  department?: string;

  @Column({ nullable: true })
  position?: string;

  @Column({ default: 0 })
  shift: number;

  @Column({ type: 'float', nullable: true })
  breakTime?: number;

  @Column({ default: false })
  isImported: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 