import React, { useState, useRef, useEffect } from 'react';
import { GripVertical } from 'lucide-react';

export interface Column {
  id: string;
  label: React.ReactNode;
  width?: number;
  minWidth?: number;
  maxWidth?: number;
  sortable?: boolean;
  frozen?: boolean;
}

interface ResizableTableProps {
  columns: Column[];
  onColumnResize?: (columnId: string, width: number) => void;
  onColumnReorder?: (orderedColumnIds: string[]) => void;
  className?: string;
  headerClassName?: string;
  children: React.ReactNode;
}

export const ResizableTable: React.FC<ResizableTableProps> = ({
  columns,
  onColumnResize,
  onColumnReorder,
  className = '',
  headerClassName = '',
  children
}) => {
  const [resizing, setResizing] = useState<{
    columnId: string;
    startX: number;
    startWidth: number;
  } | null>(null);
  
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>({});
  const [orderedColumns, setOrderedColumns] = useState<string[]>(columns.map(c => c.id));
  const [draggingColumn, setDraggingColumn] = useState<string | null>(null);
  const [dragOverColumn, setDragOverColumn] = useState<string | null>(null);
  
  const tableRef = useRef<HTMLTableElement>(null);
  
  // Initialize column widths based on props or defaults
  useEffect(() => {
    const initialWidths: Record<string, number> = {};
    columns.forEach(column => {
      initialWidths[column.id] = column.width || 150; // Default width
    });
    setColumnWidths(initialWidths);
  }, []);

  // Handle mouse move during column resize
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!resizing) return;
      
      const { columnId, startX, startWidth } = resizing;
      const diff = e.clientX - startX;
      const column = columns.find(c => c.id === columnId);
      
      if (!column) return;
      
      // Calculate new width with constraints
      let newWidth = Math.max(startWidth + diff, column.minWidth || 80);
      if (column.maxWidth) {
        newWidth = Math.min(newWidth, column.maxWidth);
      }
      
      setColumnWidths(prev => ({
        ...prev,
        [columnId]: newWidth
      }));
    };
    
    const handleMouseUp = () => {
      if (resizing) {
        // Notify parent component about resize
        onColumnResize?.(resizing.columnId, columnWidths[resizing.columnId]);
        setResizing(null);
      }
    };
    
    if (resizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      // Set cursor for the entire document during resize
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    }
    
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      // Reset cursor and user-select
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };
  }, [resizing, columns, columnWidths, onColumnResize]);

  // Start resizing a column
  const handleResizeStart = (columnId: string, e: React.MouseEvent<HTMLDivElement>) => {
    e.preventDefault();
    setResizing({
      columnId,
      startX: e.clientX,
      startWidth: columnWidths[columnId] || 150
    });
  };

  // Handle column drag and drop
  const handleDragStart = (columnId: string) => {
    setDraggingColumn(columnId);
  };

  const handleDragOver = (columnId: string, e: React.DragEvent) => {
    e.preventDefault();
    if (draggingColumn && draggingColumn !== columnId) {
      setDragOverColumn(columnId);
    }
  };

  const handleDrop = (targetColumnId: string) => {
    if (draggingColumn && draggingColumn !== targetColumnId) {
      const currentIndex = orderedColumns.indexOf(draggingColumn);
      const targetIndex = orderedColumns.indexOf(targetColumnId);
      
      if (currentIndex !== -1 && targetIndex !== -1) {
        const newOrderedColumns = [...orderedColumns];
        newOrderedColumns.splice(currentIndex, 1);
        newOrderedColumns.splice(targetIndex, 0, draggingColumn);
        
        setOrderedColumns(newOrderedColumns);
        onColumnReorder?.(newOrderedColumns);
      }
    }
    
    setDraggingColumn(null);
    setDragOverColumn(null);
  };

  // Split columns into frozen and non-frozen
  const frozenColumnIds = orderedColumns.filter(columnId => 
    columns.find(col => col.id === columnId)?.frozen
  );
  
  const nonFrozenColumnIds = orderedColumns.filter(columnId => 
    !columns.find(col => col.id === columnId)?.frozen
  );

  // Calculate total width of frozen columns
  const frozenColumnsWidth = frozenColumnIds.reduce((total, columnId) => {
    return total + (columnWidths[columnId] || 150);
  }, 0);

  return (
    <div className="overflow-x-auto relative">
      {/* Frozen Columns Table */}
      {frozenColumnIds.length > 0 && (
        <div 
          className="absolute top-0 left-0 z-10 bg-white dark:bg-gray-850 shadow-[4px_0_5px_-2px_rgba(0,0,0,0.1)] dark:shadow-[4px_0_5px_-2px_rgba(0,0,0,0.3)]" 
          style={{ width: `${frozenColumnsWidth}px` }}
        >
          <table className={`border-collapse ${className}`}>
            <colgroup>
              {frozenColumnIds.map(columnId => (
                <col key={columnId} style={{ width: `${columnWidths[columnId] || 150}px` }} />
              ))}
            </colgroup>
            
            <thead>
              <tr>
                {frozenColumnIds.map(columnId => {
                  const column = columns.find(c => c.id === columnId);
                  if (!column) return null;
                  
                  return (
                    <th
                      key={column.id}
                      className={`relative ${headerClassName} border-r border-gray-200 dark:border-gray-700 ${
                        dragOverColumn === column.id 
                          ? 'bg-blue-50 dark:bg-blue-900/20' 
                          : ''
                      } select-none`}
                      draggable
                      onDragStart={() => handleDragStart(column.id)}
                      onDragOver={(e) => handleDragOver(column.id, e)}
                      onDrop={() => handleDrop(column.id)}
                      onDragEnd={() => {
                        setDraggingColumn(null);
                        setDragOverColumn(null);
                      }}
                    >
                      <div className="flex items-center group">
                        <div 
                          className="p-0.5 mr-1 cursor-grab opacity-0 group-hover:opacity-100 transition-opacity"
                          title="Drag to reorder column"
                        >
                          <GripVertical className="h-3.5 w-3.5 text-gray-400 dark:text-gray-500" />
                        </div>
                        <div className="flex-1">{column.label}</div>
                        {/* Resize handle with visible line when hovered */}
                        <div
                          className={`absolute right-0 top-0 bottom-0 w-3 group cursor-col-resize flex items-center justify-center transition-opacity 
                            ${resizing?.columnId === column.id ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}`}
                          onMouseDown={(e) => handleResizeStart(column.id, e)}
                          title="Drag to resize column"
                        >
                          <div className="h-full w-0.5 bg-blue-500/50 dark:bg-blue-400/50 group-hover:opacity-100 transition-opacity"></div>
                        </div>
                      </div>
                    </th>
                  );
                })}
              </tr>
            </thead>
            
            {/* Simplified version that just renders the children */}
            <tbody>
              {frozenColumnIds.length > 0 && React.Children.map(children, (child) => {
                if (React.isValidElement(child) && child.props.children) {
                  return React.cloneElement(child as React.ReactElement<any>, {
                    children: React.Children.map(child.props.children, (row) => {
                      if (!React.isValidElement(row)) return null;
                      
                      // Skip day header rows
                      const rowCells = React.Children.toArray(row.props.children);
                      if (rowCells.length === 1) {
                        const firstCell = rowCells[0] as React.ReactElement;
                        if (firstCell.props && firstCell.props.colSpan) {
                          return null;
                        }
                      }
                      
                      // Only include cells for frozen columns
                      const cellsToInclude = rowCells.filter((_, idx) => {
                        return frozenColumnIds.includes(orderedColumns[idx]);
                      });
                      
                      // Add border-right to cells
                      const styledCells = cellsToInclude.map(cell => {
                        if (React.isValidElement(cell)) {
                          return React.cloneElement(cell as React.ReactElement<any>, {
                            className: `${cell.props.className || ''} border-r border-gray-200 dark:border-gray-700`
                          });
                        }
                        return cell;
                      });
                      
                      return React.cloneElement(row as React.ReactElement<any>, {
                        children: styledCells
                      });
                    })
                  });
                }
                return null;
              })}
            </tbody>
          </table>
        </div>
      )}

      {/* Main Table */}
      <table ref={tableRef} className={`w-full border-collapse ${className}`} style={{ marginLeft: frozenColumnIds.length > 0 ? `${frozenColumnsWidth}px` : '0' }}>
        <colgroup>
          {orderedColumns.map(columnId => (
            <col key={columnId} style={{ width: `${columnWidths[columnId] || 150}px` }} />
          ))}
        </colgroup>
        
        <thead>
          <tr>
            {orderedColumns.map(columnId => {
              const column = columns.find(c => c.id === columnId);
              if (!column) return null;
              
              // Skip frozen columns in the main header
              if (column.frozen) {
                return <th key={column.id} className="p-0 m-0 border-0" style={{ width: 0, padding: 0, margin: 0, border: 0 }}></th>;
              }
              
              return (
                <th
                  key={column.id}
                  className={`relative ${headerClassName} ${
                    dragOverColumn === column.id 
                      ? 'bg-blue-50 dark:bg-blue-900/20' 
                      : ''
                  } select-none`}
                  draggable
                  onDragStart={() => handleDragStart(column.id)}
                  onDragOver={(e) => handleDragOver(column.id, e)}
                  onDrop={() => handleDrop(column.id)}
                  onDragEnd={() => {
                    setDraggingColumn(null);
                    setDragOverColumn(null);
                  }}
                >
                  <div className="flex items-center group">
                    <div 
                      className="p-0.5 mr-1 cursor-grab opacity-0 group-hover:opacity-100 transition-opacity"
                      title="Drag to reorder column"
                    >
                      <GripVertical className="h-3.5 w-3.5 text-gray-400 dark:text-gray-500" />
                    </div>
                    <div className="flex-1">{column.label}</div>
                    {/* Resize handle with visible line when hovered */}
                    <div
                      className={`absolute right-0 top-0 bottom-0 w-3 group cursor-col-resize flex items-center justify-center transition-opacity 
                        ${resizing?.columnId === column.id ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}`}
                      onMouseDown={(e) => handleResizeStart(column.id, e)}
                      title="Drag to resize column"
                    >
                      <div className="h-full w-0.5 bg-blue-500/50 dark:bg-blue-400/50 group-hover:opacity-100 transition-opacity"></div>
                    </div>
                  </div>
                </th>
              );
            })}
          </tr>
        </thead>
        
        {children}
      </table>
    </div>
  );
};

// Hooks for saving column layout preferences
export const useColumnLayout = (tableId: string, defaultColumns: Column[]) => {
  const getStoredLayout = () => {
    try {
      const stored = localStorage.getItem(`table-layout-${tableId}`);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (e) {
      console.error('Failed to load column layout', e);
    }
    return null;
  };
  
  const defaultColumnIds = defaultColumns.map(c => c.id);
  const [orderedColumnIds, setOrderedColumnIds] = useState<string[]>(() => {
    const stored = getStoredLayout();
    return stored?.orderedColumnIds || defaultColumnIds;
  });
  
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>(() => {
    const stored = getStoredLayout();
    const defaultWidths = defaultColumns.reduce<Record<string, number>>((acc, col) => {
      acc[col.id] = col.width || 150;
      return acc;
    }, {});
    
    return stored?.columnWidths || defaultWidths;
  });
  
  // Save to localStorage when changes occur
  useEffect(() => {
    try {
      localStorage.setItem(`table-layout-${tableId}`, JSON.stringify({
        orderedColumnIds,
        columnWidths
      }));
    } catch (e) {
      console.error('Failed to save column layout', e);
    }
  }, [tableId, orderedColumnIds, columnWidths]);
  
  // Handlers
  const handleColumnReorder = (newOrderedColumnIds: string[]) => {
    setOrderedColumnIds(newOrderedColumnIds);
  };
  
  const handleColumnResize = (columnId: string, width: number) => {
    setColumnWidths(prev => ({
      ...prev,
      [columnId]: width
    }));
  };
  
  // Reset to defaults
  const resetLayout = () => {
    setOrderedColumnIds(defaultColumnIds);
    
    const defaultWidths = defaultColumns.reduce<Record<string, number>>((acc, col) => {
      acc[col.id] = col.width || 150;
      return acc;
    }, {});
    
    setColumnWidths(defaultWidths);
    
    // Clear stored layout
    try {
      localStorage.removeItem(`table-layout-${tableId}`);
    } catch (e) {
      console.error('Failed to clear column layout', e);
    }
  };
  
  return {
    orderedColumnIds,
    columnWidths,
    handleColumnReorder,
    handleColumnResize,
    resetLayout
  };
}; 