import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Check, 
  X,
  Calendar,
  ChevronDown,
  ChevronUp,
  DollarSign,
  Info
} from 'lucide-react';
import { TaxConfig, TaxSlab } from '../../../types/payroll';

interface TaxSlabConfigurationProps {
  isAdmin?: boolean;
}

const TaxSlabConfiguration: React.FC<TaxSlabConfigurationProps> = ({ isAdmin = false }) => {
  const [loading, setLoading] = useState(true);
  const [taxConfigs, setTaxConfigs] = useState<TaxConfig[]>([]);
  const [activeConfig, setActiveConfig] = useState<TaxConfig | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [expandedItems, setExpandedItems] = useState<number[]>([]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'PKR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Load mock data
  useEffect(() => {
    setLoading(true);
    
    // Simulate API call to fetch tax configurations
    setTimeout(() => {
      const mockTaxConfigs: TaxConfig[] = [
        {
          id: 1,
          name: "Standard Tax Slabs 2023-2024",
          description: "Income tax slabs for salaried individuals for fiscal year 2023-2024",
          effectiveDate: "2023-07-01",
          expiryDate: "2024-06-30",
          isActive: true,
          country: "Pakistan",
          taxType: "income",
          taxSlabs: [
            {
              id: 1,
              minAmount: 0,
              maxAmount: 600000,
              rate: 0,
              additionalAmount: 0,
              description: "No tax up to Rs. 600,000"
            },
            {
              id: 2,
              minAmount: 600001,
              maxAmount: 1200000,
              rate: 2.5,
              additionalAmount: 0,
              description: "2.5% of amount exceeding Rs. 600,000"
            },
            {
              id: 3,
              minAmount: 1200001,
              maxAmount: 2400000,
              rate: 12.5,
              additionalAmount: 15000,
              description: "Rs. 15,000 + 12.5% of amount exceeding Rs. 1,200,000"
            },
            {
              id: 4,
              minAmount: 2400001,
              maxAmount: 3600000,
              rate: 20,
              additionalAmount: 165000,
              description: "Rs. 165,000 + 20% of amount exceeding Rs. 2,400,000"
            },
            {
              id: 5,
              minAmount: 3600001,
              maxAmount: 6000000,
              rate: 25,
              additionalAmount: 405000,
              description: "Rs. 405,000 + 25% of amount exceeding Rs. 3,600,000"
            },
            {
              id: 6,
              minAmount: 6000001,
              maxAmount: 12000000,
              rate: 32.5,
              additionalAmount: 1005000,
              description: "Rs. 1,005,000 + 32.5% of amount exceeding Rs. 6,000,000"
            },
            {
              id: 7,
              minAmount: 12000001,
              maxAmount: null,
              rate: 35,
              additionalAmount: 2955000,
              description: "Rs. 2,955,000 + 35% of amount exceeding Rs. 12,000,000"
            }
          ],
          createdAt: "2023-06-15"
        },
        {
          id: 2,
          name: "Business Income Tax 2023-2024",
          description: "Income tax slabs for business individuals for fiscal year 2023-2024",
          effectiveDate: "2023-07-01",
          expiryDate: "2024-06-30",
          isActive: true,
          country: "Pakistan",
          taxType: "income",
          taxSlabs: [
            {
              id: 1,
              minAmount: 0,
              maxAmount: 400000,
              rate: 0,
              additionalAmount: 0,
              description: "No tax up to Rs. 400,000"
            },
            {
              id: 2,
              minAmount: 400001,
              maxAmount: 800000,
              rate: 5,
              additionalAmount: 0,
              description: "5% of amount exceeding Rs. 400,000"
            },
            {
              id: 3,
              minAmount: 800001,
              maxAmount: 1200000,
              rate: 10,
              additionalAmount: 20000,
              description: "Rs. 20,000 + 10% of amount exceeding Rs. 800,000"
            },
            {
              id: 4,
              minAmount: 1200001,
              maxAmount: 2400000,
              rate: 15,
              additionalAmount: 60000,
              description: "Rs. 60,000 + 15% of amount exceeding Rs. 1,200,000"
            },
            {
              id: 5,
              minAmount: 2400001,
              maxAmount: 3000000,
              rate: 20,
              additionalAmount: 240000,
              description: "Rs. 240,000 + 20% of amount exceeding Rs. 2,400,000"
            },
            {
              id: 6,
              minAmount: 3000001,
              maxAmount: 5000000,
              rate: 25,
              additionalAmount: 360000,
              description: "Rs. 360,000 + 25% of amount exceeding Rs. 3,000,000"
            },
            {
              id: 7,
              minAmount: 5000001,
              maxAmount: null,
              rate: 30,
              additionalAmount: 860000,
              description: "Rs. 860,000 + 30% of amount exceeding Rs. 5,000,000"
            }
          ],
          createdAt: "2023-06-15"
        }
      ];
      
      setTaxConfigs(mockTaxConfigs);
      if (mockTaxConfigs.length > 0) {
        setActiveConfig(mockTaxConfigs[0]);
        setExpandedItems([mockTaxConfigs[0].id]);
      }
      setLoading(false);
    }, 1000);
  }, []);

  const toggleExpand = (id: number) => {
    if (expandedItems.includes(id)) {
      setExpandedItems(expandedItems.filter(itemId => itemId !== id));
    } else {
      setExpandedItems([...expandedItems, id]);
    }
  };

  const handleAddTaxConfig = () => {
    setShowAddModal(true);
  };

  const handleEditTaxConfig = (config: TaxConfig) => {
    setActiveConfig(config);
    setShowEditModal(true);
  };

  const handleDeleteTaxConfig = (id: number) => {
    if (window.confirm("Are you sure you want to delete this tax configuration?")) {
      setTaxConfigs(taxConfigs.filter(config => config.id !== id));
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div>
      {/* Header and Actions */}
      <div className="mb-6 flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Tax Slab Configurations</h3>
        {isAdmin && (
          <button
            onClick={handleAddTaxConfig}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Tax Configuration
          </button>
        )}
      </div>
      
      {/* Tax Configuration List */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        {taxConfigs.length === 0 ? (
          <div className="p-6 text-center">
            <Info className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No tax configurations</h3>
            <p className="mt-1 text-sm text-gray-500">
              There are no tax configurations to display.
            </p>
          </div>
        ) : (
          <ul className="divide-y divide-gray-200">
            {taxConfigs.map((config) => (
              <li key={config.id}>
                <div className="px-4 py-5 sm:px-6">
                  <div className="flex justify-between items-center cursor-pointer" onClick={() => toggleExpand(config.id)}>
                    <div>
                      <h4 className="text-md font-semibold text-blue-600">{config.name}</h4>
                      <p className="text-sm text-gray-500">{config.description}</p>
                      <div className="mt-1 flex items-center text-sm text-gray-500">
                        <Calendar className="h-4 w-4 mr-1" />
                        <span>
                          {new Date(config.effectiveDate).toLocaleDateString()} - 
                          {config.expiryDate ? new Date(config.expiryDate).toLocaleDateString() : 'No expiry'}
                        </span>
                        <span className={`ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          config.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {config.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center">
                      {isAdmin && (
                        <div className="flex space-x-2 mr-4">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleEditTaxConfig(config);
                            }}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteTaxConfig(config.id);
                            }}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      )}
                      {expandedItems.includes(config.id) ? (
                        <ChevronUp className="h-5 w-5 text-gray-400" />
                      ) : (
                        <ChevronDown className="h-5 w-5 text-gray-400" />
                      )}
                    </div>
                  </div>
                  
                  {/* Tax Slabs Detailed View */}
                  {expandedItems.includes(config.id) && (
                    <div className="mt-4 bg-gray-50 p-4 rounded-md">
                      <h5 className="text-sm font-medium text-gray-700 mb-3">Tax Slabs</h5>
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead>
                            <tr>
                              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Income Range
                              </th>
                              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Tax Rate
                              </th>
                              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Additional Amount
                              </th>
                              <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Description
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {config.taxSlabs.map((slab) => (
                              <tr key={slab.id}>
                                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700">
                                  {formatCurrency(slab.minAmount)} - {slab.maxAmount ? formatCurrency(slab.maxAmount) : 'and above'}
                                </td>
                                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700">
                                  {slab.rate}%
                                </td>
                                <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700">
                                  {formatCurrency(slab.additionalAmount)}
                                </td>
                                <td className="px-4 py-2 text-sm text-gray-700">
                                  {slab.description}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
      
      {/* Modal placeholders - in a full implementation, these would be proper forms */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Add Tax Configuration</h3>
              <button
                onClick={() => setShowAddModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            <div className="space-y-4">
              <p className="text-gray-500">Form fields for tax configuration details would go here...</p>
            </div>
            <div className="mt-5 flex justify-end">
              <button
                type="button"
                onClick={() => setShowAddModal(false)}
                className="mr-3 inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={() => setShowAddModal(false)}
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
      
      {showEditModal && activeConfig && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Edit Tax Configuration</h3>
              <button
                onClick={() => setShowEditModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            <div className="space-y-4">
              <p className="text-gray-500">Form fields for tax configuration details would go here...</p>
            </div>
            <div className="mt-5 flex justify-end">
              <button
                type="button"
                onClick={() => setShowEditModal(false)}
                className="mr-3 inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={() => setShowEditModal(false)}
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaxSlabConfiguration; 