import { MigrationInterface, QueryRunner } from "typeorm";

export class RemovePrimaryAssignedUser1710000000010 implements MigrationInterface {
    name = 'RemovePrimaryAssignedUser1710000000010';

    public async up(queryRunner: QueryRunner): Promise<void> {
        try {
            // Check if the foreign key exists first
            const foreignKeys = await queryRunner.query(
                `SELECT CONSTRAINT_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'software_license' 
                AND COLUMN_NAME = 'assignedToId'`
            );
            
            // If foreign key exists, drop it
            if (foreignKeys && foreignKeys.length > 0) {
                for (const fk of foreignKeys) {
                    if (fk.CONSTRAINT_NAME) {
                        await queryRunner.query(
                            `ALTER TABLE software_license DROP FOREIGN KEY \`${fk.CONSTRAINT_NAME}\``
                        );
                    }
                }
            }
            
            // Then drop the column
            await queryRunner.query(
                `ALTER TABLE software_license DROP COLUMN assignedToId`
            );
        } catch (error) {
            console.error('Error in migration:', error);
            // If the column doesn't exist, that's fine too
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Add the column back
        await queryRunner.query(
            `ALTER TABLE software_license ADD COLUMN assignedToId VARCHAR(36) NULL`
        );

        // Re-add the foreign key
        await queryRunner.query(
            `ALTER TABLE software_license 
            ADD CONSTRAINT \`FK_software_license_assignedToId\` 
            FOREIGN KEY (assignedToId) REFERENCES user(id) ON DELETE SET NULL`
        );
    }
} 