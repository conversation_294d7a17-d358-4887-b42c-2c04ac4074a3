const mysql = require('mysql2/promise');
require('dotenv').config();

async function createEmployeeRelations() {
  const {
    DB_HOST = 'localhost',
    DB_PORT = '3306',
    DB_USER = 'root',
    DB_PASSWORD = 'root',
    DB_NAME = 'ims_db'
  } = process.env;

  console.log('Connecting to database...');

  try {
    // Create connection
    const connection = await mysql.createConnection({
      host: DB_HOST,
      port: parseInt(DB_PORT, 10),
      user: DB_USER,
      password: DB_PASSWORD,
      database: DB_NAME
    });

    console.log('Connected to database successfully');

    // Check if the employee exists
    const [employees] = await connection.query(
      'SELECT * FROM employees WHERE id = 1'
    );

    if (employees.length === 0) {
      console.error('Employee with ID 1 does not exist. Please create an employee first.');
      await connection.end();
      return;
    }

    console.log(`Found employee: ${employees[0].firstName} ${employees[0].lastName}`);

    // Check the table structure for employee_contacts
    const [contactColumns] = await connection.query(
      `SHOW COLUMNS FROM employee_contacts`
    );
    console.log('Contact table columns:', contactColumns.map(col => col.Field));

    // Check the table structure for employee_jobs
    const [jobColumns] = await connection.query(
      `SHOW COLUMNS FROM employee_jobs`
    );
    console.log('Job table columns:', jobColumns.map(col => col.Field));

    // Check if contact record already exists
    const [existingContact] = await connection.query(
      'SELECT * FROM employee_contacts WHERE employeeId = 1'
    );

    if (existingContact.length > 0) {
      console.log('Contact information already exists for this employee. Updating...');
      await connection.query(
        `UPDATE employee_contacts SET 
         mobileNumber = ?, 
         officialNumber = ?, 
         officialEmail = ?, 
         personalEmail = ?, 
         permanentAddress = ?, 
         currentAddress = ?, 
         emergencyContactName = ?, 
         emergencyContactPhone = ?, 
         emergencyContactRelationship = ? 
         WHERE employeeId = 1`,
        [
          '+923001234567',
          '+924235678901',
          '<EMAIL>',
          '<EMAIL>',
          '123 Main Street, Lahore, Pakistan',
          '456 Work Avenue, Lahore, Pakistan',
          'Ahmed Khan',
          '+923009876543',
          'Friend'
        ]
      );
      console.log('Contact information updated');
    } else {
      console.log('Creating new contact information for the employee...');
      // Insert contact information
      await connection.query(
        `INSERT INTO employee_contacts (
          employeeId, 
          mobileNumber, 
          officialNumber, 
          officialEmail, 
          personalEmail, 
          permanentAddress, 
          currentAddress, 
          emergencyContactName, 
          emergencyContactPhone, 
          emergencyContactRelationship,
          createdAt,
          updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          1, // employeeId
          '+923001234567',
          '+924235678901',
          '<EMAIL>',
          '<EMAIL>',
          '123 Main Street, Lahore, Pakistan',
          '456 Work Avenue, Lahore, Pakistan',
          'Ahmed Khan',
          '+923009876543',
          'Friend'
        ]
      );
      console.log('Contact information created');
    }

    // Check if job record already exists
    const [existingJob] = await connection.query(
      'SELECT * FROM employee_jobs WHERE employeeId = 1'
    );

    if (existingJob.length > 0) {
      console.log('Job information already exists for this employee. Updating...');
      await connection.query(
        `UPDATE employee_jobs SET 
         designation = ?, 
         department = ?, 
         project = ?, 
         location = ?, 
         employmentType = ?, 
         employmentStatus = ?, 
         employeeLevel = ?, 
         joinDate = ?, 
         probationEndDate = ?, 
         noticePeriod = ?, 
         reportingTo = ? 
         WHERE employeeId = 1`,
        [
          'Software Developer',
          'IT',
          'IT Management System',
          'Lahore Office',
          'Full-time',
          'active',
          'Junior',
          '2025-01-15',
          '2025-04-15',
          '30 days',
          'John Doe'
        ]
      );
      console.log('Job information updated');
    } else {
      console.log('Creating new job information for the employee...');
      // Insert job information
      await connection.query(
        `INSERT INTO employee_jobs (
          employeeId, 
          designation, 
          department, 
          project, 
          location, 
          employmentType, 
          employmentStatus, 
          employeeLevel, 
          joinDate, 
          probationEndDate, 
          noticePeriod, 
          reportingTo,
          createdAt,
          updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          1, // employeeId
          'Software Developer',
          'IT',
          'IT Management System',
          'Lahore Office',
          'Full-time',
          'active',
          'Junior',
          '2025-01-15',
          '2025-04-15',
          '30 days',
          'John Doe'
        ]
      );
      console.log('Job information created');
    }

    console.log('Employee relations created/updated successfully');
    await connection.end();
    console.log('Connection closed');
  } catch (error) {
    console.error('Error:', error);
  }
}

createEmployeeRelations(); 