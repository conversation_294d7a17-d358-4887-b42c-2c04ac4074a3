import { AppDataSource } from '../config/database';
import { LeaveBalance } from '../entities/LeaveBalance';
import { LeaveAllocation } from '../entities/LeaveAllocation';

async function createMissingLeaveBalances() {
  try {
    console.log('🔧 Creating missing leave balances from allocations...');
    
    await AppDataSource.initialize();
    console.log('✅ Database connected');
    
    const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);
    const leaveAllocationRepository = AppDataSource.getRepository(LeaveAllocation);
    
    // Get all allocations that don't have corresponding balances
    const allocations = await leaveAllocationRepository.find({
      where: { year: 2025, isActive: true }
    });
    
    console.log(`\n📊 Found ${allocations.length} allocations to process`);
    
    let created = 0;
    let updated = 0;
    
    for (const allocation of allocations) {
      // Check if balance already exists
      const existingBalance = await leaveBalanceRepository.findOne({
        where: {
          employeeId: allocation.employeeId,
          leaveType: allocation.leaveType,
          year: allocation.year,
          isActive: true
        }
      });
      
      if (existingBalance) {
        // Update existing balance to match allocation
        existingBalance.totalAllocated = allocation.totalAllocated;
        existingBalance.carriedForward = allocation.carriedForward;
        // remaining is calculated automatically from totalAllocated + carriedForward - used - pending
        
        await leaveBalanceRepository.save(existingBalance);
        updated++;
        
        console.log(`✅ Updated balance for Employee ${allocation.employeeId} - ${allocation.leaveType}: ${allocation.totalAllocated} days (remaining: ${existingBalance.remaining})`);
      } else {
        // Create new balance from allocation
        const newBalance = leaveBalanceRepository.create({
          employeeId: allocation.employeeId,
          leaveType: allocation.leaveType,
          year: allocation.year,
          totalAllocated: allocation.totalAllocated,
          carriedForward: allocation.carriedForward,
          used: 0,
          pending: 0,
          isActive: true,
          notes: `Generated from allocation (${allocation.source})`
        });
        
        await leaveBalanceRepository.save(newBalance);
        created++;
        
        console.log(`🆕 Created balance for Employee ${allocation.employeeId} - ${allocation.leaveType}: ${allocation.totalAllocated} days (remaining: ${newBalance.remaining})`);
      }
    }
    
    console.log(`\n✅ Completed: Created ${created} new balances, Updated ${updated} existing balances`);
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await AppDataSource.destroy();
  }
}

createMissingLeaveBalances(); 