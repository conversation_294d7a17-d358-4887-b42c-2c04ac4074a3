import express from 'express';
import { TaskController } from '../controllers/taskController';
import { TaskTimeTrackingController } from '../controllers/taskTimeTrackingController';
import { TaskTemplateController } from '../controllers/taskTemplateController';
import { RecurringTaskController } from '../controllers/recurringTaskController';
import { requireAuth } from '../middleware/auth';

const router = express.Router();
const taskController = new TaskController();
const timeTrackingController = new TaskTimeTrackingController();
const templateController = new TaskTemplateController();
const recurringTaskController = new RecurringTaskController();

// Apply authentication middleware to all routes
router.use(requireAuth);

// Basic Task routes
router.get('/', taskController.getAllTasks.bind(taskController));
router.get('/stats', taskController.getTaskStats.bind(taskController));
router.get('/my-tasks', taskController.getMyTasks.bind(taskController));
router.get('/project/:projectId', taskController.getTasksByProject.bind(taskController));
router.get('/:id', taskController.getTaskById.bind(taskController));
router.post('/', taskController.createTask.bind(taskController));
router.put('/:id', taskController.updateTask.bind(taskController));
router.delete('/:id', taskController.deleteTask.bind(taskController));

// Enhanced Task Management routes
router.post('/dependencies', taskController.addTaskDependency.bind(taskController));
router.delete('/dependencies', taskController.removeTaskDependency.bind(taskController));
router.post('/:parentTaskId/subtasks', taskController.createSubtask.bind(taskController));
router.put('/:id/checklist', taskController.updateTaskChecklist.bind(taskController));

// Time Tracking routes
router.post('/:taskId/time/start', timeTrackingController.startTimer.bind(timeTrackingController));
router.post('/:taskId/time/stop', timeTrackingController.stopTimer.bind(timeTrackingController));
router.post('/:taskId/time/manual', timeTrackingController.addManualTimeEntry.bind(timeTrackingController));
router.get('/:taskId/time', timeTrackingController.getTaskTimeEntries.bind(timeTrackingController));
router.get('/time/my-entries', timeTrackingController.getUserTimeEntries.bind(timeTrackingController));
router.delete('/time/:id', timeTrackingController.deleteTimeEntry.bind(timeTrackingController));

// Template routes
router.get('/templates', templateController.getAllTemplates.bind(templateController));
router.get('/templates/categories', templateController.getTemplateCategories.bind(templateController));
router.get('/templates/:id', templateController.getTemplateById.bind(templateController));
router.post('/templates', templateController.createTemplate.bind(templateController));
router.put('/templates/:id', templateController.updateTemplate.bind(templateController));
router.delete('/templates/:id', templateController.deleteTemplate.bind(templateController));
router.post('/templates/:templateId/create-task', templateController.createTaskFromTemplate.bind(templateController));

// Recurring Task routes
router.get('/recurring', recurringTaskController.getRecurringTasks.bind(recurringTaskController));
router.get('/recurring/types', recurringTaskController.getRecurrenceTypes.bind(recurringTaskController));
router.get('/recurring/:id', recurringTaskController.getRecurringTaskById.bind(recurringTaskController));
router.get('/recurring/:id/instances', recurringTaskController.getTaskInstances.bind(recurringTaskController));
router.post('/recurring', recurringTaskController.createRecurringTask.bind(recurringTaskController));
router.put('/recurring/:id', recurringTaskController.updateRecurringTask.bind(recurringTaskController));
router.delete('/recurring/:id', recurringTaskController.stopRecurringTask.bind(recurringTaskController));

export default router;
