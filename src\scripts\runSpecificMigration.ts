import { AppDataSource } from '../config/database';
import { CreateLeavePolicyTables1703000000000 } from '../migrations/CreateLeavePolicyTables';

async function runSpecificMigration() {
  try {
    console.log('🚀 Starting Leave Policy Tables Migration...');
    
    // Initialize database connection
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ Database connection established');
    }

    // Create migration instance
    const migration = new CreateLeavePolicyTables1703000000000();
    const queryRunner = AppDataSource.createQueryRunner();
    
    await queryRunner.connect();
    
    try {
      // Check if tables already exist
      const hasLeavePolicySettings = await queryRunner.hasTable('leave_policy_settings');
      const hasLeavePolicyConfigurations = await queryRunner.hasTable('leave_policy_configurations');
      
      if (hasLeavePolicySettings && hasLeavePolicyConfigurations) {
        console.log('✅ Leave policy tables already exist');
      } else {
        console.log('📋 Creating leave policy tables...');
        await migration.up(queryRunner);
        console.log('✅ Leave policy tables created successfully!');
      }
      
      console.log('📋 Summary:');
      console.log('   - Leave policy settings table ready');
      console.log('   - Leave policy configurations table ready');
      console.log('   - Leave type policies table ready');
      console.log('   - Accrual rules table ready');
      console.log('   - Holiday calendars table ready');
      console.log('   - Database schema matches API requirements');
      
    } finally {
      await queryRunner.release();
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  runSpecificMigration();
}

export { runSpecificMigration }; 