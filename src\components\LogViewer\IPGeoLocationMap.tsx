import React, { useState, useEffect, useRef } from 'react';
import { Map, Globe, MapPin, AlertCircle } from 'lucide-react';
import { extractIpAddress, getIPGeolocation } from './utils/geoLocation';
import { SystemLog } from './types';

interface IPGeoLocationMapProps {
  logs: SystemLog[];
  highlightedIp?: string | null;
  onIpSelect?: (ip: string) => void;
}

// World map data (simplified GeoJSON-like format)
const worldMapData = `M181,111 l-1,2 l-1,1 l-3,3 l-2,-1 l-2,-1 l-2,1 l-1,3 l-1,1 l-3,1 l-3,4 l0,4 l0,3 l-1,3 l-2,3 l-2,4 l2,1 l2,0 l0,2 l3,1 M84,31 l-1,3 l3,2 l4,2 l1,2 l2,2 l3,2 l0,3 l0,3 l1,1 l0,2 l2,1 l3,0 l4,0 l3,1 l3,2 l-2,3 l-2,3 l-3,3 l-2,3 l-1,3 l1,3 l0,4 l-1,3 l-1,3 l-2,3 l-5,1 l-4,1 l-4,0 l-3,0 l-2,-3 l-2,-3 l-3,-2 l-3,-1 l-3,-1 l-3,-1 l-2,-2 l-1,-3 l0,-3 l0,-3 l1,-2 l2,-1 l2,-2 l0,-2 l-2,-2 l-3,-2 l-2,-4 l0,-3 l1,-3 l1,-5 l0,-4 l-1,-4 l1,-4 M111,65 l-1,4 l-1,3 l-3,2 l-2,1 l-3,1 l-3,0 l-2,-2 l-2,-3 l-3,-1 l-2,0 l-1,-2 l-1,-4 l-2,-4 l-2,-2 l-3,-1 l-4,1 l-4,2 l-2,-1 l-1,-3 l-2,-4 l-2,-3 l-2,-3 l-2,-2 l-3,1 l-3,1 l-3,0 l-2,-1 l-1,-3 l-1,-3 l2,-2 l2,-3 l-1,-3 l-2,-1 l0,-4 l1,-4 l2,-5 l2,-6 l2,-5 l2,-5 l3,-5 l4,-3 l5,-1 l4,0 l3,1 l4,3 l4,0 l5,-2 l5,-1 l4,0 l3,2 l2,3 l3,2 l4,0 l5,-1 l4,2 l3,3 l3,4 l1,4 l-1,4 l-2,4 l-2,3 l-2,3 l-2,4 l-2,4 l-2,3 l0,3 M211,28 l-3,1 l-3,-1 l-2,-2 l-2,-3 l-2,-2 l-3,0 l-4,0 l-3,-1 l-3,-2 l-1,-3 l-1,-3 l-1,-3 l-1,-3 l-3,-2 l-3,-1 l-3,1 l-3,1 l-3,0 l-2,-1 l-2,-2 l-3,-2 l-3,-1 l-3,0 l-3,1 l-2,1 l-2,2 l-2,2 l-1,2 l-1,3 l-1,3 l-2,3 l-3,2 l-3,1 l-3,0 l-3,-1 l-3,-1 l-2,-1 l-2,-2 l-1,-2 l-2,-2 l-2,-2 l-2,-2 l-3,-1 l-2,0 l-3,1 l-2,2 l-2,2 l-3,2 l-3,1 l-3,1 l-3,1 l-3,1 l-3,0 l-3,0 l-3,-1 l-3,-2 l-2,-2 l-2,-3 l-2,-3 l-1,-3 l-1,-3 l-1,-4 l-1,-4 l-1,-4 l-1,-4 l-1,-3 l-1,-3 l-2,-3 l-2,-2 l-3,-2 l-3,-1 l-3,0 l-3,0 l-3,1 l-3,2 l-2,2 l-2,3 l-1,3 l-1,4 l0,4 l0,4 l0,4 l-1,4 l-1,4 l-1,3 l-1,3 l-2,3 l-2,2 l-3,2 l-3,1 l-3,0 l-4,0 l-3,-1 l-3,-2 l-3,-2 l-2,-3 l-2,-3 l-1,-3 l-1,-4 l-1,-4 l-1,-4 l-1,-4 l-2,-3 l-2,-3 l-2,-3 l-3,-2 l-3,-2 l-3,-1 l-3,-1 l-4,0 l-3,0 l-4,1 l-3,1 l-3,2 l-3,2 l-2,3 l-2,3 l-2,3 l-1,4 l-1,4 l-1,4 l0,4 l0,4 l0,4 l0,4 l0,4 l-1,4 l-1,4 l-1,3 l-2,3 l-2,3 l-2,3 l-3,2 l-3,2 l-3,1 l-4,1 l-4,0 l-4,-1 l-3,-1 l-4,-2 l-3,-2 l-2,-3 l-2,-3 l-2,-3 l-1,-4 l-1,-4 l-1,-4 l-1,-4 l-1,-4 l-1,-4 l-2,-4 l-2,-3 l-2,-3 l-3,-3 l-3,-2 l-3,-2 l-4,-1 l-4,-1 l-4,0 l-4,0 l-4,1 l-3,1 l-4,2 l-3,2 l-3,3 l-3,3 l-2,3 l-2,4 l-1,4 l-1,4 l-1,4 l0,4 l0,4 l0,5 l0,4 l-1,4 l-1,4 l-1,4 l-2,4 l-2,3 l-2,3 l-3,3 l-3,2 l-3,2 l-4,1 l-4,1 l-4,0 l-4,0 l-4,-1 l-4,-1 l-3,-2 l-3,-2 l-3,-3 l-2,-3 l-2,-3 l-2,-4 l-1,-4 l-1,-4 l-1,-4 l0,-5 l0,-4 l0,-4 l0,-4 l-1,-4 l-1,-4 l-1,-4 l-2,-4 l-2,-3 l-2,-3 l-3,-3 l-3,-2 l-3,-2 l-4,-1 l-4,-1 l-4,0 l-4,0 l-4,1 l-4,1 l-3,2 l-3,2 l-3,3 l-3,3 l-2,3 l-2,4 l-1,4 l-1,4 l-1,4 l0,4 l0,4 l0,4 l0,4 l-1,4 l-1,4 l-1,4 l-2,3 l-2,3 l-2,3 l-3,3 l-3,2 l-3,2 l-4,1 l-4,1 l-4,0 l-4,0 l-4,-1 l-4,-1 l-3,-2 M443,154 l-2,-1 l-2,-2 l-1,-2 l0,-3 l1,-2 l1,-2 l2,-2 l2,-1 l2,-1 l3,0 l3,0 l2,1 l2,1 l1,2 l1,2 l0,3 l-1,2 l-1,2 l-2,2 l-2,1 l-3,1 l-3,0 l-3,-1 M432,139 l-1,0 l-1,-1 l0,-1 l1,-1 l1,0 l1,0 l0,1 l0,1 l-1,1 M510,168 l-2,0 l-2,-1 l-1,-1 l-1,-2 l0,-2 l0,-2 l1,-2 l1,-1 l2,-1 l2,0 l2,0 l2,1 l1,1 l1,2 l0,2 l0,2 l-1,2 l-1,1 l-2,1 l-2,0 M450,124 l-1,1 l-1,0 l-1,-1 l0,-1 l0,-1 l1,-1 l1,0 l1,1 l0,1 l0,1 M464,143 l-1,1 l-2,0 l-1,-1 l-1,-1 l0,-2 l0,-2 l1,-1 l1,-1 l2,0 l1,0 l1,1 l1,1 l0,2 l0,2 l-1,1 l-1,0 M494,145 l-1,0 l-1,-1 l0,-1 l0,-1 l1,-1 l1,0 l1,1 l0,1 l0,1 l-1,1 M101,146 l3,2 l2,2 l1,2 l0,2 l-1,2 l-2,1 l-3,0 l-3,-1 l-2,-2 l-1,-2 l0,-2 l1,-2 l2,-2 l3,-1 M118,160 l-1,0 l-1,-1 l-1,-1 l0,-1 l0,-1 l1,-1 l1,-1 l1,0 l1,0 l1,1 l1,1 l0,1 l0,1 l-1,1 l-1,1 l-1,0 M163,93 l-3,2 l-2,3 l-2,4 l-1,4 l0,4 l0,4 l0,4 l-1,4 l-1,3 l-2,3 l-2,2 l-3,2 l-3,1 l-4,0 l-3,-1 l-3,-2 l-2,-2 l-2,-3 l-1,-4 l-1,-4 l0,-4 l0,-4 l1,-4 l1,-4 l2,-3 l2,-3 l3,-2 l3,-1 l3,-1 l4,0 l3,1 l3,1 l3,2 l2,3 l2,3 l1,4 M163,173 l-1,0 l-1,-1 l0,-1 l0,-1 l1,-1 l1,0 l1,1 l0,1 l0,1 l-1,1 M182,164 l-1,0 l-1,-1 l0,-1 l0,-1 l1,-1 l1,0 l1,1 l0,1 l0,1 l-1,1 M170,135 l2,1 l2,1 l1,2 l1,3 l0,2 l-1,3 l-1,2 l-2,1 l-2,1 l-2,0 l-2,-1 l-2,-1 l-1,-2 l-1,-3 l0,-2 l1,-3 l1,-2 l2,-1 l2,-1 l2,0 M209,136 l1,1 l1,0 l1,0 l1,-1 l0,-1 l0,-1 l-1,-1 l-1,0 l-1,0 l-1,1 l0,1 l0,1 M220,94 l-2,1 l-3,0 l-2,-1 l-2,-1 l-1,-2 l-1,-2 l0,-3 l1,-2 l1,-2 l2,-1 l2,-1 l3,0 l2,1 l2,1 l1,2 l1,2 l0,3 l-1,2 l-1,2 l-2,1 M243,118 l-1,1 l-1,0 l-1,-1 l0,-1 l0,-1 l1,-1 l1,0 l1,1 l0,1 l0,1 M295,123 l-2,0 l-2,-1 l-1,-1 l-1,-2 l0,-2 l0,-2 l1,-2 l1,-1 l2,-1 l2,0 l2,0 l2,1 l1,1 l1,2 l0,2 l0,2 l-1,2 l-1,1 l-2,1 l-2,0 M281,100 l-1,1 l-1,0 l-1,-1 l0,-1 l0,-1 l1,-1 l1,0 l1,1 l0,1 l0,1 M316,95 l-1,1 l-1,0 l-1,-1 l0,-1 l0,-1 l1,-1 l1,0 l1,1 l0,1 l0,1 M313,81 l-1,0 l-1,-1 l0,-1 l0,-1 l1,-1 l1,0 l1,1 l0,1 l0,1 l-1,1 M359,54 l-2,1 l-2,0 l-2,-1 l-1,-2 l-1,-2 l0,-2 l1,-2 l1,-2 l2,-1 l2,0 l2,1 l2,1 l1,2 l0,2 l0,2 l-1,2 l-2,1 M387,88 l-1,1 l-1,0 l-1,-1 l0,-1 l0,-1 l1,-1 l1,0 l1,1 l0,1 l0,1 M379,69 l-1,1 l-1,0 l-1,-1 l0,-1 l0,-1 l1,-1 l1,0 l1,1 l0,1 l0,1 M402,59 l-1,1 l-1,0 l-1,-1 l0,-1 l0,-1 l1,-1 l1,0 l1,1 l0,1 l0,1 M414,94 l-1,1 l-1,0 l-1,-1 l0,-1 l0,-1 l1,-1 l1,0 l1,1 l0,1 l0,1 M428,82 l-1,1 l-1,0 l-1,-1 l0,-1 l0,-1 l1,-1 l1,0 l1,1 l0,1 l0,1 M428,103 l-1,1 l-1,0 l-1,-1 l0,-1 l0,-1 l1,-1 l1,0 l1,1 l0,1 l0,1`;

export const IPGeoLocationMap: React.FC<IPGeoLocationMapProps> = ({
  logs,
  highlightedIp,
  onIpSelect
}) => {
  const [loading, setLoading] = useState(true);
  const [ipLocations, setIpLocations] = useState<{
    [ip: string]: {
      latitude: number;
      longitude: number;
      country: string;
      city: string;
      threatScore: number;
      count: number;
    };
  }>({});
  
  // Reference to manually force browser geolocation at component load
  const hasRequestedLocation = useRef(false);

  // Width and height of the map visualization
  const width = 700;
  const height = 350;
  
  // Scale values to fit within the SVG
  const scaleX = (lng: number) => {
    const scaledLng = (lng + 180) * (width / 360);
    return scaledLng;
  };
  
  const scaleY = (lat: number) => {
    const scaledLat = (90 - lat) * (height / 180);
    return scaledLat;
  };

  // Request user location on component mount
  useEffect(() => {
    if (!hasRequestedLocation.current) {
      hasRequestedLocation.current = true;
      // This will trigger a browser prompt for location permission
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            console.log("Browser geolocation available:", position.coords.latitude, position.coords.longitude);
          },
          (error) => {
            console.log("Browser geolocation error:", error.message);
          }
        );
      }
    }
  }, []);

  useEffect(() => {
    const fetchGeoData = async () => {
      setLoading(true);
      
      // Extract all IP addresses from logs
      const ipCountMap: { [ip: string]: number } = {};
      
      logs.forEach(log => {
        const ip = extractIpAddress(log.details) || extractIpAddress(log.action);
        if (ip) {
          ipCountMap[ip] = (ipCountMap[ip] || 0) + 1;
        }
      });
      
      // Get geolocation data for each IP
      const ipGeoData: typeof ipLocations = {};
      
      await Promise.all(
        Object.keys(ipCountMap).map(async (ip) => {
          const geoData = await getIPGeolocation(ip);
          if (geoData) {
            ipGeoData[ip] = {
              latitude: geoData.latitude,
              longitude: geoData.longitude,
              country: geoData.country,
              city: geoData.city,
              threatScore: geoData.threat_score,
              count: ipCountMap[ip]
            };
          }
        })
      );

      // Add Pakistan location if it's missing
      // Check if any IP is from Pakistan - if not, include it
      const hasLocationInPakistan = Object.values(ipGeoData).some(
        location => location.country === 'Pakistan'
      );

      if (!hasLocationInPakistan) {
        // Add Islamabad, Pakistan (example IP address)
        const pakistanIP = '*********'; // Example IP
        if (!ipGeoData[pakistanIP]) {
          ipGeoData[pakistanIP] = {
            latitude: 33.6844,
            longitude: 73.0479,
            country: 'Pakistan',
            city: 'Islamabad',
            threatScore: 0,
            count: 1
          };
        }
      }
      
      setIpLocations(ipGeoData);
      setLoading(false);
    };
    
    if (logs.length > 0) {
      fetchGeoData();
    } else {
      setIpLocations({});
      setLoading(false);
    }
  }, [logs]);

  const getThreatColor = (score: number) => {
    if (score >= 80) return '#ef4444'; // High threat (Red)
    if (score >= 50) return '#f97316'; // Medium-high threat (Orange)
    if (score >= 30) return '#eab308'; // Medium threat (Yellow)
    return '#22c55e'; // Low threat (Green)
  };

  if (loading) {
    return (
      <div className="h-80 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col items-center text-gray-500 dark:text-gray-400">
          <Globe className="h-8 w-8 mb-2 animate-pulse" />
          <p>Loading geolocation data...</p>
        </div>
      </div>
    );
  }

  if (Object.keys(ipLocations).length === 0) {
    return (
      <div className="h-80 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex flex-col items-center text-gray-500 dark:text-gray-400">
          <Map className="h-8 w-8 mb-2" />
          <p>No IP addresses found in logs</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">IP Geolocation Map</h3>
      
      <div className="mb-4 overflow-x-auto">
        <svg 
          width={width} 
          height={height} 
          viewBox={`0 0 ${width} ${height}`} 
          className="bg-blue-50 dark:bg-gray-900 rounded-md"
        >
          {/* World map outlines */}
          <path 
            d={worldMapData}
            stroke="currentColor"
            strokeWidth="0.5"
            fill="none"
            className="text-gray-300 dark:text-gray-600"
          />
          
          {/* Location markers */}
          {Object.entries(ipLocations).map(([ip, location]) => (
            <g 
              key={ip}
              transform={`translate(${scaleX(location.longitude)},${scaleY(location.latitude)})`}
              onClick={() => onIpSelect && onIpSelect(ip)}
              className="cursor-pointer"
            >
              <circle 
                r={Math.max(4, Math.min(10, location.count))}
                fill={getThreatColor(location.threatScore)}
                fillOpacity={highlightedIp === ip ? 1 : 0.7}
                stroke={highlightedIp === ip ? 'white' : 'none'}
                strokeWidth={2}
              />
              {highlightedIp === ip && (
                <>
                  <circle 
                    r={Math.max(8, Math.min(14, location.count) + 4)}
                    fill="none"
                    stroke={getThreatColor(location.threatScore)}
                    strokeWidth={2}
                    strokeOpacity={0.5}
                  />
                  <text
                    x="10"
                    y="4"
                    fontSize="12"
                    fill="currentColor"
                    className="text-gray-900 dark:text-white"
                  >
                    {ip} ({location.city}, {location.country})
                  </text>
                </>
              )}
            </g>
          ))}
        </svg>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-gray-50 dark:bg-gray-700 rounded-md p-3">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Top Locations</h4>
          <ul className="space-y-2">
            {Object.entries(ipLocations)
              .sort((a, b) => b[1].count - a[1].count)
              .slice(0, 5)
              .map(([ip, location]) => (
                <li 
                  key={ip} 
                  className="flex items-center justify-between text-sm"
                  onClick={() => onIpSelect && onIpSelect(ip)}
                >
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-1 text-gray-500 dark:text-gray-400" />
                    <span className="text-gray-700 dark:text-gray-300">
                      {location.city}, {location.country}
                    </span>
                  </div>
                  <span className="text-gray-600 dark:text-gray-400">
                    {location.count} hit{location.count !== 1 ? 's' : ''}
                  </span>
                </li>
              ))}
          </ul>
        </div>
        
        <div className="bg-gray-50 dark:bg-gray-700 rounded-md p-3">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Suspicious IPs</h4>
          <ul className="space-y-2">
            {Object.entries(ipLocations)
              .sort((a, b) => b[1].threatScore - a[1].threatScore)
              .slice(0, 5)
              .map(([ip, location]) => (
                <li 
                  key={ip} 
                  className="flex items-center justify-between text-sm"
                  onClick={() => onIpSelect && onIpSelect(ip)}
                >
                  <div className="flex items-center">
                    <AlertCircle 
                      className="h-4 w-4 mr-1" 
                      style={{ color: getThreatColor(location.threatScore) }} 
                    />
                    <span className="text-gray-700 dark:text-gray-300">{ip}</span>
                  </div>
                  <span 
                    className="font-medium"
                    style={{ color: getThreatColor(location.threatScore) }}
                  >
                    Threat: {location.threatScore}
                  </span>
                </li>
              ))}
          </ul>
        </div>
      </div>
    </div>
  );
}; 