import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { IsNotEmpty, IsOptional, IsEnum, IsDateString, Length, IsUrl } from 'class-validator';

export enum TrademarkType {
  WORD_MARK = 'word_mark',
  DESIGN_MARK = 'design_mark',
  COMBINED_MARK = 'combined_mark',
  SERVICE_MARK = 'service_mark',
  COLLECTIVE_MARK = 'collective_mark',
  CERTIFICATION_MARK = 'certification_mark'
}

export enum TrademarkStatus {
  PENDING = 'pending',
  REGISTERED = 'registered',
  OPPOSED = 'opposed',
  ABANDONED = 'abandoned',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired',
  RENEWED = 'renewed'
}

export enum TrademarkClass {
  CLASS_01 = 'class_01', // Chemicals
  CLASS_09 = 'class_09', // Scientific instruments, software
  CLASS_35 = 'class_35', // Advertising, business management
  CLASS_42 = 'class_42', // Scientific and technological services
  // Add more classes as needed
}

@Entity('company_trademarks')
export class CompanyTrademark {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 200 })
  @IsNotEmpty({ message: 'Trademark name is required' })
  @Length(2, 200, { message: 'Trademark name must be between 2 and 200 characters' })
  trademarkName: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  description: string;

  @Column({
    type: 'enum',
    enum: TrademarkType,
    default: TrademarkType.WORD_MARK
  })
  @IsEnum(TrademarkType, { message: 'Invalid trademark type' })
  trademarkType: TrademarkType;

  @Column({
    type: 'enum',
    enum: TrademarkStatus,
    default: TrademarkStatus.PENDING
  })
  @IsEnum(TrademarkStatus, { message: 'Invalid trademark status' })
  status: TrademarkStatus;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  registrationNumber: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  applicationNumber: string;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDateString({}, { message: 'Invalid application date' })
  applicationDate: Date;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDateString({}, { message: 'Invalid registration date' })
  registrationDate: Date;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDateString({}, { message: 'Invalid renewal date' })
  renewalDate: Date;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDateString({}, { message: 'Invalid expiry date' })
  expiryDate: Date;

  @Column({ type: 'varchar', length: 100 })
  @IsNotEmpty({ message: 'Jurisdiction is required' })
  jurisdiction: string; // e.g., "United States", "European Union", "Pakistan"

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  registryOffice: string; // e.g., "USPTO", "EUIPO", "IPO Pakistan"

  @Column({ type: 'simple-array', nullable: true })
  @IsOptional()
  trademarkClasses: TrademarkClass[];

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  goodsAndServices: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  @IsOptional()
  @IsUrl({}, { message: 'Invalid trademark image URL' })
  trademarkImageUrl: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  @IsOptional()
  attorney: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  attorneyReference: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  @IsOptional()
  registrationFee: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  @IsOptional()
  renewalFee: number;

  @Column({ type: 'varchar', length: 10, nullable: true })
  @IsOptional()
  currency: string;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  legalDocuments: {
    documentType: string;
    documentUrl: string;
    uploadDate: Date;
  }[];

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  notes: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'boolean', default: false })
  isPrimary: boolean; // Main company trademark

  @Column({ type: 'int', default: 0 })
  priority: number; // Display order

  // Metadata for tracking
  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  createdBy: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  lastModifiedBy: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Helper methods
  isExpiringSoon(daysThreshold: number = 90): boolean {
    if (!this.expiryDate) return false;
    
    const today = new Date();
    const expiryDate = new Date(this.expiryDate);
    const timeDiff = expiryDate.getTime() - today.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
    
    return daysDiff <= daysThreshold && daysDiff > 0;
  }

  isExpired(): boolean {
    if (!this.expiryDate) return false;
    
    const today = new Date();
    const expiryDate = new Date(this.expiryDate);
    
    return today > expiryDate;
  }

  getDaysUntilExpiry(): number | null {
    if (!this.expiryDate) return null;
    
    const today = new Date();
    const expiryDate = new Date(this.expiryDate);
    const timeDiff = expiryDate.getTime() - today.getTime();
    
    return Math.ceil(timeDiff / (1000 * 3600 * 24));
  }

  getStatusColor(): string {
    switch (this.status) {
      case TrademarkStatus.REGISTERED:
        return 'green';
      case TrademarkStatus.PENDING:
        return 'yellow';
      case TrademarkStatus.OPPOSED:
        return 'orange';
      case TrademarkStatus.ABANDONED:
      case TrademarkStatus.CANCELLED:
      case TrademarkStatus.EXPIRED:
        return 'red';
      case TrademarkStatus.RENEWED:
        return 'blue';
      default:
        return 'gray';
    }
  }
}
