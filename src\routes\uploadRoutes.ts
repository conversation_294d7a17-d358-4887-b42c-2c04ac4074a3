import express from 'express';
import path from 'path';
import fs from 'fs';
import { requireAuth } from '../middleware/auth';
import { handleFileUpload } from '../middleware/fileUpload';
import { v4 as uuidv4 } from 'uuid';
import { UploadedFile } from 'express-fileupload';

const router = express.Router();

// Test endpoint - No auth required for testing
router.get('/test', (req, res) => {
  try {
    const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'invoices');
    
    // Check if directory exists
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
      console.log('Created uploads directory for test');
    }
    
    // Check write permissions by creating a test file
    const testFile = path.join(uploadDir, 'test-api.txt');
    fs.writeFileSync(testFile, 'Test file from API');
    
    // List all files in the directory
    const files = fs.readdirSync(uploadDir);
    
    return res.status(200).json({
      success: true,
      message: 'Upload functionality test successful',
      uploadDir,
      files,
      writeable: true
    });
  } catch (error) {
    console.error('Upload test error:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error during test',
      error: error
    });
  }
});

// No auth middleware for direct file upload debug
router.post('/test-direct', (req, res) => {
  try {
    console.log('Direct file upload test called');
    
    // Check if any files were uploaded
    if (!req.files || Object.keys(req.files).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No files were uploaded',
        contentType: req.headers['content-type']
      });
    }
    
    // Print information about files
    console.log('Files received:', Object.keys(req.files));
    
    // Get file information
    const fileObj = req.files as any;
    const firstFileKey = Object.keys(fileObj)[0];
    const uploadedFile = fileObj[firstFileKey];
    
    // Check if it's an array
    if (Array.isArray(uploadedFile)) {
      return res.status(400).json({
        success: false,
        message: 'Multiple files not supported for this endpoint'
      });
    }
    
    // Create uploads directory
    const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'invoices');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    
    // Generate unique filename
    const filename = `direct_test_${Date.now()}${path.extname(uploadedFile.name)}`;
    const filePath = path.join(uploadDir, filename);
    
    // Move the file
    uploadedFile.mv(filePath, (err: Error) => {
      if (err) {
        console.error('Error moving file:', err);
        return res.status(500).json({
          success: false,
          message: 'Error saving file',
          error: err.message
        });
      }
      
      return res.status(200).json({
        success: true,
        message: 'File uploaded successfully',
        fileUrl: `/uploads/invoices/${filename}`,
        file: {
          originalname: uploadedFile.name,
          filename: filename,
          mimetype: uploadedFile.mimetype,
          size: uploadedFile.size
        }
      });
    });
  } catch (error) {
    console.error('Direct upload test error:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error',
      error: error
    });
  }
});

// Apply auth middleware to all routes except test endpoints
router.use(requireAuth);

// Route for uploading invoice files - updated to handle file upload directly
router.post('/invoice', (req, res) => {
  try {
    console.log('[Upload Invoice] Request received', {
      contentType: req.headers['content-type'],
      hasFiles: !!req.files,
      filesKeys: req.files ? Object.keys(req.files) : []
    });

    // Check if any files were uploaded
    if (!req.files || Object.keys(req.files).length === 0) {
      console.log('[Upload Invoice] No files detected in request');
      return res.status(400).json({ 
        success: false, 
        message: 'No files were uploaded',
        contentType: req.headers['content-type']
      });
    }

    // Handle the file(s)
    let fileField = 'file';
    
    // Use type assertion with unknown as an intermediate step
    const filesMap = req.files as unknown as { [fieldname: string]: UploadedFile | UploadedFile[] };
    
    if (!(fileField in filesMap)) {
      // Try to get the first available file field
      fileField = Object.keys(filesMap)[0];
      console.log(`[Upload Invoice] Using field: ${fileField} instead of 'file'`);
    }

    // Get the uploaded file
    const uploadedFile = filesMap[fileField];
    if (Array.isArray(uploadedFile)) {
      console.log('[Upload Invoice] Multiple files detected for single field');
      return res.status(400).json({ 
        success: false, 
        message: 'Multiple files not supported for this endpoint' 
      });
    }

    // Create uploads directory if it doesn't exist
    const uploadDirInvoices = path.join(process.cwd(), 'public', 'uploads', 'invoices');
    const uploadDirItInvoices = path.join(process.cwd(), 'public', 'uploads', 'it-invoices');
    
    // Ensure both directories exist
    if (!fs.existsSync(uploadDirInvoices)) {
      fs.mkdirSync(uploadDirInvoices, { recursive: true });
      console.log(`[Upload Invoice] Created invoices directory: ${uploadDirInvoices}`);
    }
    
    if (!fs.existsSync(uploadDirItInvoices)) {
      fs.mkdirSync(uploadDirItInvoices, { recursive: true });
      console.log(`[Upload Invoice] Created IT invoices directory: ${uploadDirItInvoices}`);
    }

    // Generate a unique filename
    const fileExt = path.extname(uploadedFile.name);
    const uniqueId = `${Date.now()}-${uuidv4().substring(0, 10)}`;
    const fileName = `${uniqueId}${fileExt}`;
    
    // Create paths for both directories
    const filePathInvoices = path.join(uploadDirInvoices, fileName);
    const filePathItInvoices = path.join(uploadDirItInvoices, fileName);

    console.log(`[Upload Invoice] Saving file as: ${fileName}`);
    console.log(`[Upload Invoice] Path 1: ${filePathInvoices}`);
    console.log(`[Upload Invoice] Path 2: ${filePathItInvoices}`);
    
    // URL path will be relative to public directory - we'll use the it-invoices path as primary
    const fileUrl = `/uploads/it-invoices/${fileName}`;
    
    // Create a buffer from the file data to write to both locations
    const fileBuffer = uploadedFile.data;

    // First write to the invoices directory
    fs.writeFile(filePathInvoices, fileBuffer, (err) => {
      if (err) {
        console.error('[Upload Invoice] Error writing to invoices directory:', err);
        // Continue anyway, we'll try the IT invoices directory next
      } else {
        console.log('[Upload Invoice] File saved to invoices directory');
      }
      
      // Now write to the IT invoices directory
      fs.writeFile(filePathItInvoices, fileBuffer, (err) => {
        if (err) {
          console.error('[Upload Invoice] Error writing to IT invoices directory:', err);
          
          // If we failed to write to both directories, return an error
          if (!fs.existsSync(filePathInvoices)) {
            return res.status(500).json({
              success: false,
              message: 'Error saving file to both directories',
              error: err.message
            });
          }
        } else {
          console.log('[Upload Invoice] File saved to IT invoices directory');
        }
        
        // At least one write succeeded, so return success
        console.log('[Upload Invoice] File uploaded successfully:', fileName);
        return res.status(200).json({
          success: true,
          message: 'File uploaded successfully',
          fileUrl: fileUrl,
          alternativeUrl: `/uploads/invoices/${fileName}`,
          file: {
            originalname: uploadedFile.name,
            filename: fileName,
            mimetype: uploadedFile.mimetype,
            size: uploadedFile.size
          }
        });
      });
    });
  } catch (error) {
    console.error('[Upload Invoice] Error handling upload:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Failed to upload file'
    });
  }
});

export default router; 