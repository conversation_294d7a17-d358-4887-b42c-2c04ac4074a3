import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn, BeforeInsert, BeforeUpdate } from 'typeorm';
import { User } from './User';
import { Ticket } from '../../entities/Ticket';
import { Attachment } from '../../entities/Attachment';

@Entity('comments')
export class Comment {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'text' })
  content!: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt!: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt!: Date;

  @ManyToOne(() => Ticket, ticket => ticket.comments, { 
    onDelete: 'CASCADE',
    nullable: false 
  })
  @JoinColumn({ name: 'ticketId' })
  ticket!: Ticket;

  @Column({ type: 'int' })
  ticketId!: number;

  @ManyToOne(() => User, user => user.comments, { 
    onDelete: 'CASCADE',
    nullable: false 
  })
  @JoinColumn({ name: 'createdById' })
  createdBy!: User;

  @Column({ type: 'varchar', length: 36 })
  createdById!: string;

  @Column({ type: 'boolean', default: false })
  isSystemComment!: boolean;

  @OneToMany(() => Attachment, attachment => attachment.comment, {
    cascade: true,
    onDelete: 'CASCADE'
  })
  attachments!: Attachment[];

  @BeforeInsert()
  @BeforeUpdate()
  ensureTicketId() {
    // Ensure ticketId is set from the ticket relationship if available
    if (this.ticket && this.ticket.id) {
      this.ticketId = this.ticket.id;
    }
    
    // Additional safeguard: never allow ticketId to be set to null during updates
    if (this.ticketId === null || this.ticketId === undefined) {
      console.error('Attempted to save comment with null ticketId. This is not allowed.');
      throw new Error('Comment ticketId cannot be null');
    }
  }
} 