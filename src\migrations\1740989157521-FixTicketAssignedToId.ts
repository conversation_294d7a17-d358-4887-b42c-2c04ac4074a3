import { MigrationInterface, QueryRunner } from "typeorm";

export class FixTicketAssignedToId1740989157521 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Drop existing foreign key constraints on assignedToId
        const [constraints] = await queryRunner.query(`
            SELECT CONSTRAINT_NAME 
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = 'ims_db'
            AND TABLE_NAME = 'tickets'
            AND COLUMN_NAME = 'assignedToId'
            AND REFERENCED_TABLE_NAME IS NOT NULL
        `);

        if (constraints && constraints.CONSTRAINT_NAME) {
            await queryRunner.query(`
                ALTER TABLE tickets 
                DROP FOREIGN KEY ${constraints.CONSTRAINT_NAME}
            `);
        }

        // Change assignedToId column type to CHAR(36) for UUID
        await queryRunner.query(`
            ALTER TABLE tickets 
            MODIFY COLUMN assignedToId CHAR(36)
        `);
        
        // Add new foreign key constraint
        await queryRunner.query(`
            ALTER TABLE tickets 
            ADD CONSTRAINT FK_tickets_assignedToId 
            FOREIGN KEY (assignedToId) 
            REFERENCES users(id) 
            ON DELETE SET NULL
            ON UPDATE CASCADE
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the new foreign key constraint
        await queryRunner.query(`
            ALTER TABLE tickets 
            DROP FOREIGN KEY FK_tickets_assignedToId
        `);
        
        // Change assignedToId column type back to INTEGER
        await queryRunner.query(`
            ALTER TABLE tickets 
            MODIFY COLUMN assignedToId INTEGER
        `);
        
        // Add back original foreign key constraint
        await queryRunner.query(`
            ALTER TABLE tickets 
            ADD CONSTRAINT FK_tickets_assignedToId 
            FOREIGN KEY (assignedToId) 
            REFERENCES users(id) 
            ON DELETE SET NULL
            ON UPDATE CASCADE
        `);
    }
} 