import React, { useState, useRef } from 'react';
import { UploadCloud, X, FileText, Receipt, FileInput, FileUp, FileDown, AlertCircle, FileCheck, FileX } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { formatFileSize } from '../utils/formatters';

// Simple UI components
interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'outline' | 'ghost' | 'destructive';
  size?: 'sm' | 'md' | 'lg' | 'icon';
  className?: string;
  title?: string;
  type?: 'button' | 'submit' | 'reset';
  disabled?: boolean;
}

const Button: React.FC<ButtonProps> = ({ 
  children, 
  onClick, 
  variant = 'primary', 
  size = 'md', 
  className = '',
  title,
  type = 'button',
  disabled = false
}) => {
  const baseClasses = 'rounded font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50';
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 shadow-sm',
    outline: 'bg-transparent border-2 border-gray-300 text-gray-700 hover:bg-gray-50 shadow-sm',
    ghost: 'bg-transparent text-gray-700 hover:bg-gray-100',
    destructive: 'bg-red-600 text-white hover:bg-red-700 shadow-sm'
  };
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2.5',
    lg: 'px-6 py-3 text-lg',
    icon: 'p-2'
  };
  
  return (
    <button
      type={type}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={onClick}
      title={title}
      disabled={disabled}
    >
      {children}
    </button>
  );
};

interface InputProps {
  id?: string;
  value: string;
  onChange?: React.ChangeEventHandler<HTMLInputElement>;
  placeholder?: string;
  className?: string;
  required?: boolean;
}

const Input: React.FC<InputProps> = ({ 
  id,
  value,
  onChange, 
  placeholder = '',
  className = '',
  required = false
}) => {
  return (
    <input
      id={id}
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      className={`w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-600 focus:border-blue-600 shadow-sm transition-all ${className}`}
      required={required}
    />
  );
};

interface LabelProps {
  htmlFor?: string;
  children: React.ReactNode;
  className?: string;
}

const Label: React.FC<LabelProps> = ({ 
  htmlFor,
  children,
  className = ''
}) => {
  return (
    <label 
      htmlFor={htmlFor}
      className={`block text-sm font-medium text-gray-700 mb-1 ${className}`}
    >
      {children}
    </label>
  );
};

interface DialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: React.ReactNode;
}

const Dialog: React.FC<DialogProps> = ({ open, onOpenChange, children }) => {
  if (!open) return null;
  
  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-2xl max-w-md w-full mx-4 overflow-hidden">
        {children}
      </div>
    </div>
  );
};

interface DialogContentProps {
  children: React.ReactNode;
  className?: string;
}

const DialogContent: React.FC<DialogContentProps> = ({ children, className = '' }) => (
  <div className={`p-6 ${className}`}>{children}</div>
);

interface DialogHeaderProps {
  children: React.ReactNode;
}

const DialogHeader: React.FC<DialogHeaderProps> = ({ children }) => (
  <div className="mb-6">{children}</div>
);

interface DialogTitleProps {
  children: React.ReactNode;
  className?: string;
}

const DialogTitle: React.FC<DialogTitleProps> = ({ children, className = '' }) => (
  <h3 className={`text-2xl font-bold text-gray-800 ${className}`}>{children}</h3>
);

interface DialogFooterProps {
  children: React.ReactNode;
}

const DialogFooter: React.FC<DialogFooterProps> = ({ children }) => (
  <div className="mt-8 flex justify-end space-x-3">{children}</div>
);

interface InvoiceUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpload: (formData: FormData) => Promise<void>;
  recordId: number | null;
}

const InvoiceUploadModal: React.FC<InvoiceUploadModalProps> = ({
  isOpen,
  onClose,
  onUpload,
  recordId
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [invoiceNumber, setInvoiceNumber] = useState('');
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    if (file) {
      // Check file type
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        toast.error('Please upload a PDF, JPEG, or PNG file');
        return;
      }
      
      // Check file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        toast.error('File size should not exceed 5MB');
        return;
      }
      
      setSelectedFile(file);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    const file = e.dataTransfer.files?.[0] || null;
    if (file) {
      // Check file type
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
      if (!allowedTypes.includes(file.type)) {
        toast.error('Please upload a PDF, JPEG, or PNG file');
        return;
      }
      
      // Check file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        toast.error('File size should not exceed 5MB');
        return;
      }
      
      setSelectedFile(file);
    }
  };

  const clearSelectedFile = () => {
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedFile) {
      toast.error('Please select a file to upload');
      return;
    }

    if (!invoiceNumber) {
      toast.error('Please enter an invoice number');
      return;
    }

    if (!recordId) {
      toast.error('Invalid record selected');
      return;
    }

    try {
      setUploading(true);
      const formData = new FormData();
      formData.append('invoice', selectedFile);
      formData.append('invoiceNumber', invoiceNumber);
      formData.append('recordId', recordId.toString());
      
      await onUpload(formData);
      
      setSelectedFile(null);
      setInvoiceNumber('');
      onClose();
    } catch (error) {
      console.error('Error uploading invoice:', error);
    } finally {
      setUploading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileUp className="h-6 w-6" />
            Upload Invoice
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="invoice_number" className="font-medium flex items-center gap-2">
                <Receipt className="h-4 w-4" />
                Invoice Number <span className="text-red-500">*</span>
              </Label>
              <div className="mt-1.5">
                <Input
                  id="invoice_number"
                  value={invoiceNumber}
                  onChange={(e) => setInvoiceNumber(e.target.value)}
                  placeholder="Enter invoice number"
                  required
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="file" className="font-medium flex items-center gap-2">
                <FileInput className="h-4 w-4" />
                Invoice File <span className="text-red-500">*</span>
              </Label>
              <div
                className={`mt-1.5 border-2 border-dashed rounded-lg p-8 text-center ${
                  selectedFile ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                }`}
                onDragOver={handleDragOver}
                onDrop={handleDrop}
              >
                {!selectedFile ? (
                  <div className="space-y-4">
                    <div className="flex justify-center">
                      <UploadCloud className="h-12 w-12 text-gray-400" />
                    </div>
                    <div>
                      <p className="text-base font-medium text-gray-700 flex items-center justify-center gap-2">
                        <FileDown className="h-5 w-5" />
                        Drag and drop your file here
                      </p>
                      <p className="text-sm text-gray-500 mt-1 flex items-center justify-center gap-2">
                        <AlertCircle className="h-4 w-4" />
                        PDF, JPEG, or PNG (max 5MB)
                      </p>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      className="mx-auto flex items-center gap-2"
                    >
                      <FileUp className="h-4 w-4" />
                      Browse Files
                    </Button>
                    <input
                      ref={fileInputRef}
                      type="file"
                      id="file"
                      className="hidden"
                      accept=".pdf,.jpg,.jpeg,.png"
                      onChange={handleFileChange}
                    />
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex justify-center">
                      <div className="h-14 w-14 rounded-full bg-blue-100 flex items-center justify-center">
                        <FileCheck className="h-8 w-8 text-blue-600" />
                      </div>
                    </div>
                    <div>
                      <p className="text-base font-medium text-gray-700 truncate flex items-center justify-center gap-2">
                        <FileText className="h-5 w-5" />
                        {selectedFile.name}
                      </p>
                      <p className="text-sm text-gray-500 mt-1 flex items-center justify-center gap-2">
                        <FileCheck className="h-4 w-4" />
                        {formatFileSize(selectedFile.size)}
                      </p>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={clearSelectedFile}
                      className="mx-auto flex items-center gap-2"
                    >
                      <FileX className="h-4 w-4" />
                      Select Different File
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex items-center gap-2"
            >
              <X className="h-4 w-4" />
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={uploading || !selectedFile || !invoiceNumber}
              className="px-5 flex items-center gap-2"
            >
              {uploading ? (
                <>
                  <UploadCloud className="h-4 w-4 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <UploadCloud className="h-4 w-4" />
                  Upload Invoice
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default InvoiceUploadModal; 