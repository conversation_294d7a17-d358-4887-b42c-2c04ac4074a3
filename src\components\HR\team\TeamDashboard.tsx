import React, { useState, useEffect } from 'react';
import {
    Users,
    Calendar,
    Clock,
    CheckCircle,
    AlertCircle,
    ArrowRight,
    X as XIcon,
    Check,
    User,
    Building,
    RefreshCw
} from 'lucide-react';
import { LeaveRequest, LeaveStatus, Attendance, AttendanceStatus } from '../../../types/attendance';
import { useAuth } from '../../../contexts/AuthContext';
import employeePortalService from '../../../services/EmployeePortalService';
import EmployeeAttendanceModal from '../attendance/EmployeeAttendanceModal';

interface TeamMember {
    id: number;
    name: string;
    position: string;
    department: string;
    profileImage?: string;
}

interface TeamDashboardProps {
    managerId?: number;
}

const TeamDashboard: React.FC<TeamDashboardProps> = ({ managerId }) => {
    const { user } = useAuth();
    const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
    const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([]);
    const [attendanceRecords, setAttendanceRecords] = useState<Attendance[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [dataLoaded, setDataLoaded] = useState(false);

    // Notification state
    const [notification, setNotification] = useState<{
        type: 'success' | 'error' | 'info';
        message: string;
    } | null>(null);

    // Modal state for attendance
    const [selectedEmployee, setSelectedEmployee] = useState<TeamMember | null>(null);
    const [showAttendanceModal, setShowAttendanceModal] = useState(false);
    const [selectedEmployeeAttendances, setSelectedEmployeeAttendances] = useState<Attendance[]>([]);
    const [isLoadingAttendance, setIsLoadingAttendance] = useState(false);
    
    // Filter states
    const [filters, setFilters] = useState({
        datePeriod: 'Today',
        status: 'All Status',
        location: 'All Locations',
        department: 'All Departments',
        shift: 'All Shifts',
        workHours: 'All Work Hours',
        searchText: ''
    });

    // Get current user ID or use provided manager ID
    const currentUserId = managerId || (user?.id ? Number(user.id) : undefined);

    useEffect(() => {
        if (currentUserId) {
            loadTeamData(currentUserId);
        }
    }, [currentUserId]);

    // Auto-dismiss notifications after 5 seconds
    useEffect(() => {
        if (notification) {
            const timer = setTimeout(() => {
                setNotification(null);
            }, 5000);
            return () => clearTimeout(timer);
        }
    }, [notification]);

    const loadTeamData = async (managerId: number) => {
        setLoading(true);
        setError(null);
        try {
            console.log('🔄 Loading team data for manager ID:', managerId);
            
            const teamData = await employeePortalService.getTeamMembers(managerId);
            console.log('📊 Team data received:', teamData);
            
            if (teamData && teamData.reportees && teamData.reportees.length > 0) {
                console.log(`✅ Found ${teamData.reportees.length} team members`);
                
                setTeamMembers(teamData.reportees.map(member => ({
                    id: member.id,
                    name: member.name,
                    position: member.designation || 'Employee',
                    department: member.department || 'N/A',
                    profileImage: (member as any).profileImage
                })));

                // Load leave requests for all team members
                const leavePromises = teamData.reportees.map(member =>
                    employeePortalService.getEmployeeLeaveData(member.id)
                );

                const leaveResults = await Promise.all(leavePromises);
                const allLeaveRequests: LeaveRequest[] = [];

                leaveResults.forEach((result, index) => {
                    if (result && result.recentRequests) {
                        const employeeName = teamData.reportees[index].name;
                        const employeeId = teamData.reportees[index].id;

                        const transformedRequests = result.recentRequests.map(req => ({
                            id: req.id,
                            employeeId: employeeId,
                            employeeName: employeeName,
                            leaveType: req.type,
                            startDate: req.from,
                            endDate: req.to,
                            days: req.days,
                            status: req.status as LeaveStatus,
                            reason: req.reason || '',
                            appliedOn: (req as any).appliedOn || new Date().toISOString(),
                            approvedBy: (req as any).approvedBy || undefined,
                            rejectionReason: (req as any).rejectionReason || undefined,
                            createdAt: new Date().toISOString(),
                            updatedAt: new Date().toISOString()
                        }));

                        allLeaveRequests.push(...transformedRequests);
                    }
                });

                setLeaveRequests(allLeaveRequests);

                // Load attendance data for all team members
                const today = new Date().toISOString().split('T')[0];
                const attendancePromises = teamData.reportees.map(member =>
                    employeePortalService.getEmployeeAttendance(member.id, today, today)
                );

                const attendanceResults = await Promise.all(attendancePromises);
                const allAttendanceRecords: Attendance[] = [];

                attendanceResults.forEach((result, index) => {
                    if (result && result.records) {
                        const employeeName = teamData.reportees[index].name;
                        const employeeId = teamData.reportees[index].id;

                        const transformedAttendance = result.records.map(record => ({
                            id: record.id || `${employeeId}-${record.date}`,
                            employeeId: employeeId,
                            employeeName: employeeName,
                            date: record.date,
                            checkInTime: record.checkInTime || '',
                            checkOutTime: record.checkOutTime,
                            status: record.status as AttendanceStatus,
                            workHours: record.workHours || null,
                            notes: record.notes || null,
                            shift: 1,
                            location: record.location || null,
                            isRemote: false,
                            createdAt: new Date().toISOString(),
                            updatedAt: new Date().toISOString()
                        }));

                        allAttendanceRecords.push(...transformedAttendance);
                    }
                });

                setAttendanceRecords(allAttendanceRecords);
            } else {
                console.warn('⚠️ No team data or reportees found');
                setTeamMembers([]);
                setLeaveRequests([]);
                setAttendanceRecords([]);
            }
            setDataLoaded(true);
        } catch (err) {
            console.error('❌ Error loading team data:', err);
            setError(`Failed to load team data: ${err instanceof Error ? err.message : 'Unknown error'}`);
            setDataLoaded(true);
        } finally {
            setLoading(false);
        }
    };

    const handleApproveLeave = async (leaveId: number) => {
        try {
            const result = await employeePortalService.approveLeaveRequest(leaveId);
            if (result.success) {
                setLeaveRequests(prev =>
                    prev.map(req =>
                        req.id === leaveId
                            ? { ...req, status: LeaveStatus.APPROVED, approvedBy: user?.name || 'Manager' }
                            : req
                    )
                );
                setNotification({
                    type: 'success',
                    message: 'Leave request approved successfully'
                });
            } else {
                setNotification({
                    type: 'error',
                    message: result.message || 'Failed to approve leave request'
                });
            }
        } catch (err) {
            console.error('Error approving leave request:', err);
            setNotification({
                type: 'error',
                message: 'An error occurred while approving the leave request'
            });
        }
    };

    const handleRejectLeave = async (leaveId: number, reason: string = 'Request rejected by manager') => {
        try {
            const result = await employeePortalService.rejectLeaveRequest(leaveId, reason);
            if (result.success) {
                setLeaveRequests(prev =>
                    prev.map(req =>
                        req.id === leaveId
                            ? { ...req, status: LeaveStatus.REJECTED, rejectionReason: reason }
                            : req
                    )
                );
                setNotification({
                    type: 'success',
                    message: 'Leave request rejected successfully'
                });
            } else {
                setNotification({
                    type: 'error',
                    message: result.message || 'Failed to reject leave request'
                });
            }
        } catch (err) {
            console.error('Error rejecting leave request:', err);
            setNotification({
                type: 'error',
                message: 'An error occurred while rejecting the leave request'
            });
        }
    };

    const calculateDays = (startDate: string, endDate: string) => {
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diffTime = end.getTime() - start.getTime();
        return Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1;
    };

    const getAttendanceStatusBadge = (status: AttendanceStatus) => {
        const statusConfig: Record<string, { bg: string; text: string; icon: React.ReactElement | null }> = {
            'present': { bg: 'bg-green-100', text: 'text-green-800', icon: <CheckCircle className="h-3 w-3 mr-1" /> },
            'absent': { bg: 'bg-red-100', text: 'text-red-800', icon: <XIcon className="h-3 w-3 mr-1" /> },
            'late': { bg: 'bg-yellow-100', text: 'text-yellow-800', icon: <Clock className="h-3 w-3 mr-1" /> },
            'half_day': { bg: 'bg-blue-100', text: 'text-blue-800', icon: <Calendar className="h-3 w-3 mr-1" /> },
            'on_leave': { bg: 'bg-purple-100', text: 'text-purple-800', icon: <Calendar className="h-3 w-3 mr-1" /> },
            'weekend': { bg: 'bg-gray-100', text: 'text-gray-800', icon: <Calendar className="h-3 w-3 mr-1" /> },
            'holiday': { bg: 'bg-indigo-100', text: 'text-indigo-800', icon: <Calendar className="h-3 w-3 mr-1" /> }
        };

        const config = statusConfig[status] || { bg: 'bg-gray-100', text: 'text-gray-800', icon: null };

        return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
                {config.icon}
                {status.replace('_', ' ')}
            </span>
        );
    };

    const getLeaveStatusBadge = (status: LeaveStatus) => {
        const statusConfig: Record<string, { bg: string; text: string; icon: React.ReactElement | null }> = {
            'pending': { bg: 'bg-yellow-100', text: 'text-yellow-800', icon: <Clock className="h-3 w-3 mr-1" /> },
            'approved': { bg: 'bg-green-100', text: 'text-green-800', icon: <CheckCircle className="h-3 w-3 mr-1" /> },
            'rejected': { bg: 'bg-red-100', text: 'text-red-800', icon: <XIcon className="h-3 w-3 mr-1" /> },
            'cancelled': { bg: 'bg-gray-100', text: 'text-gray-800', icon: <XIcon className="h-3 w-3 mr-1" /> }
        };

        const config = statusConfig[status] || { bg: 'bg-gray-100', text: 'text-gray-800', icon: null };

        return (
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
                {config.icon}
                {status}
            </span>
        );
    };

    const handleOpenAttendanceModal = async (employee: TeamMember) => {
        setSelectedEmployee(employee);
        setShowAttendanceModal(true);
        setIsLoadingAttendance(true);
        
        try {
            // Fetch full attendance history for the selected employee
            // You can adjust the date range as needed (e.g., last 30 days, last 3 months, etc.)
            const endDate = new Date().toISOString().split('T')[0];
            const startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // Last 90 days
            
            const attendanceData = await employeePortalService.getEmployeeAttendance(
                employee.id, 
                startDate, 
                endDate
            );
            
            if (attendanceData && attendanceData.records) {
                const transformedAttendance = attendanceData.records.map(record => ({
                    id: record.id || `${employee.id}-${record.date}`,
                    employeeId: employee.id,
                    employeeName: employee.name,
                    date: record.date,
                    checkInTime: record.checkInTime || '',
                    checkOutTime: record.checkOutTime,
                    status: record.status as AttendanceStatus,
                    workHours: record.workHours || null,
                    notes: record.notes || null,
                    shift: 1,
                    location: record.location || null,
                    isRemote: false,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }));
                
                setSelectedEmployeeAttendances(transformedAttendance);
            } else {
                setSelectedEmployeeAttendances([]);
            }
        } catch (error) {
            console.error('Error fetching employee attendance:', error);
            setSelectedEmployeeAttendances([]);
        } finally {
            setIsLoadingAttendance(false);
        }
    };

    if (loading) {
        return (
            <div className="min-h-[600px] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 rounded-xl flex items-center justify-center">
                <div className="text-center">
                    <div className="relative mb-6">
                        <div className="w-20 h-20 border-4 border-blue-100 border-t-blue-600 rounded-full animate-spin mx-auto"></div>
                        <div className="absolute inset-0 w-20 h-20 border-2 border-blue-200 rounded-full mx-auto opacity-30"></div>
                        <div className="absolute inset-0 flex items-center justify-center">
                            <Users className="h-8 w-8 text-blue-600 opacity-50" />
                        </div>
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Loading Team Dashboard</h3>
                    <p className="text-gray-600 mb-1">Fetching your team's information...</p>
                    <p className="text-sm text-gray-500">This may take a few moments</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-[400px] bg-gradient-to-br from-red-50 via-pink-50 to-rose-100 rounded-xl flex items-center justify-center">
                <div className="text-center max-w-md mx-auto p-8">
                    <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <AlertCircle className="h-8 w-8 text-red-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Unable to Load Team Data</h3>
                    <p className="text-gray-600 mb-6">{error}</p>
                    <button
                        onClick={() => currentUserId && loadTeamData(currentUserId)}
                        className="px-6 py-3 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-lg hover:from-red-700 hover:to-red-800 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    >
                        Try Again
                    </button>
                </div>
            </div>
        );
    }

    if (teamMembers.length === 0) {
        return (
            <div className="min-h-[400px] bg-gradient-to-br from-gray-50 via-slate-50 to-gray-100 rounded-xl flex items-center justify-center">
                <div className="text-center max-w-md mx-auto p-8">
                    <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <Users className="h-10 w-10 text-blue-600" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">No Team Data Available</h3>
                    <p className="text-gray-600 mb-4">Your team information is not available or has not been set up yet.</p>
                    
                    {/* Debug information */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4 text-left">
                        <h4 className="font-medium text-blue-900 mb-2">Debug Information:</h4>
                        <div className="text-sm text-blue-800 space-y-1">
                            <p><strong>Current User ID:</strong> {currentUserId || 'Not available'}</p>
                            <p><strong>Manager ID:</strong> {managerId || 'Not provided'}</p>
                            <p><strong>Team Members Found:</strong> {teamMembers.length}</p>
                            <p><strong>Loading State:</strong> {loading ? 'Yes' : 'No'}</p>
                            <p><strong>Data Loaded:</strong> {dataLoaded ? 'Yes' : 'No'}</p>
                            <p><strong>Error State:</strong> {error ? 'Yes' : 'No'}</p>
                            {error && <p><strong>Error Message:</strong> {error}</p>}
                        </div>
                    </div>
                    
                    <div className="space-y-3">
                        <p className="text-sm text-gray-500">Possible reasons:</p>
                        <ul className="text-sm text-gray-600 text-left space-y-1">
                            <li>• No employees are assigned to report to you</li>
                            <li>• Your manager role is not properly configured</li>
                            <li>• The reporting hierarchy needs to be set up</li>
                            <li>• Contact HR to verify your team assignments</li>
                        </ul>
                    </div>
                    
                    <div className="mt-6 space-x-3">
                        <button
                            onClick={() => currentUserId && loadTeamData(currentUserId)}
                            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                        >
                            Refresh Data
                        </button>
                        <button
                            onClick={() => window.location.reload()}
                            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm font-medium"
                        >
                            Reload Page
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-8 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 min-h-screen p-6">
            {/* Notification */}
            {notification && (
                <div className={`p-4 rounded-xl shadow-lg border-l-4 ${notification.type === 'success'
                    ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-500' :
                    notification.type === 'error'
                        ? 'bg-gradient-to-r from-red-50 to-rose-50 border-red-500' :
                        'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-500'
                    }`}>
                    <div className="flex items-center">
                        {notification.type === 'success' && <CheckCircle className="h-5 w-5 text-green-600 mr-3" />}
                        {notification.type === 'error' && <AlertCircle className="h-5 w-5 text-red-600 mr-3" />}
                        {notification.type === 'info' && <Clock className="h-5 w-5 text-blue-600 mr-3" />}
                        <p className={`text-sm font-semibold ${notification.type === 'success' ? 'text-green-800' :
                            notification.type === 'error' ? 'text-red-800' :
                                'text-blue-800'
                            }`}>
                            {notification.message}
                        </p>
                    </div>
                </div>
            )}

            {/* Header with team stats */}
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-6 backdrop-blur-sm bg-white/95">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
                    <div className="flex items-center">
                        <div className="p-4 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl mr-4 shadow-lg">
                            <Users className="h-8 w-8 text-white" />
                        </div>
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900 mb-1">Team Dashboard</h1>
                            <p className="text-gray-600">Manage your team's attendance and leave requests</p>
                        </div>
                    </div>

                    <div className="text-sm text-gray-500 bg-blue-50 px-3 py-2 rounded-lg">
                        {teamMembers.length} team member{teamMembers.length !== 1 ? 's' : ''}
                    </div>
                </div>
            </div>

            {/* Team Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 shadow-xl text-white transform hover:scale-105 transition-all duration-200">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-blue-100 text-sm font-medium mb-1">Team Members</p>
                            <p className="text-3xl font-bold">{teamMembers.length}</p>
                            <p className="text-blue-200 text-xs mt-1">Total employees</p>
                        </div>
                        <div className="bg-white/20 p-3 rounded-xl">
                            <Users className="h-8 w-8 text-white" />
                        </div>
                    </div>
                </div>

                <div className="bg-gradient-to-br from-green-500 to-green-600 rounded-2xl p-6 shadow-xl text-white transform hover:scale-105 transition-all duration-200">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-green-100 text-sm font-medium mb-1">Present Today</p>
                            <p className="text-3xl font-bold">
                                {attendanceRecords.filter(record => record.status === AttendanceStatus.PRESENT).length}
                            </p>
                            <p className="text-green-200 text-xs mt-1">Active employees</p>
                        </div>
                        <div className="bg-white/20 p-3 rounded-xl">
                            <CheckCircle className="h-8 w-8 text-white" />
                        </div>
                    </div>
                </div>

                <div className="bg-gradient-to-br from-yellow-500 to-orange-500 rounded-2xl p-6 shadow-xl text-white transform hover:scale-105 transition-all duration-200">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-yellow-100 text-sm font-medium mb-1">Pending Leaves</p>
                            <p className="text-3xl font-bold">
                                {leaveRequests.filter(request => request.status === LeaveStatus.PENDING).length}
                            </p>
                            <p className="text-yellow-200 text-xs mt-1">Awaiting approval</p>
                        </div>
                        <div className="bg-white/20 p-3 rounded-xl">
                            <Clock className="h-8 w-8 text-white" />
                        </div>
                    </div>
                </div>

                <div className="bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl p-6 shadow-xl text-white transform hover:scale-105 transition-all duration-200">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-purple-100 text-sm font-medium mb-1">On Leave</p>
                            <p className="text-3xl font-bold">
                                {attendanceRecords.filter(record => record.status === AttendanceStatus.ON_LEAVE).length}
                            </p>
                            <p className="text-purple-200 text-xs mt-1">Currently away</p>
                        </div>
                        <div className="bg-white/20 p-3 rounded-xl">
                            <Calendar className="h-8 w-8 text-white" />
                        </div>
                    </div>
                </div>
            </div>

            {/* Team Attendance Cards */}
            <div>
                <div className="flex items-center justify-between mb-4">
                    <h2 className="text-xl font-bold text-gray-900 flex items-center">
                        <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mr-3">
                            <Clock className="h-4 w-4 text-white" />
                        </div>
                        Today's Attendance
                    </h2>
                    <div className="text-sm text-gray-500">
                        {attendanceRecords.length} record{attendanceRecords.length !== 1 ? 's' : ''}
                    </div>
                </div>
                
                {/* Filters Bar */}
                <div className="bg-white rounded-lg shadow-sm mb-4 overflow-hidden">
                    <div className="flex items-center justify-between p-2 border-b border-gray-100">
                        <div className="flex items-center text-gray-500 text-sm">
                            <Filter className="h-4 w-4 mr-2" />
                            Filters
                        </div>
                        <button 
                            className="text-blue-600 text-sm hover:text-blue-800 transition-colors"
                            onClick={() => {
                                // Reset filters logic would go here
                            }}
                        >
                            Reset
                        </button>
                    </div>
                    <div className="flex flex-wrap items-center p-2">
                        <div className="flex items-center flex-grow flex-wrap gap-2">
                            {/* Date Period Filter */}
                            <div className="relative">
                                <select 
                                    className="appearance-none pl-3 pr-8 py-1.5 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    value={filters.datePeriod}
                                    onChange={(e) => setFilters(prev => ({ ...prev, datePeriod: e.target.value }))}
                                >
                                    <option value="Today">Today</option>
                                    <option value="Yesterday">Yesterday</option>
                                    <option value="This Week">This Week</option>
                                    <option value="Last Week">Last Week</option>
                                    <option value="This Month">This Month</option>
                                    <option value="Last Month">Last Month</option>
                                </select>
                                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                    <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>

                            {/* Status Filter */}
                            <div className="relative">
                                <select 
                                    className="appearance-none pl-3 pr-8 py-1.5 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    value={filters.status}
                                    onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                                >
                                    <option value="All Status">All Status</option>
                                    <option value="Present">Present</option>
                                    <option value="Absent">Absent</option>
                                    <option value="Late">Late</option>
                                    <option value="Half Day">Half Day</option>
                                    <option value="On Leave">On Leave</option>
                                </select>
                                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                    <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>

                            {/* Locations Filter */}
                            <div className="relative">
                                <select 
                                    className="appearance-none pl-3 pr-8 py-1.5 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    value={filters.location}
                                    onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                                >
                                    <option value="All Locations">All Locations</option>
                                    <option value="Office">Office</option>
                                    <option value="Remote">Remote</option>
                                </select>
                                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                    <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>

                            {/* Departments Filter */}
                            <div className="relative">
                                <select 
                                    className="appearance-none pl-3 pr-8 py-1.5 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    value={filters.department}
                                    onChange={(e) => setFilters(prev => ({ ...prev, department: e.target.value }))}
                                >
                                    <option value="All Departments">All Departments</option>
                                    {[...new Set(teamMembers.map(member => member.department))].map(dept => (
                                        <option key={dept} value={dept}>{dept}</option>
                                    ))}
                                </select>
                                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                    <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>

                            {/* Shifts Filter */}
                            <div className="relative">
                                <select 
                                    className="appearance-none pl-3 pr-8 py-1.5 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    value={filters.shift}
                                    onChange={(e) => setFilters(prev => ({ ...prev, shift: e.target.value }))}
                                >
                                    <option value="All Shifts">All Shifts</option>
                                    <option value="Morning">Morning</option>
                                    <option value="Evening">Evening</option>
                                    <option value="Night">Night</option>
                                </select>
                                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                    <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>

                            {/* Work Hours Filter */}
                            <div className="relative">
                                <select 
                                    className="appearance-none pl-3 pr-8 py-1.5 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    value={filters.workHours}
                                    onChange={(e) => setFilters(prev => ({ ...prev, workHours: e.target.value }))}
                                >
                                    <option value="All Work Hours">All Work Hours</option>
                                    <option value="Full Day">Full Day (7+ hrs)</option>
                                    <option value="Partial Day">Partial Day (&lt;7 hrs)</option>
                                    <option value="Overtime">With Overtime</option>
                                </select>
                                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                    <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                                    </svg>
                                </div>
                            </div>

                            {/* Search */}
                            <div className="relative flex-grow max-w-xs">
                                <input
                                    type="text"
                                    placeholder="Search..."
                                    value={filters.searchText}
                                    onChange={(e) => setFilters(prev => ({ ...prev, searchText: e.target.value }))}
                                    className="w-full pl-3 pr-8 py-1.5 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                    <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {attendanceRecords.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {attendanceRecords.map((record) => {
                            const employee = teamMembers.find(member => member.id === record.employeeId);

                            return (
                                <div key={record.id} className={`bg-white rounded-lg border shadow-sm overflow-hidden ${record.status === AttendanceStatus.PRESENT ? 'border-green-200' :
                                    record.status === AttendanceStatus.ABSENT ? 'border-red-200' :
                                        record.status === AttendanceStatus.LATE ? 'border-yellow-200' :
                                            record.status === AttendanceStatus.ON_LEAVE ? 'border-purple-200' :
                                                'border-gray-200'
                                    }`}>
                                    <div className={`px-4 py-2 ${record.status === AttendanceStatus.PRESENT ? 'bg-green-50' :
                                        record.status === AttendanceStatus.ABSENT ? 'bg-red-50' :
                                            record.status === AttendanceStatus.LATE ? 'bg-yellow-50' :
                                                record.status === AttendanceStatus.ON_LEAVE ? 'bg-purple-50' :
                                                    'bg-gray-50'
                                        }`}>
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center">
                                                <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden mr-3">
                                                    {employee?.profileImage ? (
                                                        <img src={employee.profileImage} alt={employee?.name} className="h-full w-full object-cover" />
                                                    ) : (
                                                        <User className="h-5 w-5 text-gray-500" />
                                                    )}
                                                </div>
                                                <div>
                                                    <h3 className="font-medium text-gray-900">{record.employeeName}</h3>
                                                    <p className="text-xs text-gray-600">{employee?.position}</p>
                                                </div>
                                            </div>
                                            {getAttendanceStatusBadge(record.status)}
                                        </div>
                                    </div>

                                    <div className="p-4">
                                        <div className="grid grid-cols-2 gap-2 mb-3">
                                            <div className="bg-gray-50 p-2 rounded">
                                                <p className="text-xs text-gray-500">Check In</p>
                                                <p className="font-medium text-gray-900">{record.checkInTime || 'N/A'}</p>
                                            </div>
                                            <div className="bg-gray-50 p-2 rounded">
                                                <p className="text-xs text-gray-500">Check Out</p>
                                                <p className="font-medium text-gray-900">{record.checkOutTime || 'N/A'}</p>
                                            </div>
                                        </div>

                                        <div className="flex items-center justify-between text-sm">
                                            <div className="flex items-center">
                                                <Building className="h-3 w-3 text-gray-400 mr-1" />
                                                <span className="text-gray-600">{employee?.department || 'N/A'}</span>
                                            </div>

                                            {record.workHours && (
                                                <div className="flex items-center">
                                                    <Clock className="h-3 w-3 text-gray-400 mr-1" />
                                                    <span className="text-gray-600">{record.workHours} hrs</span>
                                                </div>
                                            )}
                                        </div>

                                        {record.notes && (
                                            <div className="mt-2 text-xs text-gray-600 bg-gray-50 p-2 rounded">
                                                <p className="font-medium">Notes:</p>
                                                <p>{record.notes}</p>
                                            </div>
                                        )}
                                    </div>
                                    <button
                                        className="mt-2 px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-xs font-medium"
                                        onClick={() => {
                                            if (employee) {
                                                handleOpenAttendanceModal(employee);
                                            }
                                        }}
                                    >
                                        View Attendance
                                    </button>
                                </div>
                            );
                        })}
                    </div>
                ) : (
                    <div className="bg-white rounded-xl shadow-lg p-8 text-center border border-gray-200">
                        <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                            <Clock className="h-8 w-8 text-gray-400" />
                        </div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">No Attendance Records</h3>
                        <p className="text-gray-600 max-w-md mx-auto">
                            There are no attendance records for today that match your current filters.
                        </p>
                    </div>
                )}
            </div>

            {/* Leave Requests */}
            <div>
                <div className="flex items-center justify-between mb-4">
                    <h2 className="text-xl font-bold text-gray-900 flex items-center">
                        <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center mr-3">
                            <Calendar className="h-4 w-4 text-white" />
                        </div>
                        Leave Requests
                    </h2>
                    <div className="text-sm text-gray-500">
                        {leaveRequests.length} request{leaveRequests.length !== 1 ? 's' : ''}
                    </div>
                </div>

                {leaveRequests.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {leaveRequests.map((request) => {
                            const employee = teamMembers.find(member => member.id === request.employeeId);
                            const days = calculateDays(request.startDate, request.endDate);

                            return (
                                <div key={request.id} className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                                    <div className={`px-4 py-2 ${request.status === LeaveStatus.PENDING ? 'bg-yellow-50 border-b border-yellow-200' :
                                        request.status === LeaveStatus.APPROVED ? 'bg-green-50 border-b border-green-200' :
                                            request.status === LeaveStatus.REJECTED ? 'bg-red-50 border-b border-red-200' :
                                                'bg-gray-50 border-b border-gray-200'
                                        }`}>
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center">
                                                <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden mr-3">
                                                    {employee?.profileImage ? (
                                                        <img src={employee.profileImage} alt={employee?.name} className="h-full w-full object-cover" />
                                                    ) : (
                                                        <User className="h-5 w-5 text-gray-500" />
                                                    )}
                                                </div>
                                                <div>
                                                    <h3 className="font-medium text-gray-900">{request.employeeName}</h3>
                                                    <p className="text-xs text-gray-600">{employee?.position}</p>
                                                </div>
                                            </div>
                                            {getLeaveStatusBadge(request.status)}
                                        </div>
                                    </div>

                                    <div className="p-4">
                                        <div className="mb-3">
                                            <div className="flex items-center mb-1">
                                                <Calendar className="h-4 w-4 text-gray-500 mr-1" />
                                                <h4 className="font-medium text-gray-900">{request.leaveType}</h4>
                                            </div>
                                            <div className="flex items-center justify-between text-sm">
                                                <div className="flex items-center">
                                                    <span className="text-gray-600">{new Date(request.startDate).toLocaleDateString()}</span>
                                                    <ArrowRight className="h-3 w-3 text-gray-400 mx-1" />
                                                    <span className="text-gray-600">{new Date(request.endDate).toLocaleDateString()}</span>
                                                </div>
                                                <span className="font-medium text-gray-900">{days} day{days !== 1 ? 's' : ''}</span>
                                            </div>
                                        </div>

                                        {request.reason && (
                                            <div className="mb-3 text-sm">
                                                <p className="text-xs text-gray-500 mb-1">Reason:</p>
                                                <p className="text-gray-700 bg-gray-50 p-2 rounded">{request.reason}</p>
                                            </div>
                                        )}

                                        <div className="flex items-center justify-between text-xs text-gray-500">
                                            <span>Applied: {request.appliedOn ? new Date(request.appliedOn).toLocaleDateString() : 'N/A'}</span>

                                            {request.status === LeaveStatus.APPROVED && request.approvedBy && (
                                                <span>Approved by: {request.approvedBy}</span>
                                            )}

                                            {request.status === LeaveStatus.REJECTED && request.rejectionReason && (
                                                <span className="text-red-600">Reason: {request.rejectionReason}</span>
                                            )}
                                        </div>

                                        {request.status === LeaveStatus.PENDING && (
                                            <div className="mt-3 flex items-center justify-end space-x-2">
                                                <button
                                                    onClick={() => request.id && handleRejectLeave(request.id)}
                                                    className="px-3 py-1 bg-white border border-red-300 text-red-700 rounded hover:bg-red-50 transition-colors text-sm font-medium flex items-center"
                                                >
                                                    <XIcon className="h-3 w-3 mr-1" />
                                                    Reject
                                                </button>
                                                <button
                                                    onClick={() => request.id && handleApproveLeave(request.id)}
                                                    className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors text-sm font-medium flex items-center"
                                                >
                                                    <Check className="h-3 w-3 mr-1" />
                                                    Approve
                                                </button>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                ) : (
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
                        <Calendar className="h-10 w-10 text-gray-400 mx-auto mb-2" />
                        <h3 className="text-lg font-medium text-gray-900 mb-1">No Leave Requests</h3>
                        <p className="text-gray-600 max-w-md mx-auto">
                            There are no leave requests from your team members that match your current filters.
                        </p>
                    </div>
                )}
            </div>
            {showAttendanceModal && selectedEmployee && (
                <EmployeeAttendanceModal
                    employeeId={selectedEmployee.id}
                    employeeName={selectedEmployee.name}
                    employeeCode={undefined}
                    employeeDepartment={selectedEmployee.department}
                    employeePosition={selectedEmployee.position}
                    employeePhoto={selectedEmployee.profileImage}
                    attendances={selectedEmployeeAttendances}
                    onSubmitRegularization={() => {}}
                    onClose={() => {
                        setShowAttendanceModal(false);
                        setSelectedEmployee(null);
                        setSelectedEmployeeAttendances([]);
                    }}
                    canSubmitRegularization={false}
                />
            )}
        </div>
    );
};

export default TeamDashboard; 