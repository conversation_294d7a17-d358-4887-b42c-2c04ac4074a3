import { AppDataSource } from '../config/database';
import { AssetMaintenance } from '../entities/AssetMaintenance';
import { Between, FindOptionsWhere, LessThan, MoreThan } from 'typeorm';

interface FindMaintenanceOptions {
  assetId?: number;
  maintenanceType?: string;
  status?: string;
  startDate?: Date;
  endDate?: Date;
}

export const assetMaintenanceRepository = {
  async findAll(options?: {
    assetId?: number;
    maintenanceType?: string;
    status?: string;
    performedById?: string;
    startDate?: Date;
    endDate?: Date;
    page?: number;
    limit?: number;
  }) {
    const { 
      assetId, 
      maintenanceType, 
      status, 
      performedById, 
      startDate, 
      endDate,
      page = 1, 
      limit = 10 
    } = options || {};

    const where: FindOptionsWhere<AssetMaintenance> = {};
    
    if (assetId) {
      where.assetId = assetId;
    }
    
    if (maintenanceType) {
      where.maintenanceType = maintenanceType;
    }
    
    if (status) {
      where.status = status;
    }
    
    if (performedById) {
      where.performedById = performedById;
    }
    
    if (startDate && endDate) {
      where.maintenanceDate = Between(startDate, endDate);
    } else if (startDate) {
      where.maintenanceDate = MoreThan(startDate);
    } else if (endDate) {
      where.maintenanceDate = LessThan(endDate);
    }

    const [maintenanceRecords, total] = await AppDataSource.getRepository(AssetMaintenance).findAndCount({
      where,
      relations: ['asset', 'performedBy'],
      skip: (page - 1) * limit,
      take: limit,
      order: {
        maintenanceDate: 'ASC'
      }
    });

    return {
      maintenanceRecords,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    };
  },

  async findById(id: string) {
    return AppDataSource.getRepository(AssetMaintenance).findOne({
      where: { id },
      relations: ['asset', 'performedBy']
    });
  },

  async findByAssetId(assetId: number) {
    return AppDataSource.getRepository(AssetMaintenance).find({
      where: { assetId },
      order: { maintenanceDate: 'DESC' }
    });
  },

  async create(maintenanceData: Partial<AssetMaintenance>) {
    const maintenance = AppDataSource.getRepository(AssetMaintenance).create(maintenanceData);
    return AppDataSource.getRepository(AssetMaintenance).save(maintenance);
  },

  async update(id: string, maintenanceData: Partial<AssetMaintenance>) {
    await AppDataSource.getRepository(AssetMaintenance).update(id, maintenanceData);
    return this.findById(id);
  },

  async delete(id: string) {
    return AppDataSource.getRepository(AssetMaintenance).delete(id);
  },

  async getMaintenanceStats(assetId?: number) {
    const maintenanceRepo = AppDataSource.getRepository(AssetMaintenance);
    
    const whereClause = assetId ? { assetId } : {};
    
    const total = await maintenanceRepo.count({
      where: whereClause
    });
    
    const byType = await maintenanceRepo
      .createQueryBuilder('maintenance')
      .select('maintenance.maintenanceType, COUNT(maintenance.id) as count')
      .where(assetId ? 'maintenance.assetId = :assetId' : '1=1', assetId ? { assetId } : {})
      .groupBy('maintenance.maintenanceType')
      .getRawMany();
    
    const byStatus = await maintenanceRepo
      .createQueryBuilder('maintenance')
      .select('maintenance.status, COUNT(maintenance.id) as count')
      .where(assetId ? 'maintenance.assetId = :assetId' : '1=1', assetId ? { assetId } : {})
      .groupBy('maintenance.status')
      .getRawMany();
    
    const totalCost = await maintenanceRepo
      .createQueryBuilder('maintenance')
      .select('SUM(maintenance.cost)', 'sum')
      .where(assetId ? 'maintenance.assetId = :assetId' : '1=1', assetId ? { assetId } : {})
      .getRawOne();
    
    const upcomingMaintenance = await maintenanceRepo.count({
      where: {
        ...whereClause,
        status: 'Scheduled',
        maintenanceDate: MoreThan(new Date())
      }
    });
    
    const overdueMaintenance = await maintenanceRepo.count({
      where: {
        ...whereClause,
        status: 'Scheduled',
        maintenanceDate: LessThan(new Date())
      }
    });
    
    return {
      total,
      byType: byType.reduce((acc, curr) => {
        acc[curr.maintenanceType.toLowerCase()] = parseInt(curr.count);
        return acc;
      }, {}),
      byStatus: byStatus.reduce((acc, curr) => {
        acc[curr.status.toLowerCase()] = parseInt(curr.count);
        return acc;
      }, {}),
      totalCost: parseFloat(totalCost?.sum || '0'),
      upcomingMaintenance,
      overdueMaintenance
    };
  }
}; 