export interface DepartmentInfo {
  name: string;
  ticketCount: number;
  color: string;
}

export interface CategoryStatusInfo {
  open: number;
  inProgress: number;
  resolved: number;
}

export interface ResponseTimeStats {
  lessThanHour: number;
  oneToFourHours: number;
  fourToDay: number;
  moreThanDay: number;
}

export interface TrendStats {
  labels: string[];
  newTickets: number[];
  resolvedTickets: number[];
}

export type ActivityType = 'TICKET_CREATED' | 'STATUS_CHANGED' | 'PRIORITY_CHANGED' | 'COMMENT_ADDED' | 'TICKET_UPDATED';

export interface ActivityItem {
  id: string | number;
  type: ActivityType;
  description: string;
  userId: string | number;
  timestamp: string | Date;
  ticketId?: string | number;
  ticketNumber?: string;
  priority?: string;
  status?: string;
}

export interface PerformanceStats {
  averageResponseTime: string;
  averageResolutionTime: string;
  satisfactionRate: number;
}

export interface TicketStats {
  total: number;
  open: number;
  inProgress: number;
  resolved: number;
  closed: number;
  byPriority: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
  byDepartment: Record<string, DepartmentInfo>;
  responseTime: ResponseTimeStats;
  categoryStatus: Record<string, CategoryStatusInfo>;
  trends: TrendStats;
  recentActivity: ActivityItem[];
}

export interface DashboardStats {
  tickets: TicketStats;
  performance: PerformanceStats;
} 