import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateC<PERSON>umn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { IsNotEmpty, IsOptional, IsEnum, IsNumber, Length } from 'class-validator';
import { User } from './User';
import { TaskPriority, TaskType } from './Task';

export enum TemplateCategory {
  IT_INFRASTRUCTURE = 'it_infrastructure',
  SOFTWARE_DEVELOPMENT = 'software_development',
  SECURITY = 'security',
  MAINTENANCE = 'maintenance',
  DEPLOYMENT = 'deployment',
  TESTING = 'testing',
  DOCUMENTATION = 'documentation',
  GENERAL = 'general'
}

@Entity('task_templates')
export class TaskTemplate {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255 })
  @IsNotEmpty({ message: 'Template name is required' })
  @Length(2, 255, { message: 'Template name must be between 2 and 255 characters' })
  name: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  description: string;

  @Column({ type: 'varchar', length: 255 })
  @IsNotEmpty({ message: 'Task title template is required' })
  @Length(2, 255, { message: 'Task title must be between 2 and 255 characters' })
  titleTemplate: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  descriptionTemplate: string;

  @Column({
    type: 'enum',
    enum: TaskPriority,
    default: TaskPriority.MEDIUM
  })
  @IsEnum(TaskPriority, { message: 'Invalid task priority' })
  defaultPriority: TaskPriority;

  @Column({
    type: 'enum',
    enum: TaskType,
    default: TaskType.TASK
  })
  @IsEnum(TaskType, { message: 'Invalid task type' })
  defaultType: TaskType;

  @Column({
    type: 'enum',
    enum: TemplateCategory,
    default: TemplateCategory.GENERAL
  })
  @IsEnum(TemplateCategory, { message: 'Invalid template category' })
  category: TemplateCategory;

  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Estimated hours must be a number' })
  defaultEstimatedHours: number;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  defaultTags: string[];

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  defaultChecklist: {
    id: string;
    text: string;
    required: boolean;
  }[];

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  subtaskTemplates: {
    title: string;
    description?: string;
    estimatedHours?: number;
    priority: TaskPriority;
    order: number;
  }[];

  @Column({ type: 'boolean', default: false })
  requiresApproval: boolean;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  defaultApprovers: string[];

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'boolean', default: false })
  isPublic: boolean;

  @Column({ type: 'int', default: 0 })
  usageCount: number;

  // Relations
  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'createdById' })
  createdBy: User;

  @Column({ type: 'uuid' })
  createdById: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;
}
