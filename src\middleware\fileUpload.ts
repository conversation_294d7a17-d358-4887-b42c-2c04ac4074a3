import { Request, Response, NextFunction } from 'express';
import * as path from 'path';
import * as fs from 'fs';
import fileUpload, { UploadedFile } from 'express-fileupload';
import { v4 as uuidv4 } from 'uuid';

/**
 * Middleware to handle file uploads consistently throughout the application
 * This works with express-fileupload middleware
 */
export const handleFileUpload = (uploadDir: string = 'uploads') => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      console.log(`[FileUpload] Processing upload to: ${uploadDir}`);
      
      // Create the directory if it doesn't exist - ensuring it's under /public
      // Add public to the path if it's not already included
      const uploadPath = uploadDir.startsWith('public/') ? uploadDir : `public/${uploadDir}`;
      const fullUploadDir = path.join(process.cwd(), uploadPath);
      
      if (!fs.existsSync(fullUploadDir)) {
        fs.mkdirSync(fullUploadDir, { recursive: true });
        console.log(`[FileUpload] Created upload directory: ${fullUploadDir}`);
      }

      // Check if any files were uploaded
      const files = req.files;
      if (!files || Object.keys(files).length === 0) {
        console.log('[FileUpload] No files detected in request');
        console.log('Content-Type:', req.headers['content-type']);
        
        return res.status(400).json({ 
          success: false, 
          message: 'No files were uploaded',
          contentType: req.headers['content-type']
        });
      }

      console.log(`[FileUpload] Files found: ${Object.keys(files).join(', ')}`);
      
      // Handle the file(s)
      let fileField = 'file';
      
      // Use type assertion with unknown as an intermediate step
      const filesMap = files as unknown as { [fieldname: string]: UploadedFile | UploadedFile[] };
      
      if (!(fileField in filesMap)) {
        // Try to get the first available file field
        fileField = Object.keys(filesMap)[0];
        console.log(`[FileUpload] Using field: ${fileField} instead of 'file'`);
      }

      // Get the uploaded file
      const uploadedFile = filesMap[fileField];
      if (Array.isArray(uploadedFile)) {
        console.log('[FileUpload] Multiple files detected for single field');
        return res.status(400).json({ 
          success: false, 
          message: 'Multiple files not supported for this endpoint' 
        });
      }

      // Generate a unique filename
      const fileExt = path.extname(uploadedFile.name);
      const fileName = `${uuidv4()}${fileExt}`;
      const filePath = path.join(fullUploadDir, fileName);

      console.log(`[FileUpload] Saving file as: ${fileName}`);
      console.log(`[FileUpload] Full path: ${filePath}`);
      
      // Remove public from URL path
      const urlPath = uploadDir.replace(/^public\//, '');
      const fileUrl = `/${urlPath}/${fileName}`;
      
      console.log(`[FileUpload] URL path will be: ${fileUrl}`);

      try {
        // Move the file to the upload directory
        await uploadedFile.mv(filePath);
        console.log(`[FileUpload] File moved successfully to ${filePath}`);
      } catch (moveError) {
        console.error('[FileUpload] Error moving file:', moveError);
        throw moveError;
      }

      // Add the upload info to the request for controllers to use
      req.uploadInfo = {
        originalName: uploadedFile.name,
        fileName: fileName,
        filePath: filePath,
        fileUrl: fileUrl,
        fileSize: uploadedFile.size,
        mimetype: uploadedFile.mimetype
      };

      console.log('[FileUpload] Upload info attached to request:', req.uploadInfo);
      next();
    } catch (error) {
      console.error('[FileUpload] Error in middleware:', error);
      return res.status(500).json({
        success: false,
        message: 'File upload failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };
};

// Add upload info to the Request interface
declare global {
  namespace Express {
    interface Request {
      uploadInfo?: {
        originalName: string;
        fileName: string;
        filePath: string;
        fileUrl: string;
        fileSize: number;
        mimetype: string;
      };
    }
  }
} 