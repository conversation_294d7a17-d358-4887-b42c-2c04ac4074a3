import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateLeaveAllocationsTable1703500000000 implements MigrationInterface {
  name = 'CreateLeaveAllocationsTable1703500000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create leave_allocations table
    await queryRunner.createTable(
      new Table({
        name: 'leave_allocations',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'employeeId',
            type: 'int',
          },
          {
            name: 'leaveType',
            type: 'varchar',
            length: '50',
          },
          {
            name: 'year',
            type: 'int',
          },
          {
            name: 'policyAllocation',
            type: 'decimal',
            precision: 10,
            scale: 2,
            default: 0,
          },
          {
            name: 'manualAdjustment',
            type: 'decimal',
            precision: 10,
            scale: 2,
            default: 0,
          },
          {
            name: 'carriedForward',
            type: 'decimal',
            precision: 10,
            scale: 2,
            default: 0,
          },
          {
            name: 'source',
            type: 'enum',
            enum: ['POLICY', 'MANUAL', 'BULK_ADJUSTMENT', 'CARRY_FORWARD'],
            default: "'POLICY'",
          },
          {
            name: 'notes',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true,
          },
          {
            name: 'createdBy',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'datetime',
            precision: 6,
            default: 'CURRENT_TIMESTAMP(6)',
          },
          {
            name: 'updatedAt',
            type: 'datetime',
            precision: 6,
            default: 'CURRENT_TIMESTAMP(6)',
            onUpdate: 'CURRENT_TIMESTAMP(6)',
          },
        ],
      }),
      true
    );

    // Create unique index for employeeId, leaveType, year
    await queryRunner.createIndex(
      'leave_allocations',
      new TableIndex({
        name: 'IDX_leave_allocations_employee_type_year',
        columnNames: ['employeeId', 'leaveType', 'year'],
        isUnique: true
      })
    );

    // Create index for employeeId
    await queryRunner.createIndex(
      'leave_allocations',
      new TableIndex({
        name: 'IDX_leave_allocations_employee',
        columnNames: ['employeeId']
      })
    );

    // Create index for year
    await queryRunner.createIndex(
      'leave_allocations',
      new TableIndex({
        name: 'IDX_leave_allocations_year',
        columnNames: ['year']
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('leave_allocations');
  }
} 