import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { IsNotEmpty, IsNumber, IsDateString, IsOptional, IsBoolean } from 'class-validator';
import { Employee } from './Employee';
import { Shift } from './Shift';

@Entity('shift_assignments')
export class ShiftAssignment {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  @IsNotEmpty({ message: 'Employee ID is required' })
  @IsNumber({}, { message: 'Employee ID must be a valid number' })
  employeeId: number;

  @ManyToOne(() => Employee, { nullable: false })
  @JoinColumn({ name: 'employeeId' })
  employee: Employee;

  @Column()
  @IsNotEmpty({ message: 'Shift ID is required' })
  @IsNumber({}, { message: 'Shift ID must be a valid number' })
  shiftId: number;

  @ManyToOne(() => Shift, { nullable: false })
  @JoinColumn({ name: 'shiftId' })
  shift: Shift;

  @Column({ type: 'date' })
  @IsNotEmpty({ message: 'Start date is required' })
  @IsDateString({}, { message: 'Start date must be a valid date string' })
  startDate: string;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDateString({}, { message: 'End date must be a valid date string' })
  endDate?: string;

  @Column({ default: false })
  @IsBoolean()
  isPermanent: boolean;

  @Column({ nullable: true })
  @IsOptional()
  notes?: string;

  @Column({ default: true })
  @IsBoolean()
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 