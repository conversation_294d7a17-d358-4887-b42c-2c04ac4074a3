export enum LeaveWorkflowStatus {
  // Initial state
  PENDING = 'pending',
  
  // Manager actions
  APPROVED_BY_MANAGER = 'approved_by_manager',
  MODIFIED_BY_MANAGER = 'modified_by_manager',
  REJECTED_BY_MANAGER = 'rejected_by_manager',
  
  // HR actions
  APPROVED_BY_HR = 'approved_by_hr',
  MODIFIED_BY_HR = 'modified_by_hr',
  REJECTED_BY_HR = 'rejected_by_hr',
  
  // Super Admin actions
  APPROVED_BY_SUPER_ADMIN = 'approved_by_super_admin',
  MODIFIED_BY_SUPER_ADMIN = 'modified_by_super_admin',
  REJECTED_BY_SUPER_ADMIN = 'rejected_by_super_admin',
  
  // Final states
  CANCELLED = 'cancelled'
}

export enum UserRole {
  EMPLOYEE = 'employee',
  MANAGER = 'manager',
  HR = 'hr',
  SUPER_ADMIN = 'super_admin'
}

export enum WorkflowActionType {
  SUBMIT = 'submit',
  APPROVE = 'approve',
  REJECT = 'reject',
  MODIFY = 'modify',
  CANCEL = 'cancel',
  OVERRIDE = 'override'
}

export interface LeaveWorkflowRequest {
  id: string;
  employeeId: number;
  employeeName: string;
  employeeCode: string;
  managerId?: number;
  managerName?: string;
  leaveType: string;
  originalStartDate: string;
  originalEndDate: string;
  currentStartDate: string;
  currentEndDate: string;
  originalReason: string;
  currentReason: string;
  status: LeaveWorkflowStatus;
  daysRequested: number;
  emergencyContact?: string;
  attachments?: File[];
  submittedAt: string;
  submittedBy: number;
  currentStage: 'manager' | 'hr' | 'completed';
  isUrgent: boolean;
  department: string;
  position: string;
}

export interface WorkflowActionLog {
  id: string;
  requestId: string;
  actionType: WorkflowActionType;
  performedBy: number;
  performedByName: string;
  performedByRole: UserRole;
  timestamp: string;
  reason?: string;
  previousStatus: LeaveWorkflowStatus;
  newStatus: LeaveWorkflowStatus;
  modifications?: {
    startDate?: {
      from: string;
      to: string;
    };
    endDate?: {
      from: string;
      to: string;
    };
    reason?: {
      from: string;
      to: string;
    };
    leaveType?: {
      from: string;
      to: string;
    };
  };
  comments?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface WorkflowPermissions {
  canApprove: boolean;
  canReject: boolean;
  canModify: boolean;
  canView: boolean;
  canCancel: boolean;
  canOverride: boolean;
  requiresReason: boolean;
  canViewHistory: boolean;
  canReassign: boolean;
}

export interface WorkflowValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface WorkflowNotification {
  id: string;
  requestId: string;
  recipientId: number;
  recipientRole: UserRole;
  type: 'approval_required' | 'status_update' | 'modification' | 'rejection';
  title: string;
  message: string;
  actionUrl?: string;
  isRead: boolean;
  createdAt: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export interface WorkflowEscalation {
  id: string;
  requestId: string;
  escalatedFrom: number;
  escalatedTo: number;
  escalationReason: string;
  escalatedAt: string;
  escalationType: 'timeout' | 'manual' | 'urgent';
  originalDueDate: string;
  newDueDate: string;
}

export interface WorkflowRule {
  id: string;
  name: string;
  description: string;
  conditions: {
    leaveType?: string[];
    department?: string[];
    duration?: {
      min?: number;
      max?: number;
    };
    urgency?: boolean;
    employeeLevel?: string[];
  };
  actions: {
    requiresManagerApproval: boolean;
    requiresHRApproval: boolean;
    autoApprove?: boolean;
    escalationTimeoutHours?: number;
    additionalApprovers?: number[];
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface WorkflowSummary {
  totalRequests: number;
  pendingWithManager: number;
  pendingWithHR: number;
  approved: number;
  rejected: number;
  modified: number;
  cancelled: number;
  overdue: number;
  avgProcessingTime: number;
  requestsByStatus: Record<LeaveWorkflowStatus, number>;
  requestsByDepartment: Record<string, number>;
}

export interface WorkflowConfiguration {
  managerApprovalRequired: boolean;
  hrApprovalRequired: boolean;
  autoApprovalThreshold: number; // days
  escalationTimeoutHours: number;
  requireReasonForRejection: boolean;
  requireReasonForModification: boolean;
  allowEmployeeModification: boolean;
  allowManagerModification: boolean;
  allowHRModification: boolean;
  notificationSettings: {
    emailEnabled: boolean;
    smsEnabled: boolean;
    inAppEnabled: boolean;
    reminderIntervalHours: number;
  };
}

// Status transition mappings
export const WORKFLOW_TRANSITIONS: Record<LeaveWorkflowStatus, LeaveWorkflowStatus[]> = {
  [LeaveWorkflowStatus.PENDING]: [
    LeaveWorkflowStatus.APPROVED_BY_MANAGER,
    LeaveWorkflowStatus.MODIFIED_BY_MANAGER,
    LeaveWorkflowStatus.REJECTED_BY_MANAGER,
    LeaveWorkflowStatus.APPROVED_BY_SUPER_ADMIN,
    LeaveWorkflowStatus.MODIFIED_BY_SUPER_ADMIN,
    LeaveWorkflowStatus.REJECTED_BY_SUPER_ADMIN,
    LeaveWorkflowStatus.CANCELLED
  ],
  [LeaveWorkflowStatus.APPROVED_BY_MANAGER]: [
    LeaveWorkflowStatus.APPROVED_BY_HR,
    LeaveWorkflowStatus.MODIFIED_BY_HR,
    LeaveWorkflowStatus.REJECTED_BY_HR,
    LeaveWorkflowStatus.APPROVED_BY_SUPER_ADMIN,
    LeaveWorkflowStatus.MODIFIED_BY_SUPER_ADMIN,
    LeaveWorkflowStatus.REJECTED_BY_SUPER_ADMIN
  ],
  [LeaveWorkflowStatus.MODIFIED_BY_MANAGER]: [
    LeaveWorkflowStatus.APPROVED_BY_HR,
    LeaveWorkflowStatus.MODIFIED_BY_HR,
    LeaveWorkflowStatus.REJECTED_BY_HR,
    LeaveWorkflowStatus.APPROVED_BY_SUPER_ADMIN,
    LeaveWorkflowStatus.MODIFIED_BY_SUPER_ADMIN,
    LeaveWorkflowStatus.REJECTED_BY_SUPER_ADMIN
  ],
  [LeaveWorkflowStatus.REJECTED_BY_MANAGER]: [
    LeaveWorkflowStatus.APPROVED_BY_SUPER_ADMIN,
    LeaveWorkflowStatus.MODIFIED_BY_SUPER_ADMIN,
    LeaveWorkflowStatus.REJECTED_BY_SUPER_ADMIN
  ],
  [LeaveWorkflowStatus.APPROVED_BY_HR]: [],
  [LeaveWorkflowStatus.MODIFIED_BY_HR]: [],
  [LeaveWorkflowStatus.REJECTED_BY_HR]: [
    LeaveWorkflowStatus.APPROVED_BY_SUPER_ADMIN,
    LeaveWorkflowStatus.MODIFIED_BY_SUPER_ADMIN,
    LeaveWorkflowStatus.REJECTED_BY_SUPER_ADMIN
  ],
  [LeaveWorkflowStatus.APPROVED_BY_SUPER_ADMIN]: [],
  [LeaveWorkflowStatus.MODIFIED_BY_SUPER_ADMIN]: [],
  [LeaveWorkflowStatus.REJECTED_BY_SUPER_ADMIN]: [],
  [LeaveWorkflowStatus.CANCELLED]: []
};

// Role-based permissions
export const ROLE_PERMISSIONS: Record<UserRole, WorkflowPermissions> = {
  [UserRole.EMPLOYEE]: {
    canApprove: false,
    canReject: false,
    canModify: false,
    canView: true,
    canCancel: true,
    canOverride: false,
    requiresReason: true,
    canViewHistory: true,
    canReassign: false
  },
  [UserRole.MANAGER]: {
    canApprove: true,
    canReject: true,
    canModify: true,
    canView: true,
    canCancel: false,
    canOverride: false,
    requiresReason: true,
    canViewHistory: true,
    canReassign: false
  },
  [UserRole.HR]: {
    canApprove: true,
    canReject: true,
    canModify: true,
    canView: true,
    canCancel: false,
    canOverride: false,
    requiresReason: true,
    canViewHistory: true,
    canReassign: true
  },
  [UserRole.SUPER_ADMIN]: {
    canApprove: true,
    canReject: true,
    canModify: true,
    canView: true,
    canCancel: true,
    canOverride: true,
    requiresReason: true,
    canViewHistory: true,
    canReassign: true
  }
};

// Status display configurations
export const STATUS_CONFIG: Record<LeaveWorkflowStatus, {
  label: string;
  color: string;
  icon: string;
  description: string;
}> = {
  [LeaveWorkflowStatus.PENDING]: {
    label: 'Pending Manager Approval',
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    icon: '⏳',
    description: 'Waiting for manager to review and approve'
  },
  [LeaveWorkflowStatus.APPROVED_BY_MANAGER]: {
    label: 'Approved by Manager',
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    icon: '👨‍💼',
    description: 'Manager approved, pending HR review'
  },
  [LeaveWorkflowStatus.MODIFIED_BY_MANAGER]: {
    label: 'Modified by Manager',
    color: 'bg-orange-100 text-orange-800 border-orange-200',
    icon: '✏️',
    description: 'Manager modified the request, pending HR review'
  },
  [LeaveWorkflowStatus.REJECTED_BY_MANAGER]: {
    label: 'Rejected by Manager',
    color: 'bg-red-100 text-red-800 border-red-200',
    icon: '❌',
    description: 'Manager rejected the request'
  },
  [LeaveWorkflowStatus.APPROVED_BY_HR]: {
    label: 'Approved by HR',
    color: 'bg-green-100 text-green-800 border-green-200',
    icon: '✅',
    description: 'Final approval granted by HR'
  },
  [LeaveWorkflowStatus.MODIFIED_BY_HR]: {
    label: 'Modified by HR',
    color: 'bg-purple-100 text-purple-800 border-purple-200',
    icon: '🔄',
    description: 'HR modified and approved the request'
  },
  [LeaveWorkflowStatus.REJECTED_BY_HR]: {
    label: 'Rejected by HR',
    color: 'bg-red-100 text-red-800 border-red-200',
    icon: '🚫',
    description: 'Final rejection by HR'
  },
  [LeaveWorkflowStatus.APPROVED_BY_SUPER_ADMIN]: {
    label: 'Approved by Super Admin',
    color: 'bg-emerald-100 text-emerald-800 border-emerald-200',
    icon: '👑',
    description: 'Override approval by Super Admin'
  },
  [LeaveWorkflowStatus.MODIFIED_BY_SUPER_ADMIN]: {
    label: 'Modified by Super Admin',
    color: 'bg-indigo-100 text-indigo-800 border-indigo-200',
    icon: '⚡',
    description: 'Super Admin modified and approved'
  },
  [LeaveWorkflowStatus.REJECTED_BY_SUPER_ADMIN]: {
    label: 'Rejected by Super Admin',
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    icon: '🔒',
    description: 'Final rejection by Super Admin'
  },
  [LeaveWorkflowStatus.CANCELLED]: {
    label: 'Cancelled',
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    icon: '🚫',
    description: 'Request cancelled by employee'
  }
}; 