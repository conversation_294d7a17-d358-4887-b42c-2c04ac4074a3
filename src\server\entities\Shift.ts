import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { IsNotEmpty, IsOptional, IsNumber, IsBoolean, IsArray } from 'class-validator';

@Entity('shifts')
export class Shift {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 100 })
  @IsNotEmpty({ message: 'Shift name is required' })
  name: string;

  @Column({ nullable: true })
  @IsOptional()
  description?: string;

  @Column({ type: 'time' })
  @IsNotEmpty({ message: 'Start time is required' })
  startTime: string;

  @Column({ type: 'time' })
  @IsNotEmpty({ message: 'End time is required' })
  endTime: string;

  @Column({ type: 'int', default: 0 })
  @IsNumber({}, { message: 'Break duration must be a number' })
  breakDuration: number;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  @IsArray()
  workingDays?: number[]; // Array of day numbers (0-6, Sunday to Saturday)

  @Column({ default: false })
  @IsBoolean()
  isFlexible: boolean;

  @Column({ type: 'int', default: 15 })
  @IsNumber({}, { message: 'Grace time must be a number' })
  graceTimeInMinutes: number;

  @Column({ type: 'float', default: 8 })
  @IsNumber({}, { message: 'Required work hours must be a number' })
  requiredWorkHours: number;

  @Column({ nullable: true, length: 7 })
  @IsOptional()
  color?: string; // Hex color code

  @Column({ default: true })
  @IsBoolean()
  isActive: boolean;

  @Column({ type: 'int', default: 4 })
  @IsNumber({}, { message: 'Half day hours must be a number' })
  halfDayHours: number;

  @Column({ type: 'time', nullable: true })
  @IsOptional()
  breakStartTime?: string;

  @Column({ type: 'time', nullable: true })
  @IsOptional()
  breakEndTime?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Computed property to determine if this is a night shift
  get isNightShift(): boolean {
    const start = this.startTime;
    const end = this.endTime;
    
    // Convert time strings to comparable numbers (HHMM format)
    // Handle both HH:MM and HH:MM:SS formats
    const startNum = parseInt(start.split(':').slice(0, 2).join(''));
    const endNum = parseInt(end.split(':').slice(0, 2).join(''));
    
    // Night shift if:
    // 1. Starts after 6 PM (1800) OR
    // 2. Ends before 6 AM (0600) OR  
    // 3. Crosses midnight (end time < start time)
    return startNum >= 1800 || endNum <= 600 || endNum < startNum;
  }

  // Helper method to check if shift crosses midnight
  get crossesMidnight(): boolean {
    // Handle both HH:MM and HH:MM:SS formats
    const startNum = parseInt(this.startTime.split(':').slice(0, 2).join(''));
    const endNum = parseInt(this.endTime.split(':').slice(0, 2).join(''));
    return endNum < startNum;
  }
} 