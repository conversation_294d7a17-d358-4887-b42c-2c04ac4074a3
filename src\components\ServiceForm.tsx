import React, { useState } from 'react';
import {
  X,
  Plus,
  Save,
  Clock,
  Shield,
  Link,
  FileCheck,
  Info,
  Gauge,
  CheckCircle,
  Bell,
  CreditCard,
  HeadphonesIcon,
  GitBranch,
  FileText,
  Tag,
  Hash,
  AlertCircle,
  Activity,
  Calendar,
  Percent,
  DollarSign,
  Mail,
  Phone,
  MessageSquare,
  Globe,
  ExternalLink,
  Trash2,
  CheckSquare,
  BarChart2,
  Building2
} from 'lucide-react';

// Import Vendor type from VendorContacts
import type { Vendor } from './VendorContacts';

// Add these styles at the top of the file after imports
const scrollbarHideStyles = `
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
`;

const inputStyles = `
  .form-input {
    @apply block w-full px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900 text-sm
    transition-all duration-200 ease-in-out;
    @apply focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 focus:outline-none;
    @apply placeholder:text-gray-400;
  }

  .form-select {
    @apply block w-full px-3 py-2 rounded-lg border border-gray-300 bg-white text-gray-900 text-sm
    transition-all duration-200 ease-in-out appearance-none bg-no-repeat;
    @apply focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 focus:outline-none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
  }

  .form-textarea {
    @apply block w-full px-4 py-3 rounded-lg border border-gray-300 bg-gray-50 text-gray-600 text-sm
    transition-all duration-200 ease-in-out;
    @apply focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 focus:outline-none focus:bg-white;
    @apply placeholder:text-gray-400;
    height: 80px;
    resize: none;
    line-height: 1.5;
    width: 100%;
  }

  .description-wrapper {
    @apply relative;
    width: 100%;
    max-width: 800px;
  }

  .form-content {
    @apply px-6 py-4;
    width: 100%;
  }

  .character-count {
    @apply absolute bottom-3 right-3 text-xs text-gray-400 select-none pointer-events-none;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .input-group {
    @apply relative flex items-center;
  }

  .input-group-text {
    @apply inline-flex items-center px-4 py-3 rounded-l-lg border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm;
  }

  .input-group .form-input {
    @apply rounded-l-none;
  }
`;

// Combine the styles
const styles = `
  ${scrollbarHideStyles}
  ${inputStyles}
`;

interface PricingTier {
  name: string;
  price: number;
  billingCycle: 'monthly' | 'quarterly' | 'annually';
  features: string[];
}

interface PricingType {
  type: 'fixed' | 'hourly' | 'per_unit' | 'tiered';
  amount: number;
  currency: 'PKR' | 'USD' | 'EUR';
  tiers?: PricingTier[];
  minimumCommitment?: number;
  discounts?: {
    type: 'volume' | 'early-payment' | 'long-term';
    percentage: number;
    conditions: string;
  }[];
}

interface SLAMetrics {
  responseTime: number;
  uptime: number;
  penalties: string;
  resolutionTime: number;
  availabilityHours: string;
  escalationMatrix: {
    level: number;
    contact: string;
    responseTime: number;
  }[];
  maintenanceWindows: {
    day: string;
    time: string;
    duration: number;
  }[];
}

interface ServiceFormData {
  name: string;
  vendorId: string;
  description: string;
  category: string;
  subcategory?: string;
  tags: string[];
  criticality: 'high' | 'medium' | 'low';
  hasSLA: boolean;
  sla: {
    responseTime: number;
    responseTimeUnit: 'minutes' | 'hours';
    uptime: number;
    resolutionTime: number;
    resolutionTimeUnit: 'minutes' | 'hours';
  };
  performance: {
    current: number;
    target: number;
  };
  userAdoption: {
    current: number;
    target: number;
  };
  compliance: {
    required: number;
    current: number;
    lastAudit: string;
    nextDue: string;
    standards: string[];
  };
  integrations: {
    monitoring: {
      status: 'active' | 'inactive';
    };
  };
  status: 'active' | 'pending' | 'suspended' | 'terminated';
  pricing: PricingType;
  supportChannels: {
    type: 'email' | 'phone' | 'portal' | 'chat';
    contact: string;
    availability: string;
  }[];
  dependencies: string[];
  documentation: {
    type: string;
    url: string;
    lastUpdated: string;
  }[];
}

interface ServiceFormProps {
  onClose: () => void;
  onSubmit: (data: ServiceFormData) => void;
  initialData?: ServiceFormData;
  vendors: Vendor[];
}

const progressBarColors = {
  low: 'bg-orange-500',
  medium: 'bg-blue-500',
  high: 'bg-green-500',
  danger: 'bg-red-500'
};

function ProgressBar({ value, target, color = 'bg-blue-500' }: { value: number; target?: number; color?: string }) {
  return (
    <div className="relative w-full h-2 bg-gray-200 rounded-full overflow-hidden">
      <div
        className={`absolute left-0 top-0 h-full ${color} transition-all duration-300`}
        style={{ width: `${Math.min(value, 100)}%` }}
      />
      {target && (
        <div
          className="absolute top-0 h-full w-0.5 bg-gray-600"
          style={{ left: `${target}%` }}
        />
      )}
    </div>
  );
}

function MetricCard({ 
  title, 
  value, 
  target, 
  icon: Icon, 
  color = 'blue',
  suffix = '%',
  children 
}: { 
  title: string;
  value: number;
  target?: number;
  icon: any;
  color?: string;
  suffix?: string;
  children?: React.ReactNode;
}) {
  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <div className={`p-2 rounded-lg bg-${color}-50 mr-3`}>
            <Icon className={`h-5 w-5 text-${color}-500`} />
          </div>
          <h3 className="text-sm font-medium text-gray-900">{title}</h3>
        </div>
        {target && (
          <span className="text-xs font-medium text-gray-500">
            Target: {target}{suffix}
          </span>
        )}
      </div>
      <div className="flex items-end justify-between mb-2">
        <div className="flex items-baseline">
          <span className="text-2xl font-semibold text-gray-900">{value}</span>
          <span className="ml-1 text-sm text-gray-500">{suffix}</span>
        </div>
      </div>
      <ProgressBar value={value} target={target} color={`bg-${color}-500`} />
      {children}
    </div>
  );
}

function StatusBadge({ status }: { status: ServiceFormData['status'] }) {
  const statusConfig = {
    active: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
    pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
    suspended: { color: 'bg-red-100 text-red-800', icon: AlertCircle },
    terminated: { color: 'bg-gray-100 text-gray-800', icon: X }
  };

  const config = statusConfig[status];
  const Icon = config.icon;

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
      <Icon className="w-3 h-3 mr-1" />
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
}

export function ServiceForm({ onClose, onSubmit, initialData, vendors = [] }: ServiceFormProps) {
  const [activeTab, setActiveTab] = useState('basic');
  const [formData, setFormData] = useState<ServiceFormData>(initialData || {
    name: '',
    vendorId: '',
    description: '',
    category: '',
    tags: [],
    criticality: 'medium',
    hasSLA: true,
    sla: {
      responseTime: 1,
      responseTimeUnit: 'hours',
      uptime: 99.99,
      resolutionTime: 4,
      resolutionTimeUnit: 'hours'
    },
    performance: {
      current: 95,
      target: 95
    },
    userAdoption: {
      current: 88,
      target: 100
    },
    compliance: {
      required: 98,
      current: 100,
      lastAudit: new Date().toISOString().split('T')[0],
      nextDue: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      standards: []
    },
    integrations: {
      monitoring: {
        status: 'active'
      }
    },
    status: 'active',
    pricing: {
      type: 'fixed',
      amount: 0,
      currency: 'PKR'
    },
    supportChannels: [],
    dependencies: [],
    documentation: []
  });

  const [tag, setTag] = useState('');
  const [supportChannel, setSupportChannel] = useState<ServiceFormData['supportChannels'][0]>({
    type: 'email',
    contact: '',
    availability: ''
  });
  const [dependency, setDependency] = useState('');
  const [documentation, setDocumentation] = useState({
    type: '',
    url: '',
    lastUpdated: new Date().toISOString().split('T')[0]
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const addTag = () => {
    if (tag && !formData.tags.includes(tag)) {
      setFormData({
        ...formData,
        tags: [...formData.tags, tag]
      });
      setTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData({
      ...formData,
      tags: formData.tags.filter(t => t !== tagToRemove)
    });
  };

  const addSupportChannel = () => {
    if (supportChannel.contact && supportChannel.availability) {
      setFormData({
        ...formData,
        supportChannels: [...formData.supportChannels, { ...supportChannel }]
      });
      setSupportChannel({
        type: 'email',
        contact: '',
        availability: ''
      });
    }
  };

  const addDependency = () => {
    if (dependency && !formData.dependencies.includes(dependency)) {
      setFormData({
        ...formData,
        dependencies: [...formData.dependencies, dependency]
      });
      setDependency('');
    }
  };

  const addDocumentation = () => {
    if (documentation.type && documentation.url) {
      setFormData({
        ...formData,
        documentation: [...formData.documentation, { ...documentation }]
      });
      setDocumentation({
        type: '',
        url: '',
        lastUpdated: new Date().toISOString().split('T')[0]
      });
    }
  };

  const tabs = [
    { id: 'basic', label: 'Basic Info', icon: Info },
    { id: 'sla', label: 'SLA', icon: Clock },
    { id: 'performance', label: 'Performance', icon: Gauge },
    { id: 'compliance', label: 'Compliance', icon: Shield },
    { id: 'pricing', label: 'Pricing', icon: CreditCard },
    { id: 'support', label: 'Support', icon: HeadphonesIcon },
    { id: 'dependencies', label: 'Dependencies', icon: GitBranch },
    { id: 'documentation', label: 'Documentation', icon: FileText },
  ];

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div className="relative w-full max-w-5xl mx-4">
        <div className="bg-white rounded-xl shadow-xl">
          {/* Header */}
          <div className="px-4 py-3 border-b border-gray-200 flex justify-between items-center">
            <div>
              <h3 className="text-xl font-semibold text-gray-900">
                {initialData ? 'Edit Service' : 'Add New Service'}
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Fill in the service details across all sections
              </p>
            </div>
            <button
              onClick={onClose}
              className="p-2 rounded-lg text-gray-400 hover:text-gray-500 hover:bg-gray-100"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Tabs */}
          <div className="px-4 border-b border-gray-200">
            <nav className="flex space-x-2 -mb-px">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center px-3 py-2 text-sm font-medium rounded-t-lg transition-colors duration-200 whitespace-nowrap ${
                      activeTab === tab.id
                        ? 'bg-white text-blue-600 border-t border-l border-r border-gray-200 -mb-px'
                        : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="h-4 w-4 mr-1.5" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Form Content */}
          <div className="p-4">
            <form onSubmit={handleSubmit} className="space-y-4" id="service-form">
              <div className="bg-white">
                {/* Basic Information Tab */}
                {activeTab === 'basic' && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-2 gap-6">
                      <div>
                        <label className="form-label flex items-center">
                          <Building2 className="w-4 h-4 mr-1.5 text-gray-400" />
                          Vendor Name
                        </label>
                        <select
                          required
                          value={formData.vendorId}
                          onChange={(e) => setFormData({ ...formData, vendorId: e.target.value })}
                          className="form-select"
                        >
                          <option value="">Select a vendor</option>
                          {vendors.map((vendor) => (
                            <option key={vendor.id} value={vendor.id}>
                              {vendor.vendorName} - {vendor.companyName}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="form-label flex items-center">
                          <Hash className="w-4 h-4 mr-1.5 text-gray-400" />
                          Service Name
                        </label>
                        <input
                          type="text"
                          required
                          value={formData.name}
                          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                          className="form-input"
                          placeholder="Enter service name"
                        />
                      </div>
                    </div>

                    <div className="w-full max-w-[800px]">
                      <div className="description-wrapper">
                        <label className="form-label flex items-center gap-1">
                          <FileText className="w-4 h-4 text-gray-400" />
                          <span>Description</span>
                          <span className="text-gray-400 text-sm font-normal">(Max 500 characters)</span>
                        </label>
                        <div className="relative">
                          <textarea
                            required
                            value={formData.description}
                            onChange={(e) => {
                              const value = e.target.value;
                              if (value.length <= 500) {
                                setFormData({ ...formData, description: value });
                              }
                            }}
                            className="form-textarea"
                            placeholder="Describe the service, its key features, and main benefits to users..."
                            style={{ width: '800px', height: '80px' }}
                          />
                          <span className="character-count">
                            {formData.description.length}/500
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-6">
                      <div>
                        <label className="form-label flex items-center">
                          <AlertCircle className="w-4 h-4 mr-1.5 text-gray-400" />
                          Criticality
                        </label>
                        <select
                          required
                          value={formData.criticality}
                          onChange={(e) => setFormData({ ...formData, criticality: e.target.value as 'high' | 'medium' | 'low' })}
                          className="form-select"
                        >
                          <option value="low">Low</option>
                          <option value="medium">Medium</option>
                          <option value="high">High</option>
                        </select>
                      </div>
                      <div>
                        <label className="form-label flex items-center">
                          <Activity className="w-4 h-4 mr-1.5 text-gray-400" />
                          Status
                        </label>
                        <select
                          required
                          value={formData.status}
                          onChange={(e) => setFormData({ ...formData, status: e.target.value as ServiceFormData['status'] })}
                          className="form-select"
                        >
                          <option value="active">Active</option>
                          <option value="pending">Pending</option>
                          <option value="suspended">Suspended</option>
                          <option value="terminated">Terminated</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="form-label flex items-center">
                        <Tag className="w-4 h-4 mr-1.5 text-gray-400" />
                        Tags
                      </label>
                      <div className="flex flex-wrap gap-2 p-4 border rounded-lg bg-gray-50/50 mb-3">
                        {formData.tags.map((t) => (
                          <span
                            key={t}
                            className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                          >
                            {t}
                            <button
                              type="button"
                              onClick={() => removeTag(t)}
                              className="ml-2 text-blue-600 hover:text-blue-800"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </span>
                        ))}
                        {formData.tags.length === 0 && (
                          <span className="text-sm text-gray-500">No tags added yet</span>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <input
                          type="text"
                          value={tag}
                          onChange={(e) => setTag(e.target.value)}
                          className="form-input"
                          placeholder="Add a tag"
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                              addTag();
                            }
                          }}
                        />
                        <button
                          type="button"
                          onClick={addTag}
                          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
                        >
                          <Plus className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* SLA Metrics Tab */}
                {activeTab === 'sla' && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      <MetricCard
                        title="Response Time"
                        value={formData.sla.responseTime}
                        icon={Clock}
                        color="blue"
                        suffix={formData.sla.responseTimeUnit}
                      >
                        <div className="mt-4">
                          <div className="flex items-center gap-2">
                            <input
                              type="number"
                              value={formData.sla.responseTime}
                              onChange={(e) => setFormData({
                                ...formData,
                                sla: { ...formData.sla, responseTime: parseFloat(e.target.value) }
                              })}
                              className="form-input"
                            />
                            <select
                              value={formData.sla.responseTimeUnit}
                              onChange={(e) => setFormData({
                                ...formData,
                                sla: { ...formData.sla, responseTimeUnit: e.target.value as 'minutes' | 'hours' }
                              })}
                              className="form-select"
                            >
                              <option value="minutes">Minutes</option>
                              <option value="hours">Hours</option>
                            </select>
                          </div>
                        </div>
                      </MetricCard>

                      <MetricCard
                        title="Uptime"
                        value={formData.sla.uptime}
                        target={99.9}
                        icon={Gauge}
                        color="green"
                      >
                        <div className="mt-4">
                          <input
                            type="number"
                            step="0.01"
                            max="100"
                            value={formData.sla.uptime}
                            onChange={(e) => setFormData({
                              ...formData,
                              sla: { ...formData.sla, uptime: parseFloat(e.target.value) }
                            })}
                            className="form-input"
                          />
                        </div>
                      </MetricCard>

                      <MetricCard
                        title="Resolution Time"
                        value={formData.sla.resolutionTime}
                        icon={Clock}
                        color="purple"
                        suffix={formData.sla.resolutionTimeUnit}
                      >
                        <div className="mt-4">
                          <div className="flex items-center gap-2">
                            <input
                              type="number"
                              value={formData.sla.resolutionTime}
                              onChange={(e) => setFormData({
                                ...formData,
                                sla: { ...formData.sla, resolutionTime: parseFloat(e.target.value) }
                              })}
                              className="form-input"
                            />
                            <select
                              value={formData.sla.resolutionTimeUnit}
                              onChange={(e) => setFormData({
                                ...formData,
                                sla: { ...formData.sla, resolutionTimeUnit: e.target.value as 'minutes' | 'hours' }
                              })}
                              className="form-select"
                            >
                              <option value="minutes">Minutes</option>
                              <option value="hours">Hours</option>
                            </select>
                          </div>
                        </div>
                      </MetricCard>
                    </div>
                  </div>
                )}

                {/* Performance Metrics Tab */}
                {activeTab === 'performance' && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <MetricCard
                        title="Performance"
                        value={formData.performance.current}
                        target={formData.performance.target}
                        icon={Gauge}
                        color="blue"
                      >
                        <div className="mt-4 space-y-4">
                          <div>
                            <label className="form-label">Current (%)</label>
                            <input
                              type="number"
                              max="100"
                              value={formData.performance.current}
                              onChange={(e) => setFormData({
                                ...formData,
                                performance: { ...formData.performance, current: parseFloat(e.target.value) }
                              })}
                              className="form-input"
                            />
                          </div>
                          <div>
                            <label className="form-label">Target (%)</label>
                            <input
                              type="number"
                              max="100"
                              value={formData.performance.target}
                              onChange={(e) => setFormData({
                                ...formData,
                                performance: { ...formData.performance, target: parseFloat(e.target.value) }
                              })}
                              className="form-input"
                            />
                          </div>
                        </div>
                      </MetricCard>

                      <MetricCard
                        title="User Adoption"
                        value={formData.userAdoption.current}
                        target={formData.userAdoption.target}
                        icon={CheckCircle}
                        color="green"
                      >
                        <div className="mt-4">
                          <label className="form-label">Current (%)</label>
                          <input
                            type="number"
                            max="100"
                            value={formData.userAdoption.current}
                            onChange={(e) => setFormData({
                              ...formData,
                              userAdoption: { ...formData.userAdoption, current: parseFloat(e.target.value) }
                            })}
                            className="form-input"
                          />
                        </div>
                      </MetricCard>
                    </div>
                  </div>
                )}

                {/* Compliance Information Tab */}
                {activeTab === 'compliance' && (
                  <div className="space-y-6">
                    <div className="flex items-center mb-4">
                      <Shield className="h-5 w-5 text-blue-500 mr-2" />
                      <h4 className="text-lg font-medium text-gray-900">Compliance</h4>
                    </div>
                    <div className="grid grid-cols-2 gap-6">
                      <div>
                        <label className="form-label flex items-center">
                          <Percent className="w-4 h-4 mr-1.5 text-gray-400" />
                          Required Compliance (%)
                        </label>
                        <input
                          type="number"
                          max="100"
                          value={formData.compliance.required}
                          onChange={(e) => setFormData({
                            ...formData,
                            compliance: { ...formData.compliance, required: parseFloat(e.target.value) }
                          })}
                          className="form-input"
                        />
                      </div>
                      <div>
                        <label className="form-label flex items-center">
                          <BarChart2 className="w-4 h-4 mr-1.5 text-gray-400" />
                          Current Compliance (%)
                        </label>
                        <input
                          type="number"
                          max="100"
                          value={formData.compliance.current}
                          onChange={(e) => setFormData({
                            ...formData,
                            compliance: { ...formData.compliance, current: parseFloat(e.target.value) }
                          })}
                          className="form-input"
                        />
                      </div>
                      <div>
                        <label className="form-label flex items-center">
                          <Calendar className="w-4 h-4 mr-1.5 text-gray-400" />
                          Last Audit Date
                        </label>
                        <input
                          type="date"
                          value={formData.compliance.lastAudit}
                          onChange={(e) => setFormData({
                            ...formData,
                            compliance: { ...formData.compliance, lastAudit: e.target.value }
                          })}
                          className="form-input"
                        />
                      </div>
                      <div>
                        <label className="form-label flex items-center">
                          <Calendar className="w-4 h-4 mr-1.5 text-gray-400" />
                          Next Due Date
                        </label>
                        <input
                          type="date"
                          value={formData.compliance.nextDue}
                          onChange={(e) => setFormData({
                            ...formData,
                            compliance: { ...formData.compliance, nextDue: e.target.value }
                          })}
                          className="form-input"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* Pricing Information Tab */}
                {activeTab === 'pricing' && (
                  <div className="space-y-6">
                    <div className="flex items-center mb-4">
                      <CreditCard className="h-5 w-5 text-blue-500 mr-2" />
                      <h4 className="text-lg font-medium text-gray-900">Pricing</h4>
                    </div>
                    <div className="grid grid-cols-2 gap-6">
                      <div>
                        <label className="form-label flex items-center">
                          <Activity className="w-4 h-4 mr-1.5 text-gray-400" />
                          Type
                        </label>
                        <select
                          required
                          value={formData.pricing.type}
                          onChange={(e) => setFormData({
                            ...formData,
                            pricing: { ...formData.pricing, type: e.target.value as PricingType['type'] }
                          })}
                          className="form-select"
                        >
                          <option value="fixed">Fixed</option>
                          <option value="hourly">Hourly</option>
                          <option value="per_unit">Per Unit</option>
                          <option value="tiered">Tiered</option>
                        </select>
                      </div>
                      <div>
                        <label className="form-label flex items-center">
                          <DollarSign className="w-4 h-4 mr-1.5 text-gray-400" />
                          Amount
                        </label>
                        <div className="mt-1 flex rounded-md shadow-sm">
                          <span className="input-group-text">
                            {formData.pricing.currency}
                          </span>
                          <input
                            type="number"
                            required
                            value={formData.pricing.amount}
                            onChange={(e) => setFormData({
                              ...formData,
                              pricing: { ...formData.pricing, amount: parseFloat(e.target.value) }
                            })}
                            className="form-input"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Support Channels Tab */}
                {activeTab === 'support' && (
                  <div className="space-y-6">
                    <div className="flex items-center mb-4">
                      <HeadphonesIcon className="h-5 w-5 text-blue-500 mr-2" />
                      <h4 className="text-lg font-medium text-gray-900">Support Channels</h4>
                    </div>
                    <div className="grid grid-cols-3 gap-6">
                      <div>
                        <label className="form-label flex items-center">
                          <MessageSquare className="w-4 h-4 mr-1.5 text-gray-400" />
                          Type
                        </label>
                        <select
                          value={supportChannel.type}
                          onChange={(e) => setSupportChannel({
                            ...supportChannel,
                            type: e.target.value as ServiceFormData['supportChannels'][0]['type']
                          })}
                          className="form-select"
                        >
                          <option value="email">
                            <Mail className="w-4 h-4 mr-1 inline" /> Email
                          </option>
                          <option value="phone">
                            <Phone className="w-4 h-4 mr-1 inline" /> Phone
                          </option>
                          <option value="portal">
                            <Globe className="w-4 h-4 mr-1 inline" /> Portal
                          </option>
                          <option value="chat">
                            <MessageSquare className="w-4 h-4 mr-1 inline" /> Chat
                          </option>
                        </select>
                      </div>
                      <div>
                        <label className="form-label">Contact</label>
                        <input
                          type="text"
                          value={supportChannel.contact}
                          onChange={(e) => setSupportChannel({ ...supportChannel, contact: e.target.value })}
                          className="form-input"
                          placeholder="Contact information"
                        />
                      </div>
                      <div>
                        <label className="form-label">Availability</label>
                        <input
                          type="text"
                          value={supportChannel.availability}
                          onChange={(e) => setSupportChannel({ ...supportChannel, availability: e.target.value })}
                          className="form-input"
                          placeholder="e.g., 24/7, 9-5"
                        />
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={addSupportChannel}
                      className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                    >
                      <Plus className="h-5 w-5 mr-1" />
                      Add Channel
                    </button>
                    <div className="mt-2 space-y-2">
                      {formData.supportChannels.map((channel, index) => (
                        <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded-md">
                          <span className="text-sm">{channel.type}: {channel.contact} ({channel.availability})</span>
                          <button
                            type="button"
                            onClick={() => setFormData({
                              ...formData,
                              supportChannels: formData.supportChannels.filter((_, i) => i !== index)
                            })}
                            className="text-red-600 hover:text-red-800"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Dependencies Tab */}
                {activeTab === 'dependencies' && (
                  <div className="space-y-6">
                    <div className="flex items-center mb-4">
                      <GitBranch className="h-5 w-5 text-blue-500 mr-2" />
                      <h4 className="text-lg font-medium text-gray-900">Dependencies</h4>
                    </div>
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={dependency}
                        onChange={(e) => setDependency(e.target.value)}
                        className="form-input"
                        placeholder="Add a dependency"
                      />
                      <button
                        type="button"
                        onClick={addDependency}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                      >
                        <Plus className="h-5 w-5" />
                      </button>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {formData.dependencies.map((dep, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {dep}
                          <button
                            type="button"
                            onClick={() => setFormData({
                              ...formData,
                              dependencies: formData.dependencies.filter((_, i) => i !== index)
                            })}
                            className="ml-1 text-blue-600 hover:text-blue-800"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Documentation Tab */}
                {activeTab === 'documentation' && (
                  <div className="space-y-6">
                    <div className="flex items-center mb-4">
                      <FileText className="h-5 w-5 text-blue-500 mr-2" />
                      <h4 className="text-lg font-medium text-gray-900">Documentation</h4>
                    </div>
                    <div className="grid grid-cols-3 gap-6">
                      <div>
                        <label className="form-label flex items-center">
                          <FileCheck className="w-4 h-4 mr-1.5 text-gray-400" />
                          Type
                        </label>
                        <input
                          type="text"
                          value={documentation.type}
                          onChange={(e) => setDocumentation({ ...documentation, type: e.target.value })}
                          className="form-input"
                          placeholder="e.g., API, User Guide"
                        />
                      </div>
                      <div className="col-span-2">
                        <label className="form-label flex items-center">
                          <Link className="w-4 h-4 mr-1.5 text-gray-400" />
                          URL
                        </label>
                        <input
                          type="url"
                          value={documentation.url}
                          onChange={(e) => setDocumentation({ ...documentation, url: e.target.value })}
                          className="form-input"
                          placeholder="Documentation URL"
                        />
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={addDocumentation}
                      className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                    >
                      <Plus className="h-5 w-5 mr-1" />
                      Add Documentation
                    </button>
                    <div className="mt-2 space-y-2">
                      {formData.documentation.map((doc, index) => (
                        <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded-md">
                          <div className="flex items-center">
                            <FileCheck className="h-5 w-5 text-gray-400 mr-2" />
                            <span className="text-sm">{doc.type}: <a href={doc.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">{doc.url}</a></span>
                          </div>
                          <button
                            type="button"
                            onClick={() => setFormData({
                              ...formData,
                              documentation: formData.documentation.filter((_, i) => i !== index)
                            })}
                            className="text-red-600 hover:text-red-800"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </form>
          </div>

          {/* Footer Actions */}
          <div className="px-4 py-3 border-t border-gray-200 flex justify-end gap-3">
            <button
              type="button"
              onClick={onClose}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <X className="h-4 w-4 mr-1.5" />
              Cancel
            </button>
            <button
              type="submit"
              form="service-form"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
            >
              <Save className="h-4 w-4 mr-1.5" />
              {initialData ? 'Update Service' : 'Add Service'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 