import axios from 'axios';
import { SoftwareLicense, CreateSoftwareLicenseDto } from '../types/SoftwareLicense';

export class SoftwareLicenseService {
  private baseUrl = '/api/software-licenses';

  async create(data: CreateSoftwareLicenseDto): Promise<SoftwareLicense> {
    try {
      console.log('Creating software license with data:', data);
      console.log('User assignments being sent:', data.assignedUserIds);
      
      const response = await axios.post(this.baseUrl, data);
      console.log('Full response from create API:', response);
      
      // Log assigned users in response
      if (response.data && response.data.assignedUsers) {
        console.log('Assigned users in API response:', response.data.assignedUsers);
      }
      
      return response.data;
    } catch (error) {
      console.error('Error creating software license:', error);
      if (axios.isAxiosError(error) && error.response) {
        console.error('Server response:', error.response.data);
        console.error('Status code:', error.response.status);
      }
      throw error;
    }
  }

  async update(id: string, data: Partial<CreateSoftwareLicenseDto>): Promise<SoftwareLicense> {
    try {
      console.log('Updating software license with data:', data);
      console.log('User assignments being sent in update:', data.assignedUserIds);
      
      const response = await axios.put(`${this.baseUrl}/${id}`, data);
      console.log('Full response from update API:', response);
      
      // Log assigned users in response
      if (response.data && response.data.assignedUsers) {
        console.log('Assigned users in API response:', response.data.assignedUsers);
      }
      
      return response.data;
    } catch (error) {
      console.error('Error updating software license:', error);
      if (axios.isAxiosError(error) && error.response) {
        console.error('Server response:', error.response.data);
        console.error('Status code:', error.response.status);
      }
      throw error;
    }
  }

  async delete(id: string): Promise<void> {
    try {
      await axios.delete(`${this.baseUrl}/${id}`);
    } catch (error) {
      console.error('Error deleting software license:', error);
      if (axios.isAxiosError(error) && error.response) {
        console.error('Server response:', error.response.data);
      }
      throw error;
    }
  }

  async getById(id: string): Promise<SoftwareLicense> {
    try {
      console.log('Fetching software license with ID:', id);
      const response = await axios.get(`${this.baseUrl}/${id}`);
      console.log('License data retrieved from API:', response.data);
      
      // Check if assignedUsers exists and is properly formed
      if (response.data) {
        if (response.data.assignedUsers) {
          console.log('Assigned users in getById response:', response.data.assignedUsers);
          
          // Check if assigned users have the expected structure
          if (Array.isArray(response.data.assignedUsers)) {
            console.log('Number of assigned users:', response.data.assignedUsers.length);
            response.data.assignedUsers.forEach((user: any, index: number) => {
              console.log(`User ${index}:`, user);
            });
          } else {
            console.warn('Warning: assignedUsers is not an array!', response.data.assignedUsers);
          }
        } else {
          console.warn('Warning: No assignedUsers field in response data!');
        }
        
        // Check linked assets too
        if (response.data.linkedAssets) {
          console.log('Linked assets in getById response:', response.data.linkedAssets);
        } else {
          console.warn('Warning: No linkedAssets field in response data!');
        }
      }
      
      return response.data;
    } catch (error) {
      console.error('Error fetching software license:', error);
      if (axios.isAxiosError(error) && error.response) {
        console.error('Server response:', error.response.data);
      }
      throw error;
    }
  }

  async getAll(): Promise<SoftwareLicense[]> {
    try {
      const response = await axios.get(this.baseUrl);
      return response.data;
    } catch (error) {
      console.error('Error fetching software licenses:', error);
      if (axios.isAxiosError(error) && error.response) {
        console.error('Server response:', error.response.data);
      }
      throw error;
    }
  }

  async getByVendor(vendorId: string): Promise<SoftwareLicense[]> {
    try {
      const response = await axios.get(`${this.baseUrl}/vendor/${vendorId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching vendor software licenses:', error);
      if (axios.isAxiosError(error) && error.response) {
        console.error('Server response:', error.response.data);
      }
      throw error;
    }
  }
} 