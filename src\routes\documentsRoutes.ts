import express from 'express';
import path from 'path';
import fs from 'fs';

const router = express.Router();

// Debug route to check if the API is working
router.get('/debug', (req, res) => {
  console.log('Documents API debug route hit');
  return res.json({
    success: true,
    message: 'Documents API is working',
    timestamp: new Date().toISOString()
  });
});

// Secure document access endpoint
router.post('/secure-access', (req, res) => {
  try {
    const { filePath, forceInline = true } = req.body;
    
    if (!filePath) {
      console.log('No file path provided in request');
      return res.status(400).json({
        success: false,
        message: 'File path is required'
      });
    }
    
    console.log(`Secure access request for: ${filePath}`);
    
    // For security, don't allow paths with ".." to prevent directory traversal
    if (filePath.includes('..')) {
      console.log(`Rejected suspicious path: ${filePath}`);
      return res.status(400).json({
        success: false,
        message: 'Invalid file path'
      });
    }
    
    // Use the new preview route to ensure inline viewing
    let secureUrl;
    const host = `${req.protocol}://${req.get('host')}`;
    
    // Encode the file path to make it URL-safe
    const encodedPath = encodeURIComponent(filePath.replace(/^\/+/, '')); // Remove leading slashes
    
    // Check if it's a PDF to handle special case
    const isPdf = filePath.toLowerCase().endsWith('.pdf');
    
    // Add forceInline parameter to ensure documents open in browser
    // For PDFs, also add disposition=inline to prevent download
    if (isPdf) {
      secureUrl = `${host}/preview/${encodedPath}${forceInline ? '?view=inline&disposition=inline' : ''}`;
    } else {
      secureUrl = `${host}/preview/${encodedPath}${forceInline ? '?view=inline' : ''}`;
    }
    
    console.log(`Generated secure URL with preview route: ${secureUrl}`);
    
    return res.json({
      success: true,
      secureUrl,
      expiresIn: 3600, // Token valid for 1 hour
      forceInline: true, // Flag indicating this should be displayed inline
      isPdf: isPdf  // Flag indicating if this is a PDF
    });
  } catch (error) {
    console.error('Error generating secure document URL:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to generate secure access URL'
    });
  }
});

export default router; 