import { MigrationInterface, QueryRunner } from "typeorm";

export class AddEmployeeSkillsTable1746000000017 implements MigrationInterface {
    name = 'AddEmployeeSkillsTable1746000000017';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create the employee_skills table
        await queryRunner.query(`
            CREATE TABLE \`employee_skills\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`professionalSkills\` text NULL,
                \`technicalSkills\` text NULL,
                \`certifications\` text NULL,
                \`languages\` text NULL,
                \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                \`employeeId\` int NULL,
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB
        `);

        // Add foreign key constraint
        await queryRunner.query(`
            ALTER TABLE \`employee_skills\` 
            ADD CONSTRAINT \`FK_employee_skills_employee\` 
            FOREIGN KEY (\`employeeId\`) REFERENCES \`employees\`(\`id\`) 
            ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the foreign key constraint
        await queryRunner.query(`
            ALTER TABLE \`employee_skills\` 
            DROP FOREIGN KEY \`FK_employee_skills_employee\`
        `);
        
        // Drop the table
        await queryRunner.query(`DROP TABLE \`employee_skills\``);
    }
} 