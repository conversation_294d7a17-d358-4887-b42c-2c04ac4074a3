import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { IsNotEmpty, Length, IsEnum } from 'class-validator';

export enum TemplateCategory {
  PREDEFINED = 'predefined',
  CUSTOM = 'custom'
}

@Entity('role_templates')
export class RoleTemplate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, unique: true })
  @IsNotEmpty({ message: 'Template name is required' })
  @Length(2, 100, { message: 'Template name must be between 2 and 100 characters' })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: TemplateCategory,
    default: TemplateCategory.CUSTOM
  })
  @IsEnum(TemplateCategory, { message: 'Invalid template category' })
  category: TemplateCategory;

  @Column('simple-array')
  permissions: string[];

  @Column({ type: 'varchar', length: 50, nullable: true })
  iconName: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  iconColor: string;

  @Column({ type: 'varchar', nullable: true })
  createdBy: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;
} 