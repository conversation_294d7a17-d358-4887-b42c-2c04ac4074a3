import { AppDataSource } from '../config/database';
import { Role } from '../entities/Role';
import { UserRoleAssignment } from '../entities/UserRoleAssignment';
import { User } from '../entities/User';

const testRoleDatabase = async () => {
  console.log("Testing role database functionality...");

  try {
    // Initialize the data source
    await AppDataSource.initialize();
    console.log("✅ Database connection established");

    // Get repositories
    const roleRepository = AppDataSource.getRepository(Role);
    const userRoleRepository = AppDataSource.getRepository(UserRoleAssignment);
    const userRepository = AppDataSource.getRepository(User);

    // Test 1: Check if roles exist
    console.log("\n1. Testing roles table...");
    const roles = await roleRepository.find();
    console.log(`Found ${roles.length} roles:`);
    roles.forEach(role => {
      console.log(`  - ${role.name} (ID: ${role.id})`);
    });

    // Test 2: Check if we can get a specific role
    console.log("\n2. Testing specific role lookup...");
    const itAdminRole = await roleRepository.findOne({ where: { name: 'IT Administrator' } });
    if (itAdminRole) {
      console.log(`✅ Found IT Administrator role with ID: ${itAdminRole.id}`);
      console.log(`   Permissions: ${itAdminRole.permissions.length} permissions`);
    } else {
      console.log("❌ IT Administrator role not found");
    }

    // Test 3: Check if we can get users
    console.log("\n3. Testing users table...");
    const users = await userRepository.find();
    console.log(`Found ${users.length} users`);
    if (users.length > 0) {
      const firstUser = users[0];
      console.log(`  First user: ${firstUser.name} (${firstUser.email})`);
      
      // Test 4: Check role assignments for this user
      console.log("\n4. Testing role assignments...");
      const roleAssignments = await userRoleRepository.find({
        where: { userId: firstUser.id },
        relations: ['role']
      });
      
      console.log(`Found ${roleAssignments.length} role assignments for ${firstUser.name}`);
      roleAssignments.forEach(assignment => {
        console.log(`  - Role: ${assignment.role?.name || 'Unknown'} (Role ID: ${assignment.roleId})`);
        console.log(`    Assigned by: ${assignment.assignedBy}`);
        console.log(`    Project: ${assignment.projectId}`);
      });

      // Test 5: Try to create a new role assignment
      if (itAdminRole && roleAssignments.length === 0) {
        console.log("\n5. Testing role assignment creation...");
        try {
          const newAssignment = userRoleRepository.create({
            userId: firstUser.id,
            roleId: itAdminRole.id,
            assignedBy: 'system',
            projectId: 'test-project',
            notes: 'Test assignment from database test script'
          });

          const savedAssignment = await userRoleRepository.save(newAssignment);
          console.log(`✅ Successfully created role assignment with ID: ${savedAssignment.id}`);
          
          // Verify the assignment was saved
          const verifyAssignment = await userRoleRepository.findOne({
            where: { id: savedAssignment.id },
            relations: ['role', 'user']
          });
          
          if (verifyAssignment) {
            console.log(`✅ Verified assignment: ${verifyAssignment.user?.name} → ${verifyAssignment.role?.name}`);
            
            // Clean up - remove the test assignment
            await userRoleRepository.remove(verifyAssignment);
            console.log("✅ Cleaned up test assignment");
          }
        } catch (error) {
          console.error("❌ Failed to create role assignment:", error);
        }
      }
    } else {
      console.log("⚠️ No users found in database");
    }

    console.log("\n✅ All database tests completed successfully!");
    
  } catch (error) {
    console.error("❌ Database test failed:", error);
  } finally {
    // Close the connection
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log("Database connection closed");
    }
    
    process.exit(0);
  }
};

testRoleDatabase(); 