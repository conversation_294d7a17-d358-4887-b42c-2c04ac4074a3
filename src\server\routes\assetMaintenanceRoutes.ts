import express from 'express';
import { assetMaintenanceController } from '../controllers/assetMaintenanceController';
import { authMiddleware } from '../middleware/authMiddleware';

const router = express.Router();

// Get all maintenance records with filtering and pagination
router.get('/', authMiddleware.verify, assetMaintenanceController.getMaintenanceRecords);

// Get maintenance statistics
router.get('/stats', authMiddleware.verify, assetMaintenanceController.getMaintenanceStats);

// Get a single maintenance record by ID
router.get('/:id', authMiddleware.verify, assetMaintenanceController.getMaintenanceById);

// Get maintenance records for a specific asset
router.get('/asset/:assetId', authMiddleware.verify, assetMaintenanceController.getMaintenanceByAssetId);

// Create a new maintenance record
router.post('/', 
  authMiddleware.verify, 
  authMiddleware.checkRoles(['IT_ADMIN', 'IT_STAFF'] as any),
  assetMaintenanceController.createMaintenance
);

// Update an existing maintenance record
router.put('/:id', 
  authMiddleware.verify, 
  authMiddleware.checkRoles(['IT_ADMIN', 'IT_STAFF'] as any),
  assetMaintenanceController.updateMaintenance
);

// Delete a maintenance record
router.delete('/:id', 
  authMiddleware.verify, 
  authMiddleware.checkRoles(['IT_ADMIN'] as any),
  assetMaintenanceController.deleteMaintenance
);

export default router; 