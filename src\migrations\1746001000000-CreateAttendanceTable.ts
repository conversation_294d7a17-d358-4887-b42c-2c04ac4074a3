import { MigrationInterface, QueryRunner, Table, TableForeignKey } from "typeorm";

export class CreateAttendanceTable1746001000000 implements MigrationInterface {
    name = 'CreateAttendanceTable1746001000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create attendances table
        await queryRunner.createTable(
            new Table({
                name: "attendances",
                columns: [
                    {
                        name: "id",
                        type: "int",
                        isPrimary: true,
                        isGenerated: true,
                        generationStrategy: "increment"
                    },
                    {
                        name: "employeeId",
                        type: "int",
                    },
                    {
                        name: "employeeName",
                        type: "varchar",
                        isNullable: true
                    },
                    {
                        name: "date",
                        type: "varchar",
                    },
                    {
                        name: "checkInTime",
                        type: "varchar",
                        isNullable: true
                    },
                    {
                        name: "checkOutTime",
                        type: "varchar",
                        isNullable: true
                    },
                    {
                        name: "status",
                        type: "varchar",
                        default: "'present'"
                    },
                    {
                        name: "notes",
                        type: "varchar",
                        isNullable: true
                    },
                    {
                        name: "location",
                        type: "varchar",
                        isNullable: true
                    },
                    {
                        name: "coordinates",
                        type: "json",
                        isNullable: true
                    },
                    {
                        name: "ipAddress",
                        type: "varchar",
                        isNullable: true
                    },
                    {
                        name: "deviceInfo",
                        type: "varchar",
                        isNullable: true
                    },
                    {
                        name: "workHours",
                        type: "float",
                        isNullable: true
                    },
                    {
                        name: "overtime",
                        type: "float",
                        isNullable: true
                    },
                    {
                        name: "isRemote",
                        type: "boolean",
                        default: false
                    },
                    {
                        name: "isRegularized",
                        type: "boolean",
                        default: false
                    },
                    {
                        name: "regularizedBy",
                        type: "int",
                        isNullable: true
                    },
                    {
                        name: "regularizationReason",
                        type: "varchar",
                        isNullable: true
                    },
                    {
                        name: "regularizationDate",
                        type: "varchar",
                        isNullable: true
                    },
                    {
                        name: "approvalStatus",
                        type: "varchar",
                        isNullable: true,
                        default: "'pending'"
                    },
                    {
                        name: "shiftId",
                        type: "int",
                        isNullable: true
                    },
                    {
                        name: "shiftName",
                        type: "varchar",
                        isNullable: true
                    },
                    {
                        name: "shiftTiming",
                        type: "json",
                        isNullable: true
                    },
                    {
                        name: "department",
                        type: "varchar",
                        isNullable: true
                    },
                    {
                        name: "position",
                        type: "varchar",
                        isNullable: true
                    },
                    {
                        name: "shift",
                        type: "int",
                        default: 0
                    },
                    {
                        name: "breakTime",
                        type: "float",
                        isNullable: true
                    },
                    {
                        name: "isImported",
                        type: "boolean",
                        default: false
                    },
                    {
                        name: "createdAt",
                        type: "timestamp",
                        default: "now()"
                    },
                    {
                        name: "updatedAt",
                        type: "timestamp",
                        default: "now()"
                    }
                ]
            }),
            true
        );

        // Add check constraints for status and approvalStatus
        await queryRunner.query(
            `ALTER TABLE attendances ADD CONSTRAINT attendance_status_check CHECK (status IN ('present', 'absent', 'late', 'half_day', 'leave', 'holiday', 'weekend'))`
        );
        
        await queryRunner.query(
            `ALTER TABLE attendances ADD CONSTRAINT approval_status_check CHECK (approvalStatus IN ('pending', 'approved', 'rejected') OR approvalStatus IS NULL)`
        );

        // Create foreign key to employees table
        await queryRunner.createForeignKey(
            "attendances",
            new TableForeignKey({
                columnNames: ["employeeId"],
                referencedColumnNames: ["id"],
                referencedTableName: "employees",
                onDelete: "CASCADE"
            })
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign key
        const table = await queryRunner.getTable("attendances");
        if (table) {
            const foreignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf("employeeId") !== -1);
            if (foreignKey) {
                await queryRunner.dropForeignKey("attendances", foreignKey);
            }
        }

        // Drop check constraints
        await queryRunner.query(`ALTER TABLE attendances DROP CONSTRAINT IF EXISTS attendance_status_check`);
        await queryRunner.query(`ALTER TABLE attendances DROP CONSTRAINT IF EXISTS approval_status_check`);

        // Drop attendances table
        await queryRunner.dropTable("attendances");
    }
} 