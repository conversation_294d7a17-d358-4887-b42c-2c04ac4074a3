interface HolidayTemplate {
  name: string;
  type: 'public' | 'company' | 'optional';
  description: string;
  metadata: {
    religion?: string;
    culturalSignificance?: string;
    observanceLevel?: 'major' | 'minor' | 'regional';
    alternativeNames?: string[];
    lunarCalendar?: boolean;
    variableDate?: boolean;
    duration?: number;
    fastingRequired?: boolean;
    prayerTimes?: string[];
    traditionalFoods?: string[];
    customaryActivities?: string[];
  };
}

interface IslamicHolidays {
  [key: string]: HolidayTemplate;
}

interface HinduHolidays {
  [key: string]: HolidayTemplate;
}

interface ChristianHolidays {
  [key: string]: HolidayTemplate;
}

interface JewishHolidays {
  [key: string]: HolidayTemplate;
}

interface BuddhistHolidays {
  [key: string]: HolidayTemplate;
}

interface SikhHolidays {
  [key: string]: HolidayTemplate;
}

export class HolidayTemplateService {
  
  // Islamic Holiday Templates
  static readonly islamicHolidays: IslamicHolidays = {
    eidAlFitr: {
      name: 'Eid al-<PERSON>tr',
      type: 'optional',
      description: 'Festival of Breaking the Fast, marking the end of Ramadan',
      metadata: {
        religion: 'Islam',
        culturalSignificance: 'Celebrates the completion of the holy month of Ramadan and the return to normal life after a month of fasting',
        observanceLevel: 'major',
        alternativeNames: ['Eid ul-Fitr', 'Feast of Breaking the Fast', 'Lesser Eid', 'Sweet Eid'],
        lunarCalendar: true,
        variableDate: true,
        duration: 3,
        fastingRequired: false,
        prayerTimes: ['Special Eid prayer in the morning'],
        traditionalFoods: ['Dates', 'Sweet dishes', 'Biryani', 'Kebabs', 'Traditional sweets'],
        customaryActivities: ['Special prayers', 'Gift giving', 'Charity (Zakat al-Fitr)', 'Family gatherings', 'New clothes', 'Visiting relatives']
      }
    },
    eidAlAdha: {
      name: 'Eid al-Adha',
      type: 'optional',
      description: 'Festival of Sacrifice, commemorating Abraham\'s willingness to sacrifice his son as an act of obedience to God',
      metadata: {
        religion: 'Islam',
        culturalSignificance: 'Honors the willingness of Ibrahim (Abraham) to sacrifice his son Ismail as an act of obedience to Allah',
        observanceLevel: 'major',
        alternativeNames: ['Eid ul-Adha', 'Festival of Sacrifice', 'Greater Eid', 'Bakr Eid'],
        lunarCalendar: true,
        variableDate: true,
        duration: 4,
        fastingRequired: false,
        prayerTimes: ['Special Eid prayer in the morning'],
        traditionalFoods: ['Sacrificial meat dishes', 'Mutton curry', 'Beef preparations', 'Traditional sweets'],
        customaryActivities: ['Animal sacrifice (Qurbani)', 'Special prayers', 'Charity to poor', 'Family feasts', 'Visiting relatives']
      }
    },
    islamicNewYear: {
      name: 'Islamic New Year',
      type: 'optional',
      description: 'First day of Muharram, marking the beginning of the Islamic lunar year',
      metadata: {
        religion: 'Islam',
        culturalSignificance: 'Marks the beginning of the Islamic calendar and commemorates the Hijra (migration) of Prophet Muhammad',
        observanceLevel: 'major',
        alternativeNames: ['Muharram', 'Hijri New Year', 'Arabic New Year'],
        lunarCalendar: true,
        variableDate: true,
        duration: 1,
        fastingRequired: false,
        customaryActivities: ['Reflection', 'Prayer', 'Reading Quran', 'Historical remembrance']
      }
    },
    dayOfAshura: {
      name: 'Day of Ashura',
      type: 'optional',
      description: 'Day of remembrance observed on the 10th day of Muharram',
      metadata: {
        religion: 'Islam',
        culturalSignificance: 'Commemorates various historical events including the martyrdom of Imam Hussein',
        observanceLevel: 'major',
        alternativeNames: ['Ashura', 'Tenth of Muharram'],
        lunarCalendar: true,
        variableDate: true,
        duration: 1,
        fastingRequired: true,
        customaryActivities: ['Fasting', 'Prayer', 'Charity', 'Remembrance ceremonies']
      }
    },
    mawlidAnNabi: {
      name: 'Mawlid an-Nabi',
      type: 'optional',
      description: 'Celebration of the birthday of Prophet Muhammad',
      metadata: {
        religion: 'Islam',
        culturalSignificance: 'Celebrates the birth of Prophet Muhammad and reflects on his teachings',
        observanceLevel: 'major',
        alternativeNames: ['Mawlid', 'Prophet\'s Birthday', 'Milad un-Nabi'],
        lunarCalendar: true,
        variableDate: true,
        duration: 1,
        customaryActivities: ['Religious gatherings', 'Recitation of poetry', 'Charity', 'Community meals']
      }
    }
  };

  // Hindu Holiday Templates
  static readonly hinduHolidays: HinduHolidays = {
    diwali: {
      name: 'Diwali',
      type: 'optional',
      description: 'Festival of Lights, celebrating the victory of light over darkness',
      metadata: {
        religion: 'Hinduism',
        culturalSignificance: 'Celebrates the return of Lord Rama to Ayodhya and the victory of good over evil',
        observanceLevel: 'major',
        alternativeNames: ['Deepavali', 'Festival of Lights', 'Deepawali'],
        lunarCalendar: true,
        variableDate: true,
        duration: 5,
        traditionalFoods: ['Sweets', 'Rangoli', 'Mithai', 'Dry fruits', 'Traditional Indian dishes'],
        customaryActivities: ['Lighting diyas and candles', 'Fireworks', 'Rangoli decoration', 'Lakshmi Puja', 'Gift exchange', 'House cleaning']
      }
    },
    holi: {
      name: 'Holi',
      type: 'optional',
      description: 'Festival of Colors, celebrating the arrival of spring',
      metadata: {
        religion: 'Hinduism',
        culturalSignificance: 'Celebrates the eternal love of Radha and Krishna and the triumph of good over evil',
        observanceLevel: 'major',
        alternativeNames: ['Festival of Colors', 'Festival of Love', 'Phagwah'],
        duration: 2,
        traditionalFoods: ['Gujiya', 'Mathri', 'Malpua', 'Bhang', 'Traditional sweets'],
        customaryActivities: ['Playing with colors', 'Water fights', 'Music and dance', 'Bonfires', 'Community celebrations']
      }
    },
    dussehra: {
      name: 'Dussehra',
      type: 'optional',
      description: 'Festival celebrating the victory of good over evil',
      metadata: {
        religion: 'Hinduism',
        culturalSignificance: 'Commemorates Lord Rama\'s victory over the demon king Ravana',
        observanceLevel: 'major',
        alternativeNames: ['Vijayadashami', 'Dasara'],
        duration: 1,
        customaryActivities: ['Ram Lila performances', 'Burning of Ravana effigies', 'Processions', 'Prayers']
      }
    }
  };

  // Christian Holiday Templates
  static readonly christianHolidays: ChristianHolidays = {
    goodFriday: {
      name: 'Good Friday',
      type: 'optional',
      description: 'Commemoration of the crucifixion of Jesus Christ',
      metadata: {
        religion: 'Christianity',
        culturalSignificance: 'Commemorates the crucifixion and death of Jesus Christ',
        observanceLevel: 'major',
        alternativeNames: ['Holy Friday', 'Great Friday', 'Black Friday'],
        duration: 1,
        fastingRequired: true,
        customaryActivities: ['Church services', 'Prayer', 'Fasting', 'Stations of the Cross', 'Reflection']
      }
    },
    easter: {
      name: 'Easter Sunday',
      type: 'optional',
      description: 'Celebration of the resurrection of Jesus Christ',
      metadata: {
        religion: 'Christianity',
        culturalSignificance: 'Celebrates the resurrection of Jesus Christ from the dead',
        observanceLevel: 'major',
        alternativeNames: ['Resurrection Sunday', 'Pascha'],
        variableDate: true,
        duration: 1,
        traditionalFoods: ['Easter eggs', 'Hot cross buns', 'Lamb', 'Easter bread'],
        customaryActivities: ['Church services', 'Easter egg hunts', 'Family gatherings', 'Special meals']
      }
    }
  };

  // Jewish Holiday Templates
  static readonly jewishHolidays: JewishHolidays = {
    roshHashanah: {
      name: 'Rosh Hashanah',
      type: 'optional',
      description: 'Jewish New Year, marking the beginning of the High Holy Days',
      metadata: {
        religion: 'Judaism',
        culturalSignificance: 'Marks the beginning of the Jewish New Year and a period of introspection',
        observanceLevel: 'major',
        alternativeNames: ['Jewish New Year', 'Day of Judgment'],
        lunarCalendar: true,
        variableDate: true,
        duration: 2,
        traditionalFoods: ['Apples with honey', 'Challah bread', 'Pomegranates', 'Fish head'],
        customaryActivities: ['Synagogue services', 'Shofar blowing', 'Tashlich ceremony', 'Family meals', 'Reflection and prayer']
      }
    },
    yomKippur: {
      name: 'Yom Kippur',
      type: 'optional',
      description: 'Day of Atonement, the holiest day in Judaism',
      metadata: {
        religion: 'Judaism',
        culturalSignificance: 'Day of atonement, repentance, and forgiveness',
        observanceLevel: 'major',
        alternativeNames: ['Day of Atonement'],
        lunarCalendar: true,
        variableDate: true,
        duration: 1,
        fastingRequired: true,
        customaryActivities: ['25-hour fast', 'Synagogue services', 'Prayer', 'Reflection', 'Charity']
      }
    }
  };

  // Buddhist Holiday Templates
  static readonly buddhistHolidays: BuddhistHolidays = {
    buddhaPurnima: {
      name: 'Buddha Purnima',
      type: 'optional',
      description: 'Celebration of the birth, enlightenment, and death of Gautama Buddha',
      metadata: {
        religion: 'Buddhism',
        culturalSignificance: 'Commemorates the birth, enlightenment, and parinirvana of Buddha',
        observanceLevel: 'major',
        alternativeNames: ['Vesak', 'Buddha Day', 'Buddha Jayanti'],
        lunarCalendar: true,
        variableDate: true,
        duration: 1,
        customaryActivities: ['Meditation', 'Chanting', 'Offering flowers', 'Acts of kindness', 'Temple visits']
      }
    }
  };

  // Sikh Holiday Templates
  static readonly sikhHolidays: SikhHolidays = {
    guruNanakJayanti: {
      name: 'Guru Nanak Jayanti',
      type: 'optional',
      description: 'Birthday celebration of Guru Nanak, the founder of Sikhism',
      metadata: {
        religion: 'Sikhism',
        culturalSignificance: 'Celebrates the birth of Guru Nanak Dev Ji, the first Sikh Guru and founder of Sikhism',
        observanceLevel: 'major',
        alternativeNames: ['Guru Nanak Gurpurab', 'Prakash Utsav'],
        lunarCalendar: true,
        variableDate: true,
        duration: 1,
        traditionalFoods: ['Langar (community kitchen food)', 'Karah Prasad', 'Traditional Punjabi cuisine'],
        customaryActivities: ['Gurdwara visits', 'Kirtan (devotional singing)', 'Langar service', 'Processions', 'Community service']
      }
    },
    baisakhi: {
      name: 'Baisakhi',
      type: 'optional',
      description: 'Sikh New Year and harvest festival',
      metadata: {
        religion: 'Sikhism',
        culturalSignificance: 'Commemorates the formation of Khalsa and marks the Sikh New Year',
        observanceLevel: 'major',
        alternativeNames: ['Vaisakhi', 'Khalsa Day'],
        duration: 1,
        traditionalFoods: ['Traditional Punjabi dishes', 'Lassi', 'Makki di roti', 'Sarson da saag'],
        customaryActivities: ['Gurdwara visits', 'Bhangra dancing', 'Kirtan', 'Community celebrations', 'Harvest celebrations']
      }
    }
  };

  // Get all holiday templates by religion
  static getHolidaysByReligion(religion: string): HolidayTemplate[] {
    switch (religion.toLowerCase()) {
      case 'islam':
        return Object.values(this.islamicHolidays);
      case 'hinduism':
        return Object.values(this.hinduHolidays);
      case 'christianity':
        return Object.values(this.christianHolidays);
      case 'judaism':
        return Object.values(this.jewishHolidays);
      case 'buddhism':
        return Object.values(this.buddhistHolidays);
      case 'sikhism':
        return Object.values(this.sikhHolidays);
      default:
        return [];
    }
  }

  // Get all available religions
  static getAvailableReligions(): string[] {
    return ['Islam', 'Hinduism', 'Christianity', 'Judaism', 'Buddhism', 'Sikhism'];
  }

  // Generate holiday with dates for a specific year
  static generateHolidayForYear(template: HolidayTemplate, year: number, baseDate?: string): any {
    return {
      name: template.name,
      date: baseDate || `${year}-01-01`, // This would need lunar calendar calculation for accurate dates
      type: template.type,
      description: template.description,
      metadata: template.metadata
    };
  }

  // Create Eid holidays for a specific year (requires lunar calendar calculation)
  static createEidHolidays(year: number, eidAlFitrDate: string, eidAlAdhaDate: string): any[] {
    const holidays = [];
    
    // Eid al-Fitr (3 days)
    const fitrStart = new Date(eidAlFitrDate);
    for (let i = 0; i < 3; i++) {
      const date = new Date(fitrStart);
      date.setDate(date.getDate() + i);
      holidays.push({
        name: `Eid al-Fitr Day ${i + 1}`,
        date: date.toISOString().split('T')[0],
        type: 'optional',
        description: i === 0 ? 'End of Ramadan, Festival of Breaking the Fast' : `${i + 1}${i === 1 ? 'nd' : 'rd'} day of Eid al-Fitr celebration`,
        metadata: {
          ...this.islamicHolidays.eidAlFitr.metadata,
          dayOfCelebration: i + 1
        }
      });
    }

    // Eid al-Adha (4 days)
    const adhaStart = new Date(eidAlAdhaDate);
    for (let i = 0; i < 4; i++) {
      const date = new Date(adhaStart);
      date.setDate(date.getDate() + i);
      holidays.push({
        name: `Eid al-Adha Day ${i + 1}`,
        date: date.toISOString().split('T')[0],
        type: 'optional',
        description: i === 0 ? 'Festival of Sacrifice, commemorating Abraham\'s willingness to sacrifice his son' : `${i + 1}${this.getOrdinalSuffix(i + 1)} day of Eid al-Adha celebration`,
        metadata: {
          ...this.islamicHolidays.eidAlAdha.metadata,
          dayOfCelebration: i + 1
        }
      });
    }

    return holidays;
  }

  private static getOrdinalSuffix(num: number): string {
    const j = num % 10;
    const k = num % 100;
    if (j === 1 && k !== 11) return 'st';
    if (j === 2 && k !== 12) return 'nd';
    if (j === 3 && k !== 13) return 'rd';
    return 'th';
  }

  // Get upcoming Islamic holidays with approximate dates (would need proper lunar calendar integration)
  static getUpcomingIslamicHolidays(year: number): any[] {
    // These dates are approximate and should be calculated using proper lunar calendar
    const islamicCalendar = {
      2024: {
        eidAlFitr: '2024-04-10',
        eidAlAdha: '2024-06-17',
        islamicNewYear: '2024-07-07',
        dayOfAshura: '2024-07-16',
        mawlidAnNabi: '2024-09-16'
      },
      2025: {
        eidAlFitr: '2025-03-31',
        eidAlAdha: '2025-06-07',
        islamicNewYear: '2025-06-26',
        dayOfAshura: '2025-07-05',
        mawlidAnNabi: '2025-09-05'
      }
    };

    const yearData = islamicCalendar[year as keyof typeof islamicCalendar];
    if (!yearData) return [];

    return [
      ...this.createEidHolidays(year, yearData.eidAlFitr, yearData.eidAlAdha),
      {
        name: 'Islamic New Year',
        date: yearData.islamicNewYear,
        type: 'optional',
        description: 'First day of Muharram, Islamic New Year',
        metadata: this.islamicHolidays.islamicNewYear.metadata
      },
      {
        name: 'Day of Ashura',
        date: yearData.dayOfAshura,
        type: 'optional',
        description: 'Day of remembrance in Islam, 10th day of Muharram',
        metadata: this.islamicHolidays.dayOfAshura.metadata
      },
      {
        name: 'Mawlid an-Nabi',
        date: yearData.mawlidAnNabi,
        type: 'optional',
        description: 'Birthday of Prophet Muhammad',
        metadata: this.islamicHolidays.mawlidAnNabi.metadata
      }
    ];
  }

  private static getHolidaysForReligion(religion: string): HolidayTemplate[] {
    const basicTemplates: Record<string, HolidayTemplate[]> = {
      'Islam': [
        {
          name: 'Eid al-Fitr',
          type: 'optional',
          description: 'Islamic festival celebrating the end of Ramadan',
          metadata: {
            religion: 'Islam',
            observanceLevel: 'major',
            duration: 3,
            culturalSignificance: 'Major Islamic holiday - end of fasting month'
          }
        },
        {
          name: 'Eid al-Adha',
          type: 'optional',
          description: 'Islamic festival of sacrifice',
          metadata: {
            religion: 'Islam',
            observanceLevel: 'major',
            duration: 4,
            culturalSignificance: 'Major Islamic holiday - commemoration of Abraham\'s sacrifice'
          }
        }
      ],
      'Hinduism': [
        {
          name: 'Diwali',
          type: 'optional',
          description: 'Hindu festival of lights',
          metadata: {
            religion: 'Hinduism',
            observanceLevel: 'major',
            duration: 5,
            culturalSignificance: 'Festival of lights celebrating victory of light over darkness'
          }
        },
        {
          name: 'Holi',
          type: 'optional',
          description: 'Hindu festival of colors',
          metadata: {
            religion: 'Hinduism',
            observanceLevel: 'major',
            duration: 2,
            culturalSignificance: 'Festival of colors celebrating the arrival of spring'
          }
        }
      ],
      'Christianity': [
        {
          name: 'Christmas',
          type: 'public',
          description: 'Christian holiday celebrating the birth of Jesus Christ',
          metadata: {
            religion: 'Christianity',
            observanceLevel: 'major',
            duration: 1,
            culturalSignificance: 'Celebration of the birth of Jesus Christ'
          }
        }
      ]
    };
    
    return basicTemplates[religion] || [];
  }
}

// Add smart holiday generation functions
export class HolidaySmartFeatures {
  
  // Generate recurring holidays for the next year based on lunar calendar
  static generateRecurringHolidays(year: number): HolidayTemplate[] {
    const recurringHolidays: HolidayTemplate[] = [];
    
    // Islamic holidays (approximate - would need proper lunar calendar calculation)
    const islamicHolidays = [
      {
        name: `Eid al-Fitr ${year}`,
        baseDate: this.calculateEidAlFitr(year),
        type: 'optional' as const,
        description: `Islamic festival celebrating the end of Ramadan ${year}`,
        metadata: {
          religion: 'Islam',
          observanceLevel: 'major' as const,
          duration: 3,
          culturalSignificance: 'Major Islamic holiday - end of fasting month',
          lunarCalendar: true,
          variableDate: true
        }
      },
      {
        name: `Eid al-Adha ${year}`,
        baseDate: this.calculateEidAlAdha(year),
        type: 'optional' as const,
        description: `Islamic festival of sacrifice ${year}`,
        metadata: {
          religion: 'Islam',
          observanceLevel: 'major' as const,
          duration: 4,
          culturalSignificance: 'Major Islamic holiday - commemoration of Abraham\'s sacrifice',
          lunarCalendar: true,
          variableDate: true
        }
      }
    ];
    
    // Hindu holidays (approximate dates)
    const hinduHolidays = [
      {
        name: `Diwali ${year}`,
        baseDate: this.calculateDiwali(year),
        type: 'optional' as const,
        description: `Hindu festival of lights ${year}`,
        metadata: {
          religion: 'Hinduism',
          observanceLevel: 'major' as const,
          duration: 5,
          culturalSignificance: 'Festival of lights celebrating victory of light over darkness',
          lunarCalendar: true,
          variableDate: true
        }
      }
    ];
    
    return [...islamicHolidays, ...hinduHolidays];
  }
  
  // Generate holiday recommendations based on employee demographics
  static generateHolidayRecommendations(employeeReligions: string[]): HolidayTemplate[] {
    const recommendations: HolidayTemplate[] = [];
    
    // Count religion demographics
    const religionCounts = employeeReligions.reduce((acc, religion) => {
      acc[religion] = (acc[religion] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    // Generate recommendations based on employee demographics
    Object.entries(religionCounts).forEach(([religion, count]) => {
      const percentage = (count / employeeReligions.length) * 100;
      
      if (percentage >= 10) { // If at least 10% of employees follow this religion
        const religionHolidays = this.getHolidaysForReligion(religion);
        recommendations.push(...religionHolidays);
      }
    });
    
    return recommendations;
  }
  
  // Helper methods for calculating variable religious holidays
  private static calculateEidAlFitr(year: number): string {
    // Simplified calculation - in reality would use proper Islamic calendar
    const approxDates: Record<number, string> = {
      2024: '2024-04-10',
      2025: '2025-03-31',
      2026: '2026-03-20'
    };
    return approxDates[year] || `${year}-04-10`;
  }
  
  private static calculateEidAlAdha(year: number): string {
    // Simplified calculation - in reality would use proper Islamic calendar
    const approxDates: Record<number, string> = {
      2024: '2024-06-16',
      2025: '2025-06-06',
      2026: '2026-05-26'
    };
    return approxDates[year] || `${year}-06-16`;
  }
  
  private static calculateDiwali(year: number): string {
    // Simplified calculation - in reality would use proper Hindu calendar
    const approxDates: Record<number, string> = {
      2024: '2024-11-01',
      2025: '2025-10-20',
      2026: '2026-11-08'
    };
    return approxDates[year] || `${year}-11-01`;
  }
  
  // Generate holiday conflict analysis
  static analyzeHolidayConflicts(holidays: Holiday[], workDays: number[]): {
    conflicts: string[];
    recommendations: string[];
  } {
    const conflicts: string[] = [];
    const recommendations: string[] = [];
    
    holidays.forEach(holiday => {
      const holidayDate = new Date(holiday.date);
      const dayOfWeek = holidayDate.getDay();
      
      // Check if holiday falls on weekend
      if (!workDays.includes(dayOfWeek)) {
        conflicts.push(`${holiday.name} falls on a weekend - consider moving observance to weekday`);
      }
      
      // Check for clustering of holidays
      const nearbyHolidays = holidays.filter(h => {
        const diff = Math.abs(new Date(h.date).getTime() - holidayDate.getTime());
        return diff <= 7 * 24 * 60 * 60 * 1000 && h.id !== holiday.id; // Within 7 days
      });
      
      if (nearbyHolidays.length > 0) {
        conflicts.push(`${holiday.name} is close to ${nearbyHolidays.map(h => h.name).join(', ')} - consider spacing`);
      }
    });
    
    // Generate recommendations
    if (conflicts.length === 0) {
      recommendations.push('Holiday schedule looks well-balanced');
    } else {
      recommendations.push('Consider reviewing holiday distribution for better work-life balance');
    }
    
    return { conflicts, recommendations };
  }
} 