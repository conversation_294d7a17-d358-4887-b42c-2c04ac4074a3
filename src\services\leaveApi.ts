import { 
  LeaveRequest, 
  LeaveBalance, 
  LeaveStatus
} from '../types/attendance';

interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Dynamic Leave Types Management
export interface CustomLeaveType {
  id: string;
  name: string;
  code: string;
  description?: string;
  color: string;
  maxDaysPerYear: number;
  carryForwardAllowed: boolean;
  carryForwardLimit?: number;
  requiresApproval: boolean;
  allowHalfDay: boolean;
  restrictedDays?: string[];
  minimumNotice: number;
  isActive: boolean;
  applicableDepartments?: string[];
  applicableRoles?: string[];
  createdAt: string;
  updatedAt: string;
}

// Dynamic Leave Types Management - fully database-driven

// Leave Requests API - Unified service for consistent behavior across HR and Employee interfaces
export const leaveRequestsApi = {
  getAll: async (): Promise<PaginatedResponse<LeaveRequest>> => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('/api/leave-requests?limit=1000&page=1', {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        }
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching leave requests:', error);
      return {
        success: true,
        data: [],
        pagination: { page: 1, limit: 10, total: 0, totalPages: 0 }
      };
    }
  },

  // Get all leave requests without pagination for HR admin
  getAllForHR: async (): Promise<ApiResponse<LeaveRequest[]>> => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('/api/leave-requests?limit=1000&page=1', {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        }
      });
      const data = await response.json();
      return {
        success: data.success,
        data: data.data || [],
        message: data.message
      };
    } catch (error) {
      console.error('Error fetching all leave requests for HR:', error);
      return {
        success: false,
        data: [],
        message: 'Failed to fetch leave requests'
      };
    }
  },

  getById: async (id: string): Promise<ApiResponse<LeaveRequest>> => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/leave-requests/${id}`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        }
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching leave request:', error);
      return { 
        success: false, 
        data: {} as LeaveRequest,
        message: 'Failed to fetch request'
      };
    }
  },

  create: async (leaveRequest: Omit<LeaveRequest, 'id'>): Promise<ApiResponse<LeaveRequest>> => {
    try {
      console.log('🌐 leaveApi.create called with:', leaveRequest);
      
      const token = localStorage.getItem('authToken');
      console.log('🔑 Auth token:', token ? 'Present' : 'Missing');
      
      const response = await fetch('/api/leave-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify(leaveRequest)
      });
      
      console.log('📡 API response status:', response.status);
      
      const data = await response.json();
      console.log('📥 API response data:', data);
      
      return data;
    } catch (error) {
      console.error('❌ Error creating leave request:', error);
      return { 
        success: false, 
        data: {} as LeaveRequest,
        message: 'Failed to create request'
      };
    }
  },

  update: async (id: string, updates: Partial<LeaveRequest>): Promise<ApiResponse<LeaveRequest>> => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/leave-requests/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify(updates)
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error updating leave request:', error);
      return { 
        success: false, 
        data: {} as LeaveRequest,
        message: 'Failed to update request'
      };
    }
  },

  // Update leave request with full data (for employee portal modifications)
  updateLeaveRequest: async (id: string, leaveData: {
    leaveType: string;
    startDate: string;
    endDate: string;
    reason: string;
    emergencyContact?: string;
    attachments?: File[];
  }): Promise<ApiResponse<LeaveRequest>> => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/leave-requests/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify(leaveData)
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error updating leave request:', error);
      return { 
        success: false, 
        data: {} as LeaveRequest,
        message: 'Failed to update leave request'
      };
    }
  },

  approve: async (id: number, reason?: string): Promise<ApiResponse<LeaveRequest>> => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/leave-requests/${id}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify({ reason })
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error approving leave request:', error);
      return { 
        success: false, 
        data: {} as LeaveRequest,
        message: 'Failed to approve request'
      };
    }
  },

  reject: async (id: number, reason: string): Promise<ApiResponse<LeaveRequest>> => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/leave-requests/${id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify({ reason })
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error rejecting leave request:', error);
      return { 
        success: false, 
        data: {} as LeaveRequest,
        message: 'Failed to reject request'
      };
    }
  },

  // Workflow action handler - unified approach for all workflow actions
  handleWorkflowAction: async (
    requestId: string, 
    action: 'approve' | 'reject' | 'modify' | 'cancel', 
    reason?: string, 
    modifications?: any
  ): Promise<ApiResponse<LeaveRequest>> => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/leave-requests/${requestId}/workflow-action`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify({ action, reason, modifications })
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error handling workflow action:', error);
      return { 
        success: false, 
        data: {} as LeaveRequest,
        message: 'Failed to process workflow action'
      };
    }
  },

  // Get audit trail for a leave request
  getAuditTrail: async (requestId: string): Promise<ApiResponse<any[]>> => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/leave-requests/${requestId}/audit-trail`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        }
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching audit trail:', error);
      return { 
        success: false, 
        data: [],
        message: 'Failed to fetch audit trail'
      };
    }
  },

  // Get leave requests for a specific employee - used by employee portal
  getByEmployeeId: async (employeeId: number, cacheBuster?: number): Promise<ApiResponse<LeaveRequest[]>> => {
    try {
      const token = localStorage.getItem('authToken');
      const timestamp = cacheBuster || Date.now();
      const url = `/api/leave-requests/employee/${employeeId}?_t=${timestamp}`;
      
      console.log(`🔄 Fetching employee leave requests from: ${url}`);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
      
      const data = await response.json();
      console.log(`📡 Employee leave requests response:`, data);
      
      // Log each request status for debugging
      if (data.success && data.data) {
        console.log('📋 Leave requests status breakdown:', 
          data.data.map((req: any) => ({ 
            id: req.id, 
            status: req.status, 
            leaveType: req.leaveType 
          }))
        );
      }
      
      return data;
    } catch (error) {
      console.error('Error fetching employee leave requests:', error);
      return { 
        success: false, 
        data: [],
        message: 'Failed to fetch employee requests'
      };
    }
  }
};

// Leave Balances API
export const leaveBalancesApi = {
  getByEmployeeId: async (employeeId: number): Promise<ApiResponse<LeaveBalance[]>> => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/leave-balances/employee/${employeeId}`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        }
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching leave balances:', error);
      return { 
        success: true, 
        data: []
      };
    }
  },

  getAllActiveEmployeesWithBalances: async (
    year?: number, 
    department?: string, 
    page: number = 1, 
    limit: number = 10, 
    search?: string
  ): Promise<ApiResponse<any[]>> => {
    try {
      const token = localStorage.getItem('authToken');
      let url = '/api/leave-balances/all-active-employees';
      
      const params = new URLSearchParams();
      if (year) params.append('year', year.toString());
      if (department) params.append('department', department);
      params.append('page', page.toString());
      params.append('limit', limit.toString());
      if (search) params.append('search', search);
      
      url += `?${params.toString()}`;
      
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        }
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching all active employees with balances:', error);
      return { 
        success: false, 
        data: [],
        message: 'Failed to fetch employee balances'
      };
    }
  },

  bulkUpdateAllocations: async (requestData: {
    adjustments: Array<{
      employeeId: number;
      employeeName: string;
      adjustments: Array<{
        leaveType: string;
        currentValue: number;
        adjustmentValue: number;
        newValue: number;
        adjustmentMode: string;
      }>;
    }>;
    reason: string;
    adjustmentMode: string;
    updatedBy: string;
  }): Promise<ApiResponse<any>> => {
    try {
      const token = localStorage.getItem('authToken');
      console.log('🔐 Auth token exists:', !!token);
      console.log('🔐 Auth token preview:', token ? token.substring(0, 20) + '...' : 'No token');
      
      // Transform the data to match the backend expectation
      const employeeIds = requestData.adjustments.map(emp => emp.employeeId);
      
      // Flatten all adjustments from all employees into a single array
      const adjustments = requestData.adjustments.flatMap(emp => 
        emp.adjustments.map(adj => ({
          leaveType: adj.leaveType,
          adjustmentType: adj.adjustmentMode,
          value: adj.adjustmentValue
        }))
      );

      // Remove duplicates by creating a unique set based on leaveType
      const uniqueAdjustments = adjustments.reduce((acc, current) => {
        const existing = acc.find(item => item.leaveType === current.leaveType);
        if (!existing) {
          acc.push(current);
        }
        return acc;
      }, [] as Array<{leaveType: string; adjustmentType: string; value: number}>);

      const payload = {
        employeeIds,
        adjustments: uniqueAdjustments,
        reason: requestData.reason
      };

      console.log('🚀 Sending bulk adjustment request:', payload);
      console.log('🌐 Request URL: /api/leave-balances/bulk-adjust');
      console.log('🔑 Authorization header:', token ? `Bearer ${token.substring(0, 20)}...` : 'No token');

      const response = await fetch('/api/leave-balances/bulk-adjust', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify(payload)
      });

      console.log('📥 Response status:', response.status);
      console.log('📥 Response headers:', response.headers);

      const data = await response.json();
      console.log('📥 Response data:', data);
      
      if (!response.ok) {
        throw new Error(data.message || 'Failed to bulk update allocations');
      }

      return data;
    } catch (error: any) {
      console.error('Error bulk updating allocations:', error);
      return { 
        success: false, 
        data: null,
        message: error.message || 'Failed to bulk update allocations'
      };
    }
  },

  // Update individual leave balance
  updateLeaveBalance: async (data: {
    employeeId: number;
    leaveType: string;
    year: number;
    totalAllocated: number;
    notes?: string;
  }): Promise<ApiResponse<any>> => {
    try {
      console.log('🔄 Updating leave balance:', data);
      
      const token = localStorage.getItem('authToken');
      const response = await fetch('/api/leave-balances', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify(data)
      });

      const result = await response.json();
      
      console.log('📡 API Response:', {
        status: response.status,
        statusText: response.statusText,
        success: result.success,
        message: result.message,
        data: result.data
      });
      
      if (!response.ok) {
        throw new Error(result.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return result;
    } catch (error: any) {
      console.error('❌ Error updating leave balance:', error);
      return { 
        success: false, 
        data: null,
        message: error.message || 'Failed to update leave balance'
      };
    }
  },

  // Save all employee leave allocations to database
  saveAllAllocations: async (allocations: {
    employeeId: number;
    employeeName: string;
    leaveAllocations: { [leaveType: string]: number };
  }[]): Promise<ApiResponse<any>> => {
    try {
      console.log('💾 Saving all leave allocations to database:', allocations.length, 'employees');
      
      const token = localStorage.getItem('authToken');
      const currentYear = new Date().getFullYear();
      
      // Prepare the data for bulk save
      const saveData = allocations.flatMap(employee => 
        Object.entries(employee.leaveAllocations).map(([leaveType, totalAllocated]) => ({
          employeeId: employee.employeeId,
          leaveType,
          year: currentYear,
          totalAllocated,
          notes: `Allocation saved from Leave Management - ${new Date().toISOString()}`
        }))
      );

      console.log('📡 Sending bulk allocation data:', saveData.length, 'records');

      const response = await fetch('/api/leave-balances/bulk-save-allocations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify({ allocations: saveData })
      });

      const result = await response.json();
      
      console.log('📡 Bulk save response:', {
        status: response.status,
        success: result.success,
        message: result.message,
        savedCount: result.data?.savedCount
      });
      
      if (!response.ok) {
        throw new Error(result.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return result;
    } catch (error: any) {
      console.error('❌ Error saving all allocations:', error);
      return { 
        success: false, 
        data: null,
        message: error.message || 'Failed to save allocations to database'
      };
    }
  }
};

// Leave Types API
export const leaveTypesApi = {
  // Get all leave types
  getAll: async (): Promise<ApiResponse<CustomLeaveType[]>> => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('/api/leave-policies/leave-types', {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        }
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching leave types:', error);
      // Return empty array instead of hardcoded defaults
      return {
        success: true,
        data: []
      };
    }
  },

  // Get active leave types only
  getActive: async (): Promise<ApiResponse<CustomLeaveType[]>> => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('/api/leave-policies/leave-types/active', {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        }
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching active leave types:', error);
      return {
        success: true,
        data: []
      };
    }
  },

  // Get leave type by ID
  getById: async (id: string): Promise<ApiResponse<CustomLeaveType>> => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/leave-policies/leave-types/${id}`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        }
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching leave type:', error);
      return {
        success: false,
        data: null as any,
        message: 'Failed to fetch leave type'
      };
    }
  },

  // Create new leave type
  create: async (leaveTypeData: Omit<CustomLeaveType, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<CustomLeaveType>> => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('/api/leave-policies/leave-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify(leaveTypeData)
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error creating leave type:', error);
      return {
        success: false,
        data: null as any,
        message: 'Failed to create leave type'
      };
    }
  },

  // Update leave type
  update: async (id: string, leaveTypeData: Partial<CustomLeaveType>): Promise<ApiResponse<CustomLeaveType>> => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/leave-policies/leave-types/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify(leaveTypeData)
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error updating leave type:', error);
      return {
        success: false,
        data: null as any,
        message: 'Failed to update leave type'
      };
    }
  },

  // Delete leave type
  delete: async (id: string): Promise<ApiResponse<null>> => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/leave-policies/leave-types/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        }
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error deleting leave type:', error);
      return {
        success: false,
        data: null,
        message: 'Failed to delete leave type'
      };
    }
  },

  // Toggle leave type active status
  toggleStatus: async (id: string): Promise<ApiResponse<CustomLeaveType>> => {
    try {
      const token = localStorage.getItem('authToken');
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
      };

      // Get current leave type
      const getResponse = await fetch(`/api/leave-policies/leave-types/${id}`, {
        headers
      });
      const getCurrentData = await getResponse.json();
      
      if (!getCurrentData.success) {
        return getCurrentData;
      }

      // Update the status
      const response = await fetch(`/api/leave-policies/leave-types/${id}`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({ 
          isActive: !getCurrentData.data.isActive 
        })
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error toggling leave type status:', error);
      return {
        success: false,
        data: null as any,
        message: 'Failed to toggle leave type status'
      };
    }
  }
}; 