import React, { useState, useEffect } from 'react';
import { 
  Download, 
  FileText, 
  Plus, 
  Filter, 
  ChevronDown, 
  ChevronRight,
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Search
} from 'lucide-react';
import { TaxComplianceReport } from '../../../types/payroll';

interface TaxComplianceReportsProps {
  isAdmin?: boolean;
}

const TaxComplianceReports: React.FC<TaxComplianceReportsProps> = ({ isAdmin = false }) => {
  const [loading, setLoading] = useState(true);
  const [reports, setReports] = useState<TaxComplianceReport[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedYear, setSelectedYear] = useState<string>(new Date().getFullYear().toString());
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');
  
  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'PKR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };
  
  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="mr-1 h-3 w-3" />
            Pending
          </span>
        );
      case 'submitted':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <FileText className="mr-1 h-3 w-3" />
            Submitted
          </span>
        );
      case 'accepted':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="mr-1 h-3 w-3" />
            Accepted
          </span>
        );
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircle className="mr-1 h-3 w-3" />
            Rejected
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };
  
  // Load mock data
  useEffect(() => {
    setLoading(true);
    
    // Simulate API call to fetch tax reports
    setTimeout(() => {
      const currentYear = new Date().getFullYear();
      const mockReports: TaxComplianceReport[] = [
        {
          id: 1,
          reportType: 'monthly',
          period: {
            startDate: `${currentYear}-01-01`,
            endDate: `${currentYear}-01-31`
          },
          dueDate: `${currentYear}-02-15`,
          submissionDate: `${currentYear}-02-10`,
          totalTaxableAmount: 5750000,
          totalTaxCollected: 525000,
          status: 'accepted',
          filingReference: 'TX-2023-JAN-001',
          notes: 'January 2023 employee income tax filing',
          generatedBy: 1,
          documentUrl: '/documents/tax/monthly/jan-2023.pdf'
        },
        {
          id: 2,
          reportType: 'monthly',
          period: {
            startDate: `${currentYear}-02-01`,
            endDate: `${currentYear}-02-28`
          },
          dueDate: `${currentYear}-03-15`,
          submissionDate: `${currentYear}-03-12`,
          totalTaxableAmount: 5980000,
          totalTaxCollected: 545000,
          status: 'accepted',
          filingReference: 'TX-2023-FEB-001',
          notes: 'February 2023 employee income tax filing',
          generatedBy: 1,
          documentUrl: '/documents/tax/monthly/feb-2023.pdf'
        },
        {
          id: 3,
          reportType: 'monthly',
          period: {
            startDate: `${currentYear}-03-01`,
            endDate: `${currentYear}-03-31`
          },
          dueDate: `${currentYear}-04-15`,
          submissionDate: `${currentYear}-04-14`,
          totalTaxableAmount: 6100000,
          totalTaxCollected: 560000,
          status: 'accepted',
          filingReference: 'TX-2023-MAR-001',
          notes: 'March 2023 employee income tax filing',
          generatedBy: 1,
          documentUrl: '/documents/tax/monthly/mar-2023.pdf'
        },
        {
          id: 4,
          reportType: 'quarterly',
          period: {
            startDate: `${currentYear}-01-01`,
            endDate: `${currentYear}-03-31`
          },
          dueDate: `${currentYear}-04-30`,
          submissionDate: `${currentYear}-04-25`,
          totalTaxableAmount: 17830000,
          totalTaxCollected: 1630000,
          status: 'accepted',
          filingReference: 'TX-2023-Q1-001',
          notes: 'Q1 2023 income tax summary filing',
          generatedBy: 1,
          documentUrl: '/documents/tax/quarterly/q1-2023.pdf'
        },
        {
          id: 5,
          reportType: 'monthly',
          period: {
            startDate: `${currentYear}-04-01`,
            endDate: `${currentYear}-04-30`
          },
          dueDate: `${currentYear}-05-15`,
          submissionDate: `${currentYear}-05-10`,
          totalTaxableAmount: 6250000,
          totalTaxCollected: 575000,
          status: 'accepted',
          filingReference: 'TX-2023-APR-001',
          notes: 'April 2023 employee income tax filing',
          generatedBy: 1,
          documentUrl: '/documents/tax/monthly/apr-2023.pdf'
        },
        {
          id: 6,
          reportType: 'monthly',
          period: {
            startDate: `${currentYear}-05-01`,
            endDate: `${currentYear}-05-31`
          },
          dueDate: `${currentYear}-06-15`,
          submissionDate: `${currentYear}-06-12`,
          totalTaxableAmount: 6300000,
          totalTaxCollected: 580000,
          status: 'accepted',
          filingReference: 'TX-2023-MAY-001',
          notes: 'May 2023 employee income tax filing',
          generatedBy: 1,
          documentUrl: '/documents/tax/monthly/may-2023.pdf'
        },
        {
          id: 7,
          reportType: 'monthly',
          period: {
            startDate: `${currentYear}-06-01`,
            endDate: `${currentYear}-06-30`
          },
          dueDate: `${currentYear}-07-15`,
          submissionDate: `${currentYear}-07-10`,
          totalTaxableAmount: 6350000,
          totalTaxCollected: 590000,
          status: 'accepted',
          filingReference: 'TX-2023-JUN-001',
          notes: 'June 2023 employee income tax filing',
          generatedBy: 1,
          documentUrl: '/documents/tax/monthly/jun-2023.pdf'
        },
        {
          id: 8,
          reportType: 'quarterly',
          period: {
            startDate: `${currentYear}-04-01`,
            endDate: `${currentYear}-06-30`
          },
          dueDate: `${currentYear}-07-31`,
          submissionDate: `${currentYear}-07-25`,
          totalTaxableAmount: 18900000,
          totalTaxCollected: 1745000,
          status: 'accepted',
          filingReference: 'TX-2023-Q2-001',
          notes: 'Q2 2023 income tax summary filing',
          generatedBy: 1,
          documentUrl: '/documents/tax/quarterly/q2-2023.pdf'
        },
        {
          id: 9,
          reportType: 'semi_annual',
          period: {
            startDate: `${currentYear}-01-01`,
            endDate: `${currentYear}-06-30`
          },
          dueDate: `${currentYear}-08-15`,
          submissionDate: `${currentYear}-08-10`,
          totalTaxableAmount: 36730000,
          totalTaxCollected: 3375000,
          status: 'accepted',
          filingReference: 'TX-2023-H1-001',
          notes: 'First half 2023 income tax summary filing',
          generatedBy: 1,
          documentUrl: '/documents/tax/semi-annual/h1-2023.pdf'
        },
        {
          id: 10,
          reportType: 'monthly',
          period: {
            startDate: `${currentYear}-07-01`,
            endDate: `${currentYear}-07-31`
          },
          dueDate: `${currentYear}-08-15`,
          submissionDate: `${currentYear}-08-10`,
          totalTaxableAmount: 6400000,
          totalTaxCollected: 595000,
          status: 'accepted',
          filingReference: 'TX-2023-JUL-001',
          notes: 'July 2023 employee income tax filing',
          generatedBy: 1,
          documentUrl: '/documents/tax/monthly/jul-2023.pdf'
        },
        {
          id: 11,
          reportType: 'monthly',
          period: {
            startDate: `${currentYear}-08-01`,
            endDate: `${currentYear}-08-31`
          },
          dueDate: `${currentYear}-09-15`,
          status: 'pending',
          totalTaxableAmount: 6450000,
          totalTaxCollected: 600000
        }
      ];
      
      setReports(mockReports);
      setLoading(false);
    }, 1000);
  }, []);
  
  // Filter reports based on selected filters
  const filteredReports = reports.filter((report) => {
    const yearMatch = selectedYear === 'all' || 
      new Date(report.period.startDate).getFullYear().toString() === selectedYear;
    
    const statusMatch = selectedStatus === 'all' || report.status === selectedStatus;
    
    const typeMatch = selectedType === 'all' || report.reportType === selectedType;
    
    return yearMatch && statusMatch && typeMatch;
  });

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div>
      {/* Header and Actions */}
      <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <h3 className="text-lg font-medium text-gray-900">Tax Compliance Reports</h3>
        <div className="mt-3 sm:mt-0 flex space-x-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
            {showFilters ? <ChevronDown className="h-4 w-4 ml-1" /> : <ChevronRight className="h-4 w-4 ml-1" />}
          </button>
          
          {isAdmin && (
            <button
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none"
            >
              <Plus className="h-4 w-4 mr-2" />
              Generate Report
            </button>
          )}
          
          <button
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
        </div>
      </div>
      
      {/* Filters */}
      {showFilters && (
        <div className="mb-6 p-4 bg-gray-50 rounded-md border border-gray-200">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <label htmlFor="year-filter" className="block text-xs font-medium text-gray-700 mb-1">Year</label>
              <select
                id="year-filter"
                value={selectedYear}
                onChange={(e) => setSelectedYear(e.target.value)}
                className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="all">All Years</option>
                <option value="2023">2023</option>
                <option value="2022">2022</option>
                <option value="2021">2021</option>
              </select>
            </div>
            
            <div>
              <label htmlFor="status-filter" className="block text-xs font-medium text-gray-700 mb-1">Status</label>
              <select
                id="status-filter"
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="all">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="submitted">Submitted</option>
                <option value="accepted">Accepted</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>
            
            <div>
              <label htmlFor="type-filter" className="block text-xs font-medium text-gray-700 mb-1">Report Type</label>
              <select
                id="type-filter"
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="all">All Types</option>
                <option value="monthly">Monthly</option>
                <option value="quarterly">Quarterly</option>
                <option value="semi_annual">Semi-Annual</option>
                <option value="annual">Annual</option>
              </select>
            </div>
            
            <div>
              <label htmlFor="search-filter" className="block text-xs font-medium text-gray-700 mb-1">Search</label>
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  id="search-filter"
                  placeholder="Search by reference..."
                  className="focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md"
                />
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Reports Table */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Report Details
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Period
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Due Date
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tax Amount
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredReports.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                    No reports found matching the selected filters.
                  </td>
                </tr>
              ) : (
                filteredReports.map((report) => (
                  <tr key={report.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {report.reportType.charAt(0).toUpperCase() + report.reportType.slice(1)} Report
                      </div>
                      {report.filingReference && (
                        <div className="text-sm text-gray-500">Ref: {report.filingReference}</div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {formatDate(report.period.startDate)} - {formatDate(report.period.endDate)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{formatDate(report.dueDate)}</div>
                      {report.submissionDate && (
                        <div className="text-sm text-gray-500">
                          Submitted: {formatDate(report.submissionDate)}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{formatCurrency(report.totalTaxCollected)}</div>
                      <div className="text-sm text-gray-500">
                        Taxable: {formatCurrency(report.totalTaxableAmount)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(report.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-3">
                        {report.documentUrl && (
                          <button
                            className="text-blue-600 hover:text-blue-900"
                            title="Download Report"
                          >
                            <Download className="h-4 w-4" />
                          </button>
                        )}
                        <button
                          className="text-gray-600 hover:text-gray-900"
                          title="View Details"
                        >
                          <FileText className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default TaxComplianceReports; 