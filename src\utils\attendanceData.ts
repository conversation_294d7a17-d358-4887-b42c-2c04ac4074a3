import { subDays, format } from 'date-fns';

export interface AttendanceData {
  date: string;
  present: number;
  absent: number;
  late: number;
  onLeave: number;
}

export const generateSampleAttendanceData = (days: number = 30): AttendanceData[] => {
  const data: AttendanceData[] = [];
  const totalEmployees = 150; // Total number of employees
  
  // Generate data for the last 'days' days
  for (let i = days; i >= 0; i--) {
    const date = subDays(new Date(), i);
    const day = date.getDay();
    
    // Skip weekends (0 = Sunday, 6 = Saturday)
    if (day === 0 || day === 6) continue;
    
    // Base values (weekday)
    let present = Math.floor(Math.random() * 20) + 100; // 100-120
    let late = Math.floor(Math.random() * 15) + 5; // 5-20
    let onLeave = Math.floor(Math.random() * 10) + 1; // 1-10
    let absent = Math.max(0, totalEmployees - present - late - onLeave);
    
    // Adjust for Mondays and Fridays
    if (day === 1) { // Monday
      absent += Math.floor(Math.random() * 5) + 5;
      present = Math.max(0, present - 10);
    } else if (day === 5) { // Friday
      onLeave += Math.floor(Math.random() * 5) + 3;
      present = Math.max(0, present - 5);
    }
    
    // Ensure we don't exceed total employees
    const total = present + late + onLeave + absent;
    if (total > totalEmployees) {
      const diff = total - totalEmployees;
      present = Math.max(0, present - diff);
    }
    
    data.push({
      date: date.toISOString(),
      present,
      absent,
      late,
      onLeave
    });
  }
  
  return data;
};

export const getCurrentMonthAttendanceStats = (data: AttendanceData[]) => {
  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();
  
  const monthData = data.filter(item => {
    const date = new Date(item.date);
    return date.getMonth() === currentMonth && date.getFullYear() === currentYear;
  });
  
  return monthData.reduce(
    (acc, day) => {
      acc.totalPresent += day.present || 0;
      acc.totalLate += day.late || 0;
      acc.totalOnLeave += day.onLeave || 0;
      acc.totalAbsent += day.absent || 0;
      acc.totalDays++;
      return acc;
    },
    { 
      totalPresent: 0, 
      totalLate: 0, 
      totalOnLeave: 0, 
      totalAbsent: 0,
      totalDays: 0
    }
  );
};
