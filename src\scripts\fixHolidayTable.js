const mysql = require('mysql2/promise');
require('dotenv').config();

async function fixHolidayTable() {
  let connection;
  
  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USERNAME || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_DATABASE || 'ims_db',
      port: process.env.DB_PORT || 3306
    });

    console.log('✅ Connected to database');

    // Add missing columns to holidays table
    console.log('🔧 Adding missing columns to holidays table...');
    
    // Check if createdBy column exists
    const [createdByExists] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = '${process.env.DB_DATABASE || 'ims_db'}' 
      AND TABLE_NAME = 'holidays' 
      AND COLUMN_NAME = 'createdBy'
    `);
    
    if (createdByExists.length === 0) {
      await connection.execute(`ALTER TABLE holidays ADD COLUMN createdBy VARCHAR(100) NULL`);
      console.log('   ✅ Added createdBy column');
    } else {
      console.log('   ⚪ createdBy column already exists');
    }
    
    // Check if updatedBy column exists
    const [updatedByExists] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = '${process.env.DB_DATABASE || 'ims_db'}' 
      AND TABLE_NAME = 'holidays' 
      AND COLUMN_NAME = 'updatedBy'
    `);
    
    if (updatedByExists.length === 0) {
      await connection.execute(`ALTER TABLE holidays ADD COLUMN updatedBy VARCHAR(100) NULL`);
      console.log('   ✅ Added updatedBy column');
    } else {
      console.log('   ⚪ updatedBy column already exists');
    }

    console.log('✅ Missing columns added successfully');

    // Test the updated table structure
    const [tableInfo] = await connection.execute(`
      DESCRIBE holidays
    `);
    
    console.log('\n📋 Current holidays table structure:');
    tableInfo.forEach(column => {
      console.log(`   • ${column.Field} (${column.Type}) - ${column.Null === 'YES' ? 'nullable' : 'not null'}`);
    });

    // Test reading holidays to ensure everything works
    const [holidays] = await connection.execute('SELECT id, name, date, type FROM holidays LIMIT 3');
    
    console.log(`\n✅ Successfully read ${holidays.length} sample holidays from updated table`);
    holidays.forEach(holiday => {
      console.log(`   • ${holiday.name} (${holiday.date}) - ${holiday.type}`);
    });
    
  } catch (error) {
    console.error('❌ Error fixing holiday table:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔐 Database connection closed');
    }
  }
}

// Run the fix
fixHolidayTable(); 