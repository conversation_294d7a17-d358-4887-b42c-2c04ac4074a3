import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, Jo<PERSON><PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, Unique } from 'typeorm';
import { Employee } from '../server/entities/Employee';

@Entity('leave_allocations')
@Unique(['employeeId', 'leaveType', 'year'])
export class LeaveAllocation {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  employeeId: number;

  @ManyToOne(() => Employee, { eager: true })
  @JoinColumn({ name: 'employeeId' })
  employee: Employee;

  @Column({ type: 'varchar', length: 50 })
  leaveType: string;

  @Column({ type: 'int' })
  year: number;

  @Column({ type: 'int', default: 0 })
  policyAllocation: number; // Amount from policy

  @Column({ type: 'int', default: 0 })
  manualAdjustment: number; // Manual adjustments (+/-)

  @Column({ type: 'int', default: 0 })
  carriedForward: number; // From previous year

  @Column({ type: 'enum', enum: ['POLICY', 'MANUAL', 'BULK_ADJUSTMENT', 'CARRY_FORWARD'], default: 'POLICY' })
  source: 'POLICY' | 'MANUAL' | 'BULK_ADJUSTMENT' | 'CARRY_FORWARD';

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'int', nullable: true })
  createdBy?: number; // User who created/modified

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual property for total allocated
  get totalAllocated(): number {
    return this.policyAllocation + this.manualAdjustment + this.carriedForward;
  }

  // Methods for allocation operations
  adjustAllocation(amount: number, notes?: string, source: 'MANUAL' | 'BULK_ADJUSTMENT' = 'MANUAL'): void {
    this.manualAdjustment += amount;
    this.source = source;
    if (notes) this.notes = notes;
    this.updatedAt = new Date();
  }

  setCarryForward(amount: number, notes?: string): void {
    this.carriedForward = amount;
    if (notes) this.notes = notes;
    this.updatedAt = new Date();
  }
} 