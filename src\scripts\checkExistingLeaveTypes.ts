import { AppDataSource } from '../config/database';
import { LeaveTypePolicy } from '../entities/LeaveTypePolicy';
import { LeavePolicyConfiguration } from '../entities/LeavePolicyConfiguration';

async function checkExistingLeaveTypes() {
  try {
    console.log('🔍 Initializing database connection...');
    await AppDataSource.initialize();
    
    const leaveTypePolicyRepository = AppDataSource.getRepository(LeaveTypePolicy);
    const leavePolicyConfigRepository = AppDataSource.getRepository(LeavePolicyConfiguration);
    
    console.log('\n📋 Checking existing leave type policies...');
    const allLeaveTypes = await leaveTypePolicyRepository.find();
    
    console.log(`Found ${allLeaveTypes.length} leave type policies:`);
    allLeaveTypes.forEach((lt, index) => {
      console.log(`${index + 1}. ID: ${lt.id}, Type: ${lt.leaveType}, Display: ${lt.displayName}, Active: ${lt.isActive}, PolicyConfigId: ${lt.policyConfigurationId}`);
    });
    
    console.log('\n📋 Checking policy configurations...');
    const allPolicyConfigs = await leavePolicyConfigRepository.find({
      relations: ['leaveTypes']
    });
    
    console.log(`Found ${allPolicyConfigs.length} policy configurations:`);
    allPolicyConfigs.forEach((pc, index) => {
      console.log(`${index + 1}. ID: ${pc.id}, Active: ${pc.isActive}, Version: ${pc.version}, LeaveTypes: ${pc.leaveTypes?.length || 0}`);
      pc.leaveTypes?.forEach((lt, ltIndex) => {
        console.log(`   - ${ltIndex + 1}. ${lt.leaveType} (${lt.displayName})`);
      });
    });
    
    console.log('\n🧹 Checking for orphaned leave types (without policy config)...');
    const orphanedLeaveTypes = allLeaveTypes.filter(lt => !lt.policyConfigurationId);
    console.log(`Found ${orphanedLeaveTypes.length} orphaned leave types:`);
    orphanedLeaveTypes.forEach((lt, index) => {
      console.log(`${index + 1}. ID: ${lt.id}, Type: ${lt.leaveType}, Display: ${lt.displayName}`);
    });
    
    console.log('\n🔍 Checking table structure...');
    const queryRunner = AppDataSource.createQueryRunner();
    
    try {
      const columns = await queryRunner.query(`
        SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'ims_db' 
        AND TABLE_NAME = 'leave_type_policies'
        ORDER BY ORDINAL_POSITION
      `);
      
      console.log('Table structure for leave_type_policies:');
      columns.forEach((col: any) => {
        console.log(`- ${col.COLUMN_NAME}: ${col.DATA_TYPE}${col.CHARACTER_MAXIMUM_LENGTH ? `(${col.CHARACTER_MAXIMUM_LENGTH})` : ''} ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}`);
      });
    } finally {
      await queryRunner.release();
    }
    
  } catch (error) {
    console.error('❌ Error checking database:', error);
  } finally {
    await AppDataSource.destroy();
  }
}

checkExistingLeaveTypes().catch(console.error); 