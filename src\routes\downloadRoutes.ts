import express from 'express';
import path from 'path';
import fs from 'fs';
import mime from 'mime-types';

const router = express.Router();

// Helper function to set CORS headers
const setCorsHeaders = (res: express.Response) => {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Cache-Control, Pragma, Expires');
  res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours
};

// Handle OPTIONS requests for CORS preflight
router.options('/file/:filename', (req, res) => {
  setCorsHeaders(res);
  return res.status(200).end();
});

/**
 * GET /api/download/file/:filename
 * Downloads a file from the uploads directory
 * Works with PDF, Word, Excel, PNG, JPG and other files
 */
router.get('/file/:filename', (req, res) => {
  try {
    // Set CORS headers first thing
    setCorsHeaders(res);
    
    const { filename } = req.params;
    
    // Security check - prevent path traversal attacks
    if (filename.includes('../') || filename.includes('..\\')) {
      console.error(`[File Download] Path traversal attempt: ${filename}`);
      return res.status(400).json({
        success: false,
        message: 'Invalid filename'
      });
    }
    
    // Support multiple possible file locations
    const possiblePaths = [
      path.join(process.cwd(), 'public', 'uploads', 'it-invoices', filename),
      path.join(process.cwd(), 'public', 'uploads', 'invoices', filename),
      path.join(process.cwd(), 'uploads', 'it-invoices', filename),
      path.join(process.cwd(), 'uploads', 'invoices', filename),
      path.join(process.cwd(), 'public', 'uploads', filename)
    ];
    
    // Log all possible paths we're checking for debugging
    console.log(`[File Download] Checking possible file locations for: ${filename}`);
    possiblePaths.forEach(p => console.log(`- ${p} (exists: ${fs.existsSync(p)})`));
    
    // Find the first path that exists
    const filePath = possiblePaths.find(p => fs.existsSync(p));
    
    if (!filePath) {
      console.error(`[File Download] File not found: ${filename}`);
      return res.status(404).json({
        success: false,
        message: 'File not found',
        checkedPaths: possiblePaths
      });
    }
    
    // Get content type from extension or default to octet-stream
    const contentType = mime.lookup(filePath) || 'application/octet-stream';
    
    // Set headers for proper file handling
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `inline; filename="${filename}"`);
    
    // Allow appropriate caching based on file type
    if (contentType.startsWith('image/')) {
      // Cache images for 1 day
      res.setHeader('Cache-Control', 'public, max-age=86400');
    } else {
      // Don't cache other files
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
    }
    
    // Stream the file to the response
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
    
    // Handle potential errors
    fileStream.on('error', (error) => {
      console.error(`[File Download] Error streaming file: ${error}`);
      // Only send error if headers have not been sent
      if (!res.headersSent) {
        return res.status(500).json({
          success: false,
          message: 'Error streaming file',
          error: error.message
        });
      }
    });
    
    console.log(`[File Download] Successfully streaming file: ${filename}, type: ${contentType}`);
  } catch (error) {
    console.error(`[File Download] Unexpected error:`, error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : 'Unexpected error downloading file'
    });
  }
});

export default router; 