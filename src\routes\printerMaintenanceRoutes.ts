import { Router } from 'express';
import { PrinterMaintenanceController } from '../controllers/PrinterMaintenanceController';
import { authorize } from '../middleware/authorize';
import { requireAuth } from '../middleware/auth';
import { UserRole } from '../types/common';
import path from 'path';
import fs from 'fs';
import multer from 'multer';
import { getRepository } from 'typeorm';
import { PrinterMaintenance } from '../entities/PrinterMaintenance';

const router = Router();
const printerMaintenanceController = new PrinterMaintenanceController();

// Configure multer storage
const storage = multer.diskStorage({
  destination: (_req, _file, cb) => {
    const uploadDir = path.join(__dirname, '..', 'uploads', 'invoices');
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (_req, file, cb) => {
    // Create a unique filename with timestamp and original extension
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, `invoice_${uniqueSuffix}${ext}`);
  }
});

const upload = multer({ storage });

// Get all maintenance records
router.get(
  '/',
  requireAuth,
  authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF, UserRole.IT_SUPPORT]),
  printerMaintenanceController.getMaintenanceRecords.bind(printerMaintenanceController)
);

// Create a new printer maintenance record
router.post(
  '/',
  authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF]),
  printerMaintenanceController.recordMaintenance.bind(printerMaintenanceController)
);

// Upload invoice for a maintenance record
router.post(
  '/:id/upload-invoice',
  authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF]),
  upload.single('invoice'),
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: 'No file uploaded' });
      }

      // Store only the filename
      const filename = req.file.filename;
      
      // Update the maintenance record with just the filename
      const maintenanceId = req.params.id;
      await printerMaintenanceController.updateInvoiceFile(maintenanceId, filename);

      res.status(200).json({
        message: 'Invoice uploaded successfully',
        filename: filename
      });
    } catch (error) {
      console.error('Error uploading invoice:', error);
      res.status(500).json({ message: 'Failed to upload invoice' });
    }
  }
);

// Approve a maintenance invoice
router.patch(
  '/:id/approve',
  authorize([UserRole.IT_ADMIN]),
  printerMaintenanceController.approveInvoice.bind(printerMaintenanceController)
);

// Get maintenance records pending approval
router.get(
  '/pending-approvals',
  authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF]),
  printerMaintenanceController.getPendingApprovals.bind(printerMaintenanceController)
);

// Get maintenance history for an asset
router.get(
  '/asset/:assetId',
  authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF, UserRole.IT_SUPPORT]),
  printerMaintenanceController.getMaintenanceHistory.bind(printerMaintenanceController)
);

// Generate finance report
router.get(
  '/finance-report',
  authorize([UserRole.IT_ADMIN]),
  printerMaintenanceController.generateFinanceReport.bind(printerMaintenanceController)
);

// Mark records as submitted to finance
router.post(
  '/submit-to-finance',
  authorize([UserRole.IT_ADMIN]),
  printerMaintenanceController.markAsSubmittedToFinance.bind(printerMaintenanceController)
);

// Download route
router.get('/download', 
  requireAuth,
  authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF, UserRole.IT_SUPPORT]), 
  async (req, res) => {
    try {
      const filename = req.query.file as string;
      if (!filename) {
        return res.status(400).json({ message: 'Filename is required' });
      }

      // Clean the filename to prevent directory traversal
      const cleanFilename = path.basename(filename);
      
      // Try multiple possible file locations
      const possiblePaths = [
        path.join(process.cwd(), 'uploads', 'invoices', cleanFilename),
        path.join(process.cwd(), 'uploads', cleanFilename),
        path.join(__dirname, '..', 'uploads', 'invoices', cleanFilename),
        path.join(__dirname, '..', 'uploads', cleanFilename)
      ];

      console.log('Download request details:', {
        originalFilename: filename,
        cleanFilename: cleanFilename,
        possiblePaths: possiblePaths
      });

      // Try to find the file in any of the possible locations
      let filePath = null;
      for (const path of possiblePaths) {
        console.log('Checking path:', path);
        if (fs.existsSync(path)) {
          filePath = path;
          console.log('File found at:', path);
          break;
        }
      }

      if (!filePath) {
        console.error('File not found in any location:', {
          checkedPaths: possiblePaths
        });
        return res.status(404).json({ 
          message: 'File not found',
          details: {
            requestedFile: filename,
            checkedLocations: possiblePaths
          }
        });
      }

      console.log('Sending file:', filePath);
      res.download(filePath);
    } catch (error) {
      console.error('Download error:', error);
      if (!res.headersSent) {
        res.status(500).json({ 
          message: 'Error downloading file',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }
);

// Add new route for downloading invoice by maintenance record ID
router.get(
  '/:id/download',
  requireAuth,
  authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF, UserRole.IT_SUPPORT]),
  async (req, res) => {
    try {
      // 1. Extract the ID from req.params.id
      const { id } = req.params;
      
      console.log('Download request for record ID:', id);
      
      if (!id) {
        return res.status(400).json({ message: 'Maintenance record ID is required' });
      }

      // 2. Use that ID to fetch the maintenance record from the database
      const maintenanceRepo = getRepository(PrinterMaintenance);
      const record = await maintenanceRepo.findOne({ where: { id } as any });
      
      if (!record) {
        return res.status(404).json({ message: 'Maintenance record not found' });
      }

      // 3. Get the filename from record.invoiceFile
      if (!record.invoiceFilePath) {
        return res.status(404).json({ message: 'No invoice file found for this record' });
      }

      // 4. Sanitize the filename using path.basename
      const fileName = path.basename(record.invoiceFilePath);
      
      // 5. Construct the full file path
      const filePath = path.join(__dirname, '..', 'uploads', 'invoices', fileName);
      
      // 8. Add console.log to output filePath for debug
      console.log('🛠️ Serving download for:', filePath);
      
      // 6/7. Check if file exists and handle appropriately
      if (fs.existsSync(filePath)) {
        return res.download(filePath);
      } else {
        return res.status(404).json({ message: 'File not found' });
      }
    } catch (error) {
      console.error('Error downloading invoice:', error);
      return res.status(500).json({ 
        message: 'Error downloading invoice', 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  }
);

// Add export route
router.post(
  '/export',
  requireAuth,
  authorize([UserRole.IT_ADMIN, UserRole.IT_STAFF, UserRole.IT_SUPPORT]),
  printerMaintenanceController.exportMaintenance.bind(printerMaintenanceController)
);

export default router; 