const { AppDataSource } = require('../config/database');

/**
 * This script removes all auto-generated serial numbers from the database.
 * It sets the serialNumber field to empty string for all assets where the serialNumber starts with "SN-".
 */
async function removeAutoGeneratedSerialNumbers() {
  try {
    // Initialize the database connection
    await AppDataSource.initialize();
    console.log('Database connection initialized');

    // Execute a direct SQL query to update all assets with auto-generated serial numbers
    const result = await AppDataSource.query(
      "UPDATE assets SET serialNumber = '' WHERE serialNumber LIKE 'SN-%'"
    );
    
    console.log(`Updated ${result.affectedRows || 'all matching'} assets with auto-generated serial numbers`);
    
    // Close the database connection
    await AppDataSource.destroy();
    console.log('Database connection closed');
    
    console.log('Successfully removed all auto-generated serial numbers');
  } catch (error) {
    console.error('Error removing auto-generated serial numbers:', error);
    
    // Make sure to close the database connection even if there's an error
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('Database connection closed after error');
    }
  }
}

// Run the script
removeAutoGeneratedSerialNumbers(); 