import {
  Entity,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  CreateDate<PERSON>olumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  Join<PERSON><PERSON>umn,
  ManyToMany,
  JoinTable
} from 'typeorm';
import { IsNotEmpty, IsOptional, IsEnum, IsDateString, Length, IsNumber } from 'class-validator';
import { JobApplication } from './JobApplication';
import { User } from './User';
import { InterviewFeedback } from './InterviewFeedback';

export enum InterviewType {
  PHONE_SCREENING = 'phone_screening',
  VIDEO_CALL = 'video_call',
  IN_PERSON = 'in_person',
  TECHNICAL = 'technical',
  BEHAVIORAL = 'behavioral',
  PANEL = 'panel',
  GROUP = 'group',
  PRESENTATION = 'presentation',
  CASE_STUDY = 'case_study',
  FINAL = 'final'
}

export enum InterviewStatus {
  SCHEDULED = 'scheduled',
  CONFIRMED = 'confirmed',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  RESCHEDULED = 'rescheduled',
  NO_SHOW = 'no_show'
}

export enum InterviewResult {
  PENDING = 'pending',
  STRONG_YES = 'strong_yes',
  YES = 'yes',
  MAYBE = 'maybe',
  NO = 'no',
  STRONG_NO = 'strong_no'
}

@Entity('interviews')
export class Interview {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255 })
  @IsNotEmpty({ message: 'Interview title is required' })
  @Length(2, 255, { message: 'Interview title must be between 2 and 255 characters' })
  title: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  description: string;

  @Column({
    type: 'enum',
    enum: InterviewType,
    default: InterviewType.VIDEO_CALL
  })
  @IsEnum(InterviewType, { message: 'Invalid interview type' })
  type: InterviewType;

  @Column({
    type: 'enum',
    enum: InterviewStatus,
    default: InterviewStatus.SCHEDULED
  })
  @IsEnum(InterviewStatus, { message: 'Invalid interview status' })
  status: InterviewStatus;

  @Column({
    type: 'enum',
    enum: InterviewResult,
    default: InterviewResult.PENDING
  })
  @IsEnum(InterviewResult, { message: 'Invalid interview result' })
  result: InterviewResult;

  @Column({ type: 'timestamp' })
  @IsNotEmpty({ message: 'Start time is required' })
  @IsDateString({}, { message: 'Invalid start time' })
  startTime: Date;

  @Column({ type: 'timestamp' })
  @IsNotEmpty({ message: 'End time is required' })
  @IsDateString({}, { message: 'Invalid end time' })
  endTime: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  @IsOptional()
  location: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  @IsOptional()
  meetingLink: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  meetingId: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  meetingPassword: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  agenda: string;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  questions: Array<{
    id: string;
    question: string;
    category: string;
    expectedAnswer?: string;
    weight?: number;
  }>;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  preparationNotes: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  candidateInstructions: string;

  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Duration must be a number' })
  durationMinutes: number;

  @Column({ type: 'boolean', default: false })
  isRecorded: boolean;

  @Column({ type: 'varchar', length: 500, nullable: true })
  @IsOptional()
  recordingUrl: string;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  attachments: Array<{
    name: string;
    url: string;
    type: string;
    uploadedAt: Date;
  }>;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  internalNotes: string;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  customFields: Record<string, any>;

  @Column({ type: 'timestamp', nullable: true })
  reminderSentAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  confirmationSentAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  actualStartTime: Date;

  @Column({ type: 'timestamp', nullable: true })
  actualEndTime: Date;

  @Column({ type: 'int', default: 0 })
  @IsNumber({}, { message: 'Overall rating must be a number' })
  overallRating: number; // 1-5 scale

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  summary: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  strengths: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  weaknesses: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  recommendations: string;

  // Relations
  @ManyToOne(() => JobApplication, application => application.interviews, { nullable: false })
  @JoinColumn({ name: 'applicationId' })
  application: JobApplication;

  @Column({ type: 'int' })
  applicationId: number;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'scheduledById' })
  scheduledBy: User;

  @Column({ type: 'uuid' })
  scheduledById: string;

  @ManyToMany(() => User)
  @JoinTable({
    name: 'interview_interviewers',
    joinColumn: { name: 'interviewId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'userId', referencedColumnName: 'id' }
  })
  interviewers: User[];

  @OneToMany(() => InterviewFeedback, feedback => feedback.interview, { cascade: true })
  feedback: InterviewFeedback[];

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Virtual properties
  get duration(): number {
    if (this.actualStartTime && this.actualEndTime) {
      return Math.round((this.actualEndTime.getTime() - this.actualStartTime.getTime()) / (1000 * 60));
    }
    return this.durationMinutes || 0;
  }

  get isUpcoming(): boolean {
    return new Date() < this.startTime && this.status === InterviewStatus.SCHEDULED;
  }

  get isOverdue(): boolean {
    return new Date() > this.endTime && this.status === InterviewStatus.SCHEDULED;
  }

  get timeUntilInterview(): number {
    const now = new Date();
    const diffTime = this.startTime.getTime() - now.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60)); // hours
  }

  get averageFeedbackRating(): number {
    if (!this.feedback || this.feedback.length === 0) return 0;
    const totalRating = this.feedback.reduce((sum, fb) => sum + fb.overallRating, 0);
    return totalRating / this.feedback.length;
  }

  get statusDisplayName(): string {
    return this.status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  get typeDisplayName(): string {
    return this.type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  get resultDisplayName(): string {
    return this.result.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  get isVirtual(): boolean {
    return this.type === InterviewType.VIDEO_CALL || this.type === InterviewType.PHONE_SCREENING;
  }

  get canBeRescheduled(): boolean {
    return [InterviewStatus.SCHEDULED, InterviewStatus.CONFIRMED].includes(this.status) && 
           new Date() < this.startTime;
  }

  get canBeCancelled(): boolean {
    return [InterviewStatus.SCHEDULED, InterviewStatus.CONFIRMED].includes(this.status);
  }

  get requiresPreparation(): boolean {
    return [InterviewType.TECHNICAL, InterviewType.PRESENTATION, InterviewType.CASE_STUDY].includes(this.type);
  }
}
