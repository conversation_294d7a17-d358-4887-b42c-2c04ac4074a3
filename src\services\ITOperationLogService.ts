import { ITOperationLog, IssueCategory, ImpactLevel, OperationStatus } from '../entities/ITOperationLog';
import ITOperationLogRepository from '../repositories/ITOperationLogRepository';
import { User } from '../entities/User';

// Define the structure for form data
export interface ITOperationLogFormData {
  id?: string;
  title: string;
  description: string;
  issueCategory: string;
  assignedTo?: string;
  loggedBy: string;
  location?: string;
  impactLevel: string;
  status: string;
  dateOccurred: string;
  dateResolved?: string;
  tags?: string;
  externalLinks?: string;
  attachment?: string;
  notes?: string;
  createdById?: string;
  lastModifiedById?: string;
}

// Convert form data to entity
const formDataToEntity = (formData: ITOperationLogFormData, currentUser?: User): Partial<ITOperationLog> => {
  const entity: Partial<ITOperationLog> = {
    title: formData.title,
    description: formData.description,
    issueCategory: formData.issueCategory as IssueCategory,
    assignedTo: formData.assignedTo,
    loggedBy: formData.loggedBy,
    location: formData.location,
    impactLevel: formData.impactLevel as ImpactLevel,
    status: formData.status as OperationStatus,
    dateOccurred: new Date(formData.dateOccurred),
    tags: formData.tags,
    externalLinks: formData.externalLinks,
    attachment: formData.attachment,
    notes: formData.notes,
  };

  // Add date resolved if provided
  if (formData.dateResolved) {
    entity.dateResolved = new Date(formData.dateResolved);
  }

  // Add user info
  if (currentUser) {
    if (!formData.id) {
      // New log
      entity.createdById = currentUser.id;
    }
    entity.lastModifiedById = currentUser.id;
  } else {
    // If no user (e.g. from migration or test), use provided IDs
    if (formData.createdById) {
      entity.createdById = formData.createdById;
    }
    if (formData.lastModifiedById) {
      entity.lastModifiedById = formData.lastModifiedById;
    }
  }

  return entity;
};

// Convert entity to form data
const entityToFormData = (entity: ITOperationLog): ITOperationLogFormData => {
  return {
    id: entity.id,
    title: entity.title,
    description: entity.description,
    issueCategory: entity.issueCategory,
    assignedTo: entity.assignedTo,
    loggedBy: entity.loggedBy,
    location: entity.location,
    impactLevel: entity.impactLevel,
    status: entity.status,
    dateOccurred: entity.dateOccurred instanceof Date 
      ? entity.dateOccurred.toISOString().split('T')[0] 
      : typeof entity.dateOccurred === 'string' 
        ? entity.dateOccurred 
        : new Date().toISOString().split('T')[0],
    dateResolved: entity.dateResolved instanceof Date 
      ? entity.dateResolved.toISOString().split('T')[0] 
      : entity.dateResolved 
        ? String(entity.dateResolved) 
        : undefined,
    tags: entity.tags,
    externalLinks: entity.externalLinks,
    attachment: entity.attachment,
    notes: entity.notes,
    createdById: entity.createdById,
    lastModifiedById: entity.lastModifiedById
  };
};

export const ITOperationLogService = {
  // Get all logs with pagination and filtering
  getAllLogs: async (options?: {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
    search?: string;
    issueCategory?: string;
    status?: string;
    impactLevel?: string;
    dateFrom?: string;
    dateTo?: string;
    tags?: string | string[];
  }) => {
    const page = options?.page || 1;
    const limit = options?.limit || 10;
    const skip = (page - 1) * limit;
    
    // Build order object
    const order: { [key: string]: 'ASC' | 'DESC' } = {};
    if (options?.sortBy) {
      order[options.sortBy] = options.sortOrder || 'DESC';
    } else {
      order['createdAt'] = 'DESC'; // Default sort
    }
    
    // Build filters object
    const filters: { [key: string]: any } = {};
    if (options?.search) filters.search = options.search;
    if (options?.issueCategory) filters.issueCategory = options.issueCategory;
    if (options?.status) filters.status = options.status;
    if (options?.impactLevel) filters.impactLevel = options.impactLevel;
    if (options?.dateFrom) filters.dateFrom = options.dateFrom;
    if (options?.dateTo) filters.dateTo = options.dateTo;
    if (options?.tags) filters.tags = options.tags;
    
    const { logs, count } = await ITOperationLogRepository.getAllLogs({
      skip,
      take: limit,
      order,
      filters
    });
    
    return {
      logs: logs.map(entityToFormData),
      total: count,
      page,
      limit,
      totalPages: Math.ceil(count / limit)
    };
  },
  
  // Get a single log by ID
  getLogById: async (id: string): Promise<ITOperationLogFormData> => {
    try {
      const log = await ITOperationLogRepository.getLogById(id);
      return entityToFormData(log);
    } catch (error) {
      throw new Error(`Failed to get operation log: ${(error as Error).message}`);
    }
  },
  
  // Create a new log
  createLog: async (formData: ITOperationLogFormData, currentUser?: User): Promise<ITOperationLogFormData> => {
    try {
      console.log('Creating log with current user:', currentUser?.id || 'No user provided');
      
      // If no user was provided but auth token exists, try to get user from the token
      if (!currentUser && formData.createdById) {
        console.log('Using provided createdById:', formData.createdById);
      }
      
      const entity = formDataToEntity(formData, currentUser);
      
      // Make sure we're setting the correct currentUser ID
      if (currentUser) {
        entity.createdById = currentUser.id;
        entity.lastModifiedById = currentUser.id;
      } else if (formData.createdById) {
        // Only use this as a fallback
        entity.createdById = formData.createdById;
        entity.lastModifiedById = formData.createdById;
      } else {
        // Throw an error if no user ID is available
        throw new Error('User ID is required to create a log');
      }
      
      console.log('Final entity to save:', { 
        id: entity.id,
        title: entity.title,
        createdById: entity.createdById,
        lastModifiedById: entity.lastModifiedById
      });
      
      const createdLog = await ITOperationLogRepository.createLog(entity);
      return entityToFormData(createdLog);
    } catch (error) {
      throw new Error(`Failed to create operation log: ${(error as Error).message}`);
    }
  },
  
  // Update an existing log
  updateLog: async (id: string, formData: ITOperationLogFormData, currentUser?: User): Promise<ITOperationLogFormData> => {
    try {
      const entity = formDataToEntity({ ...formData, id }, currentUser);
      const updatedLog = await ITOperationLogRepository.updateLog(id, entity);
      return entityToFormData(updatedLog);
    } catch (error) {
      throw new Error(`Failed to update operation log: ${(error as Error).message}`);
    }
  },
  
  // Delete a log
  deleteLog: async (id: string): Promise<boolean> => {
    try {
      return await ITOperationLogRepository.deleteLog(id);
    } catch (error) {
      throw new Error(`Failed to delete operation log: ${(error as Error).message}`);
    }
  },
  
  // Get statistics about logs
  getStatistics: async () => {
    try {
      return await ITOperationLogRepository.getStatistics();
    } catch (error) {
      throw new Error(`Failed to get operation log statistics: ${(error as Error).message}`);
    }
  }
};

export default ITOperationLogService; 