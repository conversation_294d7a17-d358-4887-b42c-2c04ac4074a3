import React, { useState, useRef, useEffect, useCallback, memo } from 'react';
import axios from 'axios';
import { getApiConfig, handleApiError } from '../utils/api';
import { toast } from 'react-hot-toast';
import { 
  Bold, Italic, List, Heading1, Heading2, Image as ImageIcon, 
  AlignLeft, AlignCenter, AlignRight, Link, Code, Undo, Redo,
  Upload
} from 'lucide-react';

interface RichTextEditorProps {
  onSubmit: (content: string, files: File[]) => Promise<void>;
  onTyping?: () => void;
  mentions?: Array<{ id: string; name: string }>;
  placeholder?: string;
  isDisabled?: boolean;
  minHeight?: string;
}

interface UploadedImage {
  fileName: string;
  fileUrl: string;
  fileType: string;
  size: number;
}

// Memoize toolbar buttons to prevent unnecessary re-renders
const ToolbarButton = memo(({ icon: Icon, title, onClick }: { icon: any, title: string, onClick: () => void }) => (
  <button 
    type="button" 
    onClick={onClick}
    className="p-2 rounded hover:bg-gray-200"
    title={title}
  >
    <Icon size={16} />
  </button>
));

const RichTextEditor: React.FC<RichTextEditorProps> = ({ 
  onSubmit,
  onTyping,
  mentions = [],
  placeholder = 'Start writing your content here...',
  isDisabled = false,
  minHeight = '150px'
}) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [content, setContent] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([]);

  // Handle content change
  const handleContentChange = useCallback(() => {
    if (editorRef.current) {
      const newContent = editorRef.current.innerHTML;
      setContent(newContent);
      onTyping?.();
    }
  }, [onTyping]);

  // Handle form submit
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!content.trim() && !uploadedImages.length) return;

    try {
      await onSubmit(content, []);
      // Clear content after successful submission
      if (editorRef.current) {
        editorRef.current.innerHTML = '';
        setContent('');
      }
      setUploadedImages([]);
    } catch (error) {
      console.error('Error submitting content:', error);
      toast.error('Failed to submit content');
    }
  };

  // Initialize editor with initial value
  useEffect(() => {
    if (editorRef.current && content) {
      // Only update if content is different to avoid unnecessary re-renders
      if (editorRef.current.innerHTML !== content) {
        editorRef.current.innerHTML = content;
      }
    }
  }, [content]);

  // Memoize format command execution
  const execCommand = useCallback((command: string, value: string = '') => {
    document.execCommand(command, false, value);
    handleContentChange();
    if (editorRef.current) {
      editorRef.current.focus();
    }
  }, [handleContentChange]);

  // Handle image upload
  const handleImageUpload = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    setIsUploading(true);
    setError(null);
    
    try {
      const formData = new FormData();
      
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        if (file.size > 5 * 1024 * 1024) {
          throw new Error(`File ${file.name} exceeds the 5MB size limit`);
        }
        if (!['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)) {
          throw new Error(`File ${file.name} is not a supported image type`);
        }
        formData.append('images', file);
      }
      
      const response = await axios.post('/api/knowledge/upload-images', formData, getApiConfig('multipart/form-data'));
      
      const newImages = processUploadResponse(response.data, files);
      setUploadedImages(prev => [...prev, ...newImages]);
      
      // Automatically insert the newly uploaded images
      insertImagesAtCursor(newImages);
      
      toast.success(`Successfully uploaded ${newImages.length} image(s)`);
    } catch (err: any) {
      handleApiError(err, setError);
      toast.error('Failed to upload image');
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  }, []);

  // Process upload response
  const processUploadResponse = (data: any, files: FileList): UploadedImage[] => {
    if (data.images && Array.isArray(data.images)) {
      return data.images.map((img: any) => ({
        fileName: img.fileName,
        fileUrl: makeUrlAbsolute(img.fileUrl),
        fileType: img.fileType,
        size: img.size
      }));
    }
    if (data.urls && Array.isArray(data.urls)) {
      return data.urls.map((url: string, index: number) => ({
        fileName: files[index]?.name || `Image ${index + 1}`,
        fileUrl: makeUrlAbsolute(url),
        fileType: files[index]?.type || 'image/jpeg',
        size: files[index]?.size || 0
      }));
    }
    if (typeof data === 'string') {
      return [{
        fileName: files[0]?.name || 'Uploaded Image',
        fileUrl: makeUrlAbsolute(data),
        fileType: files[0]?.type || 'image/jpeg',
        size: files[0]?.size || 0
      }];
    }
    if (data.url) {
      return [{
        fileName: files[0]?.name || 'Uploaded Image',
        fileUrl: makeUrlAbsolute(data.url),
        fileType: files[0]?.type || 'image/jpeg',
        size: files[0]?.size || 0
      }];
    }
    throw new Error('Invalid response format from server');
  };

  // Make URL absolute
  const makeUrlAbsolute = (url: string): string => {
    return url.startsWith('http') 
      ? url 
      : `${window.location.origin}${url.startsWith('/') ? '' : '/'}${url}`;
  };

  // Insert images at cursor position
  const insertImagesAtCursor = (images: UploadedImage[]) => {
    if (!editorRef.current) return;
    
    // Get current selection
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;
    
    const range = selection.getRangeAt(0);
    
    // Insert each image
    images.forEach(image => {
      const imgElement = document.createElement('img');
      imgElement.src = image.fileUrl;
      imgElement.alt = image.fileName;
      imgElement.title = image.fileName;
      imgElement.className = 'max-w-full h-auto rounded-lg shadow-md my-4';
      
      range.insertNode(imgElement);
      range.setStartAfter(imgElement);
      range.setEndAfter(imgElement);
      
      // Add a line break after the image
      const br = document.createElement('br');
      range.insertNode(br);
      range.setStartAfter(br);
      range.setEndAfter(br);
    });
    
    // Update selection
    selection.removeAllRanges();
    selection.addRange(range);
    
    // Trigger content change
    handleContentChange();
  };

  return (
    <div className="rich-text-editor border border-gray-300 rounded-md overflow-hidden">
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-300 p-2 flex flex-wrap gap-1">
        <ToolbarButton icon={Bold} title="Bold" onClick={() => execCommand('bold')} />
        <ToolbarButton icon={Italic} title="Italic" onClick={() => execCommand('italic')} />
        <ToolbarButton icon={Heading1} title="Heading 1" onClick={() => execCommand('formatBlock', '<h1>')} />
        <ToolbarButton icon={Heading2} title="Heading 2" onClick={() => execCommand('formatBlock', '<h2>')} />
        <div className="w-px h-6 bg-gray-300 mx-1 self-center"></div>
        <ToolbarButton icon={List} title="Bullet List" onClick={() => execCommand('insertUnorderedList')} />
        <ToolbarButton icon={AlignLeft} title="Align Left" onClick={() => execCommand('justifyLeft')} />
        <ToolbarButton icon={AlignCenter} title="Align Center" onClick={() => execCommand('justifyCenter')} />
        <ToolbarButton icon={AlignRight} title="Align Right" onClick={() => execCommand('justifyRight')} />
        <div className="w-px h-6 bg-gray-300 mx-1 self-center"></div>
        <ToolbarButton icon={Link} title="Insert Link" onClick={() => {
          const url = prompt('Enter URL:');
          if (url) execCommand('createLink', url);
        }} />
        <ToolbarButton 
          icon={Upload} 
          title="Upload Image" 
          onClick={() => fileInputRef.current?.click()} 
        />
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleImageUpload}
          className="hidden"
          accept="image/*"
          multiple
        />
        <ToolbarButton 
          icon={Code} 
          title="Insert Code" 
          onClick={() => execCommand('insertHTML', '<code></code>')} 
        />
        <div className="w-px h-6 bg-gray-300 mx-1 self-center"></div>
        <ToolbarButton icon={Undo} title="Undo" onClick={() => execCommand('undo')} />
        <ToolbarButton icon={Redo} title="Redo" onClick={() => execCommand('redo')} />
      </div>
      
      {/* Editor Area */}
      <div
        ref={editorRef}
        contentEditable
        className="p-4 focus:outline-none"
        style={{ minHeight }}
        onInput={handleContentChange}
        onBlur={handleContentChange}
        data-placeholder={placeholder}
      />
      
      {/* Image Preview Area */}
      {uploadedImages.length > 0 && (
        <div className="border-t border-gray-200 p-4">
          <div className="text-sm font-medium text-gray-700 mb-2">Uploaded Images</div>
          <div className="grid grid-cols-4 gap-4">
            {uploadedImages.map((image, index) => (
              <div key={index} className="relative group">
                <img
                  src={image.fileUrl}
                  alt={image.fileName}
                  className="w-full h-24 object-cover rounded-md border border-gray-200"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                  <button
                    type="button"
                    onClick={() => insertImagesAtCursor([image])}
                    className="p-1 bg-white rounded-full"
                    title="Insert image at cursor"
                  >
                    <ImageIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      
      {/* Error Message */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-2 text-sm">
          {error}
        </div>
      )}
      
      {/* Loading Indicator */}
      {isUploading && (
        <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-2 text-sm">
          Uploading image(s)...
        </div>
      )}
    </div>
  );
};

export default RichTextEditor; 