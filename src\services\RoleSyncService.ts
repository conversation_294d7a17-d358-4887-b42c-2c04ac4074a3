import { UserRole } from '../types/common';
import { AppDataSource } from '../config/database';
import { Role } from '../entities/Role';
import { UserRoleAssignment } from '../entities/UserRoleAssignment';

export class RoleSyncService {
  private static roleRepo = AppDataSource.getRepository(Role);
  private static assignmentRepo = AppDataSource.getRepository(UserRoleAssignment);

  static async syncRoles() {
    const staticRoles = Object.values(UserRole);
    
    for (const roleEnum of staticRoles) {
      const roleInfo = this.getRoleInfo(roleEnum);
      await this.ensureRoleExists(roleInfo);
    }
  }

  private static getRoleInfo(role: UserRole) {
    const roleInfoMap: Record<UserRole, { displayName: string, description: string, category: string }> = {
      [UserRole.SYSTEM_ADMIN]: {
        displayName: 'System Administrator',
        description: 'Full system access with all permissions',
        category: 'system'
      },
      [UserRole.IT_ADMIN]: {
        displayName: 'IT Administrator',
        description: 'Full IT infrastructure control',
        category: 'it'
      },
      // Add other roles...
    };

    return {
      name: role,
      ...roleInfoMap[role]
    };
  }

  private static async ensureRoleExists(roleInfo: { name: string, displayName: string, description: string, category: string }) {
    const existingRole = await this.roleRepo.findOne({ where: { name: roleInfo.name } });
    
    if (!existingRole) {
      const newRole = this.roleRepo.create({
        name: roleInfo.name,
        displayName: roleInfo.displayName,
        description: roleInfo.description,
        category: roleInfo.category
      });
      await this.roleRepo.save(newRole);
    }
  }

  static async getUserRoles(userId: string): Promise<UserRole[]> {
    const assignments = await this.assignmentRepo.find({
      where: { userId },
      relations: ['role']
    });
    
    return assignments.map(assignment => assignment.role.name as UserRole);
  }

  static async hasRole(userId: string, requiredRole: UserRole): Promise<boolean> {
    const userRoles = await this.getUserRoles(userId);
    return userRoles.includes(requiredRole);
  }
}

export const roleSyncService = new RoleSyncService();
