import { AppDataSource } from '../config/database';

async function cleanupCustomLeaveTypes() {
  try {
    console.log('🔍 Initializing database connection...');
    await AppDataSource.initialize();
    
    console.log('🧹 Starting cleanup of CUSTOM_* leave types...');
    
    // Get query runner
    const queryRunner = AppDataSource.createQueryRunner();
    
    // Start transaction
    await queryRunner.startTransaction();
    
    try {
      console.log('📋 Step 1: Updating leave_balances table...');
      const balanceResult = await queryRunner.query(`
        UPDATE leave_balances 
        SET leaveType = CASE 
            WHEN leaveType = 'CUSTOM_1' THEN 'ANNUAL_LEAVE'
            WHEN leaveType = 'CUSTOM_2' THEN 'SICK_LEAVE'
            WHEN leaveType = 'CUSTOM_3' THEN 'PERSONAL_LEAVE'
            ELSE leaveType
        END
        WHERE leaveType LIKE 'CUSTOM_%'
      `);
      console.log(`✅ Updated ${balanceResult.affectedRows || 0} leave balance records`);

      console.log('📋 Step 2: Updating leave_allocations table...');
      const allocationResult = await queryRunner.query(`
        UPDATE leave_allocations 
        SET leaveType = CASE 
            WHEN leaveType = 'CUSTOM_1' THEN 'ANNUAL_LEAVE'
            WHEN leaveType = 'CUSTOM_2' THEN 'SICK_LEAVE'
            WHEN leaveType = 'CUSTOM_3' THEN 'PERSONAL_LEAVE'
            ELSE leaveType
        END
        WHERE leaveType LIKE 'CUSTOM_%'
      `);
      console.log(`✅ Updated ${allocationResult.affectedRows || 0} leave allocation records`);

      console.log('📋 Step 3: Checking leave_requests table...');
      const requestResult = await queryRunner.query(`
        UPDATE leave_requests 
        SET leaveType = CASE 
            WHEN leaveType = 'CUSTOM_1' THEN 'ANNUAL_LEAVE'
            WHEN leaveType = 'CUSTOM_2' THEN 'SICK_LEAVE'
            WHEN leaveType = 'CUSTOM_3' THEN 'PERSONAL_LEAVE'
            ELSE leaveType
        END
        WHERE leaveType LIKE 'CUSTOM_%'
      `);
      console.log(`✅ Updated ${requestResult.affectedRows || 0} leave request records`);

      console.log('📋 Step 4: Updating leave_type_policies table...');
      const policyResult = await queryRunner.query(`
        UPDATE leave_type_policies 
        SET 
            leaveType = CASE 
                WHEN leaveType = 'CUSTOM_1' THEN 'ANNUAL_LEAVE'
                WHEN leaveType = 'CUSTOM_2' THEN 'SICK_LEAVE'
                WHEN leaveType = 'CUSTOM_3' THEN 'PERSONAL_LEAVE'
                ELSE leaveType
            END,
            displayName = CASE 
                WHEN leaveType = 'CUSTOM_1' THEN 'Annual Leave'
                WHEN leaveType = 'CUSTOM_2' THEN 'Sick Leave'
                WHEN leaveType = 'CUSTOM_3' THEN 'Personal Leave'
                ELSE displayName
            END
        WHERE leaveType LIKE 'CUSTOM_%'
      `);
      console.log(`✅ Updated ${policyResult.affectedRows || 0} leave policy records`);

      // Commit transaction
      await queryRunner.commitTransaction();
      
      console.log('📊 Getting summary of updated records...');
      
      // Show summary
      const balanceCount = await queryRunner.query(`
        SELECT COUNT(*) as count FROM leave_balances 
        WHERE leaveType IN ('ANNUAL_LEAVE', 'SICK_LEAVE', 'PERSONAL_LEAVE')
      `);
      
      const allocationCount = await queryRunner.query(`
        SELECT COUNT(*) as count FROM leave_allocations 
        WHERE leaveType IN ('ANNUAL_LEAVE', 'SICK_LEAVE', 'PERSONAL_LEAVE')
      `);
      
      const policyCount = await queryRunner.query(`
        SELECT COUNT(*) as count FROM leave_type_policies 
        WHERE leaveType IN ('ANNUAL_LEAVE', 'SICK_LEAVE', 'PERSONAL_LEAVE')
      `);

      console.log('\n🎉 CLEANUP COMPLETED SUCCESSFULLY!');
      console.log('📈 Summary:');
      console.log(`   • Leave Balances with standard types: ${balanceCount[0]?.count || 0}`);
      console.log(`   • Leave Allocations with standard types: ${allocationCount[0]?.count || 0}`);
      console.log(`   • Leave Policies with standard types: ${policyCount[0]?.count || 0}`);
      console.log('\n✨ All CUSTOM_* leave types have been converted to standard types!');
      
    } catch (error) {
      // Rollback transaction on error
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    throw error;
  } finally {
    // Close database connection
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('📪 Database connection closed');
    }
  }
}

// Run the cleanup
if (require.main === module) {
  cleanupCustomLeaveTypes()
    .then(() => {
      console.log('✅ Cleanup script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Cleanup script failed:', error);
      process.exit(1);
    });
}

export { cleanupCustomLeaveTypes }; 