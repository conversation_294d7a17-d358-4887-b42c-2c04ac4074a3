import * as fs from 'fs';
import * as path from 'path';
import PDFDocument from 'pdfkit';
import { v4 as uuidv4 } from 'uuid';

/**
 * Generate a PDF from data
 * @param data Array of objects to be displayed in the PDF
 * @param title Title of the PDF
 * @returns Path to the generated PDF file
 */
export async function generatePDF(data: any[], title: string): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      // Create a document
      const doc = new PDFDocument({ margin: 50 });
      
      // Create a unique filename
      const filename = `${uuidv4()}.pdf`;
      const outputPath = path.join(__dirname, '../../temp', filename);
      
      // Ensure temp directory exists
      const tempDir = path.join(__dirname, '../../temp');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }
      
      // Pipe its output to a file
      const stream = fs.createWriteStream(outputPath);
      doc.pipe(stream);
      
      // Add the title
      doc.fontSize(20).text(title, { align: 'center' });
      doc.moveDown();
      
      // Add the date
      doc.fontSize(12).text(`Generated on: ${new Date().toLocaleDateString()}`, { align: 'right' });
      doc.moveDown();
      
      // Add a table with the data
      if (data.length > 0) {
        // Table headers
        const headers = Object.keys(data[0]);
        const columnWidth = doc.page.width / headers.length;
        
        // Draw headers
        doc.fontSize(10);
        headers.forEach((header, i) => {
          const x = 50 + (i * columnWidth);
          doc.font('Helvetica-Bold').text(
            formatHeader(header),
            x,
            doc.y,
            { width: columnWidth, align: 'left' }
          );
        });
        
        doc.moveDown();
        doc.font('Helvetica');
        
        // Draw rows
        data.forEach((row, rowIndex) => {
          let rowHeight = 0;
          
          // Check if we need a new page
          if (doc.y > doc.page.height - 100) {
            doc.addPage();
          }
          
          const yPosition = doc.y;
          
          // Draw cells
          headers.forEach((header, i) => {
            const x = 50 + (i * columnWidth);
            const cellValue = formatCellValue(row[header]);
            
            doc.text(
              cellValue,
              x, 
              yPosition,
              { width: columnWidth, align: 'left' }
            );
            
            // Track the tallest cell in the row
            const textHeight = doc.heightOfString(cellValue, { width: columnWidth });
            if (textHeight > rowHeight) {
              rowHeight = textHeight;
            }
          });
          
          // Move to the next row position
          doc.y = yPosition + rowHeight + 5;
          
          // Add a separator line
          if (rowIndex < data.length - 1) {
            doc.moveTo(50, doc.y).lineTo(doc.page.width - 50, doc.y).stroke();
            doc.moveDown(0.5);
          }
        });
      } else {
        doc.text('No data available for the selected period.', { align: 'center' });
      }
      
      // Add total amount if available
      if (data.length > 0 && data[0].invoiceAmount) {
        doc.moveDown();
        const totalAmount = data.reduce((sum, item) => sum + Number(item.invoiceAmount), 0);
        doc.fontSize(12).font('Helvetica-Bold')
          .text(`Total Amount: $${totalAmount.toFixed(2)}`, { align: 'right' });
      }
      
      // Finalize the PDF and end the stream
      doc.end();
      
      stream.on('finish', () => {
        resolve(outputPath);
      });
      
      stream.on('error', (err) => {
        reject(err);
      });
      
    } catch (err) {
      reject(err);
    }
  });
}

/**
 * Format a header name for better display
 * @param header Header name
 * @returns Formatted header name
 */
function formatHeader(header: string): string {
  // Convert camelCase to Title Case with Spaces
  return header
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, (str) => str.toUpperCase());
}

/**
 * Format a cell value for better display
 * @param value Cell value
 * @returns Formatted cell value
 */
function formatCellValue(value: any): string {
  if (value === null || value === undefined) {
    return '';
  }
  
  if (value instanceof Date) {
    return value.toLocaleDateString();
  }
  
  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No';
  }
  
  if (typeof value === 'number') {
    if (value % 1 === 0) {
      return value.toString();
    } else {
      return value.toFixed(2);
    }
  }
  
  return value.toString();
} 