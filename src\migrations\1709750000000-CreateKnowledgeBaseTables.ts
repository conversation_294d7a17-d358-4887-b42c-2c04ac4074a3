import { MigrationInterface, QueryRunner, Table, TableForeignKey } from 'typeorm';

export class CreateKnowledgeBaseTables1709750000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create knowledge_categories table
    await queryRunner.createTable(
      new Table({
        name: 'knowledge_categories',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            length: '36',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'uuid'
          },
          {
            name: 'name',
            type: 'varchar',
            length: '100',
            isNullable: false
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true
          },
          {
            name: 'slug',
            type: 'varchar',
            length: '50',
            isNullable: false,
            isUnique: true
          },
          {
            name: 'displayOrder',
            type: 'int',
            default: 0
          },
          {
            name: 'parentId',
            type: 'varchar',
            length: '36',
            isNullable: true
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'now()'
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'now()'
          }
        ]
      }),
      true
    );

    // Add self-reference foreign key for parent-child relationship
    await queryRunner.createForeignKey(
      'knowledge_categories',
      new TableForeignKey({
        columnNames: ['parentId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'knowledge_categories',
        onDelete: 'SET NULL'
      })
    );

    // Create knowledge_base table
    await queryRunner.createTable(
      new Table({
        name: 'knowledge_base',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            length: '36',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'uuid'
          },
          {
            name: 'title',
            type: 'varchar',
            length: '255',
            isNullable: false
          },
          {
            name: 'content',
            type: 'text',
            isNullable: false
          },
          {
            name: 'summary',
            type: 'text',
            isNullable: true
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['DRAFT', 'PUBLISHED', 'ARCHIVED'],
            default: "'DRAFT'"
          },
          {
            name: 'viewCount',
            type: 'int',
            default: 0
          },
          {
            name: 'isFeatured',
            type: 'boolean',
            default: false
          },
          {
            name: 'categoryId',
            type: 'varchar',
            length: '36',
            isNullable: false
          },
          {
            name: 'createdById',
            type: 'varchar',
            length: '36',
            isNullable: false
          },
          {
            name: 'lastUpdatedById',
            type: 'varchar',
            length: '36',
            isNullable: true
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'now()'
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'now()'
          }
        ]
      }),
      true
    );

    // Add foreign keys for knowledge_base table
    await queryRunner.createForeignKey(
      'knowledge_base',
      new TableForeignKey({
        columnNames: ['categoryId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'knowledge_categories',
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'knowledge_base',
      new TableForeignKey({
        columnNames: ['createdById'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'CASCADE'
      })
    );

    await queryRunner.createForeignKey(
      'knowledge_base',
      new TableForeignKey({
        columnNames: ['lastUpdatedById'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'SET NULL'
      })
    );

    // Create knowledge_tags table
    await queryRunner.createTable(
      new Table({
        name: 'knowledge_tags',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            length: '36',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'uuid'
          },
          {
            name: 'name',
            type: 'varchar',
            length: '50',
            isNullable: false
          },
          {
            name: 'articleId',
            type: 'varchar',
            length: '36',
            isNullable: false
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'now()'
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'now()'
          }
        ]
      }),
      true
    );

    // Add foreign key for knowledge_tags table
    await queryRunner.createForeignKey(
      'knowledge_tags',
      new TableForeignKey({
        columnNames: ['articleId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'knowledge_base',
        onDelete: 'CASCADE'
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop tables in reverse order to avoid foreign key constraints
    await queryRunner.dropTable('knowledge_tags');
    await queryRunner.dropTable('knowledge_base');
    await queryRunner.dropTable('knowledge_categories');
  }
} 