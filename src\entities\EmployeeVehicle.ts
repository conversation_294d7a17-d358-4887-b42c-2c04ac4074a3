import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { Employee } from './Employee';

@Entity('employee_vehicles')
export class EmployeeVehicle {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 50,  nullable: true })
  vehicleType: string;

  @Column({ type: 'varchar', length: 20,  nullable: true })
  registrationNumber: string;

  @Column({ type: 'boolean',  nullable: true, default: false })
  providedByCompany: boolean;

  @Column({ type: 'date',  nullable: true })
  handingOverDate: string;

  @Column({ type: 'date',  nullable: true })
  returnDate: string;

  @Column({ type: 'varchar', length: 255,  nullable: true })
  vehicleMakeModel: string;

  @Column({ type: 'varchar', length: 255,  nullable: true })
  vehicleColor: string;

  @Column({ type: 'varchar', length: 255,  nullable: true })
  mileageAtIssuance: string;

  // Relation to Employee
  @ManyToOne(() => Employee, employee => employee.vehicles, { onDelete: 'CASCADE' })
  @JoinColumn()
  employee: Employee;

  // System columns
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 