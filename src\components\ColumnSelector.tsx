import React from 'react';
import { X } from 'lucide-react';

interface Column {
  id: string;
  header: string;
  accessorKey?: string;
  isVisible?: boolean;
}

interface ColumnSelectorProps {
  columns: Column[];
  onColumnToggle: (columnId: string) => void;
  onClose: () => void;
  visibleColumns: string[];
}

const ColumnSelector: React.FC<ColumnSelectorProps> = ({
  columns,
  onColumnToggle,
  onClose,
  visibleColumns,
}) => {
  return (
    <div className="absolute right-0 mt-1 w-64 bg-white rounded-lg shadow-lg z-50 border border-gray-200">
      <div className="p-2">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-sm font-semibold text-gray-900">Table Columns</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 transition-colors"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
        <div className="space-y-1 max-h-[300px] overflow-y-auto">
          {columns.map((column) => (
            <label
              key={column.id}
              className="flex items-center space-x-2 hover:bg-gray-50 px-2 py-1 rounded cursor-pointer"
            >
              <input
                type="checkbox"
                checked={visibleColumns.includes(column.id)}
                onChange={() => onColumnToggle(column.id)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-3.5 w-3.5"
              />
              <span className="text-sm text-gray-700">{column.header}</span>
            </label>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ColumnSelector; 