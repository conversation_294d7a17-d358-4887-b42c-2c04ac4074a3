-- Clean up CUSTOM_* leave types that might be causing confusion
-- Run this script to remove old CUSTOM_1, CUSTOM_2, etc. entries

-- First, update any leave balances using CUSTOM_* types to use proper leave type names
UPDATE leave_balances 
SET leaveType = CASE 
    WHEN leaveType = 'CUSTOM_1' THEN 'ANNUAL_LEAVE'
    WHEN leaveType = 'CUSTOM_2' THEN 'SICK_LEAVE'
    WHEN leaveType = 'CUSTOM_3' THEN 'PERSONAL_LEAVE'
    ELSE leaveType
END
WHERE leaveType LIKE 'CUSTOM_%';

-- Update any leave allocations using CUSTOM_* types
UPDATE leave_allocations 
SET leaveType = CASE 
    WHEN leaveType = 'CUSTOM_1' THEN 'ANNUAL_LEAVE'
    WHEN leaveType = 'CUSTOM_2' THEN 'SICK_LEAVE'
    WHEN leaveType = 'CUSTOM_3' THEN 'PERSONAL_LEAVE'
    ELSE leaveType
END
WHERE leaveType LIKE 'CUSTOM_%';

-- Update any leave requests using <PERSON>US<PERSON><PERSON>_* types
UPDATE leave_requests 
SET leaveType = CASE 
    WHEN leaveType = 'CUSTOM_1' THEN 'ANNUAL_LEAVE'
    WHEN leaveType = 'CUSTOM_2' THEN 'SICK_LEAVE'
    WHEN leaveType = 'CUSTOM_3' THEN 'PERSONAL_LEAVE'
    ELSE leaveType
END
WHERE leaveType LIKE 'CUSTOM_%';

-- Update leave type policies with proper names
UPDATE leave_type_policies 
SET 
    leaveType = CASE 
        WHEN leaveType = 'CUSTOM_1' THEN 'ANNUAL_LEAVE'
        WHEN leaveType = 'CUSTOM_2' THEN 'SICK_LEAVE'
        WHEN leaveType = 'CUSTOM_3' THEN 'PERSONAL_LEAVE'
        ELSE leaveType
    END,
    displayName = CASE 
        WHEN leaveType = 'CUSTOM_1' THEN 'Annual Leave'
        WHEN leaveType = 'CUSTOM_2' THEN 'Sick Leave'
        WHEN leaveType = 'CUSTOM_3' THEN 'Personal Leave'
        ELSE displayName
    END
WHERE leaveType LIKE 'CUSTOM_%';

-- Show summary of changes
SELECT 'Updated records summary:' as status;
SELECT COUNT(*) as leave_balances_updated FROM leave_balances WHERE leaveType IN ('ANNUAL_LEAVE', 'SICK_LEAVE', 'PERSONAL_LEAVE');
SELECT COUNT(*) as leave_allocations_updated FROM leave_allocations WHERE leaveType IN ('ANNUAL_LEAVE', 'SICK_LEAVE', 'PERSONAL_LEAVE');
SELECT COUNT(*) as leave_policies_updated FROM leave_type_policies WHERE leaveType IN ('ANNUAL_LEAVE', 'SICK_LEAVE', 'PERSONAL_LEAVE'); 