import "reflect-metadata";
import { DataSource } from "typeorm";
import { config } from "dotenv";
import { RemovePreviousEmployeeIdField1746623438584 } from "../migrations/1746623438584-RemovePreviousEmployeeIdField";

// Load environment variables
config();

// Database configuration
const AppDataSource = new DataSource({
  type: "mysql",
  host: process.env.DB_HOST || "localhost",
  port: parseInt(process.env.DB_PORT || "3306"),
  username: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "root",
  database: process.env.DB_NAME || "ims_db",
  synchronize: false,
  logging: false,
  entities: [__dirname + "/../server/entities/**/*.{js,ts}"],
  subscribers: [],
  migrations: []
});

const runMigration = async () => {
  console.log("Starting migration to remove previousEmployeeId field...");

  try {
    // Initialize the data source
    await AppDataSource.initialize();
    console.log("Database connection established");

    // Create migration instance
    const migration = new RemovePreviousEmployeeIdField1746623438584();
    
    // Run the migration
    await migration.up(AppDataSource.createQueryRunner());
    console.log("Migration completed successfully!");
    
    // Close the connection
    await AppDataSource.destroy();
    console.log("Database connection closed");
    
    process.exit(0);
  } catch (error) {
    console.error("Error running migration:", error);
    process.exit(1);
  }
};

// Run the migration
runMigration(); 