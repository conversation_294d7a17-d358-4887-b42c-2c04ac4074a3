import { Repository, DataSource } from 'typeorm';
import { LeaveRequest } from '../entities/LeaveRequest';
import { LeaveApproval, ApprovalLevel, ApprovalStatus } from '../entities/LeaveApproval';
import { User } from '../entities/User';
import { LeaveStatus } from '../types/attendance';

export class LeaveApprovalService {
  public leaveRequestRepository: Repository<LeaveRequest>;
  private leaveApprovalRepository: Repository<LeaveApproval>;
  private userRepository: Repository<User>;

  constructor(private dataSource: DataSource) {
    this.leaveRequestRepository = dataSource.getRepository(LeaveRequest);
    this.leaveApprovalRepository = dataSource.getRepository(LeaveApproval);
    this.userRepository = dataSource.getRepository(User);
  }

  /**
   * Get leave request with all approval stages (timeline view)
   */
  async getLeaveRequestWithApprovals(leaveRequestId: number): Promise<LeaveRequest | null> {
    return await this.leaveRequestRepository.findOne({
      where: { id: leaveRequestId },
      relations: ['approvals', 'approvals.approver', 'employee'],
      order: {
        approvals: {
          sequence: 'ASC'
        }
      }
    });
  }

  /**
   * Get all leave requests with their approval timelines
   */
  async getAllLeaveRequestsWithApprovals(): Promise<LeaveRequest[]> {
    return await this.leaveRequestRepository.find({
      relations: ['approvals', 'approvals.approver', 'employee'],
      order: {
        createdAt: 'DESC',
        approvals: {
          sequence: 'ASC'
        }
      }
    });
  }

  /**
   * Initialize approval workflow for a new leave request
   */
  async initializeApprovalWorkflow(leaveRequestId: number): Promise<void> {
    const approvalLevels = [
      { level: ApprovalLevel.MANAGER, sequence: 1 },
      { level: ApprovalLevel.HR, sequence: 2 }
    ];

    for (const approvalLevel of approvalLevels) {
      const approval = new LeaveApproval();
      approval.leaveRequestId = leaveRequestId;
      approval.level = approvalLevel.level;
      approval.sequence = approvalLevel.sequence;
      approval.status = ApprovalStatus.PENDING;
      
      await this.leaveApprovalRepository.save(approval);
    }
  }

  /**
   * Process approval/rejection at a specific level
   */
  async processApproval(
    leaveRequestId: number,
    level: ApprovalLevel,
    approverId: string,
    approverName: string,
    decision: 'approve' | 'reject',
    comments?: string
  ): Promise<boolean> {
    const approval = await this.leaveApprovalRepository.findOne({
      where: { 
        leaveRequestId,
        level 
      }
    });

    if (!approval) {
      throw new Error(`Approval record not found for level ${level}`);
    }

    if (approval.status !== ApprovalStatus.PENDING) {
      throw new Error(`Approval already processed for level ${level}`);
    }

    // Update approval record
    approval.approverId = approverId;
    approval.approverName = approverName;
    approval.status = decision === 'approve' ? ApprovalStatus.APPROVED : ApprovalStatus.REJECTED;
    approval.comments = comments;
    approval.decisionAt = new Date();

    await this.leaveApprovalRepository.save(approval);

    // Update main leave request status
    await this.updateLeaveRequestStatus(leaveRequestId);

    return true;
  }

  /**
   * Update leave request status based on approval stages
   */
  public async updateLeaveRequestStatus(leaveRequestId: number): Promise<void> {
    const approvals = await this.leaveApprovalRepository.find({
      where: { leaveRequestId },
      order: { sequence: 'ASC' }
    });

    const leaveRequest = await this.leaveRequestRepository.findOne({
      where: { id: leaveRequestId }
    });

    if (!leaveRequest) return;

    // Don't update status if the request is already cancelled
    if (leaveRequest.status === LeaveStatus.CANCELLED) {
      return;
    }

    // Check if any approval is rejected
    const rejectedApproval = approvals.find(a => a.status === ApprovalStatus.REJECTED);
    if (rejectedApproval) {
      // If any level is rejected, reject all subsequent levels and set final status
      for (const approval of approvals) {
        if (approval.sequence > rejectedApproval.sequence && approval.status === ApprovalStatus.PENDING) {
          approval.status = ApprovalStatus.REJECTED;
          approval.approverName = 'Auto-rejected';
          approval.comments = 'Request rejected at previous level';
          approval.decisionAt = new Date();
          await this.leaveApprovalRepository.save(approval);
        }
      }
      
      leaveRequest.status = LeaveStatus.REJECTED;
      leaveRequest.currentStage = 'REJECTED';
      await this.leaveRequestRepository.save(leaveRequest);
      return;
    }

    // Check if all approvals are approved
    const allApproved = approvals.every(a => a.status === ApprovalStatus.APPROVED);
    if (allApproved) {
      leaveRequest.status = LeaveStatus.APPROVED;
      leaveRequest.currentStage = 'COMPLETED';
      await this.leaveRequestRepository.save(leaveRequest);
      return;
    }

    // Otherwise, set currentStage to the next pending level
    const nextPending = approvals.find(a => a.status === ApprovalStatus.PENDING);
    leaveRequest.status = LeaveStatus.PENDING;
    leaveRequest.currentStage = nextPending ? `${nextPending.level}_PENDING` : 'PENDING';
    await this.leaveRequestRepository.save(leaveRequest);
  }

  /**
   * Get pending approvals for a specific approver level
   */
  async getPendingApprovalsForLevel(level: ApprovalLevel): Promise<LeaveApproval[]> {
    return await this.leaveApprovalRepository.find({
      where: { 
        level,
        status: ApprovalStatus.PENDING 
      },
      relations: ['leaveRequest', 'leaveRequest.employee'],
      order: { createdAt: 'ASC' }
    });
  }

  /**
   * Get approval history for a specific leave request
   */
  async getApprovalHistory(leaveRequestId: number): Promise<LeaveApproval[]> {
    return await this.leaveApprovalRepository.find({
      where: { leaveRequestId },
      relations: ['approver'],
      order: { sequence: 'ASC' }
    });
  }

  /**
   * Check if a specific level can approve (previous levels must be approved)
   */
  async canApproveAtLevel(leaveRequestId: number, level: ApprovalLevel): Promise<boolean> {
    const approvals = await this.leaveApprovalRepository.find({
      where: { leaveRequestId },
      order: { sequence: 'ASC' }
    });

    const currentApproval = approvals.find(a => a.level === level);
    if (!currentApproval || currentApproval.status !== ApprovalStatus.PENDING) {
      return false;
    }

    // Check if all previous levels are approved
    const currentSequence = currentApproval.sequence;
    const previousApprovals = approvals.filter(a => a.sequence < currentSequence);
    
    return previousApprovals.every(a => a.status === ApprovalStatus.APPROVED);
  }

  /**
   * Raw SQL query for complex reporting (alternative to TypeORM)
   */
  async getLeaveRequestsWithApprovalsSql(): Promise<any[]> {
    const query = `
      SELECT 
        lr.id as requestId,
        lr.employeeId,
        lr.leaveType,
        lr.startDate,
        lr.endDate,
        lr.daysRequested,
        lr.status as requestStatus,
        lr.reason,
        lr.createdAt as requestCreatedAt,
        
        la.id as approvalId,
        la.level as approvalLevel,
        la.approverId,
        la.approverName,
        la.status as approvalStatus,
        la.comments as approvalComments,
        la.decisionAt,
        la.sequence,
        
        u.name as approverFullName,
        u.email as approverEmail
        
      FROM leave_requests lr
      LEFT JOIN leave_approvals la ON lr.id = la.leaveRequestId
      LEFT JOIN users u ON la.approverId = u.id
      ORDER BY lr.createdAt DESC, la.sequence ASC
    `;

    return await this.dataSource.query(query);
  }

  /**
   * Get approval statistics
   */
  async getApprovalStatistics(): Promise<any> {
    const query = `
      SELECT 
        la.level,
        la.status,
        COUNT(*) as count,
        AVG(TIMESTAMPDIFF(HOUR, la.createdAt, la.decisionAt)) as avgHoursToDecision
      FROM leave_approvals la
      WHERE la.decisionAt IS NOT NULL
      GROUP BY la.level, la.status
      ORDER BY la.level, la.status
    `;

    return await this.dataSource.query(query);
  }

  /**
   * Cancel a leave request and update both leave_requests and leave_approvals tables
   * This method ensures consistency between the two tables
   */
  async cancelLeaveRequest(
    leaveRequestId: number, 
    cancelledBy: string, 
    reason?: string
  ): Promise<LeaveRequest> {
    // Get the leave request
    const leaveRequest = await this.leaveRequestRepository.findOne({
      where: { id: leaveRequestId }
    });

    if (!leaveRequest) {
      throw new Error(`Leave request with ID ${leaveRequestId} not found`);
    }

    // Check if already cancelled
    if (leaveRequest.status === LeaveStatus.CANCELLED) {
      throw new Error('Leave request is already cancelled');
    }

    // Store original status for balance calculations
    const originalStatus = leaveRequest.status;

    // 1. Update leave_requests table
    leaveRequest.status = LeaveStatus.CANCELLED;
    leaveRequest.currentStage = 'cancelled';
    leaveRequest.updatedAt = new Date();

    // Add to modification history if it exists
    if (leaveRequest.modificationHistory) {
      const history = JSON.parse(leaveRequest.modificationHistory);
      history.push({
        timestamp: new Date().toISOString(),
        action: 'cancel',
        actor: cancelledBy,
        reason: reason || 'Request cancelled by employee',
        previousStatus: originalStatus
      });
      leaveRequest.modificationHistory = JSON.stringify(history);
    }

    await this.leaveRequestRepository.save(leaveRequest);

    // 2. Update all pending approvals in leave_approvals table
    await this.leaveApprovalRepository.update(
      { 
        leaveRequestId,
        status: ApprovalStatus.PENDING // Only update pending approvals
      },
      { 
        status: ApprovalStatus.CANCELLED,
        comments: reason || 'Request cancelled by employee',
        decisionAt: new Date()
      }
    );

    return leaveRequest;
  }
} 