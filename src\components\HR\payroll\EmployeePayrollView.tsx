import React, { useState } from 'react';
import { 
  DollarSign, 
  FileText, 
  CreditCard, 
  Download, 
  Calendar,
  AlertCircle,
  ChevronRight
} from 'lucide-react';
import { useAuth } from '../../../contexts/AuthContext';
import ExpenseReimbursementView from './ExpenseReimbursementView';

interface Payslip {
  id: string;
  period: string;
  date: string;
  amount: string;
  status: 'Paid' | 'Processing';
  downloadUrl: string;
}

const EmployeePayrollView: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'payslips' | 'expenses'>('payslips');
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Mock payroll data
  const payrollData = {
    lastPayment: {
      date: '2023-06-30',
      amount: '520,000',
      currency: 'PKR'
    },
    ytdEarnings: '3,120,000',
    payslips: [
      { id: '1', period: 'June 2023', date: '2023-06-30', amount: '520,000', status: 'Paid', downloadUrl: '#' },
      { id: '2', period: 'May 2023', date: '2023-05-31', amount: '520,000', status: 'Paid', downloadUrl: '#' },
      { id: '3', period: 'April 2023', date: '2023-04-30', amount: '520,000', status: 'Paid', downloadUrl: '#' },
      { id: '4', period: 'March 2023', date: '2023-03-31', amount: '520,000', status: 'Paid', downloadUrl: '#' },
      { id: '5', period: 'February 2023', date: '2023-02-28', amount: '520,000', status: 'Paid', downloadUrl: '#' },
      { id: '6', period: 'January 2023', date: '2023-01-31', amount: '520,000', status: 'Paid', downloadUrl: '#' }
    ] as Payslip[]
  };

  const renderPayslipsContent = () => {
    return (
      <div>
        {/* Payroll Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-start justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Last Payment</p>
                <p className="mt-1 text-2xl font-bold text-gray-900">PKR {payrollData.lastPayment.amount}</p>
                <p className="mt-1 text-sm text-gray-500">{payrollData.lastPayment.date}</p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-start justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Year to Date</p>
                <p className="mt-1 text-2xl font-bold text-gray-900">PKR {payrollData.ytdEarnings}</p>
                <p className="mt-1 text-sm text-gray-500">Total earnings this year</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <Calendar className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-start justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Next Payment</p>
                <p className="mt-1 text-2xl font-bold text-gray-900">July 31, 2023</p>
                <p className="mt-1 text-sm text-gray-500">Estimated: PKR 520,000</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <CreditCard className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Payslips Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Payslips</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pay Period
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Payment Date
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="relative px-6 py-3">
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {payrollData.payslips.map((payslip) => (
                  <tr key={payslip.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{payslip.period}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500">{payslip.date}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">PKR {payslip.amount}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        payslip.status === 'Paid' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {payslip.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <a 
                        href={payslip.downloadUrl} 
                        className="text-blue-600 hover:text-blue-900 flex items-center justify-end"
                      >
                        <Download className="h-4 w-4 mr-1" />
                        Download
                      </a>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          <div className="px-6 py-4 border-t border-gray-200">
            <button className="text-sm text-blue-600 hover:text-blue-500 font-medium flex items-center">
              View All Payslips
              <ChevronRight className="h-4 w-4 ml-1" />
            </button>
          </div>
        </div>

        {/* Tax Information */}
        <div className="mt-8 bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Tax Information</h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <p className="text-sm font-medium text-gray-500">Tax Year</p>
                <p className="mt-1 text-base font-medium text-gray-900">2023-2024</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Tax Bracket</p>
                <p className="mt-1 text-base font-medium text-gray-900">25%</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">YTD Tax Paid</p>
                <p className="mt-1 text-base font-medium text-gray-900">PKR 780,000</p>
              </div>
            </div>
            
            <div className="mt-6">
              <button className="text-sm text-blue-600 hover:text-blue-500 font-medium flex items-center">
                Download Tax Certificate
                <Download className="h-4 w-4 ml-1" />
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="p-4">
      {/* Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="flex -mb-px">
            <button
              onClick={() => setActiveTab('payslips')}
              className={`mr-8 py-4 text-sm font-medium ${
                activeTab === 'payslips'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Payslips
            </button>
            <button
              onClick={() => setActiveTab('expenses')}
              className={`py-4 text-sm font-medium ${
                activeTab === 'expenses'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Expenses & Reimbursements
            </button>
          </nav>
        </div>
      </div>
      
      {/* Tab Content */}
      {activeTab === 'payslips' ? renderPayslipsContent() : <ExpenseReimbursementView />}
    </div>
  );
};

export default EmployeePayrollView; 