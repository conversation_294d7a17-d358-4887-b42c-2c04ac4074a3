import { AppDataSource } from '../config/database';
import { LeaveRequest } from '../entities/LeaveRequest';
import { LeaveApproval, ApprovalLevel, ApprovalStatus } from '../entities/LeaveApproval';
import { LeaveApprovalService } from '../services/LeaveApprovalService';
import { LeaveStatus } from '../types/attendance';

async function migrateExistingLeaveRequests() {
  try {
    // Initialize database connection
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ Database connected successfully');
    }

    const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
    const leaveApprovalRepository = AppDataSource.getRepository(LeaveApproval);
    const leaveApprovalService = new LeaveApprovalService(AppDataSource);

    // Get all existing leave requests that don't have approval records
    const existingRequests = await leaveRequestRepository
      .createQueryBuilder('request')
      .leftJoin('request.approvals', 'approvals')
      .where('approvals.id IS NULL')
      .getMany();

    console.log(`📋 Found ${existingRequests.length} leave requests without approval records`);

    if (existingRequests.length === 0) {
      console.log('✅ All leave requests already have approval records');
      return;
    }

    let migratedCount = 0;
    let errorCount = 0;

    for (const request of existingRequests) {
      try {
        console.log(`🔄 Processing request ID: ${request.id}`);

        // Create approval records based on current status
        const approvals: Partial<LeaveApproval>[] = [];

        // Create Manager approval record
        const managerApproval = new LeaveApproval();
        managerApproval.leaveRequestId = request.id;
        managerApproval.level = ApprovalLevel.MANAGER;
        managerApproval.sequence = 1;

        // Create HR approval record
        const hrApproval = new LeaveApproval();
        hrApproval.leaveRequestId = request.id;
        hrApproval.level = ApprovalLevel.HR;
        hrApproval.sequence = 2;

        // Set approval status based on current request status
        if (request.status === LeaveStatus.APPROVED) {
          // If request is approved, both levels are approved
          managerApproval.status = ApprovalStatus.APPROVED;
          managerApproval.approverName = 'Approved';
          managerApproval.decisionAt = new Date();
          managerApproval.comments = 'Request approved';

          hrApproval.status = ApprovalStatus.APPROVED;
          hrApproval.approverName = 'Approved';
          hrApproval.decisionAt = new Date();
          hrApproval.comments = 'Request approved';

        } else if (request.status === LeaveStatus.REJECTED) {
          // If request is rejected, first level is rejected, second is pending
          managerApproval.status = ApprovalStatus.REJECTED;
          managerApproval.approverName = 'Rejected';
          managerApproval.decisionAt = new Date();
          managerApproval.comments = 'Request rejected';

          hrApproval.status = ApprovalStatus.PENDING;

        } else {
          // If request is pending, both levels are pending
          managerApproval.status = ApprovalStatus.PENDING;
          hrApproval.status = ApprovalStatus.PENDING;
        }

        // Save approval records
        await leaveApprovalRepository.save([managerApproval, hrApproval]);

        migratedCount++;
        console.log(`✅ Successfully migrated request ID: ${request.id}`);

      } catch (error) {
        errorCount++;
        console.error(`❌ Error migrating request ID: ${request.id}`, error);
      }
    }

    console.log(`\n📊 Migration Summary:`);
    console.log(`✅ Successfully migrated: ${migratedCount} requests`);
    console.log(`❌ Errors: ${errorCount} requests`);
    console.log(`📋 Total processed: ${existingRequests.length} requests`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    // Close database connection
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the migration
migrateExistingLeaveRequests()
  .then(() => {
    console.log('🎉 Migration completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  }); 