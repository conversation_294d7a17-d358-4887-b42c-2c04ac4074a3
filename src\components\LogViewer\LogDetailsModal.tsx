import React, { useMemo } from 'react';
import { 
  X, <PERSON><PERSON>ircle, AlertTriangle, CheckCircle, Activity, 
  Clock, User, Filter, Copy, MessageSquare, LogIn, LogOut, AlertCircle
} from 'lucide-react';
import { SystemLog, LogFilterState } from './types';
import { 
  isSuccessfulLogin, 
  isFailedLogin, 
  extractLoginTimeDetails,
  extractUserLoginInfo
} from './utils/loginTracker';
import {
  formatSystemDate,
  formatDetailedSystemDate
} from './utils/dateFormat';

interface LogDetailsModalProps {
  log: SystemLog;
  onClose: () => void;
  logs: SystemLog[];
  onFilterChange: (filters: Partial<LogFilterState>) => void;
}

export const LogDetailsModal: React.FC<LogDetailsModalProps> = ({
  log,
  onClose,
  logs,
  onFilterChange
}) => {
  // Find all logs from the same user
  const userLogCount = logs.filter(l => l.user === log.user).length;
  
  // Get proper icon for log type
  const getLogTypeIcon = (type: string) => {
    switch(type) {
      case 'error':
        return <XCircle className="h-6 w-6 text-red-600 dark:text-red-400" />;
      case 'warning':
        return <AlertTriangle className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />;
      case 'success':
        return <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />;
      default:
        return <Activity className="h-6 w-6 text-blue-600 dark:text-blue-400" />;
    }
  };

  // Get badge styling based on log type
  const getLogTypeBadgeClasses = (type: string) => {
    switch(type) {
      case 'error':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 border border-red-200 dark:border-red-800/30';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-800/30';
      case 'success':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 border border-green-200 dark:border-green-800/30';
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 border border-blue-200 dark:border-blue-800/30';
    }
  };

  /**
   * Simply pass through the date string without modification
   */
  const ensureSystemTimeZone = (dateString: string): string => {
    return dateString;
  };

  // Format dates directly without conversion
  const formatDate = (dateString: string) => {
    // Split ISO format into date and time parts for display
    if (dateString.includes('T') && dateString.includes('Z')) {
      const parts = dateString.split('T');
      const datePart = parts[0]; // yyyy-mm-dd
      let timePart = parts[1].replace('Z', '');
      if (timePart.includes('.')) {
        timePart = timePart.split('.')[0]; // Remove milliseconds
      }
      return `${datePart} ${timePart}`;
    }
    return dateString;
  };

  // Format detailed date directly without conversion
  const formatDetailedDate = (dateString: string) => {
    // Split ISO format into date and time parts for display
    if (dateString.includes('T') && dateString.includes('Z')) {
      const parts = dateString.split('T');
      const datePart = parts[0]; // yyyy-mm-dd
      let timePart = parts[1].replace('Z', '');
      if (timePart.includes('.')) {
        timePart = timePart.split('.')[0]; // Remove milliseconds
      }
      return `${datePart} ${timePart}`;
    }
    return dateString;
  };

  // Copy log details to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  // Get login information for all users
  const userLoginInfo = useMemo(() => {
    return extractUserLoginInfo(logs);
  }, [logs]);
  
  // Get login information for the current user
  const currentUserLoginInfo = useMemo(() => {
    return userLoginInfo.get(log.user);
  }, [userLoginInfo, log.user]);
  
  // Check if the current log is a login event
  const isLoginLog = useMemo(() => {
    return isSuccessfulLogin(log) || isFailedLogin(log);
  }, [log]);
  
  // Extract login details if this is a login log
  const loginDetails = useMemo(() => {
    if (isLoginLog) {
      return extractLoginTimeDetails(log);
    }
    return null;
  }, [isLoginLog, log]);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 sm:p-0">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-3xl w-full max-h-[calc(100vh-2rem)] overflow-y-auto animate-scale-in dark:border dark:border-gray-700">
        <div className="sticky top-0 bg-white dark:bg-gray-800 border-b dark:border-gray-700 z-10 px-6 py-4 flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Log Details</h3>
          <button 
            onClick={onClose} 
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 dark:focus:ring-gray-600 p-1 rounded"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <div className="px-6 py-4">
          <div className="mb-6">
            <div className="flex items-center gap-3 mb-3">
              {isLoginLog ? (
                isSuccessfulLogin(log) ? 
                  <LogIn className="h-6 w-6 text-green-600 dark:text-green-400" /> :
                  <AlertCircle className="h-6 w-6 text-red-600 dark:text-red-400" />
              ) : getLogTypeIcon(log.type)}
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">{log.action}</h2>
              <span className={`px-2.5 py-0.5 text-xs font-medium rounded-full ${getLogTypeBadgeClasses(log.type)}`}>
                {log.type.charAt(0).toUpperCase() + log.type.slice(1)}
              </span>
            </div>
            <div className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-400">
              <span className="flex items-center gap-1.5">
                <Clock className="w-4 h-4" /> 
                <span className="font-mono whitespace-nowrap">{formatDetailedDate(log.timestamp)}</span>
              </span>
            </div>
          </div>
          
          {/* User Information Section */}
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-750 rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-2.5">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">User Information</h4>
              <button
                onClick={() => {
                  onFilterChange({ user: log.user });
                  onClose();
                }}
                className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1 py-1 px-2 rounded hover:bg-blue-50 dark:hover:bg-blue-900/20"
              >
                <Filter className="h-3 w-3" />
                Filter by User
              </button>
            </div>
            <div className="flex items-center gap-2">
              <User className="h-5 w-5 text-gray-400 dark:text-gray-500" />
              <span className="font-medium text-gray-800 dark:text-gray-200">{log.user}</span>
            </div>
            <div className="mt-2 text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <div>Total Actions: {userLogCount}</div>
              
              {currentUserLoginInfo && (
                <>
                  <div className="flex items-center gap-1.5">
                    <LogIn className="h-4 w-4 text-green-500" />
                    <span>System Login: {currentUserLoginInfo.lastLogin ? formatDate(currentUserLoginInfo.lastLogin) : 'N/A'}</span>
                  </div>
                  <div className="flex items-center gap-1.5">
                    <span>Total Logins: {currentUserLoginInfo.loginCount}</span>
                  </div>
                  
                  {currentUserLoginInfo.failedLoginCount > 0 && (
                    <div className="flex items-center gap-1.5 text-red-600 dark:text-red-400">
                      <AlertCircle className="h-4 w-4" />
                      <span>Failed Login Attempts: {currentUserLoginInfo.failedLoginCount}</span>
                    </div>
                  )}
                  
                  {currentUserLoginInfo.averageSessionDuration && (
                    <div className="flex items-center gap-1.5">
                      <Clock className="h-4 w-4 text-blue-500" />
                      <span>Avg. Session: {currentUserLoginInfo.averageSessionDuration} min</span>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
          
          {/* Login Details Section (if this is a login event) */}
          {isLoginLog && loginDetails && (
            <div className="mb-6">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2.5">
                Login Details
              </h4>
              <div className="p-4 bg-gray-50 dark:bg-gray-750 rounded-lg border border-gray-200 dark:border-gray-700 space-y-2 text-sm">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Status:</span>
                  <span className={isSuccessfulLogin(log) ? 'text-green-600 dark:text-green-400 font-medium' : 'text-red-600 dark:text-red-400 font-medium'}>
                    {isSuccessfulLogin(log) ? 'Successful' : 'Failed'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Login Time:</span>
                  <span className="text-gray-800 dark:text-gray-200">{formatDate(loginDetails.timestamp)}</span>
                </div>
                {loginDetails.timeZone && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Time Zone:</span>
                    <span className="text-gray-800 dark:text-gray-200 font-mono">{loginDetails.timeZone}</span>
                  </div>
                )}
                {loginDetails.ipAddress && (
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600 dark:text-gray-400">IP Address:</span>
                    <span className="text-gray-800 dark:text-gray-200 font-mono">{loginDetails.ipAddress}</span>
                  </div>
                )}
                {loginDetails.userAgent && (
                  <div className="flex flex-col gap-1">
                    <span className="text-gray-600 dark:text-gray-400">User Agent:</span>
                    <span className="text-gray-800 dark:text-gray-200 text-xs break-all">{loginDetails.userAgent}</span>
                  </div>
                )}
              </div>
            </div>
          )}
          
          {/* Login History Section (if relevant) */}
          {currentUserLoginInfo && currentUserLoginInfo.loginTimes.length > 0 && (
            <div className="mb-6">
              <div className="flex items-center justify-between mb-2.5">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Login History</h4>
              </div>
              <div className="p-4 bg-gray-50 dark:bg-gray-750 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="max-h-32 overflow-y-auto">
                  {currentUserLoginInfo.loginTimes.slice(0, 5).map((loginTime, index) => (
                    <div key={index} className="flex items-center gap-2 py-1 text-sm">
                      <LogIn className="h-3.5 w-3.5 text-green-500" />
                      <span className="text-gray-700 dark:text-gray-300">{formatDate(loginTime)}</span>
                    </div>
                  ))}
                  {currentUserLoginInfo.loginTimes.length > 5 && (
                    <div className="text-xs text-center text-gray-500 pt-2">
                      + {currentUserLoginInfo.loginTimes.length - 5} more login entries
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
          
          {/* Details Section */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-2.5">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Details</h4>
              <button
                onClick={() => copyToClipboard(log.details)}
                className="text-xs text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 flex items-center gap-1 py-1 px-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <Copy className="h-3 w-3" />
                Copy
              </button>
            </div>
            <div className="p-4 bg-gray-50 dark:bg-gray-750 rounded-lg border border-gray-200 dark:border-gray-700">
              <p className="text-gray-900 dark:text-gray-200 whitespace-pre-wrap">{log.details}</p>
            </div>
          </div>
          
          {/* ID Section */}
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2.5">Log ID</h4>
            <div className="flex items-center justify-between p-2.5 bg-gray-50 dark:bg-gray-750 rounded-lg border border-gray-200 dark:border-gray-700">
              <span className="text-gray-900 dark:text-gray-200 font-mono text-sm">{log.id}</span>
              <button 
                onClick={() => copyToClipboard(log.id)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                title="Copy ID"
              >
                <Copy className="h-3.5 w-3.5" />
              </button>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700 flex flex-wrap gap-3 justify-between">
            <div>
              <button
                onClick={() => {
                  onFilterChange({ type: log.type });
                  onClose();
                }}
                className="flex items-center gap-1.5 px-3.5 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-200 dark:hover:bg-gray-650 transition-colors"
              >
                <Filter className="h-4 w-4" />
                View All {log.type.charAt(0).toUpperCase() + log.type.slice(1)} Logs
              </button>
            </div>
            <div>
              <button
                onClick={onClose}
                className="flex items-center gap-1.5 px-3.5 py-2 text-sm bg-blue-600 dark:bg-blue-700 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors"
              >
                <X className="h-4 w-4" />
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}; 