import { MigrationInterface, QueryRunner } from 'typeorm';

export class CleanupUnusedLeaveTables1703950000000 implements MigrationInterface {
  name = 'CleanupUnusedLeaveTables1703950000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('🧹 Starting cleanup of unused leave management tables...');
    
    // Check which tables exist before trying to drop them
    const tableChecks = [
      'leave_accrual_rules',
      'leave_policies', 
      'leave_policy_settings',
      'leave_policy_versions'
    ];

    for (const tableName of tableChecks) {
      const tableExists = await queryRunner.hasTable(tableName);
      if (tableExists) {
        console.log(`📋 Dropping unused table: ${tableName}`);
        await queryRunner.dropTable(tableName);
        console.log(`✅ Successfully dropped table: ${tableName}`);
      } else {
        console.log(`⚠️  Table ${tableName} does not exist, skipping...`);
      }
    }
    
    console.log('🎉 Cleanup completed! Removed unused leave management tables.');
    console.log('📊 Remaining core tables: leave_type_policies, leave_balances, leave_allocations, leave_requests, leave_policy_configurations');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('⚠️  Rolling back table cleanup...');
    
    // Recreate basic structure for rollback (simplified versions)
    console.log('📝 Note: This rollback creates basic table structures.');
    console.log('   Original complex data and relationships cannot be fully restored.');
    
    // Recreate leave_accrual_rules table
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS leave_accrual_rules (
        id INT AUTO_INCREMENT PRIMARY KEY,
        rule_name VARCHAR(255) NOT NULL,
        leave_type VARCHAR(100) NOT NULL,
        accrual_rate DECIMAL(5,2) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Recreate leave_policies table  
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS leave_policies (
        id INT AUTO_INCREMENT PRIMARY KEY,
        policy_name VARCHAR(255) NOT NULL,
        leave_type VARCHAR(100) NOT NULL,
        default_allocation DECIMAL(10,2) DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Recreate leave_policy_settings table
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS leave_policy_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(255) NOT NULL,
        setting_value TEXT,
        policy_id INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Recreate leave_policy_versions table
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS leave_policy_versions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        version_number VARCHAR(50) NOT NULL,
        policy_data TEXT,
        is_active BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    
    console.log('🔄 Rollback completed - basic table structures recreated');
  }
} 