import { AppDataSource } from '../../config/database';
import { User } from '../../entities/User';
import { Ticket } from '../../entities/Ticket';
import { Comment } from '../../entities/Comment';
import { UserRole } from '../../types/common';
import { hash } from 'bcrypt';
import logger from '../utils/logger';

/**
 * Script to initialize database after a reset
 * Creates admin user and basic database structure
 */
const initializeDatabase = async () => {
  try {
    logger.info('Starting database initialization...');
    
    // Initialize database connection
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      logger.info('Database connection established');
    }
    
    // Create admin user if it doesn't exist
    const adminExists = await AppDataSource.getRepository(User).findOne({
      where: { 
        email: '<EMAIL>' 
      }
    });
    
    if (!adminExists) {
      logger.info('Creating admin user...');
      
      // Hash the password (using 10 salt rounds)
      const hashedPassword = await hash('admin123', 10);
      
      // Create IT Admin user
      const adminUser = new User();
      adminUser.name = 'System Admin';
      adminUser.email = '<EMAIL>';
      adminUser.password = hashedPassword;
      adminUser.role = UserRole.IT_ADMIN;
      adminUser.department = 'IT';
      adminUser.location = 'Head Office';
      adminUser.project = 'IT Management System';
      
      await AppDataSource.getRepository(User).save(adminUser);
      logger.info('Admin user created successfully');
    } else {
      logger.info('Admin user already exists');
    }
    
    // Create IT Staff user if it doesn't exist
    const staffExists = await AppDataSource.getRepository(User).findOne({
      where: { 
        email: '<EMAIL>' 
      }
    });
    
    if (!staffExists) {
      logger.info('Creating IT staff user...');
      
      // Hash the password
      const hashedPassword = await hash('staff123', 10);
      
      // Create IT Staff user
      const staffUser = new User();
      staffUser.name = 'IT Support';
      staffUser.email = '<EMAIL>';
      staffUser.password = hashedPassword;
      staffUser.role = UserRole.IT_STAFF;
      staffUser.department = 'IT';
      staffUser.location = 'Head Office';
      staffUser.project = 'IT Management System';
      
      await AppDataSource.getRepository(User).save(staffUser);
      logger.info('IT staff user created successfully');
    } else {
      logger.info('IT staff user already exists');
    }
    
    // Create a test employee user if it doesn't exist
    const employeeExists = await AppDataSource.getRepository(User).findOne({
      where: { 
        email: '<EMAIL>' 
      }
    });
    
    if (!employeeExists) {
      logger.info('Creating employee user...');
      
      // Hash the password
      const hashedPassword = await hash('employee123', 10);
      
      // Create Employee user
      const employeeUser = new User();
      employeeUser.name = 'Test Employee';
      employeeUser.email = '<EMAIL>';
      employeeUser.password = hashedPassword;
      employeeUser.role = UserRole.EMPLOYEE;
      employeeUser.department = 'HR';
      employeeUser.location = 'Head Office';
      employeeUser.project = 'IT Management System';
      
      await AppDataSource.getRepository(User).save(employeeUser);
      logger.info('Employee user created successfully');
    } else {
      logger.info('Employee user already exists');
    }
    
    logger.info('Database initialization completed successfully!');
    logger.info('You can now log in with:');
    logger.info('Admin: <EMAIL> / admin123');
    logger.info('IT Staff: <EMAIL> / staff123');
    logger.info('Employee: <EMAIL> / employee123');
    
  } catch (error) {
    logger.error('Error initializing database:', error);
    process.exit(1);
  } finally {
    // Close the database connection
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      logger.info('Database connection closed');
    }
  }
};

// Run the initialization
initializeDatabase(); 