/**
 * Debug Configuration
 * Controls what gets logged to the console
 */

// Check if we're in development mode
const isDevelopment = process.env.NODE_ENV === 'development';

// Check if debug is explicitly enabled
const isDebugEnabled = process.env.REACT_APP_DEBUG === 'true';

export const debugConfig = {
  // Show API call details (set to false for clean console)
  showApiCalls: false,
  
  // Show loading states (set to false for clean console)
  showLoadingStates: false,
  
  // Show success messages (only in development)
  showSuccessMessages: isDevelopment,
  
  // Show detailed component logs (only when explicitly enabled)
  showComponentLogs: isDebugEnabled,
  
  // Show authentication logs (only when debugging)
  showAuthLogs: isDebugEnabled,
  
  // Always show errors and warnings
  showErrors: true,
  showWarnings: true
};

export default debugConfig; 