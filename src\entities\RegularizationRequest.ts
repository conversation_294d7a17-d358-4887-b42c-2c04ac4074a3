import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyTo<PERSON>ne,
  Join<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Employee } from './Employee';

export enum RegularizationStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

@Entity('regularization_requests')
export class RegularizationRequest {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'employee_id' })
  employeeId: number;

  @ManyToOne(() => Employee)
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;

  @Column({ type: 'date' })
  date: string;

  @Column({ name: 'check_in', type: 'time', nullable: true })
  checkIn: string;

  @Column({ name: 'check_out', type: 'time', nullable: true })
  checkOut: string;

  @Column({type: 'text'})
  reason: string;

  @Column({
    type: 'enum',
    enum: RegularizationStatus,
    default: RegularizationStatus.PENDING,
  })
  status: RegularizationStatus;

  @Column({ name: 'reviewer_id', nullable: true })
  reviewerId: number;

  @ManyToOne(() => Employee, { nullable: true })
  @JoinColumn({ name: 'reviewer_id' })
  reviewer: Employee;

  @Column({ name: 'reviewer_remarks', type: 'text', nullable: true })
  reviewerRemarks: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
} 