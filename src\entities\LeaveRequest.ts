import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, ManyToOne, <PERSON>in<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { User } from './User';
import { Employee } from '../server/entities/Employee';
import { LeaveStatus } from '../types/attendance';
import { LeaveApproval } from './LeaveApproval';

@Entity('leave_requests')
export class LeaveRequest {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  employeeId: number;

  @ManyToOne(() => Employee, { eager: true })
  @JoinColumn({ name: 'employeeId' })
  employee: Employee;

  @Column({ type: 'varchar', length: 50 })
  leaveType: string; // Dynamic leave type

  @Column({ type: 'date' })
  startDate: string;

  @Column({ type: 'date' })
  endDate: string;

  @Column({ type: 'int' })
  daysRequested: number;

  @Column({
    type: 'enum',
    enum: LeaveStatus,
    default: LeaveStatus.PENDING
  })
  status: LeaveStatus;

  @Column({ type: 'text' })
  reason: string;

  // These fields are now handled by the approval workflow
  // @Column({ type: 'varchar', length: 36, nullable: true })
  // approverId?: string;

  // @ManyToOne(() => User, { nullable: true })
  // @JoinColumn({ name: 'approverId' })
  // approver?: User;

  // @Column({ type: 'varchar', length: 255, nullable: true })
  // approverName?: string;

  // @Column({ type: 'text', nullable: true })
  // approverComments?: string;

  // @Column({ type: 'datetime', nullable: true })
  // approvedAt?: Date;

  // @Column({ type: 'datetime', nullable: true })
  // rejectedAt?: Date;

  @Column({ type: 'text', nullable: true })
  attachments?: string; // JSON string of file paths

  @Column({ type: 'text', nullable: true })
  emergencyContact?: string;

  // Workflow tracking fields
  @Column({ type: 'varchar', length: 255, nullable: true })
  createdBy?: string;

  @Column({ type: 'enum', enum: ['EMPLOYEE', 'HR_ADMIN', 'MANAGER'], default: 'EMPLOYEE' })
  source: 'EMPLOYEE' | 'HR_ADMIN' | 'MANAGER'; // Source of request creation

  @Column({ type: 'text', nullable: true })
  originalStartDate?: string; // Original start date before modifications

  @Column({ type: 'text', nullable: true })
  originalEndDate?: string; // Original end date before modifications

  @Column({ type: 'text', nullable: true })
  originalReason?: string; // Original reason before modifications

  @Column({ type: 'text', nullable: true })
  modificationHistory?: string; // JSON string of modification history

  @Column({ type: 'varchar', length: 255, nullable: true })
  currentStage?: string; // Current workflow stage (manager, hr, etc.)

  @Column({ type: 'int', nullable: true })
  managerId?: number; // Manager responsible for this request

  @Column({ type: 'boolean', default: false })
  isUrgent: boolean; // Urgent request flag

  // Relations
  @OneToMany(() => LeaveApproval, approval => approval.leaveRequest, { cascade: true })
  approvals: LeaveApproval[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Virtual properties for calculated fields
  get duration(): number {
    const start = new Date(this.startDate);
    const end = new Date(this.endDate);
    const diffTime = end.getTime() - start.getTime();
    return Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1;
  }

  get isPending(): boolean {
    return this.status === LeaveStatus.PENDING;
  }

  get isApproved(): boolean {
    return this.status === LeaveStatus.APPROVED;
  }

  get isRejected(): boolean {
    return this.status === LeaveStatus.REJECTED;
  }
} 