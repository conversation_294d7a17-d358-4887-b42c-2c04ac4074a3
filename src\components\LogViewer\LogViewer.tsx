import React, { useState, useMemo, useEffect, useRef } from 'react';
import { 
  Calendar, ChevronDown, XCircle, AlertTriangle, 
  CheckCircle, Activity, Clock, User, X, 
  SortAsc, SortDesc, Settings, Moon, Sun, Shield
} from 'lucide-react';
import { SystemLog, LogViewerProps, LogFilterState } from './types';
import { LogViewerHeader } from './LogViewerHeader';
import { LogDetailsModal } from './LogDetailsModal';
import { LogFilters } from './LogFilters';
import { LogTable } from './LogTable';
import { LogCard } from './LogCard';
import { LogViewerEmpty } from './LogViewerEmpty';
import { UserActivityChart } from './UserActivityChart';
import { UserLogView } from './UserLogView';
import { UserSelectionCategory } from './UserSelectionCategory';
import { ThemeProvider, useTheme, KeyboardShortcut } from './ThemeContext';
import { ThemeSettings } from './ThemeSettings';
import { SecurityDashboard } from './SecurityDashboard';
import SystemLogApiService from '../../services/SystemLogApiService';
import { extractUserLoginInfo } from './utils/loginTracker';

// Wrap the main component with the ThemeProvider
export const LogViewer: React.FC<LogViewerProps> = (props) => {
  return (
    <ThemeProvider>
      <LogViewerWithTheme {...props} />
    </ThemeProvider>
  );
};

// Main component that uses theme context
const LogViewerWithTheme: React.FC<LogViewerProps> = ({ 
  logs, 
  onRefresh, 
  onClear, 
  onExport, 
  onAddLog
}) => {
  // Theme context
  const { theme, toggleThemeMode, registerShortcut, removeShortcut } = useTheme();

  // State
  const [selectedLog, setSelectedLog] = useState<SystemLog | null>(null);
  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');
  const [logsPerPage, setLogsPerPage] = useState(15);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [showUserCategorySelector, setShowUserCategorySelector] = useState(false);
  const [showThemeSettings, setShowThemeSettings] = useState(false);
  const [showSecurityDashboard, setShowSecurityDashboard] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [filters, setFilters] = useState<LogFilterState>({
    search: '',
    type: 'all',
    user: 'all',
    dateRange: {
      start: null,
      end: null
    },
    sortBy: 'timestamp',
    sortDirection: 'desc'
  });

  // Register global keyboard shortcuts
  useEffect(() => {
    const shortcuts: KeyboardShortcut[] = [
      {
        key: 'f',
        description: 'Focus search',
        action: () => searchInputRef.current?.focus()
      },
      {
        key: 'r',
        description: 'Refresh logs',
        action: () => onRefresh?.()
      },
      {
        key: 't',
        description: 'Toggle view mode',
        action: () => setViewMode(prev => prev === 'table' ? 'cards' : 'table')
      },
      {
        key: 'd',
        description: 'Toggle dark mode',
        action: () => toggleThemeMode()
      },
      {
        key: 's',
        description: 'Open settings',
        action: () => setShowThemeSettings(true)
      },
      {
        key: 'Escape',
        description: 'Close modal',
        action: () => {
          if (selectedLog) {
            setSelectedLog(null);
          } else if (showThemeSettings) {
            setShowThemeSettings(false);
          } else if (showSecurityDashboard) {
            setShowSecurityDashboard(false);
          }
        }
      }
    ];

    shortcuts.forEach(shortcut => registerShortcut(shortcut));

    return () => {
      shortcuts.forEach(shortcut => 
        removeShortcut(shortcut.key, shortcut.ctrl, shortcut.alt, shortcut.shift)
      );
    };
  }, [registerShortcut, removeShortcut, selectedLog, showThemeSettings, showSecurityDashboard, onRefresh, toggleThemeMode]);

  // Handlers
  const handleViewLogDetails = (log: SystemLog) => {
    setSelectedLog(log);
  };

  const handleCloseDetails = () => {
    setSelectedLog(null);
  };

  const handleExport = async () => {
    if (onExport) {
      // Use custom export function if provided
      onExport(filteredLogs);
    } else {
      try {
        // Get filter parameters for the API
        const exportOptions = {
          type: filters.type !== 'all' ? filters.type : undefined,
          user: filters.user !== 'all' ? filters.user : undefined,
          dateFrom: filters.dateRange.start ? filters.dateRange.start.toISOString().split('T')[0] : undefined,
          dateTo: filters.dateRange.end ? filters.dateRange.end.toISOString().split('T')[0] : undefined,
        };
        
        // Use API service for export
        await SystemLogApiService.exportLogs(exportOptions);
      } catch (error) {
        console.error('Error exporting logs:', error);
        // Could add a toast notification here
      }
    }
  };

  // Handle user selection from user activity chart
  const handleUserSelect = (username: string) => {
    // Switch to dedicated user view instead of just filtering
    setSelectedUser(username);
  };

  const handleBackFromUserView = () => {
    setSelectedUser(null);
  };

  const toggleUserCategorySelector = () => {
    setShowUserCategorySelector(!showUserCategorySelector);
  };

  // Filtered and sorted logs
  const filteredLogs = useMemo(() => {
    return logs.filter(log => {
      // Search filter
      const matchesSearch = !filters.search || 
        log.action.toLowerCase().includes(filters.search.toLowerCase()) ||
        log.details.toLowerCase().includes(filters.search.toLowerCase()) ||
        log.user.toLowerCase().includes(filters.search.toLowerCase());
      
      // Type filter
      const matchesType = filters.type === 'all' || log.type === filters.type;
      
      // User filter
      const matchesUser = filters.user === 'all' || log.user === filters.user;
      
      // Date range filter
      const logDate = new Date(log.timestamp);
      const matchesDateStart = !filters.dateRange.start || logDate >= filters.dateRange.start;
      const matchesDateEnd = !filters.dateRange.end || logDate <= filters.dateRange.end;
      
      return matchesSearch && matchesType && matchesUser && matchesDateStart && matchesDateEnd;
    }).sort((a, b) => {
      // Sort by selected column
      if (filters.sortBy === 'timestamp') {
        return filters.sortDirection === 'asc' 
          ? new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
          : new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
      } 
      else {
        // Need to handle the case based on valid sortBy values
        const getValueForSorting = (log: SystemLog, sortKey: 'type' | 'action' | 'user'): string => {
          return log[sortKey].toString().toLowerCase();
        };
        
        const aValue = getValueForSorting(a, filters.sortBy as 'type' | 'action' | 'user');
        const bValue = getValueForSorting(b, filters.sortBy as 'type' | 'action' | 'user');
        
        return filters.sortDirection === 'asc'
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
    });
  }, [logs, filters]);

  // Pagination
  const paginatedLogs = useMemo(() => {
    // If showing all logs, return entire filtered list
    if (logsPerPage >= 9000) {
      return filteredLogs;
    }
    
    // Otherwise paginate normally
    const startIndex = (currentPage - 1) * logsPerPage;
    return filteredLogs.slice(startIndex, startIndex + logsPerPage);
  }, [filteredLogs, currentPage, logsPerPage]);

  const totalPages = logsPerPage >= 9000 ? 1 : Math.ceil(filteredLogs.length / logsPerPage);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleFilterChange = (newFilters: Partial<LogFilterState>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1); // Reset to first page when filters change
  };

  const toggleSortDirection = () => {
    handleFilterChange({
      sortDirection: filters.sortDirection === 'asc' ? 'desc' : 'asc'
    });
  };

  const setSortBy = (column: 'timestamp' | 'type' | 'action' | 'user') => {
    if (filters.sortBy === column) {
      toggleSortDirection();
    } else {
      handleFilterChange({
        sortBy: column,
        sortDirection: 'desc'
      });
    }
  };

  // If security dashboard is active, show it
  if (showSecurityDashboard) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md p-0 overflow-hidden text-${theme.fontSize}`}>
        <SecurityDashboard 
          logs={logs} 
          onLogSelect={(logId) => {
            const log = logs.find(l => l.id === logId);
            if (log) {
              handleViewLogDetails(log);
            }
            setShowSecurityDashboard(false);
          }}
          onBack={() => setShowSecurityDashboard(false)}
        />
        
        {/* Details Modal */}
        {selectedLog && (
          <LogDetailsModal 
            log={selectedLog} 
            onClose={handleCloseDetails} 
            logs={logs}
            onFilterChange={handleFilterChange}
          />
        )}
      </div>
    );
  }

  // If we have a selected user, show the user log view
  if (selectedUser) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden text-${theme.fontSize}`}>
        <div className="px-6 py-4">
          <UserLogView 
            logs={logs}
            username={selectedUser}
            onBack={handleBackFromUserView}
            onViewLogDetails={handleViewLogDetails}
          />
        </div>
        
        {/* Details Modal */}
        {selectedLog && (
          <LogDetailsModal 
            log={selectedLog} 
            onClose={handleCloseDetails} 
            logs={logs}
            onFilterChange={handleFilterChange}
          />
        )}
      </div>
    );
  }

  return (
    <div className={`w-full rounded-lg border ${theme.mode === 'dark' ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'} overflow-hidden`}>
      <div className="p-4 space-y-4">
        {/* Header */}
        <LogViewerHeader 
          onRefresh={onRefresh}
          onExport={handleExport}
          onClear={onClear}
          onSecurityDashboard={() => setShowSecurityDashboard(true)}
          onToggleUserCategories={toggleUserCategorySelector}
          showUserCategorySelector={showUserCategorySelector}
          viewMode={viewMode}
          setViewMode={setViewMode}
        />

        <div className="bg-white dark:bg-gray-800">
          {logs.length > 0 && (
            <div className="bg-white dark:bg-gray-800 px-6 pt-4 pb-2">
              {/* User Activity Chart - only show if there are logs and not showing category selector */}
              {!showUserCategorySelector ? (
                <UserActivityChart logs={logs} onUserSelect={handleUserSelect} />
              ) : (
                <UserSelectionCategory 
                  logs={logs} 
                  onUserSelect={handleUserSelect}
                  selectedUser={null}
                />
              )}
            </div>
          )}

          {/* Filters */}
          <div className="border-t border-b border-gray-200 dark:border-gray-700 px-6 py-4 relative">
            <LogFilters 
              filters={filters}
              onFilterChange={handleFilterChange}
              logs={logs}
              searchInputRef={searchInputRef}
            />
          </div>
        </div>

        {/* Content - either table or cards */}
        <div className="px-6 py-4">
          {filteredLogs.length > 0 ? (
            <div>
              {viewMode === 'table' ? (
                <LogTable 
                  logs={paginatedLogs}
                  sortBy={filters.sortBy}
                  sortDirection={filters.sortDirection}
                  onSort={setSortBy}
                  onViewDetails={handleViewLogDetails}
                />
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {paginatedLogs.map(log => (
                    <LogCard key={log.id} log={log} onClick={() => handleViewLogDetails(log)} allLogs={logs} />
                  ))}
                </div>
              )}

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="mt-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-3 flex flex-col sm:flex-row justify-between items-center gap-3">
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300 whitespace-nowrap">
                    <span className="bg-gray-50 dark:bg-gray-700 px-2 py-1 rounded border border-gray-200 dark:border-gray-600 text-xs font-medium">
                      {(currentPage - 1) * logsPerPage + 1} - {logsPerPage === 9999 ? filteredLogs.length : Math.min(currentPage * logsPerPage, filteredLogs.length)} of {filteredLogs.length}
                    </span>
                    <div className="hidden sm:flex items-center border-l border-gray-200 dark:border-gray-700 pl-2 ml-2">
                      <span className="mr-2 text-xs font-medium">Show:</span>
                      <select
                        value={logsPerPage}
                        onChange={(e) => {
                          setLogsPerPage(Number(e.target.value));
                          setCurrentPage(1);
                        }}
                        className="border rounded-md px-2 py-1 text-xs bg-white dark:bg-gray-700 dark:border-gray-600 dark:text-white focus:ring-1 focus:ring-blue-500 focus:border-blue-500 shadow-sm"
                      >
                        <option value={10}>10</option>
                        <option value={15}>15</option>
                        <option value={25}>25</option>
                        <option value={50}>50</option>
                        <option value={100}>100</option>
                        <option value={200}>200</option>
                        <option value={500}>500</option>
                        <option value={9999}>Show All</option>
                      </select>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <button
                      disabled={currentPage === 1}
                      onClick={() => handlePageChange(1)} 
                      className="px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-30 disabled:cursor-not-allowed text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 text-xs font-medium shadow-sm"
                      title="First Page"
                    >
                      &laquo;
                    </button>
                    <button
                      disabled={currentPage === 1}
                      onClick={() => handlePageChange(currentPage - 1)}
                      className="px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-30 disabled:cursor-not-allowed text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 text-xs font-medium shadow-sm"
                    >
                      Previous
                    </button>
                    
                    <div className="flex items-center">
                      {Array.from({ length: Math.min(7, totalPages) }, (_, i) => {
                        // Logic to show pages around current page
                        let pageNum;
                        if (totalPages <= 7) {
                          pageNum = i + 1;
                        } else if (currentPage <= 4) {
                          pageNum = i + 1;
                        } else if (currentPage >= totalPages - 3) {
                          pageNum = totalPages - 6 + i;
                        } else {
                          pageNum = currentPage - 3 + i;
                        }
                        
                        return (
                          <button
                            key={i}
                            onClick={() => handlePageChange(pageNum)}
                            className={`w-7 h-7 flex items-center justify-center rounded-md mx-0.5 text-xs font-medium ${
                              currentPage === pageNum 
                                ? `bg-${theme.color}-600 dark:bg-${theme.color}-700 text-white border border-${theme.color}-600 dark:border-${theme.color}-700 shadow-sm` 
                                : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 shadow-sm'
                            }`}
                          >
                            {pageNum}
                          </button>
                        );
                      })}
                    </div>
                    
                    {totalPages > 7 && (
                      <div className="flex items-center gap-2">
                        <div className="px-2 text-gray-500">
                          <input 
                            type="number" 
                            min="1" 
                            max={totalPages} 
                            value={currentPage}
                            onChange={(e) => {
                              const page = parseInt(e.target.value);
                              if (!isNaN(page) && page >= 1 && page <= totalPages) {
                                handlePageChange(page);
                              }
                            }}
                            className="w-12 h-7 rounded-md border border-gray-200 dark:border-gray-700 text-center text-xs text-gray-700 dark:text-gray-300 dark:bg-gray-700"
                          />
                          <span className="text-xs ml-1">/ {totalPages}</span>
                        </div>
                      </div>
                    )}
                    
                    <button
                      disabled={currentPage === totalPages}
                      onClick={() => handlePageChange(currentPage + 1)}
                      className="px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-30 disabled:cursor-not-allowed text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 text-xs font-medium shadow-sm"
                    >
                      Next
                    </button>
                    <button
                      disabled={currentPage === totalPages}
                      onClick={() => handlePageChange(totalPages)} 
                      className="px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-30 disabled:cursor-not-allowed text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 text-xs font-medium shadow-sm"
                      title="Last Page"
                    >
                      &raquo;
                    </button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <LogViewerEmpty />
          )}
        </div>

        {/* Details Modal */}
        {selectedLog && (
          <LogDetailsModal 
            log={selectedLog} 
            onClose={handleCloseDetails} 
            logs={logs}
            onFilterChange={handleFilterChange}
          />
        )}
      </div>

      {/* Theme Settings Modal */}
      {showThemeSettings && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <ThemeSettings onClose={() => setShowThemeSettings(false)} />
        </div>
      )}
    </div>
  );
}; 