import React, { useState } from 'react';
import MaintenanceTable from './MaintenanceTable';
import PrinterMaintenanceForm from './PrinterMaintenanceForm';

const PrinterMaintenance: React.FC = () => {
  const [showForm, setShowForm] = useState(false);
  
  const toggleForm = () => {
    setShowForm(!showForm);
  };
  
  const handleFormClose = () => {
    setShowForm(false);
  };
  
  return (
    <div className="container mx-auto px-4 pt-3">
      {showForm ? (
        <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-4">
          <div className="bg-[#4a76cf] text-white p-4 flex items-center justify-between">
            <h1 className="text-xl font-semibold">New Maintenance Log</h1>
            <button 
              onClick={handleFormClose}
              className="text-white hover:bg-[#3a66bf] rounded-full w-7 h-7 flex items-center justify-center"
              aria-label="Close"
            >
              ✕
            </button>
          </div>
          <div className="p-4">
            <PrinterMaintenanceForm onComplete={handleFormClose} inPage={true} />
          </div>
        </div>
      ) : (
        <MaintenanceTable 
          onNewClick={toggleForm} 
          noPadding={true}
          hideHeader={false}
          suppressOuterMessage={true}
        />
      )}
    </div>
  );
};

export default PrinterMaintenance; 