import React, { useEffect, useMemo, useState } from 'react';
import { SortAsc, SortDesc, XCircle, AlertTriangle, CheckCircle, Activity, ArrowUpDown, LogIn, AlertCircle, Info, Check, ArrowRight, Shield, Star } from 'lucide-react';
import { SystemLog } from './types';
import { ResizableTable, Column, useColumnLayout } from './ResizableTable';
import { useTheme } from './ThemeContext';
import { 
  isSuccessfulLogin, 
  isFailedLogin, 
  extractUserLoginInfo 
} from './utils/loginTracker';
import { formatSystemDate } from './utils/dateFormat';
import { DateDisplay } from './DateDisplay';

interface LogTableProps {
  logs: SystemLog[];
  sortBy: 'timestamp' | 'type' | 'action' | 'user';
  sortDirection: 'asc' | 'desc';
  onSort: (column: 'timestamp' | 'type' | 'action' | 'user') => void;
  onViewDetails: (log: SystemLog) => void;
}

/**
 * Simply pass through the date string without modification
 */
const ensureSystemTimeZone = (dateString: string): string => {
  return dateString;
};

export const LogTable: React.FC<LogTableProps> = ({
  logs,
  sortBy,
  sortDirection,
  onSort,
  onViewDetails
}) => {
  const SortIcon = sortDirection === 'asc' ? SortAsc : SortDesc;
  const { theme } = useTheme();
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set());
  const [selectAllChecked, setSelectAllChecked] = useState(false);
  const [hoveredRow, setHoveredRow] = useState<string | null>(null);

  // Format dates directly without conversion
  const formatDate = (dateString: string) => {
    return dateString;
  };

  // Extract login information for all users
  const userLoginInfo = useMemo(() => {
    return extractUserLoginInfo(logs);
  }, [logs]);

  // Check if a log entry is a login
  const isLoginEvent = (log: SystemLog) => {
    return isSuccessfulLogin(log) || isFailedLogin(log);
  };

  // Handle row selection
  const toggleRowSelection = (id: string, event: React.MouseEvent) => {
    event.stopPropagation();
    setSelectedRows(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  // Handle select all rows
  const toggleSelectAll = () => {
    if (selectAllChecked) {
      setSelectedRows(new Set());
    } else {
      const allIds = logs.map(log => log.id);
      setSelectedRows(new Set(allIds));
    }
    setSelectAllChecked(!selectAllChecked);
  };

  // Update selectAllChecked when selections change
  useEffect(() => {
    setSelectAllChecked(selectedRows.size === logs.length && logs.length > 0);
  }, [selectedRows, logs]);

  // Get icon based on log type
  const getLogTypeIcon = (type: string) => {
    switch(type) {
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-amber-500" />;
      case 'success':
        return <CheckCircle className="h-5 w-5 text-emerald-500" />;
      case 'info':
        return <Info className="h-5 w-5 text-blue-500" />;
      default:
        return <Activity className="h-5 w-5 text-indigo-500" />;
    }
  };

  // Get severity level for a log
  const getLogSeverityLevel = (log: SystemLog): number => {
    if (isFailedLogin(log)) return 5;
    if (log.type === 'error') return 4;
    if (log.type === 'warning') return 3;
    if (isSuccessfulLogin(log)) return 2;
    if (log.type === 'success') return 1;
    return 0;
  };

  // Get log priority indicator
  const getLogPriorityIndicator = (log: SystemLog) => {
    const level = getLogSeverityLevel(log);
    if (level >= 4) return (
      <div title="High Priority">
        <Shield className="h-4 w-4 text-red-500" />
      </div>
    );
    if (level >= 3) return (
      <div title="Medium Priority">
        <Star className="h-4 w-4 text-amber-500" />
      </div>
    );
    return null;
  };

  // Get badge styling based on log type
  const getLogTypeBadgeClasses = (type: string) => {
    switch(type) {
      case 'error':
        return 'bg-red-50 text-red-700 border border-red-200 dark:bg-red-950/30 dark:text-red-300 dark:border-red-800/30 ring-1 ring-inset ring-red-600/10 dark:ring-red-500/20';
      case 'warning':
        return 'bg-amber-50 text-amber-700 border border-amber-200 dark:bg-amber-950/30 dark:text-amber-300 dark:border-amber-800/30 ring-1 ring-inset ring-amber-600/10 dark:ring-amber-500/20';
      case 'success':
        return 'bg-emerald-50 text-emerald-700 border border-emerald-200 dark:bg-emerald-950/30 dark:text-emerald-300 dark:border-emerald-800/30 ring-1 ring-inset ring-emerald-600/10 dark:ring-emerald-500/20';
      case 'info':
        return 'bg-blue-50 text-blue-700 border border-blue-200 dark:bg-blue-950/30 dark:text-blue-300 dark:border-blue-800/30 ring-1 ring-inset ring-blue-600/10 dark:ring-blue-500/20';
      default:
        return 'bg-indigo-50 text-indigo-700 border border-indigo-200 dark:bg-indigo-950/30 dark:text-indigo-300 dark:border-indigo-800/30 ring-1 ring-inset ring-indigo-600/10 dark:ring-indigo-500/20';
    }
  };

  // Create a sortable header for columns
  const createSortableHeader = (title: string, column: 'timestamp' | 'type' | 'action' | 'user') => (
    <div 
      className="flex items-center gap-1.5 cursor-pointer group px-2"
      onClick={() => onSort(column)}
    >
      <span className="font-semibold text-gray-800 dark:text-gray-100">{title}</span>
      {sortBy === column ? (
        <SortIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
      ) : (
        <ArrowUpDown className="h-4 w-4 text-gray-300 dark:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity" />
      )}
    </div>
  );

  // Define columns for the table
  const defaultColumns: Column[] = [
    {
      id: 'selection',
      label: (
        <div className="flex justify-center">
          <div 
            className={`h-5 w-5 rounded border ${selectAllChecked ? 'bg-blue-600 border-blue-600 dark:bg-blue-500 dark:border-blue-500' : 'border-gray-300 dark:border-gray-600'} flex items-center justify-center cursor-pointer transition-colors`}
            onClick={toggleSelectAll}
          >
            {selectAllChecked && <Check className="h-3.5 w-3.5 text-white" />}
          </div>
        </div>
      ),
      width: 50,
      minWidth: 40,
      maxWidth: 60,
      sortable: false
    },
    { 
      id: 'timestamp', 
      label: createSortableHeader('Timestamp', 'timestamp'),
      width: 170,
      minWidth: 120,
      sortable: true
    },
    { 
      id: 'type', 
      label: createSortableHeader('Event Type', 'type'),
      width: 110,
      minWidth: 90,
      sortable: true
    },
    { 
      id: 'action', 
      label: createSortableHeader('Action', 'action'),
      width: 230,
      minWidth: 180,
      sortable: true
    },
    { 
      id: 'user', 
      label: createSortableHeader('Username', 'user'),
      width: 150,
      minWidth: 100,
      sortable: true
    },
    { 
      id: 'details', 
      label: <div className="font-semibold text-gray-800 dark:text-gray-100 px-2">Details</div>,
      width: 350,
      minWidth: 200,
      sortable: false
    }
  ];

  // Use the useColumnLayout hook to manage column state
  const {
    orderedColumnIds,
    columnWidths,
    handleColumnReorder,
    handleColumnResize
  } = useColumnLayout('log-viewer-table', defaultColumns);

  // Find column by id
  const getColumnById = (id: string) => defaultColumns.find(col => col.id === id);

  return (
    <div>
      {/* Table Controls */}
      <div className="flex justify-between items-center mb-1.5 px-1">
        {selectedRows.size > 0 ? (
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {selectedRows.size} item{selectedRows.size !== 1 ? 's' : ''} selected
          </div>
        ) : (
          <div></div> /* Empty div to maintain flex spacing */
        )}
        
        {selectedRows.size > 0 && (
          <div className="flex gap-1.5">
            <button className="px-2.5 py-1 text-sm font-medium text-blue-700 bg-blue-50 rounded-md hover:bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/30">
              Export Selected
            </button>
            <button className="px-2.5 py-1 text-sm font-medium text-red-700 bg-red-50 rounded-md hover:bg-red-100 dark:bg-red-900/20 dark:text-red-400 dark:hover:bg-red-900/30">
              Delete Selected
            </button>
          </div>
        )}
      </div>
      
      {/* Main Table */}
      <div className="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 shadow-lg bg-white dark:bg-gray-850">
        <div className="overflow-x-auto">
          <table className="w-full divide-y divide-gray-200 dark:divide-gray-700 table-fixed border-collapse">
            <thead className="bg-gray-50/90 dark:bg-gray-800/90 text-gray-700 dark:text-gray-200 sticky top-0 z-10 backdrop-blur-sm shadow-sm">
              <tr>
                {defaultColumns.map((column) => (
                  <th
                    key={column.id}
                    className="px-3 py-3 whitespace-nowrap font-medium relative text-left"
                    style={{ width: column.width ? `${column.width}px` : 'auto' }}
                  >
                    {column.label}
                  </th>
                ))}
              </tr>
            </thead>
            
            <tbody className="bg-white dark:bg-gray-850 divide-y divide-gray-200 dark:divide-gray-700">
              {logs.map((log) => renderLogRow(log))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  // Helper function to render a log row
  function renderLogRow(log: SystemLog) {
    const isSelected = selectedRows.has(log.id);
    const isHovered = hoveredRow === log.id;
    const severity = getLogSeverityLevel(log);

    return (
      <tr 
        key={log.id} 
        className={`
          transition-colors duration-150 ease-in-out 
          group border-l-4 
          ${isSelected ? 'bg-blue-50/40 dark:bg-blue-950/20' : ''}
          ${
            isSuccessfulLogin(log) ? 'bg-emerald-50/40 dark:bg-emerald-950/10 border-l-emerald-500/50 hover:border-l-emerald-500' : 
            isFailedLogin(log) ? 'bg-red-50/50 dark:bg-red-950/20 border-l-red-500/70 hover:border-l-red-500' : 
            log.type === 'warning' ? `border-l-amber-500/${isHovered ? '70' : '0'} hover:border-l-amber-500` :
            log.type === 'error' ? `border-l-red-500/${isHovered ? '70' : '0'} hover:border-l-red-500` :
            log.type === 'success' ? `border-l-emerald-500/${isHovered ? '70' : '0'} hover:border-l-emerald-500` :
            log.type === 'info' ? `border-l-blue-500/${isHovered ? '70' : '0'} hover:border-l-blue-500` :
            'border-l-transparent'
          }
          ${severity >= 4 ? 'shadow-[inset_0_0_0_9999px_rgba(254,226,226,0.15)] dark:shadow-[inset_0_0_0_9999px_rgba(254,226,226,0.04)]' : ''}
          hover:shadow-[inset_0_0_0_9999px_rgba(0,0,0,0.02)] dark:hover:shadow-[inset_0_0_0_9999px_rgba(255,255,255,0.01)]
          cursor-pointer
        `}
        onClick={() => onViewDetails(log)}
        onMouseEnter={() => setHoveredRow(log.id)}
        onMouseLeave={() => setHoveredRow(null)}
      >
        {/* Selection column */}
        <td className="px-3 py-2.5 whitespace-nowrap align-middle">
          <div className="flex justify-center">
            <div 
              className={`h-5 w-5 rounded border ${isSelected ? 'bg-blue-600 border-blue-600 dark:bg-blue-500 dark:border-blue-500' : 'border-gray-300 dark:border-gray-600'} flex items-center justify-center cursor-pointer transition-colors`}
              onClick={(e) => toggleRowSelection(log.id, e)}
            >
              {isSelected && <Check className="h-3.5 w-3.5 text-white" />}
            </div>
          </div>
        </td>
        
        {/* Timestamp column */}
        <td className="px-3 py-2.5 whitespace-nowrap relative align-middle">
          <div className="pl-1 flex items-center gap-1.5">
            {getLogPriorityIndicator(log)}
            <DateDisplay 
              timestamp={log.timestamp} 
              showIcons={false} 
              className="items-start"
              variant={log.type === 'success' ? 'success' : 
                      log.type === 'error' ? 'error' : 
                      log.type === 'warning' ? 'warning' : 
                      log.type === 'info' ? 'info' : 'default'}
            />
          </div>
        </td>
        
        {/* Type column */}
        <td className="px-3 py-2.5 whitespace-nowrap align-middle">
          <div className="pl-1">
            <span className={`px-2 py-0.5 inline-flex text-xs leading-4 font-medium rounded-md ${getLogTypeBadgeClasses(log.type)}`}>
              {log.type.charAt(0).toUpperCase() + log.type.slice(1)}
            </span>
          </div>
        </td>
        
        {/* Action column */}
        <td className="px-3 py-2.5 whitespace-nowrap align-middle">
          <div className="font-medium text-gray-900 dark:text-white flex items-center pl-1">
            {isSuccessfulLogin(log) && <LogIn className="h-3.5 w-3.5 text-emerald-500 mr-1.5" />}
            {isFailedLogin(log) && <AlertCircle className="h-3.5 w-3.5 text-red-500 mr-1.5" />}
            {log.action === 'View System Logs' ? 'Accessed System Log View' : log.action}
          </div>
        </td>
        
        {/* User column */}
        <td className="px-3 py-2.5 whitespace-nowrap align-middle">
          <div className="text-gray-800 dark:text-gray-200 pl-1 font-medium flex items-center gap-1.5">
            {log.user}
            {isHovered && <ArrowRight className="h-3.5 w-3.5 text-gray-400 dark:text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity" />}
          </div>
        </td>
        
        {/* Details column */}
        <td className="px-3 py-2.5 text-gray-700 dark:text-gray-300 align-middle">
          <div className="truncate max-w-md pl-1" title={log.details}>
            {/* If details contains URL, display a shorter readable version */}
            {log.details && log.details.includes('URL: GET') ? (
              <span>
                System log view request
                {log.details.includes('limit=50') && <span> (standard page size)</span>}
                {log.details.includes('sortBy=timestamp') && <span> sorted by time</span>}
              </span>
            ) : (
              log.details
            )}
          </div>
        </td>
      </tr>
    );
  }
}; 