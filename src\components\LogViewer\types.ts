export interface SystemLog {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  action: string;
  user: string;
  timestamp: string;
  details: string;
}

export interface LogViewerProps {
  logs: SystemLog[];
  onRefresh?: () => void;
  onClear?: () => void;
  onExport?: (logs: SystemLog[]) => void;
  onAddLog?: (log: Omit<SystemLog, 'id' | 'timestamp'>) => void;
}

export interface LogFilterState {
  search: string;
  type: string;
  user: string;
  dateRange: {
    start: Date | null;
    end: Date | null;
  };
  sortBy: 'timestamp' | 'type' | 'action' | 'user';
  sortDirection: 'asc' | 'desc';
} 