import React, { useState } from 'react';
import AttendanceModule from './AttendanceModule';

const AttendancePage: React.FC = () => {
  // In a real app, this would come from authentication/user context
  const [userRole, setUserRole] = useState<'employee' | 'manager' | 'admin'>('employee');
  
  // Demo data
  const employeeId = 12345;
  const employeeName = '<PERSON>';
  
  // Function to switch between different user roles for demonstration
  const handleRoleChange = (role: 'employee' | 'manager' | 'admin') => {
    setUserRole(role);
  };
  
  return (
    <div>
      {/* Role switcher for demonstration purposes */}
      <div className="bg-gray-100 border-b border-gray-200 py-2">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-end space-x-2 text-sm">
            <span className="text-gray-500">Demo View:</span>
            <button
              onClick={() => handleRoleChange('employee')}
              className={`px-3 py-1 rounded-md ${
                userRole === 'employee' 
                  ? 'bg-blue-100 text-blue-800 font-medium' 
                  : 'hover:bg-gray-200'
              }`}
            >
              Employee
            </button>
            <button
              onClick={() => handleRoleChange('manager')}
              className={`px-3 py-1 rounded-md ${
                userRole === 'manager' 
                  ? 'bg-blue-100 text-blue-800 font-medium' 
                  : 'hover:bg-gray-200'
              }`}
            >
              Manager
            </button>
            <button
              onClick={() => handleRoleChange('admin')}
              className={`px-3 py-1 rounded-md ${
                userRole === 'admin' 
                  ? 'bg-blue-100 text-blue-800 font-medium' 
                  : 'hover:bg-gray-200'
              }`}
            >
              Admin
            </button>
          </div>
        </div>
      </div>
      
      {/* Render the attendance module with the selected role */}
      <AttendanceModule 
        employeeId={employeeId} 
        employeeName={employeeName} 
        userRole={userRole} 
      />
    </div>
  );
};

export default AttendancePage; 