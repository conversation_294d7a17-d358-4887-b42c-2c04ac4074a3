import { MigrationInterface, QueryRunner } from "typeorm";

export class AddFatherNameField1746000000020 implements MigrationInterface {
    name = 'AddFatherNameField1746000000020'

    public async up(queryRunner: QueryRunner): Promise<void> {
        try {
            // Check if the column already exists
            const columnExists = await queryRunner.query(
                `SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
                 WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'employees' 
                 AND COLUMN_NAME = 'fatherName'`
            );

            if (columnExists.length === 0) {
                // Add the fatherName column to employees table
                await queryRunner.query(`ALTER TABLE \`employees\` ADD \`fatherName\` varchar(255) NULL`);
                console.log("Successfully added fatherName column to employees table");
            } else {
                console.log("fatherName column already exists, skipping modification");
            }
        } catch (error) {
            console.error("Error in AddFather<PERSON><PERSON><PERSON>ield migration:", error);
            throw error;
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        try {
            // Check if the column exists before dropping
            const columnExists = await queryRunner.query(
                `SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
                 WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'employees' 
                 AND COLUMN_NAME = 'fatherName'`
            );

            if (columnExists.length > 0) {
                await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`fatherName\``);
                console.log("Successfully dropped fatherName column from employees table");
            }
        } catch (error) {
            console.error("Error in reverting AddFatherNameField migration:", error);
            throw error;
        }
    }
} 