import React from 'react';
import { Search, Filter, RotateCcw } from 'lucide-react';

interface FilterOption {
  value: string;
  label: string;
  icon?: string;
}

interface FilterConfig {
  key: string;
  label: string;
  type: 'select' | 'search' | 'date';
  options?: FilterOption[];
  placeholder?: string;
}

interface LeaveFiltersProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  statusFilter: string;
  setStatusFilter: (value: string) => void;
  departmentFilter: string;
  setDepartmentFilter: (value: string) => void;
  leaveTypeFilter: string;
  setLeaveTypeFilter: (value: string) => void;
  dateRangeFilter: string;
  setDateRangeFilter: (value: string) => void;
  startDateFilter: string;
  setStartDateFilter: (value: string) => void;
  endDateFilter: string;
  setEndDateFilter: (value: string) => void;
  priorityFilter: string;
  setPriorityFilter: (value: string) => void;
  positionFilter?: string;
  setPositionFilter?: (value: string) => void;
  balanceStatusFilter?: string;
  setBalanceStatusFilter?: (value: string) => void;
  usageFilter?: string;
  setUsageFilter?: (value: string) => void;
  departments: string[];
  positions?: string[];
  leaveTypes?: FilterOption[];
  statuses?: FilterOption[];
  priorities?: FilterOption[];
  dateRanges?: FilterOption[];
  filteredCount: number;
  totalCount: number;
  filterType: 'requests' | 'employees' | 'allocations';
  onResetFilters: () => void;
}

const departmentNames: Record<string, string> = {
  'IT': 'Information Technology',
  'HR': 'Human Resources',
  'FINANCE': 'Finance & Accounting',
  'MARKETING': 'Marketing & Communications',
  'SALES': 'Sales',
  'DIGITAL SALES': 'Digital Sales',
  'OPERATIONS': 'Operations',
  'CSD': 'Customer Service Department',
  'LAND': 'Land Department',
  'LEGAL': 'Legal',
  'MANAGEMENT': 'Management',
  'PND': 'Planning and Development'
};

const LeaveFilters: React.FC<LeaveFiltersProps> = ({
  searchTerm,
  setSearchTerm,
  statusFilter,
  setStatusFilter,
  departmentFilter,
  setDepartmentFilter,
  leaveTypeFilter,
  setLeaveTypeFilter,
  dateRangeFilter,
  setDateRangeFilter,
  startDateFilter,
  setStartDateFilter,
  endDateFilter,
  setEndDateFilter,
  priorityFilter,
  setPriorityFilter,
  positionFilter,
  setPositionFilter,
  balanceStatusFilter,
  setBalanceStatusFilter,
  usageFilter,
  setUsageFilter,
  departments,
  positions = [],
  leaveTypes = [],
  statuses = [],
  priorities = [],
  dateRanges = [],
  filteredCount,
  totalCount,
  filterType,
  onResetFilters
}) => {
  // Dynamic filter configurations
  const getFilterConfigs = (): FilterConfig[] => {
    const baseConfigs: FilterConfig[] = [
      {
        key: 'search',
        label: 'Search',
        type: 'search',
        placeholder: filterType === 'requests' ? 'Employee name or reason...' : 'Name, department, position...'
      }
    ];

    if (filterType === 'requests') {
      return [
        ...baseConfigs,
        {
          key: 'department',
          label: 'Department',
          type: 'select',
          options: [
            { value: 'all', label: 'All Departments' },
            ...departments.map(dept => ({ 
              value: dept, 
              label: departmentNames[dept] || dept 
            }))
          ]
        },
        {
          key: 'leaveType',
          label: 'Leave Type',
          type: 'select',
          options: [
            { value: 'all', label: 'All Types' },
            ...leaveTypes
          ]
        },
        {
          key: 'status',
          label: 'Status',
          type: 'select',
          options: [
            { value: 'all', label: 'All Status' },
            ...statuses
          ]
        },
        {
          key: 'priority',
          label: 'Priority',
          type: 'select',
          options: [
            { value: 'all', label: 'All Priority' },
            ...priorities
          ]
        },
        {
          key: 'dateRange',
          label: 'Date Range',
          type: 'select',
          options: [
            { value: 'all', label: 'All Dates' },
            ...dateRanges
          ]
        }
      ];
    }

    if (filterType === 'employees') {
      return [
        ...baseConfigs,
        {
          key: 'department',
          label: 'Department',
          type: 'select',
          options: [
            { value: 'all', label: 'All Departments' },
            ...departments.map(dept => ({ 
              value: dept, 
              label: departmentNames[dept] || dept 
            }))
          ]
        },
        {
          key: 'position',
          label: 'Position',
          type: 'select',
          options: [
            { value: 'all', label: 'All Positions' },
            ...positions.map(pos => ({ value: pos, label: pos }))
          ]
        },
        {
          key: 'balanceStatus',
          label: 'Balance Status',
          type: 'select',
          options: [
            { value: 'all', label: 'All Status' },
            { value: 'critical', label: '🔴 Critical (0 days)' },
            { value: 'low', label: '🟡 Low (1-5 days)' },
            { value: 'good', label: '🟢 Good (5+ days)' }
          ]
        },
        {
          key: 'usage',
          label: 'Usage',
          type: 'select',
          options: [
            { value: 'all', label: 'All Usage' },
            { value: 'high', label: '🔴 High (15+ days)' },
            { value: 'medium', label: '🟡 Medium (5-15 days)' },
            { value: 'low', label: '🟢 Low (<5 days)' }
          ]
        }
      ];
    }

    return baseConfigs;
  };

  const getFilterValue = (key: string): string => {
    switch (key) {
      case 'search': return searchTerm;
      case 'department': return departmentFilter;
      case 'leaveType': return leaveTypeFilter;
      case 'status': return statusFilter;
      case 'priority': return priorityFilter;
      case 'dateRange': return dateRangeFilter;
      case 'position': return positionFilter || 'all';
      case 'balanceStatus': return balanceStatusFilter || 'all';
      case 'usage': return usageFilter || 'all';
      default: return '';
    }
  };

  const setFilterValue = (key: string, value: string) => {
    switch (key) {
      case 'search': setSearchTerm(value); break;
      case 'department': setDepartmentFilter(value); break;
      case 'leaveType': setLeaveTypeFilter(value); break;
      case 'status': setStatusFilter(value); break;
      case 'priority': setPriorityFilter(value); break;
      case 'dateRange': setDateRangeFilter(value); break;
      case 'position': setPositionFilter?.(value); break;
      case 'balanceStatus': setBalanceStatusFilter?.(value); break;
      case 'usage': setUsageFilter?.(value); break;
    }
  };

  const renderFilterField = (config: FilterConfig) => {
    const value = getFilterValue(config.key);
    const setValue = (newValue: string) => setFilterValue(config.key, newValue);

    if (config.type === 'search') {
      return (
      <div className="flex-1">
          <label className="block text-xs font-medium text-gray-700 mb-1">{config.label}</label>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
              placeholder={config.placeholder}
              value={value}
              onChange={(e) => setValue(e.target.value)}
            className="pl-10 pr-4 py-1.5 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm transition-colors"
          />
        </div>
      </div>
      );
    }

    if (config.type === 'select') {
      return (
        <div className="flex-1">
          <label className="block text-xs font-medium text-gray-700 mb-1">{config.label}</label>
          <select
            value={value}
            onChange={(e) => setValue(e.target.value)}
            className="w-full px-2 py-1.5 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white transition-colors"
          >
            {config.options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.icon ? `${option.icon} ` : ''}{option.label}
              </option>
            ))}
          </select>
    </div>
  );
    }

    return null;
  };

  const filterConfigs = getFilterConfigs();

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <h3 className="text-sm font-medium text-gray-700">Filter & Search Options</h3>
        </div>
        <button
          onClick={onResetFilters}
          className="flex items-center gap-1 px-3 py-1 text-xs text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors"
        >
          <RotateCcw className="h-3 w-3" />
          Reset All
        </button>
      </div>

      <div className="flex flex-col lg:flex-row lg:items-end lg:space-x-2 space-y-2 lg:space-y-0">
        {filterConfigs.map((config) => (
          <React.Fragment key={config.key}>
            {renderFilterField(config)}
          </React.Fragment>
        ))}
          </div>

      <div className="mt-3 text-xs text-gray-500">
        Showing {filteredCount} of {totalCount} {filterType === 'requests' ? 'requests' : 'employees'}
      </div>
    </div>
  );
};

export default LeaveFilters; 