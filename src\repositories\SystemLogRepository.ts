import { SystemLog, LogType } from '../entities/SystemLog';
import { AppDataSource } from '../config/database';
import { User } from '../entities/User';

interface GetAllLogsOptions {
  skip?: number;
  take?: number;
  order?: {
    [key: string]: 'ASC' | 'DESC';
  };
  filters?: {
    search?: string;
    type?: string;
    user?: string;
    dateFrom?: string;
    dateTo?: string;
  };
  includeAnonymous?: boolean;
  onlyValidUsers?: boolean;
}

export const SystemLogRepository = {
  // Get all system logs with filtering and pagination
  getAllLogs: async (options: GetAllLogsOptions = {}) => {
    const { 
      skip = 0, 
      take = 50, 
      order = { timestamp: 'DESC' }, 
      filters = {},
      includeAnonymous = true,
      onlyValidUsers = false
    } = options;
    
    const queryBuilder = AppDataSource.getRepository(SystemLog)
      .createQueryBuilder('log')
      .orderBy(
        Object.keys(order).map(key => `log.${key}`)[0],
        Object.values(order)[0]
      );
    
    if (!includeAnonymous) {
      queryBuilder.andWhere('log.user NOT IN (:...anonymousUsers)', { 
        anonymousUsers: ['anonymous', 'unknown', 'Anonymous', 'Unknown', 'System', 'system'] 
      });
    }
    
    if (onlyValidUsers) {
      queryBuilder
        .leftJoin(User, 'user', 'log.user = user.name')
        .andWhere(`(
          user.id IS NOT NULL OR 
          log.action LIKE '%Login Failed%' OR 
          log.action LIKE '%Authentication Failed%' OR
          log.action LIKE '%Login Attempt%' OR
          log.action LIKE '%Failed Login%'
        )`);
    }
    
    if (take < 9000) {
      queryBuilder.skip(skip);
      queryBuilder.take(take);
    } else {
      console.log('Retrieving ALL logs without pagination limits');
    }
    
    if (filters.search) {
      queryBuilder.andWhere(
        '(log.action LIKE :search OR log.user LIKE :search OR log.details LIKE :search)',
        { search: `%${filters.search}%` }
      );
    }
    
    if (filters.type && filters.type !== 'all') {
      queryBuilder.andWhere('log.type = :type', { type: filters.type });
    }
    
    if (filters.user && filters.user !== 'all') {
      queryBuilder.andWhere('log.user = :user', { user: filters.user });
    }
    
    if (filters.dateFrom) {
      queryBuilder.andWhere('log.timestamp >= :dateFrom', { 
        dateFrom: new Date(filters.dateFrom) 
      });
    }
    
    if (filters.dateTo) {
      queryBuilder.andWhere('log.timestamp <= :dateTo', { 
        dateTo: new Date(filters.dateTo + 'T23:59:59.999Z') 
      });
    }
    
    try {
      const [logs, count] = await queryBuilder.getManyAndCount();
      console.log(`Retrieved ${logs.length} logs out of ${count} total logs`);
      return { logs, count };
    } catch (error) {
      console.error('Error retrieving logs:', error);
      return { logs: [], count: 0 };
    }
  },
  
  // Get a single log by ID
  getLogById: async (id: string): Promise<SystemLog> => {
    const log = await AppDataSource.getRepository(SystemLog).findOne({ where: { id } });
    
    if (!log) {
      throw new Error(`System log with ID ${id} not found`);
    }
    
    return log;
  },
  
  // Create a new log
  createLog: async (logData: Partial<SystemLog>): Promise<SystemLog> => {
    const repository = AppDataSource.getRepository(SystemLog);
    const newLog = repository.create(logData);
    return await repository.save(newLog);
  },
  
  // Update an existing log
  updateLog: async (id: string, logData: Partial<SystemLog>): Promise<SystemLog> => {
    const repository = AppDataSource.getRepository(SystemLog);
    const log = await SystemLogRepository.getLogById(id);
    
    repository.merge(log, logData);
    return await repository.save(log);
  },
  
  // Delete a log
  deleteLog: async (id: string): Promise<boolean> => {
    const repository = AppDataSource.getRepository(SystemLog);
    const result = await repository.delete(id);
    
    if (result.affected === 0) {
      throw new Error(`System log with ID ${id} not found`);
    }
    
    return true;
  },
  
  // Delete logs for users that don't exist in the database
  deleteInvalidUserLogs: async (): Promise<number> => {
    const repository = AppDataSource.getRepository(SystemLog);
    
    const subQuery = AppDataSource.getRepository(User)
      .createQueryBuilder('user')
      .select('user.name');
    
    const result = await repository
      .createQueryBuilder()
      .delete()
      .from(SystemLog)
      .where(`user NOT IN (${subQuery.getQuery()})`)
      .andWhere(`action NOT LIKE '%Login Failed%'`)
      .andWhere(`action NOT LIKE '%Authentication Failed%'`)
      .andWhere(`action NOT LIKE '%Login Attempt%'`)
      .andWhere(`action NOT LIKE '%Failed Login%'`)
      .andWhere('user NOT IN (:...systemUsers)', { 
        systemUsers: ['System', 'system'] 
      })
      .execute();
    
    console.log(`Deleted ${result.affected} logs from invalid users`);
    return result.affected || 0;
  },
  
  // Delete all anonymous logs
  deleteAnonymousLogs: async (): Promise<number> => {
    const repository = AppDataSource.getRepository(SystemLog);
    
    const result = await repository
      .createQueryBuilder()
      .delete()
      .from(SystemLog)
      .where('user IN (:...anonymousUsers)', { 
        anonymousUsers: ['anonymous', 'unknown', 'Anonymous', 'Unknown', 'System', 'system']
      })
      .execute();
    
    console.log(`Deleted ${result.affected} anonymous logs`);
    return result.affected || 0;
  },
  
  // Clear all logs
  clearLogs: async (): Promise<boolean> => {
    await AppDataSource.getRepository(SystemLog).clear();
    return true;
  },
  
  // Get statistics about logs
  getStatistics: async () => {
    const repository = AppDataSource.getRepository(SystemLog);
    
    // Get count by type
    const typeStats = await Promise.all(
      Object.values(LogType).map(async (type) => {
        const count = await repository.count({ where: { type } });
        return { type, count };
      })
    );
    
    // Get most active users (top 5)
    const userStats = await repository
      .createQueryBuilder('log')
      .select('log.user, COUNT(log.id) as count')
      .groupBy('log.user')
      .orderBy('count', 'DESC')
      .limit(5)
      .getRawMany();
    
    // Count logs by day (last 7 days)
    const today = new Date();
    const lastWeek = new Date(today);
    lastWeek.setDate(lastWeek.getDate() - 7);
    
    const dailyStats = await repository
      .createQueryBuilder('log')
      .select('DATE(log.timestamp) as date, COUNT(log.id) as count')
      .where('log.timestamp >= :lastWeek', { lastWeek })
      .groupBy('DATE(log.timestamp)')
      .orderBy('date', 'ASC')
      .getRawMany();
    
    return {
      total: await repository.count(),
      byType: typeStats,
      byUser: userStats,
      byDay: dailyStats
    };
  }
}; 