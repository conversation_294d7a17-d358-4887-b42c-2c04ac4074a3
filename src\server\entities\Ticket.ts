import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { User } from './User';
import { Comment } from './Comment';
import { TicketStatus, TicketPriority } from '../types/common';
import { Attachment } from './Attachment';

@Entity('tickets')
export class Ticket {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'varchar', length: 255, unique: true })
  ticketNumber!: string;

  @Column({ type: 'varchar', length: 255 })
  title!: string;

  @Column({ type: 'text' })
  description!: string;

  @Column({
    type: 'enum',
    enum: TicketStatus,
    default: TicketStatus.OPEN
  })
  status!: TicketStatus;

  @Column({
    type: 'enum',
    enum: TicketPriority,
    default: TicketPriority.MEDIUM
  })
  priority!: TicketPriority;

  @Column({ type: 'varchar', length: 255 })
  category!: string;

  @Column({ type: 'varchar', length: 255 })
  department: string;

  @Column({ type: 'json' })
  departmentChain!: string[];

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  @ManyToOne(() => User, user => user.createdTickets, { nullable: false })
  @JoinColumn({ name: 'createdById' })
  createdBy!: User;

  @Column({ type: 'uuid' })
  createdById!: string;

  @ManyToOne(() => User, user => user.assignedTickets, { nullable: true })
  @JoinColumn({ name: 'assignedToId' })
  assignedTo!: User | null;

  @Column({ type: 'uuid', nullable: true })
  assignedToId!: string | null;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'lockedById' })
  lockedBy!: User | null;

  @Column({ type: 'uuid', nullable: true })
  lockedById!: string | null;

  @Column({ type: 'timestamp', nullable: true })
  lockedAt!: Date | null;

  @OneToMany(() => Comment, comment => comment.ticket, { 
    cascade: true,
    onDelete: 'CASCADE'
  })
  comments!: Comment[];

  @OneToMany(() => Attachment, attachment => attachment.ticket, {
    cascade: true,
    onDelete: 'CASCADE'
  })
  attachments!: Attachment[];

  @Column({ type: 'json' })
  visibleTo!: string[];
} 