import React, { useState, useEffect } from 'react';
import { 
  Calendar, 
  Plus, 
  Search, 
  Filter, 
  ChevronRight, 
  ChevronDown,
  Check,
  X,
  AlertCircle,
  DollarSign,
  Download,
  Clock,
  Edit,
  Trash2,
  FileText
} from 'lucide-react';
import { PayrollPeriod, PayrollStatus, PayrollFrequency } from '../../../types/payroll';

interface PayrollPeriodsProps {
  isAdmin?: boolean;
}

const PayrollPeriods: React.FC<PayrollPeriodsProps> = ({ isAdmin = false }) => {
  const [loading, setLoading] = useState(true);
  const [payrollPeriods, setPayrollPeriods] = useState<PayrollPeriod[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [currentPeriod, setCurrentPeriod] = useState<PayrollPeriod | null>(null);
  const [filters, setFilters] = useState({
    status: '',
    search: '',
    dateRange: { start: '', end: '' },
  });

  // Fetch payroll periods (simulated)
  useEffect(() => {
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      const mockPayrollPeriods: PayrollPeriod[] = [
        {
          id: 1,
          name: 'January 2023',
          startDate: '2023-01-01',
          endDate: '2023-01-31',
          paymentDate: '2023-02-05',
          frequency: PayrollFrequency.MONTHLY,
          status: PayrollStatus.COMPLETED,
          totalEmployees: 45,
          totalAmount: 1875000,
          createdAt: '2023-01-25T10:00:00Z',
          createdBy: 1
        },
        {
          id: 2,
          name: 'February 2023',
          startDate: '2023-02-01',
          endDate: '2023-02-28',
          paymentDate: '2023-03-05',
          frequency: PayrollFrequency.MONTHLY,
          status: PayrollStatus.COMPLETED,
          totalEmployees: 47,
          totalAmount: 1920000,
          createdAt: '2023-02-25T10:00:00Z',
          createdBy: 1
        },
        {
          id: 3,
          name: 'March 2023',
          startDate: '2023-03-01',
          endDate: '2023-03-31',
          paymentDate: '2023-04-05',
          frequency: PayrollFrequency.MONTHLY,
          status: PayrollStatus.COMPLETED,
          totalEmployees: 48,
          totalAmount: 1950000,
          createdAt: '2023-03-25T10:00:00Z',
          createdBy: 1
        },
        {
          id: 4,
          name: 'April 2023',
          startDate: '2023-04-01',
          endDate: '2023-04-30',
          paymentDate: '2023-05-05',
          frequency: PayrollFrequency.MONTHLY,
          status: PayrollStatus.COMPLETED,
          totalEmployees: 50,
          totalAmount: 2050000,
          createdAt: '2023-04-25T10:00:00Z',
          createdBy: 1
        },
        {
          id: 5,
          name: 'May 2023',
          startDate: '2023-05-01',
          endDate: '2023-05-31',
          paymentDate: '2023-06-05',
          frequency: PayrollFrequency.MONTHLY,
          status: PayrollStatus.APPROVED,
          totalEmployees: 52,
          totalAmount: 2150000,
          createdAt: '2023-05-25T10:00:00Z',
          createdBy: 1
        },
        {
          id: 6,
          name: 'June 2023',
          startDate: '2023-06-01',
          endDate: '2023-06-30',
          paymentDate: '2023-07-05',
          frequency: PayrollFrequency.MONTHLY,
          status: PayrollStatus.PENDING,
          totalEmployees: 52,
          totalAmount: 2150000,
          createdAt: '2023-06-25T10:00:00Z',
          createdBy: 1
        },
        {
          id: 7,
          name: 'Current Month',
          startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
          endDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).toISOString().split('T')[0],
          paymentDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 5).toISOString().split('T')[0],
          frequency: PayrollFrequency.MONTHLY,
          status: PayrollStatus.DRAFT,
          totalEmployees: 53,
          createdAt: new Date().toISOString(),
          createdBy: 1
        }
      ];
      
      setPayrollPeriods(mockPayrollPeriods);
      setLoading(false);
    }, 1000);
  }, []);

  const handleCreatePeriod = () => {
    // Get next month
    const today = new Date();
    const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
    const lastDayOfNextMonth = new Date(nextMonth.getFullYear(), nextMonth.getMonth() + 1, 0);
    const paymentDate = new Date(lastDayOfNextMonth.getFullYear(), lastDayOfNextMonth.getMonth(), lastDayOfNextMonth.getDate() + 5);
    
    setCurrentPeriod({
      id: 0,
      name: nextMonth.toLocaleString('default', { month: 'long' }) + ' ' + nextMonth.getFullYear(),
      startDate: nextMonth.toISOString().split('T')[0],
      endDate: lastDayOfNextMonth.toISOString().split('T')[0],
      paymentDate: paymentDate.toISOString().split('T')[0],
      frequency: PayrollFrequency.MONTHLY,
      status: PayrollStatus.DRAFT,
      createdAt: new Date().toISOString(),
      createdBy: 1
    });
    
    setShowModal(true);
  };

  const handleEditPeriod = (period: PayrollPeriod) => {
    setCurrentPeriod({...period});
    setShowModal(true);
  };

  const handleSavePeriod = () => {
    if (!currentPeriod) return;
    
    if (currentPeriod.id === 0) {
      // Add new period
      const newId = Math.max(...payrollPeriods.map(p => p.id)) + 1;
      setPayrollPeriods([...payrollPeriods, {...currentPeriod, id: newId}]);
    } else {
      // Update existing period
      setPayrollPeriods(payrollPeriods.map(p => 
        p.id === currentPeriod.id ? currentPeriod : p
      ));
    }
    
    setShowModal(false);
    setCurrentPeriod(null);
  };

  const handleDeletePeriod = (id: number) => {
    if (window.confirm('Are you sure you want to delete this payroll period?')) {
      setPayrollPeriods(payrollPeriods.filter(p => p.id !== id));
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    if (!currentPeriod) return;
    
    const { name, value } = e.target;
    setCurrentPeriod({
      ...currentPeriod,
      [name]: value
    });
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFilters({
        ...filters,
        [parent]: {
          ...filters[parent as keyof typeof filters] as any,
          [child]: value
        }
      });
    } else {
      setFilters({
        ...filters,
        [name]: value
      });
    }
  };

  const clearFilters = () => {
    setFilters({
      status: '',
      search: '',
      dateRange: { start: '', end: '' },
    });
  };

  const filteredPayrollPeriods = payrollPeriods.filter(period => {
    // Filter by status
    if (filters.status && period.status !== filters.status) {
      return false;
    }
    
    // Filter by search term
    if (filters.search && 
        !period.name.toLowerCase().includes(filters.search.toLowerCase())) {
      return false;
    }
    
    // Filter by date range - start
    if (filters.dateRange.start && period.startDate < filters.dateRange.start) {
      return false;
    }
    
    // Filter by date range - end
    if (filters.dateRange.end && period.endDate > filters.dateRange.end) {
      return false;
    }
    
    return true;
  });

  const getStatusBadgeClass = (status: PayrollStatus) => {
    switch (status) {
      case PayrollStatus.DRAFT:
        return 'bg-gray-100 text-gray-800';
      case PayrollStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800';
      case PayrollStatus.PROCESSING:
        return 'bg-blue-100 text-blue-800';
      case PayrollStatus.APPROVED:
        return 'bg-green-100 text-green-800';
      case PayrollStatus.COMPLETED:
        return 'bg-indigo-100 text-indigo-800';
      case PayrollStatus.REJECTED:
        return 'bg-red-100 text-red-800';
      case PayrollStatus.ON_HOLD:
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number = 0) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'PKR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Payroll Periods</h3>
          <p className="text-sm text-gray-500">Manage your payroll cycles and periods</p>
        </div>
        
        {/* Actions */}
        <div className="flex flex-wrap gap-2">
          {isAdmin && (
            <button
              onClick={handleCreatePeriod}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Period
            </button>
          )}
          
          <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
        </div>
      </div>
      
      {/* Filters */}
      <div className="mb-6 p-4 bg-gray-50 rounded-md border border-gray-200">
        <div className="flex flex-wrap gap-4 items-end">
          <div>
            <label htmlFor="search" className="block text-xs font-medium text-gray-700 mb-1">Search</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                id="search"
                name="search"
                value={filters.search}
                onChange={handleFilterChange}
                placeholder="Search payroll periods"
                className="pl-10 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="status" className="block text-xs font-medium text-gray-700 mb-1">Status</label>
            <select
              id="status"
              name="status"
              value={filters.status}
              onChange={handleFilterChange}
              className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
            >
              <option value="">All Statuses</option>
              <option value={PayrollStatus.DRAFT}>Draft</option>
              <option value={PayrollStatus.PENDING}>Pending</option>
              <option value={PayrollStatus.PROCESSING}>Processing</option>
              <option value={PayrollStatus.APPROVED}>Approved</option>
              <option value={PayrollStatus.COMPLETED}>Completed</option>
              <option value={PayrollStatus.REJECTED}>Rejected</option>
              <option value={PayrollStatus.ON_HOLD}>On Hold</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="dateRange.start" className="block text-xs font-medium text-gray-700 mb-1">From Date</label>
            <input
              type="date"
              id="dateRange.start"
              name="dateRange.start"
              value={filters.dateRange.start}
              onChange={handleFilterChange}
              className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <label htmlFor="dateRange.end" className="block text-xs font-medium text-gray-700 mb-1">To Date</label>
            <input
              type="date"
              id="dateRange.end"
              name="dateRange.end"
              value={filters.dateRange.end}
              onChange={handleFilterChange}
              className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <button 
              onClick={clearFilters}
              className="px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>
      
      {/* Payroll Periods Table */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <>
          {filteredPayrollPeriods.length === 0 ? (
            <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
              <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No payroll periods found</h3>
              <p className="text-gray-500 max-w-md mx-auto mb-6">
                {filters.search || filters.status || filters.dateRange.start || filters.dateRange.end ? 
                  'Try adjusting your filters to see more results.' : 
                  'Create your first payroll period to start managing employee payments.'}
              </p>
              {isAdmin && (
                <button
                  onClick={handleCreatePeriod}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Payroll Period
                </button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Period Name
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date Range
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Payment Date
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Employees
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total Amount
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredPayrollPeriods.map((period) => (
                    <tr key={period.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                            <Calendar className="h-5 w-5" />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{period.name}</div>
                            <div className="text-xs text-gray-500">{period.frequency}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(period.startDate).toLocaleDateString()} - {new Date(period.endDate).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(period.paymentDate).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(period.status)}`}>
                          {period.status.charAt(0).toUpperCase() + period.status.slice(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {period.totalEmployees || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                        {period.totalAmount ? formatCurrency(period.totalAmount) : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <button 
                            className="text-indigo-600 hover:text-indigo-900"
                            title="View Payroll Details"
                          >
                            <FileText className="h-4 w-4" />
                          </button>
                          
                          {isAdmin && period.status !== PayrollStatus.COMPLETED && (
                            <>
                              <button 
                                onClick={() => handleEditPeriod(period)}
                                className="text-blue-600 hover:text-blue-900"
                                title="Edit Period"
                              >
                                <Edit className="h-4 w-4" />
                              </button>
                              
                              {period.status === PayrollStatus.DRAFT && (
                                <button 
                                  onClick={() => handleDeletePeriod(period.id)}
                                  className="text-red-600 hover:text-red-900"
                                  title="Delete Period"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              )}
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </>
      )}
      
      {/* Create/Edit Payroll Period Modal */}
      {showModal && currentPeriod && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                {currentPeriod.id === 0 ? 'Create Payroll Period' : 'Edit Payroll Period'}
              </h3>
              <button
                onClick={() => {
                  setShowModal(false);
                  setCurrentPeriod(null);
                }}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Period Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={currentPeriod.name}
                  onChange={handleInputChange}
                  className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
              
              <div>
                <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Start Date
                </label>
                <input
                  type="date"
                  id="startDate"
                  name="startDate"
                  value={currentPeriod.startDate}
                  onChange={handleInputChange}
                  className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
              
              <div>
                <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
                  End Date
                </label>
                <input
                  type="date"
                  id="endDate"
                  name="endDate"
                  value={currentPeriod.endDate}
                  onChange={handleInputChange}
                  className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
              
              <div>
                <label htmlFor="paymentDate" className="block text-sm font-medium text-gray-700 mb-1">
                  Payment Date
                </label>
                <input
                  type="date"
                  id="paymentDate"
                  name="paymentDate"
                  value={currentPeriod.paymentDate}
                  onChange={handleInputChange}
                  className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
              
              <div>
                <label htmlFor="frequency" className="block text-sm font-medium text-gray-700 mb-1">
                  Frequency
                </label>
                <select
                  id="frequency"
                  name="frequency"
                  value={currentPeriod.frequency}
                  onChange={handleInputChange}
                  className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                >
                  <option value={PayrollFrequency.MONTHLY}>Monthly</option>
                  <option value={PayrollFrequency.BIWEEKLY}>Bi-Weekly</option>
                  <option value={PayrollFrequency.WEEKLY}>Weekly</option>
                  <option value={PayrollFrequency.DAILY}>Daily</option>
                  <option value={PayrollFrequency.CUSTOM}>Custom</option>
                </select>
              </div>
              
              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  id="status"
                  name="status"
                  value={currentPeriod.status}
                  onChange={handleInputChange}
                  className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                >
                  <option value={PayrollStatus.DRAFT}>Draft</option>
                  <option value={PayrollStatus.PENDING}>Pending</option>
                  <option value={PayrollStatus.PROCESSING}>Processing</option>
                  <option value={PayrollStatus.APPROVED}>Approved</option>
                  <option value={PayrollStatus.COMPLETED}>Completed</option>
                  <option value={PayrollStatus.REJECTED}>Rejected</option>
                  <option value={PayrollStatus.ON_HOLD}>On Hold</option>
                </select>
              </div>
            </div>
            
            <div className="mt-5 flex justify-end">
              <button
                type="button"
                onClick={() => {
                  setShowModal(false);
                  setCurrentPeriod(null);
                }}
                className="mr-3 inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSavePeriod}
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PayrollPeriods; 