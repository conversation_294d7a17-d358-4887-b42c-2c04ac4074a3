import { AppDataSource } from '../config/database';
import { KnowledgeCategory } from '../entities/KnowledgeCategory';

// These are the exact categories from the image
const exactCategories = [
  {
    name: 'Company Profile & IT Overview',
    description: 'Information about the company and IT department overview',
    slug: 'company-profile-it-overview',
    displayOrder: 1
  },
  {
    name: 'Email & Communication Tools',
    description: 'Information about email services and communication tools used in the organization',
    slug: 'email-communication-tools',
    displayOrder: 2
  },
  {
    name: 'Getting Started',
    description: 'Basic information for new employees and getting started with IT resources',
    slug: 'getting-started',
    displayOrder: 3
  },
  {
    name: 'Getting Started with IT Services',
    description: 'Detailed guides on how to use various IT services offered by the organization',
    slug: 'getting-started-it-services',
    displayOrder: 4
  },
  {
    name: 'Hardware & Device Support',
    description: 'Information about hardware devices and support procedures',
    slug: 'hardware-device-support',
    displayOrder: 5
  },
  {
    name: 'IT Helpdesk & Support Requests',
    description: 'How to contact IT helpdesk and submit support requests',
    slug: 'it-helpdesk-support-requests',
    displayOrder: 6
  },
  {
    name: 'IT Policies & Security Guidelines',
    description: 'Official IT policies and security guidelines for the organization',
    slug: 'it-policies-security-guidelines',
    displayOrder: 7
  },
  {
    name: 'IT Purchase Policy & Asset Management',
    description: 'Information about IT purchasing procedures and asset management',
    slug: 'it-purchase-policy-asset-management',
    displayOrder: 8
  },
  {
    name: 'Internet, Wi-Fi & Network Access',
    description: 'Information about internet connectivity, Wi-Fi access, and network resources',
    slug: 'internet-wifi-network-access',
    displayOrder: 9
  },
  {
    name: 'IT Security & Cybersecurity Awareness',
    description: 'Information about IT security practices and cybersecurity awareness',
    slug: 'it-security-cybersecurity-awareness',
    displayOrder: 10
  },
  {
    name: 'Software & Application Support',
    description: 'Information about software applications and support procedures',
    slug: 'software-application-support',
    displayOrder: 11
  },
  {
    name: 'Technology Knowledge',
    description: 'General technology knowledge and educational resources',
    slug: 'technology-knowledge',
    displayOrder: 12
  },
  {
    name: 'Troubleshooting Common IT Issues',
    description: 'Guides for troubleshooting common IT problems',
    slug: 'troubleshooting-common-it-issues',
    displayOrder: 13
  }
];

async function createExactCategories() {
  try {
    // Initialize the data source
    await AppDataSource.initialize();
    console.log('Database connection initialized');

    const categoryRepository = AppDataSource.getRepository(KnowledgeCategory);
    
    // Get existing categories
    const existingCategories = await categoryRepository.find();
    console.log(`Found ${existingCategories.length} existing categories`);
    
    // Get all existing category slugs
    const existingSlugs = existingCategories.map(cat => cat.slug);
    
    // Get all desired category slugs
    const desiredSlugs = exactCategories.map(cat => cat.slug);
    
    // Find categories to delete (those that exist but aren't in our desired list)
    const categoriesToDelete = existingCategories.filter(cat => !desiredSlugs.includes(cat.slug));
    
    // Delete unwanted categories
    if (categoriesToDelete.length > 0) {
      console.log(`Deleting ${categoriesToDelete.length} unwanted categories...`);
      for (const category of categoriesToDelete) {
        try {
          await categoryRepository.remove(category);
          console.log(`Deleted category: ${category.name}`);
        } catch (err) {
          console.error(`Error deleting category ${category.name}:`, err);
        }
      }
    }
    
    // Update or create desired categories
    for (const categoryData of exactCategories) {
      try {
        // Check if category already exists by slug
        const existingCategory = existingCategories.find(cat => cat.slug === categoryData.slug);
        
        if (existingCategory) {
          // Update existing category
          existingCategory.name = categoryData.name;
          existingCategory.description = categoryData.description;
          existingCategory.displayOrder = categoryData.displayOrder;
          
          await categoryRepository.save(existingCategory);
          console.log(`Updated category: ${existingCategory.name}`);
        } else {
          // Create new category
          const category = new KnowledgeCategory();
          category.name = categoryData.name;
          category.description = categoryData.description;
          category.slug = categoryData.slug;
          category.displayOrder = categoryData.displayOrder;
          
          await categoryRepository.save(category);
          console.log(`Created category: ${category.name}`);
        }
      } catch (err) {
        console.error(`Error processing category ${categoryData.name}:`, err);
      }
    }
    
    console.log('Categories have been updated to match exactly what was in the image');

  } catch (error) {
    console.error('Error updating categories:', error);
  } finally {
    // Close the connection
    await AppDataSource.destroy();
    console.log('Database connection closed');
  }
}

// Run the script
createExactCategories(); 