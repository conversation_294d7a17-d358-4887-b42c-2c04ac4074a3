import { ShiftAssignment } from '../components/attendance/ShiftAssignment';

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

class ShiftAssignmentService {
  private baseUrl = '/api/shift-assignments';
  private apiAvailable: boolean | null = null; // Cache API availability

  // Check if API is available (without showing errors)
  private async checkApiAvailability(): Promise<boolean> {
    if (this.apiAvailable !== null) {
      return this.apiAvailable;
    }

    try {
      const response = await fetch(`${this.baseUrl}`, { 
        method: 'HEAD',
        signal: AbortSignal.timeout(2000) // 2 second timeout
      });
      this.apiAvailable = response.ok;
      return this.apiAvailable;
    } catch (error) {
      // API not available, use localStorage only
      this.apiAvailable = false;
      return false;
    }
  }

  // Get all shift assignments - localStorage first, API fallback
  async getShiftAssignments(): Promise<ApiResponse<ShiftAssignment[]>> {
    try {
      // Always try localStorage first
      const localData = await this.getFromLocalStorage();
      
      // If we have local data, return it immediately
      if (localData.length > 0) {
        console.log('✅ ShiftAssignments: Using localStorage data');
        return {
          success: true,
          data: localData
        };
      }

      // Only try API if we have no local data and API is available
      const isApiAvailable = await this.checkApiAvailability();
      if (isApiAvailable) {
        try {
          const response = await fetch(`${this.baseUrl}`);
          if (response.ok) {
            const data = await response.json();
            if (data.success && data.data) {
              // Save to localStorage for future use
              await this.saveToLocalStorage(data.data);
              return data;
            }
          }
        } catch (error) {
          // API call failed, but we don't show errors
        }
      }

      // Return empty array if no data available
      return {
        success: true,
        data: []
      };
    } catch (error) {
      return {
        success: true,
        data: []
      };
    }
  }

  // Get shift assignments for a specific employee
  async getEmployeeShiftAssignments(employeeId: number): Promise<ApiResponse<ShiftAssignment[]>> {
    try {
      const response = await fetch(`${this.baseUrl}/employees/${employeeId}`);
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching employee shift assignments:', error);
      return {
        success: false,
        error: 'Failed to fetch employee shift assignments'
      };
    }
  }

  // Create shift assignments - localStorage primary
  async createShiftAssignments(assignments: ShiftAssignment[]): Promise<ApiResponse<ShiftAssignment[]>> {
    try {
      // Save to localStorage first (primary storage)
      const existingData = await this.getFromLocalStorage();
      const newData = [...existingData, ...assignments];
      await this.saveToLocalStorage(newData);

      // Try to sync with API if available (secondary)
      const isApiAvailable = await this.checkApiAvailability();
      if (isApiAvailable) {
        try {
          const response = await fetch(`${this.baseUrl}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ assignments }),
          });
          if (response.ok) {
            const data = await response.json();
            return data;
          }
        } catch (error) {
          // API sync failed, but localStorage save succeeded
        }
      }

      return {
        success: true,
        data: assignments
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to create shift assignments'
      };
    }
  }

  // Update a shift assignment
  async updateShiftAssignment(id: number, assignment: Partial<ShiftAssignment>): Promise<ApiResponse<ShiftAssignment>> {
    try {
      // Update in localStorage first
      const data = await this.getFromLocalStorage();
      const index = data.findIndex(a => a.id === id);
      if (index !== -1) {
        data[index] = { ...data[index], ...assignment };
        await this.saveToLocalStorage(data);
      }

      // Try API sync if available
      const isApiAvailable = await this.checkApiAvailability();
      if (isApiAvailable) {
        try {
          const response = await fetch(`${this.baseUrl}/${id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(assignment),
          });
          if (response.ok) {
            const data = await response.json();
            return data;
          }
        } catch (error) {
          // API sync failed, but localStorage update succeeded
        }
      }

      return {
        success: true,
        data: data[index] as ShiftAssignment
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to update shift assignment'
      };
    }
  }

  // Delete a shift assignment
  async deleteShiftAssignment(id: number): Promise<ApiResponse<void>> {
    try {
      // Delete from localStorage first
      const data = await this.getFromLocalStorage();
      const filteredData = data.filter(a => a.id !== id);
      await this.saveToLocalStorage(filteredData);

      // Try API sync if available
      const isApiAvailable = await this.checkApiAvailability();
      if (isApiAvailable) {
        try {
          const response = await fetch(`${this.baseUrl}/${id}`, {
            method: 'DELETE',
          });
          if (response.ok) {
            const data = await response.json();
            return data;
          }
        } catch (error) {
          // API sync failed, but localStorage delete succeeded
        }
      }

      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to delete shift assignment'
      };
    }
  }

  // Get shift assignments for a date range
  async getShiftAssignmentsForDateRange(startDate: string, endDate: string): Promise<ApiResponse<ShiftAssignment[]>> {
    try {
      const response = await fetch(`${this.baseUrl}/assignments?startDate=${startDate}&endDate=${endDate}`);
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching shift assignments for date range:', error);
      return {
        success: false,
        error: 'Failed to fetch shift assignments for date range'
      };
    }
  }

  // Fallback to localStorage for now if API is not available
  private getLocalStorageKey() {
    return 'shift_assignments';
  }

  async saveToLocalStorage(assignments: ShiftAssignment[]): Promise<void> {
    try {
      localStorage.setItem(this.getLocalStorageKey(), JSON.stringify(assignments));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  }

  async getFromLocalStorage(): Promise<ShiftAssignment[]> {
    try {
      const data = localStorage.getItem(this.getLocalStorageKey());
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Error reading from localStorage:', error);
      return [];
    }
  }

  async clearLocalStorage(): Promise<void> {
    try {
      localStorage.removeItem(this.getLocalStorageKey());
    } catch (error) {
      console.error('Error clearing localStorage:', error);
    }
  }
}

export default new ShiftAssignmentService(); 