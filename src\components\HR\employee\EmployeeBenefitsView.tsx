import React, { useState, useEffect } from 'react';
import { 
  Heart, 
  Shield, 
  DollarSign, 
  Calendar, 
  PlusCircle, 
  AlertCircle, 
  Clipboard, 
  User,
  HelpCircle,
  Phone,
  ExternalLink,
  Table,
  Briefcase,
  Activity,
  Gift,
  Clock,
  Package,
  Mail
} from 'lucide-react';
import { useAuth } from '../../../contexts/AuthContext';

// Benefit type definitions
interface Benefit {
  id: string;
  name: string;
  category: 'health' | 'financial' | 'wellness' | 'leave' | 'insurance' | 'perks';
  description: string;
  coverage: string;
  effectiveDate: Date;
  status: 'active' | 'pending' | 'inactive';
  planDetails?: string;
  contactInfo?: string;
  documents?: string[];
}

interface Dependent {
  id: string;
  name: string;
  relationship: string;
  dateOfBirth: Date;
  coverageType: string[];
}

const EmployeeBenefitsView: React.FC = () => {
  const { user } = useAuth();
  const [benefits, setBenefits] = useState<Benefit[]>([]);
  const [dependents, setDependents] = useState<Dependent[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'health' | 'financial' | 'dependents'>('overview');
  const [showBenefitDetails, setShowBenefitDetails] = useState<boolean>(false);
  const [selectedBenefit, setSelectedBenefit] = useState<Benefit | null>(null);

  // Fetch benefits data
  useEffect(() => {
    const fetchBenefitsData = async () => {
      try {
        setIsLoading(true);
        // Mock API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Sample benefits data
        const mockBenefits: Benefit[] = [
          {
            id: '1',
            name: 'Company Health Insurance',
            category: 'health',
            description: 'Comprehensive health insurance including medical, dental, and vision coverage.',
            coverage: 'Employee + Family',
            effectiveDate: new Date(2022, 0, 1),
            status: 'active',
            planDetails: 'Premium Plan - PKR 50,000 deductible',
            contactInfo: 'HealthCare Inc. - 1-************'
          },
          {
            id: '2',
            name: '401(k) Retirement Plan',
            category: 'financial',
            description: 'Company-matched retirement savings plan with investment options.',
            coverage: '5% employer match',
            effectiveDate: new Date(2022, 0, 1),
            status: 'active',
            planDetails: 'Vested after 3 years of service'
          },
          {
            id: '3',
            name: 'Life Insurance',
            category: 'insurance',
            description: 'Term life insurance policy provided by the company.',
            coverage: '2x Annual Salary',
            effectiveDate: new Date(2022, 0, 1),
            status: 'active'
          },
          {
            id: '4',
            name: 'Employee Assistance Program',
            category: 'wellness',
            description: 'Confidential counseling and support services for personal and work-related issues.',
            coverage: 'Full Coverage',
            effectiveDate: new Date(2022, 0, 1),
            status: 'active',
            contactInfo: 'EAP Helpline - 1-888-555-6789'
          },
          {
            id: '5',
            name: 'Gym Membership',
            category: 'wellness',
            description: 'Subsidized gym membership to promote physical fitness.',
            coverage: '50% reimbursement',
            effectiveDate: new Date(2022, 3, 15),
            status: 'active'
          },
          {
            id: '6',
            name: 'Parental Leave',
            category: 'leave',
            description: 'Paid time off for new parents.',
            coverage: '12 weeks paid',
            effectiveDate: new Date(2022, 0, 1),
            status: 'active'
          },
          {
            id: '7',
            name: 'Commuter Benefits',
            category: 'perks',
            description: 'Pre-tax deductions for commuting expenses.',
            coverage: 'Up to PKR 27,000/month',
            effectiveDate: new Date(2022, 0, 1),
            status: 'active'
          }
        ];

        // Sample dependents data
        const mockDependents: Dependent[] = [
          {
            id: '1',
            name: 'Jane Doe',
            relationship: 'Spouse',
            dateOfBirth: new Date(1985, 5, 15),
            coverageType: ['Health', 'Dental', 'Vision']
          },
          {
            id: '2',
            name: 'John Doe Jr.',
            relationship: 'Child',
            dateOfBirth: new Date(2010, 8, 22),
            coverageType: ['Health', 'Dental', 'Vision']
          }
        ];

        setBenefits(mockBenefits);
        setDependents(mockDependents);
        setIsLoading(false);
      } catch (error) {
        setError('Failed to load benefits data. Please try again later.');
        setIsLoading(false);
      }
    };

    fetchBenefitsData();
  }, []);

  // Filter benefits based on active tab
  const filteredBenefits = benefits.filter(benefit => {
    if (activeTab === 'overview') return true;
    if (activeTab === 'health') return benefit.category === 'health' || benefit.category === 'insurance' || benefit.category === 'wellness';
    if (activeTab === 'financial') return benefit.category === 'financial';
    return true;
  });

  // Format date for display
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle benefit selection
  const handleBenefitClick = (benefit: Benefit) => {
    setSelectedBenefit(benefit);
    setShowBenefitDetails(true);
  };

  // Get benefit icon based on category
  const getBenefitIcon = (category: Benefit['category']) => {
    switch (category) {
      case 'health':
        return <Activity className="h-10 w-10 text-green-500" />;
      case 'financial':
        return <DollarSign className="h-10 w-10 text-blue-500" />;
      case 'wellness':
        return <Heart className="h-10 w-10 text-pink-500" />;
      case 'leave':
        return <Calendar className="h-10 w-10 text-purple-500" />;
      case 'insurance':
        return <Shield className="h-10 w-10 text-red-500" />;
      case 'perks':
        return <Gift className="h-10 w-10 text-yellow-500" />;
      default:
        return <Package className="h-10 w-10 text-gray-500" />;
    }
  };

  // Benefit Detail Modal
  const renderBenefitDetailModal = () => {
    if (!showBenefitDetails || !selectedBenefit) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[90vh] overflow-y-auto">
          <div className="flex justify-between items-start mb-6">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                {getBenefitIcon(selectedBenefit.category)}
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-semibold text-gray-900">{selectedBenefit.name}</h3>
                <div className="mt-1">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 capitalize">
                    {selectedBenefit.status}
                  </span>
                  <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 capitalize">
                    {selectedBenefit.category}
                  </span>
                </div>
              </div>
            </div>
            <button 
              onClick={() => setShowBenefitDetails(false)}
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
            >
              <span className="sr-only">Close</span>
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <p className="text-gray-700">{selectedBenefit.description}</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-500 mb-1">Coverage</h4>
              <p className="text-gray-900 font-medium">{selectedBenefit.coverage}</p>
            </div>
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-500 mb-1">Effective Date</h4>
              <p className="text-gray-900 font-medium">{formatDate(selectedBenefit.effectiveDate)}</p>
            </div>
            {selectedBenefit.planDetails && (
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-500 mb-1">Plan Details</h4>
                <p className="text-gray-900 font-medium">{selectedBenefit.planDetails}</p>
              </div>
            )}
            {selectedBenefit.contactInfo && (
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-500 mb-1">Contact Information</h4>
                <p className="text-gray-900 font-medium">{selectedBenefit.contactInfo}</p>
              </div>
            )}
          </div>
          
          <div className="flex justify-between">
            <button
              onClick={() => setShowBenefitDetails(false)}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              Close
            </button>
            <button
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              View Full Plan Details
            </button>
          </div>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="p-4 flex justify-center items-center h-64">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
          <p className="text-gray-600">Loading benefits information...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
          <div className="flex items-center">
            <AlertCircle className="h-6 w-6 text-red-500 mr-2" />
            <p className="text-red-700">{error}</p>
          </div>
          <button 
            className="mt-2 bg-red-100 text-red-700 px-4 py-2 rounded hover:bg-red-200"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-900">My Benefits</h2>
        <div className="flex items-center space-x-2">
          <button className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 flex items-center">
            <Clipboard className="h-4 w-4 mr-2" />
            Benefits Summary
          </button>
          <button className="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-50 rounded-md hover:bg-gray-100 flex items-center">
            <HelpCircle className="h-4 w-4 mr-2" />
            Help
          </button>
        </div>
      </div>
      
      {/* Benefits Overview Card */}
      <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg p-6 mb-6 text-white">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-xl font-bold mb-2">Benefits Summary</h3>
            <p className="mb-4 opacity-90">Your comprehensive benefits package is valued at approximately:</p>
            <div className="text-3xl font-bold">PKR 2,450,000</div>
            <p className="text-sm opacity-80 mt-1">Annual value in addition to your base salary</p>
          </div>
          <div className="bg-white bg-opacity-20 p-4 rounded-lg">
            <div className="text-center">
              <p className="text-sm opacity-90 mb-1">Next Enrollment Period</p>
              <p className="font-bold">Nov 1 - Nov 15, 2023</p>
              <div className="mt-2 text-xs inline-block px-2 py-1 bg-white bg-opacity-30 rounded-full">
                {new Date() > new Date(2023, 10, 15) ? 'Closed' : 'Upcoming'}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs for filtering benefits */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="flex -mb-px">
            <button
              onClick={() => setActiveTab('overview')}
              className={`mr-8 py-4 text-sm font-medium ${
                activeTab === 'overview'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab('health')}
              className={`mr-8 py-4 text-sm font-medium ${
                activeTab === 'health'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Health & Wellness
            </button>
            <button
              onClick={() => setActiveTab('financial')}
              className={`mr-8 py-4 text-sm font-medium ${
                activeTab === 'financial'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Financial Benefits
            </button>
            <button
              onClick={() => setActiveTab('dependents')}
              className={`py-4 text-sm font-medium ${
                activeTab === 'dependents'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Dependents
            </button>
          </nav>
        </div>
      </div>
      
      {/* Benefits Content based on active tab */}
      {activeTab === 'dependents' ? (
        // Dependents Tab
        <div>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">Covered Dependents</h3>
            <button className="px-3 py-1 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 flex items-center">
              <PlusCircle className="h-4 w-4 mr-1" />
              Add Dependent
            </button>
          </div>
          
          {dependents.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm p-8 text-center">
              <User className="h-12 w-12 mx-auto text-gray-300 mb-4" />
              <h3 className="text-lg font-medium text-gray-900">No dependents found</h3>
              <p className="text-gray-500 mt-2">
                You don't have any dependents added to your benefits.
              </p>
              <button className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <PlusCircle className="h-4 w-4 mr-2" />
                Add Your First Dependent
              </button>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Name
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Relationship
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date of Birth
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Coverage
                    </th>
                    <th scope="col" className="relative px-6 py-3">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {dependents.map((dependent) => (
                    <tr key={dependent.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{dependent.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{dependent.relationship}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{formatDate(dependent.dateOfBirth)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-wrap gap-1">
                          {dependent.coverageType.map((type, index) => (
                            <span key={index} className="px-2 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                              {type}
                            </span>
                          ))}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button className="text-blue-600 hover:text-blue-900">
                          Edit
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      ) : (
        // Benefits Cards for other tabs
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredBenefits.map(benefit => (
            <div 
              key={benefit.id}
              className="bg-white rounded-lg shadow border border-gray-100 overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => handleBenefitClick(benefit)}
            >
              <div className="p-6">
                <div className="flex items-start space-x-4">
                  <div className="p-2 bg-gray-50 rounded-lg">
                    {getBenefitIcon(benefit.category)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium text-gray-900">{benefit.name}</h3>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${
                        benefit.status === 'active' ? 'bg-green-100 text-green-800' :
                        benefit.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {benefit.status}
                      </span>
                    </div>
                    <p className="mt-1 text-sm text-gray-500 line-clamp-2">{benefit.description}</p>
                    <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-gray-500">Coverage</div>
                        <div className="font-medium text-gray-900">{benefit.coverage}</div>
                      </div>
                      <div>
                        <div className="text-gray-500">Effective Date</div>
                        <div className="font-medium text-gray-900">{formatDate(benefit.effectiveDate)}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-6 py-2 text-right border-t border-gray-100">
                <button className="text-sm font-medium text-blue-600 hover:text-blue-500">
                  View Details →
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Quick Contact Card */}
      <div className="mt-8 bg-white shadow-sm rounded-lg overflow-hidden border border-gray-100">
        <div className="px-6 py-4 border-b border-gray-100">
          <h3 className="text-lg font-medium text-gray-900">Benefits Support</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-start">
              <Phone className="h-5 w-5 text-blue-500 mr-3 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-gray-900">Benefits Hotline</h4>
                <p className="text-gray-600">**************</p>
                <p className="text-xs text-gray-500 mt-1">Mon-Fri, 9am-5pm ET</p>
              </div>
            </div>
            <div className="flex items-start">
              <Mail className="h-5 w-5 text-blue-500 mr-3 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-gray-900">Email Support</h4>
                <p className="text-gray-600"><EMAIL></p>
                <p className="text-xs text-gray-500 mt-1">Response within 24 hours</p>
              </div>
            </div>
            <div className="flex items-start">
              <Briefcase className="h-5 w-5 text-blue-500 mr-3 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-gray-900">HR Representative</h4>
                <p className="text-gray-600">Sarah Johnson</p>
                <p className="text-xs text-gray-500 mt-1">Ext. 4567</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Upcoming Events */}
      <div className="mt-6 bg-blue-50 p-4 rounded-lg border border-blue-100">
        <div className="flex items-center">
          <Clock className="h-5 w-5 text-blue-500 mr-2" />
          <h3 className="text-sm font-medium text-blue-800">Upcoming Events</h3>
        </div>
        <p className="mt-2 text-sm text-blue-600">
          Open Enrollment for 2024 benefits will begin on November 1, 2023. Make sure to review your options.
        </p>
      </div>

      {/* Benefit detail modal */}
      {renderBenefitDetailModal()}
    </div>
  );
};

export default EmployeeBenefitsView; 