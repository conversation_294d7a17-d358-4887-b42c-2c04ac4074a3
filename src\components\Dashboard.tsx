import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
  <PERSON>pt<PERSON>, AlertCircle, CheckCircle2, Clock, Timer, ThumbsUp, Ticket, 
  AlertTriangle, CheckCircle, Activity, Bell, Mail,
  HeadphonesIcon, Code, Network, Shield, Server, AppWindow, Building,
  ChevronDown, X, Save, FileImage, Tag, Box, Cpu, Hash, Barcode, Calendar, MapPin,
  Image as ImageIcon, ShoppingBag, Moon, Sun, Plus, Settings, Building2, BookOpen,
  Users
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useSocket } from '../contexts/SocketContext';
import { DashboardStats, DepartmentInfo, ActivityItem } from '../types/dashboard';
import { DepartmentPieChart } from './charts/DepartmentPieChart';
import { PriorityBarChart } from './charts/PriorityBarChart';
import { ActivityLineChart } from './charts/ActivityLineChart';
import AssetManagementDashboard from './AssetManagementDashboard';
import HRDashboard from './HRDashboard';
import { TicketTrendChart } from './charts/TicketTrendChart';
import { ResponseTimeChart } from './charts/ResponseTimeChart';
import { CategoryStatusChart } from './charts/CategoryStatusChart';
import { useNavigate } from 'react-router-dom';
import { TicketStatus, TicketPriority, UserRole } from '../types/common';
import { NotificationData } from '../services/socket';
import { toast } from 'react-hot-toast';
import KnowledgeBaseWidget from './KnowledgeBaseWidget';

const ICONS = {
  'Laptop': Laptop,
  'HeadphonesIcon': HeadphonesIcon,
  'Code': Code,
  'Network': Network,
  'Shield': Shield,
  'Server': Server,
  'AppWindow': AppWindow,
  'Building': Building,
};

// Add this type for better type safety
type DashboardType = 'tickets' | 'assets' | 'network' | 'security' | 'hr';
type TimeRange = 'today' | 'week' | 'month' | 'custom' | 'year';

type DepartmentColors = {
  [key in 'IT' | 'HR' | 'FINANCE' | 'MARKETING' | 'SALES' | 'OPERATIONS' | 'CSD' | 'LAND' | 'LEGAL' | 'MANAGEMENT' | 'PND']: string;
};

// Add these default data structures
const defaultTicketTrendData = {
  labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  datasets: [
    {
      label: 'New Tickets',
      data: [0, 0, 0, 0, 0, 0, 0],
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
    },
    {
      label: 'Resolved Tickets',
      data: [0, 0, 0, 0, 0, 0, 0],
      borderColor: 'rgb(34, 197, 94)',
      backgroundColor: 'rgba(34, 197, 94, 0.1)',
    },
  ],
};

const defaultResponseTimeData = {
  labels: ['< 1 hour', '1-4 hours', '4-24 hours', '> 24 hours'],
  datasets: [{
    label: 'Response Time',
    data: [0, 0, 0, 0],
    backgroundColor: [
      'rgba(34, 197, 94, 0.8)',
      'rgba(59, 130, 246, 0.8)',
      'rgba(245, 158, 11, 0.8)',
      'rgba(239, 68, 68, 0.8)',
    ],
    borderColor: [
      'rgb(34, 197, 94)',
      'rgb(59, 130, 246)',
      'rgb(245, 158, 11)',
      'rgb(239, 68, 68)',
    ],
    borderWidth: 1,
  }],
};

const defaultActivityData = {
  labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  datasets: [
    {
      label: 'Activity',
      data: [0, 0, 0, 0, 0, 0, 0],
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      fill: true,
    },
  ],
};

// Add default empty priority stats object
const emptyPriorityStats = {
  low: 0,
  medium: 0,
  high: 0,
  critical: 0
};

// Add new interfaces for notifications and customization
interface Notification {
  id: string;
  type: 'ticket' | 'escalation' | 'email' | 'system';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  priority: 'low' | 'medium' | 'high' | 'critical';
  link?: string;
}

interface DashboardWidget {
  id: string;
  type: 'stats' | 'department' | 'priority' | 'activity' | 'trends' | 'response' | 'knowledge';
  title: string;
  enabled: boolean;
  order: number;
}

interface DashboardCustomization {
  widgets: DashboardWidget[];
  layout: 'grid' | 'list';
  defaultView: 'tickets' | 'assets';
}

// Add new WebSocket message types
interface NotificationUpdate {
  type: 'NOTIFICATION';
  data: Notification;
}

interface EmailTicketUpdate {
  type: 'EMAIL_TICKET';
  data: {
    emailId: string;
    ticketId: string;
    subject: string;
    priority: string;
  };
}

interface TicketUpdate {
  type: 'TICKET_UPDATE';
  data: {
    oldStatus?: TicketStatus;
    newStatus?: TicketStatus;
    oldPriority?: TicketPriority;
    newPriority?: TicketPriority;
  };
}

interface ActivityUpdate {
  type: 'ACTIVITY_UPDATE';
  data: ActivityItem;
}

type WebSocketUpdate = TicketUpdate | ActivityUpdate | NotificationUpdate | EmailTicketUpdate;

// Update the filter state interface
interface FilterState {
  timeRange: TimeRange;
  department: string;
  priority: string;
  status: string;
}

export const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const { isConnected, notifications, markNotificationAsRead: markAsRead } = useSocket();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // Set HR dashboard as default for users with HR access
  const hasHRAccess = user?.role === UserRole.IT_ADMIN || user?.department === 'HR';
  const [selectedDashboard, setSelectedDashboard] = useState<DashboardType>(hasHRAccess ? 'hr' : 'tickets');
  const [timeRange, setTimeRange] = useState<TimeRange>('week');
  const [department, setDepartment] = useState<string>('all');
  const [priority, setPriority] = useState<string>('all');
  const [assetType, setAssetType] = useState('all');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [appliedFilters, setAppliedFilters] = useState<FilterState>({
    timeRange: 'week',
    department: 'all',
    priority: 'all',
    status: 'all'
  });
  const navigate = useNavigate();
  const [status, setStatus] = useState('all');
  const [unreadCount, setUnreadCount] = useState(0);
  const [showNotifications, setShowNotifications] = useState(false);
  const notificationRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsDropdownOpen(false);
    }
  };

  // Update unread count when notifications change
  useEffect(() => {
    if (notifications) {
      const unread = notifications.filter(n => !n.read).length;
      setUnreadCount(unread);
    }
  }, [notifications]);

  // Handle notification
  const handleNotification = useCallback((notification: NotificationData) => {
    // Show toast for new notifications
    toast.success(notification.title, {
      duration: 5000,
    });
  }, []);

  // Memoized filter handler
  const handleApplyFilters = useCallback(() => {
    // Reset stats before applying new filters
    setStats(null);
    setAppliedFilters({
      timeRange,
      department,
      priority,
      status
    });
  }, [timeRange, department, priority, status]);

  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('authToken');
      const queryParams = new URLSearchParams({
        timeRange: appliedFilters.timeRange,
        department: appliedFilters.department !== 'all' ? appliedFilters.department : '',
        priority: appliedFilters.priority !== 'all' ? appliedFilters.priority : ''
      }).toString();

      const response = await fetch(`/api/dashboard/stats${queryParams ? `?${queryParams}` : ''}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      
      // If a specific department is selected, filter the data
      if (appliedFilters.department !== 'all') {
        const departmentTickets = data.tickets.byDepartment[appliedFilters.department]?.ticketCount || 0;
        if (departmentTickets === 0) {
          // Set all stats to zero for department with no tickets
          setStats({
            ...data,
            tickets: {
              ...data.tickets,
              total: 0,
              open: 0,
              inProgress: 0,
              resolved: 0,
              byPriority: {
                low: 0,
                medium: 0,
                high: 0,
                critical: 0
              },
              byDepartment: {
                [appliedFilters.department]: {
                  name: appliedFilters.department,
                  ticketCount: 0,
                  color: getColorForDepartment(appliedFilters.department)
                }
              }
            }
          });
        } else {
          setStats(data);
        }
      } else {
        setStats(data);
      }
      
      setError(null);
    } catch (error) {
      console.error('Error fetching stats:', error);
      setError(error instanceof Error ? error.message : 'Failed to load dashboard');
      setStats(null);
    } finally {
      setLoading(false);
    }
  }, [appliedFilters, getColorForDepartment]);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  // Add listener for custom notification:data-refresh events
  useEffect(() => {
    const handleDataRefresh = (event: Event) => {
      const customEvent = event as CustomEvent<{type: string, data: any}>;
      const { type } = customEvent.detail;
      
      console.log('Dashboard: Received data refresh event:', type);
      
      if (type === 'ticket') {
        // Refresh the dashboard stats when we receive a ticket notification
        console.log('Dashboard: Refreshing stats due to ticket notification');
        fetchStats();
      }
    };
    
    // Add event listener for custom refresh events
    window.addEventListener('notification:data-refresh', handleDataRefresh);
    
    // Clean up
    return () => {
      window.removeEventListener('notification:data-refresh', handleDataRefresh);
    };
  }, [fetchStats]);

  // Add this function to handle navigation
  const handleStatCardClick = (filterType: string) => {
    navigate('/tickets', { state: { initialFilter: filterType } });
  };

  const handleEmailTicket = (data: EmailTicketUpdate['data']) => {
    const notification: Notification = {
      id: crypto.randomUUID(),
      type: 'email',
      title: 'New Ticket from Email',
      message: `Ticket created from email: ${data.subject}`,
      timestamp: new Date(),
      read: false,
      priority: data.priority as 'low' | 'medium' | 'high' | 'critical',
      link: `/tickets/${data.ticketId}`
    };
    handleNotification(notification);
  };

  const handleMarkAsRead = (id: string) => {
    markAsRead(id);
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  // Render notification panel
  const renderNotificationPanel = () => (
    <div
      ref={notificationRef}
      className="absolute right-0 top-full mt-2 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-lg z-20 overflow-hidden"
    >
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h3 className="font-semibold text-gray-900 dark:text-white">Notifications</h3>
        <button
          onClick={() => setShowNotifications(false)}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <X className="h-5 w-5" />
        </button>
      </div>
      <div className="max-h-96 overflow-y-auto">
        {notifications.length === 0 ? (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            No notifications
          </div>
        ) : (
          notifications.map(notification => (
            <div
              key={notification.id}
              className={`p-4 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                !notification.read ? 'bg-blue-50 dark:bg-blue-900/20' : ''
              }`}
              onClick={() => handleMarkAsRead(notification.id)}
            >
              <div className="flex items-start gap-3">
                {notification.type === 'ticket' && <Ticket className="h-5 w-5 text-blue-500" />}
                {notification.type === 'escalation' && <AlertTriangle className="h-5 w-5 text-red-500" />}
                {notification.type === 'email' && <Mail className="h-5 w-5 text-green-500" />}
                {notification.type === 'system' && <Settings className="h-5 w-5 text-purple-500" />}
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">{notification.title}</h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">{notification.message}</p>
                  <span className="text-xs text-gray-400 dark:text-gray-500">
                    {new Date(notification.timestamp).toLocaleTimeString()}
                  </span>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );

  // Add knowledge base widget to default widgets
  const defaultWidgets: DashboardWidget[] = [
    { id: 'stats', type: 'stats', title: 'Statistics', enabled: true, order: 1 },
    { id: 'department', type: 'department', title: 'Tickets by Department', enabled: true, order: 2 },
    { id: 'priority', type: 'priority', title: 'Tickets by Priority', enabled: true, order: 3 },
    { id: 'activity', type: 'activity', title: 'Recent Activity', enabled: true, order: 4 },
    { id: 'trends', type: 'trends', title: 'Ticket Trends', enabled: true, order: 5 },
    { id: 'response', type: 'response', title: 'Response Time', enabled: true, order: 6 },
    { id: 'knowledge', type: 'knowledge', title: 'Knowledge Base', enabled: true, order: 7 }
  ];

  // Move renderWidget inside the component to access stats
  const renderWidget = (widget: DashboardWidget) => {
    switch (widget.type) {
      case 'stats':
        return (
          <div key={widget.id} className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* ... existing stat cards ... */}
          </div>
        );
      case 'department':
        return (
          <div key={widget.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
              {widget.title}
            </h3>
            <DepartmentPieChart departmentStats={stats?.tickets.byDepartment || {}} />
          </div>
        );
      case 'priority':
        return (
          <div key={widget.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Tickets by Priority</h3>
            <PriorityBarChart priorityStats={stats?.tickets.byPriority || emptyPriorityStats} />
          </div>
        );
      case 'activity':
        return (
          <div key={widget.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Activity Timeline</h3>
            <ActivityLineChart data={stats?.activityData || defaultActivityData} />
          </div>
        );
      case 'trends':
        return (
          <div key={widget.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Ticket Trends</h3>
            <TicketTrendChart data={stats?.ticketTrends || defaultTicketTrendData} />
          </div>
        );
      case 'response':
        return (
          <div key={widget.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Response Time Analysis</h3>
            <ResponseTimeChart data={stats?.responseTimes || defaultResponseTimeData} />
          </div>
        );
      case 'knowledge':
        return (
          <div className="col-span-1 md:col-span-2 lg:col-span-3 xl:col-span-3">
            <KnowledgeBaseWidget 
              limit={3} 
              featuredOnly={true} 
              showTitle={true} 
              title="Featured Knowledge Base Articles"
            />
          </div>
        );
      default:
        return null;
    }
  };

  // Add back the handler functions
  const handleTicketUpdate = (data: TicketUpdate['data']) => {
    setStats(prevStats => {
      if (!prevStats) return prevStats;
      
      const newStats = { ...prevStats };
      
      // Update ticket counts
      if (data.oldStatus !== data.newStatus) {
        if (data.oldStatus) {
          const oldStatusKey = data.oldStatus.toLowerCase();
          switch (oldStatusKey) {
            case 'open':
              newStats.tickets.open -= 1;
              break;
            case 'in_progress':
              newStats.tickets.inProgress -= 1;
              break;
            case 'resolved':
              newStats.tickets.resolved -= 1;
              break;
          }
        }
        if (data.newStatus) {
          const newStatusKey = data.newStatus.toLowerCase();
          switch (newStatusKey) {
            case 'open':
              newStats.tickets.open += 1;
              break;
            case 'in_progress':
              newStats.tickets.inProgress += 1;
              break;
            case 'resolved':
              newStats.tickets.resolved += 1;
              break;
          }
        }
      }
      
      // Update priority stats if changed
      if (data.oldPriority !== data.newPriority) {
        if (data.oldPriority) {
          const oldPriorityKey = data.oldPriority.toLowerCase() as keyof typeof newStats.tickets.byPriority;
          newStats.tickets.byPriority[oldPriorityKey] -= 1;
        }
        if (data.newPriority) {
          const newPriorityKey = data.newPriority.toLowerCase() as keyof typeof newStats.tickets.byPriority;
          newStats.tickets.byPriority[newPriorityKey] += 1;
        }
      }
      
      return newStats;
    });
  };

  const handleActivityUpdate = (data: ActivityItem) => {
    setStats(prevStats => {
      if (!prevStats) return prevStats;
      
      return {
        ...prevStats,
        tickets: {
          ...prevStats.tickets,
          recentActivity: [
            data,
            ...prevStats.tickets.recentActivity.slice(0, 9)
          ]
        }
      };
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 dark:bg-red-900/50 text-red-500 dark:text-red-200 rounded-lg">
        <AlertCircle className="inline-block mr-2" /> {error}
      </div>
    );
  }

  const dashboardOptions = [
    ...(user?.role === UserRole.IT_ADMIN || user?.department === 'HR' ? [{
      id: 'hr', 
      label: 'HR Dashboard', 
      icon: Users,
      description: 'HR metrics and employee management'
    }] : []),
    { 
      id: 'tickets', 
      label: 'Ticket Dashboard', 
      icon: Ticket,
      description: 'View and manage support tickets'
    },
    { 
      id: 'assets', 
      label: 'Asset Dashboard', 
      icon: Laptop,
      description: 'Track IT assets and inventory'
    },
    { 
      id: 'network', 
      label: 'Network Dashboard', 
      icon: Network,
      description: 'Monitor network performance'
    },
    { 
      id: 'security', 
      label: 'Security Dashboard', 
      icon: Shield,
      description: 'Security metrics and alerts'
    }
  ];

  // Render different dashboard content based on selection
  const renderDashboardContent = () => {
    switch (selectedDashboard) {
      case 'tickets':
        return (
          <div className="grid gap-4">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
              <StatCard
                icon={<Ticket className="w-7 h-7 text-blue-500" />}
                title="Total Tickets"
                value={stats?.tickets.total || 0}
                className="bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors rounded-lg shadow-sm"
                onClick={() => handleStatCardClick('all')}
              />
              <StatCard
                icon={<AlertCircle className="w-7 h-7 text-yellow-500" />}
                title="Open Tickets"
                value={stats?.tickets.open || 0}
                className="bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors rounded-lg shadow-sm"
                onClick={() => handleStatCardClick('open')}
              />
              <StatCard
                icon={<Clock className="w-7 h-7 text-orange-500" />}
                title="In Progress"
                value={stats?.tickets.inProgress || 0}
                className="bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors rounded-lg shadow-sm"
                onClick={() => handleStatCardClick('inProgress')}
              />
              <StatCard
                icon={<CheckCircle className="w-7 h-7 text-green-500" />}
                title="Resolved Tickets"
                value={stats?.tickets.resolved || 0}
                className="bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors rounded-lg shadow-sm"
                onClick={() => handleStatCardClick('resolved')}
              />
              <StatCard
                icon={<AlertTriangle className="w-7 h-7 text-red-500" />}
                title="Critical Issues"
                value={stats?.tickets.byPriority.critical || 0}
                className="bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors rounded-lg shadow-sm"
                onClick={() => handleStatCardClick('critical')}
              />
            </div>

            {/* Charts Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              {/* Department Distribution */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Tickets by Department</h3>
                <DepartmentPieChart departmentStats={stats?.tickets.byDepartment || {}} />
              </div>

              {/* Priority Distribution */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Tickets by Priority</h3>
                <PriorityBarChart priorityStats={stats?.tickets.byPriority || emptyPriorityStats} />
              </div>

              {/* Activity Timeline */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Activity Timeline</h3>
                <ActivityLineChart data={stats?.activityData || defaultActivityData} />
              </div>
            </div>

            {/* Additional Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Ticket Trends</h3>
                <TicketTrendChart data={stats?.ticketTrends || defaultTicketTrendData} />
              </div>
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Response Time Analysis</h3>
                <ResponseTimeChart data={stats?.responseTimes || defaultResponseTimeData} />
              </div>
            </div>
          </div>
        );

      case 'assets':
        return <AssetManagementDashboard />;

      case 'network':
        return (
          <div className="bg-white rounded-xl shadow-sm p-8 text-center">
            <Network className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">Network Dashboard Coming Soon</h2>
            <p className="text-gray-600">This feature is under development</p>
          </div>
        );

      case 'security':
        return (
          <div className="bg-white rounded-xl shadow-sm p-8 text-center">
            <Shield className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">Security Dashboard Coming Soon</h2>
            <p className="text-gray-600">This feature is under development</p>
          </div>
        );

      case 'hr':
        // Only show HR Dashboard for users with admin or HR role
        const hasHRAccess = user?.role === UserRole.IT_ADMIN || 
                          user?.department === 'HR';
        
        return hasHRAccess ? (
          <div className="bg-gray-50 dark:bg-gray-900">
            <HRDashboard />
          </div>
        ) : (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-8 text-center">
            <Users className="w-16 h-16 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
            <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-2">Access Restricted</h2>
            <p className="text-gray-600 dark:text-gray-400">You need HR permissions to access this dashboard</p>
          </div>
        );

      default:
        return null;
    }
  };

  // Render notification button only
  return (
    <div className="p-4">
      {/* Connection Status Indicator */}
      <div className={`fixed bottom-4 right-4 flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium ${
        isConnected ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
      }`}>
        <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-yellow-500'}`} />
        {isConnected ? 'Live Updates' : 'Connecting...'}
      </div>

      {/* Dashboard Header with Filters */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 mb-4">
        {/* Dashboard Selector */}
        <div className="relative" ref={dropdownRef}>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-800 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <span className="text-gray-900 dark:text-white font-medium">
                {dashboardOptions.find(opt => opt.id === selectedDashboard)?.label}
              </span>
              <ChevronDown className="h-4 w-4 text-gray-500 dark:text-gray-400" />
            </button>
            {selectedDashboard === 'hr' && (
              <div className="flex items-center justify-between flex-grow">
                <span className="text-gray-500 dark:text-gray-400">Overview of your organization's human resources</span>
                <div id="hr-dashboard-refresh-container"></div>
              </div>
            )}
          </div>

          {isDropdownOpen && (
            <div className="absolute top-full left-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg z-10">
              {dashboardOptions.map(option => (
                <button
                  key={option.id}
                  onClick={() => {
                    setSelectedDashboard(option.id as DashboardType);
                    setIsDropdownOpen(false);
                  }}
                  className={`w-full flex items-center gap-3 px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                    selectedDashboard === option.id ? 'bg-gray-50 dark:bg-gray-700' : ''
                  }`}
                >
                  <option.icon className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                  <div className="text-left">
                    <div className="font-medium text-gray-900 dark:text-white">{option.label}</div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">{option.description}</div>
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Show filters only for Ticket Dashboard */}
        {selectedDashboard === 'tickets' && (
          <div className="flex items-center gap-4">
            {/* Time Range Filter */}
            <select
              className="px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as TimeRange)}
            >
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="custom">Custom Range</option>
            </select>

            {/* Department Filter */}
            <select
              className="px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
              value={department}
              onChange={(e) => setDepartment(e.target.value)}
            >
              <option value="all">All Departments</option>
              <option value="IT">IT</option>
              <option value="HR">HR</option>
              <option value="FINANCE">Finance</option>
              <option value="MARKETING">Marketing</option>
              <option value="SALES">Sales</option>
              <option value="OPERATIONS">Operations</option>
              <option value="CSD">CSD</option>
              <option value="LAND">Land</option>
              <option value="LEGAL">Legal</option>
              <option value="MANAGEMENT">Management</option>
              <option value="PND">PND</option>
            </select>

            {/* Priority Filter */}
            <select
              className="px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
              value={priority}
              onChange={(e) => setPriority(e.target.value)}
            >
              <option value="all">All Priorities</option>
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>

            {/* Status Filter */}
            <select
              className="px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400"
              value={status}
              onChange={(e) => setStatus(e.target.value)}
            >
              <option value="all">All Statuses</option>
              <option value="open">Open</option>
              <option value="in_progress">In Progress</option>
              <option value="resolved">Resolved</option>
            </select>

            <button
              onClick={handleApplyFilters}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white rounded-lg transition-colors flex items-center gap-2"
            >
              Apply Filters
            </button>
          </div>
        )}
      </div>

      {/* Render dashboard content */}
      {renderDashboardContent()}
    </div>
  );
};

interface MetricCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: 'blue' | 'green' | 'yellow' | 'red';
}

const MetricCard: React.FC<MetricCardProps> = ({ title, value, icon, color }) => {
  const colorClasses = {
    blue: 'bg-blue-50 text-blue-500',
    green: 'bg-green-50 text-green-500',
    yellow: 'bg-yellow-50 text-yellow-500',
    red: 'bg-red-50 text-red-500',
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex items-center gap-3">
        <div className={`p-3 rounded-full ${colorClasses[color]}`}>
          {icon}
        </div>
        <div>
          <p className="text-sm text-gray-500">{title}</p>
          <p className="text-2xl font-semibold">{value}</p>
        </div>
      </div>
    </div>
  );
};

interface StatCardProps {
  icon: React.ReactNode;
  title: string;
  value: number;
  className: string;
  onClick?: () => void;
}

const StatCard: React.FC<StatCardProps> = ({ icon, title, value, className, onClick }) => {
  return (
    <button
      onClick={onClick}
      className={`${className} p-4 flex items-start gap-4 cursor-pointer`}
    >
      <div className="rounded-full p-2 bg-gray-50 dark:bg-gray-700">{icon}</div>
      <div className="flex-1">
        <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">{title}</h3>
        <p className="text-2xl font-semibold text-gray-900 dark:text-white mt-1">{value}</p>
      </div>
    </button>
  );
};

// Add this function at the top level of the component
const getColorForDepartment = (department: string): string => {
  const colors: DepartmentColors = {
    'IT': '#4F46E5',      // Indigo
    'HR': '#EC4899',      // Pink
    'FINANCE': '#10B981', // Emerald
    'MARKETING': '#F59E0B', // Amber
    'SALES': '#3B82F6',   // Blue
    'OPERATIONS': '#6366F1', // Indigo
    'CSD': '#8B5CF6',     // Violet
    'LAND': '#14B8A6',    // Teal
    'LEGAL': '#EF4444',   // Red
    'MANAGEMENT': '#8B5CF6', // Violet
    'PND': '#06B6D4',     // Cyan
  };
  return (colors as Record<string, string>)[department] || '#6B7280'; // Default gray color
};

export default Dashboard;