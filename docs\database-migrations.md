# Database Migrations

This directory contains SQL migration files that need to be applied to the database.

## Latest Migration

**employee_projects_migration.sql**: Adds the employee_projects table needed for storing employee project entries.

## How to Apply a Migration

### Using psql (PostgreSQL command line)

```bash
# For local development database
psql -U your_username -d your_database_name -f employee_projects_migration.sql

# For production database (if using external host)
psql -h your_host -U your_username -d your_database_name -f employee_projects_migration.sql
```

### Using pgAdmin

1. Open pgAdmin
2. Connect to your database
3. Right-click on your database
4. Select "Query Tool"
5. Open the SQL file (employee_projects_migration.sql)
6. Click the "Execute" button

### Using TypeORM (if applicable)

```bash
# If using TypeORM migrations system
npm run typeorm migration:run
```

## Verifying the Migration

After applying the migration, you can verify it was successful by checking if the table exists:

```sql
-- Run this in your SQL client
SELECT * FROM information_schema.tables WHERE table_name = 'employee_projects';
```

You should see one row in the results if the table was created successfully. 