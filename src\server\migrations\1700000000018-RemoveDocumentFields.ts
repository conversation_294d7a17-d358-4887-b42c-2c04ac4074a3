import { MigrationInterface, QueryRunner } from "typeorm";

export class RemoveDocumentFields1700000000018 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        try {
            // 1. First update existing records to set verificationStatus to 'verified' where it's 'pending'
            await queryRunner.query(`
                UPDATE employee_documents 
                SET verificationStatus = 'verified' 
                WHERE verificationStatus = 'pending'
            `);
            
            console.log('Updated pending status to verified');
            
            // 2. Create a temporary column with the new enum type
            await queryRunner.query(`
                ALTER TABLE employee_documents 
                ADD COLUMN temp_status ENUM('verified', 'rejected') NOT NULL DEFAULT 'verified'
            `);
            
            console.log('Added temporary column');
            
            // 3. Copy data to the temporary column
            await queryRunner.query(`
                UPDATE employee_documents 
                SET temp_status = 
                    CASE 
                        WHEN verificationStatus = 'verified' THEN 'verified'
                        WHEN verificationStatus = 'rejected' THEN 'rejected'
                        ELSE 'verified'
                    END
            `);
            
            console.log('Copied data to temporary column');
            
            // 4. Drop the original column
            await queryRunner.query(`
                ALTER TABLE employee_documents 
                DROP COLUMN verificationStatus
            `);
            
            console.log('Dropped original column');
            
            // 5. Rename the temporary column to the original name
            await queryRunner.query(`
                ALTER TABLE employee_documents 
                CHANGE COLUMN temp_status verificationStatus ENUM('verified', 'rejected') NOT NULL DEFAULT 'verified'
            `);
            
            console.log('Renamed temporary column');
            
            // 6. Remove the columns we don't need anymore
            await queryRunner.query(`
                ALTER TABLE employee_documents 
                DROP COLUMN IF EXISTS documentNumber, 
                DROP COLUMN IF EXISTS issueDate, 
                DROP COLUMN IF EXISTS expiryDate
            `);
            
            console.log('Removed unnecessary columns');
        } catch (error) {
            console.error('Migration failed:', error);
            throw error;
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        try {
            // 1. Add the removed columns back
            await queryRunner.query(`
                ALTER TABLE employee_documents 
                ADD COLUMN documentNumber VARCHAR(255) NULL,
                ADD COLUMN issueDate VARCHAR(255) NULL,
                ADD COLUMN expiryDate VARCHAR(255) NULL
            `);
            
            // 2. Create a temporary column with the original enum type
            await queryRunner.query(`
                ALTER TABLE employee_documents 
                ADD COLUMN temp_status ENUM('pending', 'verified', 'rejected') NOT NULL DEFAULT 'pending'
            `);
            
            // 3. Copy data to the temporary column
            await queryRunner.query(`
                UPDATE employee_documents 
                SET temp_status = 
                    CASE 
                        WHEN verificationStatus = 'verified' THEN 'verified'
                        WHEN verificationStatus = 'rejected' THEN 'rejected'
                        ELSE 'pending'
                    END
            `);
            
            // 4. Drop the modified column
            await queryRunner.query(`
                ALTER TABLE employee_documents 
                DROP COLUMN verificationStatus
            `);
            
            // 5. Rename the temporary column to the original name
            await queryRunner.query(`
                ALTER TABLE employee_documents 
                CHANGE COLUMN temp_status verificationStatus ENUM('pending', 'verified', 'rejected') NOT NULL DEFAULT 'pending'
            `);
        } catch (error) {
            console.error('Migration reversion failed:', error);
            throw error;
        }
    }
} 