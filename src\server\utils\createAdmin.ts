import { AppDataSource } from '../../config/database';
import { User, UserRole } from '../../entities/User';
import bcrypt from 'bcryptjs';

interface AdminConfig {
  id: string;
  email: string;
  password: string;
  name: string;
  isTest?: boolean;
}

export async function createAdmin(adminConfig?: AdminConfig) {
  try {
    const userRepository = AppDataSource.getRepository(User);

    // Use provided config or default values
    const id = adminConfig?.id || 'IT001';
    const email = adminConfig?.email || process.env.ADMIN_EMAIL || '<EMAIL>';
    const password = adminConfig?.password || process.env.ADMIN_PASSWORD || 'admin123';
    const name = adminConfig?.name || 'Test Admin';

    // Find existing admin user by email
    let admin = await userRepository.findOne({ 
      where: { email }
    });

    const hashedPassword = await bcrypt.hash(password, 10);

    if (admin) {
      // Update existing admin
      admin.name = name;
      admin.password = hashedPassword;
      admin.role = UserRole.IT_ADMIN;
      admin.department = 'IT';
      admin.isActive = true;
      admin.permissions = {
        canCreateTickets: true,
        canCreateTicketsForOthers: true,
        canEditTickets: true,
        canDeleteTickets: true,
        canCloseTickets: true,
        canLockTickets: true,
        canAssignTickets: true,
        canEscalateTickets: true,
        canViewAllTickets: true
      };
      console.log('Updating existing admin user...');
    } else {
      // Create new admin user
      admin = userRepository.create({
        id,
        name,
        email,
        password: hashedPassword,
        role: UserRole.IT_ADMIN,
        department: 'IT',
        isActive: true,
        permissions: {
          canCreateTickets: true,
          canCreateTicketsForOthers: true,
          canEditTickets: true,
          canDeleteTickets: true,
          canCloseTickets: true,
          canLockTickets: true,
          canAssignTickets: true,
          canEscalateTickets: true,
          canViewAllTickets: true
        }
      });
      console.log('Creating new admin user...');
    }

    const savedAdmin = await userRepository.save(admin);
    console.log(`Admin user saved successfully: ID=${savedAdmin.id}, email=${savedAdmin.email}`);
    
    if (adminConfig?.isTest) {
      console.log('⚠️ Test admin account created. Do not use in production!');
    }

    return savedAdmin;
  } catch (error) {
    console.error('Error creating/updating admin:', error);
    throw error;
  }
} 