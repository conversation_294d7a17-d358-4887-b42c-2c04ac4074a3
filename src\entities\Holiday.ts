import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { IsNotEmpty, IsEnum, IsOptional, IsString, IsBoolean, IsDateString } from 'class-validator';
import { HolidayCalendar } from './HolidayCalendar';

export enum HolidayType {
  PUBLIC = 'public',
  COMPANY = 'company',
  OPTIONAL = 'optional'
}

@Entity('holidays')
@Index(['date'])
@Index(['type'])
@Index(['isActive'])
export class Holiday {
  @PrimaryGeneratedColumn({ type: 'int' })
  id: number;

  @Column({ type: 'varchar', length: 100 })
  @IsNotEmpty({ message: 'Holiday name is required' })
  @IsString({ message: 'Holiday name must be a string' })
  name: string;

  @Column({ type: 'date' })
  @IsNotEmpty({ message: 'Holiday date is required' })
  @IsDateString({}, { message: 'Invalid date format' })
  date: string;

  @Column({
    type: 'enum',
    enum: HolidayType,
    default: HolidayType.PUBLIC
  })
  @IsEnum(HolidayType, { message: 'Holiday type must be public, company, or optional' })
  type: HolidayType;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  description?: string;

  @Column({ type: 'boolean', default: true })
  @IsBoolean({ message: 'isActive must be a boolean' })
  isActive: boolean;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString({ message: 'Created by must be a string' })
  createdBy?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @IsString({ message: 'Updated by must be a string' })
  updatedBy?: string;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  metadata?: {
    isRecurring?: boolean;
    applicableRegions?: string[];
    observedBy?: string[];
    notes?: string;
    religion?: string;
    culturalSignificance?: string;
    observanceLevel?: 'major' | 'minor' | 'regional';
    alternativeNames?: string[];
    lunarCalendar?: boolean;
    variableDate?: boolean;
    duration?: number;
    fastingRequired?: boolean;
    prayerTimes?: string[];
    traditionalFoods?: string[];
    customaryActivities?: string[];
  };

  @Column({ type: 'boolean', default: false })
  @IsBoolean({ message: 'isRecurring must be a boolean' })
  isRecurring: boolean;

  @Column({ type: 'boolean', default: false })
  @IsBoolean({ message: 'isOptional must be a boolean' })
  isOptional: boolean;

  @Column({ type: 'int' })
  calendarId: number;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => HolidayCalendar, calendar => calendar.holidays)
  @JoinColumn({ name: 'calendarId' })
  calendar: HolidayCalendar;
} 