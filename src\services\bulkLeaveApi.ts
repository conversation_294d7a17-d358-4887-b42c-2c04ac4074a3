import { LeaveRequest, LeaveStatus } from '../types/attendance';

interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

interface BulkLeaveRequest {
  employeeId: number;
  employeeName: string;
  leaveType: string;
  startDate: string;
  endDate: string;
  reason: string;
  isHalfDay: boolean;
  status: LeaveStatus;
  daysRequested: number;
  autoApprove?: boolean;
  notifyEmployee?: boolean;
}

interface BulkLeaveValidationResult {
  employeeId: number;
  employeeName: string;
  isValid: boolean;
  errors: string[];
  warnings: string[];
  availableBalance?: number;
  requestedDays: number;
}

interface BulkLeaveCreateRequest {
  requests: BulkLeaveRequest[];
  options: {
    autoApprove: boolean;
    notifyEmployees: boolean;
    skipValidation: boolean;
    createdBy: string;
    reason: string;
  };
}

interface BulkLeaveCreateResponse {
  successful: BulkLeaveRequest[];
  failed: Array<{
    request: BulkLeaveRequest;
    error: string;
  }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
    autoApproved: number;
  };
}

interface EmployeeLeaveBalance {
  employeeId: number;
  employeeName: string;
  leaveType: string;
  totalEntitlement: number;
  used: number;
  pending: number;
  remaining: number;
  carryForward?: number;
}

interface BulkLeavePreviewRequest {
  leaveType: string;
  startDate: string;
  endDate: string;
  isHalfDay: boolean;
  employeeIds: number[];
}

interface BulkLeavePreviewResponse {
  validations: BulkLeaveValidationResult[];
  summary: {
    totalEmployees: number;
    validRequests: number;
    invalidRequests: number;
    totalDaysRequested: number;
    estimatedCost?: number;
  };
}

// Utility function to handle API errors
const handleApiError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  if (error.message) {
    return error.message;
  }
  return 'An unexpected error occurred';
};

// Get auth token from localStorage
const getAuthToken = (): string => {
  return localStorage.getItem('authToken') || '';
};

// Bulk Leave API Service
export const bulkLeaveApi = {
  /**
   * Validate bulk leave requests before creation
   */
  validateBulkLeave: async (previewData: BulkLeavePreviewRequest): Promise<ApiResponse<BulkLeavePreviewResponse>> => {
    try {
      const token = getAuthToken();
      
      const response = await fetch('/api/leave-requests/bulk/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify(previewData)
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return result;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  /**
   * Create bulk leave requests
   */
  createBulkLeave: async (bulkData: BulkLeaveCreateRequest): Promise<ApiResponse<BulkLeaveCreateResponse>> => {
    try {
      const token = getAuthToken();
      
      console.log('Creating bulk leave requests:', {
        requestCount: bulkData.requests.length,
        autoApprove: bulkData.options.autoApprove,
        notifyEmployees: bulkData.options.notifyEmployees
      });

      const response = await fetch('/api/leave-requests/bulk/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify(bulkData)
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return result;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  /**
   * Get employee leave balances for bulk operations
   */
  getEmployeeLeaveBalances: async (employeeIds: number[], leaveType?: string): Promise<ApiResponse<EmployeeLeaveBalance[]>> => {
    try {
      const token = getAuthToken();
      const currentYear = new Date().getFullYear();
      
      const queryParams = new URLSearchParams({
        year: currentYear.toString(),
        employeeIds: employeeIds.join(',')
      });
      
      if (leaveType) {
        queryParams.append('leaveType', leaveType);
      }

      const response = await fetch(`/api/leave-balances/bulk?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
        }
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return result;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  /**
   * Bulk approve leave requests
   */
  bulkApproveLeave: async (requestIds: number[], approverComments?: string): Promise<ApiResponse<any>> => {
    try {
      const token = getAuthToken();
      
      const response = await fetch('/api/leave-requests/bulk/approve', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify({
          requestIds,
          approverComments
        })
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return result;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  /**
   * Bulk reject leave requests
   */
  bulkRejectLeave: async (requestIds: number[], rejectionReason: string): Promise<ApiResponse<any>> => {
    try {
      const token = getAuthToken();
      
      const response = await fetch('/api/leave-requests/bulk/reject', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify({
          requestIds,
          rejectionReason
        })
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return result;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  /**
   * Get bulk leave operation history
   */
  getBulkLeaveHistory: async (page: number = 1, limit: number = 20): Promise<ApiResponse<any>> => {
    try {
      const token = getAuthToken();
      
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString()
      });

      const response = await fetch(`/api/leave-requests/bulk/history?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
        }
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return result;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  /**
   * Export bulk leave data to CSV/Excel
   */
  exportBulkLeaveData: async (filters: {
    startDate?: string;
    endDate?: string;
    departments?: string[];
    leaveTypes?: string[];
    status?: string[];
    format: 'csv' | 'excel';
  }): Promise<Blob> => {
    try {
      const token = getAuthToken();
      
      const response = await fetch('/api/leave-requests/bulk/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: JSON.stringify(filters)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.blob();
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  /**
   * Import bulk leave data from CSV/Excel
   */
  importBulkLeaveData: async (file: File, options: {
    autoApprove: boolean;
    notifyEmployees: boolean;
    skipValidation: boolean;
  }): Promise<ApiResponse<any>> => {
    try {
      const token = getAuthToken();
      
      const formData = new FormData();
      formData.append('file', file);
      formData.append('options', JSON.stringify(options));

      const response = await fetch('/api/leave-requests/bulk/import', {
        method: 'POST',
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
        },
        body: formData
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      return result;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  }
};

export default bulkLeaveApi;
