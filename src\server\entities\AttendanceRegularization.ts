import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { Employee } from './Employee';

@Entity('attendance_regularization_requests')
export class AttendanceRegularization {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'employee_id' })
  employeeId: number;

  @Column({ name: 'employee_name' })
  employeeName: string;

  @Column({ type: 'date' })
  date: Date;

  @Column()
  type: string;

  @Column({ name: 'requested_time', type: 'time' })
  requestedTime: string;

  @Column({ type: 'text' })
  reason: string;

  @Column({ default: 'pending' })
  status: string;

  @Column({ name: 'approver_comments', type: 'text', nullable: true })
  approverComments?: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne(() => Employee, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;
} 