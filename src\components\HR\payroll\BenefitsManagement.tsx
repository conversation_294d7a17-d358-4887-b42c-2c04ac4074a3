import React, { useState, useEffect } from 'react';
import { 
  Heart, 
  Plus, 
  Search, 
  Filter, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  CheckCircle, 
  AlertCircle, 
  Users,
  Download,
  Upload,
  FileText,
  User
} from 'lucide-react';
import { BenefitPlan, BenefitType, BenefitCalculationType, EmployeeBenefit, PayrollCurrency } from '../../../types/payroll';

interface BenefitsManagementProps {
  isAdmin?: boolean;
}

const BenefitsManagement: React.FC<BenefitsManagementProps> = ({ isAdmin = false }) => {
  const [activeTab, setActiveTab] = useState<'plans' | 'enrollments'>('plans');
  const [benefitPlans, setBenefitPlans] = useState<BenefitPlan[]>([]);
  const [employeeBenefits, setEmployeeBenefits] = useState<EmployeeBenefit[]>([]);
  const [loading, setLoading] = useState(false);
  const [showAddPlanModal, setShowAddPlanModal] = useState(false);
  const [showEnrollEmployeeModal, setShowEnrollEmployeeModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<BenefitPlan | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterBenefitType, setFilterBenefitType] = useState<string>('all');

  // Currency format utility
  const formatCurrency = (amount: number, currency: PayrollCurrency = PayrollCurrency.USD) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount);
  };

  // Format date utility
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Load sample data on component mount
  useEffect(() => {
    setLoading(true);
    // Simulating API call
    setTimeout(() => {
      // Sample benefit plans
      const sampleBenefitPlans: BenefitPlan[] = [
        {
          id: 1,
          name: 'Premium Health Insurance',
          benefitType: BenefitType.HEALTH_INSURANCE,
          description: 'Comprehensive health insurance with dental and vision coverage',
          provider: 'BlueCross Health',
          planNumber: 'HC-2025-001',
          startDate: '2025-01-01',
          calculationType: BenefitCalculationType.FIXED,
          employerContribution: 800,
          employeeContribution: 200,
          isActive: true,
          currency: PayrollCurrency.USD,
          calculationBase: 'fixed',
          eligibilityCriteria: {
            minServiceMonths: 3,
            employmentTypes: ['full-time']
          },
          createdAt: '2024-11-10',
        },
        {
          id: 2,
          name: '401(k) Retirement Plan',
          benefitType: BenefitType.RETIREMENT_PLAN,
          description: 'Company matched retirement savings plan',
          provider: 'Fidelity Investments',
          planNumber: 'RP-2025-001',
          startDate: '2025-01-01',
          calculationType: BenefitCalculationType.PERCENTAGE,
          employerContribution: 4, // 4% match
          employeeContribution: 6, // 6% employee contribution
          isActive: true,
          currency: PayrollCurrency.USD,
          calculationBase: 'gross_salary',
          eligibilityCriteria: {
            minServiceMonths: 6,
            employmentTypes: ['full-time', 'part-time']
          },
          createdAt: '2024-11-10',
        },
        {
          id: 3,
          name: 'Life Insurance Policy',
          benefitType: BenefitType.LIFE_INSURANCE,
          description: '2x annual salary life insurance coverage',
          provider: 'Guardian Life',
          planNumber: 'LI-2025-001',
          startDate: '2025-01-01',
          calculationType: BenefitCalculationType.SALARY_BASED,
          employerContribution: 100, // Full coverage by employer
          employeeContribution: 0,
          isActive: true,
          currency: PayrollCurrency.USD,
          calculationBase: 'base_salary',
          createdAt: '2024-11-10',
        },
        {
          id: 4,
          name: 'Wellness Program',
          benefitType: BenefitType.WELLNESS_PROGRAM,
          description: 'Gym membership, health assessments, and wellness activities',
          provider: 'WellCorp',
          startDate: '2025-01-01',
          calculationType: BenefitCalculationType.FIXED,
          employerContribution: 50,
          employeeContribution: 20,
          isActive: true,
          currency: PayrollCurrency.USD,
          calculationBase: 'fixed',
          createdAt: '2024-11-10',
        }
      ];

      // Sample employee benefits (enrollments)
      const sampleEmployeeBenefits: EmployeeBenefit[] = [
        {
          id: 1,
          employeeId: 1,
          employeeName: 'John Smith',
          benefitPlanId: 1,
          benefitPlanName: 'Premium Health Insurance',
          enrollmentDate: '2024-12-15',
          effectiveDate: '2025-01-01',
          coverageLevel: 'employee_family',
          dependents: 3,
          employerContribution: 800,
          employeeContribution: 200,
          status: 'active',
        },
        {
          id: 2,
          employeeId: 1,
          employeeName: 'John Smith',
          benefitPlanId: 2,
          benefitPlanName: '401(k) Retirement Plan',
          enrollmentDate: '2024-12-15',
          effectiveDate: '2025-01-01',
          employerContribution: 283.33, // 4% of monthly salary
          employeeContribution: 425, // 6% of monthly salary
          status: 'active',
        },
        {
          id: 3,
          employeeId: 2,
          employeeName: 'Sarah Johnson',
          benefitPlanId: 1,
          benefitPlanName: 'Premium Health Insurance',
          enrollmentDate: '2024-12-10',
          effectiveDate: '2025-01-01',
          coverageLevel: 'employee_spouse',
          dependents: 1,
          employerContribution: 800,
          employeeContribution: 150,
          status: 'active',
        },
        {
          id: 4,
          employeeId: 3,
          employeeName: 'Michael Wong',
          benefitPlanId: 1,
          benefitPlanName: 'Premium Health Insurance',
          enrollmentDate: '2024-12-12',
          effectiveDate: '2025-01-01',
          coverageLevel: 'employee_only',
          dependents: 0,
          employerContribution: 800,
          employeeContribution: 100,
          status: 'active',
        },
        {
          id: 5,
          employeeId: 4,
          employeeName: 'Emma Garcia',
          benefitPlanId: 4,
          benefitPlanName: 'Wellness Program',
          enrollmentDate: '2024-12-18',
          effectiveDate: '2025-01-01',
          employerContribution: 50,
          employeeContribution: 20,
          status: 'pending',
        }
      ];

      setBenefitPlans(sampleBenefitPlans);
      setEmployeeBenefits(sampleEmployeeBenefits);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter benefit plans based on search and filter
  const filteredBenefitPlans = benefitPlans.filter(plan => {
    const matchesSearch = 
      plan.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      plan.provider.toLowerCase().includes(searchTerm.toLowerCase()) ||
      plan.description.toLowerCase().includes(searchTerm.toLowerCase());
      
    const matchesType = filterBenefitType === 'all' || plan.benefitType === filterBenefitType;
    
    return matchesSearch && matchesType;
  });

  // Filter employee benefits based on search
  const filteredEmployeeBenefits = employeeBenefits.filter(benefit => {
    const matchesSearch = 
      benefit.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      benefit.benefitPlanName.toLowerCase().includes(searchTerm.toLowerCase());
      
    const matchesType = filterBenefitType === 'all' || 
      benefitPlans.find(plan => plan.id === benefit.benefitPlanId)?.benefitType === filterBenefitType;
    
    return matchesSearch && matchesType;
  });

  // Get benefit type display name
  const getBenefitTypeDisplay = (type: BenefitType) => {
    return type.replace(/_/g, ' ').split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Get calculation type display name
  const getCalculationTypeDisplay = (type: BenefitCalculationType) => {
    return type.replace(/_/g, ' ').split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Get coverage level display
  const getCoverageLevelDisplay = (level?: string) => {
    if (!level) return 'N/A';
    
    return level.replace(/_/g, ' ').split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Benefits Management</h3>
            <p className="text-sm text-gray-500">Manage employee benefits and enrollment</p>
          </div>

          <div className="mt-4 md:mt-0 flex flex-wrap gap-2">
            <button
              onClick={() => setActiveTab('plans')}
              className={`px-4 py-2 text-sm font-medium rounded-md ${
                activeTab === 'plans'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Heart className="h-4 w-4 inline-block mr-2" />
              Benefit Plans
            </button>
            <button
              onClick={() => setActiveTab('enrollments')}
              className={`px-4 py-2 text-sm font-medium rounded-md ${
                activeTab === 'enrollments'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Users className="h-4 w-4 inline-block mr-2" />
              Employee Enrollments
            </button>
            {isAdmin && (
              <button
                onClick={() => setShowAddPlanModal(true)}
                className="px-4 py-2 text-sm font-medium rounded-md bg-green-600 text-white hover:bg-green-700"
              >
                <Plus className="h-4 w-4 inline-block mr-2" />
                Add Benefit Plan
              </button>
            )}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-4 border-b border-gray-200">
            <div className="flex flex-wrap gap-4">
              <div className="flex-grow max-w-md">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder={`Search ${activeTab === 'plans' ? 'benefit plans' : 'enrollments'}...`}
                    className="pl-10 block w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 p-2 text-sm"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              
              <div>
                <select
                  className="block w-full border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 p-2 text-sm"
                  value={filterBenefitType}
                  onChange={(e) => setFilterBenefitType(e.target.value)}
                >
                  <option value="all">All Benefit Types</option>
                  {Object.values(BenefitType).map(type => (
                    <option key={type} value={type}>
                      {getBenefitTypeDisplay(type)}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <button
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </button>
              </div>
            </div>
          </div>

          {activeTab === 'plans' ? (
            /* Benefit Plans Tab */
            <div className="overflow-x-auto">
              {loading ? (
                <div className="flex justify-center items-center p-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : filteredBenefitPlans.length === 0 ? (
                <div className="p-8 text-center">
                  <div className="mb-2">
                    <AlertCircle className="h-10 w-10 text-gray-400 mx-auto" />
                  </div>
                  <h3 className="text-sm font-medium text-gray-900">No benefit plans found</h3>
                  <p className="text-sm text-gray-500 mt-1">
                    Try adjusting your search or filters, or add a new benefit plan.
                  </p>
                  {isAdmin && (
                    <button
                      onClick={() => setShowAddPlanModal(true)}
                      className="mt-4 px-4 py-2 text-sm font-medium rounded-md bg-blue-600 text-white hover:bg-blue-700"
                    >
                      <Plus className="h-4 w-4 inline-block mr-2" />
                      Add Benefit Plan
                    </button>
                  )}
                </div>
              ) : (
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Plan Name
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Provider
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Calculation
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Employer Contribution
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Employee Contribution
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredBenefitPlans.map((plan) => (
                      <tr key={plan.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                              <Heart className="h-4 w-4 text-blue-600" />
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">{plan.name}</div>
                              <div className="text-xs text-gray-500">{plan.planNumber || 'No plan number'}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                            {getBenefitTypeDisplay(plan.benefitType)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {plan.provider}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{getCalculationTypeDisplay(plan.calculationType)}</div>
                          <div className="text-xs text-gray-500">
                            {plan.calculationBase ? `Based on ${plan.calculationBase.replace('_', ' ')}` : ''}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {plan.calculationType === BenefitCalculationType.PERCENTAGE ? 
                            `${plan.employerContribution}%` : 
                            formatCurrency(plan.employerContribution, plan.currency)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {plan.calculationType === BenefitCalculationType.PERCENTAGE ? 
                            `${plan.employeeContribution}%` : 
                            formatCurrency(plan.employeeContribution, plan.currency)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            plan.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {plan.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button className="text-blue-600 hover:text-blue-900 mr-3">
                            Enrollments
                          </button>
                          {isAdmin && (
                            <>
                              <button className="text-indigo-600 hover:text-indigo-900 mr-3">
                                <Edit className="h-4 w-4 inline" />
                              </button>
                              <button className="text-red-600 hover:text-red-900">
                                <Trash2 className="h-4 w-4 inline" />
                              </button>
                            </>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          ) : (
            /* Employee Enrollments Tab */
            <div className="overflow-x-auto">
              {loading ? (
                <div className="flex justify-center items-center p-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : filteredEmployeeBenefits.length === 0 ? (
                <div className="p-8 text-center">
                  <div className="mb-2">
                    <AlertCircle className="h-10 w-10 text-gray-400 mx-auto" />
                  </div>
                  <h3 className="text-sm font-medium text-gray-900">No employee enrollments found</h3>
                  <p className="text-sm text-gray-500 mt-1">
                    Try adjusting your search or filters, or enroll an employee in a benefit plan.
                  </p>
                  {isAdmin && (
                    <button
                      onClick={() => setShowEnrollEmployeeModal(true)}
                      className="mt-4 px-4 py-2 text-sm font-medium rounded-md bg-blue-600 text-white hover:bg-blue-700"
                    >
                      <Plus className="h-4 w-4 inline-block mr-2" />
                      Enroll Employee
                    </button>
                  )}
                </div>
              ) : (
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Employee
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Benefit Plan
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Coverage Level
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Effective Date
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Employer Cost
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Employee Cost
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredEmployeeBenefits.map((benefit) => {
                      const benefitPlan = benefitPlans.find(plan => plan.id === benefit.benefitPlanId);
                      
                      return (
                        <tr key={benefit.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-8 w-8 bg-gray-100 rounded-full flex items-center justify-center">
                                <User className="h-4 w-4 text-gray-600" />
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">{benefit.employeeName}</div>
                                <div className="text-xs text-gray-500">ID: {benefit.employeeId}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{benefit.benefitPlanName}</div>
                            {benefitPlan && (
                              <div className="text-xs text-gray-500">{getBenefitTypeDisplay(benefitPlan.benefitType)}</div>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">{getCoverageLevelDisplay(benefit.coverageLevel)}</div>
                            {benefit.dependents && benefit.dependents > 0 && (
                              <div className="text-xs text-gray-500">{benefit.dependents} dependents</div>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDate(benefit.effectiveDate)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {benefitPlan?.calculationType === BenefitCalculationType.PERCENTAGE ? 
                              `${benefit.employerContribution}%` : 
                              formatCurrency(benefit.employerContribution, benefitPlan?.currency)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {benefitPlan?.calculationType === BenefitCalculationType.PERCENTAGE ? 
                              `${benefit.employeeContribution}%` : 
                              formatCurrency(benefit.employeeContribution, benefitPlan?.currency)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              benefit.status === 'active' ? 'bg-green-100 text-green-800' : 
                              benefit.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {benefit.status.charAt(0).toUpperCase() + benefit.status.slice(1)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button className="text-blue-600 hover:text-blue-900 mr-3">
                              View
                            </button>
                            {isAdmin && (
                              <>
                                <button className="text-indigo-600 hover:text-indigo-900 mr-3">
                                  <Edit className="h-4 w-4 inline" />
                                </button>
                                <button className="text-red-600 hover:text-red-900">
                                  <Trash2 className="h-4 w-4 inline" />
                                </button>
                              </>
                            )}
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BenefitsManagement; 