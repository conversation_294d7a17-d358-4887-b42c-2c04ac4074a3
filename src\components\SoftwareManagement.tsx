import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import {
  Search,
  Filter,
  Plus,
  Shield,
  Calendar,
  AlertCircle,
  Clock,
  Tag,
  Building2,
  Users,
  MoreVertical,
  Download,
  FileText,
  Settings,
  AlertTriangle,
  CheckCircle,
  XCircle,
  DollarSign,
  BarChart2,
  Globe,
  User,
  Grid,
  Eye,
  Edit,
  Trash2,
  X,
  KeyRound,
  MoreHorizontal,
  Laptop,
  Monitor,
  Server,
  Printer,
  HardDrive
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'react-hot-toast';
import { LicenseType, PaymentFrequency, SoftwareStatus } from '../types/SoftwareLicense';
import { useNavigate } from 'react-router-dom';
import { MouseEvent as ReactMouseEvent } from 'react';

// Utility function to safely render any value
const safeRender = (value: any): string => {
  if (value === null || value === undefined) return '';
  if (typeof value === 'string') return value;
  if (typeof value === 'number' || typeof value === 'boolean') return String(value);
  if (Array.isArray(value)) {
    // Return length instead of full JSON for arrays
    return `${value.length} items`;
  }
  if (typeof value === 'object') {
    // Return a simplified representation for objects instead of raw JSON
    return '[Object]';
  }
  return String(value);
};

interface SubscriptionInfo {
  type: 'perpetual' | 'subscription';
  billingCycle?: 'monthly' | 'quarterly' | 'annual';
  renewalDate?: string;
  autoRenew?: boolean;
  lastPayment?: {
    amount: number;
    date: string;
  };
  nextPayment?: {
    amount: number;
    date: string;
  };
}

interface SoftwareAssignment {
  userId: string;
  userName: string;
  department: string;
  assignedAt: string;
  assignedBy: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  department: string;
  isActive: boolean;
}

interface Asset {
  id: string;
  assetTag?: string;
  assetType?: string;
  manufacturer?: string;
  model?: string;
}

interface Software {
  id: string;
  name: string;
  vendor: string;
  version: string;
  type: string;
  platform: string;
  purchaseDate: string;
  expiryDate: string;
  status: 'active' | 'expired' | 'maintenance';
  assignments: SoftwareAssignment[];
  assignedUsers?: Array<{id: string; name?: string; email?: string; department?: string}>;
  linkedAssets?: Array<{id: string; assetTag?: string; assetType?: string; manufacturer?: string; model?: string}>;
  department: string;
  cost: number;
  seats: number;
  supportContact?: string;
  supportEmail?: string;
  supportUrl?: string;
  tags: string[];
  installationPath?: string;
  systemRequirements?: string;
  notes?: string;
  maintenanceSchedule?: string;
  subscription: SubscriptionInfo;
  renewalStatus?: 'active' | 'pending' | 'overdue' | 'cancelled';
  renewalAmount?: number;
  lastRenewalDate?: string;
  nextRenewalDate?: string;
  autoRenewal: boolean;
  billingContact?: {
    name: string;
    email: string;
    phone?: string;
  };
  costPKR: number;
}

interface Vendor {
  id: string;
  vendorName: string;
  companyName: string;
}

export function SoftwareManagement() {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [software, setSoftware] = useState<Software[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [upcomingRenewals, setUpcomingRenewals] = useState<Software[]>([]);
  const [overdueRenewals, setOverdueRenewals] = useState<Software[]>([]);
  const [viewType, setViewType] = useState<'table' | 'grid'>('table');
  const [viewingSoftware, setViewingSoftware] = useState<Software | null>(null);
  const [showViewModal, setShowViewModal] = useState(false);
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    expired: 0,
    maintenance: 0,
    subscription: 0,
    perpetual: 0,
    free: 0,
    totalCost: 0,
    totalCostPKR: 0,
    totalSeats: 0,
    upcomingRenewals: 0,
    overdueRenewals: 0,
    subscriptionCost: 0
  });
  const [activeRowId, setActiveRowId] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    loadSoftware();
    fetchVendors();
    fetchUsers();
  }, []);

  useEffect(() => {
    calculateStats();
  }, [software]);

  useEffect(() => {
    // Check for upcoming and overdue renewals
    const now = new Date();
    const thirtyDaysFromNow = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000));

    const upcoming = software.filter(sw => {
      if (!sw.expiryDate) return false;
      const expiryDate = new Date(sw.expiryDate);
      return expiryDate > now && expiryDate <= thirtyDaysFromNow;
    });

    const overdue = software.filter(sw => {
      if (!sw.expiryDate) return false;
      const expiryDate = new Date(sw.expiryDate);
      return expiryDate < now;
    });

    setUpcomingRenewals(upcoming);
    setOverdueRenewals(overdue);
  }, [software]);

  // Update click-outside handler
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (activeRowId && !(event.target as Element)?.closest('.action-dropdown')) {
        setActiveRowId(null);
      }
    }

    // Attach the event listener
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      // Clean up the event listener
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeRowId]);

  const formatVendorName = (vendor: any): string => {
    if (!vendor) return 'Unknown Vendor';
    if (typeof vendor === 'string') return vendor;
    if (typeof vendor === 'object') {
      return vendor.companyName || vendor.vendorName || 'Unknown Vendor';
    }
    return 'Unknown Vendor';
  };

  // Helper to ensure license type matches our form options
  const formatLicenseType = (type: string): string => {
    // Map to match the LicenseType enum from our form
    const typeMap: Record<string, string> = {
      'monthly': 'Monthly Subscription',
      'yearly': 'Yearly Subscription',
      'one-time': 'One-Time (Lifetime)',
      'trial': 'Trial',
      'freemium': 'Freemium',
      'open source': 'Open Source',
      'free': 'Free'
    };
    
    // Check if type already matches one of our form types exactly
    if (Object.values(typeMap).includes(type)) {
      return type;
    }
    
    // Try to match based on normalized lowercase value
    const lowercaseType = type.toLowerCase();
    for (const [key, value] of Object.entries(typeMap)) {
      if (lowercaseType.includes(key)) {
        return value;
      }
    }
    
    // Default to Yearly Subscription if we can't determine type
    return 'Yearly Subscription';
  };

  const loadSoftware = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await axios.get('/api/software-licenses');
      const licenses = response.data;
      
      // Map API response to our component's expected structure
      const formattedSoftware = licenses.map((license: any) => {
        // Normalize status to lowercase for consistency
        let status = license.status?.toLowerCase() || 'active';
        
        // Validate status is one of the expected values
        if (!['active', 'expired', 'maintenance', 'trial', 'blocked'].includes(status)) {
          status = 'active'; // Default to active if invalid status
        }
        
        return {
          id: license.id,
          name: license.name,
          vendor: license.vendor ? (license.vendor.companyName || license.vendor.vendorName) : 'Unknown Vendor',
          version: '1.0', // Default if not available
          type: license.type, // This should match LicenseType ('Monthly Subscription', 'Yearly Subscription', etc.)
          platform: license.category || 'All',
          purchaseDate: license.purchaseDate,
          expiryDate: license.expiryDate,
          status: status, // Normalized status
          seats: license.totalSeats,
          department: license.department,
          cost: typeof license.costUSD === 'number' ? license.costUSD : 
                typeof license.costUSD === 'string' ? parseFloat(license.costUSD) : 
                typeof license.costPKR === 'number' ? license.costPKR : 0,
          costPKR: typeof license.costPKR === 'number' ? license.costPKR :
                  typeof license.costPKR === 'string' ? parseFloat(license.costPKR) : 0,
          assignedUsers: license.assignedUsers || [],
          assignments: (license.assignedUsers || []).map((user: any) => ({
            userId: user.id,
            userName: user.name,
            department: user.department,
            assignedAt: new Date().toISOString(), // Default if not available
            assignedBy: 'System'
          })),
          linkedAssets: license.linkedAssets || [],
          tags: [license.category],
          autoRenewal: license.autoRenew,
          subscription: {
            type: license.type.includes('Subscription') ? 'subscription' : 'perpetual',
            billingCycle: license.paymentFrequency === 'Monthly' ? 'monthly' : 
                          license.paymentFrequency === 'Quarterly' ? 'quarterly' : 'annual',
            autoRenew: license.autoRenew
          }
        };
      });
      
      console.log('Loaded software licenses:', formattedSoftware);
      setSoftware(formattedSoftware);
    } catch (err) {
      console.error('Error loading software licenses:', err);
      setError('Failed to load software licenses');
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = () => {
    const now = new Date();
    const thirtyDaysFromNow = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000));

    const newStats = software.reduce((acc, sw) => {
      // Convert cost to number and ensure it's a valid number
      const cost = typeof sw.cost === 'number' ? sw.cost : 
                   typeof sw.cost === 'string' ? parseFloat(sw.cost) : 0;
      
      // Convert PKR cost to number and ensure it's a valid number
      const costPKR = typeof sw.costPKR === 'number' ? sw.costPKR : 
                     typeof sw.costPKR === 'string' ? parseFloat(sw.costPKR) : 0;
      
      // Count license types
      const licenseType = sw.type || '';
      const isSubscription = licenseType.toLowerCase().includes('subscription');
      const isPerpetual = licenseType.toLowerCase().includes('lifetime') || licenseType.toLowerCase().includes('one-time');
      const isFree = licenseType.toLowerCase().includes('free') || licenseType.toLowerCase().includes('open source');
      
      // Properly check for upcoming renewals
      let hasUpcomingRenewal = false;
      let hasOverdueRenewal = false;
      
      if (sw.expiryDate) {
        const expiryDate = new Date(sw.expiryDate);
        hasUpcomingRenewal = expiryDate > now && expiryDate <= thirtyDaysFromNow;
        hasOverdueRenewal = expiryDate < now;
      }
      
      // Fix status checks by normalizing to lowercase
      const status = typeof sw.status === 'string' ? sw.status.toLowerCase() : '';
      const isActive = status === 'active';
      const isExpired = status === 'expired';
      const isMaintenance = status === 'maintenance';
      
      // Debug logging for status
      console.log(`Software ${sw.name}: status = "${sw.status}", isActive = ${isActive}`);
      
      return {
      ...acc,
      total: acc.total + 1,
        active: acc.active + (isActive ? 1 : 0),
        expired: acc.expired + (isExpired ? 1 : 0),
        maintenance: acc.maintenance + (isMaintenance ? 1 : 0),
        subscription: acc.subscription + (isSubscription ? 1 : 0),
        perpetual: acc.perpetual + (isPerpetual ? 1 : 0),
        free: acc.free + (isFree ? 1 : 0),
        totalCost: acc.totalCost + cost,
        totalCostPKR: acc.totalCostPKR + costPKR,
      totalSeats: acc.totalSeats + (sw.seats || 0),
        upcomingRenewals: acc.upcomingRenewals + (hasUpcomingRenewal ? 1 : 0),
        overdueRenewals: acc.overdueRenewals + (hasOverdueRenewal ? 1 : 0),
      subscriptionCost: acc.subscriptionCost + (sw.subscription?.type === 'subscription' ? (sw.subscription.nextPayment?.amount || 0) : 0)
      };
    }, {
      total: 0,
      active: 0,
      expired: 0,
      maintenance: 0,
      subscription: 0,
      perpetual: 0,
      free: 0,
      totalCost: 0,
      totalCostPKR: 0,
      totalSeats: 0,
      upcomingRenewals: 0,
      overdueRenewals: 0,
      subscriptionCost: 0
    });

    console.log('Calculated stats:', newStats);
    setStats(newStats);
  };

  const fetchVendors = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/vendors');
      
      // More defensive approach to ensure valid vendor objects
      let validVendors: Vendor[] = [];
      
      if (response && response.data) {
        if (Array.isArray(response.data)) {
          validVendors = response.data
            .filter((vendor: any) => vendor && typeof vendor === 'object' && vendor.id)
            .map((vendor: any) => ({
              id: String(vendor.id),
              vendorName: typeof vendor.vendorName === 'string' ? vendor.vendorName : '',
              companyName: typeof vendor.companyName === 'string' ? vendor.companyName : ''
            }));
        }
      }
      
      setVendors(validVendors);
      setError(null);
    } catch (err: any) {
      console.error('Error fetching vendors:', err);
      let errorMessage = 'Failed to load vendors';
      
      if (err) {
        if (typeof err === 'string') {
          errorMessage = err;
        } else if (err.message && typeof err.message === 'string') {
          errorMessage = err.message;
        } else if (err.response && err.response.data) {
          if (typeof err.response.data.message === 'string') {
            errorMessage = err.response.data.message;
          } else if (typeof err.response.data.error === 'string') {
            errorMessage = err.response.data.error;
          }
        }
      }
      
      setError(errorMessage);
      setVendors([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/users', {
        params: {
          page: 1,
          limit: 100, // Get a large number of users for the dropdown
          sortBy: 'name',
          sortDirection: 'ASC'
        }
      });

      if (response.data.users && Array.isArray(response.data.users)) {
        // Filter out inactive users and set to state
        const activeUsers = response.data.users.filter((user: User) => user.isActive !== false);
        setUsers(activeUsers);
      } else {
        console.error('Invalid users data format:', response.data);
        setUsers([]);
      }
      setError(null);
    } catch (err: any) {
      console.error('Error fetching users:', err);
      // Ensure error is a string
      setError(typeof err === 'string' ? err : 'Failed to load users');
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'expired': return 'bg-red-100 text-red-800';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getExpiryDateStyle = (expiryDateStr: string | null) => {
    if (!expiryDateStr) return '';
    
    try {
      const expiryDate = new Date(expiryDateStr);
      const today = new Date();
      
      // Calculate the difference in days
      const diffTime = expiryDate.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      // If already expired or within 30 days, return red styling
      if (diffDays <= 30) {
        return diffDays <= 0 
          ? 'text-red-600 font-medium' // Already expired
          : 'text-orange-500 font-medium'; // Close to expiry
      }
    } catch (e) {
      console.error('Error parsing date:', e);
    }
    
    return ''; // Default - no special styling
  };

  const renderSubscriptionInfo = (sw: Software) => {
    if (sw.subscription?.type !== 'subscription') return null;

    const nextPayment = sw.subscription.nextPayment;
    const isOverdue = nextPayment && new Date(nextPayment.date) < new Date();

    return (
      <div className="mt-3 p-3 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Subscription Info</h4>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Billing Cycle:</span>
            <span className="font-medium capitalize">{safeRender(sw.subscription.billingCycle)}</span>
          </div>
          {nextPayment && (
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Next Payment:</span>
              <div className="text-right">
                <span className="font-medium">${safeRender(nextPayment.amount)}</span>
                <span className={`block text-xs ${isOverdue ? 'text-red-600' : 'text-gray-500'}`}>
                  {nextPayment.date ? new Date(nextPayment.date).toLocaleDateString() : 'N/A'}
                </span>
              </div>
            </div>
          )}
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Auto-Renewal:</span>
            <span className={`font-medium ${sw.autoRenewal ? 'text-green-600' : 'text-red-600'}`}>
              {sw.autoRenewal === true ? 'Enabled' : 'Disabled'}
            </span>
          </div>
        </div>
      </div>
    );
  };

  const filteredSoftware = software.filter(sw => {
    if (selectedStatus !== 'all' && sw.status !== selectedStatus) return false;
    if (selectedType !== 'all' && sw.type !== selectedType) return false;
    if (selectedDepartment !== 'all' && sw.department !== selectedDepartment) return false;

    if (searchQuery) {
      const search = searchQuery.toLowerCase();
      return (
        sw.name.toLowerCase().includes(search) ||
        sw.vendor.toLowerCase().includes(search) ||
        sw.version.toLowerCase().includes(search) ||
        sw.tags?.some(tag => tag.toLowerCase().includes(search))
      );
    }

    return true;
  });

  const viewSoftware = (sw: Software) => {
    setViewingSoftware(sw);
    setShowViewModal(true);
  };

  const editSoftware = (sw: Software) => {
    navigate(`/software/edit/${sw.id}`);
  };

  const deleteSoftware = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this software?')) {
      try {
        await axios.delete(`/api/software-licenses/${id}`);
        setSoftware(prev => prev.filter(s => s.id !== id));
        toast.success('Software license deleted successfully');
      } catch (error) {
        console.error('Error deleting software:', error);
        setError('Failed to delete software');
        toast.error('Failed to delete software');
      }
    }
  };

  const handleAddNewSoftware = () => {
    navigate('/software/add');
  };

  const toggleActionMenu = (event: ReactMouseEvent, id: string) => {
    event.preventDefault();
    event.stopPropagation();
    setActiveRowId(activeRowId === id ? null : id);
  };

  const handleAction = (action: 'view' | 'edit' | 'delete', sw: Software) => {
    if (action === 'view') {
      viewSoftware(sw);
    } else if (action === 'edit') {
      editSoftware(sw);
    } else if (action === 'delete') {
      deleteSoftware(sw.id);
    }
    setActiveRowId(null);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* Total Software Card */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Total Software</h3>
              <p className="text-3xl font-bold mt-2">
                {loading ? (
                  <span className="inline-block h-8 w-8 rounded-full border-2 border-t-transparent border-blue-500 animate-spin"></span>
                ) : (
                  safeRender(stats.total)
                )}
              </p>
              <div className="mt-2 space-y-1">
                <p className="text-sm flex items-center justify-between">
                  <span className="text-green-600 flex items-center">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Active
                  </span>
                  <span className="font-medium text-green-600">
                    {loading ? (
                      <span className="inline-block h-3 w-3 rounded-full border-2 border-t-transparent border-green-500 animate-spin"></span>
                    ) : (
                      <span className="text-base">{safeRender(stats.active)}</span>
                    )}
                  </span>
                </p>
                <p className="text-sm flex items-center justify-between">
                  <span className="text-red-600 flex items-center">
                    <XCircle className="h-3 w-3 mr-1" />
                    Expired
                  </span>
                  <span className="font-medium text-red-600">
                    {loading ? (
                      <span className="inline-block h-3 w-3 rounded-full border-2 border-t-transparent border-red-500 animate-spin"></span>
                    ) : (
                      safeRender(stats.expired)
                    )}
                  </span>
                </p>
                <p className="text-sm flex items-center justify-between">
                  <span className="text-yellow-600 flex items-center">
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    Renewal Due
                  </span>
                  <span className="font-medium text-yellow-600">
                    {loading ? (
                      <span className="inline-block h-3 w-3 rounded-full border-2 border-t-transparent border-yellow-500 animate-spin"></span>
                    ) : (
                      safeRender(overdueRenewals.length)
                    )}
                  </span>
                </p>
              </div>
              {stats.total === 0 && !loading && (
                <p className="text-xs text-gray-500 mt-2 italic">
                  No software licenses found. Add your first license.
                </p>
              )}
            </div>
            <div className={`p-3 rounded-lg ${stats.total > 0 ? 'bg-blue-50' : 'bg-gray-50'}`}>
              <Shield className={`h-6 w-6 ${stats.total > 0 ? 'text-blue-500' : 'text-gray-400'}`} />
            </div>
          </div>
        </div>

        {/* Software License Types Card */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-medium text-gray-900">License Types</h3>
              <div className="mt-2 space-y-1">
                <div className="flex justify-between items-center">
                  <span className="flex items-center text-sm text-indigo-600">
                    <span className="h-2 w-2 rounded-full bg-indigo-500 mr-2"></span>
                    Subscription
                  </span>
                  <span className="font-medium w-6 text-right">{software.filter(sw => sw.type.toLowerCase().includes('subscription')).length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="flex items-center text-sm text-green-600">
                    <span className="h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                    Perpetual
                  </span>
                  <span className="font-medium w-6 text-right">{software.filter(sw => sw.type.toLowerCase().includes('lifetime') || sw.type.toLowerCase().includes('one-time')).length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="flex items-center text-sm text-blue-600">
                    <span className="h-2 w-2 rounded-full bg-blue-500 mr-2"></span>
                    Free/Open Source
                  </span>
                  <span className="font-medium w-6 text-right">{software.filter(sw => sw.type.toLowerCase().includes('free') || sw.type.toLowerCase().includes('open source')).length}</span>
                </div>
              </div>
              <div className="mt-3 pt-2 border-t border-gray-100">
                <p className="text-xs text-gray-600 flex items-center">
                  <Calendar className="h-3 w-3 mr-1" />
                  <span className="text-indigo-600 font-medium">{software.filter(sw => sw.autoRenewal).length}</span> auto-renewal enabled
              </p>
            </div>
            </div>
            <div className="bg-indigo-50 p-3 rounded-lg">
              <KeyRound className="h-6 w-6 text-indigo-500" />
            </div>
          </div>
        </div>

        {/* Total Seats Card */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Total Seats</h3>
              <p className="text-3xl font-bold mt-2">{safeRender(stats.totalSeats)}</p>
              <div className="space-y-1 mt-2">
                <p className="text-sm flex items-center justify-between">
                  <span className="text-gray-600">Total</span>
                  <span className="font-medium">{safeRender(stats.totalSeats)}</span>
                </p>
                <p className="text-sm flex items-center justify-between">
                  <span className="text-blue-600">Assigned</span>
                  <span className="font-medium">{software.reduce((total, sw) => total + (Array.isArray(sw.assignedUsers) ? sw.assignedUsers.length : 0), 0)}</span>
                </p>
                <p className="text-sm flex items-center justify-between">
                  <span className="text-green-600">Available</span>
                  <span className="font-medium">{Math.max(0, stats.totalSeats - software.reduce((total, sw) => total + (Array.isArray(sw.assignedUsers) ? sw.assignedUsers.length : 0), 0))}</span>
              </p>
            </div>
              <p className="text-xs text-gray-500 mt-2 flex items-center">
                <Users className="h-3 w-3 mr-1" />
                <span className="text-purple-600 font-medium">{software.reduce((departments, sw) => departments.add(sw.department), new Set()).size}</span> departments using licenses
              </p>
            </div>
            <div className="bg-purple-50 p-3 rounded-lg">
              <Users className="h-6 w-6 text-purple-500" />
            </div>
          </div>
        </div>

        {/* Combined Cost Card - USD and PKR */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-medium text-gray-900">Total Cost</h3>
              <div className="mt-2 space-y-2">
                <div className="flex items-start">
                  <span className="text-sm text-gray-600 w-12">USD</span>
                  <p className="text-2xl font-bold text-green-600 leading-none">${Math.round(stats.totalCost).toLocaleString()}</p>
                </div>
                <div className="flex items-start">
                  <span className="text-sm text-gray-600 w-12">PKR</span>
                  <p className="text-2xl font-bold text-blue-600 leading-none">Rs {Math.round(stats.totalCostPKR).toLocaleString()}</p>
                </div>
              </div>
              <div className="mt-2 pt-2 border-t border-gray-100 space-y-1">
                <p className="text-xs text-gray-600 flex items-center justify-between">
                  <span>Avg. cost per seat</span>
                  <span className="font-medium text-teal-600">
                    ${stats.totalSeats > 0 ? Math.round(stats.totalCost / stats.totalSeats) : 0}
                  </span>
                </p>
                <p className="text-xs text-gray-600 flex items-center justify-between">
                  <span>Subscriptions</span>
                  <span className="font-medium text-teal-600">
                    ${Math.round(software.filter(sw => sw.type.toLowerCase().includes('subscription')).reduce((total, sw) => total + (sw.cost || 0), 0))}
                  </span>
              </p>
            </div>
            </div>
            <div className="bg-gradient-to-r from-green-50 to-blue-50 p-3 rounded-lg">
              <DollarSign className="h-6 w-6 text-teal-500" />
            </div>
          </div>
        </div>
      </div>

      <div>
        {/* Search and filter bar with view toggle */}
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex flex-wrap items-center justify-between gap-4 mb-4">
            <h2 className="text-xl font-semibold text-gray-900">Software Licenses</h2>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setViewType('table')}
                className={`p-2 rounded hover:bg-gray-100 ${viewType === 'table' ? 'bg-blue-100 text-blue-600' : 'text-gray-500'}`}
                title="Table View"
              >
                <FileText className="h-5 w-5" />
              </button>
              <button
                onClick={() => setViewType('grid')}
                className={`p-2 rounded hover:bg-gray-100 ${viewType === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-500'}`}
                title="Grid View"
              >
                <Grid className="h-5 w-5" />
              </button>
              <button
                onClick={handleAddNewSoftware}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <Plus className="h-5 w-5" />
                <span>Add Software</span>
              </button>
            </div>
          </div>

          {/* Search and filters */}
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search software by name, vendor, version, or tags..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="border border-gray-300 rounded-lg py-2 px-3 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="expired">Expired</option>
              <option value="trial">Trial</option>
            </select>
            
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="border border-gray-300 rounded-lg py-2 px-3 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Types</option>
              <option value="Monthly Subscription">Monthly Subscription</option>
              <option value="Yearly Subscription">Yearly Subscription</option>
              <option value="One-Time (Lifetime)">One-Time (Lifetime)</option>
              <option value="Trial">Trial</option>
              <option value="Freemium">Freemium</option>
              <option value="Open Source">Open Source</option>
              <option value="Free">Free</option>
            </select>
            
            <select
              value={selectedDepartment}
              onChange={(e) => setSelectedDepartment(e.target.value)}
              className="border border-gray-300 rounded-lg py-2 px-3 bg-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Departments</option>
              <option value="Information Technology">Information Technology</option>
              <option value="Human Resources">Human Resources</option>
              <option value="Finance">Finance</option>
              <option value="Marketing">Marketing</option>
              <option value="Sales">Sales</option>
              <option value="Operations">Operations</option>
              <option value="Customer Service">Customer Service</option>
              <option value="Land">Land</option>
              <option value="Legal">Legal</option>
              <option value="Management">Management</option>
              <option value="Planning & Development">Planning & Development</option>
            </select>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 text-red-800 p-4 rounded-lg flex items-center gap-2 mt-4">
            <AlertCircle className="h-5 w-5 flex-shrink-0" />
            <p>{safeRender(error)}</p>
          </div>
        )}

        {/* Renewal Alerts */}
            {upcomingRenewals.length > 0 && (
          <div className="space-y-4 mt-4">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center gap-2 text-yellow-800 mb-2">
                  <Clock className="h-5 w-5" />
                  <h3 className="font-medium">Upcoming Renewals</h3>
                </div>
                <div className="space-y-2">
                  {upcomingRenewals.map(sw => (
                    <div key={sw.id} className="flex items-center justify-between text-sm">
                      <span className="text-yellow-700">{safeRender(sw.name)}</span>
                      <span className="text-yellow-600">
                      Due: {sw.expiryDate ? new Date(sw.expiryDate).toLocaleDateString() : 'N/A'}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
          </div>
        )}

        {/* Loading State */}
        {loading ? (
          <div className="flex items-center justify-center py-8 mt-4">
            <div className="h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
          </div>
        ) : (
          <>
            {/* Table View */}
            {viewType === 'table' && (
              <div className="overflow-x-auto mt-4 bg-white rounded-lg shadow">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">
                        Software Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">
                        Vendor
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">
                        License Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">
                        Department
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">
                        Cost
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">
                        Seats
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">
                        Assigned Users
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">
                        Purchase Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">
                        Expiry Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-bold text-gray-900 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredSoftware.map((sw) => (
                      <tr key={sw.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {safeRender(sw.name)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {safeRender(sw.vendor)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {safeRender(formatLicenseType(sw.type))}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {safeRender(sw.department)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          ${typeof sw.cost === 'number' ? sw.cost.toLocaleString(undefined, {minimumFractionDigits: 0, maximumFractionDigits: 2}) : '0'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {safeRender(sw.seats)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {Array.isArray(sw.assignedUsers) 
                            ? `${sw.assignedUsers.length} / ${sw.seats}`
                            : `0 / ${sw.seats}`}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {sw.purchaseDate && typeof sw.purchaseDate === 'string' 
                            ? new Date(sw.purchaseDate).toLocaleDateString() 
                            : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {sw.expiryDate && typeof sw.expiryDate === 'string' 
                            ? <span className={getExpiryDateStyle(sw.expiryDate)}>
                                {new Date(sw.expiryDate).toLocaleDateString()}
                                {getExpiryDateStyle(sw.expiryDate) === 'text-red-600 font-medium' && 
                                  <span className="ml-1 text-xs bg-red-100 text-red-800 px-1.5 py-0.5 rounded-full">!</span>
                                }
                                {getExpiryDateStyle(sw.expiryDate) === 'text-orange-500 font-medium' &&
                                  <span className="ml-1 text-xs bg-orange-100 text-orange-800 px-1.5 py-0.5 rounded-full">!</span>
                                }
                              </span>
                            : 'No expiry'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`px-2.5 py-0.5 text-xs font-medium rounded-full ${getStatusColor(safeRender(sw.status))}`}>
                            {safeRender(sw.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div style={{ position: 'relative' }}>
                            <button 
                              onClick={(e) => toggleActionMenu(e, sw.id)}
                              className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:bg-gray-100"
                            >
                              <MoreHorizontal className="h-5 w-5" />
                            </button>
                            
                            {activeRowId === sw.id && (
                              <div 
                                onClick={(e) => e.stopPropagation()}
                                className="action-dropdown"
                                style={{
                                  position: 'absolute',
                                  right: 0,
                                  top: '100%',
                                  zIndex: 50,
                                  backgroundColor: 'white',
                                  borderRadius: '0.375rem',
                                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                                  border: '1px solid #e5e7eb',
                                  width: '160px'
                                }}
                              >
                                <div className="py-1">
                            <button 
                                    onClick={(e) => {
                                      e.preventDefault();
                                      e.stopPropagation();
                                      handleAction('view', sw);
                                    }}
                                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                                  >
                                    <Eye className="h-4 w-4 mr-2 text-blue-600" />
                                    View Details
                            </button>
                            <button 
                                    onClick={(e) => {
                                      e.preventDefault();
                                      e.stopPropagation();
                                      handleAction('edit', sw);
                                    }}
                                    className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                                  >
                                    <Edit className="h-4 w-4 mr-2 text-indigo-600" />
                                    Edit
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.preventDefault();
                                      e.stopPropagation();
                                      handleAction('delete', sw);
                                    }}
                                    className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 flex items-center"
                                  >
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Delete
                            </button>
                                </div>
                              </div>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            {/* Grid View */}
            {viewType === 'grid' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mt-4">
                {filteredSoftware.map((sw) => (
                  <div
                    key={sw.id}
                    className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow"
                  >
                    <div className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <div className="p-2 bg-blue-100 rounded-lg">
                            <Shield className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900">{safeRender(sw.name)}</h3>
                            <p className="text-sm text-gray-500">{safeRender(sw.vendor)}</p>
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          <button 
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              viewSoftware(sw);
                            }} 
                            className="text-blue-600 hover:text-blue-900"
                            title="View Details"
                          >
                            <Eye className="h-5 w-5" />
                          </button>
                          <button 
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              editSoftware(sw);
                            }} 
                            className="text-indigo-600 hover:text-indigo-900"
                            title="Edit"
                          >
                            <Edit className="h-5 w-5" />
                          </button>
                          <button 
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              deleteSoftware(sw.id);
                            }} 
                            className="text-red-600 hover:text-red-900"
                            title="Delete"
                          >
                            <Trash2 className="h-5 w-5" />
                          </button>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Tag className="h-4 w-4" />
                          <span>Version {safeRender(sw.version)}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Globe className="h-4 w-4" />
                          <span>{safeRender(sw.platform || 'All Platforms')}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Users className="h-4 w-4" />
                          <span>{safeRender(sw.assignments?.length || 0)} of {safeRender(sw.seats)} seats assigned</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <Building2 className="h-4 w-4" />
                          <span>{safeRender(sw.department)}</span>
                        </div>

                        {/* Assignments Preview */}
                        {sw.assignments && sw.assignments.length > 0 && (
                          <div className="mt-3 space-y-2">
                            <p className="text-sm font-medium text-gray-700">Assigned to:</p>
                            <div className="space-y-1">
                              {sw.assignments.slice(0, 3).map((assignment, index) => (
                                <div key={index} className="flex items-center justify-between text-sm">
                                  <div className="flex items-center gap-2">
                                    <User className="h-4 w-4 text-gray-500" />
                                    <span className="text-gray-600">{safeRender(assignment.userName)}</span>
                                  </div>
                                  <span className="text-gray-500 text-xs">
                                    {safeRender(assignment.department)}
                                  </span>
                                </div>
                              ))}
                              {sw.assignments.length > 3 && (
                                <p className="text-xs text-gray-500">
                                  +{safeRender(sw.assignments.length - 3)} more
                                </p>
                              )}
                            </div>
                          </div>
                        )}

                        {sw.tags && sw.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {sw.tags.map((tag, index) => (
                              <span
                                key={index}
                                className="px-2 py-0.5 text-xs bg-gray-100 text-gray-600 rounded-full"
                              >
                                {safeRender(tag)}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>

                      {/* Subscription Info */}
                      {renderSubscriptionInfo(sw)}

                      <div className="mt-4 pt-4 border-t border-gray-100">
                        <div className="flex items-center justify-between">
                          <span className={`px-2.5 py-0.5 text-xs font-medium rounded-full ${getStatusColor(safeRender(sw.status))}`}>
                            {safeRender(sw.status)}
                          </span>
                          <div className="flex items-center gap-1 text-sm">
                            <Calendar className="h-4 w-4" />
                            {sw.expiryDate && typeof sw.expiryDate === 'string' ? (
                              <span className={getExpiryDateStyle(sw.expiryDate)}>
                                {new Date(sw.expiryDate).toLocaleDateString()}
                                {getExpiryDateStyle(sw.expiryDate) === 'text-red-600 font-medium' && 
                                  <span className="ml-1 text-xs bg-red-100 text-red-800 px-1.5 py-0.5 rounded-full">!</span>
                                }
                                {getExpiryDateStyle(sw.expiryDate) === 'text-orange-500 font-medium' &&
                                  <span className="ml-1 text-xs bg-orange-100 text-orange-800 px-1.5 py-0.5 rounded-full">!</span>
                                }
                            </span>
                            ) : (
                              <span className="text-gray-500">No expiry</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </div>

      {/* Software View Modal */}
      {showViewModal && viewingSoftware && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
              {/* Header with software name and close button */}
              <div className="bg-gray-50 px-6 py-4 flex justify-between items-center border-b border-gray-200">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Shield className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">{viewingSoftware.name}</h3>
                    <p className="text-sm text-gray-500">{viewingSoftware.vendor}</p>
                  </div>
                </div>
                      <button
                        onClick={() => setShowViewModal(false)}
                  className="text-gray-400 hover:text-gray-500 p-2 rounded-full hover:bg-gray-100"
                      >
                        <X className="h-5 w-5" />
                      </button>
                    </div>

              <div className="bg-white p-6">
                {/* Status and Key Information Row */}
                <div className="flex flex-wrap gap-4 mb-6">
                  <div className="flex items-center gap-2 px-3 py-1.5 bg-gray-50 rounded-lg">
                    <span className={`w-3 h-3 rounded-full ${
                      viewingSoftware.status === 'active' ? 'bg-green-500' : 
                      viewingSoftware.status === 'expired' ? 'bg-red-500' : 'bg-yellow-500'
                    }`}></span>
                    <span className="text-sm font-medium capitalize">{viewingSoftware.status}</span>
                      </div>
                  
                  <div className="flex items-center gap-2 px-3 py-1.5 bg-gray-50 rounded-lg">
                    <Tag className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">{viewingSoftware.type}</span>
                      </div>
                  
                  <div className="flex items-center gap-2 px-3 py-1.5 bg-gray-50 rounded-lg">
                    <Building2 className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">{viewingSoftware.department}</span>
                      </div>
                  
                  <div className="flex items-center gap-2 px-3 py-1.5 bg-gray-50 rounded-lg">
                    <Users className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">
                      {viewingSoftware.assignedUsers?.length || 0} / {viewingSoftware.seats} seats
                          </span>
                      </div>
                </div>

                {/* Main content layout with two columns */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Left column: Details */}
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <FileText className="h-5 w-5 text-blue-600" />
                      <span>License Details</span>
                    </h4>
                    
                    <div className="space-y-4 bg-gray-50 p-4 rounded-lg">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-xs text-gray-500 uppercase">Purchase Date</p>
                          <p className="font-medium text-gray-900">
                            {viewingSoftware.purchaseDate && typeof viewingSoftware.purchaseDate === 'string'
                              ? new Date(viewingSoftware.purchaseDate).toLocaleDateString()
                              : 'N/A'}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 uppercase">Expiry Date</p>
                          <p className="font-medium text-gray-900">
                            {viewingSoftware.expiryDate && typeof viewingSoftware.expiryDate === 'string'
                              ? <span className={getExpiryDateStyle(viewingSoftware.expiryDate)}>
                                  {new Date(viewingSoftware.expiryDate).toLocaleDateString()}
                                  {getExpiryDateStyle(viewingSoftware.expiryDate) === 'text-red-600 font-medium' && 
                                    <span className="ml-2 text-xs bg-red-100 text-red-800 px-2 py-0.5 rounded-full">Expired</span>
                                  }
                                  {getExpiryDateStyle(viewingSoftware.expiryDate) === 'text-orange-500 font-medium' &&
                                    <span className="ml-2 text-xs bg-orange-100 text-orange-800 px-2 py-0.5 rounded-full">Expiring Soon</span>
                                  }
                                </span>
                              : 'No expiry'}
                          </p>
                        </div>
                      </div>
                      
                      <div className="pt-3 border-t border-gray-200">
                        <p className="text-xs text-gray-500 uppercase mb-2">Cost</p>
                        <div className="flex items-center gap-4">
                          <div className="bg-green-50 rounded-lg px-3 py-2 flex-1">
                            <p className="text-xs text-gray-500">USD</p>
                            <p className="font-semibold text-gray-900">${viewingSoftware.cost}</p>
                        </div>
                          <div className="bg-blue-50 rounded-lg px-3 py-2 flex-1">
                            <p className="text-xs text-gray-500">PKR</p>
                            <p className="font-semibold text-gray-900">Rs {viewingSoftware.costPKR || 0}</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="pt-3 border-t border-gray-200">
                        <p className="text-xs text-gray-500 uppercase mb-2">Auto-Renewal</p>
                        <div className={`flex items-center gap-2 ${viewingSoftware.autoRenewal ? 'text-green-600' : 'text-red-600'}`}>
                          {viewingSoftware.autoRenewal ? (
                            <>
                              <CheckCircle className="h-5 w-5" />
                              <span className="font-medium">Enabled</span>
                            </>
                          ) : (
                            <>
                              <XCircle className="h-5 w-5" />
                              <span className="font-medium">Disabled</span>
                            </>
                          )}
                                </div>
                                </div>
                      
                      {viewingSoftware.tags && viewingSoftware.tags.length > 0 && (
                        <div className="pt-3 border-t border-gray-200">
                          <p className="text-xs text-gray-500 uppercase mb-2">Tags</p>
                          <div className="flex flex-wrap gap-1">
                            {viewingSoftware.tags.map((tag, index) => (
                              <span
                                key={index}
                                className="px-2 py-0.5 text-xs bg-gray-200 text-gray-700 rounded-full"
                              >
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                        )}
                      </div>
                      
                      {/* Linked Assets Section */}
                    <div className="mt-6">
                      <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                        <Laptop className="h-5 w-5 text-purple-600" />
                        <span>Linked Assets</span>
                        {viewingSoftware.linkedAssets && viewingSoftware.linkedAssets.length > 0 && (
                          <span className="bg-purple-100 text-purple-800 text-xs px-2 py-0.5 rounded-full">
                            {viewingSoftware.linkedAssets.length}
                          </span>
                        )}
                      </h4>
                      
                        {viewingSoftware.linkedAssets && viewingSoftware.linkedAssets.length > 0 ? (
                        <div className="space-y-2 bg-gray-50 p-4 rounded-lg max-h-60 overflow-y-auto">
                            {viewingSoftware.linkedAssets.map((asset, index) => (
                            <div 
                              key={index} 
                              className="bg-white p-3 rounded-lg border border-gray-200 flex items-center gap-3"
                            >
                              <div className="w-10 h-10 rounded-lg bg-purple-100 flex items-center justify-center text-purple-600">
                                {asset.assetType?.toLowerCase().includes('laptop') ? <Laptop className="h-5 w-5" /> :
                                 asset.assetType?.toLowerCase().includes('desktop') ? <Monitor className="h-5 w-5" /> :
                                 asset.assetType?.toLowerCase().includes('server') ? <Server className="h-5 w-5" /> :
                                 asset.assetType?.toLowerCase().includes('printer') ? <Printer className="h-5 w-5" /> :
                                 <HardDrive className="h-5 w-5" />}
                                </div>
                                <div>
                                <p className="font-medium text-gray-900">
                                  {asset.assetTag || `Asset #${asset.id}`}
                                  </p>
                                  <p className="text-xs text-gray-500">
                                  {asset.manufacturer && asset.model 
                                    ? `${asset.manufacturer} ${asset.model}`
                                    : asset.assetType || 'Hardware'}
                                  </p>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                        <div className="bg-gray-50 p-4 rounded-lg text-center">
                          <p className="text-sm text-gray-500">No assets linked to this software</p>
                        </div>
                        )}
                    </div>
                      </div>
                      
                  {/* Right column: Assigned Users */}
                        <div>
                    <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                      <Users className="h-5 w-5 text-blue-600" />
                      <span>Assigned Users</span>
                      {viewingSoftware.assignedUsers && viewingSoftware.assignedUsers.length > 0 && (
                        <span className="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full">
                          {viewingSoftware.assignedUsers.length}
                        </span>
                      )}
                    </h4>
                    
                    {viewingSoftware.assignedUsers && viewingSoftware.assignedUsers.length > 0 ? (
                      <div className="space-y-3 bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
                        {viewingSoftware.assignedUsers.map((user, index) => (
                          <div 
                            key={index} 
                            className="bg-white p-3 rounded-lg border border-gray-200 flex items-center justify-between"
                          >
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium">
                                {user.name ? user.name.charAt(0) : (user.id ? 'U' : '?')}
                        </div>
                        <div>
                                <p className="font-medium text-gray-900">
                                  {user.name || `User ID: ${user.id || 'Unknown'}`}
                                </p>
                                <p className="text-xs text-gray-500">
                                  {user.email || 'No email available'}
                          </p>
                        </div>
                      </div>
                            {user.department && (
                              <span className="text-xs px-2 py-1 bg-gray-100 rounded-lg text-gray-600">
                                {user.department}
                              </span>
                            )}
                          </div>
                            ))}
                          </div>
                    ) : (
                      <div className="bg-gray-50 p-4 rounded-lg text-center">
                        <p className="text-sm text-gray-500">No users assigned to this software</p>
                        </div>
                      )}
                    
                    {/* Subscription Information */}
                    {viewingSoftware.subscription && (
                      <div className="mt-6">
                        <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                          <Calendar className="h-5 w-5 text-green-600" />
                          <span>Subscription Details</span>
                        </h4>
                        
                        <div className="bg-gray-50 p-4 rounded-lg space-y-3">
                          <div className="flex items-center justify-between">
                            <p className="text-sm text-gray-600">Type</p>
                            <span className="font-medium text-gray-900 capitalize">
                              {viewingSoftware.subscription.type || 'Unknown'}
                            </span>
                    </div>
                          
                          {viewingSoftware.subscription.billingCycle && (
                            <div className="flex items-center justify-between">
                              <p className="text-sm text-gray-600">Billing Cycle</p>
                              <span className="font-medium text-gray-900 capitalize">
                                {viewingSoftware.subscription.billingCycle}
                              </span>
                  </div>
                          )}
                          
                          {viewingSoftware.subscription.nextPayment && (
                            <div className="flex items-center justify-between">
                              <p className="text-sm text-gray-600">Next Payment</p>
                              <div className="text-right">
                                <p className="font-medium text-gray-900">
                                  ${viewingSoftware.subscription.nextPayment.amount || 0}
                                </p>
                                <p className="text-xs text-gray-500">
                                  {viewingSoftware.subscription.nextPayment.date ? 
                                    new Date(viewingSoftware.subscription.nextPayment.date).toLocaleDateString() : 
                                    'Not scheduled'}
                                </p>
                </div>
              </div>
                          )}
                          
                          {viewingSoftware.subscription.renewalDate && (
                            <div className="flex items-center justify-between">
                              <p className="text-sm text-gray-600">Renewal Date</p>
                              <span className="font-medium text-gray-900">
                                {new Date(viewingSoftware.subscription.renewalDate).toLocaleDateString()}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-6 py-4 border-t border-gray-200 flex justify-end">
                <div className="flex gap-3">
                <button
                  type="button"
                    className="inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                  onClick={() => setShowViewModal(false)}
                >
                  Close
                </button>
                  <button
                    type="button"
                    className="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700"
                    onClick={() => {
                      setShowViewModal(false);
                      editSoftware(viewingSoftware);
                    }}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit License
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}