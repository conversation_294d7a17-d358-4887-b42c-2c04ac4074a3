import { DataSource } from 'typeorm';

// Database configuration
const AppDataSource = new DataSource({
  type: "mysql",
  host: process.env.DB_HOST || "localhost",
  port: parseInt(process.env.DB_PORT || "3306"),
  username: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "root",
  database: process.env.DB_NAME || "ims_db",
  synchronize: false,
  logging: true,
  entities: [],
  subscribers: [],
  migrations: []
});

const addMissingColumns = async () => {
  console.log("Adding missing columns to users table...");

  try {
    // Initialize the data source
    await AppDataSource.initialize();
    console.log("Database connection established");

    // Get query runner
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    
    // Add role column if it doesn't exist
    const hasRole = await queryRunner.hasColumn('users', 'role');
    if (!hasRole) {
        await queryRunner.query(`
            ALTER TABLE \`users\` 
            ADD COLUMN \`role\` VARCHAR(20) NOT NULL DEFAULT 'EMPLOYEE' AFTER \`password\`
        `);
        console.log("✅ Added role column");
    } else {
        console.log("ℹ️ role column already exists");
    }

    // Add updatedAt column if it doesn't exist  
    const hasUpdatedAt = await queryRunner.hasColumn('users', 'updatedAt');
    if (!hasUpdatedAt) {
        await queryRunner.query(`
            ALTER TABLE \`users\` 
            ADD COLUMN \`updatedAt\` TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) AFTER \`createdAt\`
        `);
        console.log("✅ Added updatedAt column");
    } else {
        console.log("ℹ️ updatedAt column already exists");
    }
    
    console.log("\n📋 Current users table schema:");
    const columns = await queryRunner.query(`DESCRIBE \`users\``);
    columns.forEach((column: any, index: number) => {
      console.log(`${index + 1}. ${column.Field} (${column.Type}) ${column.Null === 'NO' ? 'NOT NULL' : 'NULL'}`);
    });
    
    await queryRunner.release();
    console.log("\n✅ Schema update completed successfully!");
    
  } catch (error) {
    console.error("❌ Error:", error);
  } finally {
    try {
      await AppDataSource.destroy();
      console.log("Database connection closed");
    } catch (error) {
      console.error("Error closing database connection:", error);
    }
  }
};

// Run the update
addMissingColumns(); 