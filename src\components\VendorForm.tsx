import React, { useState, useEffect } from 'react';
import {
  Activity,
  Briefcase,
  Building2,
  Clock,
  Cloud,
  Database,
  FileCheck,
  Gauge,
  Globe,
  Layers,
  Link,
  Mail,
  MapPin,
  Package,
  Phone,
  Plus,
  Save,
  Settings,
  Shield,
  Tag,
  Trash2,
  TrendingUp,
  User,
  Users,
  X,
  Zap
} from 'lucide-react';

//=============================================================================
// Type Definitions and Interfaces - Basic Types
//=============================================================================
export interface Address {
    street: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
    telephone?: string;
  }
  
  export interface Country {
    code: string;
    name: string;
  }
  
  // Add countries list
  const countries: Country[] = [
    { code: 'PK', name: 'Pakistan' },
    { code: 'US', name: 'United States' },
    { code: 'GB', name: 'United Kingdom' },
    { code: 'CA', name: 'Canada' },
    { code: 'AU', name: 'Australia' },
    { code: 'DE', name: 'Germany' },
    { code: 'FR', name: 'France' },
    { code: 'IT', name: 'Italy' },
    { code: 'ES', name: 'Spain' },
    { code: 'JP', name: 'Japan' },
    { code: 'CN', name: 'China' },
    { code: 'IN', name: 'India' },
    { code: 'BR', name: 'Brazil' },
    { code: 'RU', name: 'Russia' },
    { code: 'ZA', name: 'South Africa' },
    { code: 'AE', name: 'United Arab Emirates' },
    { code: 'SA', name: 'Saudi Arabia' },
    { code: 'SG', name: 'Singapore' },
    { code: 'MY', name: 'Malaysia' }
  ].sort((a, b) => a.name.localeCompare(b.name));
  
  export interface ContactPerson {
    name: string;
    title: string;
    email: string;
    phone: string;
    alternativePhone?: string;
    department?: string;
  }
  
  //=============================================================================
  // Vendor Type Definitions
  //=============================================================================
  export type MainCategory = 
    | 'IT_HARDWARE_INFRASTRUCTURE'
    | 'INTERNET_NETWORKING'
    | 'CLOUD_HOSTING'
    | 'BUSINESS_SOFTWARE'
    | 'CALL_CENTER'
    | 'IT_SECURITY'
    | 'IT_CONSULTANCY';
  
  export type VendorType = 
    // IT Hardware & Infrastructure
    | 'HARDWARE_SUPPLIER'
    | 'PRINTER_PHOTOCOPIER_SUPPLIER'
    | 'SERVER_STORAGE_SUPPLIER'
    | 'CCTV_SURVEILLANCE_SUPPLIER'
    | 'POWER_UPS_SUPPLIER'
    | 'IT_ACCESSORIES_SUPPLIER'
    // Internet & Networking
    | 'ISP'
    | 'NETWORKING_EQUIPMENT_SUPPLIER'
    | 'FIREWALL_SECURITY_VENDOR'
    | 'VPN_SECURE_ACCESS_PROVIDER'
    // Cloud & Hosting
    | 'CLOUD_SERVICE_PROVIDER'
    | 'DOMAIN_HOSTING_PROVIDER'
    | 'BACKUP_DISASTER_RECOVERY'
    // Business Software & IT Solutions
    | 'ERP_VENDOR'
    | 'CRM_VENDOR'
    | 'HRMS_PAYROLL_VENDOR'
    | 'POS_RETAIL_SOFTWARE'
    | 'FINANCIAL_ACCOUNTING_SOFTWARE'
    // Call Center & Communication
    | 'PBX_VOIP_PROVIDER'
    | 'CALL_CENTER_SOFTWARE'
    | 'TELECOM_SERVICES'
    // IT Security & Cybersecurity
    | 'CYBERSECURITY_VENDOR'
    | 'ANTIVIRUS_ENDPOINT_PROTECTION'
    | 'IDENTITY_ACCESS_MANAGEMENT'
    // IT Consultancy & Development
    | 'IT_CONSULTANCY'
    | 'SOFTWARE_DEVELOPMENT'
    | 'BLOCKCHAIN_WEB3';
  
  export type VendorStatus = 'ACTIVE' | 'INACTIVE' | 'BLACKLISTED' | 'PENDING_REVIEW';
  
  //=============================================================================
  // Main Vendor Interface
  //=============================================================================
  export interface Vendor {
    id: string;
    vendorName: string;
    companyName: string;
    website?: string;
    contactPerson: ContactPerson;
    alternativeContacts?: ContactPerson[];
    address: Address;
    vendorType: VendorType;
    status: VendorStatus;
    contract: {
      startDate: string;
      expiryDate: string;
      renewalReminder?: boolean;
      autoRenewal?: boolean;
      terminationNotice: number;
    };
    performance: {
      rating: number;
      responseTime: number;
      deliveryScore: number;
      qualityScore: number;
    };
    financial: {
      spending: {
        current: {
          value: number;
          currency: string;
        };
        trend: number;
      };
    };
  }
  
  //=============================================================================
  // Vendor Type-Specific Fields Interface
  // TO BE MOVED: This section should be moved to the new VendorForm file
  //=============================================================================
  interface VendorTypeFields {
    // IT Hardware & Infrastructure
    hardwareSupplierFields?: {
      brands?: string[];
      productCategories?: string[];
      warranty?: string;
      inventory?: number;
      supportAvailability?: '24x7' | 'business-hours';
      deliveryTimeframe?: string;
    };
    printerFields?: {
      brands?: string[];
      supplyTypes?: string[];
      maintenanceServices?: boolean;
      rentingOptions?: boolean;
      colorSupport?: boolean;
      maxPrintSpeed?: string;
    };
    serverStorageFields?: {
      storageCapacity?: string;
      rackUnits?: number;
      supportTier?: string;
      redundancyOptions?: string[];
      backupSolutions?: string[];
      powerEfficiency?: string;
    };
    cctvFields?: {
      cameraTypes?: string[];
      storageRetention?: string;
      monitoring?: '24x7' | 'business-hours';
      resolution?: string[];
      nightVision?: boolean;
      motionDetection?: boolean;
    };
    powerUpsFields?: {
      capacity?: string;
      backupTime?: string;
      maintenanceSchedule?: string;
      batteryType?: string;
      autoShutdown?: boolean;
      remoteManagement?: boolean;
    };
    accessoriesFields?: {
      categories?: string[];
      brands?: string[];
      warranty?: string;
      bulkDiscounts?: boolean;
      customization?: boolean;
      stockAvailability?: number;
    };
  
    // Internet & Networking
    ispFields?: {
      bandwidth?: string;
      coverage?: string[];
      serviceLevel?: string;
      uptime?: string;
      staticIP?: boolean;
      ddosProtection?: boolean;
      connectionType?: 'shared' | 'dedicated';
      internetMedium?: string[];
    };
    networkingEquipmentFields?: {
      equipmentTypes?: string[];
      supportLevel?: string;
      response?: string;
      brands?: string[];
      certification?: string[];
      installationService?: boolean;
    };
    firewallFields?: {
      throughput?: string;
      features?: string[];
      certifications?: string[];
      managementOptions?: string[];
      updateFrequency?: string;
      threatPrevention?: boolean;
    };
    vpnFields?: {
      protocols?: string[];
      endpoints?: number;
      encryption?: string;
      geographicCoverage?: string[];
      multiFactorAuth?: boolean;
      mobileSupport?: boolean;
    };
  
    // Cloud & Hosting
    cloudServiceFields?: {
      services?: string[];
      dataLocations?: string[];
      compliance?: string[];
      scalingOptions?: string[];
      supportTier?: string;
      apiAccess?: boolean;
    };
    domainHostingFields?: {
      hostingTypes?: string[];
      storageSpace?: string;
      bandwidth?: string;
      sslCertificates?: boolean;
      emailAccounts?: number;
      backupFrequency?: string;
    };
    backupRecoveryFields?: {
      backupTypes?: string[];
      retentionPeriod?: string;
      recoveryTime?: string;
      encryptionLevel?: string;
      offlineBackup?: boolean;
      testingFrequency?: string;
    };
  
    // Business Software & IT Solutions
    erpFields?: {
      modules?: string[];
      deploymentType?: 'cloud' | 'on-premise' | 'hybrid';
      userLimit?: number;
      customization?: boolean;
      integrations?: string[];
      mobileAccess?: boolean;
    };
    crmFields?: {
      features?: string[];
      integrations?: string[];
      apiAvailable?: boolean;
      customReports?: boolean;
      automationSupport?: boolean;
      userLimit?: number;
    };
    hrmsFields?: {
      modules?: string[];
      employeeLimit?: number;
      payrollRegions?: string[];
      biometric?: boolean;
      mobileApp?: boolean;
      selfService?: boolean;
    };
    posFields?: {
      terminals?: number;
      features?: string[];
      hardwareSupport?: boolean;
      paymentMethods?: string[];
      inventoryManagement?: boolean;
      offlineMode?: boolean;
    };
    financialSoftwareFields?: {
      modules?: string[];
      taxCompliance?: string[];
      multiCurrency?: boolean;
      reportingFeatures?: string[];
      bankIntegrations?: string[];
      auditTrail?: boolean;
    };
  
    // Call Center & Communication
    pbxVoipFields?: {
      channels?: number;
      features?: string[];
      callRecording?: boolean;
      queueManagement?: boolean;
      ivr?: boolean;
      mobileIntegration?: boolean;
    };
    callCenterFields?: {
      seats?: number;
      features?: string[];
      channels?: string[];
      analytics?: boolean;
      crmIntegration?: boolean;
      qualityMonitoring?: boolean;
    };
    telecomFields?: {
      services?: string[];
      coverage?: string[];
      bandwidth?: string;
      dedicatedSupport?: boolean;
      redundancy?: boolean;
      slaLevel?: string;
    };
  
    // IT Security & Cybersecurity
    cybersecurityFields?: {
      services?: string[];
      certifications?: string[];
      responseTime?: string;
      monitoringType?: '24x7' | 'business-hours';
      threatIntelligence?: boolean;
      complianceSupport?: string[];
    };
    antivirusFields?: {
      protectionTypes?: string[];
      endpoints?: number;
      updateFrequency?: string;
      managementConsole?: boolean;
      aiPowered?: boolean;
      mobileProtection?: boolean;
    };
    identityAccessFields?: {
      authMethods?: string[];
      userLimit?: number;
      ssoSupport?: boolean;
      passwordPolicy?: boolean;
      biometricSupport?: boolean;
      auditLogging?: boolean;
    };
  
    // IT Consultancy & Development
    consultancyFields?: {
      expertise?: string[];
      certifications?: string[];
      teamSize?: number;
      methodology?: string[];
      onsite?: boolean;
      projectTypes?: string[];
    };
    softwareDevFields?: {
      technologies?: string[];
      teamSize?: number;
      methodology?: string;
      projectTypes?: string[];
      codeOwnership?: boolean;
      maintenanceSupport?: boolean;
    };
    blockchainFields?: {
      platforms?: string[];
      smartContracts?: boolean;
      consensus?: string[];
      security?: string[];
      scalability?: string;
      tokenTypes?: string[];
    };
  }
  
  //=============================================================================
  // Vendor Form Interfaces
  // TO BE MOVED: This section should be moved to the new VendorForm file
  //=============================================================================
  interface VendorFormData {
    vendorName: string;
    companyName: string;
    website: string;
    mainCategory: MainCategory;
    vendorType: VendorType;
    status: VendorStatus;
    typeSpecificFields: VendorTypeFields;
    address: Address;
    contactPerson: ContactPerson;
    alternativeContacts?: ContactPerson[];
  }
  
  //=============================================================================
  // Main Vendor Form Component
  //=============================================================================
  export const VendorForm = ({ onClose, onSubmit, vendors = [], vendor = null }: { 
    onClose: () => void, 
    onSubmit: (vendor: Vendor) => void,
    vendors?: Vendor[],
    vendor?: Vendor | null
  }) => {
    const [formData, setFormData] = useState<VendorFormData>({
      vendorName: vendor?.vendorName || '',
      companyName: vendor?.companyName || '',
      website: vendor?.website || '',
      mainCategory: 'IT_HARDWARE_INFRASTRUCTURE', // We'll set this based on vendor type below
      vendorType: vendor?.vendorType || 'HARDWARE_SUPPLIER',
      status: vendor?.status || 'ACTIVE',
      typeSpecificFields: {},
      address: {
        street: vendor?.address?.street || '',
        city: vendor?.address?.city || '',
        state: vendor?.address?.state || '',
        country: vendor?.address?.country || '',
        postalCode: vendor?.address?.postalCode || '',
        telephone: vendor?.address?.telephone || ''
      },
      contactPerson: {
        name: vendor?.contactPerson?.name || '',
        title: vendor?.contactPerson?.title || '',
        email: vendor?.contactPerson?.email || '',
        phone: vendor?.contactPerson?.phone || '',
        department: vendor?.contactPerson?.department || '',
      },
      alternativeContacts: vendor?.alternativeContacts || []
    });
    
    // Set the main category based on vendor type if editing
    useEffect(() => {
      if (vendor && vendor.vendorType) {
        // Find the main category for this vendor type
        const category = Object.entries(categoryMap).find(([_, categoryData]) => 
          categoryData.types.some(type => type.value === vendor.vendorType)
        );
        
        if (category) {
          setFormData(prev => ({
            ...prev,
            mainCategory: category[0] as MainCategory
          }));
        }
      }
    }, [vendor]);
  
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-[800px] max-h-[90vh] overflow-y-auto">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-semibold text-gray-800">
              {vendor ? 'Edit Vendor' : 'Add New Vendor'}
            </h2>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              <X className="h-6 w-6" />
            </button>
          </div>
  
          <form onSubmit={(e) => {
            e.preventDefault();
            onSubmit({
              id: vendor?.id || `V${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
              vendorName: formData.vendorName,
              companyName: formData.companyName,
              website: formData.website || undefined,
              contactPerson: {
                name: formData.contactPerson.name,
                title: formData.contactPerson.title,
                email: formData.contactPerson.email,
                phone: formData.contactPerson.phone,
                department: formData.contactPerson.department || undefined,
              },
              alternativeContacts: formData.alternativeContacts,
              address: {
                street: formData.address.street,
                city: formData.address.city,
                state: formData.address.state,
                country: formData.address.country,
                postalCode: formData.address.postalCode,
                telephone: formData.address.telephone || undefined
              },
              vendorType: formData.vendorType,
              status: formData.status,
              contract: vendor?.contract || {
                startDate: new Date().toISOString().split('T')[0],
                expiryDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],
                renewalReminder: false,
                autoRenewal: false,
                terminationNotice: 30
              },
              performance: vendor?.performance || {
                rating: 0,
                responseTime: 0,
                deliveryScore: 0,
                qualityScore: 0
              },
              financial: vendor?.financial || {
                spending: {
                  current: {
                    value: 0,
                    currency: 'PKR'
                  },
                  trend: 0
                }
              }
            } as Vendor);
          }}>
            {/* Vendor Details Section */}
            <div className="bg-gray-50 p-4 rounded-lg mb-6">
              <div className="flex items-center mb-4">
                <Settings className="h-5 w-5 text-gray-500 mr-2" />
                <h3 className="text-lg font-medium text-gray-700">Vendor Details</h3>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    <Tag className="h-4 w-4 inline mr-1 text-gray-500" />
                    Main Category
                  </label>
                  <select
                    required
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    value={formData.mainCategory}
                    onChange={(e) => {
                      const newMainCategory = e.target.value as MainCategory;
                      setFormData({
                        ...formData,
                        mainCategory: newMainCategory,
                        vendorType: categoryMap[newMainCategory].types[0].value
                      });
                    }}
                  >
                    <option value="">Select Main Category</option>
                    {Object.entries(categoryMap).map(([value, { label }]) => (
                      <option key={value} value={value}>{label}</option>
                    ))}
                  </select>
                </div>
  
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    <Tag className="h-4 w-4 inline mr-1 text-gray-500" />
                    Subcategory
                  </label>
                  <select
                    required
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    value={formData.vendorType}
                    onChange={(e) => setFormData({ ...formData, vendorType: e.target.value as VendorType })}
                    disabled={!formData.mainCategory}
                  >
                    <option value="">Select Subcategory</option>
                    {formData.mainCategory && categoryMap[formData.mainCategory].types.map(({ value, label }) => (
                      <option key={value} value={value}>{label}</option>
                    ))}
                  </select>
                </div>
  
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">
                    <Activity className="h-4 w-4 inline mr-1 text-gray-500" />
                    Status
                  </label>
                  <select
                    required
                    className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                    value={formData.status}
                    onChange={(e) => setFormData({ ...formData, status: e.target.value as VendorStatus })}
                  >
                    <option value="">Select Status</option>
                    <option value="ACTIVE">Active</option>
                    <option value="INACTIVE">Inactive</option>
                    <option value="PENDING_REVIEW">Pending Review</option>
                    <option value="BLACKLISTED">Blacklisted</option>
                  </select>
                </div>
              </div>
            </div>
  
            {/* Show Company Information only after category and subcategory are selected */}
            {formData.mainCategory && formData.vendorType && (
              <>
                {/* Category Specific Fields */}
                <div className="bg-gray-50 p-4 rounded-lg mb-6">
                  <div className="flex items-center mb-4">
                    <Settings className="h-5 w-5 text-gray-500 mr-2" />
                    <h3 className="text-lg font-medium text-gray-700">Category Specific Details</h3>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    {/* Hardware Supplier Fields */}
                    {formData.vendorType === 'HARDWARE_SUPPLIER' && (
                      <>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Tag className="h-4 w-4 inline mr-1 text-gray-500" />
                            Brands Offered
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., Dell, HP, Lenovo"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.hardwareSupplierFields?.brands?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                hardwareSupplierFields: {
                                  ...formData.typeSpecificFields.hardwareSupplierFields,
                                  brands: e.target.value.split(',').map(b => b.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Package className="h-4 w-4 inline mr-1 text-gray-500" />
                            Product Categories
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., Laptops, Desktops, Servers"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.hardwareSupplierFields?.productCategories?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                hardwareSupplierFields: {
                                  ...formData.typeSpecificFields.hardwareSupplierFields,
                                  productCategories: e.target.value.split(',').map(c => c.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Shield className="h-4 w-4 inline mr-1 text-gray-500" />
                            Warranty Period
                          </label>
                          <select
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.hardwareSupplierFields?.warranty || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                hardwareSupplierFields: {
                                  ...formData.typeSpecificFields.hardwareSupplierFields,
                                  warranty: e.target.value
                                }
                              }
                            })}
                          >
                            <option value="">Select Warranty Period</option>
                            <option value="1_YEAR">1 Year</option>
                            <option value="2_YEARS">2 Years</option>
                            <option value="3_YEARS">3 Years</option>
                            <option value="5_YEARS">5 Years</option>
                          </select>
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Clock className="h-4 w-4 inline mr-1 text-gray-500" />
                            Support Availability
                          </label>
                          <select
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.hardwareSupplierFields?.supportAvailability || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                hardwareSupplierFields: {
                                  ...formData.typeSpecificFields.hardwareSupplierFields,
                                  supportAvailability: e.target.value as '24x7' | 'business-hours'
                                }
                              }
                            })}
                          >
                            <option value="">Select Support Hours</option>
                            <option value="24x7">24x7</option>
                            <option value="business-hours">Business Hours</option>
                          </select>
                        </div>
                      </>
                    )}
  
                    {/* Printer & Photocopier Fields */}
                    {formData.vendorType === 'PRINTER_PHOTOCOPIER_SUPPLIER' && (
                      <>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Tag className="h-4 w-4 inline mr-1 text-gray-500" />
                            Printer Brands
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., HP, Canon, Epson"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.printerFields?.brands?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                printerFields: {
                                  ...formData.typeSpecificFields.printerFields,
                                  brands: e.target.value.split(',').map(b => b.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Package className="h-4 w-4 inline mr-1 text-gray-500" />
                            Supply Types
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., Toner, Ink, Drums"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.printerFields?.supplyTypes?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                printerFields: {
                                  ...formData.typeSpecificFields.printerFields,
                                  supplyTypes: e.target.value.split(',').map(s => s.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Gauge className="h-4 w-4 inline mr-1 text-gray-500" />
                            Max Print Speed
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., 40 ppm"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.printerFields?.maxPrintSpeed || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                printerFields: {
                                  ...formData.typeSpecificFields.printerFields,
                                  maxPrintSpeed: e.target.value
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Settings className="h-4 w-4 inline mr-1 text-gray-500" />
                            Additional Features
                          </label>
                          <div className="space-y-2">
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.printerFields?.maintenanceServices || false}
                                onChange={(e) => setFormData({
                                  ...formData,
                                  typeSpecificFields: {
                                    ...formData.typeSpecificFields,
                                    printerFields: {
                                      ...formData.typeSpecificFields.printerFields,
                                      maintenanceServices: e.target.checked
                                    }
                                  }
                                })}
                              />
                              <span className="ml-2 text-sm text-gray-600">Maintenance Services</span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.printerFields?.rentingOptions || false}
                                onChange={(e) => setFormData({
                                  ...formData,
                                  typeSpecificFields: {
                                    ...formData.typeSpecificFields,
                                    printerFields: {
                                      ...formData.typeSpecificFields.printerFields,
                                      rentingOptions: e.target.checked
                                    }
                                  }
                                })}
                              />
                              <span className="ml-2 text-sm text-gray-600">Renting Options</span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.printerFields?.colorSupport || false}
                                onChange={(e) => setFormData({
                                  ...formData,
                                  typeSpecificFields: {
                                    ...formData.typeSpecificFields,
                                    printerFields: {
                                      ...formData.typeSpecificFields.printerFields,
                                      colorSupport: e.target.checked
                                    }
                                  }
                                })}
                              />
                              <span className="ml-2 text-sm text-gray-600">Color Support</span>
                            </label>
                          </div>
                        </div>
                      </>
                    )}
  
                    {/* Server & Storage Fields */}
                    {formData.vendorType === 'SERVER_STORAGE_SUPPLIER' && (
                      <>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Database className="h-4 w-4 inline mr-1 text-gray-500" />
                            Storage Capacity Range
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., 1TB - 100TB"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.serverStorageFields?.storageCapacity || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                serverStorageFields: {
                                  ...formData.typeSpecificFields.serverStorageFields,
                                  storageCapacity: e.target.value
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Shield className="h-4 w-4 inline mr-1 text-gray-500" />
                            Support Tier
                          </label>
                          <select
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.serverStorageFields?.supportTier || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                serverStorageFields: {
                                  ...formData.typeSpecificFields.serverStorageFields,
                                  supportTier: e.target.value
                                }
                              }
                            })}
                          >
                            <option value="">Select Support Tier</option>
                            <option value="basic">Basic (9x5)</option>
                            <option value="premium">Premium (24x7)</option>
                            <option value="enterprise">Enterprise (24x7 + Dedicated)</option>
                          </select>
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Settings className="h-4 w-4 inline mr-1 text-gray-500" />
                            Redundancy Options
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., RAID, Backup, Replication"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.serverStorageFields?.redundancyOptions?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                serverStorageFields: {
                                  ...formData.typeSpecificFields.serverStorageFields,
                                  redundancyOptions: e.target.value.split(',').map(r => r.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Zap className="h-4 w-4 inline mr-1 text-gray-500" />
                            Power Efficiency
                          </label>
                          <select
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.serverStorageFields?.powerEfficiency || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                serverStorageFields: {
                                  ...formData.typeSpecificFields.serverStorageFields,
                                  powerEfficiency: e.target.value
                                }
                              }
                            })}
                          >
                            <option value="">Select Power Efficiency Rating</option>
                            <option value="standard">Standard</option>
                            <option value="energy-star">Energy Star Certified</option>
                            <option value="80-plus">80 PLUS Certified</option>
                            <option value="80-plus-titanium">80 PLUS Titanium</option>
                          </select>
                        </div>
                      </>
                    )}
  
                    {/* ISP Fields */}
                    {formData.vendorType === 'ISP' && (
                      <>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Activity className="h-4 w-4 inline mr-1 text-gray-500" />
                            Bandwidth Range
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., 1Mbps - 10Gbps"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.ispFields?.bandwidth || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                ispFields: {
                                  ...formData.typeSpecificFields.ispFields,
                                  bandwidth: e.target.value
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Globe className="h-4 w-4 inline mr-1 text-gray-500" />
                            Coverage Areas
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., Karachi, Lahore, Islamabad"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.ispFields?.coverage?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                ispFields: {
                                  ...formData.typeSpecificFields.ispFields,
                                  coverage: e.target.value.split(',').map(c => c.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Clock className="h-4 w-4 inline mr-1 text-gray-500" />
                            Service Level Agreement
                          </label>
                          <select
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.ispFields?.uptime || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                ispFields: {
                                  ...formData.typeSpecificFields.ispFields,
                                  uptime: e.target.value
                                }
                              }
                            })}
                          >
                            <option value="">Select Uptime SLA</option>
                            <option value="99.9">99.9% (Basic)</option>
                            <option value="99.95">99.95% (Premium)</option>
                            <option value="99.99">99.99% (Enterprise)</option>
                          </select>
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Shield className="h-4 w-4 inline mr-1 text-gray-500" />
                            Additional Features
                          </label>
                          <div className="space-y-2">
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.ispFields?.staticIP || false}
                                onChange={(e) => setFormData({
                                  ...formData,
                                  typeSpecificFields: {
                                    ...formData.typeSpecificFields,
                                    ispFields: {
                                      ...formData.typeSpecificFields.ispFields,
                                      staticIP: e.target.checked
                                    }
                                  }
                                })}
                              />
                              <span className="ml-2 text-sm text-gray-600">Static IP Available</span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.ispFields?.ddosProtection || false}
                                onChange={(e) => setFormData({
                                  ...formData,
                                  typeSpecificFields: {
                                    ...formData.typeSpecificFields,
                                    ispFields: {
                                      ...formData.typeSpecificFields.ispFields,
                                      ddosProtection: e.target.checked
                                    }
                                  }
                                })}
                              />
                              <span className="ml-2 text-sm text-gray-600">DDoS Protection</span>
                            </label>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Settings className="h-4 w-4 inline mr-1 text-gray-500" />
                            Connection Type
                          </label>
                          <select
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.ispFields?.connectionType || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                ispFields: {
                                  ...formData.typeSpecificFields.ispFields,
                                  connectionType: e.target.value as 'shared' | 'dedicated'
                                }
                              }
                            })}
                          >
                            <option value="">Select Connection Type</option>
                            <option value="shared">Shared</option>
                            <option value="dedicated">Dedicated</option>
                          </select>
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Link className="h-4 w-4 inline mr-1 text-gray-500" />
                            Internet Medium
                          </label>
                          <div className="space-y-2">
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.ispFields?.internetMedium?.includes('fiber') || false}
                                onChange={(e) => {
                                  const currentMediums = formData.typeSpecificFields.ispFields?.internetMedium || [];
                                  const updatedMediums = e.target.checked 
                                    ? [...currentMediums, 'fiber']
                                    : currentMediums.filter(m => m !== 'fiber');
                                  setFormData({
                                    ...formData,
                                    typeSpecificFields: {
                                      ...formData.typeSpecificFields,
                                      ispFields: {
                                        ...formData.typeSpecificFields.ispFields,
                                        internetMedium: updatedMediums
                                      }
                                    }
                                  });
                                }}
                              />
                              <span className="ml-2 text-sm text-gray-600">Fiber</span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.ispFields?.internetMedium?.includes('wireless') || false}
                                onChange={(e) => {
                                  const currentMediums = formData.typeSpecificFields.ispFields?.internetMedium || [];
                                  const updatedMediums = e.target.checked 
                                    ? [...currentMediums, 'wireless']
                                    : currentMediums.filter(m => m !== 'wireless');
                                  setFormData({
                                    ...formData,
                                    typeSpecificFields: {
                                      ...formData.typeSpecificFields,
                                      ispFields: {
                                        ...formData.typeSpecificFields.ispFields,
                                        internetMedium: updatedMediums
                                      }
                                    }
                                  });
                                }}
                              />
                              <span className="ml-2 text-sm text-gray-600">Wireless</span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.ispFields?.internetMedium?.includes('copper') || false}
                                onChange={(e) => {
                                  const currentMediums = formData.typeSpecificFields.ispFields?.internetMedium || [];
                                  const updatedMediums = e.target.checked 
                                    ? [...currentMediums, 'copper']
                                    : currentMediums.filter(m => m !== 'copper');
                                  setFormData({
                                    ...formData,
                                    typeSpecificFields: {
                                      ...formData.typeSpecificFields,
                                      ispFields: {
                                        ...formData.typeSpecificFields.ispFields,
                                        internetMedium: updatedMediums
                                      }
                                    }
                                  });
                                }}
                              />
                              <span className="ml-2 text-sm text-gray-600">Copper</span>
                            </label>
                          </div>
                        </div>
                      </>
                    )}
  
                    {/* Cloud Service Provider Fields */}
                    {formData.vendorType === 'CLOUD_SERVICE_PROVIDER' && (
                      <>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Cloud className="h-4 w-4 inline mr-1 text-gray-500" />
                            Cloud Services
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., IaaS, PaaS, SaaS"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.cloudServiceFields?.services?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                cloudServiceFields: {
                                  ...formData.typeSpecificFields.cloudServiceFields,
                                  services: e.target.value.split(',').map(s => s.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <MapPin className="h-4 w-4 inline mr-1 text-gray-500" />
                            Data Center Locations
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., US East, Europe, Asia"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.cloudServiceFields?.dataLocations?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                cloudServiceFields: {
                                  ...formData.typeSpecificFields.cloudServiceFields,
                                  dataLocations: e.target.value.split(',').map(l => l.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Shield className="h-4 w-4 inline mr-1 text-gray-500" />
                            Compliance Standards
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., ISO 27001, SOC 2, GDPR"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.cloudServiceFields?.compliance?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                cloudServiceFields: {
                                  ...formData.typeSpecificFields.cloudServiceFields,
                                  compliance: e.target.value.split(',').map(c => c.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Settings className="h-4 w-4 inline mr-1 text-gray-500" />
                            Additional Features
                          </label>
                          <div className="space-y-2">
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.cloudServiceFields?.apiAccess || false}
                                onChange={(e) => setFormData({
                                  ...formData,
                                  typeSpecificFields: {
                                    ...formData.typeSpecificFields,
                                    cloudServiceFields: {
                                      ...formData.typeSpecificFields.cloudServiceFields,
                                      apiAccess: e.target.checked
                                    }
                                  }
                                })}
                              />
                              <span className="ml-2 text-sm text-gray-600">API Access</span>
                            </label>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <TrendingUp className="h-4 w-4 inline mr-1 text-gray-500" />
                            Scaling Options
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., Auto-scaling, Vertical, Horizontal"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.cloudServiceFields?.scalingOptions?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                cloudServiceFields: {
                                  ...formData.typeSpecificFields.cloudServiceFields,
                                  scalingOptions: e.target.value.split(',').map(s => s.trim())
                                }
                              }
                            })}
                          />
                        </div>
                      </>
                    )}
  
                    {/* ERP Vendor Fields */}
                    {formData.vendorType === 'ERP_VENDOR' && (
                      <>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Layers className="h-4 w-4 inline mr-1 text-gray-500" />
                            ERP Modules
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., Finance, HR, Inventory, CRM"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.erpFields?.modules?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                erpFields: {
                                  ...formData.typeSpecificFields.erpFields,
                                  modules: e.target.value.split(',').map(m => m.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Cloud className="h-4 w-4 inline mr-1 text-gray-500" />
                            Deployment Type
                          </label>
                          <select
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.erpFields?.deploymentType || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                erpFields: {
                                  ...formData.typeSpecificFields.erpFields,
                                  deploymentType: e.target.value as 'cloud' | 'on-premise' | 'hybrid'
                                }
                              }
                            })}
                          >
                            <option value="">Select Deployment Type</option>
                            <option value="cloud">Cloud-based</option>
                            <option value="on-premise">On-Premise</option>
                            <option value="hybrid">Hybrid</option>
                          </select>
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Users className="h-4 w-4 inline mr-1 text-gray-500" />
                            User Limit
                          </label>
                          <input
                            type="number"
                            placeholder="e.g., 100"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.erpFields?.userLimit || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                erpFields: {
                                  ...formData.typeSpecificFields.erpFields,
                                  userLimit: parseInt(e.target.value)
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Link className="h-4 w-4 inline mr-1 text-gray-500" />
                            Integrations
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., Salesforce, QuickBooks, Shopify"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.erpFields?.integrations?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                erpFields: {
                                  ...formData.typeSpecificFields.erpFields,
                                  integrations: e.target.value.split(',').map(i => i.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Settings className="h-4 w-4 inline mr-1 text-gray-500" />
                            Additional Features
                          </label>
                          <div className="space-y-2">
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.erpFields?.customization || false}
                                onChange={(e) => setFormData({
                                  ...formData,
                                  typeSpecificFields: {
                                    ...formData.typeSpecificFields,
                                    erpFields: {
                                      ...formData.typeSpecificFields.erpFields,
                                      customization: e.target.checked
                                    }
                                  }
                                })}
                              />
                              <span className="ml-2 text-sm text-gray-600">Customization Available</span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.erpFields?.mobileAccess || false}
                                onChange={(e) => setFormData({
                                  ...formData,
                                  typeSpecificFields: {
                                    ...formData.typeSpecificFields,
                                    erpFields: {
                                      ...formData.typeSpecificFields.erpFields,
                                      mobileAccess: e.target.checked
                                    }
                                  }
                                })}
                              />
                              <span className="ml-2 text-sm text-gray-600">Mobile Access</span>
                            </label>
                          </div>
                        </div>
                      </>
                    )}
  
                    {/* CCTV & Surveillance Fields */}
                    {formData.vendorType === 'CCTV_SURVEILLANCE_SUPPLIER' && (
                      <>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Tag className="h-4 w-4 inline mr-1 text-gray-500" />
                            Camera Types
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., IP Cameras, PTZ, Dome Cameras"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.cctvFields?.cameraTypes?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                cctvFields: {
                                  ...formData.typeSpecificFields.cctvFields,
                                  cameraTypes: e.target.value.split(',').map(t => t.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Clock className="h-4 w-4 inline mr-1 text-gray-500" />
                            Storage Retention
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., 30 days"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.cctvFields?.storageRetention || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                cctvFields: {
                                  ...formData.typeSpecificFields.cctvFields,
                                  storageRetention: e.target.value
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Settings className="h-4 w-4 inline mr-1 text-gray-500" />
                            Additional Features
                          </label>
                          <div className="space-y-2">
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.cctvFields?.nightVision || false}
                                onChange={(e) => setFormData({
                                  ...formData,
                                  typeSpecificFields: {
                                    ...formData.typeSpecificFields,
                                    cctvFields: {
                                      ...formData.typeSpecificFields.cctvFields,
                                      nightVision: e.target.checked
                                    }
                                  }
                                })}
                              />
                              <span className="ml-2 text-sm text-gray-600">Night Vision</span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.cctvFields?.motionDetection || false}
                                onChange={(e) => setFormData({
                                  ...formData,
                                  typeSpecificFields: {
                                    ...formData.typeSpecificFields,
                                    cctvFields: {
                                      ...formData.typeSpecificFields.cctvFields,
                                      motionDetection: e.target.checked
                                    }
                                  }
                                })}
                              />
                              <span className="ml-2 text-sm text-gray-600">Motion Detection</span>
                            </label>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Clock className="h-4 w-4 inline mr-1 text-gray-500" />
                            Monitoring Hours
                          </label>
                          <select
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.cctvFields?.monitoring || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                cctvFields: {
                                  ...formData.typeSpecificFields.cctvFields,
                                  monitoring: e.target.value as '24x7' | 'business-hours'
                                }
                              }
                            })}
                          >
                            <option value="">Select Monitoring Hours</option>
                            <option value="24x7">24x7</option>
                            <option value="business-hours">Business Hours</option>
                          </select>
                        </div>
                      </>
                    )}
  
                    {/* Network Equipment Fields */}
                    {formData.vendorType === 'NETWORKING_EQUIPMENT_SUPPLIER' && (
                      <>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Package className="h-4 w-4 inline mr-1 text-gray-500" />
                            Equipment Types
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., Routers, Switches, Access Points"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.networkingEquipmentFields?.equipmentTypes?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                networkingEquipmentFields: {
                                  ...formData.typeSpecificFields.networkingEquipmentFields,
                                  equipmentTypes: e.target.value.split(',').map(t => t.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Tag className="h-4 w-4 inline mr-1 text-gray-500" />
                            Brands
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., Cisco, HP, Juniper"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.networkingEquipmentFields?.brands?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                networkingEquipmentFields: {
                                  ...formData.typeSpecificFields.networkingEquipmentFields,
                                  brands: e.target.value.split(',').map(b => b.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Shield className="h-4 w-4 inline mr-1 text-gray-500" />
                            Support Level
                          </label>
                          <select
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.networkingEquipmentFields?.supportLevel || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                networkingEquipmentFields: {
                                  ...formData.typeSpecificFields.networkingEquipmentFields,
                                  supportLevel: e.target.value
                                }
                              }
                            })}
                          >
                            <option value="">Select Support Level</option>
                            <option value="basic">Basic</option>
                            <option value="premium">Premium</option>
                            <option value="enterprise">Enterprise</option>
                          </select>
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Settings className="h-4 w-4 inline mr-1 text-gray-500" />
                            Installation Service
                          </label>
                          <div className="space-y-2">
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.networkingEquipmentFields?.installationService || false}
                                onChange={(e) => setFormData({
                                  ...formData,
                                  typeSpecificFields: {
                                    ...formData.typeSpecificFields,
                                    networkingEquipmentFields: {
                                      ...formData.typeSpecificFields.networkingEquipmentFields,
                                      installationService: e.target.checked
                                    }
                                  }
                                })}
                              />
                              <span className="ml-2 text-sm text-gray-600">Installation Service Available</span>
                            </label>
                          </div>
                        </div>
                      </>
                    )}
  
                    {/* Domain & Hosting Fields */}
                    {formData.vendorType === 'DOMAIN_HOSTING_PROVIDER' && (
                      <>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Globe className="h-4 w-4 inline mr-1 text-gray-500" />
                            Hosting Types
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., Shared, VPS, Dedicated"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.domainHostingFields?.hostingTypes?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                domainHostingFields: {
                                  ...formData.typeSpecificFields.domainHostingFields,
                                  hostingTypes: e.target.value.split(',').map(t => t.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Database className="h-4 w-4 inline mr-1 text-gray-500" />
                            Storage Space
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., 10GB - 1TB"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.domainHostingFields?.storageSpace || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                domainHostingFields: {
                                  ...formData.typeSpecificFields.domainHostingFields,
                                  storageSpace: e.target.value
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Settings className="h-4 w-4 inline mr-1 text-gray-500" />
                            Additional Features
                          </label>
                          <div className="space-y-2">
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.domainHostingFields?.sslCertificates || false}
                                onChange={(e) => setFormData({
                                  ...formData,
                                  typeSpecificFields: {
                                    ...formData.typeSpecificFields,
                                    domainHostingFields: {
                                      ...formData.typeSpecificFields.domainHostingFields,
                                      sslCertificates: e.target.checked
                                    }
                                  }
                                })}
                              />
                              <span className="ml-2 text-sm text-gray-600">SSL Certificates</span>
                            </label>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Clock className="h-4 w-4 inline mr-1 text-gray-500" />
                            Backup Frequency
                          </label>
                          <select
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.domainHostingFields?.backupFrequency || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                domainHostingFields: {
                                  ...formData.typeSpecificFields.domainHostingFields,
                                  backupFrequency: e.target.value
                                }
                              }
                            })}
                          >
                            <option value="">Select Backup Frequency</option>
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                          </select>
                        </div>
                      </>
                    )}
  
                    {/* CRM Vendor Fields */}
                    {formData.vendorType === 'CRM_VENDOR' && (
                      <>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Layers className="h-4 w-4 inline mr-1 text-gray-500" />
                            Features
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., Lead Management, Sales Pipeline, Analytics"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.crmFields?.features?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                crmFields: {
                                  ...formData.typeSpecificFields.crmFields,
                                  features: e.target.value.split(',').map(f => f.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Link className="h-4 w-4 inline mr-1 text-gray-500" />
                            Integrations
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., Email, Social Media, ERP"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.crmFields?.integrations?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                crmFields: {
                                  ...formData.typeSpecificFields.crmFields,
                                  integrations: e.target.value.split(',').map(i => i.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Users className="h-4 w-4 inline mr-1 text-gray-500" />
                            User Limit
                          </label>
                          <input
                            type="number"
                            placeholder="e.g., 100"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.crmFields?.userLimit || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                crmFields: {
                                  ...formData.typeSpecificFields.crmFields,
                                  userLimit: parseInt(e.target.value)
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Settings className="h-4 w-4 inline mr-1 text-gray-500" />
                            Additional Features
                          </label>
                          <div className="space-y-2">
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.crmFields?.apiAvailable || false}
                                onChange={(e) => setFormData({
                                  ...formData,
                                  typeSpecificFields: {
                                    ...formData.typeSpecificFields,
                                    crmFields: {
                                      ...formData.typeSpecificFields.crmFields,
                                      apiAvailable: e.target.checked
                                    }
                                  }
                                })}
                              />
                              <span className="ml-2 text-sm text-gray-600">API Available</span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.crmFields?.customReports || false}
                                onChange={(e) => setFormData({
                                  ...formData,
                                  typeSpecificFields: {
                                    ...formData.typeSpecificFields,
                                    crmFields: {
                                      ...formData.typeSpecificFields.crmFields,
                                      customReports: e.target.checked
                                    }
                                  }
                                })}
                              />
                              <span className="ml-2 text-sm text-gray-600">Custom Reports</span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.crmFields?.automationSupport || false}
                                onChange={(e) => setFormData({
                                  ...formData,
                                  typeSpecificFields: {
                                    ...formData.typeSpecificFields,
                                    crmFields: {
                                      ...formData.typeSpecificFields.crmFields,
                                      automationSupport: e.target.checked
                                    }
                                  }
                                })}
                              />
                              <span className="ml-2 text-sm text-gray-600">Automation Support</span>
                            </label>
                          </div>
                        </div>
                      </>
                    )}
  
                    {/* HRMS & Payroll Fields */}
                    {formData.vendorType === 'HRMS_PAYROLL_VENDOR' && (
                      <>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Layers className="h-4 w-4 inline mr-1 text-gray-500" />
                            Modules
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., Payroll, Attendance, Performance"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.hrmsFields?.modules?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                hrmsFields: {
                                  ...formData.typeSpecificFields.hrmsFields,
                                  modules: e.target.value.split(',').map(m => m.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Users className="h-4 w-4 inline mr-1 text-gray-500" />
                            Employee Limit
                          </label>
                          <input
                            type="number"
                            placeholder="e.g., 500"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.hrmsFields?.employeeLimit || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                hrmsFields: {
                                  ...formData.typeSpecificFields.hrmsFields,
                                  employeeLimit: parseInt(e.target.value)
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Globe className="h-4 w-4 inline mr-1 text-gray-500" />
                            Payroll Regions
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., Pakistan, UAE, Saudi Arabia"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.hrmsFields?.payrollRegions?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                hrmsFields: {
                                  ...formData.typeSpecificFields.hrmsFields,
                                  payrollRegions: e.target.value.split(',').map(r => r.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Settings className="h-4 w-4 inline mr-1 text-gray-500" />
                            Additional Features
                          </label>
                          <div className="space-y-2">
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.hrmsFields?.biometric || false}
                                onChange={(e) => setFormData({
                                  ...formData,
                                  typeSpecificFields: {
                                    ...formData.typeSpecificFields,
                                    hrmsFields: {
                                      ...formData.typeSpecificFields.hrmsFields,
                                      biometric: e.target.checked
                                    }
                                  }
                                })}
                              />
                              <span className="ml-2 text-sm text-gray-600">Biometric Integration</span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.hrmsFields?.mobileApp || false}
                                onChange={(e) => setFormData({
                                  ...formData,
                                  typeSpecificFields: {
                                    ...formData.typeSpecificFields,
                                    hrmsFields: {
                                      ...formData.typeSpecificFields.hrmsFields,
                                      mobileApp: e.target.checked
                                    }
                                  }
                                })}
                              />
                              <span className="ml-2 text-sm text-gray-600">Mobile App</span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.hrmsFields?.selfService || false}
                                onChange={(e) => setFormData({
                                  ...formData,
                                  typeSpecificFields: {
                                    ...formData.typeSpecificFields,
                                    hrmsFields: {
                                      ...formData.typeSpecificFields.hrmsFields,
                                      selfService: e.target.checked
                                    }
                                  }
                                })}
                              />
                              <span className="ml-2 text-sm text-gray-600">Employee Self Service</span>
                            </label>
                          </div>
                        </div>
                      </>
                    )}
  
                    {/* Cybersecurity Vendor Fields */}
                    {formData.vendorType === 'CYBERSECURITY_VENDOR' && (
                      <>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Shield className="h-4 w-4 inline mr-1 text-gray-500" />
                            Services
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., Penetration Testing, Security Audit, VAPT"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.cybersecurityFields?.services?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                cybersecurityFields: {
                                  ...formData.typeSpecificFields.cybersecurityFields,
                                  services: e.target.value.split(',').map(s => s.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <FileCheck className="h-4 w-4 inline mr-1 text-gray-500" />
                            Certifications
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., ISO 27001, CREST, CEH"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.cybersecurityFields?.certifications?.join(', ') || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                cybersecurityFields: {
                                  ...formData.typeSpecificFields.cybersecurityFields,
                                  certifications: e.target.value.split(',').map(c => c.trim())
                                }
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Clock className="h-4 w-4 inline mr-1 text-gray-500" />
                            Monitoring Type
                          </label>
                          <select
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.typeSpecificFields.cybersecurityFields?.monitoringType || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              typeSpecificFields: {
                                ...formData.typeSpecificFields,
                                cybersecurityFields: {
                                  ...formData.typeSpecificFields.cybersecurityFields,
                                  monitoringType: e.target.value as '24x7' | 'business-hours'
                                }
                              }
                            })}
                          >
                            <option value="">Select Monitoring Type</option>
                            <option value="24x7">24x7</option>
                            <option value="business-hours">Business Hours</option>
                          </select>
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Settings className="h-4 w-4 inline mr-1 text-gray-500" />
                            Additional Features
                          </label>
                          <div className="space-y-2">
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                className="form-checkbox h-4 w-4 text-blue-600"
                                checked={formData.typeSpecificFields.cybersecurityFields?.threatIntelligence || false}
                                onChange={(e) => setFormData({
                                  ...formData,
                                  typeSpecificFields: {
                                    ...formData.typeSpecificFields,
                                    cybersecurityFields: {
                                      ...formData.typeSpecificFields.cybersecurityFields,
                                      threatIntelligence: e.target.checked
                                    }
                                  }
                                })}
                              />
                              <span className="ml-2 text-sm text-gray-600">Threat Intelligence</span>
                            </label>
                          </div>
                        </div>
                      </>
                    )}
  
                    {/* Add more vendor type specific fields here */}
                  </div>
                </div>
  
                {/* Company Section */}
                <div className="bg-gray-50 p-4 rounded-lg mb-6">
                  <div className="flex items-center mb-4">
                    <Building2 className="h-5 w-5 text-gray-500 mr-2" />
                    <h3 className="text-lg font-medium text-gray-700">Company Information</h3>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">
                        <Tag className="h-4 w-4 inline mr-1 text-gray-500" />
                        Vendor Name
                        <span className="text-xs text-gray-500 block">Trading or brand name</span>
                      </label>
                      <input
                        required
                        type="text"
                        placeholder="e.g., Microsoft"
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        value={formData.vendorName}
                        onChange={(e) => setFormData({ ...formData, vendorName: e.target.value })}
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700">
                        <Building2 className="h-4 w-4 inline mr-1 text-gray-500" />
                        Company Name
                        <span className="text-xs text-gray-500 block">Full legal/registered name</span>
                      </label>
                      <input
                        required
                        type="text"
                        placeholder="e.g., Microsoft Corporation"
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        value={formData.companyName}
                        onChange={(e) => setFormData({ ...formData, companyName: e.target.value })}
                      />
                    </div>
                    <div className="col-span-2 space-y-2">
                      <label className="block text-sm font-medium text-gray-700">
                        <Globe className="h-4 w-4 inline mr-1 text-gray-500" />
                        Website
                      </label>
                      <input
                        type="url"
                        placeholder="e.g., https://www.microsoft.com"
                        className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        value={formData.website}
                        onChange={(e) => setFormData({ ...formData, website: e.target.value })}
                      />
                    </div>
                  </div>
  
                  {/* Address Section */}
                  <div className="mt-6">
                    <div className="flex items-center mb-4">
                      <MapPin className="h-5 w-5 text-gray-500 mr-2" />
                      <h4 className="text-lg font-medium text-gray-700">Address</h4>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="col-span-2 space-y-2">
                        <label className="block text-sm font-medium text-gray-700">
                          <MapPin className="h-4 w-4 inline mr-1 text-gray-500" />
                          Street Address
                        </label>
                        <input
                          required
                          type="text"
                          placeholder="e.g., 123 Business Street"
                          className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                          value={formData.address.street}
                          onChange={(e) => setFormData({
                            ...formData,
                            address: {
                              street: e.target.value,
                              city: formData.address.city,
                              state: formData.address.state,
                              country: formData.address.country,
                              postalCode: formData.address.postalCode,
                              telephone: formData.address.telephone
                            }
                          })}
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">
                          <MapPin className="h-4 w-4 inline mr-1 text-gray-500" />
                          City
                        </label>
                        <input
                          required
                          type="text"
                          placeholder="e.g., Karachi"
                          className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                          value={formData.address.city}
                          onChange={(e) => setFormData({
                            ...formData,
                            address: {
                              street: formData.address.street,
                              city: e.target.value,
                              state: formData.address.state,
                              country: formData.address.country,
                              postalCode: formData.address.postalCode,
                              telephone: formData.address.telephone
                            }
                          })}
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">
                          <MapPin className="h-4 w-4 inline mr-1 text-gray-500" />
                          State/Province
                        </label>
                        <input
                          required
                          type="text"
                          placeholder="e.g., Sindh"
                          className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                          value={formData.address.state}
                          onChange={(e) => setFormData({
                            ...formData,
                            address: {
                              street: formData.address.street,
                              city: formData.address.city,
                              state: e.target.value,
                              country: formData.address.country,
                              postalCode: formData.address.postalCode,
                              telephone: formData.address.telephone
                            }
                          })}
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">
                          <MapPin className="h-4 w-4 inline mr-1 text-gray-500" />
                          Country
                        </label>
                        <select
                          required
                          className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                          value={formData.address.country}
                          onChange={(e) => setFormData({
                            ...formData,
                            address: {
                              street: formData.address.street,
                              city: formData.address.city,
                              state: formData.address.state,
                              country: e.target.value,
                              postalCode: formData.address.postalCode,
                              telephone: formData.address.telephone
                            }
                          })}
                        >
                          <option value="">Select Country</option>
                          {countries.map((country) => (
                            <option key={country.code} value={country.name}>
                              {country.name}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">
                          <MapPin className="h-4 w-4 inline mr-1 text-gray-500" />
                          Postal Code
                        </label>
                        <input
                          required
                          type="text"
                          placeholder="e.g., 75530"
                          className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                          value={formData.address.postalCode}
                          onChange={(e) => setFormData({
                            ...formData,
                            address: {
                              street: formData.address.street,
                              city: formData.address.city,
                              state: formData.address.state,
                              country: formData.address.country,
                              postalCode: e.target.value,
                              telephone: formData.address.telephone
                            }
                          })}
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="block text-sm font-medium text-gray-700">
                          <Phone className="h-4 w-4 inline mr-1 text-gray-500" />
                          Telephone
                        </label>
                        <input
                          required
                          type="tel"
                          placeholder="+92 XXX XXXXXXX"
                          className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                          value={formData.address.telephone}
                          onChange={(e) => setFormData({
                            ...formData,
                            address: {
                              ...formData.address,
                              telephone: e.target.value
                            }
                          })}
                        />
                      </div>
                    </div>
                  </div>
                </div>
  
                {/* Contact Person Section */}
                <div className="bg-gray-50 p-4 rounded-lg mb-6">
                  <div className="flex items-center mb-4">
                    <Users className="h-5 w-5 text-gray-500 mr-2" />
                    <h3 className="text-lg font-medium text-gray-700">Contact Person</h3>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    {/* Primary Contact Person */}
                    <div className="col-span-2">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Primary Contact Person</h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <User className="h-4 w-4 inline mr-1 text-gray-500" />
                            Name
                          </label>
                          <input
                            required
                            type="text"
                            placeholder="Full Name"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.contactPerson.name}
                            onChange={(e) => setFormData({
                              ...formData,
                              contactPerson: {
                                ...formData.contactPerson,
                                name: e.target.value
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Briefcase className="h-4 w-4 inline mr-1 text-gray-500" />
                            Title/Position
                          </label>
                          <input
                            required
                            type="text"
                            placeholder="e.g., Sales Manager"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.contactPerson.title}
                            onChange={(e) => setFormData({
                              ...formData,
                              contactPerson: {
                                ...formData.contactPerson,
                                title: e.target.value
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Mail className="h-4 w-4 inline mr-1 text-gray-500" />
                            Email
                          </label>
                          <input
                            required
                            type="email"
                            placeholder="<EMAIL>"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.contactPerson.email}
                            onChange={(e) => setFormData({
                              ...formData,
                              contactPerson: {
                                ...formData.contactPerson,
                                email: e.target.value
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Phone className="h-4 w-4 inline mr-1 text-gray-500" />
                            Phone
                          </label>
                          <input
                            required
                            type="tel"
                            placeholder="+92 XXX XXXXXXX"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.contactPerson.phone}
                            onChange={(e) => setFormData({
                              ...formData,
                              contactPerson: {
                                ...formData.contactPerson,
                                phone: e.target.value
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Phone className="h-4 w-4 inline mr-1 text-gray-500" />
                            Alternative Phone
                          </label>
                          <input
                            type="tel"
                            placeholder="+92 XXX XXXXXXX"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.contactPerson.alternativePhone || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              contactPerson: {
                                ...formData.contactPerson,
                                alternativePhone: e.target.value
                              }
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">
                            <Building2 className="h-4 w-4 inline mr-1 text-gray-500" />
                            Department
                          </label>
                          <input
                            type="text"
                            placeholder="e.g., Sales"
                            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                            value={formData.contactPerson.department || ''}
                            onChange={(e) => setFormData({
                              ...formData,
                              contactPerson: {
                                ...formData.contactPerson,
                                department: e.target.value
                              }
                            })}
                          />
                        </div>
                      </div>
                    </div>
  
                    {/* Additional Contacts */}
                    <div className="col-span-2">
                      <div className="flex justify-between items-center mb-4">
                        <h3 className="text-lg font-medium text-gray-900">Additional Contacts</h3>
                        <button
                          type="button"
                          className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                          onClick={() => setFormData({
                            ...formData,
                            alternativeContacts: [
                              ...(formData.alternativeContacts || []),
                              { name: '', title: '', email: '', phone: '' }
                            ]
                          })}
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Add Contact
                        </button>
                      </div>
                      {formData.alternativeContacts?.map((contact, index) => (
                        <div key={index} className="border rounded-lg p-4 mb-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <label className="block text-sm font-medium text-gray-700">
                                <User className="h-4 w-4 inline mr-1 text-gray-500" />
                                Name
                              </label>
                              <input
                                required
                                type="text"
                                placeholder="Full Name"
                                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                value={contact.name}
                                onChange={(e) => {
                                  const updatedContacts = [...(formData.alternativeContacts || [])];
                                  updatedContacts[index] = { ...contact, name: e.target.value };
                                  setFormData({
                                    ...formData,
                                    alternativeContacts: updatedContacts
                                  });
                                }}
                              />
                            </div>
                            <div className="space-y-2">
                              <label className="block text-sm font-medium text-gray-700">
                                <Briefcase className="h-4 w-4 inline mr-1 text-gray-500" />
                                Title/Position
                              </label>
                              <input
                                required
                                type="text"
                                placeholder="e.g., Technical Support"
                                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                value={contact.title}
                                onChange={(e) => {
                                  const updatedContacts = [...(formData.alternativeContacts || [])];
                                  updatedContacts[index] = { ...contact, title: e.target.value };
                                  setFormData({
                                    ...formData,
                                    alternativeContacts: updatedContacts
                                  });
                                }}
                              />
                            </div>
                            <div className="space-y-2">
                              <label className="block text-sm font-medium text-gray-700">
                                <Mail className="h-4 w-4 inline mr-1 text-gray-500" />
                                Email
                              </label>
                              <input
                                required
                                type="email"
                                placeholder="<EMAIL>"
                                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                value={contact.email}
                                onChange={(e) => {
                                  const updatedContacts = [...(formData.alternativeContacts || [])];
                                  updatedContacts[index] = { ...contact, email: e.target.value };
                                  setFormData({
                                    ...formData,
                                    alternativeContacts: updatedContacts
                                  });
                                }}
                              />
                            </div>
                            <div className="space-y-2">
                              <label className="block text-sm font-medium text-gray-700">
                                <Phone className="h-4 w-4 inline mr-1 text-gray-500" />
                                Phone
                              </label>
                              <div className="flex">
                                <input
                                  required
                                  type="tel"
                                  placeholder="+92 XXX XXXXXXX"
                                  className="flex-1 p-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-blue-500"
                                  value={contact.phone}
                                  onChange={(e) => {
                                    const updatedContacts = [...(formData.alternativeContacts || [])];
                                    updatedContacts[index] = { ...contact, phone: e.target.value };
                                    setFormData({
                                      ...formData,
                                      alternativeContacts: updatedContacts
                                    });
                                  }}
                                />
                                <button
                                  type="button"
                                  className="px-3 py-2 bg-red-600 text-white rounded-r-lg hover:bg-red-700"
                                  onClick={() => {
                                    const updatedContacts = [...(formData.alternativeContacts || [])];
                                    updatedContacts.splice(index, 1);
                                    setFormData({
                                      ...formData,
                                      alternativeContacts: updatedContacts
                                    });
                                  }}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </>
            )}
  
            <div className="flex justify-end space-x-4">
  <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
  >
                Cancel
  </button>
              <button
                type="submit"
                className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-200"
              >
                <Save className="h-5 w-5" />
                Add Vendor
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  };
  
  //=============================================================================
  // Category Mapping
  //=============================================================================
  const categoryMap: Record<MainCategory, { label: string; types: { value: VendorType; label: string }[] }> = {
    IT_HARDWARE_INFRASTRUCTURE: {
      label: 'IT Hardware & Infrastructure',
      types: [
        { value: 'HARDWARE_SUPPLIER', label: 'Hardware Supplier' },
        { value: 'PRINTER_PHOTOCOPIER_SUPPLIER', label: 'Printer & Photocopier Supplier' },
        { value: 'SERVER_STORAGE_SUPPLIER', label: 'Server & Storage Supplier' },
        { value: 'CCTV_SURVEILLANCE_SUPPLIER', label: 'CCTV & Surveillance Supplier' },
        { value: 'POWER_UPS_SUPPLIER', label: 'Power & UPS Supplier' },
        { value: 'IT_ACCESSORIES_SUPPLIER', label: 'IT Accessories Supplier' }
      ]
    },
    INTERNET_NETWORKING: {
      label: 'Internet & Networking',
      types: [
        { value: 'ISP', label: 'Internet Service Provider (ISP)' },
        { value: 'NETWORKING_EQUIPMENT_SUPPLIER', label: 'Networking Equipment Supplier' },
        { value: 'FIREWALL_SECURITY_VENDOR', label: 'Firewall & Security Vendor' },
        { value: 'VPN_SECURE_ACCESS_PROVIDER', label: 'VPN & Secure Access Provider' }
      ]
    },
    CLOUD_HOSTING: {
      label: 'Cloud & Hosting',
      types: [
        { value: 'CLOUD_SERVICE_PROVIDER', label: 'Cloud Service Provider' },
        { value: 'DOMAIN_HOSTING_PROVIDER', label: 'Domain & Hosting Provider' },
        { value: 'BACKUP_DISASTER_RECOVERY', label: 'Backup & Disaster Recovery Vendor' }
      ]
    },
    BUSINESS_SOFTWARE: {
      label: 'Business Software & IT Solutions',
      types: [
        { value: 'ERP_VENDOR', label: 'ERP Vendor' },
        { value: 'CRM_VENDOR', label: 'CRM Vendor' },
        { value: 'HRMS_PAYROLL_VENDOR', label: 'HRMS & Payroll Vendor' },
        { value: 'POS_RETAIL_SOFTWARE', label: 'POS & Retail Software Vendor' },
        { value: 'FINANCIAL_ACCOUNTING_SOFTWARE', label: 'Financial & Accounting Software Vendor' }
      ]
    },
    CALL_CENTER: {
      label: 'Call Center & Communication',
      types: [
        { value: 'PBX_VOIP_PROVIDER', label: 'PBX & VoIP Provider' },
        { value: 'CALL_CENTER_SOFTWARE', label: 'Call Center Software Vendor' },
        { value: 'TELECOM_SERVICES', label: 'Telecom Services Vendor' }
      ]
    },
    IT_SECURITY: {
      label: 'IT Security & Cybersecurity',
      types: [
        { value: 'CYBERSECURITY_VENDOR', label: 'Cybersecurity Vendor' },
        { value: 'ANTIVIRUS_ENDPOINT_PROTECTION', label: 'Antivirus & Endpoint Protection Vendor' },
        { value: 'IDENTITY_ACCESS_MANAGEMENT', label: 'Identity & Access Management Vendor' }
      ]
    },
    IT_CONSULTANCY: {
      label: 'IT Consultancy & Development',
      types: [
        { value: 'IT_CONSULTANCY', label: 'IT Consultancy Vendor' },
        { value: 'SOFTWARE_DEVELOPMENT', label: 'Software Development Vendor' },
        { value: 'BLOCKCHAIN_WEB3', label: 'Blockchain & Web3 Vendor' }
      ]
    }
  };
  
  //=============================================================================
  // Main VendorContacts Component
  //=============================================================================
  export function VendorContacts() {
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedStatus, setSelectedStatus] = useState('all');
    const [selectedType, setSelectedType] = useState('all');
    const [showAddForm, setShowAddForm] = useState(false);
    const [selectedVendor, setSelectedVendor] = useState<Vendor | null>(null);
    const [expandedVendor, setExpandedVendor] = useState<string | null>(null);
  
    // Sample vendors data
    const [vendors, setVendors] = useState<Vendor[]>([
      {
        id: 'V001',
        vendorName: 'Tech Solutions Inc',
        companyName: 'Tech Solutions International Private Limited',
        contactPerson: {
          name: 'John Smith',
          title: 'Account Manager',
          email: '<EMAIL>',
          phone: '+92 300 1234567',
          department: 'Enterprise Sales'
        },
        alternativeContacts: [
          {
            name: 'Sarah Johnson',
            title: 'Technical Support Lead',
            email: '<EMAIL>',
            phone: '+92 300 7654321',
            department: 'Support'
          }
        ],
        address: {
          street: '123 Tech Park Avenue',
          city: 'Islamabad',
          state: 'Federal Territory',
          country: 'Pakistan',
          postalCode: '44000',
          telephone: '+92 300 1234567'
        },
        vendorType: 'SOFTWARE_DEVELOPMENT',
        status: 'ACTIVE',
        contract: {
          startDate: '2024-01-01',
          expiryDate: '2024-12-31',
          renewalReminder: true,
          autoRenewal: false,
          terminationNotice: 60
        },
        performance: {
          rating: 4.5,
          responseTime: 24,
          deliveryScore: 98,
          qualityScore: 95
        },
        financial: {
          spending: {
            current: {
              value: 450000,
              currency: 'PKR'
            },
            trend: 18.4
          }
        }
      },
      {
        id: 'V002',
        vendorName: 'Cloud Systems Ltd',
        companyName: 'Cloud Systems Limited',
        contactPerson: {
          name: 'David Wilson',
          title: 'Sales Director',
          email: '<EMAIL>',
          phone: '+92 321 1234567',
          department: 'Sales'
        },
        address: {
          street: '456 Cloud Tower',
          city: 'Lahore',
          state: 'Punjab',
          country: 'Pakistan',
          postalCode: '54000',
          telephone: '+92 321 1234567'
        },
        vendorType: 'CLOUD_SERVICE_PROVIDER',
        status: 'ACTIVE',
        contract: {
          startDate: '2024-02-01',
          expiryDate: '2025-01-31',
          renewalReminder: true,
          autoRenewal: true,
          terminationNotice: 90
        },
        performance: {
          rating: 4.8,
          responseTime: 12,
          deliveryScore: 99,
          qualityScore: 98
        },
        financial: {
          spending: {
            current: {
              value: 750000,
              currency: 'PKR'
            },
            trend: 22.5
          }
        }
      },
      {
        id: 'V003',
        vendorName: 'Hardware Pro Solutions',
        companyName: 'Hardware Professional Solutions Private Limited',
        contactPerson: {
          name: 'Michael Chen',
          title: 'Sales Manager',
          email: '<EMAIL>',
          phone: '+92 333 9876543',
          department: 'Sales'
        },
        address: {
          street: '789 Industrial Zone',
          city: 'Karachi',
          state: 'Sindh',
          country: 'Pakistan',
          postalCode: '75530',
          telephone: '+92 333 9876543'
        },
        vendorType: 'HARDWARE_SUPPLIER',
        status: 'ACTIVE',
        contract: {
          startDate: '2024-01-15',
          expiryDate: '2024-12-31',
          renewalReminder: true,
          autoRenewal: true,
          terminationNotice: 45
        },
        performance: {
          rating: 4.7,
          responseTime: 8,
          deliveryScore: 97,
          qualityScore: 96
        },
        financial: {
          spending: {
            current: {
              value: 850000,
              currency: 'PKR'
            },
            trend: 15.2
          }
        }
      }
    ]);
  
    //-----------------------------------------------------------------------------
    // Utility Functions
    //-----------------------------------------------------------------------------
    const getStatusColor = (status: VendorStatus) => {
      switch (status) {
        case 'ACTIVE':
          return 'bg-green-100 text-green-800';
        case 'INACTIVE':
          return 'bg-gray-100 text-gray-800';
        case 'BLACKLISTED':
          return 'bg-red-100 text-red-800';
        case 'PENDING_REVIEW':
          return 'bg-yellow-100 text-yellow-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    };
  
    const toggleVendorExpansion = (vendorId: string) => {
      setExpandedVendor(expandedVendor === vendorId ? null : vendorId);
    };
  
    const handleAddVendor = (newVendor: Vendor) => {
      setVendors([...vendors, newVendor]);
      setShowAddForm(false);
    };
}