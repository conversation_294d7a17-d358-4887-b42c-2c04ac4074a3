import React, { useState } from 'react';
import { X, AlertCircle, CheckCircle, XCircle, Edit, Trash2, Shield } from 'lucide-react';
import {
  WorkflowActionType,
  LeaveWorkflowRequest,
  UserRole
} from '../../../types/leaveWorkflow';

interface WorkflowActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  request: LeaveWorkflowRequest | null;
  action: WorkflowActionType | null;
  currentUser: {
    id: number;
    role: UserRole;
    name: string;
  };
  onSubmit: (requestId: string, action: WorkflowActionType, reason?: string, modifications?: any) => Promise<void>;
}

const WorkflowActionModal: React.FC<WorkflowActionModalProps> = ({
  isOpen,
  onClose,
  request,
  action,
  currentUser,
  onSubmit
}) => {
  const [reason, setReason] = useState('');
  const [modifications, setModifications] = useState<any>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  if (!isOpen || !request || !action) return null;

  const handleSubmit = async () => {
    if (!request || !action) return;

    // Validate required reason for certain actions
    if (action !== WorkflowActionType.APPROVE && !reason.trim()) {
      setError('Reason is required for this action');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await onSubmit(request.id, action, reason, modifications);
      onClose();
      setReason('');
      setModifications({});
    } catch (error: any) {
      setError(error.message || 'Failed to process action');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getActionIcon = (actionType: WorkflowActionType) => {
    switch (actionType) {
      case WorkflowActionType.APPROVE:
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case WorkflowActionType.REJECT:
        return <XCircle className="h-5 w-5 text-red-600" />;
      case WorkflowActionType.MODIFY:
        return <Edit className="h-5 w-5 text-orange-600" />;
      case WorkflowActionType.CANCEL:
        return <Trash2 className="h-5 w-5 text-gray-600" />;
      case WorkflowActionType.OVERRIDE:
        return <Shield className="h-5 w-5 text-purple-600" />;
      default:
        return <AlertCircle className="h-5 w-5 text-blue-600" />;
    }
  };

  const getActionColor = (actionType: WorkflowActionType) => {
    switch (actionType) {
      case WorkflowActionType.APPROVE:
        return 'bg-green-100 border-green-200';
      case WorkflowActionType.REJECT:
        return 'bg-red-100 border-red-200';
      case WorkflowActionType.MODIFY:
        return 'bg-orange-100 border-orange-200';
      case WorkflowActionType.CANCEL:
        return 'bg-gray-100 border-gray-200';
      case WorkflowActionType.OVERRIDE:
        return 'bg-purple-100 border-purple-200';
      default:
        return 'bg-blue-100 border-blue-200';
    }
  };

  const getButtonColor = (actionType: WorkflowActionType) => {
    switch (actionType) {
      case WorkflowActionType.APPROVE:
        return 'bg-green-600 hover:bg-green-700';
      case WorkflowActionType.REJECT:
        return 'bg-red-600 hover:bg-red-700';
      case WorkflowActionType.MODIFY:
        return 'bg-orange-600 hover:bg-orange-700';
      case WorkflowActionType.CANCEL:
        return 'bg-gray-600 hover:bg-gray-700';
      case WorkflowActionType.OVERRIDE:
        return 'bg-purple-600 hover:bg-purple-700';
      default:
        return 'bg-blue-600 hover:bg-blue-700';
    }
  };

  const formatActionName = (actionType: WorkflowActionType) => {
    return actionType.charAt(0).toUpperCase() + actionType.slice(1);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 font-sans">
      <div className="absolute inset-0 bg-gray-900/40 backdrop-blur-[2px]" onClick={onClose} />
      <div className="relative w-full max-w-2xl bg-white rounded-lg shadow-xl border border-gray-200">
        
        {/* Header */}
        <div className={`px-6 py-4 border-b border-gray-200 ${getActionColor(action)}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getActionIcon(action)}
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {formatActionName(action)} Leave Request
                </h3>
                <p className="text-sm text-gray-600 mt-1">
                  {request.employeeName} • {request.leaveType} • {request.daysRequested} days
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 p-2 rounded-full hover:bg-white/50"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-4">
          {/* Request Details */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-3">Request Details</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Employee:</span>
                <div className="text-gray-900">{request.employeeName}</div>
              </div>
              <div>
                <span className="font-medium text-gray-700">Leave Type:</span>
                <div className="text-gray-900">{request.leaveType}</div>
              </div>
              <div>
                <span className="font-medium text-gray-700">Duration:</span>
                <div className="text-gray-900">
                  {new Date(request.currentStartDate).toLocaleDateString()} - {new Date(request.currentEndDate).toLocaleDateString()}
                </div>
              </div>
              <div>
                <span className="font-medium text-gray-700">Days:</span>
                <div className="text-gray-900">{request.daysRequested} days</div>
              </div>
              <div className="col-span-2">
                <span className="font-medium text-gray-700">Reason:</span>
                <div className="text-gray-900 mt-1">{request.currentReason}</div>
              </div>
            </div>
          </div>

          {/* Action Form */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {action === WorkflowActionType.APPROVE ? 'Comments (Optional)' : 'Reason'} 
                {action !== WorkflowActionType.APPROVE && <span className="text-red-500 ml-1">*</span>}
              </label>
              <textarea
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 resize-none"
                rows={3}
                placeholder={
                  action === WorkflowActionType.APPROVE 
                    ? 'Optional comments for approval...'
                    : `Please provide a reason for ${action}...`
                }
              />
            </div>

            {/* Modification Fields */}
            {action === WorkflowActionType.MODIFY && (
              <div className="space-y-4">
                <div className="border-t pt-4">
                  <h4 className="font-medium text-gray-900 mb-3">Modifications</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        New Start Date
                      </label>
                      <input
                        type="date"
                        value={modifications.startDate || request.currentStartDate}
                        onChange={(e) => setModifications({
                          ...modifications,
                          startDate: e.target.value
                        })}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        New End Date
                      </label>
                      <input
                        type="date"
                        value={modifications.endDate || request.currentEndDate}
                        onChange={(e) => setModifications({
                          ...modifications,
                          endDate: e.target.value
                        })}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-200"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <span className="text-sm text-red-800">{error}</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 flex justify-end gap-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={isSubmitting || (action !== WorkflowActionType.APPROVE && !reason.trim())}
            className={`px-4 py-2 text-sm font-medium text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors ${getButtonColor(action)}`}
          >
            {isSubmitting ? 'Processing...' : `${formatActionName(action)} Request`}
          </button>
        </div>
      </div>
    </div>
  );
};

export default WorkflowActionModal; 