import { MigrationInterface, QueryRunner } from "typeorm";

export class AddNotesColumnsToEmployee1746000000020 implements MigrationInterface {
    name = 'AddNotesColumnsToEmployee1746000000020';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add notes and specialInstructions columns to employees table
        await queryRunner.query(`
            ALTER TABLE \`employees\` 
            ADD COLUMN \`notes\` TEXT NULL,
            ADD COLUMN \`special_instructions\` TEXT NULL
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove the added columns
        await queryRunner.query(`
            ALTER TABLE \`employees\` 
            DROP COLUMN \`notes\`,
            DROP COLUMN \`special_instructions\`
        `);
    }
} 