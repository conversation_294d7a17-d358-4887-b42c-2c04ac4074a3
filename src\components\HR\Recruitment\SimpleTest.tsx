import React from 'react';
import DebugInfo from './DebugInfo';

/**
 * Ultra-simple test component to debug routing issues
 * This component has zero dependencies and should always render
 */
const SimpleTest: React.FC = () => {
  // Reduced console logging to prevent spam
  console.log('✅ SimpleTest component rendered at:', window.location.pathname);

  return (
    <>
      <DebugInfo />
      <div style={{ padding: '20px', backgroundColor: '#f0f0f0', minHeight: '400px' }}>
        <h1 style={{ color: 'green', fontSize: '24px' }}>
          ✅ RECRUITMENT MODULE IS WORKING!
        </h1>
        <p style={{ fontSize: '16px', marginBottom: '20px' }}>
          If you can see this message, the routing is working correctly.
        </p>
        <div style={{ backgroundColor: 'white', padding: '15px', borderRadius: '5px', marginBottom: '20px' }}>
          <h3>Debug Information:</h3>
          <ul>
            <li>Current URL: {window.location.pathname}</li>
            <li>Component: SimpleTest</li>
            <li>Time: {new Date().toLocaleTimeString()}</li>
          </ul>
        </div>
        <div style={{ backgroundColor: '#e8f5e8', padding: '15px', borderRadius: '5px' }}>
          <h3>Next Steps:</h3>
          <p>1. Check browser console for any errors</p>
          <p>2. Verify you have HR permissions (admin, hr, HR_ADMIN, HR_STAFF)</p>
          <p>3. Try refreshing the page</p>
          <p>4. Navigate back and try again</p>
        </div>
      </div>
    </>
  );
};

export default SimpleTest;
