import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class RemoveUnusedLeaveFields1715000000000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Remove old/unused columns (MySQL compatibility: check if column exists before dropping)
        const columnsToDrop = ["isEmergencyLeave", "delegatedTo", "handoverNotes"];
        for (const col of columnsToDrop) {
            const [{ count }] = await queryRunner.query(`
                SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'leave_requests' AND COLUMN_NAME = '${col}'
            `);
            if (Number(count) > 0) {
                await queryRunner.query(`ALTER TABLE leave_requests DROP COLUMN ${col}`);
            }
        }

        // Change employeeId to INT (if needed)
        await queryRunner.query(`ALTER TABLE leave_requests MODIFY COLUMN employeeId INT NOT NULL`);

        // Add new workflow/frontend columns
        await queryRunner.addColumn("leave_requests", new TableColumn({
            name: "source",
            type: "enum",
            enum: ["EMPLOYEE", "HR_ADMIN", "MANAGER"],
            default: "'EMPLOYEE'",
            isNullable: false,
        }));

        await queryRunner.addColumn("leave_requests", new TableColumn({
            name: "currentStage",
            type: "varchar",
            length: "255",
            isNullable: true,
        }));

        await queryRunner.addColumn("leave_requests", new TableColumn({
            name: "modificationHistory",
            type: "text",
            isNullable: true,
        }));

        await queryRunner.addColumn("leave_requests", new TableColumn({
            name: "originalStartDate",
            type: "date",
            isNullable: true,
        }));

        await queryRunner.addColumn("leave_requests", new TableColumn({
            name: "originalEndDate",
            type: "date",
            isNullable: true,
        }));

        await queryRunner.addColumn("leave_requests", new TableColumn({
            name: "originalReason",
            type: "text",
            isNullable: true,
        }));

        await queryRunner.addColumn("leave_requests", new TableColumn({
            name: "managerId",
            type: "int",
            isNullable: true,
        }));

        await queryRunner.addColumn("leave_requests", new TableColumn({
            name: "isUrgent",
            type: "boolean",
            default: false,
            isNullable: false,
        }));
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Rollback: remove new columns
        await queryRunner.dropColumn("leave_requests", "isUrgent");
        await queryRunner.dropColumn("leave_requests", "managerId");
        await queryRunner.dropColumn("leave_requests", "originalReason");
        await queryRunner.dropColumn("leave_requests", "originalEndDate");
        await queryRunner.dropColumn("leave_requests", "originalStartDate");
        await queryRunner.dropColumn("leave_requests", "modificationHistory");
        await queryRunner.dropColumn("leave_requests", "currentStage");
        await queryRunner.dropColumn("leave_requests", "source");

        // Rollback: add old columns back (if needed)
        await queryRunner.query(`ALTER TABLE leave_requests ADD COLUMN isEmergencyLeave TINYINT NOT NULL DEFAULT 0`);
        await queryRunner.query(`ALTER TABLE leave_requests ADD COLUMN delegatedTo VARCHAR(255) NULL`);
        await queryRunner.query(`ALTER TABLE leave_requests ADD COLUMN handoverNotes TEXT NULL`);

        // Rollback: change employeeId back to VARCHAR(255)
        await queryRunner.query(`ALTER TABLE leave_requests MODIFY COLUMN employeeId VARCHAR(255) NOT NULL`);
    }
} 