employeeId,employeeName,date,checkInTime,checkOutTime,status,workHours,isRemote,location,department,notes
1001,<PERSON>,2023-05-15,09:00,17:30,present,8.5,false,Office,Engineering,Regular day
1002,<PERSON>,2023-05-15,09:15,17:45,present,8.5,true,Remote,Design,Working from home
1003,<PERSON>,2023-05-15,08:45,16:30,present,7.75,false,Office,Marketing,Left early for doctor appointment
1004,<PERSON>,2023-05-15,10:15,18:00,late,7.75,false,Office,Finance,Late due to traffic
1005,<PERSON>,2023-05-15,09:30,18:30,present,9,false,Office,HR,Overtime work
1006,<PERSON>,2023-05-15,09:00,17:00,present,8,true,Remote,Engineering,Regular day
1007,<PERSON>,2023-05-15,,,absent,0,false,,Sales,Sick leave
1008,<PERSON>,2023-05-15,09:00,13:00,half_day,4,false,Office,Customer Support,Half day
1009,<PERSON>,2023-05-15,08:30,17:30,present,9,false,<PERSON>,IT,Early arrival
1010,<PERSON>,2023-05-15,09:05,17:45,present,8.67,true,Remote,<PERSON><PERSON><PERSON>,Regular day 