import React, { useState, useEffect } from 'react';
import { Shield, Lock, Map, Users, AlertTriangle, ChevronLeft, ChevronRight, User } from 'lucide-react';
import { SystemLog } from './types';
import { IPGeoLocationMap } from './IPGeoLocationMap';
import { SessionAnalytics } from './SessionAnalytics';
import { extractIpAddress } from './utils/geoLocation';
import { extractSessions, getSecurityInsights } from './utils/securityAnalysis';
import { useTheme } from './ThemeContext';

interface SecurityDashboardProps {
  logs: SystemLog[];
  onLogSelect?: (logId: string) => void;
  onBack?: () => void;
}

export const SecurityDashboard: React.FC<SecurityDashboardProps> = ({
  logs,
  onLogSelect,
  onBack
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'geolocation' | 'sessions'>('overview');
  const [highlightedIp, setHighlightedIp] = useState<string | null>(null);
  const [securityStats, setSecurityStats] = useState<any>(null);
  const { theme } = useTheme();
  
  useEffect(() => {
    // Calculate security stats for overview
    const calculateStats = async () => {
      const sessions = extractSessions(logs);
      const insights = getSecurityInsights(logs, sessions);
      
      // Extract IP addresses
      const ipAddresses = new Set<string>();
      logs.forEach(log => {
        const ip = extractIpAddress(log.details) || extractIpAddress(log.action);
        if (ip) ipAddresses.add(ip);
      });
      
      setSecurityStats({
        ...insights,
        totalIPs: ipAddresses.size
      });
    };
    
    calculateStats();
  }, [logs]);
  
  const handleIpSelect = (ip: string) => {
    setHighlightedIp(ip === highlightedIp ? null : ip);
  };
  
  const handleLogSelection = (logId: string) => {
    if (onLogSelect) {
      onLogSelect(logId);
    }
  };

  const getThreatLevelColor = (level: number) => {
    if (level >= 70) return 'text-red-600 dark:text-red-400';
    if (level >= 40) return 'text-orange-600 dark:text-orange-400';
    if (level >= 20) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-green-600 dark:text-green-400';
  };
  
  const getThreatLevelBg = (level: number) => {
    if (level >= 70) return 'bg-red-100 dark:bg-red-900/30';
    if (level >= 40) return 'bg-orange-100 dark:bg-orange-900/30';
    if (level >= 20) return 'bg-yellow-100 dark:bg-yellow-900/30';
    return 'bg-green-100 dark:bg-green-900/30';
  };
  
  const getThreatLevelText = (level: number) => {
    if (level >= 70) return 'High';
    if (level >= 40) return 'Medium';
    if (level >= 20) return 'Low';
    return 'Safe';
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 text-${theme.fontSize}`}>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <Shield className={`h-6 w-6 text-${theme.color}-600 dark:text-${theme.color}-400`} />
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">Security Dashboard</h2>
        </div>
        
        {onBack && (
          <button
            onClick={onBack}
            className={`flex items-center gap-1 text-${theme.color}-600 hover:text-${theme.color}-800 dark:text-${theme.color}-400 dark:hover:text-${theme.color}-300`}
          >
            <ChevronLeft className="h-4 w-4" />
            <span>Back to Logs</span>
          </button>
        )}
      </div>
      
      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
        <div className="flex -mb-px space-x-6">
          <button
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'overview'
                ? `border-${theme.color}-500 text-${theme.color}-600 dark:text-${theme.color}-400`
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
            onClick={() => setActiveTab('overview')}
          >
            Overview
          </button>
          <button
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'geolocation'
                ? `border-${theme.color}-500 text-${theme.color}-600 dark:text-${theme.color}-400`
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
            onClick={() => setActiveTab('geolocation')}
          >
            IP Geolocation
          </button>
          <button
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'sessions'
                ? `border-${theme.color}-500 text-${theme.color}-600 dark:text-${theme.color}-400`
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
            onClick={() => setActiveTab('sessions')}
          >
            Sessions & Anomalies
          </button>
        </div>
      </div>
      
      {/* Content based on active tab */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Summary cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-600">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Total Sessions</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                    {securityStats?.totalSessions || 0}
                  </p>
                </div>
                <Users className="h-8 w-8 text-gray-400 dark:text-gray-500" />
              </div>
              <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
                {securityStats?.activeSessions || 0} active now
              </div>
            </div>
            
            <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-600">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">IP Locations</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                    {securityStats?.totalIPs || 0}
                  </p>
                </div>
                <Map className="h-8 w-8 text-gray-400 dark:text-gray-500" />
              </div>
              <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
                From {securityStats?.topSuspiciousIPs?.length || 0} countries
              </div>
            </div>
            
            <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-600">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Security Anomalies</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                    {securityStats?.totalAnomalies || 0}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-gray-400 dark:text-gray-500" />
              </div>
              <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
                {securityStats?.highSeverityAnomalies || 0} high severity
              </div>
            </div>
            
            <div className="bg-white dark:bg-gray-700 rounded-lg shadow p-4 border border-gray-200 dark:border-gray-600">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Overall Threat Level</p>
                  <div className="flex items-center gap-2 mt-1">
                    <p className={`text-xl font-bold ${
                      getThreatLevelColor(securityStats?.highSeverityAnomalies > 0 ? 80 : 
                        securityStats?.totalAnomalies > 5 ? 60 : 
                        securityStats?.totalAnomalies > 0 ? 30 : 10)
                    }`}>
                      {getThreatLevelText(securityStats?.highSeverityAnomalies > 0 ? 80 : 
                        securityStats?.totalAnomalies > 5 ? 60 : 
                        securityStats?.totalAnomalies > 0 ? 30 : 10)}
                    </p>
                  </div>
                </div>
                <Lock className="h-8 w-8 text-gray-400 dark:text-gray-500" />
              </div>
              <div className="mt-3">
                <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5">
                  <div 
                    className={`h-2.5 rounded-full ${
                      securityStats?.highSeverityAnomalies > 0 ? 'bg-red-600' : 
                      securityStats?.totalAnomalies > 5 ? 'bg-orange-500' : 
                      securityStats?.totalAnomalies > 0 ? 'bg-yellow-400' : 'bg-green-500'
                    }`}
                    style={{ width: `${securityStats?.highSeverityAnomalies > 0 ? 80 : 
                      securityStats?.totalAnomalies > 5 ? 60 : 
                      securityStats?.totalAnomalies > 0 ? 30 : 10}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Top threats and recent anomalies */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Top threatened users */}
            <div className="bg-white dark:bg-gray-700 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Top Threatened Users</h3>
              
              {securityStats?.topThreatenedUsers?.length > 0 ? (
                <div className="space-y-4">
                  {securityStats.topThreatenedUsers.map((userData: any, index: number) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`rounded-full h-10 w-10 flex items-center justify-center ${
                          getThreatLevelBg(userData.threatScore)
                        }`}>
                          <User className={`h-5 w-5 ${getThreatLevelColor(userData.threatScore)}`} />
                        </div>
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">{userData.user}</p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            Threat Score: {userData.threatScore.toFixed(0)}
                          </p>
                        </div>
                      </div>
                      <button
                        onClick={() => setActiveTab('sessions')}
                        className={`text-sm text-${theme.color}-600 hover:text-${theme.color}-800 dark:text-${theme.color}-400 dark:hover:text-${theme.color}-300 flex items-center gap-1`}
                      >
                        <span>Details</span>
                        <ChevronRight className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400">No threat data available</p>
              )}
            </div>
            
            {/* Recent security anomalies */}
            <div className="bg-white dark:bg-gray-700 rounded-lg shadow border border-gray-200 dark:border-gray-600 p-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Recent Security Anomalies</h3>
              
              {securityStats?.recentAnomalies?.length > 0 ? (
                <div className="space-y-3">
                  {securityStats.recentAnomalies.map((anomaly: any, index: number) => (
                    <div key={index} className={`p-3 rounded-lg ${
                      anomaly.severity === 'critical' ? 'bg-red-50 dark:bg-red-900/20' :
                      anomaly.severity === 'high' ? 'bg-orange-50 dark:bg-orange-900/20' :
                      anomaly.severity === 'medium' ? 'bg-yellow-50 dark:bg-yellow-900/20' :
                      'bg-blue-50 dark:bg-blue-900/20'
                    }`}>
                      <div className="flex items-start gap-3">
                        <AlertTriangle className={`h-5 w-5 mt-0.5 ${
                          anomaly.severity === 'critical' ? 'text-red-600 dark:text-red-400' :
                          anomaly.severity === 'high' ? 'text-orange-600 dark:text-orange-400' :
                          anomaly.severity === 'medium' ? 'text-yellow-600 dark:text-yellow-400' :
                          'text-blue-600 dark:text-blue-400'
                        }`} />
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">
                            {anomaly.type.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                          </p>
                          <p className="text-sm text-gray-600 dark:text-gray-300">{anomaly.description}</p>
                          {anomaly.relatedLogIds?.length > 0 && (
                            <button
                              className={`mt-1 text-xs text-${theme.color}-600 hover:text-${theme.color}-800 dark:text-${theme.color}-400 dark:hover:text-${theme.color}-300`}
                              onClick={() => handleLogSelection(anomaly.relatedLogIds[0])}
                            >
                              View related log
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400">No recent anomalies detected</p>
              )}
            </div>
          </div>
        </div>
      )}
      
      {activeTab === 'geolocation' && (
        <IPGeoLocationMap 
          logs={logs} 
          highlightedIp={highlightedIp}
          onIpSelect={handleIpSelect}
        />
      )}
      
      {activeTab === 'sessions' && (
        <SessionAnalytics logs={logs} onLogSelect={handleLogSelection} />
      )}
    </div>
  );
}; 