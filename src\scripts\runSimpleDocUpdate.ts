import { AppDataSource } from '../config/database';
import * as fs from 'fs';
import * as path from 'path';

async function runSimpleDocumentUpdate() {
  console.log('Initializing data source...');
  
  try {
    await AppDataSource.initialize();
    console.log('Data source initialized');
    
    console.log('Executing SQL commands directly...');
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    
    // Start transaction
    await queryRunner.startTransaction();
    
    try {
      // Update pending status to verified
      console.log('Updating pending status to verified...');
      await queryRunner.query(`
        UPDATE employee_documents 
        SET verificationStatus = 'verified' 
        WHERE verificationStatus = 'pending'
      `);
      
      // Remove the columns we don't need
      console.log('Removing unnecessary columns...');
      await queryRunner.query(`
        ALTER TABLE employee_documents 
        DROP COLUMN IF EXISTS documentNumber, 
        DROP COLUMN IF EXISTS issueDate, 
        DROP COLUMN IF EXISTS expiryDate
      `);
      
      // Commit transaction
      await queryRunner.commitTransaction();
      console.log('Database update completed successfully!');
    } catch (error) {
      // Rollback transaction in case of error
      await queryRunner.rollbackTransaction();
      console.error('Database update failed, transaction rolled back:', error);
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }
    
    await AppDataSource.destroy();
    console.log('Connection closed');
    
    process.exit(0);
  } catch (error) {
    console.error('Error during database update:', error);
    process.exit(1);
  }
}

runSimpleDocumentUpdate(); 