export interface TicketData {
  id: string;
  ticketNumber: string;
  title: string;
  description: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  createdBy: {
    userId: string;
    name: string;
    department: string;
    role: string;
  };
  createdAt: string;
  lastUpdated: string;
  visibleTo: string[];
  departmentChain: string[];
  comments: Array<{
    id: string;
    content: string;
    author: string;
    createdAt: string;
    type?: 'system' | 'user';
  }>;
  assignedTo?: {
    id: string;
    name: string;
    department: string;
  };
  closedAt?: string;
  closedBy?: string;
  closureReason?: string;
  closureNotes?: string;
  referredTo?: {
    id: string;
    name: string;
    department: string;
  };
  referredBy?: {
    id: string;
    name: string;
    department: string;
  };
} 