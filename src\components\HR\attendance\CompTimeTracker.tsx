import React, { useState, useEffect, useMemo } from 'react';
import { CompensatoryTimeTracking } from '../../../types/attendance';
import { 
  Clock, 
  Plus,
  Check,
  X,
  Calendar,
  Save,
  Edit2,
  Trash2,
  AlertCircle,
  Filter,
  Download,
  Search
} from 'lucide-react';

interface CompTimeTrackerProps {
  employeeId?: number;
  isAdmin: boolean;
}

const CompTimeTracker: React.FC<CompTimeTrackerProps> = ({ employeeId, isAdmin }) => {
  const [compEntries, setCompEntries] = useState<CompensatoryTimeTracking[]>([]);
  const [loading, setLoading] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [editingEntry, setEditingEntry] = useState<CompensatoryTimeTracking | null>(null);
  
  // Add filtering state
  const [filters, setFilters] = useState({
    status: '',
    dateRange: { start: '', end: '' },
    searchTerm: '',
  });

  // Mock data initialization
  useEffect(() => {
    setLoading(true);
    setTimeout(() => {
      const mockEntries: CompensatoryTimeTracking[] = [
        {
          id: 1,
          employeeId: 101,
          employeeName: 'John Smith',
          dateWorked: '2023-05-27', // A Saturday
          hoursWorked: 5,
          reason: 'Emergency server maintenance',
          expiryDate: '2023-08-27',
          status: 'approved',
          approvedBy: 201,
          approverName: 'Manager Name'
        },
        {
          id: 2,
          employeeId: 101,
          employeeName: 'John Smith',
          dateWorked: '2023-06-19', // A holiday
          hoursWorked: 8,
          reason: 'Product launch preparation',
          expiryDate: '2023-09-19',
          status: 'pending'
        },
        {
          id: 3,
          employeeId: 102,
          employeeName: 'Emily Johnson',
          dateWorked: '2023-06-25', // A Sunday
          hoursWorked: 4,
          reason: 'Client demo preparation',
          expiryDate: '2023-09-25',
          status: 'used',
          dateUsed: '2023-07-10',
          leaveRequestId: 125
        }
      ];
      
      setCompEntries(
        employeeId 
          ? mockEntries.filter(entry => entry.employeeId === employeeId)
          : mockEntries
      );
      setLoading(false);
    }, 800);
  }, [employeeId]);

  // Filtered entries
  const filteredEntries = useMemo(() => {
    return compEntries.filter(entry => {
      // Filter by status
      if (filters.status && entry.status !== filters.status) {
        return false;
      }
      
      // Filter by date range
      if (filters.dateRange.start && new Date(entry.dateWorked) < new Date(filters.dateRange.start)) {
        return false;
      }
      if (filters.dateRange.end && new Date(entry.dateWorked) > new Date(filters.dateRange.end)) {
        return false;
      }
      
      // Filter by search term
      if (filters.searchTerm && 
          !entry.reason.toLowerCase().includes(filters.searchTerm.toLowerCase()) &&
          !entry.employeeName.toLowerCase().includes(filters.searchTerm.toLowerCase())) {
        return false;
      }
      
      return true;
    });
  }, [compEntries, filters]);

  // Calculate comp time total
  const compTimeSummary = useMemo(() => {
    const totalHours = compEntries.reduce((total, entry) => {
      if (entry.status === 'approved' || entry.status === 'pending') {
        return total + entry.hoursWorked;
      }
      return total;
    }, 0);
    
    const usedHours = compEntries.reduce((total, entry) => {
      if (entry.status === 'used') {
        return total + entry.hoursWorked;
      }
      return total;
    }, 0);
    
    const pendingHours = compEntries.reduce((total, entry) => {
      if (entry.status === 'pending') {
        return total + entry.hoursWorked;
      }
      return total;
    }, 0);
    
    return { totalHours, usedHours, pendingHours, availableHours: totalHours - usedHours };
  }, [compEntries]);

  const handleAddEntry = () => {
    const today = new Date();
    const expiryDate = new Date();
    expiryDate.setDate(today.getDate() + 90); // 3 months expiry by default
    
    setEditingEntry({
      employeeId: employeeId || 0,
      employeeName: '',
      dateWorked: today.toISOString().split('T')[0],
      hoursWorked: 8,
      reason: '',
      expiryDate: expiryDate.toISOString().split('T')[0],
      status: 'pending'
    });
    setShowForm(true);
  };

  const handleEditEntry = (entry: CompensatoryTimeTracking) => {
    setEditingEntry({ ...entry });
    setShowForm(true);
  };

  const handleDeleteEntry = (id: number | undefined) => {
    if (window.confirm('Are you sure you want to delete this compensatory time entry?')) {
      setCompEntries(compEntries.filter(entry => entry.id !== id));
    }
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    if (!editingEntry) return;
    
    const { name, value, type } = e.target;
    const newValue = type === 'number' ? parseFloat(value) : value;
    
    setEditingEntry({
      ...editingEntry,
      [name]: newValue
    });
  };

  const handleSaveEntry = () => {
    if (!editingEntry) return;
    
    // Add validation here
    if (editingEntry.hoursWorked <= 0) {
      alert('Hours worked must be greater than 0');
      return;
    }
    
    if (!editingEntry.reason) {
      alert('Please provide a reason for the compensatory time');
      return;
    }
    
    if (editingEntry.id) {
      // Update existing entry
      setCompEntries(
        compEntries.map(entry => 
          entry.id === editingEntry.id ? editingEntry : entry
        )
      );
    } else {
      // Add new entry with a generated ID
      const newId = Math.max(...compEntries.map(e => e.id || 0), 0) + 1;
      setCompEntries([...compEntries, { ...editingEntry, id: newId }]);
    }
    
    setShowForm(false);
    setEditingEntry(null);
  };

  const handleApprove = (id: number | undefined) => {
    setCompEntries(
      compEntries.map(entry => 
        entry.id === id 
          ? { 
              ...entry, 
              status: 'approved', 
              approvedBy: 201, // Mock manager ID
              approverName: 'Manager Name' 
            } 
          : entry
      )
    );
  };

  const handleReject = (id: number | undefined) => {
    setCompEntries(
      compEntries.map(entry => 
        entry.id === id ? { ...entry, status: 'rejected' } : entry
      )
    );
  };

  const formatStatus = (status: string) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };
  
  const getStatusStyles = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'used':
        return 'bg-blue-100 text-blue-800';
      case 'expired':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Handle filter changes
  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (name.includes('dateRange')) {
      const datePart = name.split('.')[1]; // Get 'start' or 'end'
      setFilters({
        ...filters,
        dateRange: {
          ...filters.dateRange,
          [datePart]: value
        }
      });
    } else {
      setFilters({
        ...filters,
        [name]: value
      });
    }
  };

  // Clear filters function
  const clearFilters = () => {
    setFilters({
      status: '',
      dateRange: { start: '', end: '' },
      searchTerm: '',
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-4 gap-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Compensatory Time Off</h3>
          <p className="text-sm text-gray-500">Track and manage overtime work and compensation leave</p>
        </div>
        
        {/* Summary cards */}
        <div className="flex flex-wrap gap-2">
          <div className="bg-blue-50 p-2 rounded-md text-blue-800 text-xs font-medium">
            <span className="block">Available Hours</span>
            <span className="text-lg">{compTimeSummary.availableHours}</span>
          </div>
          <div className="bg-yellow-50 p-2 rounded-md text-yellow-800 text-xs font-medium">
            <span className="block">Pending Hours</span>
            <span className="text-lg">{compTimeSummary.pendingHours}</span>
          </div>
          <div className="bg-green-50 p-2 rounded-md text-green-800 text-xs font-medium">
            <span className="block">Used Hours</span>
            <span className="text-lg">{compTimeSummary.usedHours}</span>
          </div>
        </div>
        
        {(isAdmin || employeeId) && (
          <button
            onClick={handleAddEntry}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Comp Time
          </button>
        )}
      </div>
      
      {/* Filter section */}
      <div className="mb-4 p-3 bg-gray-50 rounded-md border border-gray-200">
        <div className="flex flex-wrap gap-3 items-end">
          <div>
            <label htmlFor="searchTerm" className="block text-xs font-medium text-gray-700 mb-1">Search</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                id="searchTerm"
                name="searchTerm"
                value={filters.searchTerm}
                onChange={handleFilterChange}
                placeholder="Search reason or name"
                className="pl-10 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>
          
          <div>
            <label htmlFor="status" className="block text-xs font-medium text-gray-700 mb-1">Status</label>
            <select
              id="status"
              name="status"
              value={filters.status}
              onChange={handleFilterChange}
              className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
            >
              <option value="">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
              <option value="used">Used</option>
              <option value="expired">Expired</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="dateRange.start" className="block text-xs font-medium text-gray-700 mb-1">From Date</label>
            <input
              type="date"
              id="dateRange.start"
              name="dateRange.start"
              value={filters.dateRange.start}
              onChange={handleFilterChange}
              className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <label htmlFor="dateRange.end" className="block text-xs font-medium text-gray-700 mb-1">To Date</label>
            <input
              type="date"
              id="dateRange.end"
              name="dateRange.end"
              value={filters.dateRange.end}
              onChange={handleFilterChange}
              className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <button 
              onClick={clearFilters}
              className="px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Clear Filters
            </button>
          </div>
          
          {isAdmin && (
            <div className="ml-auto">
              <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                <Download className="h-4 w-4 mr-1" />
                Export
              </button>
            </div>
          )}
        </div>
      </div>
      
      {loading ? (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <>
          {compEntries.length === 0 ? (
            <div className="bg-gray-50 p-8 rounded-md text-center">
              <Clock className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <h3 className="text-sm font-medium text-gray-900">No compensatory time entries</h3>
              <p className="text-xs text-gray-500 mb-4">
                {employeeId 
                  ? 'You have no compensatory time entries yet'
                  : 'No compensatory time entries found'}
              </p>
              {(isAdmin || employeeId) && (
                <button
                  onClick={handleAddEntry}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Record Comp Time
                </button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    {!employeeId && (
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Employee
                      </th>
                    )}
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date Worked
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Hours
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Reason
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Expiry
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredEntries.map((entry) => (
                    <tr key={entry.id} className="hover:bg-gray-50">
                      {!employeeId && (
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{entry.employeeName}</div>
                          <div className="text-xs text-gray-500">ID: {entry.employeeId}</div>
                        </td>
                      )}
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(entry.dateWorked).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {entry.hoursWorked} hrs
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                        {entry.reason}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusStyles(entry.status)}`}>
                          {formatStatus(entry.status)}
                        </span>
                        {entry.status === 'used' && entry.dateUsed && (
                          <div className="text-xs text-gray-500 mt-1">
                            Used on: {new Date(entry.dateUsed).toLocaleDateString()}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(entry.expiryDate).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        {isAdmin && entry.status === 'pending' && (
                          <div className="flex justify-end space-x-1">
                            <button
                              onClick={() => handleApprove(entry.id)}
                              className="text-green-600 hover:text-green-900"
                              title="Approve"
                            >
                              <Check className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleReject(entry.id)}
                              className="text-red-600 hover:text-red-900"
                              title="Reject"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </div>
                        )}
                        {(isAdmin || (employeeId && entry.status === 'pending')) && (
                          <div className="flex justify-end space-x-1 mt-1">
                            <button
                              onClick={() => handleEditEntry(entry)}
                              className="text-blue-600 hover:text-blue-900"
                              title="Edit"
                            >
                              <Edit2 className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleDeleteEntry(entry.id)}
                              className="text-red-600 hover:text-red-900"
                              title="Delete"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              
              {filteredEntries.length === 0 && (
                <div className="text-center py-4 text-gray-500">
                  No results found with the current filters.
                </div>
              )}
            </div>
          )}
          
          {/* Comp Time Entry Form */}
          {showForm && editingEntry && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    {editingEntry.id ? 'Edit Compensatory Time' : 'Record Compensatory Time'}
                  </h3>
                  <button
                    onClick={() => {
                      setShowForm(false);
                      setEditingEntry(null);
                    }}
                    className="text-gray-400 hover:text-gray-500"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>
                
                <div className="space-y-4">
                  {isAdmin && !employeeId && (
                    <div>
                      <label htmlFor="employeeName" className="block text-sm font-medium text-gray-700 mb-1">
                        Employee Name
                      </label>
                      <input
                        type="text"
                        id="employeeName"
                        name="employeeName"
                        value={editingEntry.employeeName}
                        onChange={handleFormChange}
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>
                  )}
                  
                  <div>
                    <label htmlFor="dateWorked" className="block text-sm font-medium text-gray-700 mb-1">
                      Date Worked
                    </label>
                    <input
                      type="date"
                      id="dateWorked"
                      name="dateWorked"
                      value={editingEntry.dateWorked}
                      onChange={handleFormChange}
                      className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="hoursWorked" className="block text-sm font-medium text-gray-700 mb-1">
                      Hours Worked
                    </label>
                    <input
                      type="number"
                      id="hoursWorked"
                      name="hoursWorked"
                      value={editingEntry.hoursWorked}
                      onChange={handleFormChange}
                      min="0.5"
                      step="0.5"
                      className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-1">
                      Reason
                    </label>
                    <textarea
                      id="reason"
                      name="reason"
                      rows={3}
                      value={editingEntry.reason}
                      onChange={handleFormChange}
                      className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      placeholder="Explain why you worked on a holiday or weekend"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="expiryDate" className="block text-sm font-medium text-gray-700 mb-1">
                      Expiry Date
                    </label>
                    <input
                      type="date"
                      id="expiryDate"
                      name="expiryDate"
                      value={editingEntry.expiryDate}
                      onChange={handleFormChange}
                      className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Compensatory time must be used before this date
                    </p>
                  </div>
                  
                  {isAdmin && (
                    <div>
                      <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                        Status
                      </label>
                      <select
                        id="status"
                        name="status"
                        value={editingEntry.status}
                        onChange={handleFormChange}
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      >
                        <option value="pending">Pending</option>
                        <option value="approved">Approved</option>
                        <option value="rejected">Rejected</option>
                        <option value="used">Used</option>
                        <option value="expired">Expired</option>
                      </select>
                    </div>
                  )}
                </div>
                
                <div className="mt-5 flex justify-end">
                  <button
                    type="button"
                    onClick={() => {
                      setShowForm(false);
                      setEditingEntry(null);
                    }}
                    className="mr-3 inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleSaveEntry}
                    className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save
                  </button>
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default CompTimeTracker; 