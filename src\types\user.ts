export type UserRole = 'IT_ADMIN' | 'IT_STAFF' | 'EMPLOYEE' | 'CEO' | 'FINANCE_MANAGER' | 'DEPT_HEAD' | 'VIEW' | 'HR_ADMIN' | 'HR_STAFF';

export interface UserPermissions {
  canViewAllTickets: boolean;
  canCreateTickets: boolean;
  canCreateTicketsForOthers: boolean;
  canEditTickets: boolean;
  canDeleteTickets: boolean;
  canCloseTickets: boolean;
  canLockTickets: boolean;
  canAssignTickets: boolean;
  canEscalateTickets: boolean;
}

export interface User {
  id: string;
  name: string;
  email: string;
  password: string;
  project: string | null;
  location: string | null;
  role: UserRole;
  department: string;
  permissions: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserCreateInput {
  name: string;
  email: string;
  password: string;
  project?: string;
  location?: string;
  role: UserRole;
  department: string;
  permissions?: string[];
  isActive?: boolean;
} 