import React from 'react';
import { 
  FolderOpen, 
  Play, 
  CheckCircle, 
  ListTodo, 
  Clock, 
  AlertTriangle,
  Users,
  TrendingUp
} from 'lucide-react';
import { ProjectStats as ProjectStatsType } from '../../types/project';

interface ProjectStatsProps {
  stats: ProjectStatsType;
}

export default function ProjectStats({ stats }: ProjectStatsProps) {
  const statCards = [
    {
      title: 'Total Projects',
      value: stats.totalProjects,
      icon: FolderOpen,
      color: 'bg-blue-500',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-600'
    },
    {
      title: 'Active Projects',
      value: stats.activeProjects,
      icon: Play,
      color: 'bg-green-500',
      bgColor: 'bg-green-50',
      textColor: 'text-green-600'
    },
    {
      title: 'Completed Projects',
      value: stats.completedProjects,
      icon: CheckCircle,
      color: 'bg-purple-500',
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-600'
    },
    {
      title: 'Total Tasks',
      value: stats.totalTasks,
      icon: ListTodo,
      color: 'bg-indigo-500',
      bgColor: 'bg-indigo-50',
      textColor: 'text-indigo-600'
    },
    {
      title: 'Completed Tasks',
      value: stats.completedTasks,
      icon: CheckCircle,
      color: 'bg-emerald-500',
      bgColor: 'bg-emerald-50',
      textColor: 'text-emerald-600'
    },
    {
      title: 'Overdue Tasks',
      value: stats.overdueTasks,
      icon: AlertTriangle,
      color: 'bg-red-500',
      bgColor: 'bg-red-50',
      textColor: 'text-red-600'
    },
    {
      title: 'My Projects',
      value: stats.myProjects,
      icon: Users,
      color: 'bg-orange-500',
      bgColor: 'bg-orange-50',
      textColor: 'text-orange-600'
    },
    {
      title: 'My Tasks',
      value: stats.myTasks,
      icon: Clock,
      color: 'bg-cyan-500',
      bgColor: 'bg-cyan-50',
      textColor: 'text-cyan-600'
    }
  ];

  const completionRate = stats.totalTasks > 0 ? Math.round((stats.completedTasks / stats.totalTasks) * 100) : 0;
  const projectCompletionRate = stats.totalProjects > 0 ? Math.round((stats.completedProjects / stats.totalProjects) * 100) : 0;

  return (
    <div className="space-y-6">
      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
                </div>
                <div className={`${stat.bgColor} p-3 rounded-lg`}>
                  <Icon className={`h-6 w-6 ${stat.textColor}`} />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Progress Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Task Completion Rate */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Task Completion Rate</h3>
            <span className="text-2xl font-bold text-blue-600">{completionRate}%</span>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
            <div 
              className="bg-blue-600 h-3 rounded-full transition-all duration-500"
              style={{ width: `${completionRate}%` }}
            ></div>
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Completed:</span>
              <span className="font-semibold text-gray-900 ml-1">{stats.completedTasks}</span>
            </div>
            <div>
              <span className="text-gray-600">Total:</span>
              <span className="font-semibold text-gray-900 ml-1">{stats.totalTasks}</span>
            </div>
          </div>
        </div>

        {/* Project Completion Rate */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Project Completion Rate</h3>
            <span className="text-2xl font-bold text-green-600">{projectCompletionRate}%</span>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
            <div 
              className="bg-green-600 h-3 rounded-full transition-all duration-500"
              style={{ width: `${projectCompletionRate}%` }}
            ></div>
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Completed:</span>
              <span className="font-semibold text-gray-900 ml-1">{stats.completedProjects}</span>
            </div>
            <div>
              <span className="text-gray-600">Total:</span>
              <span className="font-semibold text-gray-900 ml-1">{stats.totalProjects}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Insights */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Insights</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Workload */}
          <div className="text-center">
            <div className="bg-blue-50 p-4 rounded-lg mb-3">
              <TrendingUp className="h-8 w-8 text-blue-600 mx-auto" />
            </div>
            <h4 className="font-semibold text-gray-900">Your Workload</h4>
            <p className="text-sm text-gray-600 mt-1">
              You're involved in <span className="font-semibold">{stats.myProjects}</span> projects
              with <span className="font-semibold">{stats.myTasks}</span> assigned tasks
            </p>
          </div>

          {/* Urgent Items */}
          <div className="text-center">
            <div className="bg-red-50 p-4 rounded-lg mb-3">
              <AlertTriangle className="h-8 w-8 text-red-600 mx-auto" />
            </div>
            <h4 className="font-semibold text-gray-900">Attention Needed</h4>
            <p className="text-sm text-gray-600 mt-1">
              <span className="font-semibold text-red-600">{stats.overdueTasks}</span> tasks are overdue
              and need immediate attention
            </p>
          </div>

          {/* Progress */}
          <div className="text-center">
            <div className="bg-green-50 p-4 rounded-lg mb-3">
              <CheckCircle className="h-8 w-8 text-green-600 mx-auto" />
            </div>
            <h4 className="font-semibold text-gray-900">Overall Progress</h4>
            <p className="text-sm text-gray-600 mt-1">
              <span className="font-semibold text-green-600">{stats.activeProjects}</span> projects are currently active
              with good momentum
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
