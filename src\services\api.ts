import axios, { AxiosRequestConfig } from 'axios';
import { toast } from 'react-hot-toast';

// Declare custom window properties for error handling
declare global {
  interface Window {
    lastErrorNotificationTime?: number;
    lastErrorKey?: string;
  }
}

// Safely determine environment without relying on process.env
const isProduction = () => {
  // Check for production hostname patterns or other indicators
  const hostname = window.location.hostname;
  return !(hostname === 'localhost' || hostname === '127.0.0.1');
};

// Determine the base URL for API calls
// Use a centralized, configurable approach for easier deployment
const API_SERVER = {
  development: 'http://localhost:5000', 
  production: ''
};

// Get base URL based on environment
let baseURL = isProduction() ? API_SERVER.production : API_SERVER.development;

// Log configuration for debugging
console.log('Environment:', isProduction() ? 'production' : 'development');
console.log('API configured with baseURL:', baseURL);

// Create an axios instance with default config
const api = axios.create({
  baseURL,
  timeout: 30000, // 30 seconds default timeout
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
});

// Add a request interceptor to add the auth token to all requests
api.interceptors.request.use(
  (config) => {
    // Make sure all API requests have /api prefix
    if (!config.url?.startsWith('/api/') && !config.url?.startsWith('http')) {
      config.url = '/api' + (config.url?.startsWith('/') ? config.url : `/${config.url}`);
    }
    
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
      // console.log('🔑 Adding authorization token to request:', config.url);
    } else {
      // console.warn('⚠️ No auth token found for request:', config.url);
    }
    
    // Add diagnostic logging for all requests - commented out for production
    // console.log(`📨 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    
    // Check if the request data is FormData and remove Content-Type header
    // to let the browser set the correct boundary
    if (config.data instanceof FormData) {
      // Remove any existing Content-Type header to let Axios set it properly with boundary
      delete config.headers['Content-Type'];
      // console.log('FormData detected: Content-Type header removed to allow automatic boundary');
    }
    
    return config;
  },
  (error) => {
    console.error('🔥 Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Add a token refresh function
const refreshAuthToken = async () => {
  try {
    // Try to refresh the token by calling a refresh endpoint
    const refreshURL = baseURL.includes('/api') 
      ? baseURL + '/auth/refresh'  // baseURL already has /api
      : baseURL + '/api/auth/refresh'; // baseURL doesn't have /api
    
    const response = await axios.post(refreshURL, {}, {
      withCredentials: true
    });
    
    if (response.data?.token) {
      // Store the new token
      localStorage.setItem('authToken', response.data.token);
      console.log('Token refreshed successfully');
      return response.data.token;
    }
    return null;
  } catch (error) {
    console.error('Token refresh failed:', error);
    return null;
  }
};

// Add a response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    // Get the original request config
    const originalRequest = error.config;
    
    // Check if retry flag exists to prevent infinite loops
    if (originalRequest._retry) {
      return Promise.reject(error);
    }
    
    // Handle authentication errors
    if (error.response?.status === 401) {
      // Skip automatic logout for specific protected routes that shouldn't trigger redirects
      const isBillingInvoiceRequest = originalRequest.url?.includes('/billing-invoices');
      const isUploadRequest = originalRequest.url?.includes('/uploads/');
      const isRoleRequest = originalRequest.url?.includes('/roles') || originalRequest.url?.includes('/role');
      const isUserRequest = originalRequest.url?.includes('/users');
      
      if (isBillingInvoiceRequest || isUploadRequest || isRoleRequest || isUserRequest) {
        console.log('Authentication error for protected route:', originalRequest.url);
        console.log('Attempting to refresh token...');
        
        // Mark this request as retried to prevent infinite loops
        originalRequest._retry = true;
        
        try {
          // First, get the cached token - it might have been refreshed by another request
          let freshToken = localStorage.getItem('authToken');
          
          // If no token in storage or if it matches the failed one, try to refresh
          if (!freshToken || (originalRequest.headers?.Authorization && originalRequest.headers.Authorization.includes(freshToken))) {
            freshToken = await refreshAuthToken();
          }
          
          if (freshToken) {
            // Update the authorization header
            originalRequest.headers.Authorization = `Bearer ${freshToken}`;
            console.log('Retrying request with fresh token:', originalRequest.url);
            
            // Return a new request with the fresh token
            return axios(originalRequest);
          } else {
            console.log('Token refresh failed, but continuing without logout for:', originalRequest.url);
            // Don't log out for role/user operations, just let the request fail
            return Promise.reject(error);
          }
        } catch (refreshError) {
          console.error('Error during token refresh attempt:', refreshError);
          return Promise.reject(error);
        }
      } else {
        // For other requests, proceed with logout
        console.log('Authentication failed, logging out');
        localStorage.removeItem('authToken');
        localStorage.removeItem('userData');
        
        // Redirect to login page if not already there
        if (!window.location.pathname.includes('/login')) {
          window.location.href = '/login';
        }
      }
    }
    
    // Handle network errors
    if (!error.response) {
      return Promise.reject({
        message: 'Network error. Please check your connection.',
        originalError: error
      });
    }
    
    // Handle timeout errors
    if (error.code === 'ECONNABORTED') {
      return Promise.reject({
        message: 'Request timed out. Please try again.',
        originalError: error
      });
    }

    return Promise.reject(error);
  }
);

// Helper function to safely handle API responses
export const safelyHandleResponse = async (promise: Promise<any>) => {
  try {
    const response = await promise;
    return { data: response.data, error: null };
  } catch (error: any) {
    console.error('API error:', error);
    
    // Extract the error message from the response if available
    const errorMessage = error.response?.data?.error || error.message || 'Unknown error';
    
    return { data: null, error: errorMessage };
  }
};

// Import recruitment API
export { recruitmentAPI } from './recruitmentAPI';

// Export the api instance as the default export
export default api;