import { MigrationInterface, QueryRunner } from "typeorm";

export class NormalizeEmployeeTable1746000000014 implements MigrationInterface {
    name = 'NormalizeEmployeeTable1746000000014'

    public async up(queryRunner: QueryRunner): Promise<void> {
        try {
            // Create new tables
            
            // 1. employee_contacts table
            await queryRunner.query(`
                CREATE TABLE \`employee_contacts\` (
                    \`id\` int NOT NULL AUTO_INCREMENT,
                    \`mobileNumber\` varchar(255) NOT NULL,
                    \`officialNumber\` varchar(255) NULL,
                    \`officialEmail\` text NULL,
                    \`personalEmail\` text NULL,
                    \`permanentAddress\` text NULL,
                    \`currentAddress\` text NULL,
                    \`emergencyContactName\` text NULL,
                    \`emergencyContactPhone\` text NULL,
                    \`emergencyContactRelationship\` text NULL,
                    \`linkedinProfile\` text NULL,
                    \`otherSocialProfiles\` text NULL,
                    \`employeeId\` int NULL,
                    \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                    \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                    PRIMARY KEY (\`id\`)
                ) ENGINE=InnoDB
            `);
            
            // 2. employee_jobs table
            await queryRunner.query(`
                CREATE TABLE \`employee_jobs\` (
                    \`id\` int NOT NULL AUTO_INCREMENT,
                    \`designation\` varchar(255) NOT NULL,
                    \`department\` varchar(255) NOT NULL,
                    \`project\` text NULL,
                    \`location\` text NULL,
                    \`employmentType\` varchar(255) NULL DEFAULT 'Full-time',
                    \`employmentStatus\` varchar(255) NULL DEFAULT 'Active',
                    \`employeeLevel\` text NULL,
                    \`joinDate\` varchar(255) NOT NULL,
                    \`probationEndDate\` varchar(255) NULL,
                    \`noticePeriod\` varchar(255) NULL,
                    \`reportingTo\` text NULL,
                    \`previousEmployeeId\` varchar(255) NULL,
                    \`remoteWorkEligible\` tinyint NULL DEFAULT 0,
                    \`nextReviewDate\` varchar(255) NULL,
                    \`trainingRequirements\` text NULL,
                    \`workSchedule\` varchar(255) NULL,
                    \`shiftType\` varchar(255) NULL,
                    \`employeeId\` int NULL,
                    \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                    \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                    PRIMARY KEY (\`id\`)
                ) ENGINE=InnoDB
            `);
            
            // 3. employee_benefits table
            await queryRunner.query(`
                CREATE TABLE \`employee_benefits\` (
                    \`id\` int NOT NULL AUTO_INCREMENT,
                    \`totalSalary\` varchar(255) NULL,
                    \`salaryTier\` varchar(255) NULL,
                    \`cashAmount\` varchar(255) NULL,
                    \`bankAmount\` varchar(255) NULL,
                    \`paymentMode\` varchar(255) NULL,
                    \`foodAllowanceInSalary\` tinyint NULL DEFAULT 0,
                    \`fuelAllowanceInSalary\` tinyint NULL DEFAULT 0,
                    \`numberOfMeals\` varchar(255) NULL,
                    \`fuelInLiters\` varchar(255) NULL,
                    \`fuelAmount\` varchar(255) NULL,
                    \`foodProvidedByCompany\` tinyint NULL DEFAULT 0,
                    \`bankName\` varchar(255) NULL,
                    \`bankBranch\` varchar(255) NULL,
                    \`accountNumber\` varchar(255) NULL,
                    \`accountTitle\` varchar(255) NULL,
                    \`iban\` varchar(255) NULL,
                    \`healthInsuranceProvider\` varchar(255) NULL,
                    \`healthInsurancePolicyNumber\` varchar(255) NULL,
                    \`healthInsuranceExpiryDate\` varchar(255) NULL,
                    \`lifeInsuranceProvider\` varchar(255) NULL,
                    \`lifeInsurancePolicyNumber\` varchar(255) NULL,
                    \`lifeInsuranceExpiryDate\` varchar(255) NULL,
                    \`accommodationProvidedByEmployer\` tinyint NULL DEFAULT 0,
                    \`accommodationType\` varchar(255) NULL,
                    \`accommodationAddress\` text NULL,
                    \`employeeId\` int NULL,
                    \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                    \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                    PRIMARY KEY (\`id\`)
                ) ENGINE=InnoDB
            `);
            
            // 4. employee_education table
            await queryRunner.query(`
                CREATE TABLE \`employee_education\` (
                    \`id\` int NOT NULL AUTO_INCREMENT,
                    \`educationLevel\` varchar(255) NOT NULL,
                    \`degree\` varchar(255) NOT NULL,
                    \`major\` varchar(255) NULL,
                    \`institution\` varchar(255) NOT NULL,
                    \`graduationYear\` varchar(255) NOT NULL,
                    \`grade\` varchar(255) NULL,
                    \`employeeId\` int NULL,
                    \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                    \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                    PRIMARY KEY (\`id\`)
                ) ENGINE=InnoDB
            `);
            
            // 5. employee_experience table
            await queryRunner.query(`
                CREATE TABLE \`employee_experience\` (
                    \`id\` int NOT NULL AUTO_INCREMENT,
                    \`companyName\` varchar(255) NOT NULL,
                    \`jobTitle\` varchar(255) NOT NULL,
                    \`startDate\` varchar(255) NOT NULL,
                    \`endDate\` varchar(255) NULL,
                    \`currentlyWorking\` tinyint NOT NULL DEFAULT 0,
                    \`jobDescription\` text NULL,
                    \`employeeId\` int NULL,
                    \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                    \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                    PRIMARY KEY (\`id\`)
                ) ENGINE=InnoDB
            `);
            
            // 6. employee_family table
            await queryRunner.query(`
                CREATE TABLE \`employee_family\` (
                    \`id\` int NOT NULL AUTO_INCREMENT,
                    \`name\` varchar(255) NOT NULL,
                    \`dateOfBirth\` varchar(255) NULL,
                    \`relationship\` varchar(255) NOT NULL,
                    \`gender\` varchar(255) NULL,
                    \`cnic\` varchar(255) NULL,
                    \`occupation\` varchar(255) NULL,
                    \`employer\` varchar(255) NULL,
                    \`contactNumber\` varchar(255) NULL,
                    \`type\` varchar(255) NOT NULL,
                    \`employeeId\` int NULL,
                    \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                    \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                    PRIMARY KEY (\`id\`)
                ) ENGINE=InnoDB
            `);
            
            // 7. employee_documents table
            await queryRunner.query(`
                CREATE TABLE \`employee_documents\` (
                    \`id\` int NOT NULL AUTO_INCREMENT,
                    \`documentType\` varchar(255) NOT NULL,
                    \`documentNumber\` varchar(255) NULL,
                    \`issueDate\` varchar(255) NULL,
                    \`expiryDate\` varchar(255) NULL,
                    \`verificationStatus\` enum('pending', 'verified', 'rejected') NOT NULL DEFAULT 'pending',
                    \`filePath\` text NULL,
                    \`notes\` text NULL,
                    \`employeeId\` int NULL,
                    \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                    \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                    PRIMARY KEY (\`id\`)
                ) ENGINE=InnoDB
            `);
            
            // 8. employee_devices table
            await queryRunner.query(`
                CREATE TABLE \`employee_devices\` (
                    \`id\` int NOT NULL AUTO_INCREMENT,
                    \`deviceName\` varchar(255) NOT NULL,
                    \`makeModel\` varchar(255) NULL,
                    \`serialNumber\` varchar(255) NULL,
                    \`handoverDate\` varchar(255) NULL,
                    \`returnDate\` varchar(255) NULL,
                    \`condition\` varchar(255) NULL,
                    \`notes\` text NULL,
                    \`isVehicle\` tinyint NULL,
                    \`vehicleType\` varchar(255) NULL,
                    \`registrationNumber\` varchar(255) NULL,
                    \`providedByCompany\` tinyint NULL DEFAULT 0,
                    \`vehicleMakeModel\` text NULL,
                    \`vehicleColor\` text NULL,
                    \`mileageAtIssuance\` text NULL,
                    \`employeeId\` int NULL,
                    \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                    \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                    PRIMARY KEY (\`id\`)
                ) ENGINE=InnoDB
            `);
            
            // Add foreign key constraints
            await queryRunner.query(`
                ALTER TABLE \`employee_contacts\` 
                ADD CONSTRAINT \`FK_employee_contacts_employee\`
                FOREIGN KEY (\`employeeId\`) REFERENCES \`employees\`(\`id\`) ON DELETE CASCADE
            `);
            
            await queryRunner.query(`
                ALTER TABLE \`employee_jobs\` 
                ADD CONSTRAINT \`FK_employee_jobs_employee\`
                FOREIGN KEY (\`employeeId\`) REFERENCES \`employees\`(\`id\`) ON DELETE CASCADE
            `);
            
            await queryRunner.query(`
                ALTER TABLE \`employee_benefits\` 
                ADD CONSTRAINT \`FK_employee_benefits_employee\`
                FOREIGN KEY (\`employeeId\`) REFERENCES \`employees\`(\`id\`) ON DELETE CASCADE
            `);
            
            await queryRunner.query(`
                ALTER TABLE \`employee_education\` 
                ADD CONSTRAINT \`FK_employee_education_employee\`
                FOREIGN KEY (\`employeeId\`) REFERENCES \`employees\`(\`id\`) ON DELETE CASCADE
            `);
            
            await queryRunner.query(`
                ALTER TABLE \`employee_experience\` 
                ADD CONSTRAINT \`FK_employee_experience_employee\`
                FOREIGN KEY (\`employeeId\`) REFERENCES \`employees\`(\`id\`) ON DELETE CASCADE
            `);
            
            await queryRunner.query(`
                ALTER TABLE \`employee_family\` 
                ADD CONSTRAINT \`FK_employee_family_employee\`
                FOREIGN KEY (\`employeeId\`) REFERENCES \`employees\`(\`id\`) ON DELETE CASCADE
            `);
            
            await queryRunner.query(`
                ALTER TABLE \`employee_documents\` 
                ADD CONSTRAINT \`FK_employee_documents_employee\`
                FOREIGN KEY (\`employeeId\`) REFERENCES \`employees\`(\`id\`) ON DELETE CASCADE
            `);
            
            await queryRunner.query(`
                ALTER TABLE \`employee_devices\` 
                ADD CONSTRAINT \`FK_employee_devices_employee\`
                FOREIGN KEY (\`employeeId\`) REFERENCES \`employees\`(\`id\`) ON DELETE CASCADE
            `);
            
            // Add unique constraints
            await queryRunner.query(`
                ALTER TABLE \`employee_contacts\` 
                ADD UNIQUE INDEX \`IDX_employee_contacts_employeeId\` (\`employeeId\`)
            `);
            
            await queryRunner.query(`
                ALTER TABLE \`employee_jobs\` 
                ADD UNIQUE INDEX \`IDX_employee_jobs_employeeId\` (\`employeeId\`)
            `);
            
            await queryRunner.query(`
                ALTER TABLE \`employee_benefits\` 
                ADD UNIQUE INDEX \`IDX_employee_benefits_employeeId\` (\`employeeId\`)
            `);
            
            console.log("Successfully created normalized employee database structure");
            
        } catch (error) {
            console.error("Error in NormalizeEmployeeTable migration:", error);
            throw error;
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        try {
            // Drop tables in reverse order (respecting foreign key constraints)
            await queryRunner.query(`DROP TABLE IF EXISTS \`employee_devices\``);
            await queryRunner.query(`DROP TABLE IF EXISTS \`employee_documents\``);
            await queryRunner.query(`DROP TABLE IF EXISTS \`employee_family\``);
            await queryRunner.query(`DROP TABLE IF EXISTS \`employee_experience\``);
            await queryRunner.query(`DROP TABLE IF EXISTS \`employee_education\``);
            await queryRunner.query(`DROP TABLE IF EXISTS \`employee_benefits\``);
            await queryRunner.query(`DROP TABLE IF EXISTS \`employee_jobs\``);
            await queryRunner.query(`DROP TABLE IF EXISTS \`employee_contacts\``);
            
            console.log("Successfully reverted normalized employee database structure");
        } catch (error) {
            console.error("Error in reverting NormalizeEmployeeTable migration:", error);
            throw error;
        }
    }
} 