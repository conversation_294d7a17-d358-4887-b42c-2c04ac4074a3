import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';

export type ThemeMode = 'light' | 'dark' | 'system';
export type ThemeColor = 'blue' | 'purple' | 'green' | 'red' | 'orange';

export interface ThemeState {
  mode: ThemeMode;
  color: ThemeColor;
  fontSize: 'sm' | 'md' | 'lg';
}

export interface KeyboardShortcut {
  key: string;
  ctrl?: boolean;
  alt?: boolean;
  shift?: boolean;
  action: () => void;
  description: string;
}

interface ThemeContextType {
  theme: ThemeState;
  setTheme: React.Dispatch<React.SetStateAction<ThemeState>>;
  shortcuts: KeyboardShortcut[];
  registerShortcut: (shortcut: KeyboardShortcut) => void;
  removeShortcut: (key: string, ctrl?: boolean, alt?: boolean, shift?: boolean) => void;
  toggleThemeMode: () => void;
}

const defaultTheme: ThemeState = {
  mode: 'light',
  color: 'blue',
  fontSize: 'md',
};

export const ThemeContext = createContext<ThemeContextType>({
  theme: defaultTheme,
  setTheme: () => {},
  shortcuts: [],
  registerShortcut: () => {},
  removeShortcut: () => {},
  toggleThemeMode: () => {},
});

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Try to load theme from localStorage
  const [theme, setTheme] = useState<ThemeState>(() => {
    const savedTheme = localStorage.getItem('logviewer-theme');
    return savedTheme ? JSON.parse(savedTheme) : defaultTheme;
  });
  
  const [shortcuts, setShortcuts] = useState<KeyboardShortcut[]>([]);

  // Save theme to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('logviewer-theme', JSON.stringify(theme));
    
    // If mode is 'system', detect system preference
    if (theme.mode === 'system') {
      const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
      document.documentElement.classList.toggle('dark', isDarkMode);
    } else {
      document.documentElement.classList.toggle('dark', theme.mode === 'dark');
    }
  }, [theme]);

  // Listen for system theme changes if in 'system' mode
  useEffect(() => {
    if (theme.mode === 'system') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      const handleChange = (e: MediaQueryListEvent) => {
        document.documentElement.classList.toggle('dark', e.matches);
      };
      
      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [theme.mode]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      shortcuts.forEach(shortcut => {
        const keyMatch = e.key.toLowerCase() === shortcut.key.toLowerCase();
        const ctrlMatch = Boolean(e.ctrlKey) === Boolean(shortcut.ctrl);
        const altMatch = Boolean(e.altKey) === Boolean(shortcut.alt);
        const shiftMatch = Boolean(e.shiftKey) === Boolean(shortcut.shift);
        
        if (keyMatch && ctrlMatch && altMatch && shiftMatch) {
          e.preventDefault();
          shortcut.action();
        }
      });
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [shortcuts]);

  // Register a new keyboard shortcut
  const registerShortcut = useCallback((shortcut: KeyboardShortcut) => {
    setShortcuts(prev => {
      // Remove any existing shortcut with the same key combination
      const filtered = prev.filter(s => 
        !(s.key === shortcut.key && 
          s.ctrl === shortcut.ctrl && 
          s.alt === shortcut.alt && 
          s.shift === shortcut.shift)
      );
      return [...filtered, shortcut];
    });
  }, []);

  // Remove a keyboard shortcut
  const removeShortcut = useCallback((key: string, ctrl?: boolean, alt?: boolean, shift?: boolean) => {
    setShortcuts(prev => 
      prev.filter(s => 
        !(s.key === key && 
          s.ctrl === ctrl && 
          s.alt === alt && 
          s.shift === shift)
      )
    );
  }, []);

  const toggleThemeMode = useCallback(() => {
    setTheme(prev => ({
      ...prev,
      mode: prev.mode === 'light' ? 'dark' : 'light'
    }));
  }, []);

  const contextValue = React.useMemo(() => ({
    theme, 
    setTheme, 
    shortcuts, 
    registerShortcut, 
    removeShortcut,
    toggleThemeMode
  }), [theme, shortcuts, registerShortcut, removeShortcut, toggleThemeMode]);

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => useContext(ThemeContext);

// Helper to apply theme-based styles
export const getThemeClasses = (theme: ThemeState) => {
  const baseClasses = theme.mode === 'dark' ? 'dark' : '';
  
  const colorMap = {
    blue: {
      primary: 'blue-600',
      hover: 'blue-700',
      light: 'blue-50',
      text: 'blue-700'
    },
    purple: {
      primary: 'purple-600',
      hover: 'purple-700',
      light: 'purple-50',
      text: 'purple-700'
    },
    green: {
      primary: 'emerald-600',
      hover: 'emerald-700',
      light: 'emerald-50',
      text: 'emerald-700'
    },
    red: {
      primary: 'red-600',
      hover: 'red-700',
      light: 'red-50',
      text: 'red-700'
    },
    orange: {
      primary: 'orange-600',
      hover: 'orange-700',
      light: 'orange-50',
      text: 'orange-700'
    }
  };

  const colors = colorMap[theme.color];
  
  return {
    baseClasses,
    primaryBg: `bg-${colors.primary}`,
    primaryHover: `hover:bg-${colors.hover}`,
    lightBg: `bg-${colors.light}`,
    textColor: `text-${colors.text}`,
    borderColor: `border-${colors.primary}`,
    fontSize: `text-${theme.fontSize}`
  };
}; 