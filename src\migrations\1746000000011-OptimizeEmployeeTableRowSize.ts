import { MigrationInterface, QueryRunner } from "typeorm";

export class OptimizeEmployeeTableRowSize1746000000011 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Execute each column modification in separate transactions to ensure they complete
        try {
            // Convert some of the varchar columns to TEXT first to make space
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`permanentAddress\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`currentAddress\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`accommodationAddress\` TEXT NULL`);
            
            // Convert more columns to further reduce row size
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`professionalSkills\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`technicalSkills\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`certifications\` TEXT NULL`);
            
            // Convert additional columns to ensure enough space is freed
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`languages\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`notes\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`specialInstructions\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`otherSocialProfiles\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`professionalMemberships\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`hobbiesInterests\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`trainingRequirements\` TEXT NULL`);
            
            // Convert JSON serialized columns to LONGTEXT
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`educationEntries\` LONGTEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`experienceEntries\` LONGTEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`deviceEntries\` LONGTEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`children\` LONGTEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`dependents\` LONGTEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`documents\` LONGTEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`requiredDocuments\` LONGTEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`projectEntries\` LONGTEXT NULL`);
            
            // Now add the new column after freeing up space
            await queryRunner.query(`ALTER TABLE \`employees\` ADD \`mileageAtIssuance\` varchar(255) NULL`);
            
            console.log('Successfully optimized employee table row size and added mileageAtIssuance column');
        } catch (error) {
            console.error('Error in OptimizeEmployeeTableRowSize migration:', error);
            throw error;
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        try {
            // Remove the added column first
            await queryRunner.query(`ALTER TABLE \`employees\` DROP COLUMN \`mileageAtIssuance\``);
            
            // Convert TEXT columns back to varchar
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`permanentAddress\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`currentAddress\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`accommodationAddress\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`professionalSkills\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`technicalSkills\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`certifications\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`languages\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`notes\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`specialInstructions\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`otherSocialProfiles\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`professionalMemberships\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`hobbiesInterests\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`trainingRequirements\` varchar(255) NULL`);
            
            // Convert LONGTEXT columns back to their original type
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`educationEntries\` longtext NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`experienceEntries\` longtext NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`deviceEntries\` longtext NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`children\` longtext NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`dependents\` longtext NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`documents\` longtext NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`requiredDocuments\` longtext NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`projectEntries\` longtext NULL`);
            
            console.log('Successfully reverted optimization changes');
        } catch (error) {
            console.error('Error in reverting OptimizeEmployeeTableRowSize migration:', error);
            throw error;
        }
    }
} 