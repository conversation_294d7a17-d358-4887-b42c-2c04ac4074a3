import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { IsNotEmpty, IsOptional, IsNumber, Min, <PERSON>, Length, IsEnum } from 'class-validator';
import { Interview } from './Interview';
import { User } from './User';

export enum FeedbackDecision {
  STRONG_HIRE = 'strong_hire',
  HIRE = 'hire',
  MAYBE = 'maybe',
  NO_HIRE = 'no_hire',
  STRONG_NO_HIRE = 'strong_no_hire'
}

@Entity('interview_feedback')
export class InterviewFeedback {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int', default: 0 })
  @IsNumber({}, { message: 'Overall rating must be a number' })
  @Min(1, { message: 'Rating must be at least 1' })
  @Max(5, { message: 'Rating must be at most 5' })
  overallRating: number;

  @Column({
    type: 'enum',
    enum: FeedbackDecision,
    default: FeedbackDecision.MAYBE
  })
  @IsEnum(FeedbackDecision, { message: 'Invalid feedback decision' })
  decision: FeedbackDecision;

  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Technical skills rating must be a number' })
  @Min(1, { message: 'Rating must be at least 1' })
  @Max(5, { message: 'Rating must be at most 5' })
  technicalSkillsRating: number;

  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Communication rating must be a number' })
  @Min(1, { message: 'Rating must be at least 1' })
  @Max(5, { message: 'Rating must be at most 5' })
  communicationRating: number;

  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Problem solving rating must be a number' })
  @Min(1, { message: 'Rating must be at least 1' })
  @Max(5, { message: 'Rating must be at most 5' })
  problemSolvingRating: number;

  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Cultural fit rating must be a number' })
  @Min(1, { message: 'Rating must be at least 1' })
  @Max(5, { message: 'Rating must be at most 5' })
  culturalFitRating: number;

  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Leadership rating must be a number' })
  @Min(1, { message: 'Rating must be at least 1' })
  @Max(5, { message: 'Rating must be at most 5' })
  leadershipRating: number;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @Length(0, 2000, { message: 'Strengths must be less than 2000 characters' })
  strengths: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @Length(0, 2000, { message: 'Areas for improvement must be less than 2000 characters' })
  areasForImprovement: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @Length(0, 3000, { message: 'Detailed feedback must be less than 3000 characters' })
  detailedFeedback: string;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  questionResponses: Array<{
    questionId: string;
    question: string;
    response: string;
    rating?: number;
    notes?: string;
  }>;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  skillsAssessment: Array<{
    skill: string;
    rating: number;
    notes?: string;
  }>;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @Length(0, 1000, { message: 'Next steps must be less than 1000 characters' })
  nextSteps: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  @Length(0, 1000, { message: 'Concerns must be less than 1000 characters' })
  concerns: string;

  @Column({ type: 'boolean', default: false })
  wouldWorkWithAgain: boolean;

  @Column({ type: 'boolean', default: false })
  recommendForNextRound: boolean;

  @Column({ type: 'int', nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Interview duration must be a number' })
  actualDurationMinutes: number;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  internalNotes: string;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  customFields: Record<string, any>;

  // Relations
  @ManyToOne(() => Interview, interview => interview.feedback, { nullable: false })
  @JoinColumn({ name: 'interviewId' })
  interview: Interview;

  @Column({ type: 'int' })
  interviewId: number;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'interviewerId' })
  interviewer: User;

  @Column({ type: 'uuid' })
  interviewerId: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Virtual properties
  get averageRating(): number {
    const ratings = [
      this.technicalSkillsRating,
      this.communicationRating,
      this.problemSolvingRating,
      this.culturalFitRating,
      this.leadershipRating
    ].filter(rating => rating && rating > 0);

    if (ratings.length === 0) return this.overallRating;
    
    return ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length;
  }

  get decisionDisplayName(): string {
    return this.decision.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  get isPositiveFeedback(): boolean {
    return [FeedbackDecision.STRONG_HIRE, FeedbackDecision.HIRE].includes(this.decision);
  }

  get isNegativeFeedback(): boolean {
    return [FeedbackDecision.NO_HIRE, FeedbackDecision.STRONG_NO_HIRE].includes(this.decision);
  }

  get feedbackSummary(): string {
    if (this.decision === FeedbackDecision.STRONG_HIRE) return 'Strongly recommended for hire';
    if (this.decision === FeedbackDecision.HIRE) return 'Recommended for hire';
    if (this.decision === FeedbackDecision.MAYBE) return 'Mixed feedback, needs discussion';
    if (this.decision === FeedbackDecision.NO_HIRE) return 'Not recommended for hire';
    if (this.decision === FeedbackDecision.STRONG_NO_HIRE) return 'Strongly not recommended';
    return 'No decision recorded';
  }

  get hasDetailedRatings(): boolean {
    return !!(this.technicalSkillsRating || this.communicationRating || 
              this.problemSolvingRating || this.culturalFitRating || this.leadershipRating);
  }
}
