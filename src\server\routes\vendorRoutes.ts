import { Router } from 'express';
import {
  getVendors,
  getVendorById,
  createVendor,
  updateVendor,
  deleteVendor
} from '../controllers/vendorController';

const router = Router();

// GET /api/vendors - Get all vendors with optional filtering
router.get('/', getVendors);

// GET /api/vendors/:id - Get a single vendor by ID
router.get('/:id', getVendorById);

// POST /api/vendors - Create a new vendor
router.post('/', createVendor);

// PUT /api/vendors/:id - Update an existing vendor
router.put('/:id', updateVendor);

// DELETE /api/vendors/:id - Delete a vendor
router.delete('/:id', deleteVendor);

export default router; 