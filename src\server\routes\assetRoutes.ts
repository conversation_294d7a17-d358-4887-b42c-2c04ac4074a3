import express from 'express';
import { assetController } from '../controllers/assetController';
import { authMiddleware } from '../middleware/authMiddleware';
import logger from '../../utils/logger';

const router = express.Router();

// Error handling wrapper for async route handlers
const asyncHandler = (fn: Function) => (req: express.Request, res: express.Response, next: express.NextFunction) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Get all assets
router.get('/', authMiddleware.verify, asyncHandler(assetController.getAssets));

// Create a new asset
router.post('/', 
  authMiddleware.verify,
  asyncHandler(assetController.createAsset)
);

// Get asset by ID
router.get('/:id', authMiddleware.verify, asyncHandler(assetController.getAssetById));

// Get asset statistics
router.get('/stats', authMiddleware.verify, asyncHandler(assetController.getAssetStats));

export default router; 