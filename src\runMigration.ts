import { AppDataSource } from './config/database';
import { AddProjectFieldToAssets } from './migrations/AddProjectFieldToAssets';
import { AddProjectLocationToEmailAccounts } from './migrations/AddProjectLocationToEmailAccounts';

async function runMigration() {
    try {
        // Initialize the data source
        await AppDataSource.initialize();
        console.log('Database connection initialized');

        // Run the migrations
        console.log('Running AddProjectFieldToAssets migration...');
        const assetsMigration = new AddProjectFieldToAssets();
        await assetsMigration.up(AppDataSource.createQueryRunner());
        
        console.log('Running AddProjectLocationToEmailAccounts migration...');
        const emailMigration = new AddProjectLocationToEmailAccounts();
        await emailMigration.up(AppDataSource.createQueryRunner());
        
        console.log('All migrations completed successfully');

        // Close the connection
        await AppDataSource.destroy();
        console.log('Database connection closed');
    } catch (error) {
        console.error('Error running migration:', error);
        process.exit(1);
    }
}

// Run the migration
runMigration()
    .then(() => {
        console.log('Migration script completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Unhandled error in migration script:', error);
        process.exit(1);
    }); 