import { AppDataSource } from '../config/database';
import * as fs from 'fs';
import * as path from 'path';

async function runStatusDateMigration() {
  console.log('Initializing data source...');
  
  try {
    await AppDataSource.initialize();
    console.log('Data source initialized');
    
    console.log('Executing SQL migration to add status_date column...');
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    
    // Start transaction
    await queryRunner.startTransaction();
    
    try {
      // First check if the column exists
      console.log('Checking if status_date column exists...');
      const columns = await queryRunner.query(`
        SELECT COLUMN_NAME 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = database() 
        AND TABLE_NAME = 'employees' 
        AND COLUMN_NAME = 'status_date'
      `);
      
      // Add the column only if it doesn't exist
      if (columns.length === 0) {
        console.log('status_date column does not exist, adding it...');
        await queryRunner.query(`
          ALTER TABLE employees
          ADD COLUMN status_date DATE NULL;
        `);
        console.log('status_date column added successfully');
      } else {
        console.log('status_date column already exists');
      }
      
      // Commit transaction
      await queryRunner.commitTransaction();
      console.log('Database migration completed successfully!');
    } catch (error) {
      // Rollback transaction in case of error
      await queryRunner.rollbackTransaction();
      console.error('Database migration failed, transaction rolled back:', error);
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }
    
    await AppDataSource.destroy();
    console.log('Connection closed');
    
    process.exit(0);
  } catch (error) {
    console.error('Error during database migration:', error);
    process.exit(1);
  }
}

runStatusDateMigration(); 