class LocationService {
  private baseUrl = '/api';

  // Get all unique locations from employees
  async getAllLocations(): Promise<string[]> {
    try {
      const response = await fetch(`${this.baseUrl}/employees/locations`);
      if (!response.ok) {
        throw new Error('Failed to fetch locations');
      }
      const data = await response.json();
      return data.locations || [];
    } catch (error) {
      console.error('Error fetching locations:', error);
      // Fallback to default locations if API fails
      return this.getDefaultLocations();
    }
  }

  // Get locations for a specific employee
  async getEmployeeLocations(employeeId: number): Promise<string[]> {
    try {
      const response = await fetch(`${this.baseUrl}/employees/${employeeId}/locations`);
      if (!response.ok) {
        throw new Error('Failed to fetch employee locations');
      }
      const data = await response.json();
      return data.locations || [];
    } catch (error) {
      console.error('Error fetching employee locations:', error);
      return [];
    }
  }

  // Get locations by project
  async getLocationsByProject(project: string): Promise<string[]> {
    try {
      const response = await fetch(`${this.baseUrl}/locations/by-project?project=${encodeURIComponent(project)}`);
      if (!response.ok) {
        throw new Error('Failed to fetch project locations');
      }
      const data = await response.json();
      return data.locations || [];
    } catch (error) {
      console.error('Error fetching project locations:', error);
      return this.getProjectLocations(project);
    }
  }

  // Get default locations as fallback
  private getDefaultLocations(): string[] {
    return [
      'Grand City Head Office Lahore',
      'Grand City Site Office Kharian',
      'Grand City Site Office Arifwala',
      'Grand City Site Office Vehari',
      'Grand City Site Office Faisalabad',
      'Grand City Site Office Murree',
      'ARD Head Office Lahore',
      'ARD Site Office RUDA',
      'Remote Work',
      'Office'
    ];
  }

  // Project-specific locations (fallback for getLocationsByProject)
  private getProjectLocations(project: string): string[] {
    const PROJECT_LOCATIONS: Record<string, string[]> = {
      'Eurobiz Corporations': [
        'Grand City Head Office Lahore',
        'Grand City Site Office Kharian'
      ],
      'Guardian International': [
        'Grand City Head Office Lahore',
        'Grand City Site Office Arifwala'
      ],
      'Guardian Developers': [
        'Grand City Head Office Lahore',
        'Grand City Site Office Vehari'
      ],
      'Grand Developers': [
        'Grand City Head Office Lahore',
        'Grand City Site Office Faisalabad',
        'Grand City Site Office Murree'
      ],
      'ARD Developers': [
        'ARD Head Office Lahore',
        'ARD Site Office RUDA'
      ]
    };

    return PROJECT_LOCATIONS[project] || this.getDefaultLocations();
  }

  // Extract unique locations from employees array
  extractLocationsFromEmployees(employees: any[]): string[] {
    const locations = new Set<string>();
    
    employees.forEach(employee => {
      // Add employee location if exists
      if (employee.location && employee.location.trim()) {
        locations.add(employee.location.trim());
      }
      
      // Add locations based on project
      if (employee.project) {
        const projectLocations = this.getProjectLocations(employee.project);
        projectLocations.forEach(loc => locations.add(loc));
      }
    });

    // Add default locations
    this.getDefaultLocations().forEach(loc => locations.add(loc));

    return Array.from(locations).sort();
  }

  // Get locations from attendance records
  extractLocationsFromAttendance(attendances: any[]): string[] {
    const locations = new Set<string>();
    
    attendances.forEach(attendance => {
      if (attendance.location && attendance.location.trim()) {
        locations.add(attendance.location.trim());
      }
    });

    return Array.from(locations).sort();
  }

  // Combine all location sources
  async getCombinedLocations(employees: any[] = [], attendances: any[] = []): Promise<string[]> {
    const locationSets = await Promise.allSettled([
      this.getAllLocations(),
      Promise.resolve(this.extractLocationsFromEmployees(employees)),
      Promise.resolve(this.extractLocationsFromAttendance(attendances))
    ]);

    const allLocations = new Set<string>();

    locationSets.forEach(result => {
      if (result.status === 'fulfilled') {
        result.value.forEach((location: string) => allLocations.add(location));
      }
    });

    // Ensure we always have some default locations
    if (allLocations.size === 0) {
      this.getDefaultLocations().forEach(loc => allLocations.add(loc));
    }

    return Array.from(allLocations).sort();
  }
}

export default new LocationService(); 