import React, { useState, useEffect } from 'react';
import { 
  DollarSign, 
  Calendar, 
  Filter, 
  Search,
  Plus,
  FileText,
  Edit,
  Trash2,
  Check,
  X,
  AlertCircle,
  Download,
  Users,
  CreditCard
} from 'lucide-react';
import { 
  Loan, 
  LoanStatus, 
  LoanType, 
  SalaryAdvance, 
  AdvanceStatus 
} from '../../../types/payroll';
import LoansTable from './LoansTable';
import AdvancesTable from './AdvancesTable';

interface LoanAdvanceManagementProps {
  isAdmin?: boolean;
}

const LoanAdvanceManagement: React.FC<LoanAdvanceManagementProps> = ({ isAdmin = false }) => {
  const [activeTab, setActiveTab] = useState('loans');
  const [loading, setLoading] = useState(true);
  const [loans, setLoans] = useState<Loan[]>([]);
  const [advances, setAdvances] = useState<SalaryAdvance[]>([]);
  const [showLoanModal, setShowLoanModal] = useState(false);
  const [showAdvanceModal, setShowAdvanceModal] = useState(false);
  const [selectedLoan, setSelectedLoan] = useState<Loan | null>(null);
  const [selectedAdvance, setSelectedAdvance] = useState<SalaryAdvance | null>(null);
  
  // Load mock data
  useEffect(() => {
    setLoading(true);
    // Simulate API call to fetch loans and advances
    setTimeout(() => {
      const mockLoans: Loan[] = [
        {
          id: 1,
          employeeId: 101,
          employeeName: "Ahmed Khan",
          loanType: LoanType.PERSONAL,
          amount: 100000,
          interestRate: 5,
          termMonths: 12,
          startDate: "2023-03-15",
          endDate: "2024-03-15",
          installmentAmount: 8600,
          remainingAmount: 34400,
          totalPaid: 65600,
          totalInterestPaid: 3200,
          status: LoanStatus.ACTIVE,
          purpose: "Personal expenses",
          approvedBy: 1,
          approvedDate: "2023-03-10",
          createdAt: "2023-03-05"
        },
        {
          id: 2,
          employeeId: 102,
          employeeName: "Sara Ahmed",
          loanType: LoanType.EDUCATION,
          amount: 250000,
          interestRate: 3,
          termMonths: 24,
          startDate: "2023-01-01",
          endDate: "2025-01-01",
          installmentAmount: 10750,
          remainingAmount: 182750,
          totalPaid: 67250,
          totalInterestPaid: 2500,
          status: LoanStatus.ACTIVE,
          purpose: "Children's education fees",
          approvedBy: 1,
          approvedDate: "2022-12-20",
          createdAt: "2022-12-10"
        },
        {
          id: 3,
          employeeId: 103,
          employeeName: "Bilal Malik",
          loanType: LoanType.MEDICAL,
          amount: 80000,
          interestRate: 2,
          termMonths: 6,
          startDate: "2023-05-01",
          endDate: "2023-11-01",
          installmentAmount: 13600,
          remainingAmount: 27200,
          totalPaid: 52800,
          totalInterestPaid: 800,
          status: LoanStatus.ACTIVE,
          purpose: "Medical treatment",
          approvedBy: 1,
          approvedDate: "2023-04-28",
          createdAt: "2023-04-25"
        },
        {
          id: 4,
          employeeId: 104,
          employeeName: "Aisha Kamran",
          loanType: LoanType.VEHICLE,
          amount: 500000,
          interestRate: 6,
          termMonths: 36,
          startDate: "2022-07-01",
          endDate: "2025-07-01",
          installmentAmount: 15200,
          remainingAmount: 349600,
          totalPaid: 150400,
          totalInterestPaid: 25000,
          status: LoanStatus.ACTIVE,
          purpose: "Car purchase",
          approvedBy: 1,
          approvedDate: "2022-06-25",
          createdAt: "2022-06-20"
        },
        {
          id: 5,
          employeeId: 105,
          employeeName: "Faizan Ali",
          loanType: LoanType.HOUSING,
          amount: 1000000,
          interestRate: 7,
          termMonths: 60,
          startDate: "2022-01-01",
          endDate: "2027-01-01",
          installmentAmount: 19800,
          remainingAmount: 712800,
          totalPaid: 287200,
          totalInterestPaid: 75000,
          status: LoanStatus.ACTIVE,
          purpose: "Home renovation",
          approvedBy: 1,
          approvedDate: "2021-12-20",
          createdAt: "2021-12-10"
        },
        {
          id: 6,
          employeeId: 106,
          employeeName: "Zara Hashmi",
          loanType: LoanType.EMERGENCY,
          amount: 50000,
          interestRate: 0,
          termMonths: 5,
          startDate: "2023-06-15",
          endDate: "2023-11-15",
          installmentAmount: 10000,
          remainingAmount: 30000,
          totalPaid: 20000,
          totalInterestPaid: 0,
          status: LoanStatus.ACTIVE,
          purpose: "Family emergency",
          approvedBy: 1,
          approvedDate: "2023-06-10",
          createdAt: "2023-06-09"
        },
        {
          id: 7,
          employeeId: 107,
          employeeName: "Imran Khan",
          loanType: LoanType.PERSONAL,
          amount: 75000,
          interestRate: 5,
          termMonths: 12,
          startDate: "2023-08-01",
          endDate: "2024-08-01",
          installmentAmount: 6500,
          remainingAmount: 75000,
          totalPaid: 0,
          totalInterestPaid: 0,
          status: LoanStatus.PENDING,
          purpose: "Personal expenses",
          createdAt: "2023-07-25"
        }
      ];
      
      const mockAdvances: SalaryAdvance[] = [
        {
          id: 1,
          employeeId: 101,
          employeeName: "Ahmed Khan",
          amount: 25000,
          requestDate: "2023-07-15",
          approvalDate: "2023-07-17",
          paymentDate: "2023-07-18",
          recoveryDate: "2023-08-01",
          recoveryMethod: "one_time",
          recoveredAmount: 25000,
          remainingAmount: 0,
          status: AdvanceStatus.RECOVERED,
          purpose: "Emergency expense",
          approvedBy: 1,
          createdAt: "2023-07-15"
        },
        {
          id: 2,
          employeeId: 102,
          employeeName: "Sara Ahmed",
          amount: 50000,
          requestDate: "2023-07-20",
          approvalDate: "2023-07-22",
          paymentDate: "2023-07-23",
          recoveryMethod: "installments",
          recoveryMonths: 2,
          installmentAmount: 25000,
          recoveredAmount: 25000,
          remainingAmount: 25000,
          status: AdvanceStatus.PAID,
          purpose: "Family medical emergency",
          approvedBy: 1,
          createdAt: "2023-07-20"
        },
        {
          id: 3,
          employeeId: 103,
          employeeName: "Bilal Malik",
          amount: 15000,
          requestDate: "2023-07-25",
          recoveryMethod: "one_time",
          recoveredAmount: 0,
          remainingAmount: 15000,
          status: AdvanceStatus.PENDING,
          purpose: "Urgent bill payment",
          createdAt: "2023-07-25"
        },
        {
          id: 4,
          employeeId: 104,
          employeeName: "Aisha Kamran",
          amount: 35000,
          requestDate: "2023-07-10",
          approvalDate: "2023-07-12",
          recoveryMethod: "installments",
          recoveryMonths: 3,
          installmentAmount: 11667,
          recoveredAmount: 0,
          remainingAmount: 35000,
          status: AdvanceStatus.APPROVED,
          purpose: "Home repair",
          approvedBy: 1,
          createdAt: "2023-07-10"
        },
        {
          id: 5,
          employeeId: 106,
          employeeName: "Zara Hashmi",
          amount: 20000,
          requestDate: "2023-07-05",
          approvalDate: "2023-07-07",
          paymentDate: "2023-07-08",
          recoveryDate: "2023-08-01",
          recoveryMethod: "one_time",
          recoveredAmount: 20000,
          remainingAmount: 0,
          status: AdvanceStatus.RECOVERED,
          purpose: "Child's school fee",
          approvedBy: 1,
          createdAt: "2023-07-05"
        }
      ];
      
      setLoans(mockLoans);
      setAdvances(mockAdvances);
      setLoading(false);
    }, 1000);
  }, []);
  
  const handleViewLoan = (loan: Loan) => {
    setSelectedLoan(loan);
    // In a full implementation, you'd show a detailed view
    alert(`Viewing details for loan ${loan.id} - ${loan.employeeName}`);
  };
  
  const handleEditLoan = (loan: Loan) => {
    setSelectedLoan(loan);
    setShowLoanModal(true);
  };
  
  const handleDeleteLoan = (loanId: number) => {
    if (window.confirm("Are you sure you want to delete this loan?")) {
      // In a real application, you would call an API to delete
      setLoans(loans.filter(loan => loan.id !== loanId));
    }
  };
  
  const handleViewAdvance = (advance: SalaryAdvance) => {
    setSelectedAdvance(advance);
    // In a full implementation, you'd show a detailed view
    alert(`Viewing details for advance ${advance.id} - ${advance.employeeName}`);
  };
  
  const handleEditAdvance = (advance: SalaryAdvance) => {
    setSelectedAdvance(advance);
    setShowAdvanceModal(true);
  };
  
  const handleDeleteAdvance = (advanceId: number) => {
    if (window.confirm("Are you sure you want to delete this advance?")) {
      // In a real application, you would call an API to delete
      setAdvances(advances.filter(advance => advance.id !== advanceId));
    }
  };
  
  const handleCreateLoan = () => {
    setSelectedLoan(null);
    setShowLoanModal(true);
  };
  
  const handleCreateAdvance = () => {
    setSelectedAdvance(null);
    setShowAdvanceModal(true);
  };
  
  return (
    <div className="p-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Loan & Advance Management</h3>
          <p className="text-sm text-gray-500">Manage employee loans and salary advances</p>
        </div>
        
        {/* Action Buttons */}
        <div className="flex space-x-3">
          {isAdmin && activeTab === 'loans' && (
            <button
              onClick={handleCreateLoan}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Plus className="h-4 w-4 mr-2" />
              New Loan
            </button>
          )}
          
          {isAdmin && activeTab === 'advances' && (
            <button
              onClick={handleCreateAdvance}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <Plus className="h-4 w-4 mr-2" />
              New Advance
            </button>
          )}
          
          <button
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
        </div>
      </div>
      
      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="flex -mb-px space-x-8">
          <button
            onClick={() => setActiveTab('loans')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'loans'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <CreditCard className="inline-block mr-2 h-4 w-4" />
            Loans
          </button>
          <button
            onClick={() => setActiveTab('advances')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'advances'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <DollarSign className="inline-block mr-2 h-4 w-4" />
            Salary Advances
          </button>
        </nav>
      </div>
      
      {/* Search and Filters */}
      <div className="mb-6 p-4 bg-gray-50 rounded-md border border-gray-200">
        <div className="flex flex-wrap gap-4 items-end">
          <div className="w-full sm:w-auto">
            <label htmlFor="search" className="block text-xs font-medium text-gray-700 mb-1">Search</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                id="search"
                name="search"
                placeholder={activeTab === 'loans' ? "Search loans" : "Search advances"}
                className="pl-10 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
              />
            </div>
          </div>
          
          <div className="w-full sm:w-auto">
            <label htmlFor="status" className="block text-xs font-medium text-gray-700 mb-1">Status</label>
            <select
              id="status"
              name="status"
              className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
            >
              <option value="">All Statuses</option>
              {activeTab === 'loans' ? (
                <>
                  <option value={LoanStatus.PENDING}>Pending</option>
                  <option value={LoanStatus.APPROVED}>Approved</option>
                  <option value={LoanStatus.ACTIVE}>Active</option>
                  <option value={LoanStatus.COMPLETED}>Completed</option>
                  <option value={LoanStatus.REJECTED}>Rejected</option>
                  <option value={LoanStatus.DEFAULTED}>Defaulted</option>
                </>
              ) : (
                <>
                  <option value={AdvanceStatus.PENDING}>Pending</option>
                  <option value={AdvanceStatus.APPROVED}>Approved</option>
                  <option value={AdvanceStatus.PAID}>Paid</option>
                  <option value={AdvanceStatus.RECOVERED}>Recovered</option>
                  <option value={AdvanceStatus.REJECTED}>Rejected</option>
                </>
              )}
            </select>
          </div>
          
          {activeTab === 'loans' && (
            <div className="w-full sm:w-auto">
              <label htmlFor="loanType" className="block text-xs font-medium text-gray-700 mb-1">Loan Type</label>
              <select
                id="loanType"
                name="loanType"
                className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
              >
                <option value="">All Types</option>
                <option value={LoanType.PERSONAL}>Personal</option>
                <option value={LoanType.EDUCATION}>Education</option>
                <option value={LoanType.HOUSING}>Housing</option>
                <option value={LoanType.MEDICAL}>Medical</option>
                <option value={LoanType.VEHICLE}>Vehicle</option>
                <option value={LoanType.EMERGENCY}>Emergency</option>
                <option value={LoanType.OTHER}>Other</option>
              </select>
            </div>
          )}
          
          <div className="w-full sm:w-auto">
            <button 
              className="px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Clear Filters
            </button>
          </div>
        </div>
      </div>
      
      {/* Content based on active tab */}
      <div>
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <>
            {activeTab === 'loans' ? (
              <LoansTable 
                loans={loans}
                isAdmin={isAdmin}
                onEditLoan={handleEditLoan}
                onDeleteLoan={handleDeleteLoan}
                onViewLoan={handleViewLoan}
              />
            ) : (
              <AdvancesTable 
                advances={advances}
                isAdmin={isAdmin}
                onEditAdvance={handleEditAdvance}
                onDeleteAdvance={handleDeleteAdvance}
                onViewAdvance={handleViewAdvance}
              />
            )}
          </>
        )}
      </div>
      
      {/* Modal placeholders - in a full implementation, these would be proper forms */}
      {showLoanModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-lg w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                {selectedLoan ? 'Edit Loan' : 'New Loan'}
              </h3>
              <button
                onClick={() => setShowLoanModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            <div className="space-y-4">
              <p className="text-gray-500">Form fields for loan details would go here...</p>
            </div>
            <div className="mt-5 flex justify-end">
              <button
                type="button"
                onClick={() => setShowLoanModal(false)}
                className="mr-3 inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={() => setShowLoanModal(false)}
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
      
      {showAdvanceModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-lg w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                {selectedAdvance ? 'Edit Advance' : 'New Advance'}
              </h3>
              <button
                onClick={() => setShowAdvanceModal(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            <div className="space-y-4">
              <p className="text-gray-500">Form fields for advance details would go here...</p>
            </div>
            <div className="mt-5 flex justify-end">
              <button
                type="button"
                onClick={() => setShowAdvanceModal(false)}
                className="mr-3 inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={() => setShowAdvanceModal(false)}
                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LoanAdvanceManagement; 