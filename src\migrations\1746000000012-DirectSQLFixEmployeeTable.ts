import { MigrationInterface, QueryRunner } from "typeorm";

export class DirectSQLFixEmployeeTable1746000000012 implements MigrationInterface {
    name = 'DirectSQLFixEmployeeTable1746000000012'

    public async up(queryRunner: QueryRunner): Promise<void> {
        try {
            // First, check if mileageAtIssuance column exists
            const tableInfo = await queryRunner.query(
                `SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
                 WHERE TABLE_SCHEMA = 'ims_db' AND TABLE_NAME = 'employees' 
                 AND COLUMN_NAME = 'mileageAtIssuance'`
            );

            if (tableInfo.length === 0) {
                // If not, convert some columns to TEXT first to free up space
                await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`officialEmail\` TEXT NULL`);
                await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`personalEmail\` TEXT NULL`);
                await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`vehicleMakeModel\` TEXT NULL`);
                await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`vehicleColor\` TEXT NULL`);
                
                // Now add the mileageAtIssuance as TEXT
                await queryRunner.query(`ALTER TABLE \`employees\` ADD \`mileageAtIssuance\` TEXT NULL`);
                
                console.log("Successfully added mileageAtIssuance column and optimized table structure");
            } else {
                console.log("mileageAtIssuance column already exists, skipping modification");
                
                // Just ensure it's TEXT type
                await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`mileageAtIssuance\` TEXT NULL`);
            }
        } catch (error) {
            console.error("Error in DirectSQLFixEmployeeTable migration:", error);
            throw error;
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        try {
            // Don't remove the column, just convert back to varchar if needed for consistency
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`mileageAtIssuance\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`officialEmail\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`personalEmail\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`vehicleMakeModel\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`vehicleColor\` varchar(255) NULL`);
            
            console.log("Successfully reverted column type changes");
        } catch (error) {
            console.error("Error in reverting DirectSQLFixEmployeeTable migration:", error);
            throw error;
        }
    }
} 