import express from 'express';
import {
  getAttendances,
  getEmployeeAttendance,
  createAttendance,
  updateAttendance,
  deleteAttendance,
  importAttendances,
  getAttendanceSummary,
  clearImportedAttendances,
  clearAllAttendances,
  clockInOut,
  cleanupDuplicateAttendances
} from '../controllers/attendanceController';
import {
  getRegularizationRequests,
  getEmployeeRegularizationRequests,
  getRegularizationRequestById,
  createRegularizationRequest,
  approveRegularizationRequest,
  rejectRegularizationRequest,
  updateRegularizationRequest,
  deleteRegularizationRequest,
  getRegularizationStatistics
} from '../controllers/attendanceRegularizationController';
import { authMiddleware } from '../middleware/authMiddleware';

const router = express.Router();

// Apply authentication middleware to all routes
// Temporarily disabled for testing
// router.use(authMiddleware.verify);

// ===== ATTENDANCE ROUTES =====

// Get all attendance records with optional filtering
router.get('/', getAttendances);

// Get attendance summary
router.get('/summary', getAttendanceSummary);

// Get attendance for a specific employee
router.get('/employee/:employeeId', getEmployeeAttendance);

// Clock in/out endpoint - handles updating same record for same date
router.post('/clock', clockInOut);

// Create a new attendance record
router.post('/', createAttendance);

// Import multiple attendance records
router.post('/import', importAttendances);

// Clear imported attendance data
router.delete('/clear-imported', clearImportedAttendances);

// Clear all attendance data
router.delete('/clear-all', clearAllAttendances);

// Update an attendance record
router.put('/:id', updateAttendance);

// Delete an attendance record
router.delete('/:id', deleteAttendance);

// Cleanup duplicate attendance records
router.post('/cleanup-duplicates', cleanupDuplicateAttendances);

// ===== REGULARIZATION ROUTES =====

// Get all regularization requests with filtering
router.get('/regularization', getRegularizationRequests);

// Get regularization statistics
router.get('/regularization/stats', getRegularizationStatistics);

// Get regularization requests for a specific employee
router.get('/regularization/employee/:employeeId', getEmployeeRegularizationRequests);

// Get a specific regularization request
router.get('/regularization/:id', getRegularizationRequestById);

// Create a new regularization request
router.post('/regularization', createRegularizationRequest);

// Approve a regularization request
router.patch('/regularization/:id/approve', approveRegularizationRequest);

// Reject a regularization request
router.patch('/regularization/:id/reject', rejectRegularizationRequest);

// Update a regularization request
router.put('/regularization/:id', updateRegularizationRequest);

// Delete a regularization request
router.delete('/regularization/:id', deleteRegularizationRequest);

export default router; 