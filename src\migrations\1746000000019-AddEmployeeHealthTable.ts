import { MigrationInterface, QueryRunner } from "typeorm";

export class AddEmployeeHealthTable1746000000019 implements MigrationInterface {
    name = 'AddEmployeeHealthTable1746000000019';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create the employee_health table
        await queryRunner.query(`
            CREATE TABLE \`employee_health\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`vaccinationRecords\` text NULL,
                \`medicalHistory\` text NULL,
                \`bloodGroup\` varchar(255) NULL,
                \`allergies\` text NULL,
                \`chronicConditions\` text NULL,
                \`regularMedications\` text NULL,
                \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                \`employeeId\` int NULL,
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB
        `);

        // Add foreign key constraint
        await queryRunner.query(`
            ALTER TABLE \`employee_health\` 
            ADD CONSTRAINT \`FK_employee_health_employee\` 
            FOREIGN KEY (\`employeeId\`) REFERENCES \`employees\`(\`id\`) 
            ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the foreign key constraint
        await queryRunner.query(`
            ALTER TABLE \`employee_health\` 
            DROP FOREIGN KEY \`FK_employee_health_employee\`
        `);
        
        // Drop the table
        await queryRunner.query(`DROP TABLE \`employee_health\``);
    }
} 