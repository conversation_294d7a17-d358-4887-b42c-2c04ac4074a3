import { MigrationInterface, QueryRunner } from "typeorm";

export class AddProjectLocationToEmailAccounts1745488600000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if the columns already exist
        const table = await queryRunner.getTable("email_accounts");
        const projectColumn = table?.findColumnByName("project");
        const locationColumn = table?.findColumnByName("location");
        
        if (!projectColumn) {
            await queryRunner.query(`ALTER TABLE email_accounts ADD COLUMN project VARCHAR(100) NULL`);
            console.log('Added project column to email_accounts table');
        } else {
            console.log('Project column already exists in email_accounts table');
        }

        if (!locationColumn) {
            await queryRunner.query(`ALTER TABLE email_accounts ADD COLUMN location VARCHAR(100) NULL`);
            console.log('Added location column to email_accounts table');
        } else {
            console.log('Location column already exists in email_accounts table');
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE email_accounts DROP COLUMN IF EXISTS project`);
        await queryRunner.query(`ALTER TABLE email_accounts DROP COLUMN IF EXISTS location`);
        console.log('Removed project and location columns from email_accounts table');
    }
} 