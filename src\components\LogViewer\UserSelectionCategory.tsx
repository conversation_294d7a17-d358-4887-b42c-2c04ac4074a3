import React, { useMemo, useState } from 'react';
import { User, Users, Search, X, ChevronDown } from 'lucide-react';
import { SystemLog } from './types';

interface UserSelectionCategoryProps {
  logs: SystemLog[];
  onUserSelect: (username: string) => void;
  selectedUser: string | null;
}

export const UserSelectionCategory: React.FC<UserSelectionCategoryProps> = ({
  logs,
  onUserSelect,
  selectedUser
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const [categoryFilter, setCategoryFilter] = useState<string>('all');

  // Extract unique users and their categories from logs
  const userCategories = useMemo(() => {
    const users = new Map<string, {
      count: number;
      lastActivity: Date;
      category: string;
    }>();

    logs.forEach(log => {
      const username = log.user;
      const timestamp = new Date(log.timestamp);
      
      // Define user category based on patterns in username or action
      let category = 'staff';
      if (username.toLowerCase().includes('admin') || 
          username.toLowerCase().includes('system')) {
        category = 'admin';
      } else if (username.toLowerCase().includes('support') || 
                username.toLowerCase().includes('technical')) {
        category = 'support';
      } else if (log.action.toLowerCase().includes('login') || 
                log.action.toLowerCase().includes('password')) {
        category = 'security';
      }
      
      if (!users.has(username)) {
        users.set(username, {
          count: 0,
          lastActivity: timestamp,
          category
        });
      }
      
      const userStat = users.get(username)!;
      userStat.count++;
      
      if (timestamp > userStat.lastActivity) {
        userStat.lastActivity = timestamp;
      }
    });
    
    // Convert to array and sort by count (most active first)
    return Array.from(users.entries())
      .map(([username, stats]) => ({ username, ...stats }))
      .sort((a, b) => b.count - a.count);
  }, [logs]);

  // Filter users based on search and category
  const filteredUsers = useMemo(() => {
    return userCategories.filter(user => {
      const matchesSearch = !searchQuery || 
        user.username.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesCategory = categoryFilter === 'all' || user.category === categoryFilter;
      return matchesSearch && matchesCategory;
    });
  }, [userCategories, searchQuery, categoryFilter]);

  const categories = [
    { id: 'all', name: 'All Categories' },
    { id: 'admin', name: 'Administrators' },
    { id: 'support', name: 'Support Team' },
    { id: 'security', name: 'Security Related' },
    { id: 'staff', name: 'General Staff' }
  ];

  // Get unique categories that exist in the current data
  const activeCategories = useMemo(() => {
    const uniqueCategories = new Set(userCategories.map(user => user.category));
    return categories.filter(cat => cat.id === 'all' || uniqueCategories.has(cat.id));
  }, [userCategories]);

  return (
    <div className="mb-6">
      <h3 className="text-sm font-medium text-gray-700 mb-3">Filter by User Category</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Category selector */}
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-4">
          <h4 className="text-xs uppercase text-gray-500 font-medium mb-3">User Categories</h4>
          <div className="space-y-2">
            {activeCategories.map(category => (
              <button
                key={category.id}
                onClick={() => setCategoryFilter(category.id)}
                className={`w-full text-left px-3 py-2 rounded-md text-sm ${
                  categoryFilter === category.id
                    ? 'bg-blue-50 text-blue-700 font-medium'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                {category.name}
                {category.id !== 'all' && (
                  <span className="ml-1 text-xs text-gray-500">
                    ({userCategories.filter(u => u.category === category.id).length})
                  </span>
                )}
              </button>
            ))}
          </div>
        </div>
        
        {/* User selector */}
        <div className="md:col-span-2">
          <div className="relative">
            <div 
              className="flex items-center justify-between p-3 border border-gray-200 rounded-lg bg-white cursor-pointer"
              onClick={() => setShowDropdown(!showDropdown)}
            >
              <div className="flex items-center gap-2">
                <User className="h-5 w-5 text-gray-400" />
                <span className="text-sm text-gray-700">
                  {selectedUser ? selectedUser : 'Select a user'}
                </span>
              </div>
              <ChevronDown className="h-4 w-4 text-gray-500" />
            </div>
            
            {showDropdown && (
              <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-lg shadow-lg">
                <div className="p-2">
                  <div className="relative mb-2">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search users..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-9 pr-4 py-2 text-sm border border-gray-300 rounded-lg"
                    />
                    {searchQuery && (
                      <button 
                        onClick={() => setSearchQuery('')}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                  
                  <div className="max-h-60 overflow-y-auto py-1">
                    {filteredUsers.length > 0 ? (
                      filteredUsers.map(user => (
                        <button
                          key={user.username}
                          className={`flex items-center justify-between w-full px-4 py-2 text-sm ${
                            selectedUser === user.username 
                              ? 'bg-blue-50 text-blue-700' 
                              : 'text-gray-700 hover:bg-gray-50'
                          }`}
                          onClick={() => {
                            onUserSelect(user.username);
                            setShowDropdown(false);
                          }}
                        >
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-gray-400" />
                            <span>{user.username}</span>
                          </div>
                          <span className="text-xs text-gray-500">{user.count} logs</span>
                        </button>
                      ))
                    ) : (
                      <div className="px-4 py-2 text-sm text-gray-500">No users found</div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
          
          {/* User grid for quick selection */}
          <div className="mt-4 grid grid-cols-2 sm:grid-cols-3 gap-2">
            {filteredUsers.slice(0, 6).map(user => (
              <button
                key={user.username}
                className={`flex items-center justify-between p-3 border rounded-lg text-left ${
                  selectedUser === user.username 
                    ? 'border-blue-500 bg-blue-50 text-blue-700' 
                    : 'border-gray-200 bg-white hover:bg-gray-50'
                }`}
                onClick={() => onUserSelect(user.username)}
              >
                <div className="flex items-center gap-2 truncate">
                  <div className={`h-6 w-6 rounded-full flex items-center justify-center text-white
                    ${user.category === 'admin' ? 'bg-purple-500' : 
                      user.category === 'support' ? 'bg-blue-500' :
                      user.category === 'security' ? 'bg-red-500' : 'bg-green-500'}`}
                  >
                    <User className="h-3 w-3" />
                  </div>
                  <span className="text-sm truncate" title={user.username}>{user.username}</span>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}; 