import { Request, Response } from 'express';
import { AppDataSource } from '../config/database';
import { LeaveRequest } from '../entities/LeaveRequest';
import { LeaveBalance } from '../entities/LeaveBalance';
import { User } from '../entities/User';
import { Employee } from '../server/entities/Employee';
import { LeaveStatus } from '../types/attendance';
import { LeaveApprovalService } from '../services/LeaveApprovalService';
import { ApprovalLevel, ApprovalStatus, LeaveApproval } from '../entities/LeaveApproval';
import { UserRole } from '../types/common';
import { In } from 'typeorm';

export class LeaveRequestController {
  private leaveApprovalService: LeaveApprovalService;

  constructor() {
    this.leaveApprovalService = new LeaveApprovalService(AppDataSource);
  }

  /**
   * Get all leave requests with pagination and filtering
   */
  async getAll(req: Request, res: Response): Promise<void> {
    try {
      const {
        page = 1,
        limit = 10,
        status,
        department,
        leaveType,
        startDate,
        endDate,
        employeeId
      } = req.query;

      const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
      
      let query = leaveRequestRepository.createQueryBuilder('request')
        .leftJoinAndSelect('request.employee', 'employee')
        .leftJoinAndSelect('employee.job', 'job')
        .leftJoinAndSelect('request.approvals', 'approvals') // Add approvals relation
        .select([
          'request.id',
          'request.employeeId',
          'request.leaveType',
          'request.startDate',
          'request.endDate',
          'request.daysRequested',
          'request.status',
          'request.reason',
          'request.emergencyContact', // Include emergency contact
          'request.attachments', // Include attachments
          'request.createdAt',
          'request.updatedAt',
          'request.source',
          'request.currentStage',
          'request.modificationHistory',
          'request.originalStartDate',
          'request.originalEndDate',
          'request.originalReason',
          'request.isUrgent',
          'request.createdBy',
          'employee.firstName',
          'employee.lastName',
          'employee.employeeId',
          'job.department',
          'job.designation',
          'approvals'
        ]);

      // Apply filters
      if (status) {
        query = query.andWhere('request.status = :status', { status });
      }

      if (department) {
        query = query.andWhere('job.department = :department', { department });
      }

      if (leaveType) {
        query = query.andWhere('request.leaveType = :leaveType', { leaveType });
      }

      if (startDate) {
        query = query.andWhere('request.startDate >= :startDate', { startDate });
      }

      if (endDate) {
        query = query.andWhere('request.endDate <= :endDate', { endDate });
      }

      if (employeeId) {
        query = query.andWhere('request.employeeId = :employeeId', { employeeId: parseInt(employeeId as string) });
      }

      // Add pagination
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;

      query = query.skip(skip).take(limitNum);

      const [requests, total] = await query.getManyAndCount();

      // Transform the data to include employee name and department
      const transformedRequests = requests.map(request => ({
        ...request,
        employeeName: request.employee ? `${request.employee.firstName} ${request.employee.lastName}`.trim() : 'Unknown Employee',
        employeeCode: request.employee?.employeeId || 'N/A',
        department: request.employee?.job?.department || 'N/A',
        position: request.employee?.job?.designation || 'N/A'
      }));

      res.json({
        success: true,
        data: transformedRequests,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      });
    } catch (error: any) {
      console.error('Error fetching leave requests:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch leave requests',
        error: error.message
      });
    }
  }

  /**
   * Get leave request by ID
   */
  async getById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
      
      const request = await leaveRequestRepository.findOne({
        where: { id: parseInt(id) },
        relations: ['employee', 'employee.job', 'approvals'], // Add job relation
        select: [
          'id',
          'employeeId',
          'leaveType',
          'startDate',
          'endDate',
          'daysRequested',
          'status',
          'reason',
          'emergencyContact', // Include emergency contact
          'attachments', // Include attachments
          'createdAt',
          'updatedAt',
          'source',
          'currentStage',
          'modificationHistory',
          'originalStartDate',
          'originalEndDate',
          'originalReason',
          'isUrgent',
          'createdBy'
        ]
      });

      if (!request) {
        res.status(404).json({
          success: false,
          message: 'Leave request not found'
        });
        return;
      }

      // Transform the data to include employee name and department
      const transformedRequest = {
        ...request,
        employeeName: request.employee ? `${request.employee.firstName} ${request.employee.lastName}`.trim() : 'Unknown Employee',
        employeeCode: request.employee?.employeeId || 'N/A',
        department: request.employee?.job?.department || 'N/A',
        position: request.employee?.job?.designation || 'N/A'
      };

      res.json({
        success: true,
        data: transformedRequest
      });
    } catch (error: any) {
      console.error('Error fetching leave request:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch leave request',
        error: error.message
      });
    }
  }

  /**
   * Get leave requests by employee ID
   */
  async getByEmployeeId(req: Request, res: Response): Promise<void> {
    try {
      const { employeeId } = req.params;
      const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
      
      // Show all requests for this employee (including HR created ones on their behalf)
      const requests = await leaveRequestRepository
        .createQueryBuilder('leaveRequest')
        .leftJoinAndSelect('leaveRequest.employee', 'employee')
        .leftJoinAndSelect('leaveRequest.approvals', 'approvals') // Add approvals relation
        .where('leaveRequest.employeeId = :employeeId', { employeeId: parseInt(employeeId) })
        .select([
          'leaveRequest.id',
          'leaveRequest.employeeId',
          'leaveRequest.leaveType',
          'leaveRequest.startDate',
          'leaveRequest.endDate',
          'leaveRequest.daysRequested',
          'leaveRequest.status',
          'leaveRequest.reason',
          'leaveRequest.emergencyContact', // Include emergency contact
          'leaveRequest.attachments', // Include attachments
          'leaveRequest.createdAt',
          'leaveRequest.updatedAt',
          'leaveRequest.source',
          'leaveRequest.currentStage',
          'leaveRequest.modificationHistory',
          'leaveRequest.originalStartDate',
          'leaveRequest.originalEndDate',
          'leaveRequest.originalReason',
          'leaveRequest.isUrgent',
          'leaveRequest.createdBy',
          'employee.firstName',
          'employee.lastName',
          'approvals' // Include approvals in select
        ])
        .orderBy('leaveRequest.createdAt', 'DESC')
        .getMany();

      const data = requests.map(req => ({
        ...req,
        employeeName: req.employee ? `${req.employee.firstName} ${req.employee.lastName}` : ''
      }));

      res.status(200).json({
        success: true,
        data: data,
        message: `Found ${requests.length} leave requests`
      });
    } catch (error: any) {
      console.error('Error fetching employee leave requests:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch employee leave requests',
        error: error.message
      });
    }
  }

  /**
   * Helper function to find manager by name or identifier
   */
  private async findManagerByReportingTo(reportingTo: string): Promise<number | null> {
    if (!reportingTo || reportingTo.trim() === '') {
      return null;
    }

    const userRepository = AppDataSource.getRepository(User);
    const employeeRepository = AppDataSource.getRepository(Employee);

    // Try to find by name in User table first
    let manager = await userRepository.findOne({
      where: { name: reportingTo.trim() }
    });

    if (manager) {
      return parseInt(manager.id);
    }

    // Try to find by combined first and last name in Employee table
    const employees = await employeeRepository.find({
      relations: ['job']
    });

    for (const employee of employees) {
      const fullName = `${employee.firstName} ${employee.lastName}`.trim();
      if (fullName.toLowerCase() === reportingTo.toLowerCase().trim()) {
        // Find corresponding user record
        const user = await userRepository.findOne({
          where: { name: fullName }
        });
        if (user) {
          return parseInt(user.id);
        }
      }
    }

    // If no exact match found, try partial matching
    const partialMatch = await userRepository.createQueryBuilder('user')
      .where('LOWER(user.name) LIKE :name', { name: `%${reportingTo.toLowerCase()}%` })
      .getOne();

    if (partialMatch) {
      return parseInt(partialMatch.id);
    }

    console.warn(`Could not find manager for reportingTo: "${reportingTo}"`);
    return null;
  }

  /**
   * Update an existing leave request
   */
  async update(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const {
        leaveType,
        startDate,
        endDate,
        reason,
        emergencyContact,
        attachments = []
      } = req.body;

      // Validate required fields
      if (!leaveType || !startDate || !endDate || !reason) {
        res.status(400).json({
          success: false,
          message: 'Missing required fields'
        });
        return;
      }

      const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
      
      // Find the existing leave request
      const existingRequest = await leaveRequestRepository.findOne({
        where: { id: parseInt(id) },
        relations: ['employee']
      });

      if (!existingRequest) {
        res.status(404).json({
          success: false,
          message: 'Leave request not found'
        });
        return;
      }

      // Only allow updates for PENDING requests
      if (existingRequest.status !== LeaveStatus.PENDING) {
        res.status(400).json({
          success: false,
          message: 'Cannot modify leave request that is not pending'
        });
        return;
      }

      // Calculate new days requested
      const start = new Date(startDate);
      const end = new Date(endDate);
      const newDaysRequested = Math.floor((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;

      // Calculate the difference in days for balance adjustment
      const oldDaysRequested = existingRequest.daysRequested;
      const daysDifference = newDaysRequested - oldDaysRequested;

      // Update the leave request
      await leaveRequestRepository.update(
        { id: parseInt(id) },
        {
          leaveType,
          startDate,
          endDate,
          reason,
          daysRequested: newDaysRequested,
          emergencyContact,
          attachments: attachments.length > 0 ? JSON.stringify(attachments) : undefined,
          updatedAt: new Date()
        }
      );

      // Update leave balance if there's a difference in days
      if (daysDifference !== 0) {
        const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);
        const currentYear = new Date().getFullYear();
        
        const balance = await leaveBalanceRepository.findOne({
          where: {
            employeeId: existingRequest.employeeId,
            leaveType: existingRequest.leaveType,
            year: currentYear,
            isActive: true
          }
        });

        if (balance) {
          // Remove old pending days and add new pending days
          balance.removePending(oldDaysRequested);
          balance.addPending(newDaysRequested);
          await leaveBalanceRepository.save(balance);
        }
      }

      // Get the updated request
      const updatedRequest = await leaveRequestRepository.findOne({
        where: { id: parseInt(id) },
        relations: ['employee']
      });

      res.json({
        success: true,
        data: updatedRequest,
        message: 'Leave request updated successfully'
      });
    } catch (error: any) {
      console.error('Error updating leave request:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update leave request',
        error: error.message
      });
    }
  }

  /**
   * Create a new leave request
   */
  async create(req: Request, res: Response): Promise<void> {
    try {
      const {
        employeeId,
        leaveType,
        startDate,
        endDate,
        reason,
        emergencyContact,
        attachments = []
      } = req.body;

      // Validate required fields
      if (!employeeId || !leaveType || !startDate || !endDate || !reason) {
        res.status(400).json({
          success: false,
          message: 'Missing required fields'
        });
        return;
      }

      // Calculate days requested
      const start = new Date(startDate);
      const end = new Date(endDate);
      const daysRequested = Math.floor((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;

      const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
      const employeeRepository = AppDataSource.getRepository(Employee);

      // Verify employee exists and get their job information including reporting manager
      const employee = await employeeRepository.findOne({ 
        where: { id: employeeId },
        relations: ['job'] // Include job relation to get reportingTo
      });

      if (!employee) {
        res.status(404).json({
          success: false,
          message: 'Employee not found'
        });
        return;
      }

      // Get current user info from request (assuming it's added by auth middleware)
      const currentUser = (req as any).user;
      // Determine who is creating the leave request
      let creatorName = '';
      let source: 'EMPLOYEE' | 'HR_ADMIN' | 'MANAGER' = 'EMPLOYEE';

      // Check if the current user is an admin creating leave for another employee
      if (currentUser?.role === 'HR_ADMIN' && parseInt(employeeId) !== parseInt(currentUser?.id)) {
        // Created by HR/Admin for someone else
        creatorName = currentUser?.name || 'HR/Admin';
        source = 'HR_ADMIN';
      } else {
        // Self-applied by the employee (or admin for themselves)
        creatorName = employee.firstName + ' ' + employee.lastName;
        source = 'EMPLOYEE';
      }

      // Create the leave request
      const leaveRequest = leaveRequestRepository.create({
        employeeId: parseInt(employeeId),
        leaveType,
        startDate,
        endDate,
        reason,
        daysRequested,
        status: LeaveStatus.PENDING,
        emergencyContact,
        attachments: attachments.length > 0 ? JSON.stringify(attachments) : undefined,
        source,
        createdBy: creatorName,
        originalStartDate: startDate,
        originalEndDate: endDate,
        originalReason: reason,
        currentStage: 'manager',
        isUrgent: false
      });

      const savedLeaveRequest = await leaveRequestRepository.save(leaveRequest);

      // Initialize the approval workflow for the new leave request
      await this.leaveApprovalService.initializeApprovalWorkflow(savedLeaveRequest.id);

      // Update leave balance (add to pending)
      const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);
      const currentYear = new Date().getFullYear();
      
      const balance = await leaveBalanceRepository.findOne({
        where: {
          employeeId,
          leaveType,
          year: currentYear,
          isActive: true
        }
      });

      if (balance) {
        balance.addPending(daysRequested);
        await leaveBalanceRepository.save(balance);
      }

      // Log the manager assignment for debugging
      if (employee.job?.reportingTo) {
        // Optionally, you can still call findManagerByReportingTo for logging, but don't use managerId if not needed
        const foundManagerId = await this.findManagerByReportingTo(employee.job.reportingTo);
        if (foundManagerId) {
          console.log(`✅ Leave request created for employee ${employeeId} with manager ${foundManagerId} (${employee.job?.reportingTo})`);
        } else {
          console.log(`⚠️ Leave request created for employee ${employeeId} without manager assignment`);
        }
      } else {
        console.log(`⚠️ Leave request created for employee ${employeeId} without manager assignment (no reportingTo in job)`);
      }

      res.status(201).json({
        success: true,
        data: savedLeaveRequest,
        message: 'Leave request submitted successfully and approval workflow initialized'
      });
    } catch (error: any) {
      console.error('Error creating leave request:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create leave request',
        error: error.message
      });
    }
  }

  /**
   * Approve a leave request
   */
  async approve(req: Request, res: Response): Promise<void> {
    try {
      console.log('🚀 Approve method called with:', {
        params: req.params,
        body: req.body,
        user: req.user ? { id: req.user.id, name: req.user.name } : 'No user'
      });

      const { id } = req.params;
      const { reason } = req.body;
      const currentUser = req.user; // Get the authenticated user

      if (!currentUser) {
        console.log('❌ No authenticated user found');
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      console.log('📋 Processing approval for:', { 
        id, 
        reason, 
        userId: currentUser.id,
        userName: currentUser.name 
      });

      // Determine approval level based on user role
      let approvalLevel: ApprovalLevel;
      if (currentUser.role === UserRole.HR_ADMIN || currentUser.role === UserRole.HR_STAFF) {
        approvalLevel = ApprovalLevel.HR;
      } else if (currentUser.role === UserRole.PROJECT_MANAGER || currentUser.role === UserRole.DEPT_HEAD) {
        approvalLevel = ApprovalLevel.MANAGER;
      } else {
        // Default to MANAGER for other roles that can approve
        approvalLevel = ApprovalLevel.MANAGER;
      }

      console.log('🔍 Determined approval level:', approvalLevel);

      // Check if the user can approve at this level
      const canApprove = await this.leaveApprovalService.canApproveAtLevel(
        parseInt(id), 
        approvalLevel === ApprovalLevel.MANAGER ? ApprovalLevel.MANAGER : ApprovalLevel.HR
      );

      if (!canApprove) {
        console.log('❌ User cannot approve at this level');
        res.status(403).json({
          success: false,
          message: 'Cannot approve at this level. Previous approvals may be required or this request has already been processed.'
        });
        return;
      }

      // Use the multi-level approval service
      await this.leaveApprovalService.processApproval(
        parseInt(id),
        approvalLevel === ApprovalLevel.MANAGER ? ApprovalLevel.MANAGER : ApprovalLevel.HR,
        currentUser.id,
        currentUser.name,
        'approve',
        reason
      );

      console.log('✅ Multi-level approval processed successfully');

      // Fetch updated leave request and approval history
      const leaveRequest = await this.leaveApprovalService.getLeaveRequestWithApprovals(parseInt(id));
      const approvals = await this.leaveApprovalService.getApprovalHistory(parseInt(id));

      console.log('🎉 Approval process completed successfully');

      res.json({
        success: true,
        data: {
          leaveRequest,
          approvals
        },
        message: `Leave request approved by ${approvalLevel.toLowerCase()}`
      });
    } catch (error: any) {
      console.error('❌ Error approving leave request:', error);
      console.error('❌ Error stack:', error.stack);
      res.status(500).json({
        success: false,
        message: 'Failed to approve leave request',
        error: error.message
      });
    }
  }

  /**
   * Reject a leave request
   */
  async reject(req: Request, res: Response): Promise<void> {
    try {
      console.log('🚀 Reject method called with:', {
        params: req.params,
        body: req.body,
        user: req.user ? { id: req.user.id, name: req.user.name } : 'No user'
      });

      const { id } = req.params;
      const { reason } = req.body;
      const currentUser = req.user; // Get the authenticated user

      if (!currentUser) {
        console.log('❌ No authenticated user found');
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      if (!reason || reason.trim() === '') {
        console.log('❌ Rejection reason is required');
        res.status(400).json({
          success: false,
          message: 'Rejection reason is required'
        });
        return;
      }

      console.log('📋 Processing rejection for:', { 
        id, 
        reason, 
        userId: currentUser.id,
        userName: currentUser.name 
      });

      // Determine approval level based on user role
      let approvalLevel: ApprovalLevel;
      if (currentUser.role === UserRole.HR_ADMIN || currentUser.role === UserRole.HR_STAFF) {
        approvalLevel = ApprovalLevel.HR;
      } else if (currentUser.role === UserRole.PROJECT_MANAGER || currentUser.role === UserRole.DEPT_HEAD) {
        approvalLevel = ApprovalLevel.MANAGER;
      } else {
        // Default to MANAGER for other roles that can approve
        approvalLevel = ApprovalLevel.MANAGER;
      }

      console.log('🔍 Determined approval level:', approvalLevel);

      // Check if the user can approve at this level
      const canApprove = await this.leaveApprovalService.canApproveAtLevel(
        parseInt(id), 
        approvalLevel === ApprovalLevel.MANAGER ? ApprovalLevel.MANAGER : ApprovalLevel.HR
      );

      if (!canApprove) {
        console.log('❌ User cannot reject at this level');
        res.status(403).json({
          success: false,
          message: 'Cannot reject at this level. Previous approvals may be required or this request has already been processed.'
        });
        return;
      }

      // Use the multi-level approval service
      await this.leaveApprovalService.processApproval(
        parseInt(id),
        approvalLevel === ApprovalLevel.MANAGER ? ApprovalLevel.MANAGER : ApprovalLevel.HR,
        currentUser.id,
        currentUser.name,
        'reject',
        reason
      );

      console.log('✅ Multi-level rejection processed successfully');

      // Fetch updated leave request and approval history
      const leaveRequest = await this.leaveApprovalService.getLeaveRequestWithApprovals(parseInt(id));
      const approvals = await this.leaveApprovalService.getApprovalHistory(parseInt(id));

      console.log('🎉 Rejection process completed successfully');

      res.json({
        success: true,
        data: {
          leaveRequest,
          approvals
        },
        message: `Leave request rejected by ${approvalLevel.toLowerCase()}`
      });
    } catch (error: any) {
      console.error('❌ Error rejecting leave request:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to reject leave request',
        error: error.message
      });
    }
  }

  /**
   * Update leave request status
   */
  async updateStatus(req: Request, res: Response): Promise<void> {
    try {
      console.log('🚀 UpdateStatus method called with:', {
        params: req.params,
        body: req.body,
        user: req.user ? { id: req.user.id, name: req.user.name } : 'No user'
      });

      const { id } = req.params;
      const { status, reason } = req.body;
      const currentUser = req.user; // Get the authenticated user

      if (!currentUser) {
        console.log('❌ No authenticated user found');
        res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
        return;
      }

      const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
      const leaveRequest = await leaveRequestRepository.findOne({
        where: { id: parseInt(id) },
        relations: ['employee']
      });

      if (!leaveRequest) {
        res.status(404).json({
          success: false,
          message: 'Leave request not found'
        });
        return;
      }

      // Prepare update data
      const updateData: any = {
        status: status
      };

      console.log('📋 Updating leave request status:', updateData);

      await leaveRequestRepository.update(
        { id: leaveRequest.id },
        updateData
      );

      console.log('✅ Leave request status updated successfully:', {
        status,
        userId: currentUser.id,
        userName: currentUser.name,
        reason: reason || 'Status updated'
      });

      // Fetch the updated leave request to return complete data
      const updatedLeaveRequest = await leaveRequestRepository.findOne({
        where: { id: leaveRequest.id },
        relations: ['employee']
      });

      res.json({
        success: true,
        data: updatedLeaveRequest,
        message: 'Leave request status updated successfully'
      });
    } catch (error: any) {
      console.error('❌ Error updating leave request status:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update leave request status',
        error: error.message
      });
    }
  }

  /**
   * Delete a leave request
   */
  async delete(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
      
      const leaveRequest = await leaveRequestRepository.findOne({
        where: { id: parseInt(id) }
      });

      if (!leaveRequest) {
        res.status(404).json({
          success: false,
          message: 'Leave request not found'
        });
        return;
      }

      await leaveRequestRepository.remove(leaveRequest);

      res.json({
        success: true,
        message: 'Leave request deleted successfully'
      });
    } catch (error: any) {
      console.error('Error deleting leave request:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete leave request',
        error: error.message
      });
    }
  }

  /**
   * Cancel a leave request (by employee)
   * Allows cancellation of approved/pending leaves
   */
  async cancel(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { reason } = req.body;
      const currentUser = (req as any).user;

      const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);

      // Use the service method to cancel the leave request
      const leaveRequest = await this.leaveApprovalService.cancelLeaveRequest(
        parseInt(id),
        currentUser?.name || 'Employee',
        reason
      );

      // Update leave balance based on original status
      const currentYear = new Date().getFullYear();
      const balance = await leaveBalanceRepository.findOne({
        where: {
          employeeId: leaveRequest.employeeId,
          leaveType: leaveRequest.leaveType,
          year: currentYear,
          isActive: true
        }
      });

      if (balance) {
        // Get the original status from modification history
        const modificationHistory = leaveRequest.modificationHistory ? 
          JSON.parse(leaveRequest.modificationHistory) : [];
        const lastModification = modificationHistory[modificationHistory.length - 1];
        const originalStatus = lastModification?.previousStatus || LeaveStatus.PENDING;

        if (originalStatus === LeaveStatus.PENDING) {
          // Remove from pending
          balance.removePending(leaveRequest.daysRequested);
        } else if (originalStatus === LeaveStatus.APPROVED) {
          // Return used days to balance by reducing the used amount
          balance.used = Math.max(0, balance.used - leaveRequest.daysRequested);
        }
        await leaveBalanceRepository.save(balance);
      }

      res.json({
        success: true,
        data: leaveRequest,
        message: 'Leave request cancelled successfully'
      });
    } catch (error: any) {
      console.error('Error cancelling leave request:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to cancel leave request',
        error: error.message
      });
    }
  }

  /**
   * Validate bulk leave requests before creation
   */
  async validateBulkLeave(req: Request, res: Response): Promise<void> {
    try {
      const { leaveType, startDate, endDate, isHalfDay, employeeIds } = req.body;

      if (!leaveType || !startDate || !endDate || !employeeIds || !Array.isArray(employeeIds)) {
        res.status(400).json({
          success: false,
          message: 'Missing required fields: leaveType, startDate, endDate, employeeIds'
        });
        return;
      }

      const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
      const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);
      const employeeRepository = AppDataSource.getRepository(Employee);

      // Calculate days requested
      const start = new Date(startDate);
      const end = new Date(endDate);
      const diffTime = end.getTime() - start.getTime();
      const daysRequested = isHalfDay ? 0.5 : Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1;

      // Get employees
      const employees = await employeeRepository.findBy({
        id: In(employeeIds)
      });

      const validations = [];
      let validRequests = 0;
      let invalidRequests = 0;

      for (const employee of employees) {
        const errors: string[] = [];
        const warnings: string[] = [];

        // Check leave balance
        const currentYear = new Date().getFullYear();
        const balance = await leaveBalanceRepository.findOne({
          where: {
            employeeId: employee.id,
            leaveType,
            year: currentYear,
            isActive: true
          }
        });

        if (balance) {
          if (balance.remaining < daysRequested) {
            errors.push(`Insufficient leave balance. Available: ${balance.remaining}, Requested: ${daysRequested}`);
          }
          if (balance.pending > 0) {
            warnings.push(`Employee has ${balance.pending} days of pending leave requests`);
          }
        } else {
          errors.push('No leave balance found for this leave type');
        }

        // Check for overlapping requests
        const overlappingRequests = await leaveRequestRepository.count({
          where: {
            employeeId: employee.id,
            status: In([LeaveStatus.PENDING, LeaveStatus.APPROVED]),
            startDate: startDate,
            endDate: endDate
          }
        });

        if (overlappingRequests > 0) {
          errors.push('Employee has overlapping leave requests for this period');
        }

        const isValid = errors.length === 0;
        if (isValid) validRequests++;
        else invalidRequests++;

        validations.push({
          employeeId: employee.id,
          employeeName: `${employee.firstName} ${employee.lastName}`,
          isValid,
          errors,
          warnings,
          availableBalance: balance?.remaining || 0,
          requestedDays: daysRequested
        });
      }

      res.json({
        success: true,
        data: {
          validations,
          summary: {
            totalEmployees: employees.length,
            validRequests,
            invalidRequests,
            totalDaysRequested: employees.length * daysRequested
          }
        }
      });
    } catch (error: any) {
      console.error('Error validating bulk leave:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to validate bulk leave requests',
        error: error.message
      });
    }
  }

  /**
   * Create bulk leave requests
   */
  async createBulkLeave(req: Request, res: Response): Promise<void> {
    try {
      const { requests, options } = req.body;

      if (!requests || !Array.isArray(requests) || requests.length === 0) {
        res.status(400).json({
          success: false,
          message: 'No leave requests provided'
        });
        return;
      }

      const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
      const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);
      const employeeRepository = AppDataSource.getRepository(Employee);

      const successful: any[] = [];
      const failed: any[] = [];
      let autoApproved = 0;

      // Process each request
      for (const requestData of requests) {
        try {
          // Validate employee exists
          const employee = await employeeRepository.findOne({
            where: { id: requestData.employeeId }
          });

          if (!employee) {
            failed.push({
              request: requestData,
              error: 'Employee not found'
            });
            continue;
          }

          // Create leave request
          const leaveRequest = leaveRequestRepository.create({
            employeeId: requestData.employeeId,
            leaveType: requestData.leaveType,
            startDate: requestData.startDate,
            endDate: requestData.endDate,
            reason: requestData.reason,
            daysRequested: requestData.daysRequested,
            status: requestData.status
          });

          await leaveRequestRepository.save(leaveRequest);

          // Update leave balance
          const currentYear = new Date().getFullYear();
          const balance = await leaveBalanceRepository.findOne({
            where: {
              employeeId: requestData.employeeId,
              leaveType: requestData.leaveType,
              year: currentYear,
              isActive: true
            }
          });

          if (balance) {
            if (requestData.status === LeaveStatus.APPROVED) {
              balance.useLeave(requestData.daysRequested);
              autoApproved++;
            } else {
              balance.addPending(requestData.daysRequested);
            }
            await leaveBalanceRepository.save(balance);
          }

          successful.push({
            ...leaveRequest,
            employeeName: requestData.employeeName
          });

        } catch (error: any) {
          failed.push({
            request: requestData,
            error: error.message
          });
        }
      }

      res.json({
        success: true,
        data: {
          successful,
          failed,
          summary: {
            total: requests.length,
            successful: successful.length,
            failed: failed.length,
            autoApproved
          }
        },
        message: `Created ${successful.length} leave requests successfully${failed.length > 0 ? `, ${failed.length} failed` : ''}`
      });

    } catch (error: any) {
      console.error('Error creating bulk leave requests:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create bulk leave requests',
        error: error.message
      });
    }
  }

  /**
   * Handle workflow actions (approve, reject, modify)
   */
  async handleWorkflowAction(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { action, reason, modifications } = req.body;
      const currentUser = (req as any).user;

      const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
      const leaveRequest = await leaveRequestRepository.findOne({ where: { id: parseInt(id) } });

      if (!leaveRequest) {
        res.status(404).json({
          success: false,
          message: 'Leave request not found'
        });
        return;
      }

      // Build modification history
      const modificationHistory = leaveRequest.modificationHistory ? 
        JSON.parse(leaveRequest.modificationHistory) : [];
      
      const modification = {
        timestamp: new Date().toISOString(),
        action,
        actor: currentUser?.name || 'System',
        actorRole: currentUser?.role || 'UNKNOWN',
        reason: reason || '',
        changes: modifications || {},
        previousStatus: leaveRequest.status
      };

      // Handle different workflow actions
      switch (action) {
        case 'approve':
          leaveRequest.status = LeaveStatus.APPROVED;
          break;

        case 'reject':
          leaveRequest.status = LeaveStatus.REJECTED;
          break;

        case 'modify':
          // Apply modifications if provided
          if (modifications) {
            if (modifications.startDate) {
              leaveRequest.startDate = modifications.startDate;
            }
            if (modifications.endDate) {
              leaveRequest.endDate = modifications.endDate;
            }
            if (modifications.reason) {
              leaveRequest.reason = modifications.reason;
            }
            if (modifications.daysRequested) {
              leaveRequest.daysRequested = modifications.daysRequested;
            }
          }
          break;

        case 'cancel':
          // Use the service method to ensure consistency
          await this.leaveApprovalService.cancelLeaveRequest(
            leaveRequest.id,
            currentUser?.name || 'System',
            reason
          );
          // The service method already updates the leave request, so we don't need to do it again
          return; // Exit early since the service method handles everything
      }

      // Update modification history
      modificationHistory.push(modification);
      leaveRequest.modificationHistory = JSON.stringify(modificationHistory);

      await leaveRequestRepository.save(leaveRequest);

      res.json({
        success: true,
        message: `Leave request ${action}d successfully`,
        data: leaveRequest
      });
    } catch (error) {
      console.error('Error handling workflow action:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  /**
   * Cancel leave request using the service method (reusable endpoint)
   * This ensures consistency between leave_requests and leave_approvals tables
   */
  async cancelLeaveRequestService(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { reason, cancelledBy } = req.body;
      const currentUser = (req as any).user;

      const leaveRequest = await this.leaveApprovalService.cancelLeaveRequest(
        parseInt(id),
        cancelledBy || currentUser?.name || 'Employee',
        reason
      );

      res.json({
        success: true,
        data: leaveRequest,
        message: 'Leave request cancelled successfully using service method'
      });
    } catch (error: any) {
      console.error('Error cancelling leave request via service:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to cancel leave request',
        error: error.message
      });
    }
  }

  /**
   * Get audit trail for a leave request
   */
  async getAuditTrail(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const leaveRequestRepository = AppDataSource.getRepository(LeaveRequest);
      const leaveRequest = await leaveRequestRepository.findOne({ where: { id: parseInt(id) } });

      if (!leaveRequest) {
        res.status(404).json({
          success: false,
          message: 'Leave request not found'
        });
        return;
      }

      const auditTrail = leaveRequest.modificationHistory ? 
        JSON.parse(leaveRequest.modificationHistory) : [];

      // Add creation event
      const creationEvent = {
        timestamp: leaveRequest.createdAt.toISOString(),
        action: 'create',
        actor: leaveRequest.createdBy,
        actorRole: leaveRequest.source || 'EMPLOYEE',
        reason: 'Request created',
        changes: {
          startDate: leaveRequest.originalStartDate || leaveRequest.startDate,
          endDate: leaveRequest.originalEndDate || leaveRequest.endDate,
          reason: leaveRequest.originalReason || leaveRequest.reason,
          leaveType: leaveRequest.leaveType,
          daysRequested: leaveRequest.daysRequested
        },
        previousStatus: null
      };

      const fullAuditTrail = [creationEvent, ...auditTrail];

      res.json({
        success: true,
        data: fullAuditTrail
      });
    } catch (error) {
      console.error('Error getting audit trail:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
}