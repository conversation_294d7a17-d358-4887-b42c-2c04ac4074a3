import React, { useState } from 'react';

interface ContractFormData {
  value: string;
  // Add other form fields as needed
}

interface ContractFormProps {
  onSubmit: (data: ContractFormData) => void;
  onClose: () => void;
}

export function ContractForm({ onSubmit, onClose }: ContractFormProps) {
  const [formData, setFormData] = useState<ContractFormData>({
    value: '',
    // Initialize other fields
  });

  return (
    <div className="relative">
      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">₨</span>
      <input
        type="number"
        value={formData.value}
        onChange={(e) => setFormData(prev => ({ ...prev, value: e.target.value }))}
        className="w-full pl-8 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        placeholder="0"
        min="0"
        step="1"
        required
      />
    </div>
  );
}