import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateAssetSchema1710000000002 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Make all required fields nullable using individual statements
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN assetType VARCHAR(50) NULL`);
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN category VARCHAR(100) NULL`);
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN manufacturer VARCHAR(100) NULL`);
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN model VARCHAR(100) NULL`);
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN serialNumber VARCHAR(100) NULL`);
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN location VARCHAR(100) NULL`);
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN department VARCHAR(50) NULL`);
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN status VARCHAR(50) NULL`);
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN \`condition\` VARCHAR(50) NULL`);
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN internetAccess BOOLEAN NULL DEFAULT FALSE`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert changes (make fields required again) using individual statements
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN assetType VARCHAR(50) NOT NULL`);
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN category VARCHAR(100) NOT NULL`);
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN manufacturer VARCHAR(100) NOT NULL`);
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN model VARCHAR(100) NOT NULL`);
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN serialNumber VARCHAR(100) NOT NULL`);
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN location VARCHAR(100) NOT NULL`);
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN department VARCHAR(50) NOT NULL`);
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN status VARCHAR(50) NOT NULL`);
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN \`condition\` VARCHAR(50) NOT NULL`);
        await queryRunner.query(`ALTER TABLE assets MODIFY COLUMN internetAccess BOOLEAN NOT NULL DEFAULT FALSE`);
    }
} 