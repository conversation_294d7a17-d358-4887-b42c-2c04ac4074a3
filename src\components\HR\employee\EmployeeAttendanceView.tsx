import React, { useState, useEffect } from 'react';
import { Tab } from '@headlessui/react';
import { 
  Attendance, 
  AttendanceStatus,
  AttendanceRegularizationRequest
} from '../../../types/attendance';
import { 
  Calendar, 
  Clock, 
  User, 
  CheckCircle,
  AlertCircle,
  Home,
  Filter,
  Maximize2,
  FileText,
  XCircle,
  Clock3
} from 'lucide-react';
import RegularizationManager from '../attendance/RegularizationManager';
import { toast } from 'react-hot-toast';
import RegularizationService from '../../../services/RegularizationService';

interface EmployeeAttendanceViewProps {
  employeeId: number;
  employeeName: string;
  employeeCode?: string;
  employeeDepartment?: string;
  employeePosition?: string;
  attendances: any[];
  isSelfPortal?: boolean; // Add this prop
}

const EmployeeAttendanceView: React.FC<EmployeeAttendanceViewProps> = ({
  employeeId,
  employeeName,
  employeeCode,
  employeeDepartment,
  employeePosition,
  attendances,
  isSelfPortal = false // Default to false
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [regularizationRequests, setRegularizationRequests] = useState<AttendanceRegularizationRequest[]>([]);
  
  // Existing filter states for attendance view
  const [filters, setFilters] = useState({
    datePeriod: 'today',
    dateFrom: '',
    dateTo: '',
    status: 'all',
    location: 'all',
    department: 'all',
    shift: 'all',
    workHoursFilter: 'all',
    searchText: ''
  });

  // Regularization form states
  const [isSubmittingRegularization, setIsSubmittingRegularization] = useState(false);
  
  // Date period options
  const getDateRangeFromPeriod = (period: string) => {
    const today = new Date();
    const startOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
    
    switch (period) {
      case 'today':
        return { start: startOfToday, end: endOfToday };
      case 'yesterday':
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        return { start: new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate()), 
                 end: new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59) };
      case 'this_week':
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay());
        return { start: new Date(startOfWeek.getFullYear(), startOfWeek.getMonth(), startOfWeek.getDate()), 
                 end: endOfToday };
      case 'last_week':
        const lastWeekStart = new Date(today);
        lastWeekStart.setDate(today.getDate() - today.getDay() - 7);
        const lastWeekEnd = new Date(lastWeekStart);
        lastWeekEnd.setDate(lastWeekStart.getDate() + 6);
        return { start: new Date(lastWeekStart.getFullYear(), lastWeekStart.getMonth(), lastWeekStart.getDate()), 
                 end: new Date(lastWeekEnd.getFullYear(), lastWeekEnd.getMonth(), lastWeekEnd.getDate(), 23, 59, 59) };
      case 'this_month':
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        return { start: startOfMonth, end: endOfToday };
      case 'last_month':
        const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0, 23, 59, 59);
        return { start: lastMonthStart, end: lastMonthEnd };
      case 'last_three_months':
        const threeMonthsStart = new Date(today.getFullYear(), today.getMonth() - 3, 1);
        return { start: threeMonthsStart, end: endOfToday };
      case 'last_six_months':
        const sixMonthsStart = new Date(today.getFullYear(), today.getMonth() - 6, 1);
        return { start: sixMonthsStart, end: endOfToday };
      case 'this_year':
        const startOfYear = new Date(today.getFullYear(), 0, 1);
        return { start: startOfYear, end: endOfToday };
      case 'last_year':
        const lastYearStart = new Date(today.getFullYear() - 1, 0, 1);
        const lastYearEnd = new Date(today.getFullYear() - 1, 11, 31, 23, 59, 59);
        return { start: lastYearStart, end: lastYearEnd };
      default:
        return null;
    }
  };
  
  // Filter attendances for the current employee
  const employeeAttendances = attendances.filter(a => a.employeeId === employeeId);
  
  // Get unique departments, shifts, and locations for filter dropdowns
  const uniqueDepartments = [...new Set(employeeAttendances.map(a => a.department || a.employeeDepartment).filter((dept): dept is string => Boolean(dept)))];
  const uniqueShifts = [...new Set(employeeAttendances.map(a => {
    if (a.shiftName && a.shiftName.trim() !== '') {
      return a.shiftName;
    } else if (a.shift) {
      const shiftNames = {
        1: 'Morning Shift',
        2: 'Evening Shift', 
        3: 'Night Shift',
        4: 'Flexible Hours'
      };
      return shiftNames[a.shift as keyof typeof shiftNames] || `Shift ${a.shift}`;
    }
    return null;
  }).filter((shift): shift is string => Boolean(shift)))];
  const uniqueLocations = [...new Set(employeeAttendances.map(a => a.location).filter((location): location is string => Boolean(location)))];
  
  // Apply filters to attendance data
  const filteredAttendances = employeeAttendances.filter(attendance => {
    // Date period filter (takes precedence over manual date range)
    if (filters.datePeriod !== 'all') {
      const dateRange = getDateRangeFromPeriod(filters.datePeriod);
      if (dateRange) {
        const attendanceDate = new Date(attendance.date);
        if (attendanceDate < dateRange.start || attendanceDate > dateRange.end) return false;
      }
    } else {
      // Manual date range filter (only when no period is selected)
      if (filters.dateFrom && new Date(attendance.date) < new Date(filters.dateFrom)) return false;
      if (filters.dateTo && new Date(attendance.date) > new Date(filters.dateTo)) return false;
    }
    
    // Status filter
    if (filters.status !== 'all' && attendance.status !== filters.status) return false;
    
    // Location filter
    if (filters.location !== 'all') {
      if (filters.location === 'office' && attendance.status === AttendanceStatus.WORK_FROM_HOME) return false;
      if (filters.location === 'remote' && attendance.status !== AttendanceStatus.WORK_FROM_HOME) return false;
      if (filters.location !== 'office' && filters.location !== 'remote' && attendance.location !== filters.location) return false;
    }
    
    // Department filter
    if (filters.department !== 'all') {
      const dept = attendance.department || attendance.employeeDepartment;
      if (dept !== filters.department) return false;
    }
    
    // Shift filter
    if (filters.shift !== 'all') {
      let shiftName = attendance.shiftName;
      if (!shiftName && attendance.shift) {
        const shiftNames = {
          1: 'Morning Shift',
          2: 'Evening Shift', 
          3: 'Night Shift',
          4: 'Flexible Hours'
        };
        shiftName = shiftNames[attendance.shift as keyof typeof shiftNames] || `Shift ${attendance.shift}`;
      }
      if (shiftName !== filters.shift) return false;
    }
    
    // Work hours filter
    if (filters.workHoursFilter !== 'all') {
      // Calculate actual work hours from check-in/check-out times for more accurate filtering
      let actualWorkHours = attendance.workHours || 0;
      
      if (attendance.checkInTime && attendance.checkOutTime && 
          attendance.checkInTime !== '-' && attendance.checkOutTime !== '-') {
        try {
          const [checkInHour, checkInMin] = attendance.checkInTime.split(':').map(Number);
          const [checkOutHour, checkOutMin] = attendance.checkOutTime.split(':').map(Number);
          
          const today = new Date();
          const checkIn = new Date(today.getFullYear(), today.getMonth(), today.getDate(), checkInHour, checkInMin);
          let checkOut = new Date(today.getFullYear(), today.getMonth(), today.getDate(), checkOutHour, checkOutMin);
          
          if (checkOut < checkIn) {
            checkOut.setDate(checkOut.getDate() + 1);
          }
          
          const diffMs = checkOut.getTime() - checkIn.getTime();
          actualWorkHours = diffMs / (1000 * 60 * 60);
        } catch (error) {
          actualWorkHours = attendance.workHours || 0;
        }
      }
      
      switch (filters.workHoursFilter) {
        case 'full_day':
          if (actualWorkHours < 7) return false;
          break;
        case 'partial_day':
          if (actualWorkHours >= 7) return false;
          break;
        case 'overtime':
          // Check if work hours > 8 OR if there's recorded overtime
          const hasCalculatedOvertime = actualWorkHours > 8;
          const hasRecordedOvertime = (attendance.overtime || 0) > 0;
          if (!hasCalculatedOvertime && !hasRecordedOvertime) return false;
          break;
      }
    }
    
    // Search filter (searches across multiple fields)
    if (filters.searchText) {
      const searchTerms = filters.searchText.toLowerCase();
      const searchableText = [
        attendance.status,
        attendance.location,
        attendance.checkInTime,
        attendance.checkOutTime,
        attendance.notes,
        new Date(attendance.date).toLocaleDateString()
      ].join(' ').toLowerCase();
      
      if (!searchableText.includes(searchTerms)) return false;
    }
    
    return true;
  });
  
  // Reset filters function
  const resetFilters = () => {
    setFilters({
      datePeriod: 'today',
      dateFrom: '',
      dateTo: '',
      status: 'all',
      location: 'all',
      department: 'all',
      shift: 'all',
      workHoursFilter: 'all',
      searchText: ''
    });
  };
  
  // Format work hours function
  const formatWorkHours = (hours: number | null | undefined, checkInTime?: string | null, checkOutTime?: string | null): string => {
    // If we have check-in and check-out times, calculate more accurate work hours
    if (checkInTime && checkOutTime && checkInTime !== '-' && checkOutTime !== '-') {
      try {
        const [checkInHour, checkInMin] = checkInTime.split(':').map(Number);
        const [checkOutHour, checkOutMin] = checkOutTime.split(':').map(Number);
        
        const today = new Date();
        const checkIn = new Date(today.getFullYear(), today.getMonth(), today.getDate(), checkInHour, checkInMin);
        let checkOut = new Date(today.getFullYear(), today.getMonth(), today.getDate(), checkOutHour, checkOutMin);
        
        // If check-out time is earlier than check-in time, assume it's next day
        if (checkOut < checkIn) {
          checkOut.setDate(checkOut.getDate() + 1);
        }
        
        // Calculate the difference in hours (with decimal precision)
        const diffMs = checkOut.getTime() - checkIn.getTime();
        const calculatedHours = diffMs / (1000 * 60 * 60); // Convert to hours
        
        // Convert decimal hours to hours and minutes
        const wholeHours = Math.floor(calculatedHours);
        const minutes = Math.round((calculatedHours - wholeHours) * 60);
        
        return wholeHours > 0 && minutes > 0 
          ? `${wholeHours}h ${minutes}m`
          : wholeHours > 0 
            ? `${wholeHours}h 0m`
            : `0h ${minutes}m`;
      } catch (error) {
        console.error('Error calculating work hours:', error);
      }
    }
    
    // Fall back to stored value if calculation fails or data is missing
    if (hours === null || hours === undefined) return '-';
    
    // Convert decimal hours to hours and minutes
    const wholeHours = Math.floor(hours);
    const minutes = Math.round((hours - wholeHours) * 60);
    
    return wholeHours > 0 && minutes > 0 
      ? `${wholeHours}h ${minutes}m`
      : wholeHours > 0 
        ? `${wholeHours}h 0m`
        : `0h ${minutes}m`;
  };

  // Handle regularization form submission
  const handleSubmitRegularization = async (request: Omit<AttendanceRegularizationRequest, 'id'>) => {
    setIsSubmittingRegularization(true);
    try {
      const service = RegularizationService.getInstance();
      await service.submitRequest(request); // Save to backend
      toast.success('Regularization request submitted successfully!');
      // Reload from backend
      const requests = await service.getRequestsByEmployee(employeeId);
      setRegularizationRequests(requests);
    } catch (error) {
      console.error('Error submitting regularization request:', error);
      toast.error('Error submitting request. Please try again.');
    } finally {
      setIsSubmittingRegularization(false);
    }
  };

  // Load regularization requests on component mount
  useEffect(() => {
    const loadRequests = async () => {
      try {
        const service = RegularizationService.getInstance();
        const requests = await service.getRequestsByEmployee(employeeId);
        setRegularizationRequests(requests);
      } catch (error) {
        console.error('Error loading regularization requests:', error);
        toast.error('Error loading regularization requests');
      }
    };

    loadRequests();
  }, [employeeId]);

  // Helper functions for table
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full";
    switch (status.toLowerCase()) {
      case 'approved':
        return (
          <span className={`${baseClasses} bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 flex items-center gap-1`}>
            <CheckCircle className="w-3 h-3" />
            Approved
          </span>
        );
      case 'rejected':
        return (
          <span className={`${baseClasses} bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400 flex items-center gap-1`}>
            <XCircle className="w-3 h-3" />
            Rejected
          </span>
        );
      default:
        return (
          <span className={`${baseClasses} bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400 flex items-center gap-1`}>
            <Clock3 className="w-3 h-3" />
            Pending
          </span>
        );
    }
  };

  return (
    <div className="p-2 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="mb-3">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
          {employeeName}'s Attendance
        </h2>
        
        {/* Attendance Summary Cards - Moved up */}
        <div className="mb-3">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Attendance Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">

            {/* Work Hours */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-3 border-l-4 border-green-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-gray-500 dark:text-gray-400">Work Hours</p>
                  <div className="flex items-baseline mt-1">
                    <p className="text-lg font-bold text-gray-900 dark:text-white">
                      {(filteredAttendances.reduce((sum, a) => sum + (a.workHours || 0), 0)).toFixed(1)}
                    </p>
                    <p className="ml-1 text-xs text-gray-500 dark:text-gray-400">total</p>
                  </div>
                </div>
                <div className="h-8 w-8 rounded-full bg-green-50 dark:bg-green-900/30 flex items-center justify-center">
                  <Clock className="h-4 w-4 text-green-500 dark:text-green-400" />
                </div>
              </div>
            </div>

            {/* Over Time */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-3 border-l-4 border-orange-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-gray-500 dark:text-gray-400">Over Time</p>
                  <div className="flex items-baseline mt-1">
                    <p className="text-lg font-bold text-gray-900 dark:text-white">
                      {(() => {
                        const totalOvertimeHours = filteredAttendances.reduce((sum, a) => {
                          let actualWorkHours = a.workHours || 0;
                          
                          if (a.checkInTime && a.checkOutTime && 
                              a.checkInTime !== '-' && a.checkOutTime !== '-') {
                            try {
                              const [checkInHour, checkInMin] = a.checkInTime.split(':').map(Number);
                              const [checkOutHour, checkOutMin] = a.checkOutTime.split(':').map(Number);
                              
                              const today = new Date();
                              const checkIn = new Date(today.getFullYear(), today.getMonth(), today.getDate(), checkInHour, checkInMin);
                              let checkOut = new Date(today.getFullYear(), today.getMonth(), today.getDate(), checkOutHour, checkOutMin);
                              
                              if (checkOut < checkIn) {
                                checkOut.setDate(checkOut.getDate() + 1);
                              }
                              
                              const diffMs = checkOut.getTime() - checkIn.getTime();
                              actualWorkHours = diffMs / (1000 * 60 * 60);
                            } catch (error) {
                              actualWorkHours = a.workHours || 0;
                            }
                          }
                          
                          const overtimeFromWorkHours = actualWorkHours > 8 ? actualWorkHours - 8 : 0;
                          const recordedOvertime = a.overtime || 0;
                          const actualOvertime = Math.max(overtimeFromWorkHours, recordedOvertime);
                          
                          return sum + actualOvertime;
                        }, 0);
                        return totalOvertimeHours.toFixed(1);
                      })()}
                    </p>
                    <p className="ml-1 text-xs text-orange-500 dark:text-orange-400">hours</p>
                  </div>
                </div>
                <div className="h-8 w-8 rounded-full bg-orange-50 dark:bg-orange-900/30 flex items-center justify-center">
                  <Clock className="h-4 w-4 text-orange-500 dark:text-orange-400" />
                </div>
              </div>
            </div>

            {/* Remote Work */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-3 border-l-4 border-purple-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-gray-500 dark:text-gray-400">Remote Work</p>
                  <div className="flex items-baseline mt-1">
                    <p className="text-lg font-bold text-gray-900 dark:text-white">
                      {filteredAttendances.filter(a => 
                        a.status === AttendanceStatus.WORK_FROM_HOME || a.isRemote
                      ).length}
                    </p>
                    <p className="ml-1 text-xs text-purple-500 dark:text-purple-400">
                      {filteredAttendances.length > 0 
                        ? `${((filteredAttendances.filter(a => a.status === AttendanceStatus.WORK_FROM_HOME || a.isRemote).length / filteredAttendances.length) * 100).toFixed(0)}%`
                        : '0%'
                      }
                    </p>
                  </div>
                </div>
                <div className="h-8 w-8 rounded-full bg-purple-50 dark:bg-purple-900/30 flex items-center justify-center">
                  <Home className="h-4 w-4 text-purple-500 dark:text-purple-400" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Tab.Group selectedIndex={activeTab} onChange={setActiveTab}>
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
          <Tab.List className="flex border-b border-gray-200 dark:border-gray-700">
            <Tab className={({ selected }) =>
              `py-2 px-4 text-sm font-medium flex items-center ${
                selected 
                  ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' 
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`
            }>
              <Calendar className="h-3 w-3 mr-2" />
              My Attendance
            </Tab>
            <Tab className={({ selected }) =>
              `py-2 px-4 text-sm font-medium flex items-center ${
                selected 
                  ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20' 
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`
            }>
              <FileText className="h-3 w-3 mr-2" />
              Regularization
            </Tab>
          </Tab.List>

          <Tab.Panels>
            {/* Attendance Records Tab */}
            <Tab.Panel className="p-3">
                {/* Filters Section */}
                <div className="mb-4 bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Filters & Search</h3>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 gap-3">
                    {/* Date Period */}
                    <div>
                      <select
                        value={filters.datePeriod}
                        onChange={(e) => setFilters(prev => ({ ...prev, datePeriod: e.target.value }))}
                        className="w-full text-sm py-2 px-3 rounded border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
                      >
                        <option value="today">Today</option>
                        <option value="yesterday">Yesterday</option>
                        <option value="this_week">This Week</option>
                        <option value="last_week">Last Week</option>
                        <option value="this_month">This Month</option>
                        <option value="last_month">Last Month</option>
                        <option value="last_three_months">Last 3 Months</option>
                        <option value="last_six_months">Last 6 Months</option>
                        <option value="this_year">This Year</option>
                        <option value="last_year">Last Year</option>
                        <option value="all">All Time</option>
                      </select>
                    </div>
            
                    {/* Status */}
                    <div>
                      <select
                        value={filters.status}
                        onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                        className="w-full text-sm py-2 px-3 rounded border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
                      >
                        <option value="all">All Status</option>
                        <option value="present">Present</option>
                        <option value="absent">Absent</option>
                        <option value="late">Late</option>
                        <option value="half_day">Half Day</option>
                        <option value="work_from_home">Work From Home</option>
                        <option value="leave">Leave</option>
                      </select>
                    </div>
                    
                    {/* Location */}
                    <div>
                      <select
                        value={filters.location}
                        onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                        className="w-full text-sm py-2 px-3 rounded border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
                      >
                        <option value="all">All Locations</option>
                        <option value="office">Office</option>
                        <option value="remote">Remote</option>
                        {uniqueLocations.map(location => (
                          <option key={location} value={location}>{location}</option>
                        ))}
                      </select>
                    </div>
                    
                    {/* Department */}
                    <div>
                      <select
                        value={filters.department}
                        onChange={(e) => setFilters(prev => ({ ...prev, department: e.target.value }))}
                        className="w-full text-sm py-2 px-3 rounded border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
                      >
                        <option value="all">All Departments</option>
                        {uniqueDepartments.map(dept => (
                          <option key={dept} value={dept}>{dept}</option>
                        ))}
                      </select>
                    </div>
                    
                    {/* Shift */}
                    <div>
                      <select
                        value={filters.shift}
                        onChange={(e) => setFilters(prev => ({ ...prev, shift: e.target.value }))}
                        className="w-full text-sm py-2 px-3 rounded border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
                      >
                        <option value="all">All Shifts</option>
                        {uniqueShifts.map(shift => (
                          <option key={shift} value={shift}>{shift}</option>
                        ))}
                      </select>
                    </div>
                    
                    {/* Work Hours Filter */}
                    <div>
                      <select
                        value={filters.workHoursFilter}
                        onChange={(e) => setFilters(prev => ({ ...prev, workHoursFilter: e.target.value }))}
                        className="w-full text-sm py-2 px-3 rounded border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
                      >
                        <option value="all">All Work Hours</option>
                        <option value="full_day">Full Day (7+ hrs)</option>
                        <option value="partial_day">Partial Day (&lt;7 hrs)</option>
                        <option value="overtime">With Overtime</option>
                      </select>
                    </div>
                    
                    {/* Search */}
                    <div>
                      <input
                        type="text"
                        placeholder="Search..."
                        value={filters.searchText}
                        onChange={(e) => setFilters(prev => ({ ...prev, searchText: e.target.value }))}
                        className="w-full text-sm py-2 px-3 rounded border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
                      />
                    </div>

                    {/* Reset Button */}
                    <div>
                      <button
                        onClick={resetFilters}
                        className="w-full px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                      >
                        Reset
                      </button>
                    </div>
                  </div>
                </div>
                
                {/* Attendance Table */}
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th scope="col" className="px-3 py-2 text-left text-xs font-bold text-black dark:text-white uppercase tracking-wider">Date</th>
                        <th scope="col" className="px-3 py-2 text-left text-xs font-bold text-black dark:text-white uppercase tracking-wider">Status</th>
                        <th scope="col" className="px-3 py-2 text-left text-xs font-bold text-black dark:text-white uppercase tracking-wider">Check In</th>
                        <th scope="col" className="px-3 py-2 text-left text-xs font-bold text-black dark:text-white uppercase tracking-wider">Check Out</th>
                        <th scope="col" className="px-3 py-2 text-left text-xs font-bold text-black dark:text-white uppercase tracking-wider">Work Hours</th>
                        <th scope="col" className="px-3 py-2 text-left text-xs font-bold text-black dark:text-white uppercase tracking-wider">Overtime</th>
                        <th scope="col" className="px-3 py-2 text-left text-xs font-bold text-black dark:text-white uppercase tracking-wider">Location</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {filteredAttendances.length > 0 ? (
                        filteredAttendances.map((attendance) => (
                          <tr key={attendance.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-900 dark:text-gray-100">
                              {new Date(attendance.date).toLocaleDateString('en-US', {
                                weekday: 'short',
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                              })}
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap">
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                attendance.status === AttendanceStatus.PRESENT 
                                  ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' 
                                  : attendance.status === AttendanceStatus.LATE
                                    ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400'
                                    : attendance.status === AttendanceStatus.WORK_FROM_HOME
                                      ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400'
                                      : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400'
                              }`}>
                                {attendance.status}
                              </span>
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400">
                              {attendance.checkInTime || '-'}
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400">
                              {attendance.checkOutTime || '-'}
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400">
                              {formatWorkHours(attendance.workHours, attendance.checkInTime, attendance.checkOutTime)}
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400">
                              {(() => {
                                // Calculate actual work hours from check-in/check-out times for more accurate overtime
                                let actualWorkHours = attendance.workHours || 0;
                                
                                // If we have both check-in and check-out times, calculate precise work hours
                                if (attendance.checkInTime && attendance.checkOutTime && 
                                    attendance.checkInTime !== '-' && attendance.checkOutTime !== '-') {
                                  try {
                                    const [checkInHour, checkInMin] = attendance.checkInTime.split(':').map(Number);
                                    const [checkOutHour, checkOutMin] = attendance.checkOutTime.split(':').map(Number);
                                    
                                    const today = new Date();
                                    const checkIn = new Date(today.getFullYear(), today.getMonth(), today.getDate(), checkInHour, checkInMin);
                                    let checkOut = new Date(today.getFullYear(), today.getMonth(), today.getDate(), checkOutHour, checkOutMin);
                                    
                                    // If check-out time is earlier than check-in time, assume it's next day
                                    if (checkOut < checkIn) {
                                      checkOut.setDate(checkOut.getDate() + 1);
                                    }
                                    
                                    // Calculate the difference in hours (with decimal precision)
                                    const diffMs = checkOut.getTime() - checkIn.getTime();
                                    actualWorkHours = diffMs / (1000 * 60 * 60); // Convert to hours
                                  } catch (error) {
                                    // Fall back to stored value if calculation fails
                                    actualWorkHours = attendance.workHours || 0;
                                  }
                                }
                                
                                const overtimeFromWorkHours = actualWorkHours > 8 ? actualWorkHours - 8 : 0;
                                const recordedOvertime = attendance.overtime || 0;
                                const actualOvertime = Math.max(overtimeFromWorkHours, recordedOvertime);
                                
                                if (actualOvertime === 0) return '-';
                                
                                // Convert decimal hours to hours and minutes
                                const wholeHours = Math.floor(actualOvertime);
                                const minutes = Math.round((actualOvertime - wholeHours) * 60);
                                
                                const overtimeText = wholeHours > 0 && minutes > 0 
                                  ? `${wholeHours}h ${minutes}m`
                                  : wholeHours > 0 
                                    ? `${wholeHours}h 0m`
                                    : `0h ${minutes}m`;
                                
                                return (
                                  <span className={`${actualOvertime > 0 ? 'text-orange-600 dark:text-orange-400 font-medium' : ''}`}>
                                    {overtimeText}
                                  </span>
                                );
                              })()}
                            </td>
                            <td className="px-3 py-2 whitespace-nowrap text-xs text-gray-500 dark:text-gray-400">
                              {attendance.location || '-'}
                            </td>
                          </tr>
                        ))
                      ) : employeeAttendances.length > 0 ? (
                        <tr>
                          <td colSpan={7} className="px-3 py-3 text-center text-xs text-gray-500 dark:text-gray-400">
                            <div className="flex flex-col items-center py-2">
                              <AlertCircle className="h-6 w-6 text-gray-300 dark:text-gray-600 mb-1" />
                              <p className="text-xs">No records match your current filters</p>
                              <button
                                onClick={resetFilters}
                                className="mt-1 text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 text-xs font-medium"
                              >
                                Clear all filters
                              </button>
                            </div>
                          </td>
                        </tr>
                      ) : (
                        <tr>
                          <td colSpan={7} className="px-3 py-3 text-center text-xs text-gray-500 dark:text-gray-400">
                            No attendance records found
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
            </Tab.Panel>

            {/* Regularization Tab */}
            <Tab.Panel className="p-3">
              <RegularizationManager
                employeeId={employeeId}
                employeeName={employeeName}
                requests={regularizationRequests}
                onSubmit={handleSubmitRegularization}
                isSubmitting={isSubmittingRegularization}
                compact={false}
                mode="both"
                showFormButton={true}
              />
            </Tab.Panel>
          </Tab.Panels>
        </div>
      </Tab.Group>
    </div>
  );
};

export default EmployeeAttendanceView; 