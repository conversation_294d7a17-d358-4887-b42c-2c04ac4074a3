import { <PERSON><PERSON>ty, PrimaryGeneratedC<PERSON>umn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { IsNotEmpty, Length } from 'class-validator';

@Entity('permission_groups')
export class PermissionGroup {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, unique: true })
  @IsNotEmpty({ message: 'Group name is required' })
  @Length(2, 100, { message: 'Group name must be between 2 and 100 characters' })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column('simple-array')
  permissions: string[];

  @Column({ type: 'boolean', default: false })
  isCustom: boolean;

  @Column({ type: 'varchar', nullable: true })
  createdBy: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;
} 