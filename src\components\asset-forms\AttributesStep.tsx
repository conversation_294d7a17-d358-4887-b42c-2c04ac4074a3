import React from 'react';
import { Asset, ComputingAttributes, NetworkingAttributes, SecurityAttributes } from '../../types/asset';
import { Monitor, Cpu, HardDrive, Smartphone, Barcode, Hash, Shield, Moon } from 'lucide-react';

interface AttributesStepProps {
  assetType?: Asset['assetType'];
  attributes: Partial<ComputingAttributes | NetworkingAttributes | SecurityAttributes>;
  onChange: (attributes: Partial<ComputingAttributes | NetworkingAttributes | SecurityAttributes>) => void;
  errors: Record<string, string>;
}

export const AttributesStep: React.FC<AttributesStepProps> = ({
  assetType,
  attributes,
  onChange,
  errors,
}) => {
  const inputClassName = "w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors";
  const labelClassName = "block text-sm font-medium text-gray-700 mb-1";

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const newValue = type === 'checkbox' ? (e.target as HTMLInputElement).checked : value;
    onChange({ ...attributes, [name]: newValue });
  };

  const renderComputingFields = () => (
    <>
      <div>
        <label htmlFor="operatingSystem" className={labelClassName}>
          <Monitor className="inline-block mr-1 h-4 w-4" /> Operating System
        </label>
        <select
          id="operatingSystem"
          name="operatingSystem"
          value={(attributes as ComputingAttributes)?.operatingSystem || ''}
          onChange={handleChange}
          className={inputClassName}
        >
          <option value="">Select OS</option>
          <option value="Windows">Windows</option>
          <option value="macOS">macOS</option>
          <option value="Linux">Linux</option>
        </select>
      </div>

      <div>
        <label htmlFor="processor" className={labelClassName}>
          <Cpu className="inline-block mr-1 h-4 w-4" /> Processor
        </label>
        <input
          type="text"
          id="processor"
          name="processor"
          value={(attributes as ComputingAttributes)?.processor || ''}
          onChange={handleChange}
          className={inputClassName}
        />
      </div>

      <div>
        <label htmlFor="ramCapacity" className={labelClassName}>
          <Cpu className="inline-block mr-1 h-4 w-4" /> RAM Capacity
        </label>
        <select
          id="ramCapacity"
          name="ramCapacity"
          value={(attributes as ComputingAttributes)?.ramCapacity || ''}
          onChange={handleChange}
          className={inputClassName}
        >
          <option value="">Select RAM</option>
          <option value="4GB">4GB</option>
          <option value="8GB">8GB</option>
          <option value="16GB">16GB</option>
          <option value="32GB">32GB</option>
          <option value="64GB">64GB</option>
        </select>
      </div>

      <div>
        <label htmlFor="storageType" className={labelClassName}>
          <HardDrive className="inline-block mr-1 h-4 w-4" /> Storage Type
        </label>
        <select
          id="storageType"
          name="storageType"
          value={(attributes as ComputingAttributes)?.storageType || ''}
          onChange={handleChange}
          className={inputClassName}
        >
          <option value="">Select Storage Type</option>
          <option value="SSD">SSD</option>
          <option value="HDD">HDD</option>
          <option value="eMMC">eMMC</option>
          <option value="UFS">UFS</option>
        </select>
      </div>

      <div>
        <label htmlFor="storageCapacity" className={labelClassName}>
          <HardDrive className="inline-block mr-1 h-4 w-4" /> Storage Capacity
        </label>
        <input
          type="text"
          id="storageCapacity"
          name="storageCapacity"
          value={(attributes as ComputingAttributes)?.storageCapacity || ''}
          onChange={handleChange}
          className={inputClassName}
          placeholder="Enter Storage Capacity (e.g., 128GB)"
        />
      </div>
    </>
  );

  const renderMobileFields = () => (
    <>
      <div>
        <label htmlFor="operatingSystem" className={labelClassName}>
          <Smartphone className="inline-block mr-1 h-4 w-4" /> Operating System
        </label>
        <select
          id="operatingSystem"
          name="operatingSystem"
          value={(attributes as any)?.operatingSystem || ''}
          onChange={handleChange}
          className={inputClassName}
        >
          <option value="">Select OS</option>
          <option value="Android">Android</option>
          <option value="iOS">iOS</option>
          <option value="Other">Other</option>
        </select>
      </div>
      <div>
        <label htmlFor="processor" className={labelClassName}>
          <Cpu className="inline-block mr-1 h-4 w-4" /> Processor
        </label>
        <input
          type="text"
          id="processor"
          name="processor"
          value={(attributes as any)?.processor || ''}
          onChange={handleChange}
          className={inputClassName}
        />
      </div>
      <div>
        <label htmlFor="mobileRam" className={labelClassName}>
          <Cpu className="inline-block mr-1 h-4 w-4" /> RAM
        </label>
        <input
          type="text"
          id="mobileRam"
          name="mobileRam"
          placeholder="Enter RAM (e.g., 4GB)"
          value={(attributes as any)?.mobileRam || ''}
          onChange={handleChange}
          className={inputClassName}
        />
      </div>
      <div>
        <label htmlFor="mobileStorage" className={labelClassName}>
          <HardDrive className="inline-block mr-1 h-4 w-4" /> Storage
        </label>
        <input
          type="text"
          id="mobileStorage"
          name="mobileStorage"
          placeholder="Enter Storage (e.g., 64GB)"
          value={(attributes as any)?.mobileStorage || ''}
          onChange={handleChange}
          className={inputClassName}
        />
      </div>
      <div>
        <label htmlFor="imei" className={labelClassName}>
          <Barcode className="inline-block mr-1 h-4 w-4" /> IMEI
        </label>
        <input
          type="text"
          id="imei"
          name="imei"
          placeholder="Enter IMEI"
          value={(attributes as any)?.imei || ''}
          onChange={handleChange}
          className={inputClassName}
        />
      </div>
    </>
  );

  const renderNetworkingFields = () => (
    <>
      <div>
        <label htmlFor="macAddress" className={labelClassName}>
          <Hash className="inline-block mr-1 h-4 w-4" /> MAC Address
        </label>
        <input
          type="text"
          id="macAddress"
          name="macAddress"
          value={(attributes as NetworkingAttributes)?.macAddress || ''}
          onChange={handleChange}
          className={inputClassName}
        />
      </div>

      <div>
        <label htmlFor="firewallRules" className={labelClassName}>
          <Shield className="inline-block mr-1 h-4 w-4" /> Firewall Rules
        </label>
        <input
          type="checkbox"
          id="firewallRules"
          name="firewallRules"
          checked={(attributes as NetworkingAttributes)?.firewallRules || false}
          onChange={handleChange}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
      </div>
    </>
  );

  const renderSecurityFields = () => (
    <>
      <div>
        <label htmlFor="resolution" className={labelClassName}>
          <Monitor className="inline-block mr-1 h-4 w-4" /> Resolution
        </label>
        <select
          id="resolution"
          name="resolution"
          value={(attributes as SecurityAttributes)?.resolution || ''}
          onChange={handleChange}
          className={inputClassName}
        >
          <option value="">Select Resolution</option>
          <option value="720p">720p</option>
          <option value="1080p">1080p</option>
          <option value="4K">4K</option>
        </select>
      </div>

      <div>
        <label htmlFor="nightVision" className={labelClassName}>
          <Moon className="inline-block mr-1 h-4 w-4" /> Night Vision
        </label>
        <input
          type="checkbox"
          id="nightVision"
          name="nightVision"
          checked={(attributes as SecurityAttributes)?.nightVision || false}
          onChange={handleChange}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
      </div>
    </>
  );

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {assetType === 'Computing' && renderComputingFields()}
      {(assetType === 'Mobile Device' || assetType === 'Tablet') && renderMobileFields()}
      {assetType === 'Networking' && renderNetworkingFields()}
      {assetType === 'Security' && renderSecurityFields()}
      
      {!assetType && (
        <p className="text-gray-500 col-span-2 text-center">
          Please select an asset type first
        </p>
      )}
    </div>
  );
}; 