import React from 'react';
import { DollarSign, Server, Laptop, Cloud } from 'lucide-react';

interface MetricsCardProps {
  title: string;
  value: number;
  unit: string;
  icon: React.ElementType;
  change: number;
}

interface CostBreakdownItem {
  cost: number;
  budget: number;
}

const formatNumber = (value: number, unit: string) => {
  if (unit === '%') return `${value}%`;
  if (unit === 'GB') return `${value} GB`;
  if (unit === 'PKR') return `₨${Math.round(value).toLocaleString()}`;
  return value.toLocaleString();
};

const renderMetricsCard = (title: string, value: number, unit: string, Icon: React.ElementType, change: number) => {
  return (
    <div className="bg-white rounded-lg shadow p-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">{title}</h3>
        <Icon className="h-6 w-6" />
      </div>
      <p className="text-2xl font-bold mt-2">{formatNumber(value, unit)}</p>
      <p className={`text-sm ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
        {change >= 0 ? '+' : ''}{change}% from last month
      </p>
    </div>
  );
};

export function ReportsAnalytics() {
  const costBreakdownItems: CostBreakdownItem[] = [
    { cost: 45250, budget: 50000 },
    { cost: 28500, budget: 30000 },
    { cost: 12750, budget: 15000 },
    { cost: 4000, budget: 5000 }
  ];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {renderMetricsCard('Total Expenses', 45250, 'PKR', DollarSign, 8)}
        {renderMetricsCard('Infrastructure', 28500, 'PKR', Server, 5)}
        {renderMetricsCard('Software', 12750, 'PKR', Laptop, 12)}
        {renderMetricsCard('Services', 4000, 'PKR', Cloud, -3)}
      </div>

      <div className="bg-white rounded-lg shadow">
        <h3 className="text-lg font-medium p-4 border-b">Cost Breakdown</h3>
        <div className="divide-y">
          {costBreakdownItems.map((item, index) => (
            <div key={index} className="p-4">
              <div className="text-right">
                <p className="font-medium text-gray-900">₨{Math.round(item.cost).toLocaleString()}</p>
                <p className="text-sm text-gray-500">Budget: ₨{Math.round(item.budget).toLocaleString()}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}