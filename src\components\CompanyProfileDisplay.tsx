import React, { useEffect, useState } from 'react';
import { getCompanyProfile } from '../services/companyService';
import { toast } from 'react-hot-toast';

interface CompanyProfile {
  companyName: string;
  companyLogo: string | null;
  companyEmail: string;
  companyPhone: string;
  companyAddress: string;
  mainLocationName: string;
  subLocations: string[];
  departments: string[];
  projects: string[];
  adminUser: {
    fullName: string;
    email: string;
  };
  advancedSettings: {
    fiscalStartMonth: string;
    timezone: string;
    currency: string;
    defaultLanguage: string;
  };
}

const CompanyProfileDisplay: React.FC = () => {
  const [companyData, setCompanyData] = useState<CompanyProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCompanyData = async () => {
      try {
        setLoading(true);
        const data = await getCompanyProfile();
        setCompanyData(data);
        setError(null);
      } catch (err) {
        console.error('Failed to fetch company data:', err);
        setError('Failed to load company profile. Please try again later.');
        toast.error('Could not load company profile');
      } finally {
        setLoading(false);
      }
    };

    fetchCompanyData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error || !companyData) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
        {error || 'No company profile data found.'}
      </div>
    );
  }

  return (
    <div className="bg-white shadow-md rounded-lg overflow-hidden">
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-6">
        <div className="flex items-center">
          {companyData.companyLogo && (
            <div className="mr-6">
              <img 
                src={companyData.companyLogo} 
                alt={`${companyData.companyName} logo`} 
                className="h-20 w-20 object-contain bg-white rounded-md p-1"
              />
            </div>
          )}
          <div>
            <h1 className="text-3xl font-bold">{companyData.companyName}</h1>
            <p className="mt-1">{companyData.companyEmail}</p>
            {companyData.companyPhone && <p>{companyData.companyPhone}</p>}
          </div>
        </div>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-3">Organization</h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Main Location</h3>
                <p className="mt-1">{companyData.mainLocationName}</p>
              </div>

              {companyData.companyAddress && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Address</h3>
                  <p className="mt-1">{companyData.companyAddress}</p>
                </div>
              )}

              {companyData.subLocations.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Additional Locations</h3>
                  <ul className="mt-1 list-disc list-inside">
                    {companyData.subLocations.map((location, index) => (
                      <li key={index}>{location}</li>
                    ))}
                  </ul>
                </div>
              )}

              <div>
                <h3 className="text-sm font-medium text-gray-500">Departments</h3>
                <div className="mt-1 flex flex-wrap gap-2">
                  {companyData.departments.map((dept, index) => (
                    <span key={index} className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                      {dept}
                    </span>
                  ))}
                </div>
              </div>

              {companyData.projects.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Projects</h3>
                  <div className="mt-1 flex flex-wrap gap-2">
                    {companyData.projects.map((project, index) => (
                      <span key={index} className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                        {project}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-3">Settings</h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Administrator</h3>
                <p className="mt-1">{companyData.adminUser.fullName} ({companyData.adminUser.email})</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Fiscal Start Month</h3>
                  <p className="mt-1">{companyData.advancedSettings.fiscalStartMonth}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Timezone</h3>
                  <p className="mt-1">{companyData.advancedSettings.timezone}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Currency</h3>
                  <p className="mt-1">{companyData.advancedSettings.currency}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Language</h3>
                  <p className="mt-1">{companyData.advancedSettings.defaultLanguage}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanyProfileDisplay; 