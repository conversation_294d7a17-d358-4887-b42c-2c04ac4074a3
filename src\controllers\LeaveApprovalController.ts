import { Request, Response } from 'express';
import { LeaveApprovalService } from '../services/LeaveApprovalService';
import { ApprovalLevel } from '../entities/LeaveApproval';
import { LeaveStatus } from '../types/attendance';
import { AppDataSource } from '../config/database';

export class LeaveApprovalController {
  private leaveApprovalService: LeaveApprovalService;

  constructor() {
    this.leaveApprovalService = new LeaveApprovalService(AppDataSource);
  }

  /**
   * Get leave request with approval timeline
   */
  async getLeaveRequestWithApprovals(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const leaveRequest = await this.leaveApprovalService.getLeaveRequestWithApprovals(parseInt(id));
      
      if (!leaveRequest) {
        res.status(404).json({ 
          success: false,
          error: 'Leave request not found' 
        });
        return;
      }

      // Get approval history to determine current status
      const approvals = await this.leaveApprovalService.getApprovalHistory(parseInt(id));
      
      // Only update leave request status if it's not in a final state
      const finalStates = [LeaveStatus.CANCELLED, LeaveStatus.APPROVED, LeaveStatus.REJECTED];
      if (!finalStates.includes(leaveRequest.status)) {
        await this.leaveApprovalService.updateLeaveRequestStatus(parseInt(id));
      }
      
      // Fetch the updated leave request
      const updatedLeaveRequest = await this.leaveApprovalService.getLeaveRequestWithApprovals(parseInt(id));

      // Transform the data to match frontend expectations
      const transformedApprovals = approvals.map(approval => ({
        id: approval.id,
        level: approval.level,
        approverId: approval.approverId,
        approverName: approval.approverName,
        status: approval.status,
        comments: approval.comments,
        decisionAt: approval.decisionAt,
        sequence: approval.sequence
      }));

      res.json({
        success: true,
        data: {
          leaveRequest: updatedLeaveRequest,
          approvals: transformedApprovals
        }
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      res.status(500).json({ 
        success: false,
        error: 'Failed to fetch leave request',
        details: errorMessage 
      });
    }
  }

  /**
   * Submit a new leave request (creates approval workflow)
   */
  async submitLeaveRequest(req: Request, res: Response): Promise<void> {
    try {
      const { employeeId, leaveType, startDate, endDate, reason } = req.body;
      
      // First, create the leave request (your existing logic)
      const leaveRequest = await this.leaveApprovalService.leaveRequestRepository.save({
        employeeId,
        leaveType,
        startDate,
        endDate,
        reason,
        status: LeaveStatus.PENDING,
        daysRequested: Math.floor((new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 60 * 60 * 24)) + 1,
        source: 'EMPLOYEE'
      });

      // Initialize the approval workflow
      await this.leaveApprovalService.initializeApprovalWorkflow(leaveRequest.id);

      res.json({
        success: true,
        message: 'Leave request submitted successfully',
        data: { id: leaveRequest.id }
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      res.status(500).json({ 
        error: 'Failed to submit leave request',
        details: errorMessage 
      });
    }
  }

  /**
   * Manager approves/rejects leave request
   */
  async managerApproval(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { decision, comments } = req.body;
      const { user } = req as any; // fallback if not typed

      if (!user || !user.id || !user.name) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      // Check if manager can approve at this level
      const canApprove = await this.leaveApprovalService.canApproveAtLevel(
        parseInt(id), 
        ApprovalLevel.MANAGER
      );

      if (!canApprove) {
        res.status(403).json({ error: 'Cannot approve at this level' });
        return;
      }

      await this.leaveApprovalService.processApproval(
        parseInt(id),
        ApprovalLevel.MANAGER,
        user.id,
        user.name,
        decision,
        comments
      );

      // Fetch updated leave request and approval history
      const leaveRequest = await this.leaveApprovalService.getLeaveRequestWithApprovals(parseInt(id));
      const approvals = await this.leaveApprovalService.getApprovalHistory(parseInt(id));

      res.json({
        success: true,
        message: `Leave request ${decision}d by manager`,
        data: {
          leaveRequest,
          approvals
        }
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      res.status(500).json({ 
        error: 'Failed to process approval',
        details: errorMessage 
      });
    }
  }

  /**
   * HR approves/rejects leave request
   */
  async hrApproval(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { decision, comments } = req.body;
      const { user } = req as any;

      if (!user || !user.id || !user.name) {
        res.status(401).json({ error: 'User not authenticated' });
        return;
      }

      // Check if HR can approve at this level
      const canApprove = await this.leaveApprovalService.canApproveAtLevel(
        parseInt(id), 
        ApprovalLevel.HR
      );

      if (!canApprove) {
        res.status(403).json({ error: 'Cannot approve at this level' });
        return;
      }

      await this.leaveApprovalService.processApproval(
        parseInt(id),
        ApprovalLevel.HR,
        user.id,
        user.name,
        decision,
        comments
      );

      // Fetch updated leave request and approval history
      const leaveRequest = await this.leaveApprovalService.getLeaveRequestWithApprovals(parseInt(id));
      const approvals = await this.leaveApprovalService.getApprovalHistory(parseInt(id));

      res.json({
        success: true,
        message: `Leave request ${decision}d by HR`,
        data: {
          leaveRequest,
          approvals
        }
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      res.status(500).json({ 
        error: 'Failed to process approval',
        details: errorMessage 
      });
    }
  }

  /**
   * Get pending approvals for manager
   */
  async getPendingManagerApprovals(req: Request, res: Response): Promise<void> {
    try {
      const pendingApprovals = await this.leaveApprovalService.getPendingApprovalsForLevel(
        ApprovalLevel.MANAGER
      );

      res.json({
        success: true,
        data: pendingApprovals
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      res.status(500).json({ 
        error: 'Failed to fetch pending approvals',
        details: errorMessage 
      });
    }
  }

  /**
   * Get pending approvals for HR
   */
  async getPendingHRApprovals(req: Request, res: Response): Promise<void> {
    try {
      const pendingApprovals = await this.leaveApprovalService.getPendingApprovalsForLevel(
        ApprovalLevel.HR
      );

      res.json({
        success: true,
        data: pendingApprovals
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      res.status(500).json({ 
        error: 'Failed to fetch pending approvals',
        details: errorMessage 
      });
    }
  }

  /**
   * Get approval history for a leave request
   */
  async getApprovalHistory(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const history = await this.leaveApprovalService.getApprovalHistory(parseInt(id));

      res.json({
        success: true,
        data: history
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      res.status(500).json({ 
        error: 'Failed to fetch approval history',
        details: errorMessage 
      });
    }
  }

  /**
   * Get approval statistics (for dashboard)
   */
  async getApprovalStatistics(req: Request, res: Response): Promise<void> {
    try {
      const statistics = await this.leaveApprovalService.getApprovalStatistics();

      res.json({
        success: true,
        data: statistics
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      res.status(500).json({ 
        error: 'Failed to fetch approval statistics',
        details: errorMessage 
      });
    }
  }
} 