import { Request, Response } from 'express';
import { Vendor } from '../../entities/Vendor';
import { AppDataSource } from '../../config/database';

// Get all vendors with optional filtering
export const getVendors = async (req: Request, res: Response) => {
  try {
    const vendorRepository = AppDataSource.getRepository(Vendor);
    const { status, vendorType, search } = req.query;
    
    let queryBuilder = vendorRepository.createQueryBuilder('vendor');
    
    // Apply filters if provided
    if (status && status !== 'all') {
      queryBuilder = queryBuilder.andWhere('vendor.status = :status', { status });
    }
    
    if (vendorType && vendorType !== 'all') {
      queryBuilder = queryBuilder.andWhere('vendor.vendorType = :vendorType', { vendorType });
    }
    
    if (search) {
      // Use MySQL LIKE for text search, not ILIKE (PostgreSQL)
      queryBuilder = queryBuilder.andWhere(
        '(vendor.vendorName LIKE :search OR vendor.companyName LIKE :search)',
        { search: `%${search}%` }
      );
    }
    
    const vendors = await queryBuilder.getMany();
    
    res.status(200).json(vendors);
  } catch (error: any) {
    console.error('Error fetching vendors:', error);
    res.status(500).json({ 
      message: 'Failed to fetch vendors', 
      error: error.message || 'Unknown error' 
    });
  }
};

// Get a single vendor by ID
export const getVendorById = async (req: Request, res: Response) => {
  try {
    const vendorRepository = AppDataSource.getRepository(Vendor);
    const { id } = req.params;
    
    const vendor = await vendorRepository.findOne({ where: { id } });
    
    if (!vendor) {
      return res.status(404).json({ message: 'Vendor not found' });
    }
    
    res.status(200).json(vendor);
  } catch (error: any) {
    console.error('Error fetching vendor:', error);
    res.status(500).json({ 
      message: 'Failed to fetch vendor', 
      error: error.message || 'Unknown error'
    });
  }
};

// Create a new vendor
export const createVendor = async (req: Request, res: Response) => {
  try {
    const vendorRepository = AppDataSource.getRepository(Vendor);
    const vendorData = req.body;
    
    // Create and save the new vendor
    const newVendor = vendorRepository.create({
      ...vendorData,
      // Set default values for required nested objects
      contract: vendorData.contract || {
        startDate: new Date().toISOString().split('T')[0],
        expiryDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0],
        terminationNotice: 30
      },
      performance: vendorData.performance || {
        rating: 0,
        responseTime: 0,
        deliveryScore: 0,
        qualityScore: 0
      },
      financial: vendorData.financial || {
        spending: {
          current: {
            value: 0,
            currency: 'PKR'
          },
          trend: 0
        }
      }
    });
    
    await vendorRepository.save(newVendor);
    
    res.status(201).json(newVendor);
  } catch (error: any) {
    console.error('Error creating vendor:', error);
    res.status(500).json({ 
      message: 'Failed to create vendor', 
      error: error.message || 'Unknown error'
    });
  }
};

// Update an existing vendor
export const updateVendor = async (req: Request, res: Response) => {
  try {
    const vendorRepository = AppDataSource.getRepository(Vendor);
    const { id } = req.params;
    const vendorData = req.body;
    
    // Find the vendor to update
    const vendor = await vendorRepository.findOne({ where: { id } });
    
    if (!vendor) {
      return res.status(404).json({ message: 'Vendor not found' });
    }
    
    // Update the vendor
    vendorRepository.merge(vendor, vendorData);
    const updatedVendor = await vendorRepository.save(vendor);
    
    res.status(200).json(updatedVendor);
  } catch (error: any) {
    console.error('Error updating vendor:', error);
    res.status(500).json({ 
      message: 'Failed to update vendor', 
      error: error.message || 'Unknown error'
    });
  }
};

// Delete a vendor
export const deleteVendor = async (req: Request, res: Response) => {
  try {
    const vendorRepository = AppDataSource.getRepository(Vendor);
    const { id } = req.params;
    
    // Find the vendor to delete
    const vendor = await vendorRepository.findOne({ where: { id } });
    
    if (!vendor) {
      return res.status(404).json({ message: 'Vendor not found' });
    }
    
    // Delete the vendor
    await vendorRepository.remove(vendor);
    
    res.status(204).send();
  } catch (error: any) {
    console.error('Error deleting vendor:', error);
    res.status(500).json({ 
      message: 'Failed to delete vendor', 
      error: error.message || 'Unknown error'
    });
  }
}; 