import { Request, Response } from 'express';
import { AppDataSource } from '../../config/database';
import { User } from '../../entities/User';
import { UserRole, UserPermissions } from '../../types/common';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { validateEmail } from '../utils/validation';
import { SystemLogService } from '../services/SystemLogService';
import { LogType } from '../../entities/SystemLog';

export interface AuthRequest extends Request {
  user?: User;
}

const userRepository = AppDataSource.getRepository(User);
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const COOKIE_NAME = 'auth_token';

// Helper function for logging authentication events
const logAuthEvent = async (type: LogType, action: string, user: string, details: string, ip: string) => {
  try {
    await SystemLogService.createLog({
      type,
      action,
      user,
      details: `${details} (IP: ${ip})`
    });
  } catch (error) {
    console.error('Failed to log auth event:', error);
  }
};

// Register User
export const registerUser = async (req: Request, res: Response) => {
  try {
    const { name, email, password, role, department } = req.body;

    if (!validateEmail(email)) {
      return res.status(400).json({ message: 'Invalid email format' });
    }

    const existingUser = await userRepository.findOne({ where: { email } });
    if (existingUser) {
      return res.status(400).json({ message: 'Email already registered' });
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    const permissions: UserPermissions = {
      canCreateTickets: true,
      canCreateTicketsForOthers: role === UserRole.IT_ADMIN || role === UserRole.IT_STAFF,
      canEditTickets: role === UserRole.IT_ADMIN || role === UserRole.IT_STAFF,
      canDeleteTickets: role === UserRole.IT_ADMIN,
      canCloseTickets: role === UserRole.IT_ADMIN || role === UserRole.IT_STAFF,
      canLockTickets: role === UserRole.IT_ADMIN || role === UserRole.IT_STAFF,
      canAssignTickets: role === UserRole.IT_ADMIN || role === UserRole.IT_STAFF,
      canEscalateTickets: role === UserRole.IT_ADMIN || role === UserRole.IT_STAFF,
      canViewAllTickets: role === UserRole.IT_ADMIN || role === UserRole.IT_STAFF,
      canCreateEmployee: role === UserRole.IT_ADMIN,
      canEditEmployee: role === UserRole.IT_ADMIN,
      canDeleteEmployee: role === UserRole.IT_ADMIN,
      canViewEmployees: role === UserRole.IT_ADMIN,
      canManageAttendance: role === UserRole.IT_ADMIN,
      canManageLeave: role === UserRole.IT_ADMIN,
      canManagePayroll: role === UserRole.IT_ADMIN,
      canManagePerformance: role === UserRole.IT_ADMIN,
      department,
      isAdmin: role === UserRole.IT_ADMIN
    };

    const user = userRepository.create({
      name,
      email,
      password: hashedPassword,
      role,
      department,
      isActive: true,
      permissions
    });

    const savedUser = await userRepository.save(user);
    const { password: _, ...userWithoutPassword } = savedUser;
    const token = jwt.sign({ userId: user.id }, JWT_SECRET, { expiresIn: '24h' });

    res.status(201).json({ user: userWithoutPassword, token });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: 'Error creating user' });
  }
};

// Login User
export const login = async (req: Request, res: Response) => {
  try {
    console.log('🔐 Login attempt started');
    const { email, password } = req.body;
    
    console.log('📧 Login email:', email);
    console.log('🔍 Attempting to find user in database...');
    
    // Find user by email
    const user = await userRepository.findOne({ 
      where: { email },
      select: ['id', 'email', 'name', 'password', 'role', 'department', 'permissions', 'isActive'] 
    });
    
    console.log('👤 User lookup result:', user ? `Found user: ${user.name} (${user.email})` : 'User not found');
    
    if (!user) {
      console.log('❌ Login failed: User not found');
      return res.status(401).json({ message: 'Invalid credentials' });
    }
    
    console.log('✅ User found, checking if active...');
    if (!user.isActive) {
      console.log('❌ Login failed: User account is inactive');
      return res.status(403).json({ message: 'Account is inactive' });
    }
    
    console.log('✅ User is active, validating password...');
    // Validate password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    console.log('🔑 Password validation result:', isPasswordValid ? 'Valid' : 'Invalid');
    
    if (!isPasswordValid) {
      console.log('❌ Login failed: Invalid password');
      return res.status(401).json({ message: 'Invalid credentials' });
    }
    
    console.log('✅ Password valid, checking for admin user...');
    // Special case for the Administrator user - just assign IT_ADMIN role
    if (user.name === 'Administrator' || user.email === '<EMAIL>') {
      console.log('👑 Admin user detected, updating role to IT_ADMIN...');
      user.role = 'IT_ADMIN';
      // Save the updated user role to the database
      await userRepository.save(user);
      console.log('✅ Admin role updated successfully');
    }
    
    console.log('🎫 Generating JWT token...');
    // Create JWT token
    const token = jwt.sign(
      { userId: user.id, email: user.email, role: user.role },
      JWT_SECRET,
      { expiresIn: '24h' }
    );
    console.log('✅ JWT token generated successfully');
    
    // Exclude password from response
    const { password: _, ...userWithoutPassword } = user;
    
    console.log('🍪 Setting auth cookie...');
    // Set cookie with token (if using cookies)
    res.cookie('auth_token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    });
    
    // Log successful login
    console.log(`✅ Login successful: ${user.email} (${user.role})`);
    
    // Return token and user data
    res.json({ token, user: userWithoutPassword });
  } catch (error: any) {
    console.error('❌ Login error occurred:');
    console.error('Error type:', error.constructor?.name || 'Unknown');
    console.error('Error message:', error.message || 'No message');
    console.error('Error stack:', error.stack || 'No stack trace');
    
    // Log additional details based on error type
    if (error.code) {
      console.error('Error code:', error.code);
    }
    
    if (error.sqlMessage) {
      console.error('SQL error:', error.sqlMessage);
    }
    
    if (error.errno) {
      console.error('Error number:', error.errno);
    }
    
    res.status(500).json({ 
      message: 'Error during login process',
      // In development, include more details
      ...(process.env.NODE_ENV !== 'production' && {
        error: error.message || 'Unknown error',
        type: error.constructor?.name || 'Unknown'
      })
    });
  }
};

// Update User Role
export const updateUserRole = async (req: Request, res: Response) => {
  try {
    const { userId, newRole } = req.body;

    // Validate the new role
    const validRoles = Object.values(UserRole);
    if (!validRoles.includes(newRole)) {
      return res.status(400).json({ error: 'Invalid role' });
    }

    // Find user
    const user = await userRepository.findOne({ where: { id: userId } });
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Update permissions based on new role
    const permissions: UserPermissions = {
      canCreateTickets: true,
      canCreateTicketsForOthers: ['IT_ADMIN', 'IT_STAFF', 'HR_ADMIN'].includes(newRole),
      canEditTickets: ['IT_ADMIN', 'IT_STAFF', 'HR_ADMIN'].includes(newRole),
      canDeleteTickets: newRole === UserRole.IT_ADMIN,
      canCloseTickets: ['IT_ADMIN', 'IT_STAFF'].includes(newRole),
      canLockTickets: ['IT_ADMIN', 'IT_STAFF'].includes(newRole),
      canAssignTickets: ['IT_ADMIN', 'IT_STAFF'].includes(newRole),
      canEscalateTickets: ['IT_ADMIN', 'IT_STAFF'].includes(newRole),
      canViewAllTickets: ['IT_ADMIN', 'IT_STAFF', 'HR_ADMIN'].includes(newRole),
      
      // HR permissions
      canCreateEmployee: ['IT_ADMIN', 'HR_ADMIN', 'HR_STAFF'].includes(newRole),
      canEditEmployee: ['IT_ADMIN', 'HR_ADMIN', 'HR_STAFF'].includes(newRole),
      canDeleteEmployee: ['IT_ADMIN', 'HR_ADMIN'].includes(newRole),
      canViewEmployees: ['IT_ADMIN', 'IT_STAFF', 'HR_ADMIN', 'HR_STAFF', 'DEPT_HEAD', 'FINANCE_MANAGER'].includes(newRole),
      canManageAttendance: ['IT_ADMIN', 'HR_ADMIN', 'HR_STAFF'].includes(newRole),
      canManageLeave: ['IT_ADMIN', 'HR_ADMIN', 'HR_STAFF', 'DEPT_HEAD'].includes(newRole),
      canManagePayroll: ['IT_ADMIN', 'HR_ADMIN', 'FINANCE_MANAGER'].includes(newRole),
      canManagePerformance: ['IT_ADMIN', 'HR_ADMIN'].includes(newRole),
      
      department: user.department,
      isAdmin: newRole === UserRole.IT_ADMIN || newRole === UserRole.HR_ADMIN
    };

    // Update user
    user.role = newRole as UserRole;
    user.permissions = permissions;
    await userRepository.save(user);

    res.json({ message: 'User role updated successfully', user });
  } catch (error) {
    console.error('Update Role Error:', error);
    res.status(500).json({ error: 'Failed to update user role' });
  }
};

// Get Current User
export const getCurrentUser = async (req: Request, res: Response) => {
  try {
    const user = req.user;
    
    if (!user) {
      return res.status(401).json({ message: 'Not authenticated' });
    }
    
    // Special case for Administrator - ensure IT_ADMIN role
    if (user.name === 'Administrator' || user.email === '<EMAIL>') {
      user.role = 'IT_ADMIN';
      // Update role in database
      await userRepository.update({ id: user.id }, {
        role: 'IT_ADMIN'
      });
    }
    
    res.json(user);
  } catch (error) {
    console.error('Get current user error:', error);
    res.status(500).json({ message: 'Error retrieving user data' });
  }
};

// Create Test User function
export const createTestUser = async (req: Request, res: Response) => {
  try {
    // Delete existing user if exists
    await userRepository.delete({ email: '<EMAIL>' });

    // Hash password
    const hashedPassword = await bcrypt.hash('password123', 10);

    // Create default admin permissions
    const permissions = {
      canCreateTickets: true,
      canCreateTicketsForOthers: true,
      canEditTickets: true,
      canDeleteTickets: true,
      canCloseTickets: true,
      canLockTickets: true,
      canAssignTickets: true,
      canEscalateTickets: true,
      canViewAllTickets: true
    };

    // Create new test user
    const user = userRepository.create({
      name: 'Test Admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: UserRole.IT_ADMIN,
      department: 'IT',
      isActive: true,
      permissions
    });

    await userRepository.save(user);

    const { password: _, ...userWithoutPassword } = user;
    res.status(201).json({ message: 'Test user created successfully', user: userWithoutPassword });
  } catch (error) {
    console.error('Create test user error:', error);
    res.status(500).json({ error: 'Failed to create test user' });
  }
};

export const changePassword = async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user?.id; // From auth middleware
    const { currentPassword, newPassword } = req.body;

    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const user = await userRepository.findOne({ where: { id: userId } });
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const validPassword = await bcrypt.compare(currentPassword, user.password);
    if (!validPassword) {
      return res.status(401).json({ 
        code: 'INVALID_CURRENT_PASSWORD',
        message: 'Current password is incorrect' 
      });
    }

    if (newPassword.length < 8) {
      return res.status(400).json({ 
        code: 'INVALID_PASSWORD',
        message: 'New password must be at least 8 characters long' 
      });
    }

    const hashedPassword = await bcrypt.hash(newPassword, 10);
    user.password = hashedPassword;
    await userRepository.save(user);

    res.json({ message: 'Password updated successfully' });
  } catch (error) {
    console.error('Change password error:', error);
    res.status(500).json({ 
      code: 'SERVER_ERROR',
      message: 'An error occurred while changing your password. Please try again later.' 
    });
  }
};

export const authController = {
  registerUser,
  login,
  updateUserRole,
  getCurrentUser,
  createTestUser,
  changePassword
};