import { AppDataSource } from '../config/database';
import { LeaveBalance } from '../entities/LeaveBalance';
import { LeaveTypePolicy } from '../entities/LeaveTypePolicy';
import { Employee } from '../server/entities/Employee';

async function allocateLeaveBalances() {
  try {
    console.log('🚀 Starting leave balance allocation...');
    
    await AppDataSource.initialize();
    console.log('✅ Database connected');
    
    const leaveBalanceRepository = AppDataSource.getRepository(LeaveBalance);
    const leaveTypePolicyRepository = AppDataSource.getRepository(LeaveTypePolicy);
    const employeeRepository = AppDataSource.getRepository(Employee);
    
    // Get all active employees
    const employees = await employeeRepository.find({
      where: { status: 'active' }
    });
    console.log(`📊 Found ${employees.length} active employees`);
    
    // Get all active leave type policies
    const leaveTypePolicies = await leaveTypePolicyRepository.find({
      where: { isActive: true }
    });
    console.log(`📋 Found ${leaveTypePolicies.length} active leave type policies`);
    
    const currentYear = new Date().getFullYear();
    let allocated = 0;
    let updated = 0;
    
    for (const employee of employees) {
      console.log(`\n👤 Processing employee: ${employee.firstName} ${employee.lastName} (ID: ${employee.id})`);
      
      for (const policy of leaveTypePolicies) {
        // Check if balance already exists for this employee and leave type
        const existingBalance = await leaveBalanceRepository.findOne({
          where: {
            employeeId: employee.id,
            leaveType: policy.leaveType,
            year: currentYear,
            isActive: true
          }
        });
        
        if (existingBalance) {
          console.log(`  📝 Updating ${policy.leaveType}: ${existingBalance.totalAllocated} → ${policy.maxDaysPerYear} days`);
          existingBalance.totalAllocated = policy.maxDaysPerYear;
          existingBalance.notes = `Auto-allocated based on ${policy.displayName} policy`;
          await leaveBalanceRepository.save(existingBalance);
          updated++;
        } else {
          console.log(`  ✨ Creating ${policy.leaveType}: ${policy.maxDaysPerYear} days`);
          const newBalance = leaveBalanceRepository.create({
            employeeId: employee.id,
            leaveType: policy.leaveType,
            year: currentYear,
            totalAllocated: policy.maxDaysPerYear,
            used: 0,
            pending: 0,
            carriedForward: 0,
            lapsed: 0,
            notes: `Auto-allocated based on ${policy.displayName} policy`,
            isActive: true
          });
          await leaveBalanceRepository.save(newBalance);
          allocated++;
        }
      }
    }
    
    console.log(`\n🎉 Leave balance allocation completed!`);
    console.log(`📊 Summary:`);
    console.log(`   - Employees processed: ${employees.length}`);
    console.log(`   - Leave types: ${leaveTypePolicies.length}`);
    console.log(`   - New balances created: ${allocated}`);
    console.log(`   - Existing balances updated: ${updated}`);
    console.log(`   - Total operations: ${allocated + updated}`);
    
  } catch (error) {
    console.error('❌ Error allocating leave balances:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
    }
  }
}

// Run the allocation if this script is executed directly
if (require.main === module) {
  allocateLeaveBalances();
}

export { allocateLeaveBalances }; 