import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../contexts/AuthContext';
import api from '../services/api';

interface UserPermissions {
  [key: string]: boolean;
}

interface PermissionHookReturn {
  userPermissions: UserPermissions;
  loading: boolean;
  error: string | null;
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
  refreshPermissions: () => Promise<void>;
  checkPermission: (permission: string, resource?: any) => Promise<boolean>;
}

/**
 * Hook for managing user permissions in frontend components
 * Replaces hardcoded role checks with dynamic permission evaluation
 */
export const usePermissions = (): PermissionHookReturn => {
  const { user } = useAuth();
  const [userPermissions, setUserPermissions] = useState<UserPermissions>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Standard permissions that replace common role checks
  const standardPermissions = [
    // System permissions (replaces IT_ADMIN, SYSTEM_ADMIN)
    'system.admin',
    'system.configure',
    'system.backup',
    
    // User management permissions
    'users.create',
    'users.read', 
    'users.update',
    'users.delete',
    
    // Ticket permissions (replaces IT_STAFF)
    'tickets.create',
    'tickets.read',
    'tickets.update',
    'tickets.delete',
    'tickets.assign',
    'tickets.manage',
    
    // Employee permissions (replaces HR_ADMIN, HR_STAFF)
    'employees.admin',
    'employees.create',
    'employees.read',
    'employees.update',
    'employees.delete',
    'employees.manage',
    
    // Department permissions (replaces DEPT_HEAD)
    'department.manage',
    'department.view',
    
    // Dashboard permissions
    'dashboard.access',
    'dashboard.admin',
    
    // Reports permissions
    'reports.view',
    'reports.create',
    'reports.export'
  ];

  /**
   * Fetch user permissions from the server
   */
  const fetchPermissions = useCallback(async () => {
    if (!user?.id) {
      setUserPermissions({});
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Try dynamic permission checking first
      const response = await api.post('/roles/check-permissions', {
        userId: user.id,
        actions: standardPermissions
      });

      if (response.data?.success && response.data?.permissions) {
        setUserPermissions(response.data.permissions);
      } else {
        // Fallback to role-based permissions
        const fallbackPermissions = getFallbackPermissions(user.role);
        setUserPermissions(fallbackPermissions);
      }
    } catch (error) {
      console.warn('Dynamic permissions not available, using fallback:', error);
      
      // Fallback to role-based permissions
      const fallbackPermissions = getFallbackPermissions(user.role);
      setUserPermissions(fallbackPermissions);
      setError(null); // Don't show error for fallback
    } finally {
      setLoading(false);
    }
  }, [user?.id, user?.role]);

  /**
   * Fallback permission mapping for when dynamic system is not available
   */
  const getFallbackPermissions = (role: string): UserPermissions => {
    const permissions: UserPermissions = {};
    
    // Initialize all permissions as false
    standardPermissions.forEach(permission => {
      permissions[permission] = false;
    });

    // Set permissions based on role
    switch (role) {
      case 'SYSTEM_ADMIN':
      case 'IT_ADMIN':
        // Full system access
        standardPermissions.forEach(permission => {
          permissions[permission] = true;
        });
        break;
        
      case 'IT_STAFF':
        permissions['tickets.create'] = true;
        permissions['tickets.read'] = true;
        permissions['tickets.update'] = true;
        permissions['tickets.assign'] = true;
        permissions['tickets.manage'] = true;
        permissions['users.read'] = true;
        permissions['dashboard.access'] = true;
        break;
        
      case 'HR_ADMIN':
        permissions['employees.admin'] = true;
        permissions['employees.create'] = true;
        permissions['employees.read'] = true;
        permissions['employees.update'] = true;
        permissions['employees.delete'] = true;
        permissions['employees.manage'] = true;
        permissions['tickets.create'] = true;
        permissions['tickets.read'] = true;
        permissions['dashboard.access'] = true;
        permissions['reports.view'] = true;
        break;
        
      case 'HR_STAFF':
        permissions['employees.create'] = true;
        permissions['employees.read'] = true;
        permissions['employees.update'] = true;
        permissions['employees.manage'] = true;
        permissions['tickets.create'] = true;
        permissions['tickets.read'] = true;
        permissions['dashboard.access'] = true;
        break;
        
      case 'DEPT_HEAD':
        permissions['department.manage'] = true;
        permissions['department.view'] = true;
        permissions['employees.read'] = true;
        permissions['tickets.create'] = true;
        permissions['tickets.read'] = true;
        permissions['dashboard.access'] = true;
        permissions['reports.view'] = true;
        break;
        
      case 'EMPLOYEE':
      default:
        permissions['tickets.create'] = true;
        permissions['tickets.read'] = true;
        permissions['dashboard.access'] = true;
        break;
    }

    return permissions;
  };

  /**
   * Check if user has a specific permission
   */
  const hasPermission = useCallback((permission: string): boolean => {
    return userPermissions[permission] || false;
  }, [userPermissions]);

  /**
   * Check if user has any of the specified permissions
   */
  const hasAnyPermission = useCallback((permissions: string[]): boolean => {
    return permissions.some(permission => userPermissions[permission]);
  }, [userPermissions]);

  /**
   * Check if user has all of the specified permissions
   */
  const hasAllPermissions = useCallback((permissions: string[]): boolean => {
    return permissions.every(permission => userPermissions[permission]);
  }, [userPermissions]);

  /**
   * Refresh permissions from server
   */
  const refreshPermissions = useCallback(async () => {
    await fetchPermissions();
  }, [fetchPermissions]);

  /**
   * Check a specific permission with resource context
   */
  const checkPermission = useCallback(async (permission: string, resource?: any): Promise<boolean> => {
    if (!user?.id) return false;

    try {
      const response = await api.post('/roles/check-permissions', {
        userId: user.id,
        actions: [permission],
        resource
      });

      return response.data?.permissions?.[permission] || false;
    } catch (error) {
      console.warn('Permission check failed, using cached result:', error);
      return hasPermission(permission);
    }
  }, [user?.id, hasPermission]);

  // Load permissions when user changes
  useEffect(() => {
    fetchPermissions();
  }, [fetchPermissions]);

  return {
    userPermissions,
    loading,
    error,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    refreshPermissions,
    checkPermission
  };
};

/**
 * Helper hook for common permission patterns
 */
export const useRolePermissions = () => {
  const { hasPermission, hasAnyPermission } = usePermissions();

  return {
    // System access (replaces IT_ADMIN checks)
    isSystemAdmin: hasPermission('system.admin'),
    canConfigureSystem: hasPermission('system.configure'),
    
    // User management (replaces IT_ADMIN checks)
    canManageUsers: hasAnyPermission(['system.admin', 'users.create', 'users.update', 'users.delete']),
    canViewUsers: hasAnyPermission(['system.admin', 'users.read']),
    canCreateUsers: hasPermission('users.create'),
    canEditUsers: hasPermission('users.update'),
    canDeleteUsers: hasPermission('users.delete'),
    
    // Ticket management (replaces IT_STAFF checks)
    canManageTickets: hasAnyPermission(['system.admin', 'tickets.manage']),
    canCreateTickets: hasPermission('tickets.create'),
    canEditTickets: hasPermission('tickets.update'),
    canDeleteTickets: hasPermission('tickets.delete'),
    canAssignTickets: hasPermission('tickets.assign'),
    
    // Employee management (replaces HR_ADMIN, HR_STAFF checks)
    isHRAdmin: hasPermission('employees.admin'),
    canManageEmployees: hasAnyPermission(['employees.admin', 'employees.manage']),
    canCreateEmployees: hasPermission('employees.create'),
    canEditEmployees: hasPermission('employees.update'),
    canDeleteEmployees: hasPermission('employees.delete'),
    canViewEmployees: hasPermission('employees.read'),
    
    // Department management (replaces DEPT_HEAD checks)
    canManageDepartment: hasPermission('department.manage'),
    canViewDepartment: hasPermission('department.view'),
    
    // Dashboard access
    canAccessDashboard: hasPermission('dashboard.access'),
    canAdminDashboard: hasPermission('dashboard.admin'),
    
    // Reports
    canViewReports: hasPermission('reports.view'),
    canCreateReports: hasPermission('reports.create'),
    canExportReports: hasPermission('reports.export')
  };
};
