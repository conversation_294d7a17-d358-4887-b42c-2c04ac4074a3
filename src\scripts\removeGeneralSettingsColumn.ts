import { AppDataSource } from '../config/database';
import { RemoveGeneralSettingsColumn1703700000000 } from '../migrations/1703700000000-RemoveGeneralSettingsColumn';

async function removeGeneralSettingsColumn() {
  try {
    console.log('🔄 Starting removal of generalSettings column...');
    
    // Initialize the database connection
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ Database connection initialized');
    }

    // Create and run the migration
    const migration = new RemoveGeneralSettingsColumn1703700000000();
    const queryRunner = AppDataSource.createQueryRunner();

    await queryRunner.connect();
    
    try {
      await migration.up(queryRunner);
      console.log('✅ Successfully removed generalSettings column from leave_policy_configurations table');
    } finally {
      await queryRunner.release();
    }

    console.log('🎉 GeneralSettings column removal completed successfully!');
  } catch (error) {
    console.error('❌ Error removing generalSettings column:', error);
    throw error;
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('✅ Database connection closed');
    }
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  removeGeneralSettingsColumn()
    .then(() => {
      console.log('✅ Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    });
}

export { removeGeneralSettingsColumn }; 