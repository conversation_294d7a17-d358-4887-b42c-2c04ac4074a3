import React from 'react';
import { 
  X, Edit2, Calendar, Tag, Box, MapPin, Building2, DollarSign, 
  User, Clock, AlertTriangle, Shield, Info, Cpu, HardDrive, 
  Smartphone, Monitor, Printer, Network, Server, Headphones, Settings, FileText
} from 'lucide-react';
import { AssetFrontend } from './AssetManagement';

interface ViewAssetModalProps {
  asset: AssetFrontend;
  onClose: () => void;
  onEdit: () => void;
}

export const ViewAssetModal: React.FC<ViewAssetModalProps> = ({
  asset,
  onClose,
  onEdit,
}) => {
  const formatPKR = (amount: number | undefined) => {
    if (!amount) return 'PKR 0';
    return new Intl.NumberFormat('en-PK', {
      style: 'currency',
      currency: 'PKR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (date: string | undefined) => {
    if (!date) return 'N/A';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Helper function to check if a value exists and is not empty
  const hasValue = (value: any): boolean => {
    if (value === undefined || value === null) return false;
    if (typeof value === 'string') return value.trim() !== '';
    if (Array.isArray(value)) return value.length > 0;
    return true;
  };

  // Get icon based on asset type
  const getAssetTypeIcon = () => {
    const type = asset.assetType?.toLowerCase() || '';
    
    if (type.includes('comput')) return <Cpu className="h-6 w-6 text-blue-500" />;
    if (type.includes('mobile') || type.includes('tablet')) return <Smartphone className="h-6 w-6 text-purple-500" />;
    if (type.includes('display') || type.includes('monitor')) return <Monitor className="h-6 w-6 text-green-500" />;
    if (type.includes('print')) return <Printer className="h-6 w-6 text-red-500" />;
    if (type.includes('network')) return <Network className="h-6 w-6 text-indigo-500" />;
    if (type.includes('server')) return <Server className="h-6 w-6 text-yellow-500" />;
    if (type.includes('peripheral') || type.includes('accessory')) return <Headphones className="h-6 w-6 text-pink-500" />;
    
    return <Box className="h-6 w-6 text-blue-500" />;
  };

  // Get status color
  const getStatusColor = () => {
    const status = asset.status?.toLowerCase() || '';
    
    if (status === 'active') return 'bg-green-100 text-green-800 border-green-200';
    if (status === 'maintenance' || status === 'under repair') return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    if (status === 'retired') return 'bg-red-100 text-red-800 border-red-200';
    if (status === 'reserved') return 'bg-blue-100 text-blue-800 border-blue-200';
    
    return 'bg-gray-100 text-gray-800 border-gray-200';
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 backdrop-blur-sm">
      <div className="flex items-center justify-center min-h-screen px-4 py-8">
        <div className="relative bg-white rounded-xl shadow-2xl w-full max-w-3xl overflow-hidden">
          {/* Header with gradient background */}
          <div className="bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-3">
                {getAssetTypeIcon()}
                <h3 className="text-xl font-bold text-white">
                  {asset.manufacturer} {asset.model}
                </h3>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={onEdit}
                  className="p-2 text-white hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
                  title="Edit Asset"
                >
                  <Edit2 className="h-5 w-5" />
                </button>
                <button
                  onClick={onClose}
                  className="p-2 text-white hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
                  title="Close"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
            </div>
            
            {/* Status badge */}
            {hasValue(asset.status) && (
              <div className="mt-2">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor()} border`}>
                  {asset.status}
                </span>
              </div>
            )}
          </div>

          {/* Content */}
          <div className="p-6 max-h-[70vh] overflow-y-auto">
            <div className="space-y-6">
              {/* Assignment Information */}
              <section className="bg-blue-50 rounded-lg p-5 border border-blue-100 shadow-sm">
                <h4 className="text-md font-semibold text-gray-700 mb-4 flex items-center gap-2">
                  <User className="h-5 w-5 text-blue-500" />
                  Assignment Information
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {hasValue(asset.assignedTo) && (
                    <div className="col-span-2 bg-white p-4 rounded-lg border border-blue-100 shadow-sm">
                      <label className="block text-sm font-medium text-gray-500 mb-2">Assigned To</label>
                      <div className="space-y-2">
                        {Array.isArray(asset.assignedTo) ? (
                          asset.assignedTo.map((user, index) => (
                            <div key={index} className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                              <div className="bg-blue-100 p-2 rounded-full">
                                <User className="h-5 w-5 text-blue-600" />
                              </div>
                              <div>
                                <p className="text-sm font-semibold text-gray-900">{user.name}</p>
                                <p className="text-xs text-gray-500">{user.department}</p>
                              </div>
                            </div>
                          ))
                        ) : asset.assignedTo ? (
                          <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                            <div className="bg-blue-100 p-2 rounded-full">
                              <User className="h-5 w-5 text-blue-600" />
                            </div>
                            <div>
                              <p className="text-sm font-semibold text-gray-900">{asset.assignedTo.name}</p>
                              <p className="text-xs text-gray-500">{asset.assignedTo.department}</p>
                            </div>
                          </div>
                        ) : (
                          <p className="text-sm text-gray-500">No user assigned</p>
                        )}
                      </div>
                    </div>
                  )}
                  {hasValue(asset.department) && (
                    <div className="bg-white p-4 rounded-lg border border-blue-100 shadow-sm">
                      <label className="block text-sm font-medium text-gray-500 mb-2">Department</label>
                      <p className="text-md font-semibold text-gray-900 flex items-center gap-2">
                        <Building2 className="h-4 w-4 text-blue-500" />
                        {asset.department}
                      </p>
                    </div>
                  )}
                  {hasValue(asset.location) && (
                    <div className="bg-white p-4 rounded-lg border border-blue-100 shadow-sm">
                      <label className="block text-sm font-medium text-gray-500 mb-2">Location</label>
                      <p className="text-md font-semibold text-gray-900 flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-blue-500" />
                        {asset.location}
                      </p>
                    </div>
                  )}
                </div>
              </section>

              {/* Asset Information */}
              <section className="bg-gray-50 rounded-lg p-5 border border-gray-100 shadow-sm">
                <h4 className="text-md font-semibold text-gray-700 mb-4 flex items-center gap-2">
                  <Box className="h-5 w-5 text-gray-700" />
                  Asset Information
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {hasValue(asset.id) && (
                    <div className="bg-white p-4 rounded-lg border border-gray-100 shadow-sm">
                      <label className="block text-sm font-medium text-gray-500 mb-2">Asset ID</label>
                      <p className="text-md font-semibold text-gray-900">{asset.id}</p>
                    </div>
                  )}
                  {hasValue(asset.assetType) && (
                    <div className="bg-white p-4 rounded-lg border border-gray-100 shadow-sm">
                      <label className="block text-sm font-medium text-gray-500 mb-2">Asset Type</label>
                      <p className="text-md font-semibold text-gray-900 flex items-center gap-2">
                        {getAssetTypeIcon()}
                        {asset.assetType}
                      </p>
                    </div>
                  )}
                  {hasValue(asset.category) && (
                    <div className="bg-white p-4 rounded-lg border border-gray-100 shadow-sm">
                      <label className="block text-sm font-medium text-gray-500 mb-2">Category</label>
                      <p className="text-md font-semibold text-gray-900">{asset.category}</p>
                    </div>
                  )}
                  {hasValue(asset.serialNumber) && (
                    <div className="bg-white p-4 rounded-lg border border-gray-100 shadow-sm">
                      <label className="block text-sm font-medium text-gray-500 mb-2">Serial Number</label>
                      <p className="text-md font-semibold text-gray-900">{asset.serialNumber}</p>
                    </div>
                  )}
                  {hasValue(asset.manufacturer) && (
                    <div className="bg-white p-4 rounded-lg border border-gray-100 shadow-sm">
                      <label className="block text-sm font-medium text-gray-500 mb-2">Manufacturer</label>
                      <p className="text-md font-semibold text-gray-900">{asset.manufacturer}</p>
                    </div>
                  )}
                  {hasValue(asset.model) && (
                    <div className="bg-white p-4 rounded-lg border border-gray-100 shadow-sm">
                      <label className="block text-sm font-medium text-gray-500 mb-2">Model</label>
                      <p className="text-md font-semibold text-gray-900">{asset.model}</p>
                    </div>
                  )}
                </div>
              </section>

              {/* Purchase & Warranty */}
              {(hasValue(asset.purchaseDate) || hasValue(asset.warrantyExpiry) || 
                hasValue(asset.cost) || hasValue(asset.vendor)) && (
                <section className="bg-green-50 rounded-lg p-5 border border-green-100 shadow-sm">
                  <h4 className="text-md font-semibold text-gray-700 mb-4 flex items-center gap-2">
                    <DollarSign className="h-5 w-5 text-green-500" />
                    Purchase & Warranty
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {hasValue(asset.purchaseDate) && (
                      <div className="bg-white p-4 rounded-lg border border-green-100 shadow-sm">
                        <label className="block text-sm font-medium text-gray-500 mb-2">Purchase Date</label>
                        <p className="text-md font-semibold text-gray-900 flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-green-500" />
                          {formatDate(asset.purchaseDate)}
                        </p>
                      </div>
                    )}
                    {hasValue(asset.cost) && (
                      <div className="bg-white p-4 rounded-lg border border-green-100 shadow-sm">
                        <label className="block text-sm font-medium text-gray-500 mb-2">Purchase Cost</label>
                        <p className="text-md font-semibold text-gray-900 flex items-center gap-2">
                          <DollarSign className="h-4 w-4 text-green-500" />
                          {formatPKR(asset.cost)}
                        </p>
                      </div>
                    )}
                    {hasValue(asset.warrantyExpiry) && (
                      <div className="bg-white p-4 rounded-lg border border-green-100 shadow-sm">
                        <label className="block text-sm font-medium text-gray-500 mb-2">Warranty Until</label>
                        <p className="text-md font-semibold text-gray-900 flex items-center gap-2">
                          <Shield className="h-4 w-4 text-green-500" />
                          {formatDate(asset.warrantyExpiry)}
                        </p>
                      </div>
                    )}
                    {hasValue(asset.vendor) && (
                      <div className="bg-white p-4 rounded-lg border border-green-100 shadow-sm">
                        <label className="block text-sm font-medium text-gray-500 mb-2">Vendor</label>
                        <p className="text-md font-semibold text-gray-900">{asset.vendor}</p>
                      </div>
                    )}
                  </div>
                </section>
              )}

              {/* Status & Condition */}
              <section className="bg-purple-50 rounded-lg p-5 border border-purple-100 shadow-sm">
                <h4 className="text-md font-semibold text-gray-700 mb-4 flex items-center gap-2">
                  <Info className="h-5 w-5 text-purple-500" />
                  Status & Condition
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {hasValue(asset.status) && (
                    <div className="bg-white p-4 rounded-lg border border-purple-100 shadow-sm">
                      <label className="block text-sm font-medium text-gray-500 mb-2">Status</label>
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor()} border`}>
                        {asset.status}
                      </span>
                    </div>
                  )}
                  {hasValue(asset.condition) && (
                    <div className="bg-white p-4 rounded-lg border border-purple-100 shadow-sm">
                      <label className="block text-sm font-medium text-gray-500 mb-2">Condition</label>
                      <p className="text-md font-semibold text-gray-900">{asset.condition}</p>
                    </div>
                  )}
                </div>
              </section>

              {/* Technical Attributes */}
              {hasValue(asset.attributes) && Object.keys(asset.attributes).length > 0 && (
                <section className="bg-yellow-50 rounded-lg p-5 border border-yellow-100 shadow-sm">
                  <h4 className="text-md font-semibold text-gray-700 mb-4 flex items-center gap-2">
                    <Settings className="h-5 w-5 text-yellow-600" />
                    Technical Specifications
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(asset.attributes).map(([key, value]) => 
                      hasValue(value) && (
                        <div key={key} className="bg-white p-4 rounded-lg border border-yellow-100 shadow-sm">
                          <label className="block text-sm font-medium text-gray-500 mb-2">
                            {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                          </label>
                          <p className="text-md font-semibold text-gray-900">{value}</p>
                        </div>
                      )
                    )}
                  </div>
                </section>
              )}

              {/* Notes */}
              {hasValue(asset.notes) && (
                <section className="bg-indigo-50 rounded-lg p-5 border border-indigo-100 shadow-sm">
                  <h4 className="text-md font-semibold text-gray-700 mb-4 flex items-center gap-2">
                    <FileText className="h-5 w-5 text-indigo-500" />
                    Notes
                  </h4>
                  <div className="bg-white p-4 rounded-lg border border-indigo-100 shadow-sm">
                    <p className="text-gray-700 whitespace-pre-line">{asset.notes}</p>
                  </div>
                </section>
              )}
            </div>
          </div>
          
          {/* Footer */}
          <div className="bg-gray-50 px-6 py-3 flex justify-end border-t">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors mr-2"
            >
              Close
            </button>
            <button
              onClick={onEdit}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
            >
              <Edit2 className="h-4 w-4" />
              Edit Asset
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}; 