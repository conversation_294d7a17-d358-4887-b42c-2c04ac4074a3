const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Database connection config
const DB_HOST = process.env.DB_HOST || 'localhost';
const DB_PORT = process.env.DB_PORT || '3306';
const DB_USERNAME = process.env.DB_USERNAME || 'root';
const DB_PASSWORD = process.env.DB_PASSWORD || 'root';
const DB_NAME = 'ims_db';

async function importSampleData() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: DB_HOST,
      port: parseInt(DB_PORT),
      user: DB_USERNAME,
      password: DB_PASSWORD,
      database: DB_NAME
    });
    
    console.log('Connected to MySQL server');
    
    // Check if the users table has UUID format for IDs
    const [tableInfo] = await connection.query(`
      SELECT COLUMN_TYPE 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = '${DB_NAME}' 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'id'
    `);
    
    const isUUID = tableInfo.length > 0 && tableInfo[0].COLUMN_TYPE.toLowerCase().includes('varchar');
    console.log(`User ID format: ${isUUID ? 'UUID' : 'Integer'}`);
    
    // Create additional users
    await createUsers(connection, isUUID);
    
    // Create assets
    await createAssets(connection, isUUID);
    
    // Create tickets
    await createTickets(connection, isUUID);
    
    // Create knowledge base articles
    await createKnowledgeBase(connection, isUUID);
    
    console.log('Sample data imported successfully!');
    
  } catch (error) {
    console.error('Error importing sample data:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

async function createUsers(connection, isUUID) {
  const salt = await bcrypt.genSalt(10);
  const password = await bcrypt.hash('password123', salt);
  
  const users = [
    {
      id: isUUID ? uuidv4() : 2,
      name: 'IT Staff',
      email: '<EMAIL>',
      password,
      role: 'IT_STAFF',
      department: 'IT',
      isActive: 1,
      permissions: JSON.stringify({
        canCreateTickets: true,
        canCreateTicketsForOthers: true,
        canEditTickets: true,
        canDeleteTickets: false,
        canCloseTickets: true,
        canLockTickets: false,
        canAssignTickets: true,
        canEscalateTickets: true,
        canViewAllTickets: true
      })
    },
    {
      id: isUUID ? uuidv4() : 3,
      name: 'Employee User',
      email: '<EMAIL>',
      password,
      role: 'EMPLOYEE',
      department: 'Marketing',
      isActive: 1,
      permissions: JSON.stringify({
        canCreateTickets: true,
        canCreateTicketsForOthers: false,
        canEditTickets: false,
        canDeleteTickets: false,
        canCloseTickets: false,
        canLockTickets: false,
        canAssignTickets: false,
        canEscalateTickets: false,
        canViewAllTickets: false
      })
    },
    {
      id: isUUID ? uuidv4() : 4,
      name: 'Department Head',
      email: '<EMAIL>',
      password,
      role: 'DEPT_HEAD',
      department: 'Sales',
      isActive: 1,
      permissions: JSON.stringify({
        canCreateTickets: true,
        canCreateTicketsForOthers: true,
        canEditTickets: false,
        canDeleteTickets: false,
        canCloseTickets: false,
        canLockTickets: false,
        canAssignTickets: false,
        canEscalateTickets: true,
        canViewAllTickets: false
      })
    }
  ];
  
  for (const user of users) {
    // Check if user already exists
    const [existingUser] = await connection.query(
      'SELECT * FROM users WHERE email = ?',
      [user.email]
    );
    
    if (existingUser.length === 0) {
      if (isUUID) {
        await connection.query(`
          INSERT INTO users (id, name, email, password, role, department, isActive, permissions)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          user.id,
          user.name,
          user.email,
          user.password,
          user.role,
          user.department,
          user.isActive,
          user.permissions
        ]);
      } else {
        await connection.query(`
          INSERT INTO users (name, email, password, role, department, isActive, permissions)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
          user.name,
          user.email,
          user.password,
          user.role,
          user.department,
          user.isActive,
          user.permissions
        ]);
      }
      console.log(`Created user: ${user.name}`);
    } else {
      console.log(`User already exists: ${user.email}`);
    }
  }
  
  // Return the user mapping
  const [users2] = await connection.query('SELECT id, email FROM users');
  const userMap = users2.reduce((map, user) => {
    map[user.email] = user.id;
    return map;
  }, {});
  
  return userMap;
}

async function createAssets(connection, isUUID) {
  // Get user mapping first
  const [users] = await connection.query('SELECT id, email FROM users');
  const userMap = users.reduce((map, user) => {
    map[user.email] = user.id;
    return map;
  }, {});
  
  const assets = [
    {
      assetType: 'Laptop',
      category: 'Computer Equipment',
      manufacturer: 'Dell',
      model: 'XPS 15 9510',
      serialNumber: 'DELL12345678',
      assetTag: 'LP-001',
      location: 'Main Office',
      department: 'Marketing',
      status: 'In Use',
      condition: 'Good',
      purchaseDate: '2023-01-15',
      warrantyExpiry: '2026-01-15',
      cost: 1899.99,
      vendor: 'Dell Inc.',
      project: null,
      internetAccess: 1,
      ipAddress: '*************',
      notes: 'Assigned to marketing team member',
      assignedTo: '<EMAIL>'
    },
    {
      assetType: 'Printer',
      category: 'Office Equipment',
      manufacturer: 'HP',
      model: 'LaserJet Pro M404n',
      serialNumber: 'HP987654321',
      assetTag: 'PR-001',
      location: 'Main Office',
      department: 'IT',
      status: 'In Use',
      condition: 'Excellent',
      purchaseDate: '2023-02-20',
      warrantyExpiry: '2025-02-20',
      cost: 349.99,
      vendor: 'HP Inc.',
      project: null,
      internetAccess: 1,
      ipAddress: '*************',
      notes: 'Main office printer on 1st floor',
      assignedTo: null
    },
    {
      assetType: 'Mobile Phone',
      category: 'Communication Equipment',
      manufacturer: 'Apple',
      model: 'iPhone 13 Pro',
      serialNumber: 'IPHX123456789',
      assetTag: 'MO-001',
      location: 'Main Office',
      department: 'Sales',
      status: 'In Use',
      condition: 'Good',
      purchaseDate: '2023-03-10',
      warrantyExpiry: '2025-03-10',
      cost: 999.99,
      vendor: 'Apple Inc.',
      project: null,
      internetAccess: 1,
      ipAddress: null,
      notes: 'Company phone for sales manager',
      assignedTo: '<EMAIL>'
    }
  ];

  for (const asset of assets) {
    // Check if asset already exists
    const [existingAsset] = await connection.query(
      'SELECT * FROM assets WHERE assetTag = ?',
      [asset.assetTag]
    );
    
    if (existingAsset.length === 0) {
      let assignedUserId = null;
      
      if (asset.assignedTo && userMap[asset.assignedTo]) {
        assignedUserId = userMap[asset.assignedTo];
      }
      
      await connection.query(`
        INSERT INTO assets (
          assetType, category, manufacturer, model, serialNumber, assetTag,
          location, department, status, condition, purchaseDate, warrantyExpiry,
          cost, vendor, project, internetAccess, ipAddress, notes, assignedToId
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        asset.assetType,
        asset.category,
        asset.manufacturer,
        asset.model,
        asset.serialNumber,
        asset.assetTag,
        asset.location,
        asset.department,
        asset.status,
        asset.condition,
        asset.purchaseDate,
        asset.warrantyExpiry,
        asset.cost,
        asset.vendor,
        asset.project,
        asset.internetAccess,
        asset.ipAddress,
        asset.notes,
        assignedUserId
      ]);
      console.log(`Created asset: ${asset.manufacturer} ${asset.model}`);
    } else {
      console.log(`Asset already exists: ${asset.assetTag}`);
    }
  }
}

async function createTickets(connection, isUUID) {
  // Get user IDs
  const [users] = await connection.query('SELECT id, email FROM users');
  const userMap = users.reduce((map, user) => {
    map[user.email] = user.id;
    return map;
  }, {});
  
  const tickets = [
    {
      title: 'Laptop not turning on',
      description: 'My laptop won\'t power on even when connected to a charger.',
      status: 'OPEN',
      priority: 'HIGH',
      category: 'Hardware',
      createdBy: '<EMAIL>',
      assignedTo: '<EMAIL>'
    },
    {
      title: 'Need new software installation',
      description: 'I need Adobe Photoshop installed on my workstation.',
      status: 'IN_PROGRESS',
      priority: 'MEDIUM',
      category: 'Software',
      createdBy: '<EMAIL>',
      assignedTo: '<EMAIL>'
    },
    {
      title: 'Email access issues',
      description: 'Unable to send or receive emails since this morning.',
      status: 'OPEN',
      priority: 'URGENT',
      category: 'Email',
      createdBy: '<EMAIL>',
      assignedTo: null
    }
  ];
  
  for (const ticket of tickets) {
    const createdById = userMap[ticket.createdBy];
    const assignedToId = ticket.assignedTo ? userMap[ticket.assignedTo] : null;
    
    if (!createdById) {
      console.log(`Skipping ticket "${ticket.title}" - creator user not found`);
      continue;
    }
    
    await connection.query(`
      INSERT INTO tickets (
        title, description, status, priority, category, 
        createdById, assignedToId, createdAt, updatedAt
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [
      ticket.title,
      ticket.description,
      ticket.status,
      ticket.priority,
      ticket.category,
      createdById,
      assignedToId
    ]);
    console.log(`Created ticket: ${ticket.title}`);
  }
}

async function createKnowledgeBase(connection, isUUID) {
  // Get admin user ID
  const [adminUser] = await connection.query(
    'SELECT id FROM users WHERE email = ?',
    ['<EMAIL>']
  );
  
  if (adminUser.length === 0) {
    console.log('Admin user not found, skipping knowledge base creation');
    return;
  }
  
  const adminId = adminUser[0].id;
  
  // Create categories
  const categories = [
    { name: 'Hardware', description: 'Hardware related articles' },
    { name: 'Software', description: 'Software related articles' },
    { name: 'Network', description: 'Network related articles' }
  ];
  
  const categoryIds = {};
  
  for (const category of categories) {
    // Check if category already exists
    const [existingCategory] = await connection.query(
      'SELECT * FROM knowledge_categories WHERE name = ?',
      [category.name]
    );
    
    if (existingCategory.length === 0) {
      const [result] = await connection.query(`
        INSERT INTO knowledge_categories (name, description, createdAt, updatedAt)
        VALUES (?, ?, NOW(), NOW())
      `, [
        category.name,
        category.description
      ]);
      
      categoryIds[category.name] = result.insertId;
      console.log(`Created category: ${category.name}`);
    } else {
      categoryIds[category.name] = existingCategory[0].id;
      console.log(`Category already exists: ${category.name}`);
    }
  }
  
  // Create articles
  const articles = [
    {
      title: 'How to troubleshoot laptop power issues',
      content: `
        <h2>Laptop Power Troubleshooting Guide</h2>
        <p>Follow these steps to diagnose and potentially resolve laptop power issues:</p>
        <ol>
          <li>Check if the power adapter is connected properly</li>
          <li>Try a different power outlet</li>
          <li>Remove the battery and try running directly from the power adapter</li>
          <li>Check for any visible damage to the charging port</li>
          <li>If possible, try using a different power adapter</li>
        </ol>
        <p>If none of these steps work, please contact IT support for further assistance.</p>
      `,
      category: 'Hardware',
      isPublished: true
    },
    {
      title: 'Setting up company email on your mobile device',
      content: `
        <h2>Mobile Email Setup Guide</h2>
        <p>Follow these steps to set up your company email on your mobile device:</p>
        <ol>
          <li>Open the Mail app on your device</li>
          <li>Go to Settings > Add Account</li>
          <li>Select Microsoft Exchange or Office 365</li>
          <li>Enter your company email address and password</li>
          <li>For server, enter "outlook.office365.com"</li>
          <li>Ensure SSL is enabled</li>
          <li>Complete the setup by following on-screen instructions</li>
        </ol>
        <p>If you encounter any issues, please contact IT support.</p>
      `,
      category: 'Software',
      isPublished: true
    },
    {
      title: 'Troubleshooting common Wi-Fi connectivity issues',
      content: `
        <h2>Wi-Fi Troubleshooting Guide</h2>
        <p>If you're experiencing Wi-Fi connectivity issues, try these steps:</p>
        <ol>
          <li>Turn Wi-Fi off and on again on your device</li>
          <li>Restart your device</li>
          <li>Move closer to the Wi-Fi access point</li>
          <li>Forget the network and reconnect</li>
          <li>Check if other devices can connect to the same network</li>
          <li>Reset network settings on your device</li>
        </ol>
        <p>If problems persist, please submit a support ticket with details about your device and the specific issue you're experiencing.</p>
      `,
      category: 'Network',
      isPublished: true
    }
  ];
  
  for (const article of articles) {
    // Skip if the category doesn't exist
    if (!categoryIds[article.category]) {
      console.log(`Skipping article "${article.title}" - category not found`);
      continue;
    }
    
    // Check if article already exists
    const [existingArticle] = await connection.query(
      'SELECT * FROM knowledge_base WHERE title = ?',
      [article.title]
    );
    
    if (existingArticle.length === 0) {
      await connection.query(`
        INSERT INTO knowledge_base (
          title, content, categoryId, createdById, isPublished, createdAt, updatedAt
        )
        VALUES (?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        article.title,
        article.content,
        categoryIds[article.category],
        adminId,
        article.isPublished ? 1 : 0
      ]);
      console.log(`Created article: ${article.title}`);
    } else {
      console.log(`Article already exists: ${article.title}`);
    }
  }
}

// Run the script
importSampleData(); 