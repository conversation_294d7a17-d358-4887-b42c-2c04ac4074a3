import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { IsNotEmpty, IsString, IsEnum, IsOptional } from 'class-validator';

export enum LogType {
  INFO = 'info',
  WARNING = 'warning',
  ERROR = 'error',
  SUCCESS = 'success'
}

@Entity('system_logs')
export class SystemLog {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ 
    type: 'enum', 
    enum: LogType, 
    default: LogType.INFO 
  })
  @IsNotEmpty({ message: 'Log type is required' })
  @IsEnum(LogType, { message: 'Type must be one of: info, warning, error, success' })
  type: LogType;

  @Column({ type: 'varchar', length: 255 })
  @IsNotEmpty({ message: 'Action is required' })
  @IsString({ message: 'Action must be a string' })
  action: string;

  @Column({ type: 'varchar', length: 100 })
  @IsNotEmpty({ message: 'User is required' })
  @IsString({ message: 'User must be a string' })
  user: string;

  @CreateDateColumn({ 
    type: 'timestamp', 
    precision: 6, 
    default: () => 'CURRENT_TIMESTAMP(6)' 
  })
  timestamp: Date;

  @Column({ type: 'text' })
  @IsOptional()
  @IsString({ message: 'Details must be a string' })
  details: string;
} 