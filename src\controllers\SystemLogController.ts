import { Request, Response } from 'express';
import { SystemLogService } from '../services/SystemLogService';

export const SystemLogController = {
  // Get all system logs
  getAllLogs: async (req: Request, res: Response): Promise<void> => {
    try {
      const options = {
        page: req.query.page ? parseInt(req.query.page as string) : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        sortBy: req.query.sortBy as string,
        sortOrder: req.query.sortOrder as 'ASC' | 'DESC',
        search: req.query.search as string,
        type: req.query.type as string,
        user: req.query.user as string,
        dateFrom: req.query.dateFrom as string,
        dateTo: req.query.dateTo as string
      };
      
      const result = await SystemLogService.getAllLogs(options);
      
      res.status(200).json({
        success: true,
        ...result
      });
    } catch (error) {
      console.error('Error in getAllLogs controller:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch system logs',
        error: (error as Error).message
      });
    }
  },
  
  // Get a single system log by ID
  getLogById: async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      
      const log = await SystemLogService.getLogById(id);
      
      res.status(200).json({
        success: true,
        data: log
      });
    } catch (error) {
      console.error(`Error in getLogById controller for ID ${req.params.id}:`, error);
      
      if ((error as Error).message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: `System log with ID ${req.params.id} not found`,
          error: (error as Error).message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Failed to fetch system log',
          error: (error as Error).message
        });
      }
    }
  },
  
  // Create a new system log
  createLog: async (req: Request, res: Response): Promise<void> => {
    try {
      const logData = req.body;
      const currentUser = (req as any).user; // From auth middleware
      
      const createdLog = await SystemLogService.createLog(logData, currentUser);
      
      res.status(201).json({
        success: true,
        data: createdLog,
        message: 'System log created successfully'
      });
    } catch (error) {
      console.error('Error in createLog controller:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create system log',
        error: (error as Error).message
      });
    }
  },
  
  // Update an existing system log
  updateLog: async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const logData = req.body;
      const currentUser = (req as any).user; // From auth middleware
      
      const updatedLog = await SystemLogService.updateLog(id, logData, currentUser);
      
      res.status(200).json({
        success: true,
        data: updatedLog,
        message: 'System log updated successfully'
      });
    } catch (error) {
      console.error(`Error in updateLog controller for ID ${req.params.id}:`, error);
      
      if ((error as Error).message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: `System log with ID ${req.params.id} not found`,
          error: (error as Error).message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Failed to update system log',
          error: (error as Error).message
        });
      }
    }
  },
  
  // Delete a system log
  deleteLog: async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      
      await SystemLogService.deleteLog(id);
      
      res.status(200).json({
        success: true,
        message: 'System log deleted successfully'
      });
    } catch (error) {
      console.error(`Error in deleteLog controller for ID ${req.params.id}:`, error);
      
      if ((error as Error).message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: `System log with ID ${req.params.id} not found`,
          error: (error as Error).message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Failed to delete system log',
          error: (error as Error).message
        });
      }
    }
  },
  
  // Clear all system logs
  clearLogs: async (req: Request, res: Response): Promise<void> => {
    try {
      await SystemLogService.clearLogs();
      
      res.status(200).json({
        success: true,
        message: 'All system logs cleared successfully'
      });
    } catch (error) {
      console.error('Error in clearLogs controller:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to clear system logs',
        error: (error as Error).message
      });
    }
  },
  
  // Get statistics for system logs
  getStatistics: async (req: Request, res: Response): Promise<void> => {
    try {
      const statistics = await SystemLogService.getStatistics();
      
      res.status(200).json({
        success: true,
        data: statistics
      });
    } catch (error) {
      console.error('Error in getStatistics controller:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get system log statistics',
        error: (error as Error).message
      });
    }
  },
  
  // Export logs as CSV
  exportLogs: async (req: Request, res: Response): Promise<void> => {
    try {
      const options = {
        type: req.query.type as string,
        user: req.query.user as string,
        dateFrom: req.query.dateFrom as string,
        dateTo: req.query.dateTo as string
      };
      
      const csv = await SystemLogService.exportLogs(options);
      
      // Set headers for CSV download
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=system_logs.csv');
      
      res.status(200).send(csv);
    } catch (error) {
      console.error('Error in exportLogs controller:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to export system logs',
        error: (error as Error).message
      });
    }
  }
}; 