import { Router } from 'express';
import { login, logout, getCurrentUser, register } from '../controllers/authController';
import { requireAuth } from '../middleware/auth';

const router = Router();

// Public routes
router.post('/register', register);
router.post('/login', login);
router.post('/logout', logout);

// Protected routes
router.get('/me', requireAuth, getCurrentUser);

export default router; 