import { AppDataSource } from '../config/database';

async function fixOrphanedLeaveTypes() {
  try {
    console.log('🔍 Initializing database connection...');
    await AppDataSource.initialize();
    
    const queryRunner = AppDataSource.createQueryRunner();
    
    await queryRunner.startTransaction();
    
    try {
      console.log('🧹 Fixing orphaned leave types...\n');
      
      // First, let's see what we have
      console.log('📋 Current state check:');
      
      const policies = await queryRunner.query(`
        SELECT leaveType, displayName FROM leave_type_policies 
        WHERE isActive = 1 ORDER BY leaveType
      `);
      
      console.log(`✅ Active leave type policies (${policies.length}):`);
      policies.forEach((p: any) => {
        console.log(`   • ${p.leaveType} (${p.displayName})`);
      });
      
      const orphanedInAllocations = await queryRunner.query(`
        SELECT DISTINCT la.leaveType, COUNT(*) as count
        FROM leave_allocations la
        LEFT JOIN leave_type_policies ltp ON la.leaveType = ltp.leaveType AND ltp.isActive = 1
        WHERE ltp.leaveType IS NULL
        GROUP BY la.leaveType
        ORDER BY la.leaveType
      `);
      
      const orphanedInBalances = await queryRunner.query(`
        SELECT DISTINCT lb.leaveType, COUNT(*) as count
        FROM leave_balances lb
        LEFT JOIN leave_type_policies ltp ON lb.leaveType = ltp.leaveType AND ltp.isActive = 1
        WHERE ltp.leaveType IS NULL
        GROUP BY lb.leaveType
        ORDER BY lb.leaveType
      `);
      
      console.log(`\n⚠️  Orphaned leave types in allocations (${orphanedInAllocations.length}):`);
      orphanedInAllocations.forEach((o: any) => {
        console.log(`   • ${o.leaveType} - ${o.count} records`);
      });
      
      console.log(`\n⚠️  Orphaned leave types in balances (${orphanedInBalances.length}):`);
      orphanedInBalances.forEach((o: any) => {
        console.log(`   • ${o.leaveType} - ${o.count} records`);
      });
      
             // Get the policy configuration ID first
       console.log('\n🔧 Getting policy configuration...');
       const policyConfig = await queryRunner.query(`
         SELECT id FROM leave_policy_configurations 
         WHERE isActive = 1 ORDER BY id DESC LIMIT 1
       `);
       
       if (policyConfig.length === 0) {
         console.log('❌ No active policy configuration found. Creating one...');
         await queryRunner.query(`
           INSERT INTO leave_policy_configurations (
             name, description, isActive, createdAt, updatedAt
           ) VALUES (
             'Default Policy', 'Auto-created policy configuration', 1, NOW(), NOW()
           )
         `);
         
         const newPolicyConfig = await queryRunner.query(`
           SELECT id FROM leave_policy_configurations 
           WHERE isActive = 1 ORDER BY id DESC LIMIT 1
         `);
         
         if (newPolicyConfig.length === 0) {
           throw new Error('Failed to create policy configuration');
         }
         
         console.log(`✅ Created policy configuration with ID: ${newPolicyConfig[0].id}`);
       }
       
       const policyConfigId = policyConfig.length > 0 ? policyConfig[0].id : 
         (await queryRunner.query(`SELECT id FROM leave_policy_configurations WHERE isActive = 1 ORDER BY id DESC LIMIT 1`))[0].id;
       
       console.log(`📋 Using policy configuration ID: ${policyConfigId}`);
       
       // Option 1: Create missing leave type policies for the orphaned types
       console.log('\n🔧 SOLUTION 1: Creating leave type policies for orphaned types...');
       
       const orphanedTypes = [...new Set([
         ...orphanedInAllocations.map((o: any) => o.leaveType),
         ...orphanedInBalances.map((o: any) => o.leaveType)
       ])];
       
       for (const leaveType of orphanedTypes) {
         const displayName = leaveType.split('_').map((word: string) => 
           word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
         ).join(' ');
         
         console.log(`   📝 Creating policy for ${leaveType} (${displayName})...`);
         
         await queryRunner.query(`
           INSERT INTO leave_type_policies (
             leaveType, enabled, displayName, description, settings, 
             applicableRoles, applicableDepartments, maxDaysPerYear, 
             minServicePeriod, allowCarryForward, carryForwardLimit, 
             encashmentAllowed, documentRequired, sortOrder, isActive, 
             category, genderEligibility, allowHalfDay, minDaysNotice,
             policyConfigurationId
           ) VALUES (
             ?, 1, ?, 'Auto-created leave type policy', '{}', 
             '[]', '[]', 25, 0, 0, 0, 0, 0, 1, 1, 
             'general', 'all', 1, 0, ?
           )
         `, [leaveType, displayName, policyConfigId]);
         
         console.log(`   ✅ Created policy for ${leaveType}`);
       }
      
      await queryRunner.commitTransaction();
      
      console.log('\n🎉 ORPHANED LEAVE TYPES FIXED!');
      console.log('📋 What was done:');
      console.log(`   • Created ${orphanedTypes.length} new leave type policies`);
      console.log('   • All existing leave allocations and balances now have valid policies');
      console.log('   • You can now edit these leave types in the Leave Policy Configuration tab');
      
      console.log('\n📝 NEXT STEPS:');
      console.log('   1. Go to Leave Policy Configuration tab');
      console.log('   2. Review and configure the auto-created leave types');
      console.log('   3. Set proper max days per year, carry forward rules, etc.');
      console.log('   4. Adjust the display names if needed');
      
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
    
  } catch (error) {
    console.error('❌ Error fixing orphaned leave types:', error);
    throw error;
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('\n📪 Database connection closed');
    }
  }
}

// Run the fix
if (require.main === module) {
  fixOrphanedLeaveTypes()
    .then(() => {
      console.log('✅ Orphaned leave types fix completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Orphaned leave types fix failed:', error);
      process.exit(1);
    });
}

export { fixOrphanedLeaveTypes }; 