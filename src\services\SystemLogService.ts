import { SystemLogRepository } from '../repositories/SystemLogRepository';
import { SystemLog, LogType } from '../entities/SystemLog';
import { User } from '../entities/User';

// Type for frontend system log format
export interface SystemLogData {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  action: string;
  user: string;
  timestamp: string;
  details: string;
}

// Convert entity to frontend format
const entityToData = (entity: SystemLog): SystemLogData => {
  return {
    id: entity.id,
    type: entity.type as 'info' | 'warning' | 'error' | 'success',
    action: entity.action,
    user: entity.user,
    timestamp: entity.timestamp.toISOString(),
    details: entity.details
  };
};

// Convert frontend format to entity
const dataToEntity = (data: Partial<SystemLogData>, currentUser?: User): Partial<SystemLog> => {
  return {
    ...(data.id && { id: data.id }),
    ...(data.type && { type: data.type as LogType }),
    ...(data.action && { action: data.action }),
    ...(data.user && { user: data.user }),
    ...(data.details && { details: data.details })
  };
};

export const SystemLogService = {
  // Get all logs with pagination and filtering
  getAllLogs: async (options?: {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
    search?: string;
    type?: string;
    user?: string;
    dateFrom?: string;
    dateTo?: string;
    includeAnonymous?: boolean;
    onlyValidUsers?: boolean;
  }) => {
    const page = options?.page || 1;
    const limit = options?.limit || 50;
    const skip = (page - 1) * limit;
    
    // Build order object
    const order: { [key: string]: 'ASC' | 'DESC' } = {};
    if (options?.sortBy) {
      order[options.sortBy] = options.sortOrder || 'DESC';
    } else {
      order['timestamp'] = 'DESC'; // Default sort
    }
    
    // Apply filters
    const filters: {
      search?: string;
      type?: string;
      user?: string;
      dateFrom?: string;
      dateTo?: string;
    } = {};
    
    if (options?.search) filters.search = options.search;
    if (options?.type) filters.type = options.type;
    if (options?.user) filters.user = options.user;
    if (options?.dateFrom) filters.dateFrom = options.dateFrom;
    if (options?.dateTo) filters.dateTo = options.dateTo;
    
    const { logs, count } = await SystemLogRepository.getAllLogs({
      skip,
      take: limit,
      order,
      filters,
      includeAnonymous: options?.includeAnonymous !== false, // Default to true unless explicitly set to false
      onlyValidUsers: options?.onlyValidUsers === true // Default to false unless explicitly set to true
    });
    
    // Handle case when requesting all logs
    const isShowAll = limit >= 9000;
    
    return {
      logs: logs.map(entityToData),
      total: count,
      page: isShowAll ? 1 : page,
      limit: isShowAll ? count : limit,
      totalPages: isShowAll ? 1 : Math.ceil(count / limit)
    };
  },
  
  // Get a single log by ID
  getLogById: async (id: string): Promise<SystemLogData> => {
    const log = await SystemLogRepository.getLogById(id);
    return entityToData(log);
  },
  
  // Create a new log
  createLog: async (data: Omit<SystemLogData, 'id' | 'timestamp'>, currentUser?: User): Promise<SystemLogData> => {
    const entity = dataToEntity(data, currentUser);
    const createdLog = await SystemLogRepository.createLog(entity);
    return entityToData(createdLog);
  },
  
  // Update an existing log
  updateLog: async (id: string, data: Partial<SystemLogData>, currentUser?: User): Promise<SystemLogData> => {
    const entity = dataToEntity(data, currentUser);
    const updatedLog = await SystemLogRepository.updateLog(id, entity);
    return entityToData(updatedLog);
  },
  
  // Delete a log
  deleteLog: async (id: string): Promise<boolean> => {
    return await SystemLogRepository.deleteLog(id);
  },
  
  // Clear all logs
  clearLogs: async (): Promise<boolean> => {
    return await SystemLogRepository.clearLogs();
  },
  
  // Get statistics about logs
  getStatistics: async () => {
    return await SystemLogRepository.getStatistics();
  },
  
  // Export logs as CSV
  exportLogs: async (options?: {
    type?: string;
    user?: string;
    dateFrom?: string;
    dateTo?: string;
  }): Promise<string> => {
    // Get all logs matching filters
    const { logs } = await SystemLogRepository.getAllLogs({
      filters: options,
      take: 10000 // Get up to 10,000 logs for export
    });
    
    if (logs.length === 0) {
      return 'id,type,action,user,timestamp,details\n'; // Empty CSV with headers
    }
    
    // Convert to CSV format
    const headers = 'id,type,action,user,timestamp,details';
    const rows = logs.map(log => {
      // Escape and format fields properly for CSV
      const details = log.details ? `"${log.details.replace(/"/g, '""')}"` : '';
      const action = `"${log.action.replace(/"/g, '""')}"`;
      const user = `"${log.user.replace(/"/g, '""')}"`;
      
      return `${log.id},${log.type},${action},${user},${log.timestamp.toISOString()},${details}`;
    });
    
    return `${headers}\n${rows.join('\n')}`;
  },
  
  // Delete all anonymous logs
  deleteAnonymousLogs: async (): Promise<number> => {
    return await SystemLogRepository.deleteAnonymousLogs();
  },
  
  // Delete logs for users that don't exist in the database
  deleteInvalidUserLogs: async (): Promise<number> => {
    return await SystemLogRepository.deleteInvalidUserLogs();
  }
}; 