import express from 'express';
import { ProjectController } from '../controllers/projectController';
import { requireAuth } from '../middleware/auth';

const router = express.Router();
const projectController = new ProjectController();

// Apply authentication middleware to all routes
router.use(requireAuth);

// Project routes
router.get('/', projectController.getAllProjects.bind(projectController));
router.get('/stats', projectController.getProjectStats.bind(projectController));
router.get('/my-projects', projectController.getMyProjects.bind(projectController));
router.get('/:id', projectController.getProjectById.bind(projectController));
router.post('/', projectController.createProject.bind(projectController));
router.put('/:id', projectController.updateProject.bind(projectController));
router.delete('/:id', projectController.deleteProject.bind(projectController));

export default router;
