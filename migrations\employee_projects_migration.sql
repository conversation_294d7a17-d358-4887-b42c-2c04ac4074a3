-- Create the employee_projects table
CREATE TABLE IF NOT EXISTS employee_projects (
  id SERIAL PRIMARY KEY,
  project_name VARCHAR(255) NOT NULL,
  role VARCHAR(255) NOT NULL,
  start_date VARCHAR(50),
  end_date VARCHAR(50),
  current_project BOOLEAN NOT NULL DEFAULT FALSE,
  technologies VARCHAR(255),
  description TEXT,
  achievements TEXT,
  team_size VARCHAR(50),
  client_name VARCHAR(255),
  employee_id INT,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
  FOREI<PERSON><PERSON> KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE
);

-- Add index for improved query performance
CREATE INDEX IF NOT EXISTS idx_employee_projects_employee_id ON employee_projects(employee_id);

-- Log the migration
INSERT INTO schema_migrations (version, name, run_on) 
VALUES ('20250515000000', 'Add employee projects table', NOW())
ON CONFLICT DO NOTHING; 