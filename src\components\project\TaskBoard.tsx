import React, { useState, useEffect } from 'react';
import { Plus, User, Calendar, AlertCircle } from 'lucide-react';
import { Project, Task, TaskStatus } from '../../types/project';

interface TaskBoardProps {
  projects: Project[];
}

interface TaskColumn {
  id: TaskStatus;
  title: string;
  color: string;
  tasks: Task[];
}

export default function TaskBoard({ projects }: TaskBoardProps) {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedProject, setSelectedProject] = useState<number | 'all'>('all');

  useEffect(() => {
    fetchTasks();
  }, [selectedProject]);

  const fetchTasks = async () => {
    try {
      setLoading(true);
      let url = '/api/tasks';
      
      if (selectedProject !== 'all') {
        url += `?projectId=${selectedProject}`;
      }

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setTasks(data.tasks || []);
      }
    } catch (error) {
      console.error('Error fetching tasks:', error);
    } finally {
      setLoading(false);
    }
  };

  const columns: TaskColumn[] = [
    {
      id: TaskStatus.TODO,
      title: 'To Do',
      color: 'bg-gray-100 border-gray-300',
      tasks: tasks.filter(task => task.status === TaskStatus.TODO)
    },
    {
      id: TaskStatus.IN_PROGRESS,
      title: 'In Progress',
      color: 'bg-blue-50 border-blue-300',
      tasks: tasks.filter(task => task.status === TaskStatus.IN_PROGRESS)
    },
    {
      id: TaskStatus.IN_REVIEW,
      title: 'In Review',
      color: 'bg-yellow-50 border-yellow-300',
      tasks: tasks.filter(task => task.status === TaskStatus.IN_REVIEW)
    },
    {
      id: TaskStatus.DONE,
      title: 'Done',
      color: 'bg-green-50 border-green-300',
      tasks: tasks.filter(task => task.status === TaskStatus.DONE)
    }
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'border-l-red-500';
      case 'high':
        return 'border-l-orange-500';
      case 'medium':
        return 'border-l-yellow-500';
      case 'low':
        return 'border-l-green-500';
      default:
        return 'border-l-gray-500';
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString();
  };

  const isOverdue = (dueDate?: string) => {
    if (!dueDate) return false;
    return new Date(dueDate) < new Date();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Task Board</h2>
          <p className="text-gray-600 mt-1">Manage tasks across all projects</p>
        </div>
        
        <div className="flex items-center gap-4">
          {/* Project Filter */}
          <select
            value={selectedProject}
            onChange={(e) => setSelectedProject(e.target.value === 'all' ? 'all' : Number(e.target.value))}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Projects</option>
            {projects.map(project => (
              <option key={project.id} value={project.id}>
                {project.name}
              </option>
            ))}
          </select>
          
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <Plus className="h-4 w-4" />
            Add Task
          </button>
        </div>
      </div>

      {/* Kanban Board */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {columns.map((column) => (
          <div key={column.id} className={`rounded-lg border-2 border-dashed ${column.color} min-h-[500px]`}>
            {/* Column Header */}
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-gray-900">{column.title}</h3>
                <span className="bg-gray-200 text-gray-700 px-2 py-1 rounded-full text-sm">
                  {column.tasks.length}
                </span>
              </div>
            </div>

            {/* Tasks */}
            <div className="p-4 space-y-3">
              {column.tasks.map((task) => (
                <div
                  key={task.id}
                  className={`bg-white rounded-lg shadow-sm border border-gray-200 p-4 cursor-pointer hover:shadow-md transition-shadow border-l-4 ${getPriorityColor(task.priority)}`}
                >
                  {/* Task Header */}
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium text-gray-900 text-sm line-clamp-2">
                      {task.title}
                    </h4>
                    {isOverdue(task.dueDate) && task.status !== TaskStatus.DONE && (
                      <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0 ml-2" />
                    )}
                  </div>

                  {/* Task Description */}
                  {task.description && (
                    <p className="text-xs text-gray-600 mb-3 line-clamp-2">
                      {task.description}
                    </p>
                  )}

                  {/* Task Meta */}
                  <div className="space-y-2">
                    {/* Project */}
                    <div className="flex items-center gap-1">
                      <span className="text-xs text-gray-500">Project:</span>
                      <span className="text-xs font-medium text-gray-700">
                        {task.project?.name || 'Unknown'}
                      </span>
                    </div>

                    {/* Assignee */}
                    {task.assignedTo && (
                      <div className="flex items-center gap-2">
                        <User className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-600">
                          {task.assignedTo.name}
                        </span>
                      </div>
                    )}

                    {/* Due Date */}
                    {task.dueDate && (
                      <div className="flex items-center gap-2">
                        <Calendar className="h-3 w-3 text-gray-400" />
                        <span className={`text-xs ${
                          isOverdue(task.dueDate) && task.status !== TaskStatus.DONE
                            ? 'text-red-600 font-medium'
                            : 'text-gray-600'
                        }`}>
                          {formatDate(task.dueDate)}
                        </span>
                      </div>
                    )}

                    {/* Priority */}
                    <div className="flex items-center justify-between">
                      <span className={`text-xs font-medium ${
                        task.priority === 'critical' ? 'text-red-600' :
                        task.priority === 'high' ? 'text-orange-600' :
                        task.priority === 'medium' ? 'text-yellow-600' :
                        'text-green-600'
                      }`}>
                        {task.priority.toUpperCase()}
                      </span>
                      
                      {/* Progress */}
                      {task.progress > 0 && (
                        <span className="text-xs text-gray-600">
                          {task.progress}%
                        </span>
                      )}
                    </div>

                    {/* Progress Bar */}
                    {task.progress > 0 && (
                      <div className="w-full bg-gray-200 rounded-full h-1">
                        <div 
                          className="bg-blue-600 h-1 rounded-full transition-all duration-300"
                          style={{ width: `${task.progress}%` }}
                        ></div>
                      </div>
                    )}

                    {/* Tags */}
                    {task.tags && task.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {task.tags.slice(0, 2).map((tag, index) => (
                          <span
                            key={index}
                            className="bg-blue-100 text-blue-800 px-1 py-0.5 rounded text-xs"
                          >
                            {tag}
                          </span>
                        ))}
                        {task.tags.length > 2 && (
                          <span className="text-xs text-gray-500">
                            +{task.tags.length - 2} more
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}

              {/* Add Task Button */}
              <button className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-500 hover:border-gray-400 hover:text-gray-600 transition-colors">
                <Plus className="h-5 w-5 mx-auto mb-1" />
                <span className="text-sm">Add Task</span>
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {tasks.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-500 mb-4">No tasks found</div>
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mx-auto">
            <Plus className="h-4 w-4" />
            Create Your First Task
          </button>
        </div>
      )}
    </div>
  );
}
