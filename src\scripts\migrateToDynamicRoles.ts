import { AppDataSource } from '../config/database';
import { RoleSyncService } from '../services/RoleSyncService';
import { RoleConfigurationService } from '../services/RoleConfigurationService';
import { User } from '../server/entities/User';
import { UserRole } from '../types/common';
import logger from '../utils/logger';

/**
 * Migration script to convert from hardcoded roles to dynamic role system
 */
class DynamicRoleMigration {
  private userRepository = AppDataSource.getRepository(User);

  /**
   * Run the complete migration
   */
  async migrate(): Promise<void> {
    try {
      logger.info('Starting migration to dynamic role system...');

      // Step 1: Initialize database connection
      await AppDataSource.initialize();
      logger.info('Database connection established');

      // Step 2: Sync default roles and permissions
      await RoleSyncService.syncRoles();
      logger.info('Default roles and permissions synced');

      // Step 3: Migrate existing users
      await this.migrateExistingUsers();
      logger.info('Existing users migrated');

      // Step 4: Create additional custom roles based on current usage
      await this.createCustomRoles();
      logger.info('Custom roles created');

      // Step 5: Validate migration
      await this.validateMigration();
      logger.info('Migration validation completed');

      logger.info('✅ Migration to dynamic role system completed successfully');
    } catch (error) {
      logger.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Migrate existing users to new role system
   */
  private async migrateExistingUsers(): Promise<void> {
    const users = await this.userRepository.find();
    logger.info(`Found ${users.length} users to migrate`);

    for (const user of users) {
      try {
        const roleMapping = this.mapOldRoleToNew(user.role as any);
        
        if (roleMapping) {
          // Find the new role
          const roles = await RoleConfigurationService.getAllRoles();
          const newRole = roles.find(r => r.name === roleMapping);
          
          if (newRole) {
            // Assign the new role to the user
            await RoleConfigurationService.assignRoleToUser(
              user.id,
              newRole.id,
              {
                isPrimary: true,
                assignedBy: 'migration-script'
              }
            );
            
            logger.info(`Migrated user ${user.email} from ${user.role} to ${roleMapping}`);
          } else {
            logger.warn(`New role '${roleMapping}' not found for user ${user.email}`);
          }
        } else {
          logger.warn(`No mapping found for role '${user.role}' for user ${user.email}`);
        }
      } catch (error) {
        logger.error(`Error migrating user ${user.email}:`, error);
      }
    }
  }

  /**
   * Map old hardcoded roles to new dynamic roles
   */
  private mapOldRoleToNew(oldRole: string): string | null {
    const roleMapping: Record<string, string> = {
      // System roles
      'SYSTEM_ADMIN': 'System Administrator',
      'ADMIN': 'System Administrator',
      
      // IT roles
      'IT_ADMIN': 'IT Administrator',
      'IT_STAFF': 'IT Staff',
      'IT_SUPPORT': 'IT Support',
      'IT_TECHNICIAN': 'IT Technician',
      
      // HR roles
      'HR_ADMIN': 'HR Administrator',
      'HR_STAFF': 'HR Staff',
      'HR_SPECIALIST': 'HR Specialist',
      
      // Management roles
      'CEO': 'System Administrator', // CEO gets full access
      'DEPT_HEAD': 'Department Head',
      'PROJECT_MANAGER': 'Project Manager',
      'FINANCE_MANAGER': 'Finance Manager',
      
      // General roles
      'EMPLOYEE': 'Employee',
      'SENIOR_EMPLOYEE': 'Senior Employee',
      
      // Specialized roles
      'SECURITY_OFFICER': 'Security Officer',
      'VIEW': 'Employee' // View-only becomes basic employee
    };

    return roleMapping[oldRole] || null;
  }

  /**
   * Create additional custom roles based on current system usage
   */
  private async createCustomRoles(): Promise<void> {
    const customRoles = [
      {
        name: 'IT Support',
        description: 'IT support staff with limited technical permissions',
        category: 'operational' as any,
        permissions: [
          'tickets.create',
          'tickets.read',
          'tickets.update'
        ]
      },
      {
        name: 'IT Technician',
        description: 'IT technician with hardware and software support permissions',
        category: 'operational' as any,
        permissions: [
          'tickets.create',
          'tickets.read',
          'tickets.update',
          'tickets.assign'
        ]
      },
      {
        name: 'HR Staff',
        description: 'HR staff with employee management permissions',
        category: 'operational' as any,
        permissions: [
          'employees.create',
          'employees.read',
          'employees.update',
          'tickets.create',
          'tickets.read'
        ]
      },
      {
        name: 'HR Specialist',
        description: 'HR specialist with advanced employee management permissions',
        category: 'operational' as any,
        permissions: [
          'employees.create',
          'employees.read',
          'employees.update',
          'tickets.create',
          'tickets.read',
          'tickets.update'
        ]
      },
      {
        name: 'Department Head',
        description: 'Department head with departmental oversight permissions',
        category: 'departmental' as any,
        permissions: [
          'employees.read',
          'tickets.create',
          'tickets.read',
          'tickets.update'
        ]
      },
      {
        name: 'Project Manager',
        description: 'Project manager with project-specific permissions',
        category: 'departmental' as any,
        permissions: [
          'employees.read',
          'tickets.create',
          'tickets.read',
          'tickets.update',
          'tickets.assign'
        ]
      },
      {
        name: 'Finance Manager',
        description: 'Finance manager with financial oversight permissions',
        category: 'departmental' as any,
        permissions: [
          'employees.read',
          'tickets.create',
          'tickets.read'
        ]
      },
      {
        name: 'Senior Employee',
        description: 'Senior employee with additional permissions',
        category: 'operational' as any,
        permissions: [
          'tickets.create',
          'tickets.read'
        ]
      },
      {
        name: 'Security Officer',
        description: 'Security officer with security-related permissions',
        category: 'specialized' as any,
        permissions: [
          'tickets.create',
          'tickets.read',
          'system.configure'
        ]
      }
    ];

    for (const roleData of customRoles) {
      try {
        // Check if role already exists
        const existingRoles = await RoleConfigurationService.getAllRoles();
        const exists = existingRoles.some(r => r.name === roleData.name);
        
        if (!exists) {
          await RoleConfigurationService.createRole(roleData);
          logger.info(`Created custom role: ${roleData.name}`);
        } else {
          logger.info(`Custom role already exists: ${roleData.name}`);
        }
      } catch (error) {
        logger.error(`Error creating custom role ${roleData.name}:`, error);
      }
    }
  }

  /**
   * Validate the migration
   */
  private async validateMigration(): Promise<void> {
    // Check that all users have role assignments
    const users = await this.userRepository.find();
    const usersWithoutRoles: string[] = [];

    for (const user of users) {
      const assignments = await RoleConfigurationService.getUserRoleAssignments(user.id);
      if (assignments.length === 0) {
        usersWithoutRoles.push(user.email);
      }
    }

    if (usersWithoutRoles.length > 0) {
      logger.warn(`Users without role assignments: ${usersWithoutRoles.join(', ')}`);
    }

    // Check that all roles have permissions
    const roles = await RoleConfigurationService.getAllRoles();
    const rolesWithoutPermissions: string[] = [];

    for (const role of roles) {
      if (!role.permissions || role.permissions.length === 0) {
        rolesWithoutPermissions.push(role.name);
      }
    }

    if (rolesWithoutPermissions.length > 0) {
      logger.warn(`Roles without permissions: ${rolesWithoutPermissions.join(', ')}`);
    }

    logger.info('Migration validation summary:');
    logger.info(`- Total users: ${users.length}`);
    logger.info(`- Users without roles: ${usersWithoutRoles.length}`);
    logger.info(`- Total roles: ${roles.length}`);
    logger.info(`- Roles without permissions: ${rolesWithoutPermissions.length}`);
  }

  /**
   * Rollback migration (if needed)
   */
  async rollback(): Promise<void> {
    logger.warn('Rolling back dynamic role migration...');
    
    try {
      // This would remove all role assignments and revert to old system
      // Implementation depends on your specific rollback requirements
      logger.warn('Rollback functionality not implemented - manual intervention required');
    } catch (error) {
      logger.error('Error during rollback:', error);
      throw error;
    }
  }
}

// Script execution
async function runMigration() {
  const migration = new DynamicRoleMigration();
  
  try {
    await migration.migrate();
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  runMigration();
}

export { DynamicRoleMigration };
