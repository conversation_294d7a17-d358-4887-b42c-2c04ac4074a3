import { AppDataSource } from '../config/database';
import { Asset } from '../entities/Asset';

/**
 * <PERSON><PERSON><PERSON> to drop and recreate the asset table
 * Run with: npx ts-node src/scripts/resetAssetTable.ts
 */
async function resetAssetTable() {
  try {
    console.log('Starting database connection...');
    await AppDataSource.initialize();
    console.log('Database connection established');

    // First, check if asset_maintenance table exists
    console.log('Checking for related tables...');
    const tables = await AppDataSource.query(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME IN ('asset_maintenance', 'asset_assignment')
    `);
    
    console.log('Related tables found:', tables.map((t: any) => t.TABLE_NAME));
    
    // Disable foreign key checks
    console.log('Disabling foreign key checks...');
    await AppDataSource.query('SET FOREIGN_KEY_CHECKS = 0');
    
    // Drop the table if it exists
    console.log('Dropping asset table...');
    await AppDataSource.query('DROP TABLE IF EXISTS assets');
    console.log('Asset table dropped successfully');
    
    // Re-enable foreign key checks
    console.log('Re-enabling foreign key checks...');
    await AppDataSource.query('SET FOREIGN_KEY_CHECKS = 1');

    // Synchronize the entity to recreate the table
    console.log('Recreating asset table...');
    await AppDataSource.synchronize(false);
    console.log('Asset table recreated successfully');

    console.log('Asset table reset complete');
    process.exit(0);
  } catch (error) {
    console.error('Error resetting asset table:', error);
    process.exit(1);
  }
}

// Run the function
resetAssetTable(); 