import api from './api';
import { employeeApi } from './employeeApi';
import { leaveBalancesApi, leaveRequestsApi } from './leaveApi';
import { LeaveStatus } from '../types/attendance';

interface ApiEmployee {
  id: number;
  employeeId?: string; // Employee code like "GC-0001"
  firstName: string;
  lastName: string;
  job?: {
    designation?: string;
    department?: string;
    reportingTo?: string;
    reportingToId?: number;
  };
  contact?: {
    officialEmail?: string;
  };
  designation?: string;
  department?: string;
  reportingToId?: number;
  reportingTo?: string;
  officialEmail?: string;
  email?: string;
  profileImagePath?: string;
}

export interface EmployeePortalProfile {
  id: number;
  employeeId: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  email: string;
  department: string;
  designation: string;
  joinDate: string;
  employmentType: string;
  reportingManager: string;
  location: string;
  phone: string;
  profileImagePath?: string;

  // Personal Information
  gender: string;
  dateOfBirth: string;
  religion?: string;
  cnicNumber: string;
  cnicExpiryDate?: string;
  nationality?: string;
  maritalStatus?: string;
  bloodType?: string;
  fatherName?: string;
  status: string;
  statusDate?: string;
  notes?: string;
  specialInstructions?: string;

  // Employment Details
  employmentStatus?: string;
  employeeLevel?: string;
  probationEndDate?: string;
  noticePeriod?: string;
  reportingTo?: string;
  remoteWorkEligible?: boolean;
  nextReviewDate?: string;
  trainingRequirements?: string;
  workSchedule?: string;
  shiftType?: string;
  project?: string;

  // Compensation & Benefits
  totalSalary?: string;
  salaryTier?: string;
  salaryTrench?: string;
  cashAmount?: string;
  bankAmount?: string;
  paymentMode?: string;

  // Allowances
  foodAllowanceInSalary?: boolean;
  fuelAllowanceInSalary?: boolean;
  numberOfMeals?: string;
  fuelInLiters?: string;
  fuelAmount?: string;
  foodProvidedByCompany?: boolean;

  // Bank Details
  bankName?: string;
  bankBranch?: string;
  accountNumber?: string;
  accountTitle?: string;
  iban?: string;

  // Insurance
  healthInsuranceProvider?: string;
  healthInsurancePolicyNumber?: string;
  healthInsuranceExpiryDate?: string;
  lifeInsuranceProvider?: string;
  lifeInsurancePolicyNumber?: string;
  lifeInsuranceExpiryDate?: string;

  // Accommodation
  accommodationProvidedByEmployer?: boolean;
  accommodationType?: string;
  accommodationAddress?: string;

  // Contact Information
  mobileNumber: string;
  officialNumber?: string;
  officialEmail?: string;
  personalEmail?: string;
  permanentAddress?: string;
  currentAddress?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelationship?: string;
  linkedinProfile?: string;
  otherSocialProfiles?: string;

  // Education
  education: EmployeeEducationData[];

  // Experience
  experience: EmployeeExperienceData[];

  // Family
  family: EmployeeFamilyData[];

  // Skills
  skills: EmployeeSkillsData[];

  // Devices
  devices: EmployeeDeviceData[];

  // Projects
  projects: EmployeeProjectData[];

  // Vehicles
  vehicles: EmployeeVehicleData[];

  // Health Records
  healthRecords: EmployeeHealthData[];
}

export interface EmployeeEducationData {
  id: number;
  educationLevel: string;
  degree: string;
  major?: string;
  institution: string;
  graduationYear: string;
  grade?: string;
}

export interface EmployeeExperienceData {
  id: number;
  companyName: string;
  position: string;
  startDate: string;
  endDate?: string;
  description?: string;
  salary?: string;
  reasonForLeaving?: string;
}

export interface EmployeeFamilyData {
  id: number;
  name: string;
  dateOfBirth?: string;
  relationship: string;
  gender?: string;
  cnic?: string;
  occupation?: string;
  employer?: string;
  contactNumber?: string;
  type: string; // 'spouse', 'child', 'dependent'
}

export interface EmployeeSkillsData {
  id: number;
  professionalSkills?: string;
  technicalSkills?: string;
  certifications?: string;
  languages?: string;
}

export interface EmployeeDeviceData {
  id: number;
  deviceType: string;
  deviceBrand?: string;
  deviceModel?: string;
  serialNumber?: string;
  assignedDate: string;
  returnDate?: string;
  condition?: string;
  notes?: string;
}

export interface EmployeeProjectData {
  id: number;
  projectName: string;
  role: string;
  startDate: string;
  endDate?: string;
  description?: string;
  status: string;
}

export interface EmployeeVehicleData {
  id: number;
  vehicleType: string;
  make?: string;
  model?: string;
  year?: string;
  licensePlate?: string;
  assignedDate: string;
  returnDate?: string;
}

export interface EmployeeHealthData {
  id: number;
  recordType: string;
  recordDate: string;
  description?: string;
  doctorName?: string;
  hospitalName?: string;
  medication?: string;
  notes?: string;
}

export interface EmployeeLeaveData {
  balances: {
    leaveType: string;
    total: number;
    used: number;
    pending: number;
    remaining: number;
    carryForward?: number;
    expiryDate?: string;
  }[];
  recentRequests: {
    id: number;
    type: string;
    from: string;
    to: string;
    status: string;
    days: number;
    reason?: string;
    emergencyContact?: string; // Emergency contact information
    attachments?: string | any[]; // Attachments (JSON string or array)
  }[];
  pendingRequestsCount: number;
  totalDaysUsed: number;
  totalDaysRemaining: number;
}

export interface EmployeePayrollData {
  lastPayment: {
    date: string;
    amount: string;
    currency: string;
  };
  ytdEarnings: string;
  payslips: {
    id: string;
    period: string;
    date: string;
    amount: string;
    status: string;
    downloadUrl?: string;
  }[];
  bankDetails?: {
    bankName: string;
    accountNumber: string;
  };
}

export interface EmployeePerformanceData {
  currentRating?: number;
  lastReviewDate?: string;
  nextReviewDate?: string;
  goals: {
    id: number;
    title: string;
    status: 'pending' | 'in_progress' | 'completed';
    progress: number;
    deadline?: string;
  }[];
  achievements: {
    id: number;
    title: string;
    description: string;
    date: string;
    type: 'award' | 'recognition' | 'milestone';
  }[];
}

export interface EmployeeDocumentData {
  documents: {
    id: number;
    name: string;
    type: string;
    uploadDate: string;
    size: string;
    downloadUrl: string;
  }[];
  totalCount: number;
}

export interface EmployeeAttendanceData {
  summary: {
    currentMonth: {
      totalDays: number;
      presentDays: number;
      absentDays: number;
      leaveDays: number;
      holidayDays: number;
      workingDays: number;
      punctualityScore: number;
    };
    recentAttendance: {
      date: string;
      checkIn: string;
      checkOut: string;
      status: 'present' | 'absent' | 'late' | 'early_out';
      totalHours: number;
    }[];
  };
  allAttendanceRecords?: any[]; // Full attendance records for enhanced view
}

export interface EmployeeBenefitsData {
  salary: {
    totalSalary: string;
    salaryTier: string;
    salaryTrench: string;
    cashAmount: string;
    bankAmount: string;
    paymentMode: string;
  };
  allowances: {
    foodAllowanceInSalary: string;
    fuelAllowanceInSalary: string;
    numberOfMeals: number;
    fuelInLiters: number;
    fuelAmount: string;
    foodProvidedByCompany: boolean;
  };
  insurance: {
    healthInsuranceProvider?: string;
    healthInsurancePolicyNumber?: string;
    healthInsuranceExpiryDate?: string;
    lifeInsuranceProvider?: string;
    lifeInsurancePolicyNumber?: string;
    lifeInsuranceExpiryDate?: string;
  };
  bankDetails: {
    bankName?: string;
    bankBranch?: string;
    accountNumber?: string;
    accountTitle?: string;
    iban?: string;
  };
  accommodation?: {
    accommodationProvidedByEmployer: boolean;
    accommodationType?: string;
    accommodationAddress?: string;
  };
}

export interface EmployeeTrainingData {
  completedTrainings: {
    id: number;
    title: string;
    completionDate: string;
    certificateUrl?: string;
    score?: number;
  }[];
  upcomingTrainings: {
    id: number;
    title: string;
    startDate: string;
    duration: string;
    mandatory: boolean;
  }[];
  trainingRequirements: string[];
}

export interface EmployeeTeamData {
  reportees: {
    id: number;
    employeeCode?: string;
    name: string;
    designation: string;
    department: string;
    email: string;
  }[];
  manager?: {
    id: number;
    employeeCode?: string;
    name: string;
    designation: string;
    department: string;
    email: string;
  };
  colleagues: {
    id: number;
    employeeCode?: string;
    name: string;
    designation: string;
    department: string;
    email: string;
  }[];
}

class EmployeePortalService {
  private baseUrl = '/api';

  /**
   * Get comprehensive employee profile by email with all relations
   */
  async getEmployeeProfile(userEmail: string): Promise<EmployeePortalProfile | null> {
    try {
      // Special case for admin user
      if (userEmail === '<EMAIL>') {
        return {
          id: 999999,
          employeeId: 'ADMIN-001',
          firstName: 'Super',
          middleName: '',
          lastName: 'Administrator',
          email: userEmail,
          department: 'IT Department',
          designation: 'System Administrator',
          joinDate: 'Not specified',
          employmentType: 'Full Time',
          reportingManager: 'Not specified',
          location: 'Head Office',
          phone: 'Not specified',
          profileImagePath: undefined,
          gender: 'Not specified',
          dateOfBirth: 'Not specified',
          religion: 'Not specified',
          cnicNumber: 'Not specified',
          cnicExpiryDate: '',
          nationality: 'Not specified',
          maritalStatus: 'Not specified',
          bloodType: 'Not specified',
          fatherName: 'Not specified',
          status: 'Active',
          statusDate: '',
          notes: '',
          specialInstructions: '',
          mobileNumber: 'Not specified',
          officialNumber: '',
          officialEmail: userEmail,
          personalEmail: '',
          permanentAddress: 'Not specified',
          currentAddress: 'Not specified',
          emergencyContactName: 'Not specified',
          emergencyContactPhone: 'Not specified',
          emergencyContactRelationship: 'Not specified',
          linkedinProfile: '',
          otherSocialProfiles: '',
          education: [],
          experience: [],
          family: [],
          skills: [],
          devices: [],
          projects: [],
          vehicles: [],
          healthRecords: []
        };
      }

      // Step 1: Get all employees to find the employee by email
      const employeesResponse = await employeeApi.getAll();

      if (!employeesResponse.success) {
        console.error('Failed to fetch employees');
        return null;
      }

      const employees = employeesResponse.data || employeesResponse.employees || [];

      // Find employee by email
      const employee = employees.find((emp: any) => {
        const officialEmail = emp.contact?.officialEmail || emp.officialEmail;
        const personalEmail = emp.contact?.personalEmail || emp.personalEmail;
        return officialEmail === userEmail || personalEmail === userEmail || emp.email === userEmail;
      });

      if (!employee) {
        console.error('Employee not found for email:', userEmail);
        return null;
      }

      // Step 2: Fetch detailed employee data with all relations using the comprehensive endpoint
      let detailedEmployee;
      try {
        const detailedResponse = await api.get(`/employees/${employee.id}`);
        detailedEmployee = detailedResponse.data.employee || detailedResponse.data;
        console.log('Fetched detailed employee data:', {
          id: detailedEmployee.id,
          name: `${detailedEmployee.firstName} ${detailedEmployee.lastName}`,
          hasEducation: detailedEmployee.educationEntries?.length > 0,
          hasExperience: detailedEmployee.experienceEntries?.length > 0,
          hasFamily: detailedEmployee.family?.length > 0,
          hasSkills: detailedEmployee.skills?.length > 0,
          hasDevices: detailedEmployee.deviceEntries?.length > 0,
          hasProjects: detailedEmployee.projectEntries?.length > 0,
          hasVehicles: detailedEmployee.vehicles?.length > 0,
          hasHealthRecords: detailedEmployee.healthRecords?.length > 0,
          hasJob: !!detailedEmployee.job,
          hasBenefit: !!detailedEmployee.benefit,
          hasContact: !!detailedEmployee.contact
        });

        // Debug: Log the actual structure of related entities
        if (detailedEmployee.job) {
          console.log('Job data structure:', Object.keys(detailedEmployee.job));
        }
        if (detailedEmployee.benefit) {
          console.log('Benefit data structure:', Object.keys(detailedEmployee.benefit));
          console.log('Benefit data sample:', {
            totalSalary: detailedEmployee.benefit.totalSalary,
            salaryTier: detailedEmployee.benefit.salaryTier,
            bankName: detailedEmployee.benefit.bankName,
            paymentMode: detailedEmployee.benefit.paymentMode
          });
        }
        if (detailedEmployee.contact) {
          console.log('Contact data structure:', Object.keys(detailedEmployee.contact));
        }
      } catch (error) {
        console.warn('Could not fetch detailed employee data, using basic data:', error);
        detailedEmployee = employee;
      }

      // Step 3: Build comprehensive profile from the detailed response
      const profile: EmployeePortalProfile = {
        id: detailedEmployee.id,
        employeeId: detailedEmployee.employeeId || `EMP-${detailedEmployee.id}`,
        firstName: detailedEmployee.firstName || 'Not specified',
        middleName: detailedEmployee.middleName || '',
        lastName: detailedEmployee.lastName || 'Not specified',
        email: detailedEmployee.contact?.officialEmail || detailedEmployee.contact?.personalEmail || detailedEmployee.officialEmail || detailedEmployee.personalEmail || detailedEmployee.email || userEmail,
        department: detailedEmployee.job?.department || detailedEmployee.department || 'Not specified',
        designation: detailedEmployee.job?.designation || detailedEmployee.designation || 'Not specified',
        joinDate: detailedEmployee.job?.joinDate || detailedEmployee.joinDate || 'Not specified',
        employmentType: detailedEmployee.job?.employmentType || detailedEmployee.employmentType || 'Full Time',
        reportingManager: detailedEmployee.job?.reportingTo || detailedEmployee.reportingManager || 'Not specified',
        location: detailedEmployee.job?.location || detailedEmployee.location || 'Not specified',
        phone: detailedEmployee.contact?.mobileNumber || detailedEmployee.mobileNumber || 'Not specified',
        profileImagePath: detailedEmployee.profileImagePath,

        // Personal Information
        gender: detailedEmployee.gender || 'Not specified',
        dateOfBirth: detailedEmployee.dateOfBirth || 'Not specified',
        religion: detailedEmployee.religion || 'Not specified',
        cnicNumber: detailedEmployee.cnicNumber || 'Not specified',
        cnicExpiryDate: detailedEmployee.cnicExpiryDate || '',
        nationality: detailedEmployee.nationality || 'Not specified',
        maritalStatus: detailedEmployee.maritalStatus || 'Not specified',
        bloodType: detailedEmployee.bloodType || 'Not specified',
        fatherName: detailedEmployee.fatherName || 'Not specified',
        status: detailedEmployee.status || detailedEmployee.employmentStatus || 'Active',
        statusDate: detailedEmployee.statusDate || '',
        notes: detailedEmployee.notes || '',
        specialInstructions: detailedEmployee.specialInstructions || '',

        // Contact Information
        mobileNumber: detailedEmployee.contact?.mobileNumber || detailedEmployee.mobileNumber || 'Not specified',
        officialNumber: detailedEmployee.contact?.officialNumber || detailedEmployee.officialNumber || '',
        officialEmail: detailedEmployee.contact?.officialEmail || detailedEmployee.officialEmail || '',
        personalEmail: detailedEmployee.contact?.personalEmail || detailedEmployee.personalEmail || '',
        permanentAddress: detailedEmployee.contact?.permanentAddress || detailedEmployee.permanentAddress || 'Not specified',
        currentAddress: detailedEmployee.contact?.currentAddress || detailedEmployee.currentAddress || 'Not specified',
        emergencyContactName: detailedEmployee.contact?.emergencyContactName || detailedEmployee.emergencyContactName || 'Not specified',
        emergencyContactPhone: detailedEmployee.contact?.emergencyContactPhone || detailedEmployee.emergencyContactPhone || 'Not specified',
        emergencyContactRelationship: detailedEmployee.contact?.emergencyContactRelationship || detailedEmployee.emergencyContactRelationship || 'Not specified',
        linkedinProfile: detailedEmployee.contact?.linkedinProfile || detailedEmployee.linkedinProfile || '',
        otherSocialProfiles: detailedEmployee.contact?.otherSocialProfiles || detailedEmployee.otherSocialProfiles || '',

        // Education - Use educationEntries from backend response
        education: (detailedEmployee.educationEntries || detailedEmployee.education || []).map((edu: any) => ({
          id: edu.id,
          educationLevel: edu.educationLevel || 'Not specified',
          degree: edu.degree || 'Not specified',
          major: edu.major || '',
          institution: edu.institution || 'Not specified',
          graduationYear: edu.graduationYear || 'Not specified',
          grade: edu.grade || ''
        })),

        // Experience - Use experienceEntries from backend response
        experience: (detailedEmployee.experienceEntries || detailedEmployee.experience || []).map((exp: any) => ({
          id: exp.id,
          companyName: exp.companyName || 'Not specified',
          position: exp.position || 'Not specified',
          startDate: exp.startDate || 'Not specified',
          endDate: exp.endDate || '',
          description: exp.description || '',
          salary: exp.salary || '',
          reasonForLeaving: exp.reasonForLeaving || ''
        })),

        // Family - Backend splits family into children and dependents, but we need the original family array
        family: [
          ...(detailedEmployee.children || []),
          ...(detailedEmployee.dependents || []),
          // Add spouse if exists
          ...(detailedEmployee.spouseName ? [{
            id: Date.now(),
            name: detailedEmployee.spouseName,
            dateOfBirth: detailedEmployee.spouseDateOfBirth || '',
            relationship: 'spouse',
            gender: '',
            cnic: detailedEmployee.spouseCNIC || '',
            occupation: detailedEmployee.spouseOccupation || '',
            employer: detailedEmployee.spouseEmployer || '',
            contactNumber: detailedEmployee.spouseContactNumber || '',
            type: 'spouse'
          }] : [])
        ].map((fam: any) => ({
          id: fam.id,
          name: fam.name || 'Not specified',
          dateOfBirth: fam.dateOfBirth || '',
          relationship: fam.relationship || 'Not specified',
          gender: fam.gender || '',
          cnic: fam.cnic || '',
          occupation: fam.occupation || '',
          employer: fam.employer || '',
          contactNumber: fam.contactNumber || '',
          type: fam.type || 'dependent'
        })),

        // Skills - Backend flattens skills to top level fields
        skills: [{
          id: 1,
          professionalSkills: detailedEmployee.professionalSkills || '',
          technicalSkills: detailedEmployee.technicalSkills || '',
          certifications: detailedEmployee.certifications || '',
          languages: detailedEmployee.languages || ''
        }],

        // Devices - Use deviceEntries from backend response
        devices: (detailedEmployee.deviceEntries || detailedEmployee.devices || []).map((device: any) => ({
          id: device.id,
          deviceType: device.deviceType || 'Not specified',
          deviceBrand: device.deviceBrand || '',
          deviceModel: device.deviceModel || '',
          serialNumber: device.serialNumber || '',
          assignedDate: device.assignedDate || 'Not specified',
          returnDate: device.returnDate || '',
          condition: device.condition || '',
          notes: device.notes || ''
        })),

        // Projects - Use projectEntries from backend response
        projects: (detailedEmployee.projectEntries || detailedEmployee.projects || []).map((project: any) => ({
          id: project.id,
          projectName: project.projectName || 'Not specified',
          role: project.role || 'Not specified',
          startDate: project.startDate || 'Not specified',
          endDate: project.endDate || '',
          description: project.description || '',
          status: project.status || 'Unknown'
        })),

        // Vehicles - Backend flattens vehicles to top level fields
        vehicles: detailedEmployee.vehicleType ? [{
          id: 1,
          vehicleType: detailedEmployee.vehicleType || 'Not specified',
          make: '',
          model: detailedEmployee.vehicleMakeModel || '',
          year: '',
          licensePlate: detailedEmployee.registrationNumber || '',
          assignedDate: detailedEmployee.handingOverDate || 'Not specified',
          returnDate: detailedEmployee.returnDate || ''
        }] : [],

        // Health Records - Backend flattens health records to top level fields
        healthRecords: detailedEmployee.medicalHistory ? [{
          id: 1,
          recordType: 'Medical History',
          recordDate: new Date().toISOString().split('T')[0],
          description: detailedEmployee.medicalHistory || '',
          doctorName: '',
          hospitalName: '',
          medication: detailedEmployee.regularMedications || '',
          notes: `Blood Group: ${detailedEmployee.bloodGroup || 'N/A'}, Allergies: ${detailedEmployee.allergies || 'N/A'}, Chronic Conditions: ${detailedEmployee.chronicConditions || 'N/A'}`
        }] : [],

        // Employment Details - Additional fields from job relation
        employmentStatus: detailedEmployee.job?.employmentStatus || detailedEmployee.employmentStatus,
        employeeLevel: detailedEmployee.job?.employeeLevel || detailedEmployee.employeeLevel,
        probationEndDate: detailedEmployee.job?.probationEndDate || detailedEmployee.probationEndDate,
        noticePeriod: detailedEmployee.job?.noticePeriod || detailedEmployee.noticePeriod,
        reportingTo: detailedEmployee.job?.reportingTo || detailedEmployee.reportingTo,
        remoteWorkEligible: detailedEmployee.job?.remoteWorkEligible || detailedEmployee.remoteWorkEligible,
        nextReviewDate: detailedEmployee.job?.nextReviewDate || detailedEmployee.nextReviewDate,
        trainingRequirements: detailedEmployee.job?.trainingRequirements || detailedEmployee.trainingRequirements,
        workSchedule: detailedEmployee.job?.workSchedule || detailedEmployee.workSchedule,
        shiftType: detailedEmployee.job?.shiftType || detailedEmployee.shiftType,
        project: detailedEmployee.job?.project || detailedEmployee.project,

        // Compensation & Benefits - From benefit relation
        totalSalary: detailedEmployee.benefit?.totalSalary || detailedEmployee.totalSalary,
        salaryTier: detailedEmployee.benefit?.salaryTier || detailedEmployee.salaryTier,
        salaryTrench: detailedEmployee.benefit?.salaryTrench || detailedEmployee.salaryTrench,
        cashAmount: detailedEmployee.benefit?.cashAmount || detailedEmployee.cashAmount,
        bankAmount: detailedEmployee.benefit?.bankAmount || detailedEmployee.bankAmount,
        paymentMode: detailedEmployee.benefit?.paymentMode || detailedEmployee.paymentMode,

        // Allowances - From benefit relation
        foodAllowanceInSalary: detailedEmployee.benefit?.foodAllowanceInSalary || detailedEmployee.foodAllowanceInSalary,
        fuelAllowanceInSalary: detailedEmployee.benefit?.fuelAllowanceInSalary || detailedEmployee.fuelAllowanceInSalary,
        numberOfMeals: detailedEmployee.benefit?.numberOfMeals || detailedEmployee.numberOfMeals,
        fuelInLiters: detailedEmployee.benefit?.fuelInLiters || detailedEmployee.fuelInLiters,
        fuelAmount: detailedEmployee.benefit?.fuelAmount || detailedEmployee.fuelAmount,
        foodProvidedByCompany: detailedEmployee.benefit?.foodProvidedByCompany || detailedEmployee.foodProvidedByCompany,

        // Bank Details - From benefit relation
        bankName: detailedEmployee.benefit?.bankName || detailedEmployee.bankName,
        bankBranch: detailedEmployee.benefit?.bankBranch || detailedEmployee.bankBranch,
        accountNumber: detailedEmployee.benefit?.accountNumber || detailedEmployee.accountNumber,
        accountTitle: detailedEmployee.benefit?.accountTitle || detailedEmployee.accountTitle,
        iban: detailedEmployee.benefit?.iban || detailedEmployee.iban,

        // Insurance - From benefit relation
        healthInsuranceProvider: detailedEmployee.benefit?.healthInsuranceProvider || detailedEmployee.healthInsuranceProvider,
        healthInsurancePolicyNumber: detailedEmployee.benefit?.healthInsurancePolicyNumber || detailedEmployee.healthInsurancePolicyNumber,
        healthInsuranceExpiryDate: detailedEmployee.benefit?.healthInsuranceExpiryDate || detailedEmployee.healthInsuranceExpiryDate,
        lifeInsuranceProvider: detailedEmployee.benefit?.lifeInsuranceProvider || detailedEmployee.lifeInsuranceProvider,
        lifeInsurancePolicyNumber: detailedEmployee.benefit?.lifeInsurancePolicyNumber || detailedEmployee.lifeInsurancePolicyNumber,
        lifeInsuranceExpiryDate: detailedEmployee.benefit?.lifeInsuranceExpiryDate || detailedEmployee.lifeInsuranceExpiryDate,

        // Accommodation - From benefit relation
        accommodationProvidedByEmployer: detailedEmployee.benefit?.accommodationProvidedByEmployer || detailedEmployee.accommodationProvidedByEmployer,
        accommodationType: detailedEmployee.benefit?.accommodationType || detailedEmployee.accommodationType,
        accommodationAddress: detailedEmployee.benefit?.accommodationAddress || detailedEmployee.accommodationAddress
      };

      console.log('Built comprehensive profile with:', {
        educationCount: profile.education.length,
        experienceCount: profile.experience.length,
        familyCount: profile.family.length,
        skillsCount: profile.skills.length,
        devicesCount: profile.devices.length,
        projectsCount: profile.projects.length,
        vehiclesCount: profile.vehicles.length,
        healthRecordsCount: profile.healthRecords.length,
        compensation: {
          totalSalary: profile.totalSalary,
          salaryTier: profile.salaryTier,
          bankName: profile.bankName,
          paymentMode: profile.paymentMode,
          foodAllowanceInSalary: profile.foodAllowanceInSalary,
          fuelAllowanceInSalary: profile.fuelAllowanceInSalary
        }
      });

      return profile;

    } catch (error) {
      console.error('Error fetching comprehensive employee profile:', error);
      return null;
    }
  }

  /**
   * Get employee leave data including balances and recent requests
   */
  async getEmployeeLeaveData(employeeId: number, cacheBuster?: number): Promise<EmployeeLeaveData> {
    try {
      console.log('Fetching leave data for employee ID:', employeeId);

      // Validate employee ID
      if (!employeeId || employeeId <= 0) {
        console.warn('Invalid employee ID provided for leave data:', employeeId);
        return this.getEmptyLeaveData();
      }

      // Use the same calculation service that Leave Management uses
      const currentYear = new Date().getFullYear();



      // Use the calculation service endpoint directly with cache busting
      const cacheParam = cacheBuster ? `&_t=${cacheBuster}` : '';
      const calculationResponse = await fetch(`/api/leave-balances/all-active-employees?year=${currentYear}&limit=1000${cacheParam}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      if (!calculationResponse.ok) {
        console.error(`Failed to fetch calculated balances: ${calculationResponse.status}`);
        return this.getEmptyLeaveData();
      }

      const calculationData = await calculationResponse.json();

      if (!calculationData.success || !calculationData.data) {
        console.warn('Calculation service returned no data');
        return this.getEmptyLeaveData();
      }

      // Find the specific employee in the calculated data
      const employee = calculationData.data.find((emp: any) => emp.employeeId === employeeId);

      if (!employee || !employee.balances || employee.balances.length === 0) {
        console.warn(`Employee ${employeeId} not found in calculated balances or has no balances`);
        return this.getEmptyLeaveData();
      }

      const balances = employee.balances;

      // Fetch recent leave requests using the employee-specific endpoint with cache busting
      const requestsResponse = await leaveRequestsApi.getByEmployeeId(employeeId, cacheBuster);
      const requests = requestsResponse.success ? requestsResponse.data || [] : [];
      console.log(`✅ Found ${requests.length} leave requests for employee ${employeeId}`, {
        requests: requests.map((req: any) => ({ 
          id: req.id, 
          status: req.status, 
          leaveType: req.leaveType,
          currentStage: req.currentStage 
        }))
      });

      // Calculate totals
      const totalDaysUsed = balances.reduce((sum: number, balance: any) => sum + (balance.used || 0), 0);
      const totalDaysRemaining = balances.reduce((sum: number, balance: any) => sum + (balance.remaining || 0), 0);
      const pendingRequestsCount = requests.filter((req: any) => req.status === 'PENDING' || req.status === 'pending').length;

      // Get all requests (not just last 5) and transform them properly
      const recentRequests = requests
        .sort((a: any, b: any) => new Date(b.startDate).getTime() - new Date(a.startDate).getTime())
        .map((req: any) => ({
          id: req.id,
          type: req.leaveType,
          from: req.startDate,
          to: req.endDate,
          status: req.status,
          days: req.daysRequested || req.totalDays || 1,
          reason: req.reason,
          emergencyContact: req.emergencyContact, // Include emergency contact
          attachments: req.attachments, // Include attachments
          source: req.source,
          createdBy: req.createdBy,
          employeeName: req.employeeName || (req.employee ? `${req.employee.firstName} ${req.employee.lastName}` : ''),
          originalStartDate: req.originalStartDate,
          originalEndDate: req.originalEndDate,
          originalReason: req.originalReason,
          modificationHistory: req.modificationHistory,
          currentStage: req.currentStage,
          isUrgent: req.isUrgent,
          // Add approval information from approvals array if available
          approvedBy: null, // Will be populated from approvals
          approvedById: null,
          approvedByName: null,
          approvedOn: null,
          appliedOn: req.createdAt || new Date().toISOString(),
          rejectionReason: null, // Will be populated from approvals
          rejectedBy: null,
          rejectedById: null,
          rejectedByName: null,
          rejectedOn: null
        }));

      console.log(`✅ Returning leave data for employee ${employeeId}:`, {
        balancesCount: balances.length,
        recentRequestsCount: recentRequests.length,
        totalDaysUsed,
        totalDaysRemaining,
        actualBalances: balances.map((b: any) => ({
          leaveType: b.leaveType,
          total: b.total || b.totalAllocated || 0,
          used: b.used || 0,
          remaining: b.remaining || 0
        }))
      });

      return {
        balances: balances.map((balance: any) => ({
          leaveType: balance.leaveType,
          total: balance.total || balance.totalAllocated || 0,
          used: balance.used || 0,
          pending: balance.pending || 0,
          remaining: balance.remaining || 0,
          carryForward: balance.carryForward || balance.carriedForward || 0,
          expiryDate: balance.expiryDate
        })),
        recentRequests,
        pendingRequestsCount,
        totalDaysUsed,
        totalDaysRemaining
      };
    } catch (error) {
      console.error('Error fetching employee leave data:', error);
      console.log('Returning empty leave data due to API failure');
      return this.getEmptyLeaveData();
    }
  }

  private getEmptyLeaveData(): EmployeeLeaveData {
    return {
      balances: [],
      recentRequests: [],
      pendingRequestsCount: 0,
      totalDaysUsed: 0,
      totalDaysRemaining: 0
    };
  }

  /**
   * Get employee payroll data
   */
  async getEmployeePayrollData(employeeId: number): Promise<EmployeePayrollData> {
    try {
      // Validate employee ID
      if (!employeeId || employeeId <= 0) {
        console.warn('Invalid employee ID provided for payroll:', employeeId);
        return this.getMockPayrollData();
      }

      // Try to fetch from payroll API
      const response = await api.get(`/employees/${employeeId}/payroll`);

      if (response.data) {
        return response.data;
      }

      // Fallback to mock data
      return this.getMockPayrollData();
    } catch (error) {
      console.error('Error fetching employee payroll data:', error);
      console.log('Returning mock payroll data due to API failure');
      return this.getMockPayrollData();
    }
  }

  private getMockPayrollData(): EmployeePayrollData {
    return {
      lastPayment: {
        date: '2024-01-31',
        amount: '75,000',
        currency: 'PKR'
      },
      ytdEarnings: '825,000 PKR',
      payslips: [
        {
          id: '1',
          period: 'January 2024',
          date: '2024-01-31',
          amount: '75,000',
          status: 'paid'
        },
        {
          id: '2',
          period: 'December 2023',
          date: '2023-12-31',
          amount: '75,000',
          status: 'paid'
        },
        {
          id: '3',
          period: 'November 2023',
          date: '2023-11-30',
          amount: '75,000',
          status: 'paid'
        }
      ]
    };
  }

  /**
   * Get employee performance data
   */
  async getEmployeePerformanceData(employeeId: number): Promise<EmployeePerformanceData> {
    try {
      // Validate employee ID
      if (!employeeId || employeeId <= 0) {
        console.warn('Invalid employee ID provided for performance:', employeeId);
        return this.getMockPerformanceData();
      }

      // Try to fetch from performance API
      const response = await api.get(`/employees/${employeeId}/performance`);

      if (response.data) {
        return response.data;
      }

      // Return mock data if no response
      return this.getMockPerformanceData();
    } catch (error) {
      console.error('Error fetching employee performance data:', error);
      console.log('Returning mock performance data due to API failure');
      return this.getMockPerformanceData();
    }
  }

  private getMockPerformanceData(): EmployeePerformanceData {
    return {
      currentRating: 4.2,
      lastReviewDate: '2023-12-15',
      nextReviewDate: '2024-06-15',
      goals: [
        {
          id: 1,
          title: 'Complete Project Alpha',
          status: 'in_progress',
          progress: 75,
          deadline: '2024-03-31'
        },
        {
          id: 2,
          title: 'Improve Team Collaboration',
          status: 'completed',
          progress: 100,
          deadline: '2024-01-31'
        },
        {
          id: 3,
          title: 'Learn New Technology Stack',
          status: 'pending',
          progress: 25,
          deadline: '2024-06-30'
        }
      ],
      achievements: [
        {
          id: 1,
          title: 'Employee of the Month',
          description: 'Outstanding performance in Q4 2023',
          date: '2023-12-01',
          type: 'award'
        },
        {
          id: 2,
          title: 'Project Delivery Excellence',
          description: 'Delivered Project Beta ahead of schedule',
          date: '2023-11-15',
          type: 'recognition'
        }
      ]
    };
  }

  /**
   * Get employee documents
   */
  async getEmployeeDocuments(employeeId: number): Promise<EmployeeDocumentData> {
    try {
      // Validate employee ID
      if (!employeeId || employeeId <= 0) {
        console.warn('Invalid employee ID provided for documents:', employeeId);
        return this.getMockDocumentsData();
      }

      const response = await api.get(`/employees/${employeeId}/documents`);

      if (response.data && response.data.documents) {
        return {
          documents: response.data.documents.map((doc: any) => ({
            id: doc.id,
            name: doc.fileName || doc.name,
            type: doc.documentType || 'Document',
            uploadDate: doc.uploadDate || doc.createdAt,
            size: doc.fileSize || 'N/A',
            downloadUrl: doc.filePath || '#'
          })),
          totalCount: response.data.documents.length
        };
      }

      return this.getMockDocumentsData();
    } catch (error) {
      console.error('Error fetching employee documents:', error);
      console.log('Returning mock documents data due to API failure');
      return this.getMockDocumentsData();
    }
  }

  private getMockDocumentsData(): EmployeeDocumentData {
    return {
      documents: [
        {
          id: 1,
          name: 'Employment Contract.pdf',
          type: 'Contract',
          uploadDate: '2023-06-15',
          size: '2.5 MB',
          downloadUrl: '#'
        },
        {
          id: 2,
          name: 'Employee Handbook.pdf',
          type: 'Policy',
          uploadDate: '2023-06-16',
          size: '1.8 MB',
          downloadUrl: '#'
        },
        {
          id: 3,
          name: 'CNIC Copy.jpg',
          type: 'ID Document',
          uploadDate: '2023-06-15',
          size: '800 KB',
          downloadUrl: '#'
        },
        {
          id: 4,
          name: 'Educational Certificates.pdf',
          type: 'Education',
          uploadDate: '2023-06-15',
          size: '3.2 MB',
          downloadUrl: '#'
        }
      ],
      totalCount: 4
    };
  }

  /**
   * Update employee profile information
   */
  async updateEmployeeProfile(employeeId: number, profileData: Partial<EmployeePortalProfile>): Promise<boolean> {
    try {
      const response = await api.put(`/employees/${employeeId}`, profileData);
      return response.data?.success || false;
    } catch (error) {
      console.error('Error updating employee profile:', error);
      return false;
    }
  }

  /**
   * Submit leave request - now uses unified API with enhanced fields
   */
  async submitLeaveRequest(employeeId: number, leaveData: {
    leaveType: string;
    startDate: string;
    endDate: string;
    reason: string;
    totalDays: number;
    emergencyContact?: string;
    attachments?: File[];
  }): Promise<boolean> {
    try {
      // Use unified leaveRequestsApi for consistent behavior
      const { leaveRequestsApi } = await import('./leaveApi');

      const leaveRequest = {
        employeeId,
        employeeName: '', // Will be populated by backend
        leaveType: leaveData.leaveType,
        startDate: leaveData.startDate,
        endDate: leaveData.endDate,
        reason: leaveData.reason,
        status: LeaveStatus.PENDING,
        daysRequested: leaveData.totalDays,
        emergencyContact: leaveData.emergencyContact, // Include emergency contact
        createdAt: new Date().toISOString(), // Required by LeaveRequest interface
        // Only include fields that exist in the LeaveRequest interface
        // Removed extra fields that don't exist in database schema
      };

      const response = await leaveRequestsApi.create(leaveRequest);
      return response.success;
    } catch (error) {
      console.error('Error submitting leave request:', error);
      return false;
    }
  }

  /**
   * Update existing leave request
   */
  async updateLeaveRequest(requestId: number, leaveData: {
    leaveType: string;
    startDate: string;
    endDate: string;
    reason: string;
    emergencyContact?: string;
    attachments?: File[];
  }): Promise<boolean> {
    try {
      const { leaveRequestsApi } = await import('./leaveApi');

      const response = await leaveRequestsApi.updateLeaveRequest(requestId.toString(), leaveData);
      return response.success;
    } catch (error) {
      console.error('Error updating leave request:', error);
      return false;
    }
  }

  /**
   * Get comprehensive employee attendance data
   */
  async getEmployeeAttendanceData(employeeId: number): Promise<EmployeeAttendanceData> {
    try {
      console.log('Fetching attendance data for employee ID:', employeeId);

      // Validate employee ID
      if (!employeeId || employeeId <= 0) {
        console.warn('Invalid employee ID provided:', employeeId);
        return this.getMockAttendanceData();
      }

      // Try to fetch from attendance API
      const attendanceResponse = await api.get(`/api/attendance/employee/${employeeId}`);
      const attendanceRecords = attendanceResponse.data?.data || attendanceResponse.data || [];

      console.log('Attendance records fetched:', attendanceRecords.length);

      // Return the full attendance records for the Employee Portal
      return {
        summary: {
          currentMonth: {
            totalDays: attendanceRecords.length,
            presentDays: attendanceRecords.filter((a: any) => a.status === 'present' || a.status === 'work_from_home').length,
            absentDays: attendanceRecords.filter((a: any) => a.status === 'absent').length,
            leaveDays: attendanceRecords.filter((a: any) => a.status === 'leave').length,
            holidayDays: 0,
            workingDays: attendanceRecords.length,
            punctualityScore: 85
          },
          recentAttendance: attendanceRecords.slice(0, 10).map((record: any) => ({
            date: record.date,
            checkIn: record.checkInTime || 'N/A',
            checkOut: record.checkOutTime || 'N/A',
            status: record.status || 'present',
            totalHours: record.workHours || 0
          }))
        },
        allAttendanceRecords: attendanceRecords
      };
    } catch (error) {
      console.error('Error fetching employee attendance data:', error);
      console.log('Returning mock attendance data due to API failure');
      return this.getMockAttendanceData();
    }
  }

  private getMockAttendanceData(): EmployeeAttendanceData {
    // Generate mock attendance data for demonstration
    const mockAttendanceRecords = [];
    const today = new Date();

    for (let i = 30; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);

      // Skip weekends
      if (date.getDay() === 0 || date.getDay() === 6) continue;

      const status = Math.random() > 0.9 ? 'absent' : Math.random() > 0.8 ? 'late' : 'present';
      const checkInTime = status === 'absent' ? '-' : `0${8 + Math.floor(Math.random() * 2)}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`;
      const checkOutTime = status === 'absent' ? '-' : `1${7 + Math.floor(Math.random() * 2)}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`;
      const workHours = status === 'absent' ? 0 : 7.5 + Math.random() * 2;

      mockAttendanceRecords.push({
        id: i + 1,
        employeeId: 1,
        employeeName: 'Mock Employee',
        date: date.toISOString().split('T')[0],
        checkInTime,
        checkOutTime,
        status,
        workHours,
        overtime: workHours > 8 ? workHours - 8 : 0,
        location: 'Office',
        shift: 1,
        shiftName: 'Morning Shift',
        department: 'IT',
        isRemote: false
      });
    }

    return {
      summary: {
        currentMonth: {
          totalDays: mockAttendanceRecords.length,
          presentDays: mockAttendanceRecords.filter(a => a.status === 'present').length,
          absentDays: mockAttendanceRecords.filter(a => a.status === 'absent').length,
          leaveDays: 0,
          holidayDays: 0,
          workingDays: mockAttendanceRecords.length,
          punctualityScore: 85
        },
        recentAttendance: mockAttendanceRecords.slice(0, 10).map(record => ({
          date: record.date,
          checkIn: record.checkInTime,
          checkOut: record.checkOutTime,
          status: record.status as any,
          totalHours: record.workHours
        }))
      },
      allAttendanceRecords: mockAttendanceRecords
    };
  }

  /**
   * Get employee benefits and salary data
   */
  async getEmployeeBenefitsData(employeeId: number): Promise<EmployeeBenefitsData> {
    try {
      console.log('Fetching benefits data for employee ID:', employeeId);

      // Validate employee ID
      if (!employeeId || employeeId <= 0) {
        console.warn('Invalid employee ID provided for benefits:', employeeId);
        return this.getMockBenefitsData();
      }

      // Get employee details with benefits info
      const response = await employeeApi.getById(employeeId);

      if (!response.success || !response.data) {
        throw new Error('Employee data not found');
      }

      const employee = response.data as any;
      const benefit = employee.benefit || {};

      return {
        salary: {
          totalSalary: benefit.totalSalary || '0',
          salaryTier: benefit.salaryTier || 'N/A',
          salaryTrench: benefit.salaryTrench || 'N/A',
          cashAmount: benefit.cashAmount || '0',
          bankAmount: benefit.bankAmount || '0',
          paymentMode: benefit.paymentMode || 'N/A'
        },
        allowances: {
          foodAllowanceInSalary: benefit.foodAllowanceInSalary || '0',
          fuelAllowanceInSalary: benefit.fuelAllowanceInSalary || '0',
          numberOfMeals: benefit.numberOfMeals || 0,
          fuelInLiters: benefit.fuelInLiters || 0,
          fuelAmount: benefit.fuelAmount || '0',
          foodProvidedByCompany: benefit.foodProvidedByCompany || false
        },
        insurance: {
          healthInsuranceProvider: benefit.healthInsuranceProvider,
          healthInsurancePolicyNumber: benefit.healthInsurancePolicyNumber,
          healthInsuranceExpiryDate: benefit.healthInsuranceExpiryDate,
          lifeInsuranceProvider: benefit.lifeInsuranceProvider,
          lifeInsurancePolicyNumber: benefit.lifeInsurancePolicyNumber,
          lifeInsuranceExpiryDate: benefit.lifeInsuranceExpiryDate
        },
        bankDetails: {
          bankName: benefit.bankName,
          bankBranch: benefit.bankBranch,
          accountNumber: benefit.accountNumber,
          accountTitle: benefit.accountTitle,
          iban: benefit.iban
        },
        accommodation: {
          accommodationProvidedByEmployer: benefit.accommodationProvidedByEmployer || false,
          accommodationType: benefit.accommodationType,
          accommodationAddress: benefit.accommodationAddress
        }
      };
    } catch (error) {
      console.error('Error fetching employee benefits data:', error);
      console.log('Returning mock benefits data due to API failure');
      return this.getMockBenefitsData();
    }
  }

  private getMockBenefitsData(): EmployeeBenefitsData {
    return {
      salary: {
        totalSalary: '75,000',
        salaryTier: 'Mid-Level',
        salaryTrench: 'T2',
        cashAmount: '25,000',
        bankAmount: '50,000',
        paymentMode: 'Bank Transfer'
      },
      allowances: {
        foodAllowanceInSalary: '5,000',
        fuelAllowanceInSalary: '8,000',
        numberOfMeals: 2,
        fuelInLiters: 30,
        fuelAmount: '8,000',
        foodProvidedByCompany: true
      },
      insurance: {
        healthInsuranceProvider: 'EFU Life Insurance',
        healthInsurancePolicyNumber: 'HI-2024-001',
        healthInsuranceExpiryDate: '2024-12-31',
        lifeInsuranceProvider: 'State Life Insurance',
        lifeInsurancePolicyNumber: 'LI-2024-001',
        lifeInsuranceExpiryDate: '2025-12-31'
      },
      bankDetails: {
        bankName: 'Habib Bank Limited',
        bankBranch: 'Main Branch',
        accountNumber: '**********',
        accountTitle: 'John Doe',
        iban: 'PK36HABB000000**********'
      },
      accommodation: {
        accommodationProvidedByEmployer: false,
        accommodationType: '',
        accommodationAddress: ''
      }
    };
  }

  /**
   * Get employee training data
   */
  async getEmployeeTrainingData(employeeId: number): Promise<EmployeeTrainingData> {
    try {
      console.log('Fetching training data for employee ID:', employeeId);

      // Validate employee ID
      if (!employeeId || employeeId <= 0) {
        console.warn('Invalid employee ID provided for training:', employeeId);
        return this.getMockTrainingData();
      }

      // Get employee details with training requirements
      const response = await employeeApi.getById(employeeId);

      if (!response.success || !response.data) {
        throw new Error('Employee data not found');
      }

      const employee = response.data as any;
      const trainingRequirements = (employee as any).job?.trainingRequirements
        ? (employee as any).job.trainingRequirements.split(',').map((req: string) => req.trim()).filter(Boolean)
        : [];

      // For now, return structured data with training requirements from job
      // TODO: Integrate with actual training management system when available
      return {
        completedTrainings: [],
        upcomingTrainings: [],
        trainingRequirements
      };
    } catch (error) {
      console.error('Error fetching employee training data:', error);
      console.log('Returning mock training data due to API failure');
      return this.getMockTrainingData();
    }
  }

  private getMockTrainingData(): EmployeeTrainingData {
    return {
      completedTrainings: [
        {
          id: 1,
          title: 'IT Security Awareness',
          completionDate: '2023-11-15',
          certificateUrl: '#',
          score: 92
        },
        {
          id: 2,
          title: 'Microsoft Office Suite',
          completionDate: '2023-10-20',
          certificateUrl: '#',
          score: 87
        }
      ],
      upcomingTrainings: [
        {
          id: 3,
          title: 'Project Management Fundamentals',
          startDate: '2024-02-15',
          duration: '3 days',
          mandatory: true
        },
        {
          id: 4,
          title: 'Communication Skills Workshop',
          startDate: '2024-03-01',
          duration: '2 days',
          mandatory: false
        }
      ],
      trainingRequirements: [
        'IT Security Training',
        'Customer Service Excellence',
        'Time Management'
      ]
    };
  }

  /**
   * Get employee team data (manager, reportees, colleagues)
   */
  async getEmployeeTeamData(employeeId: number): Promise<EmployeeTeamData | null> {
    try {
      console.log('Fetching team data for employee ID:', employeeId);

      // Validate employee ID
      if (!employeeId || employeeId <= 0) {
        console.warn('Invalid employee ID provided for team data:', employeeId);
        return null;
      }

      // First, check if this employee is a manager (has direct reports)
      const managerTeamData = await this.getTeamMembers(employeeId);

      // If employee has direct reports, they are a manager
      if (managerTeamData && managerTeamData.reportees && managerTeamData.reportees.length > 0) {
        console.log(`Employee ${employeeId} is a manager with ${managerTeamData.reportees.length} direct reports`);
        return managerTeamData;
      }

      // If not a manager, fetch all employees to find the employee's manager and colleagues
      const employeesResponse = await employeeApi.getAll();

      if (!employeesResponse.success) {
        console.error('Failed to fetch employees for team data');
        return null;
      }

      const employees: ApiEmployee[] = employeesResponse.data || employeesResponse.employees || [];

      // Find the current employee
      const currentEmployee = employees.find((emp: ApiEmployee) => emp.id === employeeId);

      if (!currentEmployee) {
        console.error(`Employee with ID ${employeeId} not found`);
        return null;
      }

      // Find the employee's manager
      const managerReportsToId = currentEmployee.job?.reportingToId || currentEmployee.reportingToId;
      const manager = employees.find((emp: ApiEmployee) => emp.id === managerReportsToId);

      // Find colleagues (employees who report to the same manager)
      const colleagues = employees
        .filter((emp: ApiEmployee) => {
          if (emp.id === employeeId) return false; // Exclude self

          const empReportsToId = emp.job?.reportingToId || emp.reportingToId;
          return empReportsToId === managerReportsToId && managerReportsToId; // Only include if there's a common manager
        })
        .map((emp: ApiEmployee) => ({
          id: emp.id,
          name: `${emp.firstName || ''} ${emp.lastName || ''}`.trim(),
          designation: emp.job?.designation || emp.designation || 'Employee',
          department: emp.job?.department || emp.department || 'N/A',
          email: emp.contact?.officialEmail || emp.officialEmail || emp.email || ''
        }));

      // If the employee is a manager, get their direct reports
      const reportees = managerTeamData?.reportees || [];

      return {
        reportees,
        manager: manager ? {
          id: manager.id,
          name: `${manager.firstName || ''} ${manager.lastName || ''}`.trim(),
          designation: manager.job?.designation || manager.designation || 'Manager',
          department: manager.job?.department || manager.department || 'N/A',
          email: manager.contact?.officialEmail || manager.officialEmail || manager.email || ''
        } : undefined,
        colleagues
      };
    } catch (error) {
      console.error('Error fetching employee team data:', error);
      return null;
    }
  }

  private getMockTeamData(): EmployeeTeamData {
    return {
      reportees: [
        {
          id: 101,
          name: 'Sarah Johnson',
          designation: 'Junior Developer',
          department: 'IT',
          email: '<EMAIL>'
        },
        {
          id: 102,
          name: 'Ahmed Khan',
          designation: 'QA Analyst',
          department: 'IT',
          email: '<EMAIL>'
        }
      ],
      manager: {
        id: 50,
        name: 'Michael Smith',
        designation: 'IT Manager',
        department: 'IT',
        email: '<EMAIL>'
      },
      colleagues: [
        {
          id: 103,
          name: 'Lisa Brown',
          designation: 'Senior Developer',
          department: 'IT',
          email: '<EMAIL>'
        },
        {
          id: 104,
          name: 'David Wilson',
          designation: 'System Administrator',
          department: 'IT',
          email: '<EMAIL>'
        },
        {
          id: 105,
          name: 'Emily Davis',
          designation: 'Business Analyst',
          department: 'IT',
          email: '<EMAIL>'
        }
      ]
    };
  }

  /**
   * Enhanced payroll data fetching with real employee benefit data
   */
  async getEmployeePayrollDataEnhanced(employeeId: number): Promise<EmployeePayrollData> {
    try {
      // Get employee details with benefits info
      const response = await employeeApi.getById(employeeId);

      if (!response.success || !response.data) {
        throw new Error('Employee data not found');
      }

      const employee = response.data as any;
      const benefit = employee.benefit || {};

      return {
        lastPayment: {
          date: new Date().toISOString().split('T')[0],
          amount: benefit.totalSalary || '0',
          currency: 'PKR'
        },
        ytdEarnings: (parseFloat(benefit.totalSalary || '0') * 12).toString(),
        payslips: [], // TODO: Integrate with actual payroll system
        bankDetails: {
          bankName: benefit.bankName || 'N/A',
          accountNumber: benefit.accountNumber || 'N/A'
        }
      };
    } catch (error) {
      console.error('Error fetching enhanced payroll data:', error);
      return {
        lastPayment: {
          date: 'N/A',
          amount: 'N/A',
          currency: 'PKR'
        },
        ytdEarnings: 'N/A',
        payslips: []
      };
    }
  }

  /**
   * Get team members for a manager
   */
  async getTeamMembers(managerId: number): Promise<EmployeeTeamData | null> {
    try {
      console.log('Fetching team members for manager ID:', managerId);

      // Validate manager ID
      if (!managerId || managerId <= 0) {
        console.warn('Invalid manager ID provided for team data:', managerId);
        return null;
      }

      // Fetch all employees to find team members
      const employeesResponse = await employeeApi.getAll();

      if (!employeesResponse.success) {
        console.error('Failed to fetch employees for team data');
        return null;
      }

      const employees: ApiEmployee[] = employeesResponse.data || employeesResponse.employees || [];

      // Find direct reports (employees who report to this manager)
      const reportees = employees
        .filter((emp: ApiEmployee) => {
          // Check if the employee reports to this manager
          // First try reportingToId (if exists)
          const reportsToId = emp.job?.reportingToId || emp.reportingToId;
          if (reportsToId === managerId) {
            return true;
          }
          
          // If no reportingToId, check reportingTo string field
          const reportingTo = emp.job?.reportingTo || emp.reportingTo;
          if (reportingTo) {
            // Find manager by name
            const manager = employees.find((m: ApiEmployee) => m.id === managerId);
            if (manager) {
              const managerName = `${manager.firstName || ''} ${manager.lastName || ''}`.trim();
              return reportingTo.toLowerCase().includes(managerName.toLowerCase()) ||
                     managerName.toLowerCase().includes(reportingTo.toLowerCase());
            }
          }
          
          return false;
        })
        .map((emp: ApiEmployee) => ({
          id: emp.id,
          employeeCode: emp.employeeId || `EMP-${emp.id}`, // Use employeeId or fallback to EMP-{id}
          name: `${emp.firstName || ''} ${emp.lastName || ''}`.trim(),
          designation: emp.job?.designation || emp.designation || 'Employee',
          department: emp.job?.department || emp.department || 'N/A',
          email: emp.contact?.officialEmail || emp.officialEmail || emp.email || ''
        }));

      // Find the manager
      const manager = employees.find((emp: ApiEmployee) => emp.id === managerId);

      if (!manager) {
        console.warn(`Manager with ID ${managerId} not found`);
        return {
          reportees,
          colleagues: []
        };
      }

      // Find colleagues (other employees who report to this manager, excluding direct reports)
      const colleagues = employees
        .filter((emp: ApiEmployee) => {
          if (emp.id === managerId) return false; // Exclude the manager
          
          // Check if this employee reports to the same manager as the others
          // First try reportingToId (if exists)
          const reportsToId = emp.job?.reportingToId || emp.reportingToId;
          if (reportsToId === managerId && !reportees.some(r => r.id === emp.id)) {
            return true;
          }
          
          // If no reportingToId, check reportingTo string field
          const reportingTo = emp.job?.reportingTo || emp.reportingTo;
          if (reportingTo && !reportees.some(r => r.id === emp.id)) {
            // Find manager by name
            const manager = employees.find((m: ApiEmployee) => m.id === managerId);
            if (manager) {
              const managerName = `${manager.firstName || ''} ${manager.lastName || ''}`.trim();
              return reportingTo.toLowerCase().includes(managerName.toLowerCase()) ||
                     managerName.toLowerCase().includes(reportingTo.toLowerCase());
            }
          }
          
          return false;
        })
        .map((emp: ApiEmployee) => ({
          id: emp.id,
          employeeCode: emp.employeeId || `EMP-${emp.id}`, // Use employeeId or fallback to EMP-{id}
          name: `${emp.firstName || ''} ${emp.lastName || ''}`.trim(),
          designation: emp.job?.designation || emp.designation || 'Employee',
          department: emp.job?.department || emp.department || 'N/A',
          email: emp.contact?.officialEmail || emp.officialEmail || emp.email || ''
        }));

      return {
        reportees,
        manager: {
          id: manager.id,
          employeeCode: manager.employeeId || `EMP-${manager.id}`, // Use employeeId or fallback to EMP-{id}
          name: `${manager.firstName || ''} ${manager.lastName || ''}`.trim(),
          designation: manager.job?.designation || manager.designation || 'Manager',
          department: manager.job?.department || manager.department || 'N/A',
          email: manager.contact?.officialEmail || manager.officialEmail || manager.email || ''
        },
        colleagues
      };
    } catch (error) {
      console.error('Error fetching team members:', error);
      return null;
    }
  }

  /**
   * Get employee attendance for a specific date range
   */
  async getEmployeeAttendance(employeeId: number, startDate: string, endDate: string): Promise<{ records: any[] } | null> {
    try {
      console.log(`Fetching attendance for employee ${employeeId} from ${startDate} to ${endDate}`);

      // Validate employee ID
      if (!employeeId || employeeId <= 0) {
        console.warn('Invalid employee ID provided for attendance data:', employeeId);
        return null;
      }

      // Fetch attendance records from the API
      const response = await fetch(`/api/attendance/employee/${employeeId}?startDate=${startDate}&endDate=${endDate}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      if (!response.ok) {
        console.error(`Failed to fetch attendance: ${response.status}`);
        return null;
      }

      const data = await response.json();

      if (!data.success) {
        console.warn('Attendance API returned error:', data.message);
        return null;
      }

      return {
        records: data.data || []
      };
    } catch (error) {
      console.error('Error fetching employee attendance:', error);
      return null;
    }
  }

  /**
   * Approve a leave request
   */
  async approveLeaveRequest(leaveId: number): Promise<{ success: boolean; message?: string }> {
    try {
      console.log('Approving leave request:', leaveId);

      // Call the leave API to approve the request
      const response = await leaveRequestsApi.approve(leaveId);

      return {
        success: response.success,
        message: response.message
      };
    } catch (error) {
      console.error('Error approving leave request:', error);
      return {
        success: false,
        message: 'An error occurred while approving the leave request'
      };
    }
  }

  /**
   * Reject a leave request
   */
  async rejectLeaveRequest(leaveId: number, reason: string): Promise<{ success: boolean; message?: string }> {
    try {
      const token = localStorage.getItem('authToken');
      if (!token) {
        return { success: false, message: 'Authentication token not found' };
      }

      const response = await fetch(`${this.baseUrl}/leave-requests/${leaveId}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ reason })
      });

      const data = await response.json();
      return { success: data.success, message: data.message };
    } catch (error) {
      console.error('Error rejecting leave request:', error);
      return { success: false, message: 'Failed to reject leave request' };
    }
  }

  async cancelLeaveRequest(leaveId: number, reason?: string): Promise<{ success: boolean; message?: string }> {
    try {
      const token = localStorage.getItem('authToken');
      if (!token) {
        return { success: false, message: 'Authentication token not found' };
      }

      const response = await fetch(`${this.baseUrl}/leave-requests/${leaveId}/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ reason: reason || 'Cancelled by employee' })
      });

      const data = await response.json();
      return { success: data.success, message: data.message };
    } catch (error) {
      console.error('Error cancelling leave request:', error);
      return { success: false, message: 'Failed to cancel leave request' };
    }
  }
}

export const employeePortalService = new EmployeePortalService();
export default employeePortalService;