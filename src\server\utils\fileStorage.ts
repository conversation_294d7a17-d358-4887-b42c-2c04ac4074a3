import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

// Base directory for file uploads
const UPLOAD_DIR = path.join(__dirname, '../../uploads');

// Ensure the upload directory exists
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

/**
 * Uploads a file to local storage
 * @param file The file buffer
 * @param folderName Optional subfolder name
 * @param originalName Original file name for extension
 * @returns The path to the saved file (relative to uploads directory)
 */
export const uploadToStorage = async (
  file: Buffer, 
  folderName: string = 'default',
  originalName: string
): Promise<string> => {
  // Create folder if it doesn't exist
  const folderPath = path.join(UPLOAD_DIR, folderName);
  if (!fs.existsSync(folderPath)) {
    fs.mkdirSync(folderPath, { recursive: true });
  }
  
  // Generate unique filename
  const fileExt = path.extname(originalName);
  const fileName = `${uuidv4()}${fileExt}`;
  const filePath = path.join(folderPath, fileName);
  
  // Write file to disk
  await fs.promises.writeFile(filePath, file);
  
  // Return relative path
  return path.join(folderName, fileName);
};

/**
 * Gets the URL for a file
 * @param filePath Relative file path
 * @returns Full URL to access the file
 */
export const getFileUrl = (filePath: string): string => {
  // In a real app, this would include the domain and proper URL structure
  return `/uploads/${filePath}`;
};

/**
 * Deletes a file from storage
 * @param filePath Relative file path
 * @returns Success status
 */
export const deleteFile = async (filePath: string): Promise<boolean> => {
  try {
    const fullPath = path.join(UPLOAD_DIR, filePath);
    if (fs.existsSync(fullPath)) {
      await fs.promises.unlink(fullPath);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error deleting file:', error);
    return false;
  }
}; 