import React from 'react';
import { Wifi, WifiOff, Database, Cloud, RefreshCw } from 'lucide-react';
import { useApi } from '../../context/ApiProvider';

interface ApiStatusProps {
  className?: string;
  showToggle?: boolean;
}

const ApiStatus: React.FC<ApiStatusProps> = ({ 
  className = '',
  showToggle = true 
}) => {
  const { isOnline, useMockData, toggleMockData } = useApi();

  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      {/* Connection Status */}
      <div className="flex items-center space-x-1">
        {isOnline ? (
          <Wifi className="h-4 w-4 text-green-500" />
        ) : (
          <WifiOff className="h-4 w-4 text-red-500" />
        )}
        <span className={`text-xs font-medium ${
          isOnline ? 'text-green-600' : 'text-red-600'
        }`}>
          {isOnline ? 'Online' : 'Offline'}
        </span>
      </div>

      {/* Data Source Status */}
      <div className="flex items-center space-x-1">
        {useMockData ? (
          <Database className="h-4 w-4 text-blue-500" />
        ) : (
          <Cloud className="h-4 w-4 text-purple-500" />
        )}
        <span className={`text-xs font-medium ${
          useMockData ? 'text-blue-600' : 'text-purple-600'
        }`}>
          {useMockData ? 'Mock Data' : 'Live API'}
        </span>
      </div>

      {/* Toggle Button */}
      {showToggle && isOnline && (
        <button
          onClick={toggleMockData}
          className="flex items-center space-x-1 px-2 py-1 text-xs rounded border hover:bg-gray-50 transition-colors"
          title={`Switch to ${useMockData ? 'Live API' : 'Mock Data'}`}
        >
          <RefreshCw className="h-3 w-3" />
          <span>Toggle</span>
        </button>
      )}
    </div>
  );
};

// Compact version for small spaces
export const CompactApiStatus: React.FC<{ className?: string }> = ({ 
  className = '' 
}) => {
  const { isOnline, useMockData } = useApi();

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      <div className={`h-2 w-2 rounded-full ${
        isOnline ? 'bg-green-500' : 'bg-red-500'
      }`} />
      <div className={`h-2 w-2 rounded-full ${
        useMockData ? 'bg-blue-500' : 'bg-purple-500'
      }`} />
    </div>
  );
};

export default ApiStatus; 