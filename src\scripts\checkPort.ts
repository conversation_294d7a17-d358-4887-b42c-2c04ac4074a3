import * as net from 'net';

/**
 * <PERSON>ript to check if port 5000 is already in use
 * Run with: npx ts-node src/scripts/checkPort.ts
 */
async function checkPort(port: number): Promise<boolean> {
  return new Promise((resolve) => {
    const server = net.createServer();
    
    server.once('error', (err: any) => {
      if (err.code === 'EADDRINUSE') {
        console.error(`❌ Port ${port} is already in use!`);
        console.error('This could be caused by:');
        console.error('1. Another instance of the server is already running');
        console.error('2. Another application is using this port');
        console.error('\nTo fix this:');
        console.error(`- Stop any other applications using port ${port}`);
        console.error(`- Change the PORT in .env to a different value (e.g., PORT=5001)`);
        console.error(`- If you change the port, also update the frontend API configuration in src/services/api.ts`);
        resolve(false);
      }
    });
    
    server.once('listening', () => {
      server.close();
      console.log(`✅ Port ${port} is available!`);
      resolve(true);
    });
    
    server.listen(port);
  });
}

async function main() {
  console.log('Checking if server port is available...');
  
  // Check the default port
  const port = 5000;
  const isAvailable = await checkPort(port);
  
  if (!isAvailable) {
    // Also check the fallback port
    const fallbackPort = 5001;
    console.log(`\nChecking fallback port ${fallbackPort}...`);
    const isFallbackAvailable = await checkPort(fallbackPort);
    
    if (isFallbackAvailable) {
      console.log(`\nRecommendation: Update your configuration to use port ${fallbackPort} instead.`);
      console.log(`1. Set PORT=${fallbackPort} in your .env file`);
      console.log(`2. Update the frontend API configuration in src/services/api.ts to use port ${fallbackPort}`);
    } else {
      console.log('\nBoth default and fallback ports are in use. Please free up one of these ports or configure a different port in your .env file.');
    }
  } else {
    console.log('\nYour server should start correctly on port 5000.');
    console.log('Make sure your frontend is configured to connect to http://localhost:5000/api');
  }
}

// Run the function
main().catch(console.error); 