import { Router } from 'express';
import { LeaveRequestController } from '../controllers/leaveRequestController';
import { requireAuth } from '../middleware/auth';

const router = Router();
const leaveRequestController = new LeaveRequestController();

// Apply authentication middleware to all routes
router.use(requireAuth);

// Get all leave requests with filtering and pagination
router.get('/', leaveRequestController.getAll.bind(leaveRequestController));

// Get leave request by ID
router.get('/:id', leaveRequestController.getById.bind(leaveRequestController));

// Create new leave request
router.post('/', leaveRequestController.create.bind(leaveRequestController));

// Update existing leave request
router.put('/:id', leaveRequestController.update.bind(leaveRequestController));

// Update leave request status
router.put('/:id/status', leaveRequestController.updateStatus.bind(leaveRequestController));

// Delete leave request
router.delete('/:id', leaveRequestController.delete.bind(leaveRequestController));

// Approve leave request
router.post('/:id/approve', leaveRequestController.approve.bind(leaveRequestController));

// Reject leave request
router.post('/:id/reject', leaveRequestController.reject.bind(leaveRequestController));

// Cancel leave request
router.post('/:id/cancel', leaveRequestController.cancel.bind(leaveRequestController));

// Get leave requests by employee ID
router.get('/employee/:employeeId', leaveRequestController.getByEmployeeId.bind(leaveRequestController));

// Workflow action handler
router.post('/:id/workflow-action', leaveRequestController.handleWorkflowAction.bind(leaveRequestController));

// Get audit trail
router.get('/:id/audit-trail', leaveRequestController.getAuditTrail.bind(leaveRequestController));

// Bulk operations
router.post('/bulk/validate', leaveRequestController.validateBulkLeave.bind(leaveRequestController));
router.post('/bulk/create', leaveRequestController.createBulkLeave.bind(leaveRequestController));

export default router;