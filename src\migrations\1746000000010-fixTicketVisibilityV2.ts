import { MigrationInterface, QueryRunner } from "typeorm";
import { UserRole } from "../types/common";

export class FixTicketVisibilityV21746000000010 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Get all tickets
        const tickets = await queryRunner.query(
            `SELECT id, ticketNumber, visibleTo FROM tickets`
        );

        console.log('Found tickets:', tickets);

        // Update each ticket's visibility
        for (const ticket of tickets) {
            try {
                // Parse current visibleTo array
                let currentVisibleTo = [];
                try {
                    if (typeof ticket.visibleTo === 'string') {
                        // Try parsing as <PERSON><PERSON><PERSON> first
                        try {
                            currentVisibleTo = JSON.parse(ticket.visibleTo);
                        } catch {
                            // If not JSON, try as comma-separated string
                            currentVisibleTo = ticket.visibleTo.split(',').filter(Boolean);
                        }
                    } else if (Array.isArray(ticket.visibleTo)) {
                        currentVisibleTo = ticket.visibleTo;
                    }
                } catch (error) {
                    console.error(`Error parsing visibleTo for ticket ${ticket.ticketNumber}:`, error);
                }

                console.log(`Processing ticket ${ticket.ticketNumber}:`, {
                    currentVisibleTo
                });

                const visibleTo = [
                    UserRole.IT_ADMIN,
                    UserRole.IT_STAFF,
                    ...currentVisibleTo
                ].filter((v, i, arr) => arr.indexOf(v) === i); // Remove duplicates

                console.log(`Updating ticket ${ticket.ticketNumber} visibility to:`, visibleTo);

                // Store as JSON string
                await queryRunner.query(
                    `UPDATE tickets SET visibleTo = ? WHERE id = ?`,
                    [JSON.stringify(visibleTo), ticket.id]
                );
            } catch (error) {
                console.error(`Error processing ticket ${ticket.ticketNumber}:`, error);
            }
        }

        // Verify the changes
        const updatedTickets = await queryRunner.query(
            `SELECT id, ticketNumber, visibleTo FROM tickets`
        );
        console.log('Updated tickets:', updatedTickets);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // No down migration needed as this is a data fix
    }
} 