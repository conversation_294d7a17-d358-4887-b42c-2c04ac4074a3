/**
 * Global Data Cache Service
 * Prevents unnecessary API calls by caching frequently accessed data
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

class DataCacheService {
  private cache = new Map<string, CacheEntry<any>>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly SHORT_TTL = 2 * 60 * 1000; // 2 minutes
  private readonly LONG_TTL = 15 * 60 * 1000; // 15 minutes

  /**
   * Get data from cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) {
      return null;
    }

    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    console.log(`📦 Cache HIT for key: ${key}`);
    return entry.data;
  }

  /**
   * Set data in cache
   */
  set<T>(key: string, data: T, ttl: number = this.DEFAULT_TTL): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + ttl
    };
    
    this.cache.set(key, entry);
    console.log(`💾 Cache SET for key: ${key}, TTL: ${ttl}ms`);
  }

  /**
   * Check if key exists and is valid
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) {
      return false;
    }

    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Clear specific cache entry
   */
  delete(key: string): void {
    this.cache.delete(key);
    console.log(`🗑️ Cache DELETED for key: ${key}`);
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    console.log('🧹 Cache CLEARED');
  }

  /**
   * Clear expired entries
   */
  cleanup(): void {
    const now = Date.now();
    let deletedCount = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        this.cache.delete(key);
        deletedCount++;
      }
    }

    if (deletedCount > 0) {
      console.log(`🧹 Cache cleanup: removed ${deletedCount} expired entries`);
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    const now = Date.now();
    let validEntries = 0;
    let expiredEntries = 0;

    for (const entry of this.cache.values()) {
      if (now > entry.expiresAt) {
        expiredEntries++;
      } else {
        validEntries++;
      }
    }

    return {
      total: this.cache.size,
      valid: validEntries,
      expired: expiredEntries
    };
  }

  /**
   * Cached API call wrapper
   */
  async cachedApiCall<T>(
    key: string,
    apiCall: () => Promise<T>,
    ttl: number = this.DEFAULT_TTL
  ): Promise<T> {
    // Check cache first
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // Make API call
    console.log(`🌐 Cache MISS for key: ${key}, making API call`);
    const data = await apiCall();
    
    // Cache the result
    this.set(key, data, ttl);
    
    return data;
  }

  /**
   * Get TTL constants for different data types
   */
  getTTL() {
    return {
      SHORT: this.SHORT_TTL,
      DEFAULT: this.DEFAULT_TTL,
      LONG: this.LONG_TTL
    };
  }

  /**
   * Generate cache keys for common data types
   */
  keys = {
    employeeList: (filters?: any) => `employees_${JSON.stringify(filters || {})}`,
    employeeTeam: (employeeId: number) => `team_${employeeId}`,
    employeeLeave: (employeeId: number) => `leave_${employeeId}`,
    employeeAttendance: (employeeId: number, period?: string) => `attendance_${employeeId}_${period || 'all'}`,
    teamLeaveRequests: (teamMemberIds: number[]) => `team_leave_${teamMemberIds.sort().join(',')}`,
    departmentEmployees: (department: string) => `dept_employees_${department}`,
    leaveBalances: (employeeId: number, year: number) => `leave_balances_${employeeId}_${year}`
  };
}

// Export singleton instance
export const dataCache = new DataCacheService();

// Auto cleanup every 10 minutes
setInterval(() => {
  dataCache.cleanup();
}, 10 * 60 * 1000);

export default dataCache;
