import { MigrationInterface, QueryRunner, Table, TableForeignKey } from 'typeorm';

export class CreateLeavePolicyTables1703000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create leave_policy_settings table
    await queryRunner.createTable(
      new Table({
        name: 'leave_policy_settings',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'minDaysNotice',
            type: 'int',
            default: 1,
          },
          {
            name: 'maxConsecutiveDays',
            type: 'int',
            default: 30,
          },
          {
            name: 'allowHalfDay',
            type: 'boolean',
            default: true,
          },
          {
            name: 'requiresApproval',
            type: 'boolean',
            default: true,
          },
          {
            name: 'allowNegativeBalance',
            type: 'boolean',
            default: false,
          },
          {
            name: 'restrictLeavesTaken',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'autoApprovalLimit',
            type: 'int',
            default: 0,
          },
          {
            name: 'weekendsBetweenLeavesCount',
            type: 'boolean',
            default: false,
          },
          {
            name: 'holidaysBetweenLeavesCount',
            type: 'boolean',
            default: false,
          },
          {
            name: 'allowCompensatoryTimeOff',
            type: 'boolean',
            default: true,
          },
          {
            name: 'compensatoryExpiryDays',
            type: 'int',
            default: 180,
          },
          {
            name: 'approvalWorkflow',
            type: 'enum',
            enum: ['single', 'multi', 'department'],
            default: "'single'",
          },
          {
            name: 'workingHoursPerDay',
            type: 'int',
            default: 8,
          },
          {
            name: 'maxDaysPerRequest',
            type: 'int',
            default: 30,
          },
          {
            name: 'allowBackdatedLeave',
            type: 'boolean',
            default: false,
          },
          {
            name: 'requireDocuments',
            type: 'boolean',
            default: false,
          },
          {
            name: 'allowLeaveModification',
            type: 'boolean',
            default: true,
          },
          {
            name: 'autoCalculateWorkingDays',
            type: 'boolean',
            default: true,
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true,
          },
          {
            name: 'createdBy',
            type: 'int',
          },
          {
            name: 'updatedBy',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true
    );

    // Create leave_policy_configurations table
    await queryRunner.createTable(
      new Table({
        name: 'leave_policy_configurations',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'companyId',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'generalSettings',
            type: 'json',
          },
          {
            name: 'version',
            type: 'varchar',
            length: '50',
          },
          {
            name: 'effectiveDate',
            type: 'timestamp',
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true,
          },
          {
            name: 'createdBy',
            type: 'int',
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true
    );

    // Create leave_type_policies table
    await queryRunner.createTable(
      new Table({
        name: 'leave_type_policies',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'leaveType',
            type: 'varchar',
            length: '50',
          },
          {
            name: 'enabled',
            type: 'boolean',
            default: true,
          },
          {
            name: 'displayName',
            type: 'varchar',
            length: '100',
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'settings',
            type: 'json',
          },
          {
            name: 'applicableRoles',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'applicableDepartments',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'maxDaysPerYear',
            type: 'int',
          },
          {
            name: 'minServicePeriod',
            type: 'int',
            default: 0,
          },
          {
            name: 'allowCarryForward',
            type: 'boolean',
            default: false,
          },
          {
            name: 'carryForwardLimit',
            type: 'int',
            default: 0,
          },
          {
            name: 'encashmentAllowed',
            type: 'boolean',
            default: false,
          },
          {
            name: 'documentRequired',
            type: 'boolean',
            default: false,
          },
          {
            name: 'color',
            type: 'varchar',
            length: '7',
            isNullable: true,
          },
          {
            name: 'sortOrder',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true,
          },
          {
            name: 'category',
            type: 'varchar',
            length: '50',
            isNullable: true,
          },
          {
            name: 'allowHalfDay',
            type: 'boolean',
            default: true,
          },
          {
            name: 'minDaysNotice',
            type: 'int',
            default: 0,
          },
          {
            name: 'policyConfigurationId',
            type: 'int',
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true
    );

    // Create leave_accrual_rules table
    await queryRunner.createTable(
      new Table({
        name: 'leave_accrual_rules',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'leaveType',
            type: 'varchar',
            length: '50',
          },
          {
            name: 'accrualFrequency',
            type: 'enum',
            enum: ['daily', 'weekly', 'monthly', 'quarterly', 'annually'],
          },
          {
            name: 'accrualAmount',
            type: 'decimal',
            precision: 10,
            scale: 2,
          },
          {
            name: 'maxAccumulation',
            type: 'decimal',
            precision: 10,
            scale: 2,
          },
          {
            name: 'carryOverLimit',
            type: 'decimal',
            precision: 10,
            scale: 2,
          },
          {
            name: 'carryOverExpiry',
            type: 'int',
          },
          {
            name: 'effectiveDate',
            type: 'timestamp',
          },
          {
            name: 'isProrated',
            type: 'boolean',
            default: false,
          },
          {
            name: 'waitingPeriod',
            type: 'int',
            default: 0,
          },
          {
            name: 'description',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'applicableRoles',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'applicableDepartments',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'policyConfigurationId',
            type: 'int',
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true
    );

    // Create holiday_calendars table
    await queryRunner.createTable(
      new Table({
        name: 'holiday_calendars',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '100',
          },
          {
            name: 'year',
            type: 'int',
          },
          {
            name: 'applicableDepartments',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'applicableLocations',
            type: 'json',
            isNullable: true,
          },
          {
            name: 'policyConfigurationId',
            type: 'int',
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true
    );

    // Create leave_policy_versions table
    await queryRunner.createTable(
      new Table({
        name: 'leave_policy_versions',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'policyId',
            type: 'int',
          },
          {
            name: 'version',
            type: 'varchar',
            length: '50',
          },
          {
            name: 'changeDescription',
            type: 'text',
          },
          {
            name: 'effectiveDate',
            type: 'timestamp',
          },
          {
            name: 'createdBy',
            type: 'int',
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true,
          },
          {
            name: 'policyData',
            type: 'longtext',
          },
          {
            name: 'approvedBy',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'approvedAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true
    );

    // Add foreign keys
    await queryRunner.createForeignKey(
      'leave_type_policies',
      new TableForeignKey({
        columnNames: ['policyConfigurationId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'leave_policy_configurations',
        onDelete: 'CASCADE',
      })
    );

    await queryRunner.createForeignKey(
      'leave_accrual_rules',
      new TableForeignKey({
        columnNames: ['policyConfigurationId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'leave_policy_configurations',
        onDelete: 'CASCADE',
      })
    );

    await queryRunner.createForeignKey(
      'holiday_calendars',
      new TableForeignKey({
        columnNames: ['policyConfigurationId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'leave_policy_configurations',
        onDelete: 'CASCADE',
      })
    );

    await queryRunner.createForeignKey(
      'leave_policy_versions',
      new TableForeignKey({
        columnNames: ['policyId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'leave_policy_configurations',
        onDelete: 'CASCADE',
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign keys first
    const leavePolicyVersionsTable = await queryRunner.getTable('leave_policy_versions');
    const leavePolicyVersionsForeignKey = leavePolicyVersionsTable?.foreignKeys.find(
      fk => fk.columnNames.indexOf('policyId') !== -1
    );
    if (leavePolicyVersionsForeignKey) {
      await queryRunner.dropForeignKey('leave_policy_versions', leavePolicyVersionsForeignKey);
    }

    const holidayCalendarsTable = await queryRunner.getTable('holiday_calendars');
    const holidayCalendarsForeignKey = holidayCalendarsTable?.foreignKeys.find(
      fk => fk.columnNames.indexOf('policyConfigurationId') !== -1
    );
    if (holidayCalendarsForeignKey) {
      await queryRunner.dropForeignKey('holiday_calendars', holidayCalendarsForeignKey);
    }

    const leaveAccrualRulesTable = await queryRunner.getTable('leave_accrual_rules');
    const leaveAccrualRulesForeignKey = leaveAccrualRulesTable?.foreignKeys.find(
      fk => fk.columnNames.indexOf('policyConfigurationId') !== -1
    );
    if (leaveAccrualRulesForeignKey) {
      await queryRunner.dropForeignKey('leave_accrual_rules', leaveAccrualRulesForeignKey);
    }

    const leaveTypePoliciesTable = await queryRunner.getTable('leave_type_policies');
    const leaveTypePoliciesForeignKey = leaveTypePoliciesTable?.foreignKeys.find(
      fk => fk.columnNames.indexOf('policyConfigurationId') !== -1
    );
    if (leaveTypePoliciesForeignKey) {
      await queryRunner.dropForeignKey('leave_type_policies', leaveTypePoliciesForeignKey);
    }

    // Drop tables
    await queryRunner.dropTable('leave_policy_versions');
    await queryRunner.dropTable('holiday_calendars');
    await queryRunner.dropTable('leave_accrual_rules');
    await queryRunner.dropTable('leave_type_policies');
    await queryRunner.dropTable('leave_policy_configurations');
    await queryRunner.dropTable('leave_policy_settings');
  }
} 