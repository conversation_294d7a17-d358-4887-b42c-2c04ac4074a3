import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateAttendanceRegularizationRequestsTable1710000000100 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create table (MySQL syntax)
    await queryRunner.query(`
      CREATE TABLE IF NOT EXISTS attendance_regularization_requests (
        id INT AUTO_INCREMENT PRIMARY KEY,
        employee_id INT NOT NULL,
        employee_name VARCHAR(255) NOT NULL,
        date DATE NOT NULL,
        type VARCHAR(50) NOT NULL,
        requested_time TIME,
        reason TEXT NOT NULL,
        status ENUM('pending','approved','rejected') NOT NULL DEFAULT 'pending',
        approver_comments TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        CONSTRAINT fk_regularization_employee FOREIG<PERSON>EY (employee_id) REFERENCES employees(id) ON DELETE CASCADE
      ) ENGINE=InnoDB;
    `);

    // Helpful indexes for faster filtering
    // Create index on employee_id if it doesn't already exist
    const [empIdx]: any = await queryRunner.query(`
      SELECT COUNT(1) as count FROM INFORMATION_SCHEMA.STATISTICS 
      WHERE table_schema = DATABASE() AND table_name = 'attendance_regularization_requests' 
        AND index_name = 'idx_regularization_employee_id';
    `);
    if (!empIdx || empIdx.count === 0) {
      await queryRunner.query(`CREATE INDEX idx_regularization_employee_id ON attendance_regularization_requests(employee_id);`);
    }

    // Create index on status if it doesn't already exist
    const [statusIdx]: any = await queryRunner.query(`
      SELECT COUNT(1) as count FROM INFORMATION_SCHEMA.STATISTICS 
      WHERE table_schema = DATABASE() AND table_name = 'attendance_regularization_requests' 
        AND index_name = 'idx_regularization_status';
    `);
    if (!statusIdx || statusIdx.count === 0) {
      await queryRunner.query(`CREATE INDEX idx_regularization_status ON attendance_regularization_requests(status);`);
    }

    // Create index on date if it doesn't already exist
    const [dateIdx]: any = await queryRunner.query(`
      SELECT COUNT(1) as count FROM INFORMATION_SCHEMA.STATISTICS 
      WHERE table_schema = DATABASE() AND table_name = 'attendance_regularization_requests' 
        AND index_name = 'idx_regularization_date';
    `);
    if (!dateIdx || dateIdx.count === 0) {
      await queryRunner.query(`CREATE INDEX idx_regularization_date ON attendance_regularization_requests(date);`);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS attendance_regularization_requests;`);
  }
} 