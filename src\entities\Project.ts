import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  ManyToMany,
  JoinTable
} from 'typeorm';
import { IsNotEmpty, IsOptional, IsEnum, IsDateString, Length } from 'class-validator';
import { User } from './User';
import { Task } from './Task';
import { ProjectMember } from './ProjectMember';

export enum ProjectStatus {
  PLANNING = 'planning',
  ACTIVE = 'active',
  ON_HOLD = 'on_hold',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export enum ProjectPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

@Entity('projects')
export class Project {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255 })
  @IsNotEmpty({ message: 'Project name is required' })
  @Length(2, 255, { message: 'Project name must be between 2 and 255 characters' })
  name: string;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  description: string;

  @Column({
    type: 'enum',
    enum: ProjectStatus,
    default: ProjectStatus.PLANNING
  })
  @IsEnum(ProjectStatus, { message: 'Invalid project status' })
  status: ProjectStatus;

  @Column({
    type: 'enum',
    enum: ProjectPriority,
    default: ProjectPriority.MEDIUM
  })
  @IsEnum(ProjectPriority, { message: 'Invalid project priority' })
  priority: ProjectPriority;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDateString({}, { message: 'Invalid start date format' })
  startDate: string;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDateString({}, { message: 'Invalid end date format' })
  endDate: string;

  @Column({ type: 'date', nullable: true })
  @IsOptional()
  @IsDateString({}, { message: 'Invalid due date format' })
  dueDate: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  @IsOptional()
  progress: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  @IsOptional()
  budget: number;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @Length(2, 100, { message: 'Client name must be between 2 and 100 characters' })
  clientName: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @Length(2, 100, { message: 'Department must be between 2 and 100 characters' })
  department: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @IsOptional()
  @Length(2, 100, { message: 'Location must be between 2 and 100 characters' })
  location: string;

  @Column({ type: 'json', nullable: true })
  @IsOptional()
  tags: string[];

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  notes: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  // Relations
  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'createdById' })
  createdBy: User;

  @Column({ type: 'uuid' })
  createdById: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'managerId' })
  manager: User;

  @Column({ type: 'uuid', nullable: true })
  managerId: string;

  @OneToMany(() => Task, task => task.project, { cascade: true })
  tasks: Task[];

  @OneToMany(() => ProjectMember, member => member.project, { cascade: true })
  members: ProjectMember[];

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;
}
