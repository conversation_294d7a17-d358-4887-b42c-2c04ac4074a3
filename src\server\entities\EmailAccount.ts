import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('email_accounts')
export class EmailAccount {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  emailAddress: string;

  @Column({ type: 'enum', enum: ['User', 'Department'] })
  assignedToType: 'User' | 'Department';

  @Column({ type: 'varchar', length: 100 })
  assignedToName: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  assignedToId?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  designation?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  employmentStatus?: string;

  @Column({ type: 'varchar', length: 100 })
  department: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  project?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  location?: string;

  @Column({ type: 'varchar', length: 100 })
  accountType: string;

  @Column({ type: 'varchar', length: 100 })
  platform: string;

  @Column({ type: 'varchar', length: 100 })
  status: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  loginUrl?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  hostingProvider?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  recoveryEmail?: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  recoveryPhone?: string;

  @Column({ type: 'boolean', default: false })
  twoFactorEnabled: boolean;

  @Column({ type: 'varchar', length: 100 })
  primaryUser: string;

  @Column({ type: 'varchar', length: 100 })
  createdBy: string;

  @Column({ type: 'text', nullable: true })
  notes?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  subscriptionType?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  software?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  asset?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  password?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  secretQuestion?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  secretAnswer?: string;

  @Column({ type: 'date', nullable: true })
  creationDate?: Date;

  @Column({ type: 'date', nullable: true })
  lastAccessDate?: Date;

  @Column({ type: 'text', nullable: true })
  ownershipChangeLog?: string;

  @Column({ type: 'int', nullable: true })
  passwordAge?: number;

  @Column({ type: 'varchar', length: 100, nullable: true })
  ticketId?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  licenseRecord?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  userOffboardingProcess?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 