import { Router } from 'express';
import { SystemLogController } from '../controllers/SystemLogController';

const router = Router();

// Note: This file will be moved to the server/routes directory later
// GET /api/system-logs/statistics - Get statistics about logs
router.get('/statistics', SystemLogController.getStatistics);

// GET /api/system-logs/export - Export logs as CSV
router.get('/export', SystemLogController.exportLogs);

// GET /api/system-logs - Get all system logs with optional filtering
router.get('/', SystemLogController.getAllLogs);

// GET /api/system-logs/:id - Get a single system log by ID
router.get('/:id', SystemLogController.getLogById);

// POST /api/system-logs - Create a new system log
router.post('/', SystemLogController.createLog);

// PUT /api/system-logs/:id - Update an existing system log
router.put('/:id', SystemLogController.updateLog);

// DELETE /api/system-logs/:id - Delete a system log
router.delete('/:id', SystemLogController.deleteLog);

// DELETE /api/system-logs - Clear all system logs
router.delete('/', SystemLogController.clearLogs);

export default router; 