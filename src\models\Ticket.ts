import mongoose, { Schema, Document } from 'mongoose';

type UserRole = 'IT_ADMIN' | 'IT_STAFF' | 'EMPLOYEE' | 'CEO' | 'FINANCE_MANAGER' | 'DEPT_HEAD' | 'VIEW';

export interface ITicket extends Document {
  ticketNumber: string;
  title: string;
  description: string;
  category: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in_progress' | 'resolved' | 'closed' | 'locked';
  createdBy: {
    userId: string;
    name: string;
    department: string;
    role: UserRole;
  };
  assignedTo?: string;
  employeeDetails?: {
    id: string;
    name: string;
    department: string;
    email: string;
  };
  visibleTo: string[];
  departmentChain: string[];
  attachments: Array<{
    filename: string;
    path: string;
    mimetype: string;
  }>;
  workflow: {
    currentStage: string;
    history: Array<{
      stage: string;
      timestamp: Date;
      by: string;
      role: UserRole;
      comment?: string;
    }>;
  };
  createdAt: Date;
  lastUpdated: Date;
}

const TicketSchema = new Schema({
  ticketNumber: { type: String, required: true, unique: true },
  title: { type: String, required: true },
  description: { type: String, required: true },
  category: { type: String, required: true },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical'],
    default: 'medium'
  },
  status: { 
    type: String, 
    enum: ['open', 'in_progress', 'resolved', 'closed', 'locked'],
    default: 'open'
  },
  createdBy: {
    userId: { type: String, required: true },
    name: { type: String, required: true },
    department: { type: String, required: true },
    role: { 
      type: String, 
      required: true,
      enum: ['IT_ADMIN', 'IT_STAFF', 'EMPLOYEE', 'CEO', 'FINANCE_MANAGER', 'DEPT_HEAD', 'VIEW']
    }
  },
  assignedTo: { type: Schema.Types.ObjectId, ref: 'User' },
  employeeDetails: {
    id: String,
    name: String,
    department: String,
    email: String
  },
  visibleTo: [{ type: String }],
  departmentChain: [{ type: String }],
  attachments: [{
    filename: String,
    path: String,
    mimetype: String
  }],
  workflow: {
    currentStage: { type: String, default: 'created' },
    history: [{
      stage: String,
      timestamp: { type: Date, default: Date.now },
      by: { type: Schema.Types.ObjectId, ref: 'User' },
      role: { 
        type: String,
        enum: ['IT_ADMIN', 'IT_STAFF', 'EMPLOYEE', 'CEO', 'FINANCE_MANAGER', 'DEPT_HEAD', 'VIEW']
      },
      comment: String
    }]
  },
  createdAt: { type: Date, default: Date.now },
  lastUpdated: { type: Date, default: Date.now }
}, {
  timestamps: { createdAt: 'createdAt', updatedAt: 'lastUpdated' }
});

// Add indexes for better query performance
TicketSchema.index({ ticketNumber: 1 });
TicketSchema.index({ 'createdBy.department': 1 });
TicketSchema.index({ visibleTo: 1 });
TicketSchema.index({ status: 1 });

export default mongoose.model<ITicket>('Ticket', TicketSchema); 