import { MigrationInterface, QueryRunner, Table, TableForeign<PERSON>ey } from "typeorm";

export class CreateAssetsTable1710000000000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createTable(
            new Table({
                name: "assets",
                columns: [
                    {
                        name: "id",
                        type: "varchar",
                        length: "36",
                        isPrimary: true,
                        isGenerated: false
                    },
                    {
                        name: "assetType",
                        type: "varchar",
                        length: "50"
                    },
                    {
                        name: "category",
                        type: "varchar",
                        length: "100"
                    },
                    {
                        name: "manufacturer",
                        type: "varchar",
                        length: "100"
                    },
                    {
                        name: "model",
                        type: "varchar",
                        length: "100"
                    },
                    {
                        name: "serialNumber",
                        type: "varchar",
                        length: "100",
                        isUnique: true
                    },
                    {
                        name: "assetTag",
                        type: "varchar",
                        length: "50",
                        isNullable: true
                    },
                    {
                        name: "location",
                        type: "varchar",
                        length: "100"
                    },
                    {
                        name: "department",
                        type: "varchar",
                        length: "50"
                    },
                    {
                        name: "status",
                        type: "varchar",
                        length: "50"
                    },
                    {
                        name: "condition",
                        type: "varchar",
                        length: "50"
                    },
                    {
                        name: "purchaseDate",
                        type: "date",
                        isNullable: true
                    },
                    {
                        name: "warrantyExpiry",
                        type: "date",
                        isNullable: true
                    },
                    {
                        name: "cost",
                        type: "decimal",
                        precision: 10,
                        scale: 2,
                        isNullable: true
                    },
                    {
                        name: "vendor",
                        type: "varchar",
                        length: "100",
                        isNullable: true
                    },
                    {
                        name: "internetAccess",
                        type: "boolean",
                        default: false
                    },
                    {
                        name: "ipAddress",
                        type: "varchar",
                        length: "50",
                        isNullable: true
                    },
                    {
                        name: "attributes",
                        type: "json",
                        isNullable: true
                    },
                    {
                        name: "notes",
                        type: "text",
                        isNullable: true
                    },
                    {
                        name: "assignedToId",
                        type: "varchar",
                        length: "36",
                        isNullable: true
                    },
                    {
                        name: "assignedAt",
                        type: "date",
                        isNullable: true
                    },
                    {
                        name: "lastMaintenance",
                        type: "date",
                        isNullable: true
                    },
                    {
                        name: "maintenanceBy",
                        type: "varchar",
                        length: "100",
                        isNullable: true
                    },
                    {
                        name: "nextMaintenance",
                        type: "date",
                        isNullable: true
                    },
                    {
                        name: "maintenanceCost",
                        type: "decimal",
                        precision: 10,
                        scale: 2,
                        isNullable: true
                    },
                    {
                        name: "createdAt",
                        type: "timestamp",
                        default: "CURRENT_TIMESTAMP"
                    },
                    {
                        name: "updatedAt",
                        type: "timestamp",
                        default: "CURRENT_TIMESTAMP",
                        onUpdate: "CURRENT_TIMESTAMP"
                    }
                ]
            }),
            true
        );

        // Add foreign key for assignedToId
        await queryRunner.createForeignKey(
            "assets",
            new TableForeignKey({
                columnNames: ["assignedToId"],
                referencedColumnNames: ["id"],
                referencedTableName: "users",
                onDelete: "SET NULL"
            })
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        const table = await queryRunner.getTable("assets");
        
        if (table) {
            const foreignKey = table.foreignKeys.find(
                fk => fk.columnNames.indexOf("assignedToId") !== -1
            );
            
            if (foreignKey) {
                await queryRunner.dropForeignKey("assets", foreignKey);
            }
        }
        
        await queryRunner.dropTable("assets");
    }
} 