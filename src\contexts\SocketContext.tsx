import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import { socketService, NotificationData } from '../services/socket';
import { useAuth } from './AuthContext';
import { toast } from 'react-hot-toast';

interface SocketContextType {
  isConnected: boolean;
  notifications: NotificationData[];
  addNotification: (notification: NotificationData) => void;
  markNotificationAsRead: (id: string) => void;
  reconnect: () => Promise<void>;
}

const SocketContext = createContext<SocketContextType | undefined>(undefined);

export function SocketProvider({ children }: { children: React.ReactNode }) {
  const { user, isAuthenticated } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const unsubscribeRef = useRef<(() => void) | null>(null);
  const connectionAttemptRef = useRef<boolean>(false);
  const isSubscribedRef = useRef<boolean>(false);

  // Connect to socket when user is authenticated - DISABLED TO PREVENT CONTINUOUS CALLS
  useEffect(() => {
    // TEMPORARILY DISABLE SOCKET CONNECTION TO STOP CONTINUOUS API CALLS
    console.log('SocketContext: Socket connection temporarily disabled to prevent continuous API calls');
    
    // Set connected to false and clear any existing connections
    setIsConnected(false);
    connectionAttemptRef.current = false;
    isSubscribedRef.current = false;
    
    // Disconnect any existing socket
    socketService.disconnect();
    
    // Return empty cleanup
    return () => {};
  }, []); // Empty dependency array

  // Set token when it changes - FULLY DISABLED
  // useEffect(() => {
  //   const token = localStorage.getItem('authToken');
  //   socketService.setToken(token);
  // }, [isAuthenticated]);

  // Handle component unmount - unsubscribe only when the entire SocketProvider is unmounted
  useEffect(() => {
    return () => {
      // Only unsubscribe if we're subscribed
      if (isSubscribedRef.current && user?.id) {
        console.log(`SocketContext: Unsubscribing from notifications for user ${user.id} on unmount`);
        socketService.unsubscribeFromNotifications(user.id);
        isSubscribedRef.current = false;
      }
      
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, []);

  // Monitor connection status - DISABLED TO PREVENT CONTINUOUS CALLS
  useEffect(() => {
    // TEMPORARILY DISABLE CONNECTION MONITORING TO STOP CONTINUOUS API CALLS
    console.log('SocketContext: Connection monitoring temporarily disabled');
    
    // Return empty cleanup
    return () => {};
  }, []); // Empty dependency array

  const addNotification = (notification: NotificationData) => {
    setNotifications(prev => {
      // Check if notification already exists
      const exists = prev.some(n => n.id === notification.id);
      if (exists) return prev;
      return [notification, ...prev];
    });
  };

  const markNotificationAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, read: true } 
          : notification
      )
    );
    socketService.markNotificationAsRead(id);
  };
  
  const reconnect = async (): Promise<void> => {
    try {
      console.log('SocketContext: Attempting to reconnect...');
      await socketService.connect();
      setIsConnected(true);
      
      // Resubscribe to notifications if needed
      if (user?.id && !isSubscribedRef.current) {
        console.log(`SocketContext: Resubscribing to notifications for user ${user.id}`);
        socketService.subscribeToNotifications(user.id);
        isSubscribedRef.current = true;
      }
    } catch (error) {
      console.error('Failed to reconnect:', error);
      setIsConnected(false);
    }
  };

  return (
    <SocketContext.Provider value={{ 
      isConnected, 
      notifications, 
      addNotification,
      markNotificationAsRead,
      reconnect
    }}>
      {children}
    </SocketContext.Provider>
  );
}

export function useSocket() {
  const context = useContext(SocketContext);
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
} 