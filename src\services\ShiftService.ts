import { ShiftRepository } from '../repositories/ShiftRepository';
import { Shift } from '../server/entities/Shift';
import { ShiftAssignment } from '../server/entities/ShiftAssignment';

export interface CreateShiftDto {
  name: string;
  description?: string;
  startTime: string;
  endTime: string;
  breakDuration: number;
  workingDays?: number[];
  isFlexible?: boolean;
  graceTimeInMinutes?: number;
  requiredWorkHours?: number;
  color?: string;
  halfDayHours?: number;
  breakStartTime?: string;
  breakEndTime?: string;
}

export interface UpdateShiftDto extends Partial<CreateShiftDto> {
  isActive?: boolean;
}

export interface CreateShiftAssignmentDto {
  employeeId: number;
  shiftId: number;
  startDate: string;
  endDate?: string;
  isPermanent: boolean;
  notes?: string;
}

export interface ShiftValidationResult {
  isValid: boolean;
  errors: string[];
}

export class ShiftService {
  constructor(private shiftRepository: ShiftRepository) {}

  // Shift management
  async createShift(createShiftDto: CreateShiftDto): Promise<Shift> {
    // Validate shift data
    const validation = this.validateShiftData(createShiftDto);
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    // Check for duplicate shift name
    const existingShifts = await this.shiftRepository.getAllShifts();
    const duplicateName = existingShifts.find(
      shift => shift.name.toLowerCase() === createShiftDto.name.toLowerCase()
    );

    if (duplicateName) {
      throw new Error('A shift with this name already exists');
    }

    return await this.shiftRepository.createShift(createShiftDto);
  }

  async getAllShifts(): Promise<Shift[]> {
    return await this.shiftRepository.getAllShifts();
  }

  async getActiveShifts(): Promise<Shift[]> {
    return await this.shiftRepository.getActiveShifts();
  }

  async getShiftById(id: number): Promise<Shift> {
    const shift = await this.shiftRepository.getShiftById(id);
    if (!shift) {
      throw new Error('Shift not found');
    }
    return shift;
  }

  async updateShift(id: number, updateShiftDto: UpdateShiftDto): Promise<Shift> {
    const existingShift = await this.getShiftById(id);

    // If name is being updated, check for duplicates
    if (updateShiftDto.name && updateShiftDto.name !== existingShift.name) {
      const existingShifts = await this.shiftRepository.getAllShifts();
      const duplicateName = existingShifts.find(
        shift => shift.id !== id && shift.name.toLowerCase() === updateShiftDto.name!.toLowerCase()
      );

      if (duplicateName) {
        throw new Error('A shift with this name already exists');
      }
    }

    // Validate updated data
    const validation = this.validateShiftData({ ...existingShift, ...updateShiftDto });
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
    }

    const updatedShift = await this.shiftRepository.updateShift(id, updateShiftDto);
    if (!updatedShift) {
      throw new Error('Failed to update shift');
    }

    return updatedShift;
  }

  async deleteShift(id: number): Promise<void> {
    const shift = await this.getShiftById(id);
    await this.shiftRepository.deleteShift(id);
  }

  async toggleShiftStatus(id: number): Promise<Shift> {
    const updatedShift = await this.shiftRepository.toggleShiftStatus(id);
    if (!updatedShift) {
      throw new Error('Failed to toggle shift status');
    }
    return updatedShift;
  }

  // Shift assignment management
  async assignShiftToEmployee(assignmentDto: CreateShiftAssignmentDto): Promise<ShiftAssignment> {
    // Validate that shift exists and is active
    const shift = await this.getShiftById(assignmentDto.shiftId);
    if (!shift.isActive) {
      throw new Error('Cannot assign inactive shift to employee');
    }

    // Validate dates
    if (assignmentDto.endDate && assignmentDto.endDate < assignmentDto.startDate) {
      throw new Error('End date cannot be before start date');
    }

    return await this.shiftRepository.assignShiftToEmployee(assignmentDto);
  }

  async getEmployeeShiftAssignments(employeeId: number): Promise<ShiftAssignment[]> {
    return await this.shiftRepository.getEmployeeShiftAssignments(employeeId);
  }

  async getCurrentEmployeeShift(employeeId: number, date?: string): Promise<ShiftAssignment | null> {
    return await this.shiftRepository.getCurrentEmployeeShift(employeeId, date);
  }

  async getShiftAssignments(shiftId: number): Promise<ShiftAssignment[]> {
    return await this.shiftRepository.getShiftAssignments(shiftId);
  }

  async endShiftAssignment(assignmentId: number, endDate?: string): Promise<ShiftAssignment> {
    const updatedAssignment = await this.shiftRepository.endShiftAssignment(assignmentId, endDate);
    if (!updatedAssignment) {
      throw new Error('Failed to end shift assignment');
    }
    return updatedAssignment;
  }

  async deleteShiftAssignment(assignmentId: number): Promise<void> {
    const success = await this.shiftRepository.deleteShiftAssignment(assignmentId);
    if (!success) {
      throw new Error('Failed to delete shift assignment');
    }
  }

  // Utility methods
  async getShiftStatistics() {
    return await this.shiftRepository.getShiftStatistics();
  }

  async getEmployeesWithoutShift(): Promise<number[]> {
    return await this.shiftRepository.getEmployeesWithoutShift();
  }

  // Business logic methods
  calculateWorkingHours(shiftStartTime: string, shiftEndTime: string, breakDuration: number): number {
    const start = this.parseTime(shiftStartTime);
    const end = this.parseTime(shiftEndTime);
    
    let totalMinutes: number;
    
    // Handle overnight shifts
    if (end < start) {
      totalMinutes = (24 * 60 - start) + end;
    } else {
      totalMinutes = end - start;
    }
    
    // Subtract break duration
    totalMinutes -= breakDuration;
    
    return Math.max(0, totalMinutes / 60);
  }

  isLateCheckIn(checkInTime: string, shiftStartTime: string, graceMinutes: number): boolean {
    const checkIn = this.parseTime(checkInTime);
    const shiftStart = this.parseTime(shiftStartTime);
    const allowedLateness = shiftStart + graceMinutes;
    
    return checkIn > allowedLateness;
  }

  isEarlyCheckOut(checkOutTime: string, shiftEndTime: string): boolean {
    const checkOut = this.parseTime(checkOutTime);
    const shiftEnd = this.parseTime(shiftEndTime);
    
    return checkOut < shiftEnd;
  }

  getShiftStatus(currentTime: string, shiftStartTime: string, shiftEndTime: string): 'before' | 'during' | 'after' {
    const current = this.parseTime(currentTime);
    const start = this.parseTime(shiftStartTime);
    const end = this.parseTime(shiftEndTime);
    
    // Handle overnight shifts
    if (end < start) {
      if (current >= start || current <= end) {
        return 'during';
      } else if (current < start && current > end) {
        return 'before';
      } else {
        return 'after';
      }
    } else {
      if (current < start) return 'before';
      if (current > end) return 'after';
      return 'during';
    }
  }

  private validateShiftData(shiftData: Partial<CreateShiftDto>): ShiftValidationResult {
    const errors: string[] = [];

    if (!shiftData.name || shiftData.name.trim().length === 0) {
      errors.push('Shift name is required');
    }

    if (!shiftData.startTime || !this.isValidTimeFormat(shiftData.startTime)) {
      errors.push('Valid start time is required (HH:MM format)');
    }

    if (!shiftData.endTime || !this.isValidTimeFormat(shiftData.endTime)) {
      errors.push('Valid end time is required (HH:MM format)');
    }

    if (typeof shiftData.breakDuration !== 'number' || shiftData.breakDuration < 0) {
      errors.push('Break duration must be a non-negative number');
    }

    if (shiftData.graceTimeInMinutes !== undefined && 
        (typeof shiftData.graceTimeInMinutes !== 'number' || shiftData.graceTimeInMinutes < 0)) {
      errors.push('Grace time must be a non-negative number');
    }

    if (shiftData.requiredWorkHours !== undefined && 
        (typeof shiftData.requiredWorkHours !== 'number' || shiftData.requiredWorkHours <= 0)) {
      errors.push('Required work hours must be a positive number');
    }

    if (shiftData.color && !this.isValidColorCode(shiftData.color)) {
      errors.push('Invalid color code format');
    }

    if (shiftData.workingDays && Array.isArray(shiftData.workingDays)) {
      const validDays = shiftData.workingDays.every(day => 
        typeof day === 'number' && day >= 0 && day <= 6
      );
      if (!validDays) {
        errors.push('Working days must be numbers between 0-6 (Sunday to Saturday)');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  private isValidTimeFormat(time: string): boolean {
    // Accept both HH:MM and HH:MM:SS formats
    return /^([01]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/.test(time);
  }

  private isValidColorCode(color: string): boolean {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
  }

  private parseTime(timeString: string): number {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  }
}

export default ShiftService; 