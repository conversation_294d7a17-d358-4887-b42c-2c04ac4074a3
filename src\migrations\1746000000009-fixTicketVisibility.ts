import { MigrationInterface, QueryRunner } from "typeorm";
import { UserRole } from "../types/common";

export class FixTicketVisibility1746000000009 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Get all tickets
        const tickets = await queryRunner.query(
            `SELECT id, visibleTo FROM tickets`
        );

        // Update each ticket's visibility
        for (const ticket of tickets) {
            const visibleTo = [
                UserRole.IT_ADMIN.toString(),
                UserRole.IT_STAFF.toString(),
                // Keep existing department and role values
                ...(ticket.visibleTo || []).filter(
                    (v: string) => !v.includes('IT_ADMIN') && !v.includes('IT_STAFF')
                )
            ];

            await queryRunner.query(
                `UPDATE tickets SET visibleTo = ? WHERE id = ?`,
                [JSON.stringify(visibleTo), ticket.id]
            );
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // No down migration needed as this is a data fix
    }
} 