import { Request, Response, NextFunction } from 'express';
import { UserRole } from '../../../types/common';

// Type for permission key strings
type HRPermissionKey = 
  | 'canCreateEmployee' 
  | 'canEditEmployee'
  | 'canDeleteEmployee'
  | 'canViewEmployees'
  | 'canManageAttendance'
  | 'canManageLeave'
  | 'canManagePayroll'
  | 'canManagePerformance';

// Middleware to check if the user has specific HR permissions
export const checkHRPermission = (requiredPermission: HRPermissionKey) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Unauthorized - User not authenticated' });
      }

      const userRole = req.user.role;
      
      // Admin roles always have full access to everything - check first
      if (['IT_ADMIN', 'ADMIN', 'SYSTEM_ADMIN', 'CEO'].includes(userRole)) {
        return next();
      }
      
      const userPermissions = req.user.permissions || {};
      
      // HR Admin has full access to HR functions
      if (userRole === 'HR_ADMIN') {
        return next();
      }
      
      // Check specific permission in the permissions object
      if (userPermissions[requiredPermission] === true) {
        return next();
      }
      
      // For HR roles, check specific permissions based on role
      if (userRole === 'HR_STAFF') {
        // HR Staff has limited permissions
        switch (requiredPermission) {
          case 'canViewEmployees':
          case 'canCreateEmployee':
          case 'canEditEmployee':
          case 'canManageAttendance':
          case 'canManageLeave':
            return next();
          default:
            break;
        }
      } else if (userRole === 'DEPT_HEAD') {
        // Department heads can view employees and manage leave
        switch (requiredPermission) {
          case 'canViewEmployees':
          case 'canManageLeave':
            return next();
          default:
            break;
        }
      } else if (userRole === 'FINANCE_MANAGER') {
        // Finance managers can view employees and manage payroll
        switch (requiredPermission) {
          case 'canViewEmployees':
          case 'canManagePayroll':
            return next();
          default:
            break;
        }
      }
      
      // Access denied
      return res.status(403).json({ 
        message: 'Forbidden - You do not have permission to access this resource' 
      });
    } catch (error) {
      console.error('HR permission check error:', error);
      return res.status(500).json({ message: 'Internal server error during permission verification' });
    }
  };
};

// Check if user can access or manage HR resources for a specific department only
export const checkDepartmentalHRAccess = (allowOtherDepartments = false) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Unauthorized - User not authenticated' });
      }

      const userRole = req.user.role;
      const userDepartment = req.user.department;
      
      // Admin roles always have full access to everything - check first
      if (['IT_ADMIN', 'ADMIN', 'SYSTEM_ADMIN', 'CEO'].includes(userRole)) {
        return next();
      }
      
      // Get the target department from request
      const targetDepartment = req.params.department || req.body.department || req.query.department;
      
      // HR Admin has access to all departments
      if (userRole === 'HR_ADMIN') {
        return next();
      }
      
      // HR Staff can access all departments if allowOtherDepartments is true
      if (userRole === 'HR_STAFF' && allowOtherDepartments) {
        return next();
      }
      
      // If no department is specified in the request, proceed with caution
      if (!targetDepartment) {
        // For safety, we only allow admins to proceed when no department is specified,
        // unless otherwise instructed
        if (allowOtherDepartments || userRole === 'HR_STAFF') {
          return next();
        } else {
          return res.status(400).json({ message: 'Department must be specified' });
        }
      }
      
      // Department heads can only access their own department
      if (userRole === 'DEPT_HEAD' && userDepartment === targetDepartment) {
        return next();
      }
      
      // HR Staff normally has access to all departments but can be restricted
      if (userRole === 'HR_STAFF') {
        return next();
      }
      
      // Access denied
      return res.status(403).json({ 
        message: 'Forbidden - You do not have permission to access resources for this department' 
      });
    } catch (error) {
      console.error('Departmental HR access check error:', error);
      return res.status(500).json({ message: 'Internal server error during department access verification' });
    }
  }; 
}; 