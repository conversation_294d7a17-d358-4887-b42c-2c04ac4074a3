import React, { useMemo } from 'react';
import { 
  Shield, AlertTriangle, XCircle, ArrowLeft, 
  Activity, Clock, User, Check, BarChart 
} from 'lucide-react';
import { SystemLog } from './types';
import { formatSystemDate } from './utils/dateFormat';
import { isSuccessfulLogin, isFailedLogin, isLogout } from './utils/loginTracker';

interface SecurityRiskScoringProps {
  logs: SystemLog[];
  onBack: () => void;
  onViewUserDetails: (username: string) => void;
}

// Risk factor structure
interface RiskFactor {
  id: string;
  title: string;
  description: string;
  weight: number; // 1-10, importance of this factor
  scoreFunction: (userLogs: SystemLog[]) => number; // 0-10, how severe this risk is
}

// User risk profile
interface UserRiskProfile {
  username: string;
  department: string;
  overallScore: number; // 0-100
  lastActivity: Date | null;
  factors: {
    factorId: string;
    score: number;
    normalizedScore: number; // Factor score weighted and scaled to 0-100
    details: string;
  }[];
  recentSuspiciousActivities: SystemLog[];
}

export const SecurityRiskScoring: React.FC<SecurityRiskScoringProps> = ({ 
  logs, 
  onBack,
  onViewUserDetails
}) => {
  // Define risk factors
  const riskFactors: RiskFactor[] = useMemo(() => [
    {
      id: 'failed_login_ratio',
      title: 'Failed Login Attempts',
      description: 'High ratio of failed to successful logins may indicate brute force attempts',
      weight: 8,
      scoreFunction: (userLogs: SystemLog[]) => {
        const failedLogins = userLogs.filter(log => isFailedLogin(log)).length;
        const successfulLogins = userLogs.filter(log => isSuccessfulLogin(log)).length;
        
        if (successfulLogins === 0) {
          return failedLogins > 0 ? 10 : 0; // All failures = max score
        }
        
        const ratio = failedLogins / successfulLogins;
        return Math.min(10, ratio * 5); // Score of 10 if ratio >= 2.0
      }
    },
    {
      id: 'missing_logouts',
      title: 'Missing Logout Events',
      description: 'Sessions without proper logout may indicate abandoned sessions',
      weight: 6,
      scoreFunction: (userLogs: SystemLog[]) => {
        const logins = userLogs.filter(log => isSuccessfulLogin(log)).length;
        const logouts = userLogs.filter(log => isLogout(log)).length;
        
        if (logins === 0) return 0;
        
        const missingRatio = 1 - (logouts / logins);
        return Math.min(10, missingRatio * 10); // Score of 10 if all logins have no logout
      }
    },
    {
      id: 'after_hours',
      title: 'After Hours Activity',
      description: 'Activity outside standard business hours',
      weight: 7,
      scoreFunction: (userLogs: SystemLog[]) => {
        const afterHoursLogs = userLogs.filter(log => {
          const date = new Date(log.timestamp);
          const hour = date.getHours();
          const dayOfWeek = date.getDay(); // 0 = Sunday, 6 = Saturday
          
          // Consider activity suspicious if outside 8am-6pm or on weekends
          return hour < 8 || hour >= 18 || dayOfWeek === 0 || dayOfWeek === 6;
        });
        
        if (userLogs.length === 0) return 0;
        
        const afterHoursRatio = afterHoursLogs.length / userLogs.length;
        return Math.min(10, afterHoursRatio * 10); // Score of 10 if all activity is after hours
      }
    },
    {
      id: 'error_rate',
      title: 'Error & Warning Rate',
      description: 'High number of errors may indicate problematic behavior',
      weight: 5,
      scoreFunction: (userLogs: SystemLog[]) => {
        const errorLogs = userLogs.filter(log => log.type === 'error' || log.type === 'warning');
        
        if (userLogs.length === 0) return 0;
        
        const errorRatio = errorLogs.length / userLogs.length;
        return Math.min(10, errorRatio * 20); // Score of 10 if 50% or more are errors
      }
    },
    {
      id: 'rapid_actions',
      title: 'Rapid Action Sequence',
      description: 'Unusually high number of actions in a short period',
      weight: 6,
      scoreFunction: (userLogs: SystemLog[]) => {
        if (userLogs.length < 10) return 0; // Need enough logs to detect pattern
        
        // Sort logs by timestamp
        const sortedLogs = [...userLogs].sort((a, b) => 
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
        );
        
        // Check for clusters of rapid activity
        let maxActionsPerMinute = 0;
        let minuteWindows = 0;
        
        for (let i = 0; i < sortedLogs.length - 1; i++) {
          const windowStart = new Date(sortedLogs[i].timestamp);
          const windowEnd = new Date(windowStart.getTime() + 60000); // 1 minute later
          
          let actionsInWindow = 1;
          let j = i + 1;
          
          while (j < sortedLogs.length && new Date(sortedLogs[j].timestamp) <= windowEnd) {
            actionsInWindow++;
            j++;
          }
          
          if (actionsInWindow > maxActionsPerMinute) {
            maxActionsPerMinute = actionsInWindow;
          }
          
          minuteWindows++;
        }
        
        // Base score on most active minute
        // Consider 10+ actions per minute as suspicious
        return Math.min(10, maxActionsPerMinute / 2);
      }
    }
  ], []);
  
  // Calculate risk profiles for all users
  const userRiskProfiles = useMemo(() => {
    // Group logs by username
    const userLogsMap = new Map<string, SystemLog[]>();
    logs.forEach(log => {
      if (!userLogsMap.has(log.user)) {
        userLogsMap.set(log.user, []);
      }
      userLogsMap.get(log.user)!.push(log);
    });
    
    const profiles: UserRiskProfile[] = [];
    
    // Calculate risks for each user
    for (const [username, userLogs] of userLogsMap.entries()) {
      if (userLogs.length === 0) continue;
      
      // Extract department from logs if available
      let department = 'Unknown';
      const departmentMatch = userLogs[0].details.match(/Department:\s*([^|]+)/i);
      if (departmentMatch && departmentMatch[1]) {
        department = departmentMatch[1].trim();
      }
      
      // Calculate score for each risk factor
      const factorScores = riskFactors.map(factor => {
        const score = factor.scoreFunction(userLogs);
        
        // Create explanation text
        let details = '';
        if (factor.id === 'failed_login_ratio') {
          const failed = userLogs.filter(log => isFailedLogin(log)).length;
          const success = userLogs.filter(log => isSuccessfulLogin(log)).length;
          details = `${failed} failed vs ${success} successful logins`;
        } else if (factor.id === 'missing_logouts') {
          const logins = userLogs.filter(log => isSuccessfulLogin(log)).length;
          const logouts = userLogs.filter(log => isLogout(log)).length;
          details = `${logins - logouts} sessions without logout`;
        } else if (factor.id === 'after_hours') {
          const afterHours = userLogs.filter(log => {
            const date = new Date(log.timestamp);
            const hour = date.getHours();
            const dayOfWeek = date.getDay();
            return hour < 8 || hour >= 18 || dayOfWeek === 0 || dayOfWeek === 6;
          }).length;
          details = `${afterHours} activities outside business hours`;
        } else if (factor.id === 'error_rate') {
          const errors = userLogs.filter(log => log.type === 'error' || log.type === 'warning').length;
          details = `${errors} errors/warnings (${Math.round((errors / userLogs.length) * 100)}%)`;
        } else if (factor.id === 'rapid_actions') {
          const sortedLogs = [...userLogs].sort((a, b) => 
            new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
          );
          
          let maxActionsPerMinute = 0;
          
          for (let i = 0; i < sortedLogs.length - 1; i++) {
            const windowStart = new Date(sortedLogs[i].timestamp);
            const windowEnd = new Date(windowStart.getTime() + 60000);
            
            let actionsInWindow = 1;
            let j = i + 1;
            
            while (j < sortedLogs.length && new Date(sortedLogs[j].timestamp) <= windowEnd) {
              actionsInWindow++;
              j++;
            }
            
            if (actionsInWindow > maxActionsPerMinute) {
              maxActionsPerMinute = actionsInWindow;
            }
          }
          
          details = `Maximum ${maxActionsPerMinute} actions in one minute`;
        }
        
        // Normalize score based on weight and convert to 0-100 scale
        const normalizedScore = (score * factor.weight) / (riskFactors.reduce((sum, f) => sum + f.weight, 0) / 10);
        
        return {
          factorId: factor.id,
          score,
          normalizedScore,
          details
        };
      });
      
      // Calculate overall score
      const overallScore = Math.min(100, Math.round(
        factorScores.reduce((sum, factor) => sum + factor.normalizedScore, 0)
      ));
      
      // Find recent suspicious activities
      const suspiciousLogs = userLogs.filter(log => 
        log.type === 'error' || 
        log.type === 'warning' || 
        isFailedLogin(log) ||
        log.action?.toLowerCase().includes('fail') ||
        log.action?.toLowerCase().includes('denied') ||
        log.details?.toLowerCase().includes('suspicious')
      )
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 5); // Get 5 most recent suspicious logs
      
      // Find last activity
      const lastActivity = userLogs.length > 0 ? 
        new Date(userLogs.reduce((latest, log) => 
          new Date(log.timestamp) > new Date(latest.timestamp) ? log : latest
        ).timestamp) : null;
      
      profiles.push({
        username,
        department,
        overallScore,
        lastActivity,
        factors: factorScores,
        recentSuspiciousActivities: suspiciousLogs
      });
    }
    
    // Sort by risk score (highest first)
    return profiles.sort((a, b) => b.overallScore - a.overallScore);
  }, [logs, riskFactors]);
  
  // Get risk level category
  const getRiskLevel = (score: number): { 
    label: string; 
    color: string; 
    bgColor: string;
    textColor: string;
  } => {
    if (score >= 70) {
      return { 
        label: 'Critical Risk', 
        color: 'border-red-600', 
        bgColor: 'bg-red-100 dark:bg-red-900/30',
        textColor: 'text-red-700 dark:text-red-300'
      };
    } else if (score >= 50) {
      return { 
        label: 'High Risk', 
        color: 'border-orange-500', 
        bgColor: 'bg-orange-100 dark:bg-orange-900/30',
        textColor: 'text-orange-700 dark:text-orange-300'
      };
    } else if (score >= 30) {
      return { 
        label: 'Medium Risk', 
        color: 'border-amber-500', 
        bgColor: 'bg-amber-100 dark:bg-amber-900/30',
        textColor: 'text-amber-700 dark:text-amber-300'
      };
    } else if (score >= 10) {
      return { 
        label: 'Low Risk', 
        color: 'border-yellow-500', 
        bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',
        textColor: 'text-yellow-700 dark:text-yellow-300'
      };
    } else {
      return { 
        label: 'Minimal Risk', 
        color: 'border-green-500', 
        bgColor: 'bg-green-100 dark:bg-green-900/30',
        textColor: 'text-green-700 dark:text-green-300'
      };
    }
  };
  
  // Format time
  const formatRelativeTime = (date: Date | null): string => {
    if (!date) return 'Never';
    
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return `${Math.floor(diffMins / 1440)}d ago`;
  };
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <button 
          onClick={onBack}
          className="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          <span>Back to All Logs</span>
        </button>
      </div>
      
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 flex items-center gap-2 mb-6">
          <Shield className="h-5 w-5 text-blue-500" />
          Security Risk Analysis
        </h2>
        
        <div className="mb-6">
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
            This analysis examines user activity patterns to identify potential security risks based on behavior.
            Higher scores indicate higher risk levels.
          </p>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-5 gap-4 mb-6">
            {riskFactors.map(factor => (
              <div key={factor.id} className="bg-gray-50 dark:bg-gray-800 p-3 rounded-md border border-gray-200 dark:border-gray-700">
                <h3 className="text-sm font-medium text-gray-800 dark:text-gray-200">{factor.title}</h3>
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">{factor.description}</p>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">Weight: {factor.weight}/10</div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="space-y-6">
          {userRiskProfiles.map(profile => {
            const riskLevel = getRiskLevel(profile.overallScore);
            
            return (
              <div 
                key={profile.username}
                className={`border rounded-md overflow-hidden transition-all hover:shadow-md ${riskLevel.color}`}
              >
                <div className={`p-4 ${riskLevel.bgColor}`}>
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 
                        className="text-base font-medium text-gray-900 dark:text-white cursor-pointer hover:text-blue-600 dark:hover:text-blue-400"
                        onClick={() => onViewUserDetails(profile.username)}
                      >
                        {profile.username}
                      </h3>
                      <div className="text-sm text-gray-600 dark:text-gray-300">{profile.department}</div>
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <div className="text-sm text-gray-600 dark:text-gray-300 flex items-center gap-1">
                        <Clock className="h-3.5 w-3.5" />
                        <span>{formatRelativeTime(profile.lastActivity)}</span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <div 
                          className={`px-3 py-1 rounded-full text-sm font-medium ${riskLevel.textColor} ${riskLevel.bgColor}`}
                        >
                          {riskLevel.label}
                        </div>
                        <div className="text-lg font-bold text-gray-800 dark:text-white">
                          {profile.overallScore}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Risk Factor Scores */}
                  <div className="mt-3 space-y-2">
                    <div className="text-xs text-gray-700 dark:text-gray-300 font-medium">
                      Risk Factors:
                    </div>
                    
                    <div className="flex flex-wrap gap-2">
                      {profile.factors.map(factor => {
                        const factorDef = riskFactors.find(f => f.id === factor.factorId)!;
                        // Score color based on severity
                        const getScoreColor = (score: number) => {
                          if (score >= 8) return 'text-red-600 dark:text-red-400';
                          if (score >= 6) return 'text-orange-600 dark:text-orange-400';
                          if (score >= 4) return 'text-amber-600 dark:text-amber-400';
                          if (score >= 2) return 'text-yellow-600 dark:text-yellow-400';
                          return 'text-green-600 dark:text-green-400';
                        };
                        
                        return (
                          <div 
                            key={factor.factorId}
                            className="bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-md px-2 py-1 text-xs"
                            title={factor.details}
                          >
                            <div className="font-medium text-gray-800 dark:text-gray-100">{factorDef.title}</div>
                            <div className="flex justify-between items-center mt-0.5">
                              <span className="text-gray-600 dark:text-gray-300">{factor.details}</span>
                              <span className={`font-bold ${getScoreColor(factor.score)}`}>{factor.score}/10</span>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                  
                  {/* Suspicious Activities */}
                  {profile.recentSuspiciousActivities.length > 0 && (
                    <div className="mt-4">
                      <div 
                        className="text-xs flex items-center gap-1 text-gray-700 dark:text-gray-300 font-medium mb-2 cursor-pointer"
                      >
                        <AlertTriangle className="h-3.5 w-3.5 text-amber-500" />
                        Recent Suspicious Activities:
                      </div>
                      
                      <div className="space-y-1">
                        {profile.recentSuspiciousActivities.map((log, i) => (
                          <div 
                            key={i} 
                            className="bg-gray-50 dark:bg-gray-800/70 p-2 rounded-md text-xs flex items-start gap-2"
                          >
                            {log.type === 'error' && <XCircle className="h-3.5 w-3.5 text-red-500 mt-0.5 flex-shrink-0" />}
                            {log.type === 'warning' && <AlertTriangle className="h-3.5 w-3.5 text-amber-500 mt-0.5 flex-shrink-0" />}
                            {isFailedLogin(log) && <Activity className="h-3.5 w-3.5 text-red-500 mt-0.5 flex-shrink-0" />}
                            
                            <div className="flex-grow">
                              <div className="flex justify-between">
                                <span className="font-medium text-gray-800 dark:text-gray-200">{log.action}</span>
                                <span className="text-gray-500 dark:text-gray-400">{formatSystemDate(log.timestamp)}</span>
                              </div>
                              <div className="text-gray-600 dark:text-gray-300 truncate">{log.details}</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
          
          {userRiskProfiles.length === 0 && (
            <div className="text-center py-12 bg-gray-50 dark:bg-gray-800/40 rounded-lg border border-gray-200 dark:border-gray-700">
              <Shield className="h-12 w-12 mx-auto text-gray-400 mb-3" />
              <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200">No user data available</h3>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                There is not enough log data to perform a risk analysis.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}; 