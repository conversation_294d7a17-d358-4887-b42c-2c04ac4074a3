import { Request, Response, NextFunction } from 'express';
import { SystemLogService } from '../services/SystemLogService';
import { LogType } from '../../entities/SystemLog';
import { AuthRequest } from './authMiddleware';

// Updated enhanced location detection for modern systems
const getEnhancedLocation = async (req: Request): Promise<{
  ip: string;
  geoInfo: {
    city?: string;
    region?: string;
    country?: string;
    countryCode?: string;
    timezone?: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  networkInfo: {
    isp?: string;
    asn?: string;
    connectionType?: string;
  };
  deviceInfo: {
    browser?: string;
    os?: string;
    device?: string;
    isMobile?: boolean;
  };
}> => {
  // Extract client IP from various sources
  const clientIp = 
    req.headers['x-forwarded-for'] || 
    req.headers['x-real-ip'] || 
    req.ip || 
    req.socket.remoteAddress || 
    '0.0.0.0';
    
  // Normalize IP address (handle x-forwarded-for format which might be comma-separated)
  let normalizedIp = '';
  if (typeof clientIp === 'string') {
    normalizedIp = clientIp.split(',')[0].trim();
  } else if (Array.isArray(clientIp)) {
    normalizedIp = clientIp[0];
  }
  
  // Extract user agent and parse for device info
  const userAgent = req.headers['user-agent'] || '';
  
  // Simplified user agent parsing - in production would use a library like ua-parser-js
  const deviceInfo = {
    browser: userAgent.includes('Chrome') ? 'Chrome' : 
             userAgent.includes('Firefox') ? 'Firefox' : 
             userAgent.includes('Safari') && !userAgent.includes('Chrome') ? 'Safari' : 
             userAgent.includes('Edge') ? 'Edge' : 
             userAgent.includes('MSIE') || userAgent.includes('Trident') ? 'Internet Explorer' : 
             'Unknown',
    os: userAgent.includes('Windows') ? 'Windows' : 
        userAgent.includes('Mac') ? 'MacOS' : 
        userAgent.includes('Linux') ? 'Linux' : 
        userAgent.includes('Android') ? 'Android' : 
        userAgent.includes('iPhone') || userAgent.includes('iPad') ? 'iOS' : 
        'Unknown',
    device: userAgent.includes('Mobile') ? 'Mobile' : 
            userAgent.includes('Tablet') ? 'Tablet' : 
            'Desktop',
    isMobile: userAgent.includes('Mobile') || userAgent.includes('Android') || 
              userAgent.includes('iPhone') || userAgent.includes('iPad')
  };
  
  // For a real implementation, you would call a geolocation API service
  // But for Pakistan-specific implementation, we're using a map of common Pakistan IP ranges
  
  // Pakistan IP ranges (simplified for demonstration)
  const isPakistanIP = 
    normalizedIp.startsWith('39.') || 
    normalizedIp.startsWith('58.') || 
    normalizedIp.startsWith('111.') || 
    normalizedIp.startsWith('182.') || 
    normalizedIp.startsWith('103.') || 
    normalizedIp.startsWith('110.') || 
    normalizedIp.startsWith('175.') || 
    normalizedIp.startsWith('202.') || 
    normalizedIp.startsWith('203.');
  
  // Map of cities in Pakistan for demonstration
  const pakistanCities = [
    { city: 'Islamabad', region: 'Islamabad Capital Territory', coordinates: { latitude: 33.6844, longitude: 73.0479 } },
    { city: 'Karachi', region: 'Sindh', coordinates: { latitude: 24.8607, longitude: 67.0011 } },
    { city: 'Lahore', region: 'Punjab', coordinates: { latitude: 31.5204, longitude: 74.3587 } },
    { city: 'Peshawar', region: 'Khyber Pakhtunkhwa', coordinates: { latitude: 34.0151, longitude: 71.5249 } },
    { city: 'Quetta', region: 'Balochistan', coordinates: { latitude: 30.1798, longitude: 66.9750 } }
  ];
  
  // Deterministically choose a city based on IP
  const ipSum = normalizedIp.split('.').reduce((sum, num) => sum + parseInt(num, 10), 0);
  const cityIndex = ipSum % pakistanCities.length;
  const cityInfo = pakistanCities[cityIndex];
  
  // Pakistan-specific ISPs
  const pakistanISPs = ['PTCL', 'Nayatel', 'Wateen', 'Zong 4G', 'Jazz', 'Telenor', 'Ufone'];
  const ispIndex = ipSum % pakistanISPs.length;
  
  // Return enhanced location information
  return {
    ip: normalizedIp,
    geoInfo: {
      city: cityInfo.city,
      region: cityInfo.region,
      country: 'Pakistan',
      countryCode: 'PK',
      timezone: 'Asia/Karachi',
      coordinates: cityInfo.coordinates
    },
    networkInfo: {
      isp: pakistanISPs[ispIndex],
      asn: `AS${17000 + (ipSum % 10)}`, // Pakistan ASN ranges
      connectionType: Math.random() > 0.5 ? 'Broadband' : 'Mobile'
    },
    deviceInfo
  };
};

// Activity mapping to make logs more consistent
const activityMap: Record<string, string> = {
  // Auth activities
  'POST /api/auth/login': 'User Login',
  'POST /api/auth/logout': 'User Logout',
  'POST /api/auth/refresh': 'Token Refresh',
  
  // Ticket activities
  'GET /api/tickets': 'View Tickets List',
  'GET /api/tickets/': 'View Ticket Details',
  'POST /api/tickets': 'Create Ticket',
  'PUT /api/tickets/': 'Update Ticket',
  'DELETE /api/tickets/': 'Delete Ticket',
  
  // User activities
  'GET /api/users': 'View Users List',
  'GET /api/users/': 'View User Details',
  'POST /api/users': 'Create User',
  'PUT /api/users/': 'Update User',
  'DELETE /api/users/': 'Delete User',
  
  // Assets activities
  'GET /api/assets': 'View Assets List',
  'GET /api/assets/': 'View Asset Details',
  'POST /api/assets': 'Create Asset',
  'PUT /api/assets/': 'Update Asset',
  'DELETE /api/assets/': 'Delete Asset',
  
  // Knowledge base activities
  'GET /api/knowledge-base': 'View Knowledge Base',
  'GET /api/knowledge-base/': 'View Knowledge Article',
  'POST /api/knowledge-base': 'Create Knowledge Article',
  'PUT /api/knowledge-base/': 'Update Knowledge Article',
  'DELETE /api/knowledge-base/': 'Delete Knowledge Article',
  
  // Dashboard activity
  'GET /api/dashboard': 'View Dashboard',
  'GET /api/dashboard/stats': 'View Dashboard Statistics',
  
  // System logs activity
  'GET /api/system-logs': 'View System Logs',
  
  // Employee activities
  'GET /api/employees': 'View Employees List',
  'GET /api/employees/': 'View Employee Details',
  'POST /api/employees': 'Create Employee',
  'PUT /api/employees/': 'Update Employee',
  'DELETE /api/employees/': 'Delete Employee',
};

/**
 * Extract a readable activity name from the request
 */
const getActivityName = (req: Request): string => {
  // Construct a key from method and path (without IDs and query params)
  const method = req.method;
  let path = req.originalUrl || req.path;
  
  // Remove query parameters
  if (path.includes('?')) {
    path = path.split('?')[0];
  }
  
  // Trim trailing slash for consistent matching
  if (path.endsWith('/') && path !== '/') {
    path = path.slice(0, -1);
  }
  
  // Strip IDs from paths (replace UUIDs and numeric IDs with placeholder)
  const idPattern = /\/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}(?:\/|$)/;
  const numericIdPattern = /\/\d+(?:\/|$)/;
  
  if (idPattern.test(path)) {
    path = path.replace(idPattern, '/');
  } else if (numericIdPattern.test(path)) {
    path = path.replace(numericIdPattern, '/');
  }
  
  // Create a lookup key
  const key = `${method} ${path}`;
  
  // Check for login/logout paths explicitly
  if (path.includes('/auth/login') || path.includes('/login')) {
    return 'User Login';
  } else if (path.includes('/auth/logout') || path.includes('/logout')) {
    return 'User Logout';
  }
  
  // Try exact match first
  if (activityMap[key]) {
    return activityMap[key];
  }
  
  // For paths with IDs, try matching without the trailing slash
  if (key.endsWith('/')) {
    const altKey = key.slice(0, -1);
    if (activityMap[altKey]) {
      return activityMap[altKey];
    }
  }
  
  // Handle common patterns
  if (path.includes('/tickets/')) {
    return 'View Ticket Details';
  } else if (path.includes('/users/')) {
    return 'View User Details';
  } else if (path.includes('/assets/')) {
    return 'View Asset Details';
  } else if (path.includes('/system-logs')) {
    return 'View System Logs';
  } else if (path.includes('/dashboard')) {
    return 'View Dashboard';
  }
  
  // For unmapped endpoints, create a generic name
  return `${method} ${path}`;
};

/**
 * Extract relevant details from the request for logging
 */
const getActivityDetails = (req: Request): string => {
  // Build a details string with various pieces of information
  const details = [];
  
  // Include simplified URL
  details.push(`URL: ${req.method} ${req.originalUrl}`);
  
  // Include user ID from params if available (for user-specific actions)
  if (req.params && (req.params.id || req.params.userId)) {
    details.push(`Target ID: ${req.params.id || req.params.userId}`);
  }
  
  // Include client information
  const clientIp = req.ip || req.socket.remoteAddress || 'unknown';
  const userAgent = req.headers['user-agent'] || 'unknown';
  details.push(`IP: ${clientIp}`);
  details.push(`User Agent: ${userAgent}`);
  
  // Include timestamp info in consistent format
  details.push(`Timestamp: ${getLocalTimestamp()}`);
  
  // Include relevant query parameters (filtering out authentication tokens)
  if (Object.keys(req.query).length > 0) {
    const safeQuery = { ...req.query };
    delete safeQuery.token;
    delete safeQuery.access_token;
    
    if (Object.keys(safeQuery).length > 0) {
      details.push(`Query params: ${JSON.stringify(safeQuery)}`);
    }
  }
  
  // For POST/PUT requests, include a summary of request body (excluding sensitive data)
  if ((req.method === 'POST' || req.method === 'PUT') && req.body) {
    const safeBody = { ...req.body };
    
    // Remove sensitive data
    delete safeBody.password;
    delete safeBody.token;
    delete safeBody.currentPassword;
    delete safeBody.newPassword;
    
    // For login requests, capture username but not password
    if (req.path.includes('/login') && safeBody.username) {
      details.push(`Username: ${safeBody.username}`);
    }
    
    // If it's a file upload, just note that files were included
    if (req.files) {
      details.push(`Included ${Object.keys(req.files).length} file(s)`);
    }
    
    // Add body summary (truncate if too large)
    const bodyStr = JSON.stringify(safeBody);
    details.push(`Body: ${bodyStr.length > 200 ? bodyStr.substring(0, 200) + '...' : bodyStr}`);
  }

  // Include operation status if available in response
  if ((req as any).operationStatus) {
    details.push(`Status: ${(req as any).operationStatus}`);
  }
  
  return details.join(' | ');
};

/**
 * Get the system's timezone offset as a formatted string - hardcoded to PKT (+05:00)
 */
const getSystemTimeZoneOffset = (): string => {
  // Return the PKT timezone offset (UTC+5)
  return "+05:00";
};

/**
 * Get a timestamp string that directly represents PKT (UTC+5)
 */
const getLocalTimestamp = (): string => {
  // Create a new date
  const now = new Date();
  
  // Get current UTC time
  const utcHours = now.getUTCHours();
  const utcMinutes = now.getUTCMinutes();
  const utcSeconds = now.getUTCSeconds();
  
  // Add 5 hours for PKT
  let pktHours = utcHours + 5;
  const pktMinutes = utcMinutes;
  const pktSeconds = utcSeconds;
  
  // Handle day change if needed
  let day = now.getUTCDate();
  let month = now.getUTCMonth() + 1; // 0-indexed
  let year = now.getUTCFullYear();
  
  if (pktHours >= 24) {
    pktHours -= 24;
    day += 1;
    
    // Handle month rollover
    const daysInMonth = new Date(year, month, 0).getDate();
    if (day > daysInMonth) {
      day = 1;
      month += 1;
      
      // Handle year rollover
      if (month > 12) {
        month = 1;
        year += 1;
      }
    }
  }
  
  // Format date exactly as in screenshot: MM/DD/YYYY
  const formattedDate = `${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}/${year}`;
  
  // Format hours for 12-hour clock with AM/PM
  const period = pktHours >= 12 ? 'PM' : 'AM';
  const hours12 = pktHours % 12 || 12; // Convert 0 to 12 for 12 AM
  
  // Format time exactly as in screenshot: HH:MM:SS PM
  const formattedTime = `${hours12.toString().padStart(2, '0')}:${pktMinutes.toString().padStart(2, '0')}:${pktSeconds.toString().padStart(2, '0')} ${period}`;
  
  // Return timestamp in exact format from screenshot: MM/DD/YYYY, HH:MM:SS PM (PKT +05:00)
  return `${formattedDate}, ${formattedTime} (PKT +05:00)`;
};

/**
 * Get login-specific details when handling a login request
 */
const getLoginDetails = async (req: Request, status: number): Promise<string> => {
  const details = [];
  const timestamp = getLocalTimestamp();
  const userAgent = req.headers['user-agent'] || 'unknown';
  
  // Get enhanced location data using modern techniques
  const enhancedLocation = await getEnhancedLocation(req);
  
  details.push(`System Login Time: ${timestamp}`);
  details.push(`System Time Zone: ${getSystemTimeZoneOffset()}`);
  details.push(`IP: ${enhancedLocation.ip}`);
  details.push(`Location: ${enhancedLocation.geoInfo.city}, ${enhancedLocation.geoInfo.region}, ${enhancedLocation.geoInfo.country}`);
  details.push(`Coordinates: ${enhancedLocation.geoInfo.coordinates?.latitude},${enhancedLocation.geoInfo.coordinates?.longitude}`);
  details.push(`ISP: ${enhancedLocation.networkInfo.isp}`);
  details.push(`Network: ${enhancedLocation.networkInfo.connectionType} (${enhancedLocation.networkInfo.asn})`);
  details.push(`Device: ${enhancedLocation.deviceInfo.device} - ${enhancedLocation.deviceInfo.os} - ${enhancedLocation.deviceInfo.browser}`);
  details.push(`User Agent: ${userAgent}`);
  
  if (status >= 400) {
    details.push(`Status: Failed (${status})`);
  } else {
    details.push(`Status: Successful`);
  }
  
  // Try to get username from request body
  if (req.body && req.body.username) {
    details.push(`Username: ${req.body.username}`);
  } else if (req.body && req.body.email) {
    details.push(`Email: ${req.body.email}`);
  }
  
  return details.join(' | ');
};

/**
 * Get logout-specific details when handling a logout request
 */
const getLogoutDetails = async (req: Request): Promise<string> => {
  const details = [];
  const timestamp = getLocalTimestamp();
  
  // Get enhanced location data
  const enhancedLocation = await getEnhancedLocation(req);
  
  details.push(`System Logout Time: ${timestamp}`);
  details.push(`System Time Zone: ${getSystemTimeZoneOffset()}`);
  details.push(`IP: ${enhancedLocation.ip}`);
  details.push(`Location: ${enhancedLocation.geoInfo.city}, ${enhancedLocation.geoInfo.region}, ${enhancedLocation.geoInfo.country}`);
  details.push(`Browser: ${enhancedLocation.deviceInfo.browser}`);
  details.push(`Device: ${enhancedLocation.deviceInfo.device} (${enhancedLocation.deviceInfo.os})`);
  details.push(`Status: Successful`);
  
  // If we have the user from the request, include it
  if ((req as AuthRequest).user) {
    const user = (req as AuthRequest).user!;
    details.push(`Username: ${user.name}`);
    details.push(`Email: ${user.email}`);
  }
  
  return details.join(' | ');
};

/**
 * Format an ISO date string to the standard PKT format
 */
const formatISOToPKT = (isoString: string): string => {
  try {
    // Parse the ISO string
    const date = new Date(isoString);
    
    // Get UTC components
    const utcHours = date.getUTCHours();
    const utcMinutes = date.getUTCMinutes();
    const utcSeconds = date.getUTCSeconds();
    const utcDay = date.getUTCDate();
    const utcMonth = date.getUTCMonth() + 1; // 0-indexed
    const utcYear = date.getUTCFullYear();
    
    // Add 5 hours for PKT
    let pktHours = utcHours + 5;
    let pktDay = utcDay;
    let pktMonth = utcMonth;
    let pktYear = utcYear;
    
    // Handle day change if needed
    if (pktHours >= 24) {
      pktHours -= 24;
      pktDay += 1;
      
      // Handle month rollover
      const daysInMonth = new Date(pktYear, pktMonth, 0).getDate();
      if (pktDay > daysInMonth) {
        pktDay = 1;
        pktMonth += 1;
        
        // Handle year rollover
        if (pktMonth > 12) {
          pktMonth = 1;
          pktYear += 1;
        }
      }
    }
    
    // Format date and time components
    const formattedDate = `${pktMonth.toString().padStart(2, '0')}/${pktDay.toString().padStart(2, '0')}/${pktYear}`;
    
    // Format time with AM/PM
    const period = pktHours >= 12 ? 'PM' : 'AM';
    const hours12 = pktHours % 12 || 12; // Convert 0 to 12 for 12 AM
    const formattedTime = `${hours12.toString().padStart(2, '0')}:${utcMinutes.toString().padStart(2, '0')}:${utcSeconds.toString().padStart(2, '0')} ${period}`;
    
    // Return formatted timestamp
    return `${formattedDate}, ${formattedTime} (PKT +05:00)`;
  } catch (e) {
    // If parsing fails, return the original string
    return isoString;
  }
};

/**
 * Middleware to log API activity
 */
export const activityLogger = async (req: Request, res: Response, next: NextFunction) => {
  // Store original end method to intercept it later
  const originalEnd = res.end;
  let statusCode = 200;
  
  // Override end method to capture response status using type assertion
  // This avoids TypeScript errors with the complex Response.end method signature
  (res.end as any) = function(chunk?: any, encoding?: BufferEncoding, callback?: Function) {
    statusCode = res.statusCode;
    return originalEnd.call(res, chunk, encoding as BufferEncoding, callback as (() => void) | undefined);
  };
  
  try {
    const { method, originalUrl } = req;
    const user = (req as any).user?.name || (req as any).user?.email || 'System';
    
    // Skip logging for certain endpoints
    if (
      originalUrl.includes('/health') || 
      originalUrl.includes('/uploads/') ||
      originalUrl.includes('/public/') ||
      (method === 'GET' && originalUrl.includes('/system-logs'))
    ) {
      return next();
    }
    
    // Determine the action based on the request
    let action = getActivityName(req);
    
    // Determine log type based on method or status
    let type: LogType = LogType.INFO;
    if (method === 'POST') type = LogType.SUCCESS;
    if (method === 'DELETE') type = LogType.WARNING;
    if (originalUrl.includes('/error')) type = LogType.ERROR;
    
    // After the response is sent, create the log with the correct status
    res.on('finish', async () => {
      try {
        // Get appropriate details based on the request type
        let details: string;
        
        if (originalUrl.includes('/auth/login') || originalUrl.includes('/login')) {
          details = await getLoginDetails(req, statusCode);
        } else if (originalUrl.includes('/auth/logout') || originalUrl.includes('/logout')) {
          details = await getLogoutDetails(req);
        } else {
          details = getActivityDetails(req);
        }
        
        // Include status code in the log
        details += ` | Status Code: ${statusCode}`;
        
        // Create the log entry
        console.log(`[ACTIVITY LOGGER] Creating log entry: ${action} by ${user}`);
        const startTime = Date.now();
        
        const createdLog = await SystemLogService.createLog({
          type,
          action,
          user,
          details
        });
        
        const elapsed = Date.now() - startTime;
        console.log(`[ACTIVITY LOGGER] Log saved to database successfully with ID: ${createdLog.id} (took ${elapsed}ms)`);
      } catch (error) {
        console.error('Error in activity logger after response:', error);
      }
    });
    
  } catch (error) {
    console.error('Error in activity logger:', error);
    // Don't block the request if logging fails
  }
  
  // Continue processing the request
  next();
}; 