interface Holiday {
  id: number;
  name: string;
  date: string;
  type: 'public' | 'company' | 'optional';
  description?: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface HolidayConfiguration {
  holidays: Holiday[];
  weekendDays: number[];
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

class HolidayService {
  private static readonly BASE_URL = '/api/holidays';
  private static readonly CONFIG_URL = '/api/holidays/config';

  // Fetch all holidays
  static async getHolidays(year?: number): Promise<ApiResponse<Holiday[]>> {
    try {
      const url = year 
        ? `${this.BASE_URL}?year=${year}` 
        : this.BASE_URL;
        
      const response = await fetch(url);
      const data = await response.json();
      
      if (response.ok && data.success) {
        return {
          success: true,
          data: data.holidays || data.data || []
        };
      } else {
        return {
          success: false,
          error: data.message || 'Failed to fetch holidays'
        };
      }
    } catch (error) {
      console.error('Error fetching holidays:', error);
      return {
        success: false,
        error: 'Network error while fetching holidays'
      };
    }
  }

  // Create a new holiday
  static async createHoliday(holiday: Omit<Holiday, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Holiday>> {
    try {
      const response = await fetch(this.BASE_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(holiday),
      });
      
      const data = await response.json();
      
      if (response.ok && data.success) {
        return {
          success: true,
          data: data.holiday || data.data
        };
      } else {
        return {
          success: false,
          error: data.message || 'Failed to create holiday'
        };
      }
    } catch (error) {
      console.error('Error creating holiday:', error);
      return {
        success: false,
        error: 'Network error while creating holiday'
      };
    }
  }

  // Update an existing holiday
  static async updateHoliday(id: number, holiday: Partial<Holiday>): Promise<ApiResponse<Holiday>> {
    try {
      const response = await fetch(`${this.BASE_URL}/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(holiday),
      });
      
      const data = await response.json();
      
      if (response.ok && data.success) {
        return {
          success: true,
          data: data.holiday || data.data
        };
      } else {
        return {
          success: false,
          error: data.message || 'Failed to update holiday'
        };
      }
    } catch (error) {
      console.error('Error updating holiday:', error);
      return {
        success: false,
        error: 'Network error while updating holiday'
      };
    }
  }

  // Delete a holiday
  static async deleteHoliday(id: number): Promise<ApiResponse<void>> {
    try {
      const response = await fetch(`${this.BASE_URL}/${id}`, {
        method: 'DELETE',
      });
      
      const data = await response.json();
      
      if (response.ok && data.success) {
        return {
          success: true
        };
      } else {
        return {
          success: false,
          error: data.message || 'Failed to delete holiday'
        };
      }
    } catch (error) {
      console.error('Error deleting holiday:', error);
      return {
        success: false,
        error: 'Network error while deleting holiday'
      };
    }
  }

  // Get holiday configuration (holidays + weekend settings)
  static async getHolidayConfiguration(): Promise<ApiResponse<HolidayConfiguration>> {
    try {
      const response = await fetch(this.CONFIG_URL);
      const data = await response.json();
      
      if (response.ok && data.success) {
        return {
          success: true,
          data: {
            holidays: data.holidays || data.data?.holidays || [],
            weekendDays: data.weekendDays || data.data?.weekendDays || [0, 6]
          }
        };
      } else {
        return {
          success: false,
          error: data.message || 'Failed to fetch holiday configuration'
        };
      }
    } catch (error) {
      console.error('Error fetching holiday configuration:', error);
      return {
        success: false,
        error: 'Network error while fetching holiday configuration'
      };
    }
  }

  // Save holiday configuration (holidays + weekend settings)
  static async saveHolidayConfiguration(config: HolidayConfiguration): Promise<ApiResponse<HolidayConfiguration>> {
    try {
      const response = await fetch(this.CONFIG_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });
      
      const data = await response.json();
      
      if (response.ok && data.success) {
        return {
          success: true,
          data: data.config || data.data
        };
      } else {
        return {
          success: false,
          error: data.message || 'Failed to save holiday configuration'
        };
      }
    } catch (error) {
      console.error('Error saving holiday configuration:', error);
      return {
        success: false,
        error: 'Network error while saving holiday configuration'
      };
    }
  }

  // Bulk import holidays
  static async importHolidays(holidays: Omit<Holiday, 'id' | 'createdAt' | 'updatedAt'>[]): Promise<ApiResponse<Holiday[]>> {
    try {
      const response = await fetch(`${this.BASE_URL}/import`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ holidays }),
      });
      
      const data = await response.json();
      
      if (response.ok && data.success) {
        return {
          success: true,
          data: data.holidays || data.data || []
        };
      } else {
        return {
          success: false,
          error: data.message || 'Failed to import holidays'
        };
      }
    } catch (error) {
      console.error('Error importing holidays:', error);
      return {
        success: false,
        error: 'Network error while importing holidays'
      };
    }
  }

  // Get upcoming holidays
  static async getUpcomingHolidays(limit: number = 10): Promise<ApiResponse<Holiday[]>> {
    try {
      const response = await fetch(`${this.BASE_URL}/upcoming?limit=${limit}`);
      const data = await response.json();
      
      if (response.ok && data.success) {
        return {
          success: true,
          data: data.holidays || data.data || []
        };
      } else {
        return {
          success: false,
          error: data.message || 'Failed to fetch upcoming holidays'
        };
      }
    } catch (error) {
      console.error('Error fetching upcoming holidays:', error);
      return {
        success: false,
        error: 'Network error while fetching upcoming holidays'
      };
    }
  }

  // Toggle holiday active status
  static async toggleHolidayStatus(id: number): Promise<ApiResponse<Holiday>> {
    try {
      const response = await fetch(`${this.BASE_URL}/${id}/toggle-status`, {
        method: 'PATCH',
      });
      
      const data = await response.json();
      
      if (response.ok && data.success) {
        return {
          success: true,
          data: data.holiday || data.data
        };
      } else {
        return {
          success: false,
          error: data.message || 'Failed to toggle holiday status'
        };
      }
    } catch (error) {
      console.error('Error toggling holiday status:', error);
      return {
        success: false,
        error: 'Network error while toggling holiday status'
      };
    }
  }

  // Save to localStorage as backup
  static saveToLocalStorage(config: HolidayConfiguration): void {
    try {
      localStorage.setItem('holiday_config', JSON.stringify(config));
    } catch (error) {
      console.error('Error saving holidays to localStorage:', error);
    }
  }

  // Get from localStorage as fallback
  static getFromLocalStorage(): HolidayConfiguration {
    try {
      const stored = localStorage.getItem('holiday_config');
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error reading holidays from localStorage:', error);
    }
    
    return {
      holidays: [],
      weekendDays: [0, 6] // Default to Sunday and Saturday
    };
  }
}

export default HolidayService;
export type { Holiday, HolidayConfiguration, ApiResponse }; 