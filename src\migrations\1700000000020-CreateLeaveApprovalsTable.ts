import { MigrationInterface, QueryRunner, Table, TableIndex, TableForeignKey } from 'typeorm';

export class CreateLeaveApprovalsTable1700000000020 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'leave_approvals',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'leaveRequestId',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'level',
            type: 'enum',
            enum: ['MANAGER', 'HR', 'DIRECTOR'],
            isNullable: false,
          },
          {
            name: 'approverId',
            type: 'varchar',
            length: '36',
            isNullable: true,
          },
          {
            name: 'approverName',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['pending', 'approved', 'rejected'],
            default: "'pending'",
            isNullable: false,
          },
          {
            name: 'comments',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'decisionAt',
            type: 'datetime',
            isNullable: true,
          },
          {
            name: 'sequence',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
          {
            name: 'updatedAt',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
        ],
      }),
      true
    );

    // Create foreign key constraint to leave_requests table
    await queryRunner.createForeignKey(
      'leave_approvals',
      new TableForeignKey({
        columnNames: ['leaveRequestId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'leave_requests',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      })
    );

    // Create foreign key constraint to users table (for approverId)
    await queryRunner.createForeignKey(
      'leave_approvals',
      new TableForeignKey({
        columnNames: ['approverId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
      })
    );

    // Create indexes for better query performance
    await queryRunner.createIndex(
      'leave_approvals',
      new TableIndex({
        name: 'IDX_leave_approvals_leave_request_id',
        columnNames: ['leaveRequestId']
      })
    );

    await queryRunner.createIndex(
      'leave_approvals',
      new TableIndex({
        name: 'IDX_leave_approvals_approver_id',
        columnNames: ['approverId']
      })
    );

    await queryRunner.createIndex(
      'leave_approvals',
      new TableIndex({
        name: 'IDX_leave_approvals_level',
        columnNames: ['level']
      })
    );

    await queryRunner.createIndex(
      'leave_approvals',
      new TableIndex({
        name: 'IDX_leave_approvals_status',
        columnNames: ['status']
      })
    );

    // Create unique constraint to prevent duplicate approvals for same request and level
    await queryRunner.createIndex(
      'leave_approvals',
      new TableIndex({
        name: 'IDX_leave_approvals_unique_request_level',
        columnNames: ['leaveRequestId', 'level'],
        isUnique: true
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('leave_approvals');
  }
} 