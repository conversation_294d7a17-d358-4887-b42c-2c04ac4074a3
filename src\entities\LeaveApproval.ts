import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, Join<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { LeaveRequest } from './LeaveRequest';
import { User } from './User';

export enum ApprovalLevel {
  MANAGER = 'MANAGER',
  HR = 'HR',
  DIRECTOR = 'DIRECTOR'
}

export enum ApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled'
}

@Entity('leave_approvals')
export class LeaveApproval {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  leaveRequestId: number;

  @ManyToOne(() => LeaveRequest, leaveRequest => leaveRequest.approvals, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'leaveRequestId' })
  leaveRequest: LeaveRequest;

  @Column({
    type: 'enum',
    enum: ApprovalLevel
  })
  level: ApprovalLevel;

  @Column({ type: 'varchar', length: 36, nullable: true })
  approverId?: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'approverId' })
  approver?: User;

  @Column({ type: 'varchar', length: 255, nullable: true })
  approverName?: string;

  @Column({
    type: 'enum',
    enum: ApprovalStatus,
    default: ApprovalStatus.PENDING
  })
  status: ApprovalStatus;

  @Column({ type: 'text', nullable: true })
  comments?: string;

  @Column({ type: 'datetime', nullable: true })
  decisionAt?: Date;

  @Column({ type: 'int', nullable: true })
  sequence: number; // Order of approval (1 for manager, 2 for HR, etc.)

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Helper methods
  get isPending(): boolean {
    return this.status === ApprovalStatus.PENDING;
  }

  get isApproved(): boolean {
    return this.status === ApprovalStatus.APPROVED;
  }

  get isRejected(): boolean {
    return this.status === ApprovalStatus.REJECTED;
  }

  get isCancelled(): boolean {
    return this.status === ApprovalStatus.CANCELLED;
  }
} 