import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddCreatedByToLeaveRequests1716000000000 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.addColumn("leave_requests", new TableColumn({
            name: "createdBy",
            type: "varchar",
            length: "255",
            isNullable: true,
        }));
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn("leave_requests", "createdBy");
    }
} 