import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { Employee } from './Employee';

@Entity('employee_health')
export class EmployeeHealth {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'text', nullable: true })
  vaccinationRecords: string;

  @Column({ type: 'text', nullable: true })
  medicalHistory: string;

  @Column({ type: 'varchar', length: 255,  nullable: true })
  bloodGroup: string;

  @Column({ type: 'text', nullable: true })
  allergies: string;

  @Column({ type: 'text', nullable: true })
  chronicConditions: string;

  @Column({ type: 'text', nullable: true })
  regularMedications: string;

  // Relation to Employee
  @ManyToOne(() => Employee, employee => employee.healthRecords, { onDelete: 'CASCADE' })
  @JoinC<PERSON>umn()
  employee: Employee;

  // System columns
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 