import React, { useState } from 'react';
import { 
  Clock, 
  CheckSquare, 
  User, 
  Calendar, 
  AlertTriangle, 
  Play, 
  Pause, 
  Plus,
  MessageSquare,
  Paperclip,
  MoreVertical,
  Link as LinkIcon,
  Timer
} from 'lucide-react';

interface ChecklistItem {
  id: string;
  text: string;
  completed: boolean;
  createdAt: string;
  completedAt?: string;
  completedBy?: string;
}

interface TaskDependency {
  id: number;
  title: string;
  status: string;
}

interface TimeEntry {
  id: number;
  durationMinutes: number;
  startTime: string;
  endTime?: string;
  description?: string;
  type: 'manual' | 'timer' | 'imported';
}

interface EnhancedTask {
  id: number;
  title: string;
  description?: string;
  status: 'todo' | 'in_progress' | 'in_review' | 'done' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'critical';
  type: 'task' | 'bug' | 'feature' | 'improvement' | 'research' | 'subtask';
  assignedTo?: {
    id: string;
    name: string;
    avatar?: string;
  };
  dueDate?: string;
  estimatedHours?: number;
  timeSpentMinutes: number;
  progress: number;
  checklist?: ChecklistItem[];
  dependencies?: TaskDependency[];
  subtasks?: EnhancedTask[];
  isBlocked: boolean;
  blockReason?: string;
  requiresApproval: boolean;
  approvalStatus: 'not_required' | 'pending' | 'approved' | 'rejected';
  tags?: string[];
  commentsCount: number;
  attachmentsCount: number;
  timeEntries?: TimeEntry[];
  isTimerRunning?: boolean;
}

interface EnhancedTaskCardProps {
  task: EnhancedTask;
  onTaskUpdate: (taskId: number, updates: Partial<EnhancedTask>) => void;
  onStartTimer: (taskId: number) => void;
  onStopTimer: (taskId: number) => void;
  onAddTimeEntry: (taskId: number, entry: Omit<TimeEntry, 'id'>) => void;
  onUpdateChecklist: (taskId: number, checklist: ChecklistItem[]) => void;
  onViewDetails: (taskId: number) => void;
}

export function EnhancedTaskCard({
  task,
  onTaskUpdate,
  onStartTimer,
  onStopTimer,
  onAddTimeEntry,
  onUpdateChecklist,
  onViewDetails
}: EnhancedTaskCardProps) {
  const [showTimeEntry, setShowTimeEntry] = useState(false);
  const [manualTime, setManualTime] = useState({ hours: 0, minutes: 0, description: '' });

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'border-red-500 bg-red-50';
      case 'high': return 'border-orange-500 bg-orange-50';
      case 'medium': return 'border-yellow-500 bg-yellow-50';
      case 'low': return 'border-green-500 bg-green-50';
      default: return 'border-gray-300 bg-white';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'todo': return 'bg-gray-100 text-gray-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'in_review': return 'bg-yellow-100 text-yellow-800';
      case 'done': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const handleChecklistToggle = (itemId: string) => {
    if (!task.checklist) return;
    
    const updatedChecklist = task.checklist.map(item => 
      item.id === itemId 
        ? { 
            ...item, 
            completed: !item.completed,
            completedAt: !item.completed ? new Date().toISOString() : undefined
          }
        : item
    );
    
    onUpdateChecklist(task.id, updatedChecklist);
  };

  const handleAddManualTime = () => {
    const totalMinutes = manualTime.hours * 60 + manualTime.minutes;
    if (totalMinutes > 0) {
      onAddTimeEntry(task.id, {
        durationMinutes: totalMinutes,
        description: manualTime.description,
        type: 'manual',
        startTime: new Date().toISOString()
      });
      setManualTime({ hours: 0, minutes: 0, description: '' });
      setShowTimeEntry(false);
    }
  };

  const completedChecklistItems = task.checklist?.filter(item => item.completed).length || 0;
  const totalChecklistItems = task.checklist?.length || 0;
  const checklistProgress = totalChecklistItems > 0 ? (completedChecklistItems / totalChecklistItems) * 100 : 0;

  const blockedDependencies = task.dependencies?.filter(dep => dep.status !== 'done').length || 0;

  return (
    <div className={`rounded-lg border-l-4 p-4 shadow-sm hover:shadow-md transition-shadow ${getPriorityColor(task.priority)}`}>
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <h3 
              className="font-medium text-gray-900 cursor-pointer hover:text-blue-600"
              onClick={() => onViewDetails(task.id)}
            >
              {task.title}
            </h3>
            {task.isBlocked && (
              <AlertTriangle className="h-4 w-4 text-red-500" title={task.blockReason} />
            )}
            {task.requiresApproval && (
              <div className={`px-2 py-1 text-xs rounded-full ${
                task.approvalStatus === 'approved' ? 'bg-green-100 text-green-800' :
                task.approvalStatus === 'rejected' ? 'bg-red-100 text-red-800' :
                'bg-yellow-100 text-yellow-800'
              }`}>
                {task.approvalStatus}
              </div>
            )}
          </div>
          
          <div className="flex items-center gap-4 text-sm text-gray-500">
            <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(task.status)}`}>
              {task.status.replace('_', ' ')}
            </span>
            
            {task.assignedTo && (
              <div className="flex items-center gap-1">
                <User className="h-3 w-3" />
                <span>{task.assignedTo.name}</span>
              </div>
            )}
            
            {task.dueDate && (
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span>{new Date(task.dueDate).toLocaleDateString()}</span>
              </div>
            )}
          </div>
        </div>
        
        <button className="p-1 hover:bg-gray-100 rounded">
          <MoreVertical className="h-4 w-4 text-gray-400" />
        </button>
      </div>

      {/* Progress Bar */}
      {task.progress > 0 && (
        <div className="mb-3">
          <div className="flex justify-between text-xs text-gray-500 mb-1">
            <span>Progress</span>
            <span>{task.progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${task.progress}%` }}
            />
          </div>
        </div>
      )}

      {/* Checklist Preview */}
      {task.checklist && task.checklist.length > 0 && (
        <div className="mb-3">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
            <span className="flex items-center gap-1">
              <CheckSquare className="h-4 w-4" />
              Checklist ({completedChecklistItems}/{totalChecklistItems})
            </span>
            <span>{Math.round(checklistProgress)}%</span>
          </div>
          
          <div className="space-y-1 max-h-20 overflow-y-auto">
            {task.checklist.slice(0, 3).map(item => (
              <label key={item.id} className="flex items-center gap-2 text-sm cursor-pointer">
                <input
                  type="checkbox"
                  checked={item.completed}
                  onChange={() => handleChecklistToggle(item.id)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className={item.completed ? 'line-through text-gray-500' : 'text-gray-700'}>
                  {item.text}
                </span>
              </label>
            ))}
            {task.checklist.length > 3 && (
              <div className="text-xs text-gray-500 pl-6">
                +{task.checklist.length - 3} more items
              </div>
            )}
          </div>
        </div>
      )}

      {/* Dependencies Warning */}
      {blockedDependencies > 0 && (
        <div className="mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm">
          <div className="flex items-center gap-1 text-yellow-800">
            <LinkIcon className="h-4 w-4" />
            <span>Waiting for {blockedDependencies} dependencies</span>
          </div>
        </div>
      )}

      {/* Time Tracking */}
      <div className="mb-3">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-4">
            <span className="flex items-center gap-1 text-gray-600">
              <Clock className="h-4 w-4" />
              {formatTime(task.timeSpentMinutes)}
              {task.estimatedHours && (
                <span className="text-gray-400">/ {task.estimatedHours}h</span>
              )}
            </span>
            
            {task.isTimerRunning ? (
              <button
                onClick={() => onStopTimer(task.id)}
                className="flex items-center gap-1 px-2 py-1 bg-red-100 text-red-700 rounded text-xs hover:bg-red-200"
              >
                <Pause className="h-3 w-3" />
                Stop
              </button>
            ) : (
              <button
                onClick={() => onStartTimer(task.id)}
                className="flex items-center gap-1 px-2 py-1 bg-green-100 text-green-700 rounded text-xs hover:bg-green-200"
              >
                <Play className="h-3 w-3" />
                Start
              </button>
            )}
          </div>
          
          <button
            onClick={() => setShowTimeEntry(!showTimeEntry)}
            className="flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs hover:bg-blue-200"
          >
            <Plus className="h-3 w-3" />
            Add Time
          </button>
        </div>

        {/* Manual Time Entry */}
        {showTimeEntry && (
          <div className="mt-2 p-3 bg-gray-50 rounded border">
            <div className="flex items-center gap-2 mb-2">
              <input
                type="number"
                placeholder="Hours"
                value={manualTime.hours}
                onChange={(e) => setManualTime(prev => ({ ...prev, hours: parseInt(e.target.value) || 0 }))}
                className="w-16 px-2 py-1 border rounded text-sm"
                min="0"
              />
              <span className="text-sm text-gray-500">h</span>
              <input
                type="number"
                placeholder="Minutes"
                value={manualTime.minutes}
                onChange={(e) => setManualTime(prev => ({ ...prev, minutes: parseInt(e.target.value) || 0 }))}
                className="w-16 px-2 py-1 border rounded text-sm"
                min="0"
                max="59"
              />
              <span className="text-sm text-gray-500">m</span>
            </div>
            <input
              type="text"
              placeholder="Description (optional)"
              value={manualTime.description}
              onChange={(e) => setManualTime(prev => ({ ...prev, description: e.target.value }))}
              className="w-full px-2 py-1 border rounded text-sm mb-2"
            />
            <div className="flex gap-2">
              <button
                onClick={handleAddManualTime}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                Add
              </button>
              <button
                onClick={() => setShowTimeEntry(false)}
                className="px-3 py-1 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between text-sm text-gray-500">
        <div className="flex items-center gap-4">
          {task.subtasks && task.subtasks.length > 0 && (
            <span className="flex items-center gap-1">
              <CheckSquare className="h-4 w-4" />
              {task.subtasks.filter(st => st.status === 'done').length}/{task.subtasks.length} subtasks
            </span>
          )}
          
          <span className="flex items-center gap-1">
            <MessageSquare className="h-4 w-4" />
            {task.commentsCount}
          </span>
          
          <span className="flex items-center gap-1">
            <Paperclip className="h-4 w-4" />
            {task.attachmentsCount}
          </span>
        </div>
        
        {task.tags && task.tags.length > 0 && (
          <div className="flex gap-1">
            {task.tags.slice(0, 2).map(tag => (
              <span key={tag} className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                {tag}
              </span>
            ))}
            {task.tags.length > 2 && (
              <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded text-xs">
                +{task.tags.length - 2}
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
