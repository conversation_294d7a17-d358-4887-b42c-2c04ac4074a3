import { MigrationInterface, QueryRunner } from "typeorm";

export class FixEmployeeTableRowSize1746000000013 implements MigrationInterface {
    name = 'FixEmployeeTableRowSize1746000000013'

    public async up(queryRunner: QueryRunner): Promise<void> {
        try {
            // Convert more fields to TEXT type to reduce row size
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`profileImagePath\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`linkedinProfile\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`emergencyContactName\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`emergencyContactPhone\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`emergencyContactRelationship\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`project\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`location\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`employeeLevel\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`reportingTo\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`vehicleMakeModel\` TEXT NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`vehicleColor\` TEXT NULL`);
            
            // Now add the mileageAtIssuance column as TEXT type if it doesn't exist
            const mileageColumnExists = await queryRunner.query(
                `SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
                 WHERE TABLE_SCHEMA = 'ims_db' AND TABLE_NAME = 'employees' 
                 AND COLUMN_NAME = 'mileageAtIssuance'`
            );
            
            if (mileageColumnExists.length === 0) {
                await queryRunner.query(`ALTER TABLE \`employees\` ADD \`mileageAtIssuance\` TEXT NULL`);
                console.log("Added mileageAtIssuance column as TEXT type");
            } else {
                // Ensure it's TEXT type
                await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`mileageAtIssuance\` TEXT NULL`);
                console.log("Updated mileageAtIssuance column to TEXT type");
            }
            
            console.log("Successfully optimized employee table row size");
        } catch (error) {
            console.error("Error in FixEmployeeTableRowSize migration:", error);
            throw error;
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        try {
            // Convert TEXT fields back to varchar
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`profileImagePath\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`linkedinProfile\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`emergencyContactName\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`emergencyContactPhone\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`emergencyContactRelationship\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`project\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`location\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`employeeLevel\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`reportingTo\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`vehicleMakeModel\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`vehicleColor\` varchar(255) NULL`);
            await queryRunner.query(`ALTER TABLE \`employees\` MODIFY \`mileageAtIssuance\` varchar(255) NULL`);
            
            console.log("Successfully reverted field type changes");
        } catch (error) {
            console.error("Error in reverting FixEmployeeTableRowSize migration:", error);
            throw error;
        }
    }
} 