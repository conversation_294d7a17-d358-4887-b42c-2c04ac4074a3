import { DataSource } from 'typeorm';
import { UpdateUserSchema1747700000001 } from '../migrations/1747700000001-UpdateUserSchema';

// Database configuration
const AppDataSource = new DataSource({
  type: "mysql",
  host: process.env.DB_HOST || "localhost",
  port: parseInt(process.env.DB_PORT || "3306"),
  username: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "root",
  database: process.env.DB_NAME || "ims_db",
  synchronize: false,
  logging: true,
  entities: [],
  subscribers: [],
  migrations: []
});

const runUserSchemaMigration = async () => {
  console.log("Starting User schema migration to fix database issues...");

  try {
    // Initialize the data source
    await AppDataSource.initialize();
    console.log("Database connection established");

    // Create migration instance
    const migration = new UpdateUserSchema1747700000001();
    
    // Run the migration
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    
    console.log("Running User schema migration...");
    await migration.up(queryRunner);
    
    await queryRunner.release();
    console.log("✅ User schema migration completed successfully!");
    console.log("You can now log in to the system.");
    
  } catch (error) {
    console.error("❌ Migration failed:", error);
    process.exit(1);
  } finally {
    try {
      await AppDataSource.destroy();
      console.log("Database connection closed");
    } catch (error) {
      console.error("Error closing database connection:", error);
    }
  }
};

// Run the migration
runUserSchemaMigration(); 