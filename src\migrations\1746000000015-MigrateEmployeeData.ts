import { MigrationInterface, QueryRunner } from "typeorm";

export class MigrateEmployeeData1746000000015 implements MigrationInterface {
    name = 'MigrateEmployeeData1746000000015'

    public async up(queryRunner: QueryRunner): Promise<void> {
        try {
            // 1. Get all employees from the old table
            const employees = await queryRunner.query(`SELECT * FROM employees`);
            console.log(`Found ${employees.length} employees to migrate data for`);
            
            // 2. For each employee, insert data into the new normalized tables
            for (const employee of employees) {
                // Insert into employee_contacts
                await queryRunner.query(`
                    INSERT INTO employee_contacts (
                        mobileNumber, officialNumber, officialEmail, personalEmail,
                        permanentAddress, currentAddress, emergencyContactName,
                        emergencyContactPhone, emergencyContactRelationship,
                        linkedinProfile, otherSocialProfiles, employeeId
                    ) VALUES (
                        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                    )
                `, [
                    employee.mobileNumber || '', 
                    employee.officialNumber || null,
                    employee.officialEmail || null,
                    employee.personalEmail || null,
                    employee.permanentAddress || null,
                    employee.currentAddress || null,
                    employee.emergencyContactName || null,
                    employee.emergencyContactPhone || null,
                    employee.emergencyContactRelationship || null,
                    employee.linkedinProfile || null,
                    employee.otherSocialProfiles || null,
                    employee.id
                ]);
                
                // Insert into employee_jobs
                await queryRunner.query(`
                    INSERT INTO employee_jobs (
                        designation, department, project, location, 
                        employmentType, employmentStatus, employeeLevel,
                        joinDate, probationEndDate, noticePeriod, reportingTo,
                        previousEmployeeId, remoteWorkEligible, nextReviewDate,
                        trainingRequirements, workSchedule, shiftType, employeeId
                    ) VALUES (
                        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                    )
                `, [
                    employee.designation || '',
                    employee.department || '',
                    employee.project || null,
                    employee.location || null,
                    employee.employmentType || 'Full-time',
                    employee.employmentStatus || 'Active',
                    employee.employeeLevel || null,
                    employee.joinDate || '',
                    employee.probationEndDate || null,
                    employee.noticePeriod || null,
                    employee.reportingTo || null,
                    employee.previousEmployeeId || null,
                    employee.remoteWorkEligible || 0,
                    employee.nextReviewDate || null,
                    employee.trainingRequirements || null,
                    employee.workSchedule || null,
                    employee.shiftType || null,
                    employee.id
                ]);
                
                // Insert into employee_benefits
                await queryRunner.query(`
                    INSERT INTO employee_benefits (
                        totalSalary, salaryTier, cashAmount, bankAmount, paymentMode,
                        foodAllowanceInSalary, fuelAllowanceInSalary, numberOfMeals,
                        fuelInLiters, fuelAmount, foodProvidedByCompany,
                        bankName, bankBranch, accountNumber, accountTitle, iban,
                        healthInsuranceProvider, healthInsurancePolicyNumber, healthInsuranceExpiryDate,
                        lifeInsuranceProvider, lifeInsurancePolicyNumber, lifeInsuranceExpiryDate,
                        accommodationProvidedByEmployer, accommodationType, accommodationAddress,
                        employeeId
                    ) VALUES (
                        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                    )
                `, [
                    employee.totalSalary || null,
                    employee.salaryTier || null,
                    employee.cashAmount || null,
                    employee.bankAmount || null,
                    employee.paymentMode || null,
                    employee.foodAllowanceInSalary || 0,
                    employee.fuelAllowanceInSalary || 0,
                    employee.numberOfMeals || null,
                    employee.fuelInLiters || null,
                    employee.fuelAmount || null,
                    employee.foodProvidedByCompany || 0,
                    employee.bankName || null,
                    employee.bankBranch || null,
                    employee.accountNumber || null,
                    employee.accountTitle || null,
                    employee.iban || null,
                    employee.healthInsuranceProvider || null,
                    employee.healthInsurancePolicyNumber || null,
                    employee.healthInsuranceExpiryDate || null,
                    employee.lifeInsuranceProvider || null,
                    employee.lifeInsurancePolicyNumber || null,
                    employee.lifeInsuranceExpiryDate || null,
                    employee.accommodationProvidedByEmployer || 0,
                    employee.accommodationType || null,
                    employee.accommodationAddress || null,
                    employee.id
                ]);
                
                // 3. Process JSON fields to normalize into related tables
                // Education
                if (employee.educationEntries) {
                    try {
                        const educationEntries = JSON.parse(employee.educationEntries);
                        if (Array.isArray(educationEntries)) {
                            for (const entry of educationEntries) {
                                await queryRunner.query(`
                                    INSERT INTO employee_education (
                                        educationLevel, degree, major, institution,
                                        graduationYear, grade, employeeId
                                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                                `, [
                                    entry.educationLevel || '',
                                    entry.degree || '',
                                    entry.major || null,
                                    entry.institution || '',
                                    entry.graduationYear || '',
                                    entry.grade || null,
                                    employee.id
                                ]);
                            }
                        }
                    } catch (e) {
                        console.error(`Error parsing educationEntries for employee ${employee.id}:`, e);
                    }
                }
                
                // Experience
                if (employee.experienceEntries) {
                    try {
                        const experienceEntries = JSON.parse(employee.experienceEntries);
                        if (Array.isArray(experienceEntries)) {
                            for (const entry of experienceEntries) {
                                await queryRunner.query(`
                                    INSERT INTO employee_experience (
                                        companyName, jobTitle, startDate, endDate,
                                        currentlyWorking, jobDescription, employeeId
                                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                                `, [
                                    entry.companyName || '',
                                    entry.jobTitle || '',
                                    entry.startDate || '',
                                    entry.endDate || null,
                                    entry.currentlyWorking ? 1 : 0,
                                    entry.jobDescription || null,
                                    employee.id
                                ]);
                            }
                        }
                    } catch (e) {
                        console.error(`Error parsing experienceEntries for employee ${employee.id}:`, e);
                    }
                }
                
                // Family (Children and Dependents)
                if (employee.children) {
                    try {
                        const children = JSON.parse(employee.children);
                        if (Array.isArray(children)) {
                            for (const child of children) {
                                await queryRunner.query(`
                                    INSERT INTO employee_family (
                                        name, dateOfBirth, relationship, gender,
                                        type, employeeId
                                    ) VALUES (?, ?, ?, ?, ?, ?)
                                `, [
                                    child.name || '',
                                    child.dateOfBirth || null,
                                    child.relationship || 'Child',
                                    child.gender || null,
                                    'child',
                                    employee.id
                                ]);
                            }
                        }
                    } catch (e) {
                        console.error(`Error parsing children for employee ${employee.id}:`, e);
                    }
                }
                
                if (employee.dependents) {
                    try {
                        const dependents = JSON.parse(employee.dependents);
                        if (Array.isArray(dependents)) {
                            for (const dependent of dependents) {
                                await queryRunner.query(`
                                    INSERT INTO employee_family (
                                        name, dateOfBirth, relationship, cnic,
                                        type, employeeId
                                    ) VALUES (?, ?, ?, ?, ?, ?)
                                `, [
                                    dependent.name || '',
                                    dependent.dateOfBirth || null,
                                    dependent.relationship || '',
                                    dependent.cnic || null,
                                    'dependent',
                                    employee.id
                                ]);
                            }
                        }
                    } catch (e) {
                        console.error(`Error parsing dependents for employee ${employee.id}:`, e);
                    }
                }
                
                // Add spouse information as a family member if it exists
                if (employee.spouseName) {
                    await queryRunner.query(`
                        INSERT INTO employee_family (
                            name, dateOfBirth, relationship, cnic,
                            occupation, employer, contactNumber, type, employeeId
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    `, [
                        employee.spouseName,
                        employee.spouseDateOfBirth || null,
                        'Spouse',
                        employee.spouseCNIC || null,
                        employee.spouseOccupation || null,
                        employee.spouseEmployer || null,
                        employee.spouseContactNumber || null,
                        'spouse',
                        employee.id
                    ]);
                }
                
                // Documents
                if (employee.documents) {
                    try {
                        const documents = JSON.parse(employee.documents);
                        if (Array.isArray(documents)) {
                            for (const doc of documents) {
                                await queryRunner.query(`
                                    INSERT INTO employee_documents (
                                        documentType, documentNumber, issueDate, expiryDate,
                                        verificationStatus, filePath, employeeId
                                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                                `, [
                                    doc.documentType || '',
                                    doc.documentNumber || null,
                                    doc.issueDate || null,
                                    doc.expiryDate || null,
                                    doc.verificationStatus || 'pending',
                                    (doc.files && doc.files.length > 0) ? doc.files[0].preview : null,
                                    employee.id
                                ]);
                            }
                        }
                    } catch (e) {
                        console.error(`Error parsing documents for employee ${employee.id}:`, e);
                    }
                }
                
                // Devices
                if (employee.deviceEntries) {
                    try {
                        const devices = JSON.parse(employee.deviceEntries);
                        if (Array.isArray(devices)) {
                            for (const device of devices) {
                                await queryRunner.query(`
                                    INSERT INTO employee_devices (
                                        deviceName, makeModel, serialNumber, handoverDate,
                                        returnDate, condition, employeeId
                                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                                `, [
                                    device.deviceName || '',
                                    device.makeModel || null,
                                    device.serialNumber || null,
                                    device.handoverDate || null,
                                    device.returnDate || null,
                                    device.condition || null,
                                    employee.id
                                ]);
                            }
                        }
                    } catch (e) {
                        console.error(`Error parsing deviceEntries for employee ${employee.id}:`, e);
                    }
                }
                
                // Vehicle as a device if vehicle info exists
                if (employee.vehicleType) {
                    await queryRunner.query(`
                        INSERT INTO employee_devices (
                            deviceName, isVehicle, vehicleType, registrationNumber,
                            providedByCompany, handoverDate, returnDate, 
                            vehicleMakeModel, vehicleColor, mileageAtIssuance, employeeId
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    `, [
                        'Vehicle',
                        1,
                        employee.vehicleType,
                        employee.registrationNumber || null,
                        employee.providedByCompany || 0,
                        employee.handingOverDate || null,
                        employee.returnDate || null,
                        employee.vehicleMakeModel || null,
                        employee.vehicleColor || null,
                        employee.mileageAtIssuance || null,
                        employee.id
                    ]);
                }
            }
            
            console.log("Successfully migrated employee data to normalized tables");
            
        } catch (error) {
            console.error("Error in MigrateEmployeeData migration:", error);
            throw error;
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // We won't attempt to reverse the data migration as it's too complex
        // and could potentially cause data loss. If needed, a backup of the
        // original employees table should be created before running the migration.
        console.log("Data migration cannot be automatically reverted");
    }
} 