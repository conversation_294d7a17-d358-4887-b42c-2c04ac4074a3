To create an Excel file for attendance import, please create a new Excel file with the following columns:

Column Headers (Row 1):
A: employeeId
B: employeeName
C: date
D: checkInTime
E: checkOutTime
F: status
G: workHours
H: isRemote
I: location
J: department
K: notes

Sample Data (starting from Row 2):
Row 2: 1001, <PERSON>, 2023-05-15, 09:00, 17:30, present, 8.5, <PERSON><PERSON><PERSON>, Office, Engineering, Regular day
Row 3: 1002, <PERSON>, 2023-05-15, 09:15, 17:45, present, 8.5, TRUE, Remote, Design, Working from home
Row 4: 1003, <PERSON>, 2023-05-15, 08:45, 16:30, present, 7.75, FALSE, Office, Marketing, Left early for doctor appointment
Row 5: 1004, <PERSON>, 2023-05-15, 10:15, 18:00, late, 7.75, <PERSON><PERSON><PERSON>, <PERSON>, Finance, Late due to traffic
Row 6: 1005, <PERSON>, 2023-05-15, 09:30, 18:30, present, 9, FALS<PERSON>, Office, HR, Overtime work
Row 7: 1006, <PERSON>, 2023-05-15, 09:00, 17:00, present, 8, TRUE, <PERSON>mote, Engineering, Regular day
Row 8: 1007, <PERSON>, 2023-05-15, , , absent, 0, <PERSON><PERSON><PERSON>, , <PERSON>, Sick leave
Row 9: 1008, <PERSON>, 2023-05-15, 09:00, 13:00, half_day, 4, FALSE, Office, Customer Support, Half day
Row 10: 1009, Thomas Taylor, 2023-05-15, 08:30, 17:30, present, 9, FALSE, Office, IT, Early arrival
Row 11: 1010, Jessica Moore, 2023-05-15, 09:05, 17:45, present, 8.67, TRUE, Remote, Analytics, Regular day

Format the date column as Date (YYYY-MM-DD) and save as .xlsx file.

Note: You need to use "present", "absent", "late", "half_day", etc. for the status values to match the expected enumeration values. 