import { <PERSON><PERSON><PERSON>, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, PrimaryColumn, BeforeInsert } from 'typeorm';
import { Asset } from './Asset';
import { User } from './User';
import { v4 as uuidv4 } from 'uuid';

@Entity('asset_maintenance')
export class AssetMaintenance {
  @PrimaryColumn({ type: 'varchar', length: 36 })
  id: string;

  @BeforeInsert()
  generateId() {
    if (!this.id) {
      this.id = uuidv4();
    }
  }

  @ManyToOne(() => Asset, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'assetId' })
  asset: Asset;

  @Column({ type: 'int' })
  assetId: number;

  @Column({ type: 'varchar', length: 50 })
  maintenanceType: string; // Preventive, Corrective, Predictive, Condition-based

  @Column({ type: 'date' })
  maintenanceDate: Date;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  cost: number;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'performedById' })
  performedBy: User;

  @Column({ type: 'varchar', length: 36, nullable: true })
  performedById: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  vendor: string;

  @Column({ type: 'varchar', length: 50, default: 'Completed' })
  status: string; // Scheduled, In Progress, Completed, Cancelled

  @Column({ type: 'date', nullable: true })
  nextMaintenanceDate: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'json', nullable: true })
  partsReplaced: Record<string, any>[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 