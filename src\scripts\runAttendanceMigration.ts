import { AppDataSource } from '../config/database';
import { CreateAttendanceTable1746001000000 } from '../migrations/1746001000000-CreateAttendanceTable';

async function runAttendanceMigration() {
  console.log('Starting attendance table migration...');
  
  try {
    // Initialize the data source
    if (!AppDataSource.isInitialized) {
      console.log('Initializing database connection...');
      await AppDataSource.initialize();
      console.log('Database connection initialized');
    }
    
    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    
    // Start transaction
    await queryRunner.startTransaction();
    
    try {
      console.log('Running migration to create attendance table...');
      const migration = new CreateAttendanceTable1746001000000();
      await migration.up(queryRunner);
      
      // Commit transaction
      await queryRunner.commitTransaction();
      console.log('Migration completed successfully');
    } catch (error) {
      // Rollback transaction on error
      await queryRunner.rollbackTransaction();
      console.error('Migration failed:', error);
      throw error;
    } finally {
      // Release query runner
      await queryRunner.release();
    }
  } catch (error) {
    console.error('Migration script failed:', error);
    process.exit(1);
  } finally {
    // Close database connection
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('Database connection closed');
    }
  }
}

// Run the migration
runAttendanceMigration().then(() => {
  console.log('Migration script completed');
  process.exit(0);
}).catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
}); 