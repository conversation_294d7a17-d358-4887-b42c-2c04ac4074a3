import { Router } from 'express';
import { Request, Response } from 'express';
import { leaveAllocationService } from '../services/LeaveAllocationService';
import { LeaveAllocationController } from '../controllers/leaveAllocationController';
import { requireAuth } from '../middleware/auth';

const router = Router();
const leaveAllocationController = new LeaveAllocationController();

// Apply authentication middleware to all routes
router.use(requireAuth);

/**
 * Calculate leave allocation for a specific employee and leave type
 * GET /api/leave-allocation/calculate/:employeeId/:leaveType/:year
 */
router.get('/calculate/:employeeId/:leaveType/:year', async (req: Request, res: Response) => {
  try {
    const { employeeId, leaveType, year } = req.params;
    const effectiveDate = req.query.effectiveDate ? new Date(req.query.effectiveDate as string) : undefined;

    const allocation = await leaveAllocationService.calculateEmployeeAllocation(
      parseInt(employeeId),
      leaveType,
      parseInt(year),
      effectiveDate
    );

    res.json({
      success: true,
      data: allocation,
      message: 'Leave allocation calculated successfully'
    });
  } catch (error: any) {
    console.error('Error calculating leave allocation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to calculate leave allocation',
      error: error.message
    });
  }
});

/**
 * Calculate leave allocation with carry forward
 * GET /api/leave-allocation/calculate-with-carryforward/:employeeId/:leaveType/:year
 */
router.get('/calculate-with-carryforward/:employeeId/:leaveType/:year', async (req: Request, res: Response) => {
  try {
    const { employeeId, leaveType, year } = req.params;

    const allocationWithCarryForward = await leaveAllocationService.calculateAllocationWithCarryForward(
      parseInt(employeeId),
      leaveType,
      parseInt(year)
    );

    res.json({
      success: true,
      data: allocationWithCarryForward,
      message: 'Leave allocation with carry forward calculated successfully'
    });
  } catch (error: any) {
    console.error('Error calculating leave allocation with carry forward:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to calculate leave allocation with carry forward',
      error: error.message
    });
  }
});

/**
 * Get allocation source breakdown for analysis
 * GET /api/leave-allocation/analysis/:year?employeeIds=1,2,3&leaveTypes=ANNUAL,SICK
 */
router.get('/analysis/:year', async (req: Request, res: Response) => {
  try {
    const { year } = req.params;
    const employeeIds = req.query.employeeIds ? 
      (req.query.employeeIds as string).split(',').map(id => parseInt(id.trim())) : 
      [];
    const leaveTypes = req.query.leaveTypes ? 
      (req.query.leaveTypes as string).split(',').map(type => type.trim()) : 
      ['ANNUAL', 'SICK', 'CASUAL'];

    if (employeeIds.length === 0) {
      res.status(400).json({
        success: false,
        message: 'At least one employee ID must be provided via employeeIds query parameter'
      });
      return;
    }

    const allocations = await leaveAllocationService.bulkCalculateAllocations(
      employeeIds,
      leaveTypes,
      parseInt(year)
    );

    // Analyze the allocation sources and patterns
    const analysis = {
      totalAllocations: allocations.length,
      uniqueEmployees: new Set(allocations.map(a => a.employeeId)).size,
      leaveTypesAnalyzed: leaveTypes,
      year: parseInt(year),
      sourceBreakdown: {
        accrualRule: {
          count: allocations.filter(a => a.source === 'accrual_rule').length,
          totalDays: allocations.filter(a => a.source === 'accrual_rule').reduce((sum, a) => sum + a.totalDays, 0),
          averageDays: 0
        },
        leaveTypePolicy: {
          count: allocations.filter(a => a.source === 'leave_type_policy').length,
          totalDays: allocations.filter(a => a.source === 'leave_type_policy').reduce((sum, a) => sum + a.totalDays, 0),
          averageDays: 0
        },
        default: {
          count: allocations.filter(a => a.source === 'default').length,
          totalDays: allocations.filter(a => a.source === 'default').reduce((sum, a) => sum + a.totalDays, 0),
          averageDays: 0
        }
      },
      leaveTypeBreakdown: {} as Record<string, { count: number; totalDays: number; averageDays: number; sources: Record<string, number> }>
    };

    // Calculate averages
    Object.keys(analysis.sourceBreakdown).forEach(source => {
      const sourceData = analysis.sourceBreakdown[source as keyof typeof analysis.sourceBreakdown];
      sourceData.averageDays = sourceData.count > 0 ? Math.round(sourceData.totalDays / sourceData.count * 100) / 100 : 0;
    });

    // Analyze by leave type
    leaveTypes.forEach(leaveType => {
      const typeAllocations = allocations.filter(a => a.leaveType === leaveType);
      analysis.leaveTypeBreakdown[leaveType] = {
        count: typeAllocations.length,
        totalDays: typeAllocations.reduce((sum, a) => sum + a.totalDays, 0),
        averageDays: typeAllocations.length > 0 ? Math.round(typeAllocations.reduce((sum, a) => sum + a.totalDays, 0) / typeAllocations.length * 100) / 100 : 0,
        sources: {
          accrual_rule: typeAllocations.filter(a => a.source === 'accrual_rule').length,
          leave_type_policy: typeAllocations.filter(a => a.source === 'leave_type_policy').length,
          default: typeAllocations.filter(a => a.source === 'default').length
        }
      };
    });

    res.json({
      success: true,
      data: {
        analysis,
        allocations: allocations.map(a => ({
          employeeId: a.employeeId,
          leaveType: a.leaveType,
          totalDays: a.totalDays,
          source: a.source,
          calculation: a.calculation
        }))
      },
      message: 'Leave allocation analysis completed successfully'
    });
  } catch (error: any) {
    console.error('Error analyzing leave allocations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to analyze leave allocations',
      error: error.message
    });
  }
});

/**
 * Diagnose allocation issues for troubleshooting
 * GET /api/leave-allocation/diagnose/:employeeId/:leaveType/:year
 */
router.get('/diagnose/:employeeId/:leaveType/:year', async (req: Request, res: Response) => {
  try {
    const { employeeId, leaveType, year } = req.params;

    if (!employeeId || !leaveType || !year) {
      res.status(400).json({
        success: false,
        message: 'Employee ID, leave type, and year are required'
      });
      return;
    }

    const diagnosis = await leaveAllocationService.diagnoseAllocation(
      parseInt(employeeId),
      leaveType,
      parseInt(year)
    );

    res.json({
      success: true,
      data: diagnosis,
      message: 'Allocation diagnosis completed'
    });
  } catch (error: any) {
    console.error('Error diagnosing leave allocation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to diagnose leave allocation',
      error: error.message
    });
  }
});

/**
 * Bulk calculate leave allocations for multiple employees and leave types
 * POST /api/leave-allocation/bulk-calculate
 */
router.post('/bulk-calculate', async (req: Request, res: Response) => {
  try {
    const { employeeIds, leaveTypes, year } = req.body;

    if (!employeeIds || !Array.isArray(employeeIds) || employeeIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Employee IDs are required and must be a non-empty array'
      });
    }

    if (!leaveTypes || !Array.isArray(leaveTypes) || leaveTypes.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Leave types are required and must be a non-empty array'
      });
    }

    const targetYear = year || new Date().getFullYear();

    console.log(`📊 Bulk calculating allocations for ${employeeIds.length} employees and ${leaveTypes.length} leave types for year ${targetYear}`);

    const allocations = await leaveAllocationService.bulkCalculateAllocations(
      employeeIds,
      leaveTypes,
      targetYear
    );

    res.json({
      success: true,
      data: {
        allocations,
        summary: {
          totalEmployees: employeeIds.length,
          totalLeaveTypes: leaveTypes.length,
          totalAllocations: allocations.length,
          year: targetYear
        }
      },
      message: `Successfully calculated ${allocations.length} leave allocations`
    });

  } catch (error: any) {
    console.error('Error in bulk calculate allocations:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to calculate bulk leave allocations',
      error: error.message
    });
  }
});

/**
 * Bulk save allocation adjustments
 * POST /api/leave-allocation/bulk-save
 */
router.post('/bulk-save', leaveAllocationController.bulkSaveAllocationAdjustments.bind(leaveAllocationController));

/**
 * Auto-allocate leave from policies to all employees
 * POST /api/leave-allocation/auto-allocate-from-policies
 */
router.post('/auto-allocate-from-policies', leaveAllocationController.autoAllocateFromPolicies.bind(leaveAllocationController));

export default router; 