export type LicenseType = 'One-Time (Lifetime)' | 'Monthly Subscription' | 'Yearly Subscription' | 'Trial' | 'Freemium' | 'Open Source' | 'Free';
export type PaymentFrequency = 'Monthly' | 'Quarterly' | 'Yearly' | 'One-time';
export type SoftwareStatus = 'Active' | 'Inactive' | 'Expired' | 'Pending' | 'Suspended';

// Interface for creating a new software license
export interface CreateSoftwareLicenseDto {
  softwareName: string;
  vendorId: string;
  category: string;
  type: LicenseType;
  department: string;
  status: SoftwareStatus;
  licenseKey?: string;
  totalSeats: number;
  usedSeats?: number;
  purchaseDate: Date;
  expiryDate?: Date | null;
  paymentFrequency: PaymentFrequency;
  costPKR: number;
  costUSD: number;
  paidBy: string;
  invoiceFile?: File;
  autoRenew?: boolean;
  notes?: string;
  currencyRate?: number;
  renewalReminder?: boolean;
  multiLocation?: boolean;
  loginInfo?: string;
  socialMediaLinks?: string[];
  assignedUserIds?: string[];
  linkedAssetIds?: string[];
}

export interface UpdateSoftwareLicenseDto extends Partial<CreateSoftwareLicenseDto> {
  id: string;
}

// Interface for the software license entity
export interface SoftwareLicense {
  id: string;
  name: string;
  vendor: {
    id: string;
    vendorName: string;
    companyName: string;
  };
  category: string;
  type: LicenseType;
  department: string;
  status: SoftwareStatus;
  licenseKey: string;
  totalSeats: number;
  usedSeats: number;
  purchaseDate: Date;
  expiryDate?: Date;
  paymentFrequency: PaymentFrequency;
  costPKR: number;
  costUSD: number;
  paidBy: string;
  invoiceUrl?: string;
  autoRenew: boolean;
  notes?: string;
  currencyConverter: boolean;
  renewalReminder: boolean;
  multiLocationUse: boolean;
  loginSharingInfo?: string;
  socialMediaLinks?: string[];
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
  updatedBy?: string;
} 