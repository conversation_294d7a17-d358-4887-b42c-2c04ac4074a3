import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { Employee } from './Employee';

@Entity('employee_skills')
export class EmployeeSkill {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'text', nullable: true })
  professionalSkills: string;

  @Column({ type: 'text', nullable: true })
  technicalSkills: string;

  @Column({ type: 'text', nullable: true })
  certifications: string;

  @Column({ type: 'text', nullable: true })
  languages: string;

  // Relation to Employee
  @ManyToOne(() => Employee, employee => employee.skills, { onDelete: 'CASCADE' })
  @JoinColumn()
  employee: Employee;

  // System columns
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 