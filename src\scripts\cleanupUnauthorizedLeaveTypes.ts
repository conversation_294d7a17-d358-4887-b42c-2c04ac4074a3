import { AppDataSource } from '../config/database';

async function cleanupUnauthorizedLeaveTypes() {
  try {
    console.log('🔍 Initializing database connection...');
    await AppDataSource.initialize();
    
    const queryRunner = AppDataSource.createQueryRunner();
    
    await queryRunner.startTransaction();
    
    try {
      console.log('🧹 Cleaning up unauthorized leave types...\n');
      
      // Step 1: Get all leave types defined in active policy configuration
      console.log('📋 Step 1: Getting authorized leave types from policy configuration...');
      const authorizedLeaveTypes = await queryRunner.query(`
        SELECT DISTINCT ltp.leaveType, ltp.displayName, ltp.isActive, ltp.enabled
        FROM leave_type_policies ltp
        JOIN leave_policy_configurations lpc ON ltp.policyConfigurationId = lpc.id
        WHERE lpc.isActive = 1 AND ltp.isActive = 1 AND ltp.enabled = 1
        ORDER BY ltp.leaveType
      `);
      
      console.log(`✅ Found ${authorizedLeaveTypes.length} authorized leave types:`);
      authorizedLeaveTypes.forEach((lt: any) => {
        console.log(`   • ${lt.leaveType} (${lt.displayName})`);
      });
      
      if (authorizedLeaveTypes.length === 0) {
        console.log('❌ No authorized leave types found in policy configuration!');
        console.log('💡 Please configure leave types in the Leave Policy Configuration tab first.');
        return;
      }
      
      const authorizedTypesList = authorizedLeaveTypes.map((lt: any) => lt.leaveType);
      
      // Step 2: Find unauthorized leave types in allocations
      console.log('\n📋 Step 2: Finding unauthorized leave types in allocations...');
      const unauthorizedAllocations = await queryRunner.query(`
        SELECT DISTINCT leaveType, COUNT(*) as count
        FROM leave_allocations
        WHERE leaveType NOT IN (${authorizedTypesList.map(() => '?').join(',')})
        GROUP BY leaveType
        ORDER BY leaveType
      `, authorizedTypesList);
      
      console.log(`⚠️  Found ${unauthorizedAllocations.length} unauthorized leave types in allocations:`);
      unauthorizedAllocations.forEach((ua: any) => {
        console.log(`   • ${ua.leaveType} - ${ua.count} records`);
      });
      
      // Step 3: Find unauthorized leave types in balances
      console.log('\n📋 Step 3: Finding unauthorized leave types in balances...');
      const unauthorizedBalances = await queryRunner.query(`
        SELECT DISTINCT leaveType, COUNT(*) as count
        FROM leave_balances
        WHERE leaveType NOT IN (${authorizedTypesList.map(() => '?').join(',')})
        GROUP BY leaveType
        ORDER BY leaveType
      `, authorizedTypesList);
      
      console.log(`⚠️  Found ${unauthorizedBalances.length} unauthorized leave types in balances:`);
      unauthorizedBalances.forEach((ub: any) => {
        console.log(`   • ${ub.leaveType} - ${ub.count} records`);
      });
      
      // Step 4: Delete unauthorized allocation records
      console.log('\n🗑️  Step 4: Deleting unauthorized allocation records...');
      for (const ua of unauthorizedAllocations) {
        console.log(`   🗑️  Deleting ${ua.count} allocation records for ${ua.leaveType}...`);
        await queryRunner.query(`
          DELETE FROM leave_allocations 
          WHERE leaveType = ?
        `, [ua.leaveType]);
        console.log(`   ✅ Deleted ${ua.leaveType} allocation records`);
      }
      
      // Step 5: Delete unauthorized balance records
      console.log('\n🗑️  Step 5: Deleting unauthorized balance records...');
      for (const ub of unauthorizedBalances) {
        console.log(`   🗑️  Deleting ${ub.count} balance records for ${ub.leaveType}...`);
        await queryRunner.query(`
          DELETE FROM leave_balances 
          WHERE leaveType = ?
        `, [ub.leaveType]);
        console.log(`   ✅ Deleted ${ub.leaveType} balance records`);
      }
      
      // Step 6: Delete unauthorized leave type policies
      console.log('\n🗑️  Step 6: Deleting unauthorized leave type policies...');
      const unauthorizedPolicies = await queryRunner.query(`
        SELECT leaveType, displayName
        FROM leave_type_policies
        WHERE leaveType NOT IN (${authorizedTypesList.map(() => '?').join(',')})
      `, authorizedTypesList);
      
      console.log(`⚠️  Found ${unauthorizedPolicies.length} unauthorized leave type policies:`);
      unauthorizedPolicies.forEach((up: any) => {
        console.log(`   • ${up.leaveType} (${up.displayName})`);
      });
      
      if (unauthorizedPolicies.length > 0) {
        await queryRunner.query(`
          DELETE FROM leave_type_policies
          WHERE leaveType NOT IN (${authorizedTypesList.map(() => '?').join(',')})
        `, authorizedTypesList);
        console.log(`   ✅ Deleted ${unauthorizedPolicies.length} unauthorized leave type policies`);
      }
      
      await queryRunner.commitTransaction();
      
      console.log('\n🎉 CLEANUP COMPLETE!');
      console.log('📋 Summary:');
      console.log(`   ✅ Kept ${authorizedLeaveTypes.length} authorized leave types`);
      console.log(`   🗑️  Deleted ${unauthorizedAllocations.length} unauthorized leave types from allocations`);
      console.log(`   🗑️  Deleted ${unauthorizedBalances.length} unauthorized leave types from balances`);
      console.log(`   🗑️  Deleted ${unauthorizedPolicies.length} unauthorized leave type policies`);
      
      console.log('\n🔒 FINAL STATE:');
      console.log('   Your database now contains ONLY the leave types defined in your policy configuration');
      authorizedLeaveTypes.forEach((lt: any) => {
        console.log(`   ✅ ${lt.leaveType} (${lt.displayName})`);
      });
      
      console.log('\n📝 NEXT STEPS:');
      console.log('   1. Refresh your Leave Management page');
      console.log('   2. You should now see only the authorized leave types');
      console.log('   3. All unauthorized data has been permanently removed');
      
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
    
  } catch (error) {
    console.error('❌ Error cleaning up unauthorized leave types:', error);
    throw error;
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('\n📪 Database connection closed');
    }
  }
}

// Run the cleanup
if (require.main === module) {
  cleanupUnauthorizedLeaveTypes()
    .then(() => {
      console.log('✅ Unauthorized leave types cleanup completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Unauthorized leave types cleanup failed:', error);
      process.exit(1);
    });
}

export { cleanupUnauthorizedLeaveTypes }; 