import express from 'express';
import {
  createEmployee,
  getAllEmployees,
  getEmployeesForListing,
  getEmployeeById,
  updateEmployee,
  deleteEmployee,
  uploadEmployeeDocuments,
  getEmployeeDocuments,
  downloadEmployeeDocument,
  deleteEmployeeDocument,
  repairEmployeeData,
  importEmployees,
  getEmployeeLocations,
  getEmployeeLocationsByEmployee
} from '../controllers/employeeController';

const router = express.Router();

// Debug route
router.get('/debug', (req, res) => {
  console.log('Employee Debug route hit');
  return res.json({
    success: true,
    message: 'Employee API is working',
    timestamp: new Date().toISOString()
  });
});

// Add OPTIONS handler for the listing route
router.options('/listing', (req, res) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.sendStatus(200);
});

// Create a new employee
router.post('/', createEmployee);

// Import employees in bulk
router.post('/import', importEmployees);

// Test route for checking import functionality
router.post('/import-debug', (req, res) => {
  console.log('Import-debug route hit');
  console.log('Body size:', JSON.stringify(req.body).length, 'bytes');
  return res.json({
    success: true,
    message: 'Import debug route working',
    bodySize: JSON.stringify(req.body).length,
    entriesReceived: Array.isArray(req.body.employees) ? req.body.employees.length : 0
  });
});

// Get all employees for listing (with minimal data)
router.get('/listing', getEmployeesForListing);

// Get all employees (with full data)
router.get('/', getAllEmployees);

// Get employee by ID
router.get('/:id', getEmployeeById);

// Update employee
router.put('/:id', updateEmployee);

// Delete employee
router.delete('/:id', deleteEmployee);

// Upload employee documents
router.post('/:id/documents', uploadEmployeeDocuments);

// Get employee documents
router.get('/:id/documents', getEmployeeDocuments);

// Download a specific employee document
router.get('/:id/documents/:documentId', downloadEmployeeDocument);

// Delete a specific employee document
router.delete('/:id/documents/:documentId', deleteEmployeeDocument);

// Secure document access endpoint
router.post('/documents/secure-access', (req, res) => {
  try {
    const { filePath } = req.body;
    
    if (!filePath) {
      return res.status(400).json({
        success: false,
        message: 'File path is required'
      });
    }
    
    // For security, don't allow paths with ".." to prevent directory traversal
    if (filePath.includes('..')) {
      return res.status(400).json({
        success: false,
        message: 'Invalid file path'
      });
    }
    
    // Handle relative paths
    let secureUrl;
    if (filePath.startsWith('/')) {
      // If it's already a server path, use it directly
      secureUrl = `${req.protocol}://${req.get('host')}${filePath}`;
    } else {
      // Otherwise, assume it's a relative path within uploads
      secureUrl = `${req.protocol}://${req.get('host')}/uploads/${filePath}`;
    }
    
    console.log(`Generated secure URL for document: ${secureUrl}`);
    
    return res.json({
      success: true,
      secureUrl,
      expiresIn: 3600 // Token valid for 1 hour
    });
  } catch (error) {
    console.error('Error generating secure document URL:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to generate secure access URL'
    });
  }
});

// Repair employee data - creates missing related entities
router.post('/repair-data', repairEmployeeData);

// Location-related routes
router.get('/locations', getEmployeeLocations);
router.get('/:id/locations', getEmployeeLocationsByEmployee);

export default router; 