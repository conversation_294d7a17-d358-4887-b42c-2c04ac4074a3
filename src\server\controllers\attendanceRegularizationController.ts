import { Request, Response } from 'express';
import { AppDataSource } from '../../config/database';
import { Regularization } from '../entities/Regularization';
import { Employee } from '../entities/Employee';
import { Attendance, AttendanceStatus, ApprovalStatus } from '../entities/Attendance';
import { logger } from '../utils/logger';
import { validate } from 'class-validator';

/**
 * Get all regularization requests with filtering
 */
export const getRegularizationRequests = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { 
      employeeId, 
      status, 
      startDate, 
      endDate, 
      limit = '100', 
      offset = '0' 
    } = req.query;

    console.log('📋 Fetching regularization requests with filters:', req.query);

    const repository = AppDataSource.getRepository(Regularization);
    
    let query = repository.createQueryBuilder('regularization')
      .leftJoinAndSelect('regularization.employee', 'employee')
      .orderBy('regularization.createdAt', 'DESC');

    // Apply filters
    if (employeeId) {
      query = query.andWhere('regularization.employeeId = :employeeId', { 
        employeeId: employeeId.toString()
      });
    }

    if (status && status !== 'all') {
      query = query.andWhere('regularization.status = :status', { status });
    }

    if (startDate) {
      query = query.andWhere('regularization.date >= :startDate', { startDate });
    }

    if (endDate) {
      query = query.andWhere('regularization.date <= :endDate', { endDate });
    }

    // Apply pagination
    const limitNum = parseInt(limit as string);
    const offsetNum = parseInt(offset as string);
    
    query = query.skip(offsetNum).take(limitNum);

    const [requests, total] = await query.getManyAndCount();

    console.log(`✅ Found ${requests.length} regularization requests (total: ${total})`);

    return res.status(200).json({
      success: true,
      data: requests,
      pagination: {
        total,
        limit: limitNum,
        offset: offsetNum,
        hasMore: offsetNum + limitNum < total
      }
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('❌ Error fetching regularization requests:', error);
    logger.error('Error fetching regularization requests', { error: errorMessage });
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch regularization requests',
      error: process.env.NODE_ENV === 'development' ? errorMessage : undefined
    });
  }
};

/**
 * Get regularization requests by employee ID
 */
export const getEmployeeRegularizationRequests = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { employeeId } = req.params;
    const { status } = req.query;

    console.log(`📋 Fetching regularization requests for employee ${employeeId}`);

    const repository = AppDataSource.getRepository(Regularization);
    
    let query = repository.createQueryBuilder('regularization')
      .leftJoinAndSelect('regularization.employee', 'employee')
      .where('regularization.employeeId = :employeeId', { employeeId })
      .orderBy('regularization.createdAt', 'DESC');

    if (status && status !== 'all') {
      query = query.andWhere('regularization.status = :status', { status });
    }

    const requests = await query.getMany();

    console.log(`✅ Found ${requests.length} regularization requests for employee ${employeeId}`);

    return res.status(200).json({
      success: true,
      data: requests
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('❌ Error fetching employee regularization requests:', error);
    logger.error('Error fetching employee regularization requests', { error: errorMessage });
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch employee regularization requests',
      error: process.env.NODE_ENV === 'development' ? errorMessage : undefined
    });
  }
};

/**
 * Get a specific regularization request by ID
 */
export const getRegularizationRequestById = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;

    console.log(`📋 Fetching regularization request ${id}`);

    const repository = AppDataSource.getRepository(Regularization);
    const request = await repository.findOne({
      where: { id: parseInt(id) },
      relations: ['employee']
    });

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Regularization request not found'
      });
    }

    console.log(`✅ Found regularization request ${id}`);

    return res.status(200).json({
      success: true,
      data: request
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('❌ Error fetching regularization request:', error);
    logger.error('Error fetching regularization request', { error: errorMessage });
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch regularization request',
      error: process.env.NODE_ENV === 'development' ? errorMessage : undefined
    });
  }
};

/**
 * Create a new regularization request
 */
export const createRegularizationRequest = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { 
      employeeId, 
      employeeName, 
      date, 
      type, 
      requestedTime, 
      reason 
    } = req.body;

    console.log('📝 Creating new regularization request:', { employeeId, date, type });

    // Validate required fields
    if (!employeeId || !employeeName || !date || !type || !reason) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: employeeId, employeeName, date, type, reason'
      });
    }

    // Validate employee exists - convert string ID to number for Employee lookup
    const employee = await AppDataSource.getRepository(Employee).findOne({
      where: { id: parseInt(employeeId.toString()) }
    });

    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'Employee not found'
      });
    }

    // Create regularization request
    const repository = AppDataSource.getRepository(Regularization);
    const regularizationRequest = repository.create({
      employeeId: employeeId.toString(),
      employeeName,
      date: new Date(date),
      type,
      requestedTime: requestedTime || null,
      reason: reason.trim(),
      status: 'pending'
    });

    // Validate the entity
    const errors = await validate(regularizationRequest);
    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: errors.map(error => ({
          property: error.property,
          constraints: error.constraints
        }))
      });
    }

    // Save to database
    const savedRequest = await repository.save(regularizationRequest);

    console.log(`✅ Created regularization request ${savedRequest.id}`);

    return res.status(201).json({
      success: true,
      message: 'Regularization request created successfully',
      data: savedRequest
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('❌ Error creating regularization request:', error);
    logger.error('Error creating regularization request', { error: errorMessage });
    return res.status(500).json({
      success: false,
      message: 'Failed to create regularization request',
      error: process.env.NODE_ENV === 'development' ? errorMessage : undefined
    });
  }
};

/**
 * Approve a regularization request
 */
export const approveRegularizationRequest = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const { approverComments, approverId, approverName } = req.body;

    console.log(`✅ Approving regularization request ${id}`);

    const repository = AppDataSource.getRepository(Regularization);
    const request = await repository.findOne({
      where: { id: parseInt(id) },
      relations: ['employee']
    });

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Regularization request not found'
      });
    }

    if (request.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Request is not in pending status'
      });
    }

    // Update request status
    request.status = 'approved';
    request.approverComments = approverComments || null;
    request.updatedAt = new Date();

    const updatedRequest = await repository.save(request);

    // Update corresponding attendance record if it exists
    const attendanceRepository = AppDataSource.getRepository(Attendance);
    // Ensure request.date is a Date object for toISOString
    const dateString = (request.date instanceof Date)
      ? request.date.toISOString().split('T')[0]
      : new Date(request.date).toISOString().split('T')[0];
    const attendance = await attendanceRepository.findOne({
      where: {
        employeeId: parseInt(request.employeeId),
        date: dateString
      }
    });

    if (attendance) {
      // Apply regularization based on type
      switch (request.type.toLowerCase()) {
        case 'check-in':
          if (request.requestedTime) {
            attendance.checkInTime = request.requestedTime;
          }
          break;
        case 'check-out':
          if (request.requestedTime) {
            attendance.checkOutTime = request.requestedTime;
          }
          break;
        case 'absent':
          attendance.status = AttendanceStatus.PRESENT;
          break;
        case 'work-from-home':
          attendance.isRemote = true;
          break;
        case 'half-day':
          attendance.status = AttendanceStatus.HALF_DAY;
          break;
      }

      attendance.isRegularized = true;
      attendance.regularizedBy = approverId || null;
      attendance.regularizationReason = request.reason;
      attendance.regularizationDate = new Date().toISOString().split('T')[0];
      attendance.approvalStatus = ApprovalStatus.APPROVED;

      // Recalculate work hours if check-in/out times changed
      if (attendance.checkInTime && attendance.checkOutTime) {
        const checkIn = new Date(`1970-01-01T${attendance.checkInTime}`);
        const checkOut = new Date(`1970-01-01T${attendance.checkOutTime}`);
        let diffHours = (checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60);
        
        if (diffHours < 0) {
          diffHours += 24;
        }
        
        attendance.workHours = parseFloat(diffHours.toFixed(2));
      }

      await attendanceRepository.save(attendance);
    }

    console.log(`✅ Approved regularization request ${id}`);

    return res.status(200).json({
      success: true,
      message: 'Regularization request approved successfully',
      data: updatedRequest
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('❌ Error approving regularization request:', error);
    logger.error('Error approving regularization request', { error: errorMessage });
    return res.status(500).json({
      success: false,
      message: 'Failed to approve regularization request',
      error: process.env.NODE_ENV === 'development' ? errorMessage : undefined
    });
  }
};

/**
 * Reject a regularization request
 */
export const rejectRegularizationRequest = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const { approverComments, approverId, approverName } = req.body;

    console.log(`❌ Rejecting regularization request ${id}`);

    if (!approverComments || !approverComments.trim()) {
      return res.status(400).json({
        success: false,
        message: 'Approver comments are required for rejection'
      });
    }

    const repository = AppDataSource.getRepository(Regularization);
    const request = await repository.findOne({
      where: { id: parseInt(id) },
      relations: ['employee']
    });

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Regularization request not found'
      });
    }

    if (request.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Request is not in pending status'
      });
    }

    // Update request status
    request.status = 'rejected';
    request.approverComments = approverComments.trim();
    request.updatedAt = new Date();

    const updatedRequest = await repository.save(request);

    console.log(`✅ Rejected regularization request ${id}`);

    return res.status(200).json({
      success: true,
      message: 'Regularization request rejected successfully',
      data: updatedRequest
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('❌ Error rejecting regularization request:', error);
    logger.error('Error rejecting regularization request', { error: errorMessage });
    return res.status(500).json({
      success: false,
      message: 'Failed to reject regularization request',
      error: process.env.NODE_ENV === 'development' ? errorMessage : undefined
    });
  }
};

/**
 * Update a regularization request
 */
export const updateRegularizationRequest = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const updates = req.body;

    console.log(`📝 Updating regularization request ${id}`);

    const repository = AppDataSource.getRepository(Regularization);
    const request = await repository.findOne({
      where: { id: parseInt(id) }
    });

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Regularization request not found'
      });
    }

    // Only allow updates if request is pending
    if (request.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Cannot update request that is not in pending status'
      });
    }

    // Allow updating specific fields
    const allowedFields = ['date', 'type', 'requestedTime', 'reason'];
    for (const field of allowedFields) {
      if (field in updates) {
        (request as any)[field] = updates[field];
      }
    }

    request.updatedAt = new Date();

    // Validate the updated entity
    const errors = await validate(request);
    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: errors.map(error => ({
          property: error.property,
          constraints: error.constraints
        }))
      });
    }

    const updatedRequest = await repository.save(request);

    console.log(`✅ Updated regularization request ${id}`);

    return res.status(200).json({
      success: true,
      message: 'Regularization request updated successfully',
      data: updatedRequest
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('❌ Error updating regularization request:', error);
    logger.error('Error updating regularization request', { error: errorMessage });
    return res.status(500).json({
      success: false,
      message: 'Failed to update regularization request',
      error: process.env.NODE_ENV === 'development' ? errorMessage : undefined
    });
  }
};

/**
 * Delete a regularization request
 */
export const deleteRegularizationRequest = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;

    console.log(`🗑️ Deleting regularization request ${id}`);

    const repository = AppDataSource.getRepository(Regularization);
    const request = await repository.findOne({
      where: { id: parseInt(id) }
    });

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Regularization request not found'
      });
    }

    // Only allow deletion if request is pending
    if (request.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete request that is not in pending status'
      });
    }

    await repository.remove(request);

    console.log(`✅ Deleted regularization request ${id}`);

    return res.status(200).json({
      success: true,
      message: 'Regularization request deleted successfully'
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('❌ Error deleting regularization request:', error);
    logger.error('Error deleting regularization request', { error: errorMessage });
    return res.status(500).json({
      success: false,
      message: 'Failed to delete regularization request',
      error: process.env.NODE_ENV === 'development' ? errorMessage : undefined
    });
  }
};

/**
 * Get regularization statistics
 */
export const getRegularizationStatistics = async (req: Request, res: Response): Promise<Response> => {
  try {
    const { employeeId, startDate, endDate } = req.query;

    console.log('📊 Fetching regularization statistics');

    const repository = AppDataSource.getRepository(Regularization);
    
    let query = repository.createQueryBuilder('regularization');

    // Apply filters
    if (employeeId) {
      query = query.andWhere('regularization.employeeId = :employeeId', { 
        employeeId: employeeId as string 
      });
    }

    if (startDate) {
      query = query.andWhere('regularization.date >= :startDate', { startDate });
    }

    if (endDate) {
      query = query.andWhere('regularization.date <= :endDate', { endDate });
    }

    const requests = await query.getMany();

    const statistics = {
      total: requests.length,
      pending: requests.filter(req => req.status === 'pending').length,
      approved: requests.filter(req => req.status === 'approved').length,
      rejected: requests.filter(req => req.status === 'rejected').length,
      approvalRate: requests.length > 0 
        ? ((requests.filter(req => req.status === 'approved').length / requests.length) * 100).toFixed(1)
        : '0'
    };

    console.log('✅ Fetched regularization statistics:', statistics);

    return res.status(200).json({
      success: true,
      data: statistics
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('❌ Error fetching regularization statistics:', error);
    logger.error('Error fetching regularization statistics', { error: errorMessage });
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch regularization statistics',
      error: process.env.NODE_ENV === 'development' ? errorMessage : undefined
    });
  }
}; 