import api, { safelyHandleResponse } from './api';
import { SystemLogData } from './SystemLogService';

// Interface for filtering options when getting logs
interface GetLogsOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  search?: string;
  type?: string;
  user?: string;
  dateFrom?: string;
  dateTo?: string;
  includeAnonymous?: boolean;
  onlyValidUsers?: boolean;
}

const SystemLogApiService = {
  // Get all system logs with optional filtering and pagination
  getAllLogs: async (options?: GetLogsOptions) => {
    const params = new URLSearchParams();
    
    // Add all options to query params
    if (options) {
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }
    
    return await safelyHandleResponse(
      api.get(`/system-logs?${params.toString()}`)
    );
  },
  
  // Get a single system log by ID
  getLogById: async (id: string) => {
    return await safelyHandleResponse(
      api.get(`/system-logs/${id}`)
    );
  },
  
  // Create a new system log
  createLog: async (logData: Omit<SystemLogData, 'id' | 'timestamp'>) => {
    return await safelyHandleResponse(
      api.post('/system-logs', logData)
    );
  },
  
  // Update an existing system log
  updateLog: async (id: string, logData: Partial<SystemLogData>) => {
    return await safelyHandleResponse(
      api.put(`/system-logs/${id}`, logData)
    );
  },
  
  // Delete a system log
  deleteLog: async (id: string) => {
    return await safelyHandleResponse(
      api.delete(`/system-logs/${id}`)
    );
  },
  
  // Clear all system logs
  clearLogs: async () => {
    return await safelyHandleResponse(
      api.delete('/system-logs')
    );
  },
  
  // Get statistics about system logs
  getStatistics: async () => {
    return await safelyHandleResponse(
      api.get('/system-logs/statistics')
    );
  },
  
  // Export logs as CSV
  exportLogs: async (options?: {
    type?: string;
    user?: string;
    dateFrom?: string;
    dateTo?: string;
  }) => {
    const params = new URLSearchParams();
    
    // Add all options to query params
    if (options) {
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }
    
    // Using fetch directly for blob response
    const baseURL = api.defaults.baseURL || '';
    const apiEndpointUrl = `${baseURL}/system-logs/export?${params.toString()}`;
    
    try {
      const response = await fetch(apiEndpointUrl, {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
          Accept: 'text/csv'
        }
      });
      
      if (!response.ok) {
        throw new Error(`Export failed: ${response.status} ${response.statusText}`);
      }
      
      // Get the blob data
      const blob = await response.blob();
      
      // Create an object URL for the blob
      const url = window.URL.createObjectURL(blob);
      
      // Create a download link and trigger it
      const a = document.createElement('a');
      a.href = url;
      a.download = 'system_logs.csv';
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      return { data: true, error: null };
    } catch (error) {
      console.error('Error exporting logs:', error);
      return { data: null, error: error instanceof Error ? error.message : 'Unknown error during export' };
    }
  },
  
  // Delete all anonymous logs
  deleteAnonymousLogs: async () => {
    return await safelyHandleResponse(
      api.delete('/system-logs/anonymous')
    );
  },
  
  // Delete logs for users that don't exist in the database
  deleteInvalidUserLogs: async () => {
    return await safelyHandleResponse(
      api.delete('/system-logs/invalid-users')
    );
  }
};

export default SystemLogApiService; 