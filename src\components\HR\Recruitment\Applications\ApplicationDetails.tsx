import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Grid,
  Chip,
  Avatar,
  Divider,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Alert,
  CircularProgress,
  Rating,
  Paper
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  Work as WorkIcon,
  School as SchoolIcon,
  Language as LanguageIcon,
  Schedule as ScheduleIcon,
  Assessment as AssessmentIcon,
  MoreVert as MoreVertIcon,
  Download as DownloadIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { recruitmentAPI } from '../../../../services/recruitmentAPI';

interface JobApplication {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  coverLetter?: string;
  resumeUrl?: string;
  portfolioUrl?: string;
  linkedinUrl?: string;
  githubUrl?: string;
  status: string;
  source: string;
  rating: number;
  isStarred: boolean;
  notes?: string;
  skills?: string[];
  experience?: Array<{
    company: string;
    position: string;
    startDate: string;
    endDate?: string;
    current: boolean;
    description?: string;
  }>;
  education?: Array<{
    institution: string;
    degree: string;
    field: string;
    startDate: string;
    endDate?: string;
    gpa?: string;
  }>;
  languages?: Array<{
    language: string;
    proficiency: string;
  }>;
  expectedSalary?: number;
  salaryCurrency?: string;
  availableStartDate?: string;
  createdAt: string;
  jobPosting: {
    id: number;
    title: string;
    department: string;
    location: string;
  };
  assignedTo?: {
    id: string;
    firstName: string;
    lastName: string;
  };
  interviews?: any[];
  evaluations?: any[];
}

const ApplicationDetails: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [application, setApplication] = useState<JobApplication | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [notesDialogOpen, setNotesDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState('');
  const [newNotes, setNewNotes] = useState('');
  const [newRating, setNewRating] = useState(0);

  useEffect(() => {
    if (id) {
      fetchApplication();
    }
  }, [id]);

  const fetchApplication = async () => {
    try {
      setLoading(true);
      const response = await recruitmentAPI.getJobApplicationById(Number(id));
      setApplication(response.data.application);
      setNewStatus(response.data.application.status);
      setNewRating(response.data.application.rating);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch application');
    } finally {
      setLoading(false);
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleToggleStar = async () => {
    if (!application) return;
    try {
      await recruitmentAPI.toggleApplicationStar(application.id);
      await fetchApplication();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to update application');
    }
  };

  const handleStatusUpdate = async () => {
    if (!application) return;
    try {
      await recruitmentAPI.updateApplicationStatus(application.id, newStatus);
      await fetchApplication();
      setStatusDialogOpen(false);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to update status');
    }
  };

  const handleRatingUpdate = async (rating: number) => {
    if (!application) return;
    try {
      await recruitmentAPI.rateApplication(application.id, rating);
      await fetchApplication();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to update rating');
    }
  };

  const handleAddNotes = async () => {
    if (!application || !newNotes.trim()) return;
    try {
      await recruitmentAPI.addApplicationNotes(application.id, newNotes, true);
      await fetchApplication();
      setNotesDialogOpen(false);
      setNewNotes('');
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to add notes');
    }
  };

  const getStatusColor = (status: string) => {
    const statusColors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      submitted: 'info',
      under_review: 'warning',
      interviewing: 'primary',
      hired: 'success',
      rejected: 'error',
      withdrawn: 'default'
    };
    return statusColors[status] || 'default';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error || !application) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error || 'Application not found'}
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="between" alignItems="center" mb={3}>
        <Box display="flex" alignItems="center">
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/hr/recruitment/applications')}
            sx={{ mr: 2 }}
          >
            Back to Applications
          </Button>
          <Typography variant="h4" component="h1">
            Application Details
          </Typography>
        </Box>
        <Box>
          <IconButton onClick={handleToggleStar}>
            {application.isStarred ? (
              <StarIcon color="warning" />
            ) : (
              <StarBorderIcon />
            )}
          </IconButton>
          <IconButton onClick={handleMenuClick}>
            <MoreVertIcon />
          </IconButton>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Candidate Information */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ width: 60, height: 60, mr: 2, bgcolor: 'primary.main' }}>
                  {getInitials(application.firstName, application.lastName)}
                </Avatar>
                <Box>
                  <Typography variant="h6">
                    {application.firstName} {application.lastName}
                  </Typography>
                  <Chip
                    label={application.status.replace(/_/g, ' ').toUpperCase()}
                    color={getStatusColor(application.status)}
                    size="small"
                  />
                </Box>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Rating
                </Typography>
                <Rating
                  value={application.rating}
                  onChange={(event, newValue) => {
                    if (newValue !== null) {
                      handleRatingUpdate(newValue);
                    }
                  }}
                />
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                  Contact Information
                </Typography>
                <Box display="flex" alignItems="center" mb={1}>
                  <EmailIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2">{application.email}</Typography>
                </Box>
                {application.phone && (
                  <Box display="flex" alignItems="center" mb={1}>
                    <PhoneIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                    <Typography variant="body2">{application.phone}</Typography>
                  </Box>
                )}
                {application.address && (
                  <Box display="flex" alignItems="center" mb={1}>
                    <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                    <Typography variant="body2">
                      {application.city}, {application.country}
                    </Typography>
                  </Box>
                )}
              </Box>

              <Divider sx={{ my: 2 }} />

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                  Application Details
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Applied:</strong> {formatDate(application.createdAt)}
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Source:</strong> {application.source.replace(/_/g, ' ')}
                </Typography>
                {application.expectedSalary && (
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>Expected Salary:</strong> {application.salaryCurrency || '$'}
                    {application.expectedSalary.toLocaleString()}
                  </Typography>
                )}
                {application.availableStartDate && (
                  <Typography variant="body2">
                    <strong>Available:</strong> {formatDate(application.availableStartDate)}
                  </Typography>
                )}
              </Box>

              <Box sx={{ mt: 2 }}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<ScheduleIcon />}
                  onClick={() => navigate(`/hr/recruitment/applications/${application.id}/interview`)}
                  sx={{ mb: 1 }}
                >
                  Schedule Interview
                </Button>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<AssessmentIcon />}
                  onClick={() => navigate(`/hr/recruitment/applications/${application.id}/evaluate`)}
                >
                  Add Evaluation
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Main Content */}
        <Grid item xs={12} md={8}>
          {/* Job Position */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Applied Position
              </Typography>
              <Typography variant="h5" color="primary" gutterBottom>
                {application.jobPosting.title}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {application.jobPosting.department} • {application.jobPosting.location}
              </Typography>
            </CardContent>
          </Card>

          {/* Cover Letter */}
          {application.coverLetter && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Cover Letter
                </Typography>
                <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                  {application.coverLetter}
                </Typography>
              </CardContent>
            </Card>
          )}

          {/* Skills */}
          {application.skills && application.skills.length > 0 && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Skills
                </Typography>
                <Box>
                  {application.skills.map((skill, index) => (
                    <Chip
                      key={index}
                      label={skill}
                      variant="outlined"
                      sx={{ mr: 1, mb: 1 }}
                    />
                  ))}
                </Box>
              </CardContent>
            </Card>
          )}

          {/* Experience */}
          {application.experience && application.experience.length > 0 && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Work Experience
                </Typography>
                <Box>
                  {application.experience.map((exp, index) => (
                    <Paper key={index} elevation={1} sx={{ p: 2, mb: 2, position: 'relative' }}>
                      <Box display="flex" alignItems="center" mb={1}>
                        <WorkIcon sx={{ mr: 1, color: 'primary.main' }} />
                        <Typography variant="subtitle1" fontWeight="bold">
                          {exp.position}
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="primary" sx={{ mb: 1 }}>
                        {exp.company}
                      </Typography>
                      <Typography variant="caption" color="textSecondary" sx={{ mb: 1, display: 'block' }}>
                        {formatDate(exp.startDate)} - {exp.current ? 'Present' : formatDate(exp.endDate || '')}
                      </Typography>
                      {exp.description && (
                        <Typography variant="body2" sx={{ mt: 1 }}>
                          {exp.description}
                        </Typography>
                      )}
                      {/* Simple timeline connector */}
                      {index < application.experience!.length - 1 && (
                        <Box
                          sx={{
                            position: 'absolute',
                            left: 12,
                            bottom: -16,
                            width: 2,
                            height: 16,
                            bgcolor: 'primary.main',
                            opacity: 0.3
                          }}
                        />
                      )}
                    </Paper>
                  ))}
                </Box>
              </CardContent>
            </Card>
          )}

          {/* Education */}
          {application.education && application.education.length > 0 && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Education
                </Typography>
                {application.education.map((edu, index) => (
                  <Paper key={index} elevation={1} sx={{ p: 2, mb: 2 }}>
                    <Box display="flex" alignItems="center" mb={1}>
                      <SchoolIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      <Typography variant="subtitle1" fontWeight="bold">
                        {edu.degree} in {edu.field}
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="primary">
                      {edu.institution}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      {formatDate(edu.startDate)} - {formatDate(edu.endDate || '')}
                      {edu.gpa && ` • GPA: ${edu.gpa}`}
                    </Typography>
                  </Paper>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Links */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Links & Documents
              </Typography>
              <Grid container spacing={2}>
                {application.resumeUrl && (
                  <Grid item xs={12} sm={6}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<DownloadIcon />}
                      href={application.resumeUrl}
                      target="_blank"
                    >
                      View Resume
                    </Button>
                  </Grid>
                )}
                {application.portfolioUrl && (
                  <Grid item xs={12} sm={6}>
                    <Button
                      fullWidth
                      variant="outlined"
                      href={application.portfolioUrl}
                      target="_blank"
                    >
                      View Portfolio
                    </Button>
                  </Grid>
                )}
                {application.linkedinUrl && (
                  <Grid item xs={12} sm={6}>
                    <Button
                      fullWidth
                      variant="outlined"
                      href={application.linkedinUrl}
                      target="_blank"
                    >
                      LinkedIn Profile
                    </Button>
                  </Grid>
                )}
                {application.githubUrl && (
                  <Grid item xs={12} sm={6}>
                    <Button
                      fullWidth
                      variant="outlined"
                      href={application.githubUrl}
                      target="_blank"
                    >
                      GitHub Profile
                    </Button>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => { setStatusDialogOpen(true); handleMenuClose(); }}>
          <EditIcon sx={{ mr: 1 }} />
          Update Status
        </MenuItem>
        <MenuItem onClick={() => { setNotesDialogOpen(true); handleMenuClose(); }}>
          Add Notes
        </MenuItem>
      </Menu>

      {/* Status Update Dialog */}
      <Dialog open={statusDialogOpen} onClose={() => setStatusDialogOpen(false)}>
        <DialogTitle>Update Application Status</DialogTitle>
        <DialogContent>
          <FormControl fullWidth sx={{ mt: 1 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={newStatus}
              label="Status"
              onChange={(e) => setNewStatus(e.target.value)}
            >
              <MenuItem value="submitted">Submitted</MenuItem>
              <MenuItem value="under_review">Under Review</MenuItem>
              <MenuItem value="interview_scheduled">Interview Scheduled</MenuItem>
              <MenuItem value="interviewing">Interviewing</MenuItem>
              <MenuItem value="offer_pending">Offer Pending</MenuItem>
              <MenuItem value="hired">Hired</MenuItem>
              <MenuItem value="rejected">Rejected</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setStatusDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleStatusUpdate} variant="contained">
            Update
          </Button>
        </DialogActions>
      </Dialog>

      {/* Notes Dialog */}
      <Dialog open={notesDialogOpen} onClose={() => setNotesDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Notes</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            multiline
            rows={4}
            value={newNotes}
            onChange={(e) => setNewNotes(e.target.value)}
            placeholder="Add your notes about this candidate..."
            sx={{ mt: 1 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNotesDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleAddNotes} variant="contained">
            Add Notes
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ApplicationDetails;
