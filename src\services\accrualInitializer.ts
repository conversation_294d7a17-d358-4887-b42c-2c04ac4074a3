import { leaveAccrualJobService } from './LeaveAccrualJobService';

export class AccrualSystemInitializer {
  private isInitialized = false;

  /**
   * Initialize the accrual processing system
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('⚠️ Accrual system already initialized');
      return;
    }

    try {
      console.log('🚀 Initializing Automated Leave Accrual System...');
      
      // Initialize the accrual job service
      await leaveAccrualJobService.initializeAccrualJobs();
      
      this.isInitialized = true;
      
      console.log('✅ Automated Leave Accrual System initialized successfully');
      console.log('📋 Scheduled jobs:');
      console.log('   - Daily accruals: Every day at 2:00 AM UTC');
      console.log('   - Monthly accruals: 1st of each month at 3:00 AM UTC');
      console.log('   - Quarterly accruals: 1st of quarter at 4:00 AM UTC');
      console.log('   - Annual accruals: January 1st at 5:00 AM UTC');
      
    } catch (error) {
      console.error('❌ Failed to initialize accrual system:', error);
      throw error;
    }
  }

  /**
   * Shutdown the accrual processing system
   */
  async shutdown(): Promise<void> {
    if (!this.isInitialized) {
      console.log('ℹ️ Accrual system not initialized, nothing to shutdown');
      return;
    }

    try {
      console.log('🛑 Shutting down Automated Leave Accrual System...');
      
      leaveAccrualJobService.stopAllJobs();
      
      this.isInitialized = false;
      
      console.log('✅ Automated Leave Accrual System shutdown completed');
      
    } catch (error) {
      console.error('❌ Error during accrual system shutdown:', error);
      throw error;
    }
  }

  /**
   * Check if the system is initialized
   */
  isSystemInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Get system status
   */
  async getSystemStatus(): Promise<any> {
    return {
      initialized: this.isInitialized,
      ...(this.isInitialized ? await leaveAccrualJobService.getAccrualStatus() : {})
    };
  }
}

export const accrualSystemInitializer = new AccrualSystemInitializer();

// Graceful shutdown handling
process.on('SIGINT', async () => {
  console.log('🔄 Received SIGINT, shutting down accrual system gracefully...');
  try {
    await accrualSystemInitializer.shutdown();
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during graceful shutdown:', error);
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  console.log('🔄 Received SIGTERM, shutting down accrual system gracefully...');
  try {
    await accrualSystemInitializer.shutdown();
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during graceful shutdown:', error);
    process.exit(1);
  }
}); 