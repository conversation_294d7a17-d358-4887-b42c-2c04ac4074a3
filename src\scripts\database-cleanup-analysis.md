# Leave Management Database Cleanup Analysis

## 🗄️ Current Database Structure vs Frontend Usage

### Tables that can be REMOVED:
```sql
-- Complex accrual system (not used in current frontend)
DROP TABLE IF EXISTS leave_accrual_rules;

-- Redundant with leave_type_policies
DROP TABLE IF EXISTS leave_policies;

-- Settings now embedded in leave_type_policies.settings JSON
DROP TABLE IF EXISTS leave_policy_settings;

-- Version tracking not implemented in UI
DROP TABLE IF EXISTS leave_policy_versions;
```

### Tables to KEEP (actively used):
- ✅ `leave_type_policies` - Core leave type definitions
- ✅ `leave_balances` - Employee balances
- ✅ `leave_allocations` - Leave allocations 
- ✅ `leave_requests` - Leave applications
- ✅ `leave_policy_configurations` - Policy container

### Benefits of Cleanup:
1. **Simpler schema** - Easier to understand and maintain
2. **Better performance** - Fewer unused tables
3. **Clear architecture** - Database matches frontend functionality
4. **Reduced confusion** - No unused entities

### Migration Steps:
1. Backup existing data
2. Migrate any useful data from unused tables
3. Drop unused tables
4. Update entity definitions
5. Clean up unused repository files

## 🎯 Recommended Action:
Focus on the 5 core tables that match the current simplified frontend design. 