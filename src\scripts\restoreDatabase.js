const mysql = require('mysql2/promise');
require('dotenv').config();

// Database connection config
const DB_HOST = process.env.DB_HOST || 'localhost';
const DB_PORT = process.env.DB_PORT || '3306';
const DB_USERNAME = process.env.DB_USERNAME || 'root';
const DB_PASSWORD = process.env.DB_PASSWORD || 'root';
const DB_NAME = 'ims_db';

async function restoreDatabase() {
  let connection;
  
  try {
    // Create connection without database first
    connection = await mysql.createConnection({
      host: DB_HOST,
      port: parseInt(DB_PORT),
      user: DB_USERNAME,
      password: DB_PASSWORD,
    });
    
    console.log('Connected to MySQL server');
    
    // Check if database exists, if not create it
    await connection.query(`CREATE DATABASE IF NOT EXISTS ${DB_NAME}`);
    console.log(`Database ${DB_NAME} created or already exists`);
    
    // Switch to the database
    await connection.query(`USE ${DB_NAME}`);
    console.log(`Using database ${DB_NAME}`);
    
    // Create admin user if not exists
    const adminUser = {
      id: 1, // Explicitly set the ID to 1
      name: 'Administrator',
      email: '<EMAIL>',
      password: '$2b$10$EjaI3g8bI/L0t4Z4Xg/FgeE/ZxRdT0YaGU.Fg0V9yA9UNqDJy6Pcy', // hash for 'admin123'
      role: 'IT_ADMIN',
      department: 'IT',
      isActive: 1,
      permissions: JSON.stringify({
        canCreateTickets: true,
        canCreateTicketsForOthers: true,
        canEditTickets: true,
        canDeleteTickets: true,
        canCloseTickets: true,
        canLockTickets: true,
        canAssignTickets: true,
        canEscalateTickets: true,
        canViewAllTickets: true
      })
    };
    
    // Create users table if not exists
    await connection.query(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        role ENUM('EMPLOYEE', 'DEPT_HEAD', 'IT_STAFF', 'IT_ADMIN', 'IT_SUPPORT') NOT NULL DEFAULT 'EMPLOYEE',
        department VARCHAR(255) NOT NULL,
        project TEXT NULL,
        location TEXT NULL,
        isActive TINYINT(1) NOT NULL DEFAULT 1,
        permissions JSON NULL,
        createdAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
        updatedAt DATETIME(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
      )
    `);
    console.log('Users table created or already exists');
    
    // Check if admin user exists
    const [rows] = await connection.query('SELECT * FROM users WHERE email = ?', [adminUser.email]);
    
    if (rows.length === 0) {
      // Insert admin user with explicit ID
      await connection.query(`
        INSERT INTO users (id, name, email, password, role, department, isActive, permissions)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        adminUser.id,
        adminUser.name,
        adminUser.email,
        adminUser.password,
        adminUser.role,
        adminUser.department,
        adminUser.isActive,
        adminUser.permissions
      ]);
      console.log('Admin user created');
    } else {
      // Update admin user to ensure correct role and permissions
      await connection.query(`
        UPDATE users 
        SET id = ?, role = ?, isActive = ?, permissions = ?
        WHERE email = ?
      `, [
        adminUser.id,
        adminUser.role,
        adminUser.isActive,
        adminUser.permissions,
        adminUser.email
      ]);
      console.log('Admin user updated');
    }
    
    console.log('Database restoration completed successfully');
    
  } catch (error) {
    console.error('Error restoring database:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

// Run the restoration
restoreDatabase(); 