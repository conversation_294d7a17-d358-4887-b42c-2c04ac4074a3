import { Router } from 'express';
import { LeaveBalanceController } from '../controllers/leaveBalanceController';
import { requireAuth } from '../middleware/auth';

const router = Router();
const leaveBalanceController = new LeaveBalanceController();

// Apply authentication middleware to all routes
router.use(requireAuth);

// Get all leave balances for a specific employee
router.get('/employee/:employeeId', leaveBalanceController.getByEmployeeId.bind(leaveBalanceController));

// Get leave balance for a specific employee and leave type
router.get('/employee/:employeeId/type/:leaveType', leaveBalanceController.getByEmployeeAndType.bind(leaveBalanceController));

// Get all employee balances (for HR management)
router.get('/all', leaveBalanceController.getAllEmployeeBalances.bind(leaveBalanceController));

// Get all active employees with their leave balances (including employees with no balances)
router.get('/all-active-employees', leaveBalanceController.getAllActiveEmployeesWithBalances.bind(leaveBalanceController));

// Create or update leave balance
router.post('/', leaveBalanceController.upsertLeaveBalance.bind(leaveBalanceController));
router.put('/', leaveBalanceController.upsertLeaveBalance.bind(leaveBalanceController));

// Auto-allocate leaves based on policies
router.post('/auto-allocate', leaveBalanceController.autoAllocateLeaves.bind(leaveBalanceController));

// Bulk allocate specific leave type to multiple employees
router.post('/bulk-allocate', leaveBalanceController.bulkAllocateLeave.bind(leaveBalanceController));

// Bulk adjust leave quotas for multiple employees
router.post('/bulk-adjust', leaveBalanceController.bulkAdjustLeaveQuotas.bind(leaveBalanceController));

// Bulk save all employee leave allocations
router.post('/bulk-save-allocations', leaveBalanceController.bulkSaveAllocations.bind(leaveBalanceController));

export default router; 