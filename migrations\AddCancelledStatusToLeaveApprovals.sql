-- Migration: Add CANCELLED status to leave_approvals table
-- Date: 2025-07-18

-- Update the enum to include CANCELLED status
ALTER TABLE leave_approvals 
MODIFY COLUMN status ENUM('pending', 'approved', 'rejected', 'cancelled') NOT NULL DEFAULT 'pending';

-- Update any existing CANCELLED values to use lowercase 'cancelled' to match enum
UPDATE leave_approvals 
SET status = 'cancelled' 
WHERE status = 'CANCELLED'; 