import { MigrationInterface, QueryRunner } from "typeorm";

export class AddSalaryTrenchField1700000000019 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        try {
            // Add the salaryTrench column to employee_benefits table
            await queryRunner.query(`
                ALTER TABLE employee_benefits 
                ADD COLUMN salaryTrench VARCHAR(255) NULL
            `);
            
            console.log('Added salaryTrench column to employee_benefits table');
        } catch (error) {
            console.error('Migration failed:', error);
            throw error;
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        try {
            // Remove the salaryTrench column from employee_benefits table
            await queryRunner.query(`
                ALTER TABLE employee_benefits 
                DROP COLUMN IF EXISTS salaryTrench
            `);
            
            console.log('Removed salaryTrench column from employee_benefits table');
        } catch (error) {
            console.error('Migration reversion failed:', error);
            throw error;
        }
    }
} 