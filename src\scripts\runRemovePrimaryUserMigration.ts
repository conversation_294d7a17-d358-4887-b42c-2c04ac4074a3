import { AppDataSource } from '../config/database';
import { RemovePrimaryAssignedUser1710000000010 } from '../migrations/RemovePrimaryAssignedUser';

async function runMigration() {
  console.log('Initializing data source...');
  
  try {
    await AppDataSource.initialize();
    console.log('Data source initialized');
    
    console.log('Running migration to remove primary assigned user...');
    const queryRunner = AppDataSource.createQueryRunner();
    
    const migration = new RemovePrimaryAssignedUser1710000000010();
    await migration.up(queryRunner);
    
    console.log('Migration completed successfully');
    
    await AppDataSource.destroy();
    console.log('Connection closed');
    
    process.exit(0);
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  }
}

runMigration(); 