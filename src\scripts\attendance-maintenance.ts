import { AppDataSource } from '../config/database';
import { AttendanceValidationService } from '../server/services/AttendanceValidationService';
import { logger } from '../server/utils/logger';

/**
 * Attendance Data Maintenance Script
 * 
 * This script helps maintain data integrity in the attendance system by:
 * 1. Finding orphaned attendance records (attendance without valid employees)
 * 2. Cleaning up invalid data
 * 3. Generating reports on data quality
 */

interface MaintenanceReport {
  totalAttendanceRecords: number;
  orphanedRecords: number;
  cleanedRecords: number;
  errors: string[];
  warnings: string[];
  executionTime: number;
}

class AttendanceMaintenanceService {
  
  /**
   * Run comprehensive maintenance check
   */
  static async runMaintenance(options: {
    dryRun?: boolean;
    cleanupOrphaned?: boolean;
    generateReport?: boolean;
  } = {}): Promise<MaintenanceReport> {
    const startTime = Date.now();
    const report: MaintenanceReport = {
      totalAttendanceRecords: 0,
      orphanedRecords: 0,
      cleanedRecords: 0,
      errors: [],
      warnings: [],
      executionTime: 0
    };

    try {
      logger.info('Starting attendance data maintenance...');

      // Initialize database connection
      if (!AppDataSource.isInitialized) {
        await AppDataSource.initialize();
        logger.info('Database connection initialized');
      }

      // 1. Get total attendance records count
      const totalRecords = await AppDataSource.getRepository('Attendance')
        .createQueryBuilder('attendance')
        .getCount();
      
      report.totalAttendanceRecords = totalRecords;
      logger.info(`Total attendance records: ${totalRecords}`);

      // 2. Find orphaned records
      const orphanedRecords = await AttendanceValidationService.findOrphanedAttendanceRecords();
      report.orphanedRecords = orphanedRecords.length;

      if (orphanedRecords.length > 0) {
        logger.warn(`Found ${orphanedRecords.length} orphaned attendance records`);
        
        orphanedRecords.forEach(record => {
          report.warnings.push(
            `Orphaned record: ID ${record.id}, Employee ID ${record.employeeId}, Date ${record.date}`
          );
        });

        // 3. Clean up orphaned records if requested
        if (options.cleanupOrphaned && !options.dryRun) {
          logger.info('Cleaning up orphaned records...');
          const cleanupResult = await AttendanceValidationService.cleanupOrphanedRecords();
          report.cleanedRecords = cleanupResult.deletedCount;
          report.errors.push(...cleanupResult.errors);
          
          logger.info(`Cleaned up ${cleanupResult.deletedCount} orphaned records`);
          if (cleanupResult.errors.length > 0) {
            logger.error(`Cleanup errors: ${cleanupResult.errors.join(', ')}`);
          }
        } else if (options.dryRun) {
          logger.info('DRY RUN: Would clean up orphaned records (use --cleanup to actually delete)');
        }
      } else {
        logger.info('No orphaned attendance records found');
      }

      // 4. Additional data integrity checks
      await this.performIntegrityChecks(report);

      // 5. Generate detailed report if requested
      if (options.generateReport) {
        await this.generateDetailedReport(report);
      }

      report.executionTime = Date.now() - startTime;
      logger.info(`Maintenance completed in ${report.executionTime}ms`);

    } catch (error) {
      logger.error('Error during maintenance:', error);
      report.errors.push(`Maintenance error: ${error}`);
    }

    return report;
  }

  /**
   * Perform additional data integrity checks
   */
  private static async performIntegrityChecks(report: MaintenanceReport): Promise<void> {
    try {
      // Check for attendance records with invalid employee IDs
      const invalidEmployeeIds = await AppDataSource.query(`
        SELECT DISTINCT a.employeeId 
        FROM attendances a 
        LEFT JOIN employees e ON a.employeeId = e.id 
        WHERE e.id IS NULL
      `);

      if (invalidEmployeeIds.length > 0) {
        report.warnings.push(`Found ${invalidEmployeeIds.length} distinct invalid employee IDs in attendance records`);
      }

      // Check for duplicate attendance records (same employee, same date)
      const duplicates = await AppDataSource.query(`
        SELECT employeeId, date, COUNT(*) as count
        FROM attendances 
        GROUP BY employeeId, date 
        HAVING COUNT(*) > 1
      `);

      if (duplicates.length > 0) {
        report.warnings.push(`Found ${duplicates.length} sets of duplicate attendance records`);
        duplicates.forEach((dup: any) => {
          report.warnings.push(`Duplicate: Employee ${dup.employeeId}, Date ${dup.date} (${dup.count} records)`);
        });
      }

      // Check for attendance records with invalid dates
      const invalidDates = await AppDataSource.query(`
        SELECT id, employeeId, date 
        FROM attendances 
        WHERE date NOT REGEXP '^[0-9]{4}-[0-9]{2}-[0-9]{2}$'
        LIMIT 10
      `);

      if (invalidDates.length > 0) {
        report.warnings.push(`Found ${invalidDates.length} attendance records with invalid date formats`);
      }

      // Check for attendance records with invalid time formats
      const invalidTimes = await AppDataSource.query(`
        SELECT id, employeeId, checkInTime, checkOutTime 
        FROM attendances 
        WHERE (checkInTime IS NOT NULL AND checkInTime NOT REGEXP '^[0-9]{2}:[0-9]{2}$')
           OR (checkOutTime IS NOT NULL AND checkOutTime NOT REGEXP '^[0-9]{2}:[0-9]{2}$')
        LIMIT 10
      `);

      if (invalidTimes.length > 0) {
        report.warnings.push(`Found ${invalidTimes.length} attendance records with invalid time formats`);
      }

    } catch (error) {
      logger.error('Error during integrity checks:', error);
      report.errors.push(`Integrity check error: ${error}`);
    }
  }

  /**
   * Generate detailed maintenance report
   */
  private static async generateDetailedReport(report: MaintenanceReport): Promise<void> {
    const reportContent = `
ATTENDANCE DATA MAINTENANCE REPORT
Generated: ${new Date().toISOString()}
Execution Time: ${report.executionTime}ms

SUMMARY:
- Total Attendance Records: ${report.totalAttendanceRecords}
- Orphaned Records Found: ${report.orphanedRecords}
- Records Cleaned: ${report.cleanedRecords}
- Errors: ${report.errors.length}
- Warnings: ${report.warnings.length}

ERRORS:
${report.errors.map(error => `- ${error}`).join('\n')}

WARNINGS:
${report.warnings.map(warning => `- ${warning}`).join('\n')}

RECOMMENDATIONS:
${report.orphanedRecords > 0 ? '- Run cleanup to remove orphaned records' : '- No orphaned records found'}
${report.warnings.length > 0 ? '- Review warnings and fix data quality issues' : '- Data quality looks good'}
${report.errors.length > 0 ? '- Address errors before next maintenance run' : '- No errors encountered'}
    `;

    // Save report to file
    const fs = require('fs');
    const path = require('path');
    const reportsDir = path.join(process.cwd(), 'logs', 'maintenance');
    
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    const reportFile = path.join(reportsDir, `attendance-maintenance-${Date.now()}.txt`);
    fs.writeFileSync(reportFile, reportContent);
    
    logger.info(`Detailed report saved to: ${reportFile}`);
  }
}

/**
 * CLI interface for running maintenance
 */
async function main() {
  const args = process.argv.slice(2);
  const options = {
    dryRun: args.includes('--dry-run'),
    cleanupOrphaned: args.includes('--cleanup'),
    generateReport: args.includes('--report')
  };

  console.log('🔧 Attendance Data Maintenance Tool');
  console.log('=====================================');
  
  if (options.dryRun) {
    console.log('🔍 Running in DRY RUN mode - no changes will be made');
  }

  try {
    const report = await AttendanceMaintenanceService.runMaintenance(options);
    
    console.log('\n📊 MAINTENANCE SUMMARY:');
    console.log(`Total Records: ${report.totalAttendanceRecords}`);
    console.log(`Orphaned Records: ${report.orphanedRecords}`);
    console.log(`Cleaned Records: ${report.cleanedRecords}`);
    console.log(`Errors: ${report.errors.length}`);
    console.log(`Warnings: ${report.warnings.length}`);
    console.log(`Execution Time: ${report.executionTime}ms`);

    if (report.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      report.errors.forEach(error => console.log(`  - ${error}`));
    }

    if (report.warnings.length > 0) {
      console.log('\n⚠️  WARNINGS:');
      report.warnings.forEach(warning => console.log(`  - ${warning}`));
    }

    console.log('\n✅ Maintenance completed successfully!');
    
  } catch (error) {
    console.error('❌ Maintenance failed:', error);
    process.exit(1);
  }
}

// Export for use as module or run as script
export { AttendanceMaintenanceService };

// Run as script if called directly
if (require.main === module) {
  main().catch(console.error);
} 