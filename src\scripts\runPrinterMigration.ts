import { AppDataSource } from '../config/database';
import * as fs from 'fs';
import * as path from 'path';

async function runPrinterMigration() {
  try {
    // Initialize the data source
    await AppDataSource.initialize();
    console.log('Database connection initialized');

    // Get the query runner
    const queryRunner = AppDataSource.createQueryRunner();
    
    // Check if printer_maintenance table already exists
    const tableExists = await queryRunner.hasTable('printer_maintenance');
    
    if (tableExists) {
      console.log('Printer maintenance table already exists, dropping it to recreate with correct structure');
      await queryRunner.dropTable('printer_maintenance');
      console.log('Existing printer_maintenance table dropped successfully');
    }
    
    console.log('Creating printer maintenance table with correct field names...');
    
    // Read the migration file
    const migrationFilePath = path.join(
      process.cwd(),
      'migrations',
      '20250301_add_printer_maintenance',
      'migration.sql'
    );
    
    const migrationSQL = fs.readFileSync(migrationFilePath, 'utf8');
    
    // Split the SQL statements and execute them
    const sqlStatements = migrationSQL.split(';');
    
    for (const sql of sqlStatements) {
      const trimmedSQL = sql.trim();
      if (trimmedSQL && !trimmedSQL.startsWith('--')) {
        await queryRunner.query(trimmedSQL + ';');
      }
    }
    
    console.log('Printer maintenance table migration completed successfully');
    
    // Close the connection
    await AppDataSource.destroy();
    console.log('Database connection closed');
    
  } catch (error) {
    console.error('Error running printer migration:', error);
    
    // Close the connection if it's initialized
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('Database connection closed');
    }
    
    process.exit(1);
  }
}

// Run the migration
runPrinterMigration()
  .then(() => {
    console.log('Printer migration script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Printer migration script failed:', error);
    process.exit(1);
  }); 