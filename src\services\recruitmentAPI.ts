import axios from 'axios';

const API_BASE_URL = (typeof process !== 'undefined' && process.env?.REACT_APP_API_URL) || 'http://localhost:5000/api';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Job Posting API
export const jobPostingAPI = {
  // Create a new job posting
  create: (data: any) => api.post('/hr/recruitment/job-postings', data),

  // Get all job postings with filters and pagination
  getAll: (params?: {
    page?: number;
    limit?: number;
    status?: string;
    department?: string;
    jobType?: string;
    experienceLevel?: string;
    workLocation?: string;
    search?: string;
    isActive?: boolean;
    isUrgent?: boolean;
    isFeatured?: boolean;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  }) => api.get('/hr/recruitment/job-postings', { params }),

  // Get job posting by ID
  getById: (id: number) => api.get(`/hr/recruitment/job-postings/${id}`),

  // Update job posting
  update: (id: number, data: any) => api.put(`/hr/recruitment/job-postings/${id}`, data),

  // Delete job posting
  delete: (id: number) => api.delete(`/hr/recruitment/job-postings/${id}`),

  // Publish job posting
  publish: (id: number) => api.post(`/hr/recruitment/job-postings/${id}/publish`),

  // Close job posting
  close: (id: number) => api.post(`/hr/recruitment/job-postings/${id}/close`),

  // Get job posting statistics
  getStats: () => api.get('/hr/recruitment/job-postings/stats'),

  // Public endpoints (no auth required)
  getPublic: (params?: any) => api.get('/hr/recruitment/public/job-postings', { params }),
  getPublicById: (id: number) => api.get(`/hr/recruitment/public/job-postings/${id}`),
};

// Job Application API
export const jobApplicationAPI = {
  // Create a new job application (public endpoint)
  create: (data: any) => api.post('/hr/recruitment/applications', data),

  // Get all job applications with filters and pagination
  getAll: (params?: {
    page?: number;
    limit?: number;
    status?: string;
    jobPostingId?: number;
    assignedToId?: string;
    source?: string;
    search?: string;
    rating?: number;
    isStarred?: boolean;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  }) => api.get('/hr/recruitment/applications', { params }),

  // Get job application by ID
  getById: (id: number) => api.get(`/hr/recruitment/applications/${id}`),

  // Update job application
  update: (id: number, data: any) => api.put(`/hr/recruitment/applications/${id}`, data),

  // Delete job application
  delete: (id: number) => api.delete(`/hr/recruitment/applications/${id}`),

  // Update application status
  updateStatus: (id: number, status: string, notes?: string) =>
    api.patch(`/hr/recruitment/applications/${id}/status`, { status, notes }),

  // Assign application to user
  assign: (id: number, assignedToId: string) =>
    api.patch(`/hr/recruitment/applications/${id}/assign`, { assignedToId }),

  // Star/unstar application
  toggleStar: (id: number) => api.patch(`/hr/recruitment/applications/${id}/star`),

  // Rate application
  rate: (id: number, rating: number) =>
    api.patch(`/hr/recruitment/applications/${id}/rate`, { rating }),

  // Add notes to application
  addNotes: (id: number, notes: string, isInternal: boolean = false) =>
    api.post(`/hr/recruitment/applications/${id}/notes`, { notes, isInternal }),

  // Get application statistics
  getStats: () => api.get('/hr/recruitment/applications/stats'),
};

// Interview API
export const interviewAPI = {
  // Create a new interview
  create: (data: any) => api.post('/recruitment/interviews', data),

  // Get all interviews with filters and pagination
  getAll: (params?: {
    page?: number;
    limit?: number;
    status?: string;
    type?: string;
    applicationId?: number;
    interviewerId?: string;
    startDate?: string;
    endDate?: string;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
  }) => api.get('/recruitment/interviews', { params }),

  // Get interview by ID
  getById: (id: number) => api.get(`/recruitment/interviews/${id}`),

  // Update interview
  update: (id: number, data: any) => api.put(`/recruitment/interviews/${id}`, data),

  // Delete interview
  delete: (id: number) => api.delete(`/recruitment/interviews/${id}`),

  // Update interview status
  updateStatus: (id: number, status: string) => 
    api.patch(`/recruitment/interviews/${id}/status`, { status }),

  // Reschedule interview
  reschedule: (id: number, startTime: string, endTime: string, reason?: string) => 
    api.patch(`/recruitment/interviews/${id}/reschedule`, { startTime, endTime, reason }),

  // Cancel interview
  cancel: (id: number, reason?: string) => 
    api.patch(`/recruitment/interviews/${id}/cancel`, { reason }),

  // Add interview feedback
  addFeedback: (id: number, feedback: any) => 
    api.post(`/recruitment/interviews/${id}/feedback`, feedback),

  // Get interview calendar
  getCalendar: (params?: {
    startDate?: string;
    endDate?: string;
    interviewerId?: string;
  }) => api.get('/recruitment/interviews/calendar', { params }),
};

// Application Evaluation API
export const evaluationAPI = {
  // Create evaluation
  create: (data: any) => api.post('/recruitment/evaluations', data),

  // Get evaluations for application
  getByApplication: (applicationId: number) => 
    api.get(`/recruitment/applications/${applicationId}/evaluations`),

  // Update evaluation
  update: (id: number, data: any) => api.put(`/recruitment/evaluations/${id}`, data),

  // Delete evaluation
  delete: (id: number) => api.delete(`/recruitment/evaluations/${id}`),
};

// Interview Feedback API
export const feedbackAPI = {
  // Create feedback
  create: (data: any) => api.post('/recruitment/feedback', data),

  // Get feedback for interview
  getByInterview: (interviewId: number) => 
    api.get(`/recruitment/interviews/${interviewId}/feedback`),

  // Update feedback
  update: (id: number, data: any) => api.put(`/recruitment/feedback/${id}`, data),

  // Delete feedback
  delete: (id: number) => api.delete(`/recruitment/feedback/${id}`),
};

// Recruitment Analytics API
export const analyticsAPI = {
  // Get recruitment dashboard data
  getDashboard: (params?: {
    startDate?: string;
    endDate?: string;
    department?: string;
  }) => api.get('/recruitment/analytics/dashboard', { params }),

  // Get hiring funnel data
  getHiringFunnel: (params?: {
    startDate?: string;
    endDate?: string;
    jobPostingId?: number;
  }) => api.get('/recruitment/analytics/funnel', { params }),

  // Get time-to-hire metrics
  getTimeToHire: (params?: {
    startDate?: string;
    endDate?: string;
    department?: string;
  }) => api.get('/recruitment/analytics/time-to-hire', { params }),

  // Get source effectiveness
  getSourceEffectiveness: (params?: {
    startDate?: string;
    endDate?: string;
  }) => api.get('/recruitment/analytics/source-effectiveness', { params }),

  // Get interviewer performance
  getInterviewerPerformance: (params?: {
    startDate?: string;
    endDate?: string;
    interviewerId?: string;
  }) => api.get('/recruitment/analytics/interviewer-performance', { params }),
};

// Combined recruitment API object
export const recruitmentAPI = {
  // Job Postings
  createJobPosting: jobPostingAPI.create,
  getJobPostings: jobPostingAPI.getAll,
  getJobPostingById: jobPostingAPI.getById,
  updateJobPosting: jobPostingAPI.update,
  deleteJobPosting: jobPostingAPI.delete,
  publishJobPosting: jobPostingAPI.publish,
  closeJobPosting: jobPostingAPI.close,
  getJobPostingStats: jobPostingAPI.getStats,
  getPublicJobPostings: jobPostingAPI.getPublic,
  getPublicJobPostingById: jobPostingAPI.getPublicById,

  // Job Applications
  createJobApplication: jobApplicationAPI.create,
  getJobApplications: jobApplicationAPI.getAll,
  getJobApplicationById: jobApplicationAPI.getById,
  updateJobApplication: jobApplicationAPI.update,
  deleteJobApplication: jobApplicationAPI.delete,
  updateApplicationStatus: jobApplicationAPI.updateStatus,
  assignApplication: jobApplicationAPI.assign,
  toggleApplicationStar: jobApplicationAPI.toggleStar,
  rateApplication: jobApplicationAPI.rate,
  addApplicationNotes: jobApplicationAPI.addNotes,
  getApplicationStats: jobApplicationAPI.getStats,

  // Interviews
  createInterview: interviewAPI.create,
  getInterviews: interviewAPI.getAll,
  getInterviewById: interviewAPI.getById,
  updateInterview: interviewAPI.update,
  deleteInterview: interviewAPI.delete,
  updateInterviewStatus: interviewAPI.updateStatus,
  rescheduleInterview: interviewAPI.reschedule,
  cancelInterview: interviewAPI.cancel,
  addInterviewFeedback: interviewAPI.addFeedback,
  getInterviewCalendar: interviewAPI.getCalendar,

  // Evaluations & Feedback
  createEvaluation: evaluationAPI.create,
  getEvaluationsByApplication: evaluationAPI.getByApplication,
  updateEvaluation: evaluationAPI.update,
  deleteEvaluation: evaluationAPI.delete,
  createFeedback: feedbackAPI.create,
  getFeedbackByInterview: feedbackAPI.getByInterview,
  updateFeedback: feedbackAPI.update,
  deleteFeedback: feedbackAPI.delete,

  // Analytics
  getDashboard: analyticsAPI.getDashboard,
  getHiringFunnel: analyticsAPI.getHiringFunnel,
  getTimeToHire: analyticsAPI.getTimeToHire,
  getSourceEffectiveness: analyticsAPI.getSourceEffectiveness,
  getInterviewerPerformance: analyticsAPI.getInterviewerPerformance,
};

export default recruitmentAPI;
