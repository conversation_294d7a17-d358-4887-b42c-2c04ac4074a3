import api, { safelyHandleResponse } from './api';
import { Attendance, AttendanceFilter, AttendanceSummary } from '../types/attendance';

// Only use localStorage as a temporary fallback
const saveToLocalStorage = (attendances: Attendance[]) => {
  try {
    localStorage.setItem('attendance_data_temp', JSON.stringify(attendances));
    console.log('Saved attendance data to localStorage as temporary backup');
  } catch (error) {
    console.error('Error saving to localStorage:', error);
  }
};

const getFromLocalStorage = (): Attendance[] => {
  try {
    const data = localStorage.getItem('attendance_data_temp');
    if (data) {
      const parsed = JSON.parse(data);
      console.log('Retrieved attendance data from localStorage as fallback');
      return parsed;
    }
  } catch (error) {
    console.error('Error reading from localStorage:', error);
  }
  return [];
};

// Try to ensure we're authenticated
const ensureAuthenticated = async () => {
  const authToken = localStorage.getItem('authToken');
  const userData = localStorage.getItem('userData');
  
  if (!authToken || !userData) {
    console.log('No authentication token found - attempting login');
    
    try {
      // Try default login credentials
      const loginResult = await api.post('/api/auth/login', {
        email: '<EMAIL>',
        password: 'admin123'
      });
      
      if (loginResult.data?.token) {
        localStorage.setItem('authToken', loginResult.data.token);
        localStorage.setItem('userData', JSON.stringify(loginResult.data.user));
        console.log('Successfully logged in for API access');
        return true;
      }
    } catch (error) {
      console.error('Auto-login failed:', error);
    }
    return false;
  }
  
  return true;
};

class AttendanceService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = '/api/attendance';
  }

  /**
   * Get all attendance records with optional filtering
   * @param params Filter parameters
   * @returns Promise with attendance records
   */
  async getAttendances(params?: {
    startDate?: string;
    endDate?: string;
    employeeId?: number;
    department?: string;
  }) {
    // Authentication disabled for testing - direct API call
    console.log('🔍 Fetching attendance data from API...');
    
    try {
      const queryParams = new URLSearchParams();
      
      if (params?.startDate) queryParams.append('startDate', params.startDate);
      if (params?.endDate) queryParams.append('endDate', params.endDate);
      if (params?.employeeId) queryParams.append('employeeId', params.employeeId.toString());
      if (params?.department) queryParams.append('department', params.department);
      
      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
      
      const result = await safelyHandleResponse(
        api.get<{ success: boolean; data: Attendance[] }>(`${this.baseUrl}${queryString}`)
      );
      
      console.log('📊 API Response:', result);
      
      // If API call succeeded, return the data and also update localStorage as backup
      if (result.data && !result.error) {
        console.log('✅ Successfully fetched', result.data.data?.length || 0, 'attendance records');
        // Save to localStorage as backup
        if (Array.isArray(result.data.data)) {
          saveToLocalStorage(result.data.data);
        }
        return { data: result.data.data, error: null };
      }
      
      // If API call failed, log the error and use localStorage as fallback
      console.error('❌ API call failed:', result.error);
      console.log('📦 Using localStorage data as fallback');
      const localData = getFromLocalStorage();
      
      // Filter locally instead of on the server
      let filteredData = localData;
      
      if (params?.startDate && params?.endDate) {
        filteredData = localData.filter(
          a => a.date >= params.startDate! && a.date <= params.endDate!
        );
      }
      
      if (params?.employeeId) {
        filteredData = filteredData.filter(a => a.employeeId === params.employeeId);
      }
      
      if (params?.department) {
        filteredData = filteredData.filter(a => a.department === params.department);
      }
      
      return { data: filteredData, error: null };
    } catch (error) {
      console.error('Error fetching attendance records:', error);
      
      // Fallback to localStorage
      const localData = getFromLocalStorage();
      console.log('Returning localStorage data due to API error');
      
      return { data: localData, error: null };
    }
  }

  /**
   * Get attendance for a specific employee
   * @param employeeId Employee ID
   * @param params Filter parameters
   * @returns Promise with employee attendance records
   */
  async getEmployeeAttendance(employeeId: number, params?: { startDate?: string; endDate?: string }) {
    try {
      const queryParams = new URLSearchParams();
      
      if (params?.startDate) queryParams.append('startDate', params.startDate);
      if (params?.endDate) queryParams.append('endDate', params.endDate);
      
      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
      
      return await safelyHandleResponse(
        api.get<{ success: boolean; data: Attendance[] }>(
          `${this.baseUrl}/employee/${employeeId}${queryString}`
        )
      );
    } catch (error) {
      console.error(`Error fetching attendance for employee ${employeeId}:`, error);
      return { data: null, error: 'Failed to fetch employee attendance' };
    }
  }

  /**
   * Create a new attendance record
   * @param attendanceData Attendance data
   * @returns Promise with created attendance record
   */
  async createAttendance(attendanceData: Partial<Attendance>) {
    try {
      return await safelyHandleResponse(
        api.post<{ success: boolean; data: Attendance }>(
          `${this.baseUrl}`, 
          attendanceData
        )
      );
    } catch (error) {
      console.error('Error creating attendance record:', error);
      return { data: null, error: 'Failed to create attendance record' };
    }
  }

  /**
   * Update an existing attendance record
   * @param id Attendance record ID
   * @param updates Updates to apply
   * @returns Promise with updated attendance record
   */
  async updateAttendance(id: number, updates: Partial<Attendance>) {
    try {
      return await safelyHandleResponse(
        api.put<{ success: boolean; data: Attendance }>(
          `${this.baseUrl}/${id}`, 
          updates
        )
      );
    } catch (error) {
      console.error(`Error updating attendance record ${id}:`, error);
      return { data: null, error: 'Failed to update attendance record' };
    }
  }

  /**
   * Delete an attendance record
   * @param id Attendance record ID
   * @returns Promise with success message
   */
  async deleteAttendance(id: number) {
    try {
      return await safelyHandleResponse(
        api.delete<{ success: boolean; message: string }>(
          `${this.baseUrl}/${id}`
        )
      );
    } catch (error) {
      console.error(`Error deleting attendance record ${id}:`, error);
      return { data: null, error: 'Failed to delete attendance record' };
    }
  }

  /**
   * Import multiple attendance records - MUST save to database
   * @param attendances Array of attendance records
   * @returns Promise with import results
   */
  async importAttendances(attendances: Partial<Attendance>[]) {
    // Ensure we're authenticated first
    await ensureAuthenticated();
    
    try {
      console.log('🏢 Professional Import: Saving to database only...');
      
      // STRICT DATABASE-ONLY IMPORT - No localStorage fallback
      const result = await safelyHandleResponse(
        api.post<{ 
          success: boolean; 
          message: string; 
          results: { 
            success: number; 
            failed: number; 
            skipped: number; 
            errors: any[];
            skippedEmployees: any[];
            summary: any;
          } 
        }>(
          `${this.baseUrl}/import`, 
          { attendances }
        )
      );
      
      // If API call succeeded, return the result
      if (!result.error && result.data) {
        console.log('✅ Successfully saved to database:', result.data.results?.summary);
        
        // Clear any temporary localStorage data since we have database success
        localStorage.removeItem('attendance_data_temp');
        
        return result;
      }
      
      // If API call failed with auth error, try login once more
      if (result.error && result.error.includes('Authentication required')) {
        console.log('🔐 Authentication required, retrying login...');
        
        if (await ensureAuthenticated()) {
          const retryResult = await safelyHandleResponse(
            api.post<any>(`${this.baseUrl}/import`, { attendances })
          );
          
          if (!retryResult.error && retryResult.data) {
            console.log('✅ Successfully saved to database after authentication');
            localStorage.removeItem('attendance_data_temp');
            return retryResult;
          }
        }
      }
      
      // If database save failed, return error - NO localStorage fallback
      console.error('❌ Database save failed - import rejected');
      return {
        data: null,
        error: `Database save failed: ${result.error || 'Unknown error'}. Please check server connection and try again.`
      };
      
    } catch (error) {
      console.error('❌ Critical error during import:', error);
      
      // Return error - NO localStorage fallback for professional system
      return { 
        data: null,
        error: `Import failed: ${(error as Error).message}. Data must be saved to database.`
      };
    }
  }

  /**
   * Get attendance summary
   * @param params Filter parameters
   * @returns Promise with attendance summary
   */
  async getAttendanceSummary(params: { employeeId?: number; startDate: string; endDate: string }) {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.employeeId) queryParams.append('employeeId', params.employeeId.toString());
      queryParams.append('startDate', params.startDate);
      queryParams.append('endDate', params.endDate);
      
      return await safelyHandleResponse(
        api.get<{ success: boolean; data: AttendanceSummary }>(
          `${this.baseUrl}/summary?${queryParams.toString()}`
        )
      );
    } catch (error) {
      console.error('Error fetching attendance summary:', error);
      return { data: null, error: 'Failed to fetch attendance summary' };
    }
  }

  /**
   * Clear all imported attendance data
   * @returns Promise with deletion result
   */
  async clearImportedAttendances() {
    try {
      const result = await safelyHandleResponse(
        api.delete<{ success: boolean; message: string; deletedCount: number }>(
          `${this.baseUrl}/clear-imported`
        )
      );
      
      // Also clear localStorage if successful
      if (result.data && !result.error) {
        localStorage.removeItem('attendance_data_temp');
        console.log('Cleared imported attendance data from database and localStorage');
      }
      
      return result;
    } catch (error) {
      console.error('Error clearing imported attendance data:', error);
      return { data: null, error: 'Failed to clear imported attendance data' };
    }
  }

  /**
   * Clear all attendance data (imported and manual)
   * @returns Promise with deletion result
   */
  async clearAllAttendances() {
    try {
      const result = await safelyHandleResponse(
        api.delete<{ success: boolean; message: string; deletedCount: number }>(
          `${this.baseUrl}/clear-all`
        )
      );
      
      // Also clear localStorage if successful
      if (result.data && !result.error) {
        localStorage.removeItem('attendance_data_temp');
        console.log('Cleared all attendance data from database and localStorage');
      }
      
      return result;
    } catch (error) {
      console.error('Error clearing all attendance data:', error);
      return { data: null, error: 'Failed to clear all attendance data' };
    }
  }
}

export default new AttendanceService(); 