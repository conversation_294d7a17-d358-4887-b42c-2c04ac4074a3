import { AppDataSource } from '../../config/database';
import { Employee } from '../entities/Employee';
import { Attendance } from '../entities/Attendance';
import { logger } from '../utils/logger';

export interface AttendanceValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export class AttendanceValidationService {
  /**
   * Comprehensive validation for attendance records
   * Ensures every attendance record belongs to an existing employee
   */
  static async validateAttendanceRecord(attendanceData: Partial<Attendance>): Promise<AttendanceValidationResult> {
    const result: AttendanceValidationResult = {
      isValid: true,
      errors: [],
      warnings: []
    };

    try {
      // 1. Validate required fields
      if (!attendanceData.employeeId) {
        result.errors.push('Employee ID is required');
        result.isValid = false;
      }

      if (!attendanceData.date) {
        result.errors.push('Date is required');
        result.isValid = false;
      }

      // If basic validation fails, return early
      if (!result.isValid) {
        return result;
      }

      // 2. Validate employee exists and is active
      const employee = await AppDataSource.getRepository(Employee).findOne({
        where: { id: attendanceData.employeeId },
        relations: ['job']
      });

      if (!employee) {
        result.errors.push(`Employee with ID ${attendanceData.employeeId} does not exist`);
        result.isValid = false;
        return result;
      }

      // 3. Check if employee is active
      if (employee.status && employee.status.toLowerCase() !== 'active') {
        result.errors.push(`Cannot create attendance for inactive employee (Status: ${employee.status})`);
        result.isValid = false;
      }

      // 4. Validate date format and business rules
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dateRegex.test(attendanceData.date!)) {
        result.errors.push('Date must be in YYYY-MM-DD format');
        result.isValid = false;
      }

      // 5. Check for duplicate attendance records
      if (attendanceData.employeeId && attendanceData.date) {
        const existingAttendance = await AppDataSource.getRepository(Attendance).findOne({
          where: { 
            employeeId: attendanceData.employeeId, 
            date: attendanceData.date 
          }
        });

        if (existingAttendance) {
          result.errors.push(`Attendance record already exists for employee ${attendanceData.employeeId} on ${attendanceData.date}`);
          result.isValid = false;
        }
      }

      // 6. Validate time format if provided
      if (attendanceData.checkInTime && !this.isValidTimeFormat(attendanceData.checkInTime)) {
        result.errors.push('Check-in time must be in HH:MM format');
        result.isValid = false;
      }

      if (attendanceData.checkOutTime && !this.isValidTimeFormat(attendanceData.checkOutTime)) {
        result.errors.push('Check-out time must be in HH:MM format');
        result.isValid = false;
      }

      // 7. Business rule validations
      if (attendanceData.checkInTime && attendanceData.checkOutTime) {
        const checkIn = new Date(`1970-01-01T${attendanceData.checkInTime}`);
        const checkOut = new Date(`1970-01-01T${attendanceData.checkOutTime}`);
        
        if (checkOut <= checkIn) {
          result.warnings.push('Check-out time should be after check-in time (unless overnight shift)');
        }
      }

      // 8. Validate future dates
      const attendanceDate = new Date(attendanceData.date!);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (attendanceDate > today) {
        result.warnings.push('Attendance date is in the future');
      }

      logger.info(`Attendance validation completed for employee ${attendanceData.employeeId}: ${result.isValid ? 'VALID' : 'INVALID'}`);
      
    } catch (error) {
      logger.error('Error during attendance validation:', error);
      result.errors.push('Internal validation error occurred');
      result.isValid = false;
    }

    return result;
  }

  /**
   * Validate multiple attendance records for bulk operations
   */
  static async validateBulkAttendanceRecords(attendanceRecords: Partial<Attendance>[]): Promise<{
    validRecords: Partial<Attendance>[];
    invalidRecords: Array<{ record: Partial<Attendance>; errors: string[] }>;
    summary: { total: number; valid: number; invalid: number; };
  }> {
    const validRecords: Partial<Attendance>[] = [];
    const invalidRecords: Array<{ record: Partial<Attendance>; errors: string[] }> = [];

    for (const record of attendanceRecords) {
      const validation = await this.validateAttendanceRecord(record);
      
      if (validation.isValid) {
        validRecords.push(record);
      } else {
        invalidRecords.push({
          record,
          errors: validation.errors
        });
      }
    }

    return {
      validRecords,
      invalidRecords,
      summary: {
        total: attendanceRecords.length,
        valid: validRecords.length,
        invalid: invalidRecords.length
      }
    };
  }

  /**
   * Enhanced validation specifically for import operations
   * Provides detailed reporting on employee existence issues
   */
  static async validateImportRecords(attendanceRecords: Partial<Attendance>[]): Promise<{
    validRecords: Partial<Attendance>[];
    invalidRecords: Array<{ record: Partial<Attendance>; errors: string[]; employeeId?: number }>;
    missingEmployees: Array<{ employeeId: number; recordCount: number; employeeName?: string }>;
    summary: { 
      total: number; 
      valid: number; 
      invalid: number; 
      missingEmployeeCount: number;
      duplicateCount: number;
    };
  }> {
    const validRecords: Partial<Attendance>[] = [];
    const invalidRecords: Array<{ record: Partial<Attendance>; errors: string[]; employeeId?: number }> = [];
    const missingEmployeeIds = new Set<number>();
    const missingEmployeeDetails = new Map<number, { count: number; name?: string }>();
    let duplicateCount = 0;

    logger.info(`Starting import validation for ${attendanceRecords.length} records`);

    // First pass: collect all unique employee IDs to batch check
    const employeeIds = new Set<number>();
    attendanceRecords.forEach(record => {
      if (record.employeeId) {
        employeeIds.add(record.employeeId);
      }
    });

    // Batch check employee existence
    const existingEmployees = await AppDataSource.getRepository(Employee)
      .createQueryBuilder('employee')
      .where('employee.id IN (:...ids)', { ids: Array.from(employeeIds) })
      .getMany();

    const existingEmployeeIds = new Set(existingEmployees.map(emp => emp.id));

    logger.info(`Found ${existingEmployees.length} existing employees out of ${employeeIds.size} unique employee IDs`);

    // Second pass: validate each record
    for (const record of attendanceRecords) {
      const errors: string[] = [];
      
      // Basic field validation
      if (!record.employeeId) {
        errors.push('Employee ID is required');
      }
      
      if (!record.date) {
        errors.push('Date is required');
      }

      // Employee existence check
      if (record.employeeId && !existingEmployeeIds.has(record.employeeId)) {
        errors.push(`Employee with ID ${record.employeeId} does not exist in the employee table`);
        missingEmployeeIds.add(record.employeeId);
        
        // Track missing employee details
        if (!missingEmployeeDetails.has(record.employeeId)) {
          missingEmployeeDetails.set(record.employeeId, { 
            count: 1, 
            name: record.employeeName 
          });
        } else {
          const existing = missingEmployeeDetails.get(record.employeeId)!;
          existing.count++;
          if (!existing.name && record.employeeName) {
            existing.name = record.employeeName;
          }
        }
      }

      // Date format validation
      if (record.date) {
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(record.date)) {
          errors.push('Date must be in YYYY-MM-DD format');
        }
      }

      // Check for duplicates within the import batch
      if (record.employeeId && record.date) {
        const duplicateInBatch = attendanceRecords.filter(r => 
          r.employeeId === record.employeeId && 
          r.date === record.date
        ).length > 1;
        
        if (duplicateInBatch) {
          errors.push(`Duplicate record found in import batch for employee ${record.employeeId} on ${record.date}`);
          duplicateCount++;
        }

        // Check for existing records in database
        const existingRecord = await AppDataSource.getRepository(Attendance).findOne({
          where: { 
            employeeId: record.employeeId, 
            date: record.date 
          }
        });

        if (existingRecord) {
          errors.push(`Attendance record already exists in database for employee ${record.employeeId} on ${record.date}`);
        }
      }

      // Time format validation
      if (record.checkInTime && !this.isValidTimeFormat(record.checkInTime)) {
        errors.push('Check-in time must be in HH:MM format');
      }

      if (record.checkOutTime && !this.isValidTimeFormat(record.checkOutTime)) {
        errors.push('Check-out time must be in HH:MM format');
      }

      // Categorize record
      if (errors.length === 0) {
        validRecords.push(record);
      } else {
        invalidRecords.push({
          record,
          errors,
          employeeId: record.employeeId
        });
      }
    }

    // Convert missing employee details to array
    const missingEmployees = Array.from(missingEmployeeDetails.entries()).map(([employeeId, details]) => ({
      employeeId,
      recordCount: details.count,
      employeeName: details.name
    }));

    const summary = {
      total: attendanceRecords.length,
      valid: validRecords.length,
      invalid: invalidRecords.length,
      missingEmployeeCount: missingEmployeeIds.size,
      duplicateCount: Math.floor(duplicateCount / 2) // Divide by 2 since each duplicate is counted twice
    };

    logger.info(`Import validation completed: ${summary.valid} valid, ${summary.invalid} invalid, ${summary.missingEmployeeCount} missing employees`);

    return {
      validRecords,
      invalidRecords,
      missingEmployees,
      summary
    };
  }

  /**
   * Validate that an employee exists and is eligible for attendance tracking
   */
  static async validateEmployeeEligibility(employeeId: number): Promise<{
    isEligible: boolean;
    employee?: Employee;
    reason?: string;
  }> {
    try {
      const employee = await AppDataSource.getRepository(Employee).findOne({
        where: { id: employeeId },
        relations: ['job']
      });

      if (!employee) {
        return {
          isEligible: false,
          reason: 'Employee not found'
        };
      }

      if (employee.status && employee.status.toLowerCase() !== 'active') {
        return {
          isEligible: false,
          employee,
          reason: `Employee is not active (Status: ${employee.status})`
        };
      }

      return {
        isEligible: true,
        employee
      };
    } catch (error) {
      logger.error(`Error validating employee eligibility for ID ${employeeId}:`, error);
      return {
        isEligible: false,
        reason: 'Error checking employee eligibility'
      };
    }
  }

  /**
   * Validate time format (HH:MM)
   */
  private static isValidTimeFormat(time: string): boolean {
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return timeRegex.test(time);
  }

  /**
   * Check for orphaned attendance records (attendance without valid employee)
   */
  static async findOrphanedAttendanceRecords(): Promise<Attendance[]> {
    try {
      const orphanedRecords = await AppDataSource.getRepository(Attendance)
        .createQueryBuilder('attendance')
        .leftJoin('attendance.employee', 'employee')
        .where('employee.id IS NULL')
        .getMany();

      if (orphanedRecords.length > 0) {
        logger.warn(`Found ${orphanedRecords.length} orphaned attendance records`);
      }

      return orphanedRecords;
    } catch (error) {
      logger.error('Error finding orphaned attendance records:', error);
      return [];
    }
  }

  /**
   * Clean up orphaned attendance records
   */
  static async cleanupOrphanedRecords(): Promise<{ deletedCount: number; errors: string[] }> {
    try {
      const orphanedRecords = await this.findOrphanedAttendanceRecords();
      const errors: string[] = [];
      let deletedCount = 0;

      for (const record of orphanedRecords) {
        try {
          await AppDataSource.getRepository(Attendance).remove(record);
          deletedCount++;
          logger.info(`Deleted orphaned attendance record ID: ${record.id}`);
        } catch (error) {
          const errorMsg = `Failed to delete orphaned record ID ${record.id}: ${error}`;
          errors.push(errorMsg);
          logger.error(errorMsg);
        }
      }

      return { deletedCount, errors };
    } catch (error) {
      logger.error('Error during orphaned records cleanup:', error);
      return { deletedCount: 0, errors: ['Cleanup operation failed'] };
    }
  }
} 