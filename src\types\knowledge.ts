import { KnowledgeStatus } from '../entities/KnowledgeBase';

export interface KnowledgeArticle {
  id: string;
  title: string;
  summary?: string;
  content: string;
  status: KnowledgeStatus;
  viewCount: number;
  isFeatured: boolean;
  category: {
    id: string;
    name: string;
    slug: string;
  };
  createdBy: {
    id: string;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
  tags: Array<{
    id: string;
    name: string;
  }>;
}

export interface KnowledgeCategory {
  id: string;
  name: string;
  description?: string;
  slug: string;
  displayOrder: number;
  parentId?: string;
}

export interface UploadedImage {
  fileName: string;
  fileUrl: string;
  fileType: string;
  size: number;
} 