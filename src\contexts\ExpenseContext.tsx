import React, { createContext, useContext, useState, useEffect } from 'react';
import { Expense, BudgetItem } from '../types/expense';
import { ExpenseService } from '../services/ExpenseService';
import { toast } from 'react-hot-toast';

interface ExpenseContextType {
  expenses: Expense[];
  budgetItems: BudgetItem[];
  loading: boolean;
  error: string | null;
  addExpense: (data: Partial<Expense>) => Promise<void>;
  updateExpense: (id: string, data: Partial<Expense>) => Promise<void>;
  deleteExpense: (id: string) => Promise<void>;
  updateBudgetItem: (id: string, data: Partial<BudgetItem>) => Promise<void>;
  exportExpenses: (format: 'csv' | 'pdf') => Promise<void>;
  refreshData: () => Promise<void>;
}

const ExpenseContext = createContext<ExpenseContextType | undefined>(undefined);

export function ExpenseProvider({ children }: { children: React.ReactNode }) {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [budgetItems, setBudgetItems] = useState<BudgetItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refreshData = async () => {
    setLoading(true);
    setError(null);
    try {
      const [expensesData, budgetData] = await Promise.all([
        ExpenseService.getExpenses(),
        ExpenseService.getBudgetItems()
      ]);
      setExpenses(expensesData);
      setBudgetItems(budgetData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch data';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    refreshData();
  }, []);

  const addExpense = async (data: Partial<Expense>) => {
    try {
      const newExpense = await ExpenseService.createExpense(data);
      if (newExpense) {
        setExpenses(prev => [newExpense, ...prev]);
        toast.success('Expense added successfully');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add expense';
      toast.error(errorMessage);
      throw err;
    }
  };

  const updateExpense = async (id: string, data: Partial<Expense>) => {
    try {
      const updatedExpense = await ExpenseService.updateExpense(id, data);
      if (updatedExpense) {
        setExpenses(prev => prev.map(expense => 
          expense.id === id ? updatedExpense : expense
        ));
        toast.success('Expense updated successfully');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update expense';
      toast.error(errorMessage);
      throw err;
    }
  };

  const deleteExpense = async (id: string) => {
    try {
      const success = await ExpenseService.deleteExpense(id);
      if (success) {
        setExpenses(prev => prev.filter(expense => expense.id !== id));
        toast.success('Expense deleted successfully');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete expense';
      toast.error(errorMessage);
      throw err;
    }
  };

  const updateBudgetItem = async (id: string, data: Partial<BudgetItem>) => {
    try {
      const updatedBudgetItem = await ExpenseService.updateBudgetItem(id, data);
      if (updatedBudgetItem) {
        setBudgetItems(prev => prev.map(item => 
          item.id === id ? updatedBudgetItem : item
        ));
        toast.success('Budget item updated successfully');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update budget item';
      toast.error(errorMessage);
      throw err;
    }
  };

  const exportExpenses = async (format: 'csv' | 'pdf') => {
    try {
      const blob = await ExpenseService.exportExpenses(format);
      if (blob) {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `expenses-${new Date().toISOString().split('T')[0]}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        toast.success(`Expenses exported as ${format.toUpperCase()}`);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to export expenses';
      toast.error(errorMessage);
      throw err;
    }
  };

  const value = {
    expenses,
    budgetItems,
    loading,
    error,
    addExpense,
    updateExpense,
    deleteExpense,
    updateBudgetItem,
    exportExpenses,
    refreshData
  };

  return (
    <ExpenseContext.Provider value={value}>
      {children}
    </ExpenseContext.Provider>
  );
}

export function useExpense() {
  const context = useContext(ExpenseContext);
  if (context === undefined) {
    throw new Error('useExpense must be used within an ExpenseProvider');
  }
  return context;
} 