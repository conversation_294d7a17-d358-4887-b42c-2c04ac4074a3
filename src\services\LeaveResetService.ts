import { LeaveResetConfiguration, LeaveBalance } from '../types/attendance';

export class LeaveResetService {
  private static instance: LeaveResetService;

  public static getInstance(): LeaveResetService {
    if (!LeaveResetService.instance) {
      LeaveResetService.instance = new LeaveResetService();
    }
    return LeaveResetService.instance;
  }

  /**
   * Get reset configurations
   */
  async getResetConfigurations(): Promise<LeaveResetConfiguration[]> {
    // TODO: Fetch from database - no hardcoded data
    // For now return empty array, will be populated through admin interface
    return [];
  }

  /**
   * Update reset configuration
   */
  async updateResetConfiguration(config: LeaveResetConfiguration): Promise<LeaveResetConfiguration> {
    // Validate configuration
    this.validateResetConfiguration(config);

    // Mock update - in real implementation, save to database
    const updatedConfig = {
      ...config,
      updatedAt: new Date().toISOString()
    };

    console.log('Updated reset configuration:', updatedConfig);
    return updatedConfig;
  }

  /**
   * Process automatic leave reset for all employees
   */
  async processAutomaticReset(resetDate?: Date): Promise<{
    processedEmployees: number;
    totalResets: number;
    carryForwards: number;
    errors: string[];
  }> {
    const processDate = resetDate || new Date();
    const configurations = await this.getResetConfigurations();
    const activeConfigs = configurations.filter(config => 
      config.isActive && config.autoResetEnabled && this.shouldProcessReset(config, processDate)
    );

    const result = {
      processedEmployees: 0,
      totalResets: 0,
      carryForwards: 0,
      errors: [] as string[]
    };

    // Mock employee list - in real implementation, get from database
    const employees = await this.getAllActiveEmployees();

    for (const employee of employees) {
      try {
        for (const config of activeConfigs) {
          await this.processEmployeeReset(employee.id, config, processDate);
          result.totalResets++;

          if (config.carryForwardEnabled) {
            const carryForward = await this.processCarryForward(employee.id, config, processDate);
            if (carryForward > 0) {
              result.carryForwards++;
            }
          }
        }
        result.processedEmployees++;
      } catch (error) {
        result.errors.push(`Error processing employee ${employee.id}: ${error}`);
      }
    }

    return result;
  }

  /**
   * Process reset for a specific employee and leave type
   */
  async processEmployeeReset(
    employeeId: number, 
    config: LeaveResetConfiguration, 
    resetDate: Date
  ): Promise<void> {
    const currentYear = resetDate.getFullYear();
    const previousYear = currentYear - 1;

    // Get current balance
    const currentBalance = await this.getEmployeeLeaveBalance(employeeId, config.leaveType, previousYear);
    
    if (!currentBalance) {
      console.log(`No balance found for employee ${employeeId}, leave type ${config.leaveType}`);
      return;
    }

    // Calculate carry forward amount
    let carryForwardAmount = 0;
    if (config.carryForwardEnabled && currentBalance.remaining > 0) {
      carryForwardAmount = Math.min(currentBalance.remaining, config.carryForwardLimit);
    }

    // Create new balance for current year
    const newBalance = {
      employeeId,
      leaveType: config.leaveType,
      year: currentYear,
      totalAllocated: await this.getEmployeeAllocation(employeeId, config.leaveType, currentYear),
      used: 0,
      pending: 0,
      carriedForward: carryForwardAmount,
      lapsed: currentBalance.remaining - carryForwardAmount,
      expiryDate: config.carryForwardEnabled ? this.calculateCarryForwardExpiry(resetDate, config) : undefined,
      isActive: true
    };

    // Save new balance (mock implementation)
    console.log('Creating new leave balance:', newBalance);

    // Mark previous year balance as inactive
    await this.deactivateBalance(currentBalance.id!);
  }

  /**
   * Process carry forward for an employee
   */
  private async processCarryForward(
    employeeId: number, 
    config: LeaveResetConfiguration, 
    resetDate: Date
  ): Promise<number> {
    const previousYear = resetDate.getFullYear() - 1;
    const balance = await this.getEmployeeLeaveBalance(employeeId, config.leaveType, previousYear);
    
    if (!balance || balance.remaining <= 0) {
      return 0;
    }

    const carryForwardAmount = Math.min(balance.remaining, config.carryForwardLimit);
    
    if (carryForwardAmount > 0) {
      console.log(`Carrying forward ${carryForwardAmount} days for employee ${employeeId}`);
    }

    return carryForwardAmount;
  }

  /**
   * Check if reset should be processed for a configuration
   */
  private shouldProcessReset(config: LeaveResetConfiguration, currentDate: Date): boolean {
    const [month, day] = config.resetDate.split('-').map(Number);
    const resetDate = new Date(currentDate.getFullYear(), month - 1, day);
    
    // Check if today is the reset date
    return currentDate.toDateString() === resetDate.toDateString();
  }

  /**
   * Calculate carry forward expiry date
   */
  private calculateCarryForwardExpiry(resetDate: Date, config: LeaveResetConfiguration): string {
    const expiryDate = new Date(resetDate);
    expiryDate.setMonth(expiryDate.getMonth() + config.carryForwardExpiryMonths);
    return expiryDate.toISOString().split('T')[0];
  }

  /**
   * Get employee allocation for a leave type
   */
  private async getEmployeeAllocation(employeeId: number, leaveType: string, year: number): Promise<number> {
    try {
      // Use the dynamic leave allocation service
      const { leaveAllocationService } = await import('./LeaveAllocationService');
      const allocation = await leaveAllocationService.calculateEmployeeAllocation(employeeId, leaveType, year);
      
      console.log(`Dynamic allocation for employee ${employeeId}, leave type ${leaveType}, year ${year}: ${allocation.totalDays} days (${allocation.source})`);
      console.log(`Calculation: ${allocation.calculation}`);
      
      return allocation.totalDays;
    } catch (error) {
      // NO FALLBACK TO DEFAULTS - return 0 if dynamic calculation fails
      console.log(`Dynamic allocation failed for employee ${employeeId}, leave type ${leaveType}. Returning 0 days. Please configure leave policy.`);
      
      return 0;
    }
  }

  /**
   * Get employee leave balance for a specific year
   */
  private async getEmployeeLeaveBalance(employeeId: number, leaveType: string, year: number): Promise<any> {
    // TODO: Fetch from database
    // Mock implementation
    console.log(`Getting balance for employee ${employeeId}, leave type ${leaveType}, year ${year}`);
    
    return {
      id: 1,
      employeeId,
      leaveType,
      year,
      totalAllocated: 21,
      used: 5,
      pending: 2,
      remaining: 14,
      carriedForward: 0,
      lapsed: 0,
      isActive: true
    };
  }

  /**
   * Get all active employees
   */
  private async getAllActiveEmployees(): Promise<{ id: number; name: string }[]> {
    // Mock implementation - in real app, query User entity
    return [
      { id: 1, name: 'John Doe' },
      { id: 2, name: 'Jane Smith' }
    ];
  }

  /**
   * Deactivate a balance record
   */
  private async deactivateBalance(balanceId: number): Promise<void> {
    console.log(`Deactivating balance record ${balanceId}`);
  }

  /**
   * Validate reset configuration
   */
  private validateResetConfiguration(config: LeaveResetConfiguration): void {
    if (!config.leaveType) {
      throw new Error('Leave type is required');
    }

    if (!config.resetDate || !/^\d{2}-\d{2}$/.test(config.resetDate)) {
      throw new Error('Reset date must be in MM-DD format');
    }

    if (config.carryForwardEnabled && config.carryForwardLimit < 0) {
      throw new Error('Carry forward limit cannot be negative');
    }

    if (config.carryForwardEnabled && config.carryForwardExpiryMonths < 1) {
      throw new Error('Carry forward expiry must be at least 1 month');
    }
  }

  /**
   * Get upcoming resets (for notifications)
   */
  async getUpcomingResets(daysAhead: number = 30): Promise<{
    leaveType: string;
    resetDate: string;
    affectedEmployees: number;
    carryForwardEnabled: boolean;
  }[]> {
    const configurations = await this.getResetConfigurations();
    const currentDate = new Date();
    const endDate = new Date();
    endDate.setDate(currentDate.getDate() + daysAhead);

    const upcomingResets = configurations
      .filter(config => config.isActive)
      .map(config => {
        const [month, day] = config.resetDate.split('-').map(Number);
        const resetDate = new Date(currentDate.getFullYear(), month - 1, day);
        
        // If reset date has passed this year, check next year
        if (resetDate < currentDate) {
          resetDate.setFullYear(currentDate.getFullYear() + 1);
        }

        return {
          leaveType: config.leaveType,
          resetDate: resetDate.toISOString().split('T')[0],
          affectedEmployees: 0, // TODO: Calculate from database
          carryForwardEnabled: config.carryForwardEnabled,
          daysUntilReset: Math.ceil((resetDate.getTime() - currentDate.getTime()) / (1000 * 60 * 60 * 24))
        };
      })
      .filter(reset => reset.daysUntilReset <= daysAhead)
      .sort((a, b) => a.daysUntilReset - b.daysUntilReset);

    return upcomingResets.map(({ daysUntilReset, ...reset }) => reset);
  }
}

export const leaveResetService = LeaveResetService.getInstance(); 