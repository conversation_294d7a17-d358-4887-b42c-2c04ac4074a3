const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');

// Database connection config
const DB_HOST = process.env.DB_HOST || 'localhost';
const DB_PORT = process.env.DB_PORT || '3306';
const DB_USERNAME = process.env.DB_USERNAME || 'root';
const DB_PASSWORD = process.env.DB_PASSWORD || 'root';
const DB_NAME = 'ims_db';

async function fixAdminCredentials() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: DB_HOST,
      port: parseInt(DB_PORT),
      user: DB_USERNAME,
      password: DB_PASSWORD,
      database: DB_NAME
    });
    
    console.log('Connected to MySQL server');
    
    // Generate hash for admin123
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('admin123', salt);
    
    console.log(`New password hash generated: ${hashedPassword}`);
    
    // Update the admin user's password
    const [result] = await connection.query(`
      UPDATE users 
      SET password = ?, role = 'IT_ADMIN', isActive = 1 
      WHERE email = '<EMAIL>'
    `, [hashedPassword]);
    
    if (result.affectedRows > 0) {
      console.log('Admin credentials updated successfully');
    } else {
      console.log('Admin user not found. Creating a new admin user...');
      
      // Create a new admin user if it doesn't exist
      await connection.query(`
        INSERT INTO users (name, email, password, role, department, isActive, permissions)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        'Administrator',
        '<EMAIL>',
        hashedPassword,
        'IT_ADMIN',
        'IT',
        1,
        JSON.stringify({
          canCreateTickets: true,
          canCreateTicketsForOthers: true,
          canEditTickets: true,
          canDeleteTickets: true,
          canCloseTickets: true,
          canLockTickets: true,
          canAssignTickets: true,
          canEscalateTickets: true,
          canViewAllTickets: true
        })
      ]);
      
      console.log('New admin user created successfully');
    }
    
    // Display the updated admin user details (exclude the password)
    const [adminUser] = await connection.query(`
      SELECT id, name, email, role, department, isActive FROM users WHERE email = '<EMAIL>'
    `);
    
    console.log('Admin user details:', adminUser[0]);
    
  } catch (error) {
    console.error('Error fixing admin credentials:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

// Run the script
fixAdminCredentials(); 