import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn
} from 'typeorm';

@Entity('vendors')
export class Vendor {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar' })
  vendorName: string;

  @Column({ type: 'varchar' })
  companyName: string;

  @Column({ type: 'varchar', nullable: true })
  website: string;

  @Column({
    type: 'enum',
    enum: [
      'HARDWARE_SUPPLIER',
      'PRINTER_PHOTOCOPIER_SUPPLIER',
      'SERVER_STORAGE_SUPPLIER',
      'CCTV_SURVEILLANCE_SUPPLIER',
      'POWER_UPS_SUPPLIER',
      'IT_ACCESSORIES_SUPPLIER',
      'ISP',
      'NETWORKING_EQUIPMENT_SUPPLIER',
      'FIREWALL_SECURITY_VENDOR',
      'VPN_SECURE_ACCESS_PROVIDER',
      'CLOUD_SERVICE_PROVIDER',
      'DOMAIN_HOSTING_PROVIDER',
      'BACKUP_DISASTER_RECOVERY',
      'ERP_VENDOR',
      'CRM_VENDOR',
      'HRMS_PAYROLL_VENDOR',
      'POS_RETAIL_SOFTWARE',
      'FINANCIAL_ACCOUNTING_SOFTWARE',
      'PBX_VOIP_PROVIDER',
      'CALL_CENTER_SOFTWARE',
      'TELECOM_SERVICES',
      'CYBERSECURITY_VENDOR',
      'ANTIVIRUS_ENDPOINT_PROTECTION',
      'IDENTITY_ACCESS_MANAGEMENT',
      'IT_CONSULTANCY',
      'SOFTWARE_DEVELOPMENT',
      'BLOCKCHAIN_WEB3'
    ]
  })
  vendorType: string;

  @Column({
    type: 'enum',
    enum: ['ACTIVE', 'INACTIVE', 'BLACKLISTED', 'PENDING_REVIEW'],
    default: 'ACTIVE'
  })
  status: string;

  @Column('json')
  contactPerson: {
    name: string;
    title: string;
    email: string;
    phone: string;
    department?: string;
  };

  @Column('json', { nullable: true })
  alternativeContacts: {
    name: string;
    title: string;
    email: string;
    phone: string;
    department?: string;
  }[];

  @Column('json')
  address: {
    street: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
    telephone?: string;
  };

  @Column('json')
  contract: {
    startDate: string;
    expiryDate: string;
    renewalReminder?: boolean;
    autoRenewal?: boolean;
    terminationNotice: number;
  };

  @Column('json')
  performance: {
    rating: number;
    responseTime: number;
    deliveryScore: number;
    qualityScore: number;
  };

  @Column('json')
  financial: {
    spending: {
      current: {
        value: number;
        currency: string;
      };
      trend: number;
    };
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 