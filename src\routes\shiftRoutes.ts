import { Router } from 'express';
import { DataSource } from 'typeorm';
import { ShiftController } from '../controllers/ShiftController';

export function createShiftRoutes(dataSource: DataSource): Router {
  const router = Router();
  const shiftController = new ShiftController(dataSource);

  // Shift management routes
  router.post('/shifts', shiftController.createShift);
  router.get('/shifts', shiftController.getAllShifts);
  router.get('/shifts/active', shiftController.getActiveShifts);
  router.get('/shifts/statistics', shiftController.getShiftStatistics);
  router.get('/shifts/:id', shiftController.getShiftById);
  router.put('/shifts/:id', shiftController.updateShift);
  router.delete('/shifts/:id', shiftController.deleteShift);
  router.patch('/shifts/:id/toggle-status', shiftController.toggleShiftStatus);

  // Shift assignment routes
  router.post('/shift-assignments', shiftController.assignShiftToEmployee);
  router.get('/shift-assignments/employees/:employeeId', shiftController.getEmployeeShiftAssignments);
  router.get('/shift-assignments/employees/:employeeId/current', shiftController.getCurrentEmployeeShift);
  router.get('/shift-assignments/shifts/:id', shiftController.getShiftAssignments);
  router.patch('/shift-assignments/:id/end', shiftController.endShiftAssignment);
  router.delete('/shift-assignments/:id', shiftController.deleteShiftAssignment);

  // Utility routes
  router.get('/employees/without-shift', shiftController.getEmployeesWithoutShift);

  return router;
} 