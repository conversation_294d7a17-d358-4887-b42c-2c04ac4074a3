import React, { useState, useMemo, useRef, useEffect } from 'react';
import { 
  Activity, CheckCircle, XCircle, AlertTriangle, 
  ZoomIn, ZoomOut, ChevronLeft, ChevronRight,
  Clock, Info, Filter 
} from 'lucide-react';
import { SystemLog } from './types';
import { formatSystemDate } from './utils/dateFormat';
import { 
  isSuccessfulLogin, 
  isFailedLogin, 
  isLogout 
} from './utils/loginTracker';

interface UserActivityTimelineProps {
  logs: SystemLog[];
  username: string;
  onViewLogDetails: (log: SystemLog) => void;
}

// Define date range for zooming
interface DateRange {
  start: Date;
  end: Date;
}

export const UserActivityTimeline: React.FC<UserActivityTimelineProps> = ({ 
  logs, 
  username, 
  onViewLogDetails 
}) => {
  // Timeline container ref for measurements
  const timelineContainerRef = useRef<HTMLDivElement>(null);
  
  // Filter user logs
  const userLogs = useMemo(() => {
    return logs.filter(log => log.user === username);
  }, [logs, username]);
  
  // Sort logs by timestamp
  const sortedLogs = useMemo(() => {
    return [...userLogs].sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
  }, [userLogs]);
  
  // Calculate absolute time range from all logs
  const absoluteTimeRange = useMemo((): DateRange | null => {
    if (sortedLogs.length === 0) return null;
    
    const firstLog = new Date(sortedLogs[0].timestamp);
    const lastLog = new Date(sortedLogs[sortedLogs.length - 1].timestamp);
    
    // Add some padding to the time range
    const paddingMs = (lastLog.getTime() - firstLog.getTime()) * 0.05; // 5% padding
    
    return {
      start: new Date(firstLog.getTime() - paddingMs),
      end: new Date(lastLog.getTime() + paddingMs)
    };
  }, [sortedLogs]);
  
  // Set initial zoom level to show all logs
  const [currentRange, setCurrentRange] = useState<DateRange | null>(absoluteTimeRange);
  const [zoomLevel, setZoomLevel] = useState<number>(1); // 1 = all logs, higher values = zoomed in
  
  // Update current range when absolute range changes
  useEffect(() => {
    setCurrentRange(absoluteTimeRange);
  }, [absoluteTimeRange]);
  
  // Filter for event types
  const [selectedTypes, setSelectedTypes] = useState<{
    login: boolean;
    logout: boolean;
    error: boolean;
    warning: boolean;
    info: boolean;
    success: boolean;
  }>({
    login: true,
    logout: true,
    error: true,
    warning: true,
    info: true,
    success: true
  });
  
  // Toggle filter for a specific type
  const toggleTypeFilter = (type: keyof typeof selectedTypes) => {
    setSelectedTypes(prev => ({
      ...prev,
      [type]: !prev[type]
    }));
  };
  
  // Filter logs based on selected types
  const filteredLogs = useMemo(() => {
    return sortedLogs.filter(log => {
      // Filter by log type
      if (log.type === 'error' && !selectedTypes.error) return false;
      if (log.type === 'warning' && !selectedTypes.warning) return false;
      if (log.type === 'info' && !selectedTypes.info) return false;
      if (log.type === 'success' && !selectedTypes.success) return false;
      
      // Filter login/logout events
      if (isSuccessfulLogin(log) && !selectedTypes.login) return false;
      if (isLogout(log) && !selectedTypes.logout) return false;
      
      // Include if it passes all filters
      return true;
    });
  }, [sortedLogs, selectedTypes]);
  
  // Filter logs that fall within the current time range
  const visibleLogs = useMemo(() => {
    if (!currentRange) return filteredLogs;
    
    return filteredLogs.filter(log => {
      const logTime = new Date(log.timestamp).getTime();
      return logTime >= currentRange.start.getTime() && logTime <= currentRange.end.getTime();
    });
  }, [filteredLogs, currentRange]);
  
  // Zoom in - reduce time range by 50%
  const zoomIn = () => {
    if (!currentRange) return;
    
    const midpoint = (currentRange.start.getTime() + currentRange.end.getTime()) / 2;
    const newRangeHalfSize = (currentRange.end.getTime() - currentRange.start.getTime()) / 4; // Quarter of current range
    
    setCurrentRange({
      start: new Date(midpoint - newRangeHalfSize),
      end: new Date(midpoint + newRangeHalfSize)
    });
    
    setZoomLevel(prev => prev * 2);
  };
  
  // Zoom out - increase time range by 100%
  const zoomOut = () => {
    if (!currentRange || !absoluteTimeRange) return;
    
    const midpoint = (currentRange.start.getTime() + currentRange.end.getTime()) / 2;
    const newRangeHalfSize = (currentRange.end.getTime() - currentRange.start.getTime()); // Double the current range
    
    // Calculate new range, but don't go beyond absolute range
    const newStart = Math.max(
      absoluteTimeRange.start.getTime(),
      midpoint - newRangeHalfSize
    );
    const newEnd = Math.min(
      absoluteTimeRange.end.getTime(),
      midpoint + newRangeHalfSize
    );
    
    setCurrentRange({
      start: new Date(newStart),
      end: new Date(newEnd)
    });
    
    setZoomLevel(prev => Math.max(1, prev / 2));
  };
  
  // Reset zoom to show all logs
  const resetZoom = () => {
    setCurrentRange(absoluteTimeRange);
    setZoomLevel(1);
  };
  
  // Pan left/right
  const pan = (direction: 'left' | 'right') => {
    if (!currentRange) return;
    
    const rangeSize = currentRange.end.getTime() - currentRange.start.getTime();
    const panAmount = rangeSize * 0.25; // Pan by 25% of the visible range
    
    if (direction === 'left') {
      setCurrentRange({
        start: new Date(currentRange.start.getTime() - panAmount),
        end: new Date(currentRange.end.getTime() - panAmount)
      });
    } else {
      setCurrentRange({
        start: new Date(currentRange.start.getTime() + panAmount),
        end: new Date(currentRange.end.getTime() + panAmount)
      });
    }
  };
  
  // Calculate position on timeline for a log
  const getTimelinePosition = (log: SystemLog): string => {
    if (!currentRange) return '0%';
    
    const logTime = new Date(log.timestamp).getTime();
    const rangeStart = currentRange.start.getTime();
    const rangeEnd = currentRange.end.getTime();
    const rangeSize = rangeEnd - rangeStart;
    
    // Calculate position as percentage
    const position = ((logTime - rangeStart) / rangeSize) * 100;
    
    // Clamp to visible range
    return `${Math.max(0, Math.min(100, position))}%`;
  };
  
  // Get icon and color for log type
  const getEventIconAndColor = (log: SystemLog): { icon: React.ReactNode; color: string; bgColor: string } => {
    if (isSuccessfulLogin(log)) {
      return { 
        icon: <Activity className="h-4 w-4" />, 
        color: 'text-green-600',
        bgColor: 'bg-green-100 dark:bg-green-900/30'
      };
    }
    
    if (isFailedLogin(log)) {
      return { 
        icon: <XCircle className="h-4 w-4" />, 
        color: 'text-rose-600',
        bgColor: 'bg-rose-100 dark:bg-rose-900/30'
      };
    }
    
    if (isLogout(log)) {
      return { 
        icon: <Clock className="h-4 w-4" />, 
        color: 'text-blue-600',
        bgColor: 'bg-blue-100 dark:bg-blue-900/30'
      };
    }
    
    switch (log.type) {
      case 'success':
        return { 
          icon: <CheckCircle className="h-4 w-4" />, 
          color: 'text-green-600',
          bgColor: 'bg-green-100 dark:bg-green-900/30'
        };
      case 'error':
        return { 
          icon: <XCircle className="h-4 w-4" />, 
          color: 'text-red-600',
          bgColor: 'bg-red-100 dark:bg-red-900/30'
        };
      case 'warning':
        return { 
          icon: <AlertTriangle className="h-4 w-4" />, 
          color: 'text-amber-600',
          bgColor: 'bg-amber-100 dark:bg-amber-900/30'
        };
      case 'info':
      default:
        return { 
          icon: <Info className="h-4 w-4" />, 
          color: 'text-blue-600',
          bgColor: 'bg-blue-100 dark:bg-blue-900/30'
        };
    }
  };
  
  // Format date for time markers
  const formatDate = (date: Date): string => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  };
  
  // Generate time markers for the timeline
  const timeMarkers = useMemo(() => {
    if (!currentRange) return [];
    
    const markers = [];
    const rangeSize = currentRange.end.getTime() - currentRange.start.getTime();
    
    // Determine number of markers based on zoom level
    const numMarkers = Math.min(10, Math.max(3, Math.ceil(zoomLevel * 5)));
    
    for (let i = 0; i <= numMarkers; i++) {
      const time = new Date(currentRange.start.getTime() + (rangeSize * i / numMarkers));
      markers.push({
        time,
        position: `${(i / numMarkers) * 100}%`
      });
    }
    
    return markers;
  }, [currentRange, zoomLevel]);
  
  // Handle empty state
  if (sortedLogs.length === 0) {
    return (
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-8 text-center">
        <Activity className="h-12 w-12 mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-200">No activity data</h3>
        <p className="text-gray-500 dark:text-gray-400 mt-2">
          There are no logged activities for this user yet.
        </p>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-3">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center gap-2">
          <Activity className="h-5 w-5 text-blue-500" />
          Activity Timeline
        </h3>
        
        {/* Type filters */}
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => toggleTypeFilter('login')}
            className={`text-xs px-2 py-1 rounded-full flex items-center gap-1 transition-colors ${
              selectedTypes.login 
                ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' 
                : 'bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400'
            }`}
          >
            <Activity className="h-3 w-3" />
            Login
          </button>
          <button
            onClick={() => toggleTypeFilter('logout')}
            className={`text-xs px-2 py-1 rounded-full flex items-center gap-1 transition-colors ${
              selectedTypes.logout 
                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' 
                : 'bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400'
            }`}
          >
            <Clock className="h-3 w-3" />
            Logout
          </button>
          <button
            onClick={() => toggleTypeFilter('error')}
            className={`text-xs px-2 py-1 rounded-full flex items-center gap-1 transition-colors ${
              selectedTypes.error 
                ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' 
                : 'bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400'
            }`}
          >
            <XCircle className="h-3 w-3" />
            Errors
          </button>
          <button
            onClick={() => toggleTypeFilter('warning')}
            className={`text-xs px-2 py-1 rounded-full flex items-center gap-1 transition-colors ${
              selectedTypes.warning 
                ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300' 
                : 'bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400'
            }`}
          >
            <AlertTriangle className="h-3 w-3" />
            Warnings
          </button>
          <button
            onClick={() => toggleTypeFilter('success')}
            className={`text-xs px-2 py-1 rounded-full flex items-center gap-1 transition-colors ${
              selectedTypes.success 
                ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' 
                : 'bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400'
            }`}
          >
            <CheckCircle className="h-3 w-3" />
            Success
          </button>
          <button
            onClick={() => toggleTypeFilter('info')}
            className={`text-xs px-2 py-1 rounded-full flex items-center gap-1 transition-colors ${
              selectedTypes.info 
                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' 
                : 'bg-gray-100 text-gray-500 dark:bg-gray-800 dark:text-gray-400'
            }`}
          >
            <Info className="h-3 w-3" />
            Info
          </button>
        </div>
      </div>
      
      {/* Timeline controls */}
      <div className="flex items-center justify-between mb-1 bg-gray-50 dark:bg-gray-800 p-2 rounded-md">
        <div className="flex items-center gap-2">
          <button
            onClick={() => pan('left')}
            className="p-1.5 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
            title="Pan left"
          >
            <ChevronLeft className="h-5 w-5" />
          </button>
          <button
            onClick={() => pan('right')}
            className="p-1.5 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
            title="Pan right"
          >
            <ChevronRight className="h-5 w-5" />
          </button>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {visibleLogs.length} event{visibleLogs.length !== 1 ? 's' : ''} in view
          </span>
          <button
            onClick={zoomIn}
            className="p-1.5 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
            title="Zoom in"
            disabled={zoomLevel >= 16}
          >
            <ZoomIn className="h-5 w-5" />
          </button>
          <button
            onClick={zoomOut}
            className="p-1.5 rounded-full text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
            title="Zoom out"
            disabled={zoomLevel <= 1}
          >
            <ZoomOut className="h-5 w-5" />
          </button>
          <button
            onClick={resetZoom}
            className="text-xs px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded"
            disabled={zoomLevel === 1}
          >
            Reset
          </button>
        </div>
      </div>
      
      {/* Timeline visualization */}
      <div className="relative" ref={timelineContainerRef}>
        {/* Time indicators */}
        <div className="absolute left-0 right-0 top-0 h-6 flex text-xs text-gray-500 dark:text-gray-400 pointer-events-none">
          {timeMarkers.map((marker, index) => (
            <div 
              key={index} 
              className="absolute transform -translate-x-1/2"
              style={{ left: marker.position }}
            >
              <div className="h-1.5 w-px bg-gray-300 dark:bg-gray-600 mx-auto" />
              <div className="mt-1">{formatDate(marker.time)}</div>
            </div>
          ))}
        </div>
        
        {/* Timeline track */}
        <div className="h-0.5 w-full bg-gray-200 dark:bg-gray-700 mt-12 mb-3 relative">
          {/* Timeline event indicators */}
          {visibleLogs.map(log => {
            const { icon, color, bgColor } = getEventIconAndColor(log);
            const position = getTimelinePosition(log);
            
            return (
              <div
                key={log.id}
                className={`absolute -top-1 -translate-x-1/2 transform`}
                style={{ left: position }}
              >
                <div 
                  className={`w-2 h-2 rounded-full ${color.replace('text-', 'bg-')} border-2 border-white dark:border-gray-800`}
                />
              </div>
            );
          })}
        </div>
        
        {/* Timeline events */}
        <div className="space-y-4 mt-4">
          {visibleLogs.map(log => {
            const { icon, color, bgColor } = getEventIconAndColor(log);
            
            return (
              <div 
                key={log.id}
                className="flex space-x-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800/60 p-2 rounded-lg transition-colors"
                onClick={() => onViewLogDetails(log)}
              >
                <div>
                  <div className={`w-10 h-10 rounded-full ${bgColor} flex items-center justify-center ${color}`}>
                    {icon}
                  </div>
                </div>
                
                <div className="flex-1">
                  <div className="flex justify-between items-start">
                    <h3 className={`text-sm font-medium ${color}`}>{log.action}</h3>
                    <time className="text-xs text-gray-500 dark:text-gray-400">
                      {formatSystemDate(log.timestamp)}
                    </time>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mt-1 line-clamp-2">
                    {log.details}
                  </p>
                </div>
              </div>
            );
          })}
          
          {visibleLogs.length === 0 && (
            <div className="text-center py-12 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
              <Filter className="h-10 w-10 mx-auto text-gray-400 mb-2" />
              <h3 className="text-base font-medium text-gray-700 dark:text-gray-300">No events in this time range</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Try adjusting your filters or zoom level to see more events
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}; 