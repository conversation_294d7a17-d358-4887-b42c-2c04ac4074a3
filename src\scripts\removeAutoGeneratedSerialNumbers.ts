import { AppDataSource } from '../config/database';
import { Asset } from '../entities/Asset';
import logger from '../utils/logger';

/**
 * This script removes all auto-generated serial numbers from the database.
 * It sets the serialNumber field to null for all assets where the serialNumber starts with "SN-".
 */
async function removeAutoGeneratedSerialNumbers() {
  try {
    // Initialize the database connection
    await AppDataSource.initialize();
    logger.info('Database connection initialized');

    // Get the Asset repository
    const assetRepository = AppDataSource.getRepository(Asset);
    
    // Find all assets with auto-generated serial numbers
    const assets = await assetRepository.find({
      where: {
        // Using raw query to check if serialNumber starts with "SN-"
        serialNumber: AppDataSource.manager.connection.driver.options.type === 'mysql' 
          ? () => "serialNumber LIKE 'SN-%'" 
          : () => "serialNumber LIKE 'SN-%'"
      }
    });
    
    logger.info(`Found ${assets.length} assets with auto-generated serial numbers`);
    
    // Update each asset to remove the serial number
    for (const asset of assets) {
      logger.info(`Removing serial number ${asset.serialNumber} from asset ID ${asset.id}`);
      asset.serialNumber = null;
      await assetRepository.save(asset);
    }
    
    logger.info('Successfully removed all auto-generated serial numbers');
    
    // Close the database connection
    await AppDataSource.destroy();
    logger.info('Database connection closed');
    
    console.log('Successfully removed all auto-generated serial numbers');
  } catch (error) {
    logger.error('Error removing auto-generated serial numbers:', error);
    console.error('Error removing auto-generated serial numbers:', error);
    
    // Make sure to close the database connection even if there's an error
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      logger.info('Database connection closed after error');
    }
  }
}

// Run the script
removeAutoGeneratedSerialNumbers(); 