import { AppDataSource } from '../config/database';

async function consolidateLeaveTypes() {
  try {
    console.log('🔍 Initializing database connection...');
    await AppDataSource.initialize();
    
    const queryRunner = AppDataSource.createQueryRunner();
    
    await queryRunner.startTransaction();
    
    try {
      console.log('🧹 Consolidating duplicate leave types...\n');
      
      // Define the consolidation mapping
      const consolidationMap = {
        // All annual leave variants → ANNUAL_LEAVE
        'annual': 'ANNUAL_LEAVE',
        'ANNUAL': 'ANNUAL_LEAVE',
        // All sick leave variants → SICK_LEAVE  
        'sick': 'SICK_LEAVE',
        'SICK': 'SICK_LEAVE',
        // Custom leave type → PERSONAL_LEAVE
        'CUSTOM_1750750560593': 'PERSONAL_LEAVE'
      };
      
      console.log('📋 Consolidation Plan:');
      Object.entries(consolidationMap).forEach(([from, to]) => {
        console.log(`   • ${from} → ${to}`);
      });
      
      // Step 1: Fix the confusing ANNUAL_LEAVE policy (currently named "Sick Leaves")
      console.log('\n🔧 Step 1: Fixing ANNUAL_LEAVE policy name...');
      await queryRunner.query(`
        UPDATE leave_type_policies 
        SET displayName = 'Annual Leave', description = 'Annual leave entitlement'
        WHERE leaveType = 'ANNUAL_LEAVE'
      `);
      console.log('   ✅ Fixed ANNUAL_LEAVE policy name');
      
      // Step 2: Create SICK_LEAVE policy if it doesn't exist
      console.log('\n🔧 Step 2: Creating SICK_LEAVE policy...');
      const policyConfigId = (await queryRunner.query(`
        SELECT id FROM leave_policy_configurations 
        WHERE isActive = 1 ORDER BY id DESC LIMIT 1
      `))[0].id;
      
      await queryRunner.query(`
        INSERT IGNORE INTO leave_type_policies (
          leaveType, enabled, displayName, description, settings, 
          applicableRoles, applicableDepartments, maxDaysPerYear, 
          minServicePeriod, allowCarryForward, carryForwardLimit, 
          encashmentAllowed, documentRequired, sortOrder, isActive, 
          category, genderEligibility, allowHalfDay, minDaysNotice,
          policyConfigurationId
        ) VALUES (
          'SICK_LEAVE', 1, 'Sick Leave', 'Sick leave entitlement', '{}', 
          '[]', '[]', 15, 0, 0, 0, 0, 0, 2, 1, 
          'general', 'all', 1, 0, ?
        )
      `, [policyConfigId]);
      console.log('   ✅ Created SICK_LEAVE policy');
      
      // Step 3: Create PERSONAL_LEAVE policy
      console.log('\n🔧 Step 3: Creating PERSONAL_LEAVE policy...');
      await queryRunner.query(`
        INSERT IGNORE INTO leave_type_policies (
          leaveType, enabled, displayName, description, settings, 
          applicableRoles, applicableDepartments, maxDaysPerYear, 
          minServicePeriod, allowCarryForward, carryForwardLimit, 
          encashmentAllowed, documentRequired, sortOrder, isActive, 
          category, genderEligibility, allowHalfDay, minDaysNotice,
          policyConfigurationId
        ) VALUES (
          'PERSONAL_LEAVE', 1, 'Personal Leave', 'Personal leave entitlement', '{}', 
          '[]', '[]', 10, 0, 0, 0, 0, 0, 3, 1, 
          'general', 'all', 1, 0, ?
        )
      `, [policyConfigId]);
      console.log('   ✅ Created PERSONAL_LEAVE policy');
      
      // Step 4: Consolidate allocations data
      console.log('\n🔧 Step 4: Consolidating allocations data...');
      for (const [fromType, toType] of Object.entries(consolidationMap)) {
        const count = await queryRunner.query(`
          SELECT COUNT(*) as count FROM leave_allocations WHERE leaveType = ?
        `, [fromType]);
        
        if (count[0].count > 0) {
          console.log(`   📊 Moving ${count[0].count} allocation records: ${fromType} → ${toType}`);
          
          // Update existing records
          await queryRunner.query(`
            UPDATE leave_allocations 
            SET leaveType = ? 
            WHERE leaveType = ?
          `, [toType, fromType]);
          
          console.log(`   ✅ Updated ${fromType} allocations to ${toType}`);
        }
      }
      
      // Step 5: Consolidate balances data  
      console.log('\n🔧 Step 5: Consolidating balances data...');
      for (const [fromType, toType] of Object.entries(consolidationMap)) {
        const count = await queryRunner.query(`
          SELECT COUNT(*) as count FROM leave_balances WHERE leaveType = ?
        `, [fromType]);
        
        if (count[0].count > 0) {
          console.log(`   📊 Moving ${count[0].count} balance records: ${fromType} → ${toType}`);
          
          // Update existing records
          await queryRunner.query(`
            UPDATE leave_balances 
            SET leaveType = ? 
            WHERE leaveType = ?
          `, [toType, fromType]);
          
          console.log(`   ✅ Updated ${fromType} balances to ${toType}`);
        }
      }
      
      // Step 6: Clean up old leave type policies
      console.log('\n🔧 Step 6: Cleaning up old leave type policies...');
      const oldTypes = Object.keys(consolidationMap);
      
      for (const oldType of oldTypes) {
        await queryRunner.query(`
          UPDATE leave_type_policies 
          SET isActive = 0, enabled = 0
          WHERE leaveType = ?
        `, [oldType]);
        console.log(`   🗑️  Deactivated policy for ${oldType}`);
      }
      
      await queryRunner.commitTransaction();
      
      console.log('\n🎉 LEAVE TYPES CONSOLIDATION COMPLETE!');
      console.log('📋 Final Result:');
      console.log('   ✅ ANNUAL_LEAVE - Annual leave entitlement');
      console.log('   ✅ SICK_LEAVE - Sick leave entitlement');
      console.log('   ✅ PERSONAL_LEAVE - Personal leave entitlement');
      console.log('   🗑️  Deactivated old duplicate policies');
      
      console.log('\n📝 NEXT STEPS:');
      console.log('   1. Refresh your Leave Management page');
      console.log('   2. You should now see 3 clean leave types');
      console.log('   3. All your existing data has been preserved');
      console.log('   4. Configure the leave types as needed in Policy Configuration');
      
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
    
  } catch (error) {
    console.error('❌ Error consolidating leave types:', error);
    throw error;
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('\n📪 Database connection closed');
    }
  }
}

// Run the consolidation
if (require.main === module) {
  consolidateLeaveTypes()
    .then(() => {
      console.log('✅ Leave types consolidation completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Leave types consolidation failed:', error);
      process.exit(1);
    });
}

export { consolidateLeaveTypes }; 