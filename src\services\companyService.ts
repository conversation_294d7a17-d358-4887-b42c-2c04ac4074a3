import { toast } from 'react-hot-toast';

// Types
interface CompanyProfile {
  companyName: string;
  companyLogo: File | null | string;
  companyEmail: string;
  companyPhone: string;
  companyAddress: string;
  mainLocationName: string;
  subLocations: string[];
  departments: string[];
  projects: string[];
  adminUser: {
    fullName: string;
    email: string;
  };
  advancedSettings: {
    fiscalStartMonth: string;
    timezone: string;
    currency: string;
    defaultLanguage: string;
  };
}

// Helper to load the company profile from localStorage or return default
const loadCompanyProfile = (): CompanyProfile => {
  const storedProfile = localStorage.getItem('companyProfile');
  
  if (storedProfile) {
    return JSON.parse(storedProfile);
  }
  
  // Return default company profile if none exists
  return {
    companyName: 'Your Company Name',
    companyLogo: null,
    companyEmail: '<EMAIL>',
    companyPhone: '+****************',
    companyAddress: '123 Business Ave, Suite 100, Cityville, ST 12345',
    mainLocationName: 'Headquarters',
    subLocations: ['Branch Office 1', 'Branch Office 2'],
    departments: ['IT', 'HR', 'Finance', 'Marketing', 'Operations'],
    projects: ['Website Redesign', 'ERP Implementation', 'Office Relocation'],
    adminUser: {
      fullName: 'Admin User',
      email: '<EMAIL>'
    },
    advancedSettings: {
      fiscalStartMonth: 'January',
      timezone: 'UTC',
      currency: 'USD',
      defaultLanguage: 'en'
    }
  };
};

// Get company profile
export const getCompanyProfile = async (): Promise<CompanyProfile> => {
  // Simulate API call with delay
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(loadCompanyProfile());
    }, 300);
  });
};

// Update company profile
export const updateCompanyProfile = async (
  updatedProfile: CompanyProfile
): Promise<CompanyProfile> => {
  // Simulate API call with delay
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        // Convert File object to string for storage
        const profileToStore = { ...updatedProfile };
        
        // Store the updated profile
        localStorage.setItem('companyProfile', JSON.stringify(profileToStore));
        
        resolve(profileToStore);
      } catch (error) {
        console.error('Error saving company profile:', error);
        reject(new Error('Failed to save company profile'));
      }
    }, 500);
  });
};

// Delete company logo
export const deleteCompanyLogo = async (): Promise<void> => {
  const profile = loadCompanyProfile();
  profile.companyLogo = null;
  
  localStorage.setItem('companyProfile', JSON.stringify(profile));
  return Promise.resolve();
};

// Upload company logo (simulated)
export const uploadCompanyLogo = async (file: File): Promise<string> => {
  return new Promise((resolve) => {
    // Create a FileReader to convert the file to base64
    const reader = new FileReader();
    
    reader.onload = () => {
      // This would normally be the URL returned from the server
      const logoUrl = reader.result as string;
      resolve(logoUrl);
    };
    
    reader.readAsDataURL(file);
  });
}; 