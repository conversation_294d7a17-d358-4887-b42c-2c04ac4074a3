import React, { useState, useEffect } from 'react';
import { Plus, Calendar, Users, CheckCircle, Clock, AlertCircle, Filter, Search, MoreVertical, Timer, Template, Zap } from 'lucide-react';
import { EnhancedTaskCard } from './task/EnhancedTaskCard';

interface ChecklistItem {
  id: string;
  text: string;
  completed: boolean;
  createdAt: string;
  completedAt?: string;
  completedBy?: string;
}

interface TaskDependency {
  id: number;
  title: string;
  status: string;
}

interface TimeEntry {
  id: number;
  durationMinutes: number;
  startTime: string;
  endTime?: string;
  description?: string;
  type: 'manual' | 'timer' | 'imported';
}

interface EnhancedTodo {
  id: number;
  title: string;
  description?: string;
  status: 'todo' | 'in_progress' | 'in_review' | 'done' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'critical';
  type: 'task' | 'bug' | 'feature' | 'improvement' | 'research' | 'subtask';
  assignedTo?: {
    id: string;
    name: string;
    avatar?: string;
  };
  dueDate?: string;
  estimatedHours?: number;
  timeSpentMinutes: number;
  progress: number;
  checklist?: ChecklistItem[];
  dependencies?: TaskDependency[];
  subtasks?: EnhancedTodo[];
  isBlocked: boolean;
  blockReason?: string;
  requiresApproval: boolean;
  approvalStatus: 'not_required' | 'pending' | 'approved' | 'rejected';
  tags?: string[];
  commentsCount: number;
  attachmentsCount: number;
  timeEntries?: TimeEntry[];
  isTimerRunning?: boolean;
  projectId: number;
  createdAt: string;
  updatedAt: string;
}

interface Project {
  id: number;
  name: string;
  description: string;
  status: 'planning' | 'active' | 'completed' | 'on-hold';
  progress: number;
  startDate: string;
  endDate: string;
  teamMembers: string[];
  tasks: EnhancedTodo[];
  createdAt: string;
  updatedAt: string;
}

interface ProjectTaskManagementProps {
  currentView?: string;
}

function ProjectTaskManagement({ currentView = 'dashboard' }: ProjectTaskManagementProps) {
  const [activeTab, setActiveTab] = useState<'dashboard' | 'projects' | 'todos' | 'calendar' | 'templates' | 'time-tracking'>('dashboard');
  const [projects, setProjects] = useState<Project[]>([]);
  const [todos, setTodos] = useState<EnhancedTodo[]>([]);
  const [templates, setTemplates] = useState<any[]>([]);
  const [showCreateProject, setShowCreateProject] = useState(false);
  const [showCreateTodo, setShowCreateTodo] = useState(false);
  const [showCreateTemplate, setShowCreateTemplate] = useState(false);
  const [showTemplateSelector, setShowTemplateSelector] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [activeTimers, setActiveTimers] = useState<Set<number>>(new Set());
  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([]);

  // Initialize with sample data
  useEffect(() => {
    const sampleProjects: Project[] = [
      {
        id: 1,
        name: 'IT Infrastructure Upgrade',
        description: 'Modernize server infrastructure and network equipment',
        status: 'active',
        progress: 65,
        startDate: '2024-01-15',
        endDate: '2024-06-30',
        teamMembers: ['John Doe', 'Jane Smith', 'Mike Johnson'],
        tasks: [],
        createdAt: '2024-01-15T00:00:00Z',
        updatedAt: '2024-01-20T00:00:00Z'
      },
      {
        id: 2,
        name: 'Security Audit Implementation',
        description: 'Comprehensive security review and implementation of new protocols',
        status: 'planning',
        progress: 25,
        startDate: '2024-02-01',
        endDate: '2024-08-15',
        teamMembers: ['Sarah Wilson', 'Tom Brown'],
        tasks: [],
        createdAt: '2024-02-01T00:00:00Z',
        updatedAt: '2024-02-05T00:00:00Z'
      }
    ];

    const sampleTodos: EnhancedTodo[] = [
      {
        id: 1,
        title: 'Update server documentation',
        description: 'Document all server configurations and maintenance procedures',
        status: 'in_progress',
        priority: 'high',
        type: 'task',
        assignedTo: {
          id: 'user1',
          name: 'John Doe'
        },
        dueDate: '2024-02-15',
        estimatedHours: 6,
        timeSpentMinutes: 120,
        progress: 40,
        checklist: [
          {
            id: 'c1',
            text: 'Review existing documentation',
            completed: true,
            createdAt: '2024-01-20T00:00:00Z',
            completedAt: '2024-01-21T10:00:00Z',
            completedBy: 'user1'
          },
          {
            id: 'c2',
            text: 'Update server configuration docs',
            completed: false,
            createdAt: '2024-01-20T00:00:00Z'
          }
        ],
        dependencies: [],
        subtasks: [],
        isBlocked: false,
        requiresApproval: false,
        approvalStatus: 'not_required',
        tags: ['documentation', 'maintenance'],
        commentsCount: 2,
        attachmentsCount: 1,
        timeEntries: [],
        isTimerRunning: false,
        projectId: 1,
        createdAt: '2024-01-20T00:00:00Z',
        updatedAt: '2024-01-25T00:00:00Z'
      },
      {
        id: 2,
        title: 'Install security patches',
        description: 'Apply latest security updates to all production servers',
        status: 'done',
        priority: 'high',
        type: 'task',
        assignedTo: {
          id: 'user2',
          name: 'Jane Smith'
        },
        dueDate: '2024-01-25',
        estimatedHours: 4,
        timeSpentMinutes: 240,
        progress: 100,
        checklist: [
          {
            id: 'c3',
            text: 'Download latest patches',
            completed: true,
            createdAt: '2024-01-18T00:00:00Z',
            completedAt: '2024-01-19T09:00:00Z',
            completedBy: 'user2'
          },
          {
            id: 'c4',
            text: 'Test patches on staging',
            completed: true,
            createdAt: '2024-01-18T00:00:00Z',
            completedAt: '2024-01-20T14:00:00Z',
            completedBy: 'user2'
          },
          {
            id: 'c5',
            text: 'Apply patches to production',
            completed: true,
            createdAt: '2024-01-18T00:00:00Z',
            completedAt: '2024-01-21T16:00:00Z',
            completedBy: 'user2'
          }
        ],
        dependencies: [],
        subtasks: [],
        isBlocked: false,
        requiresApproval: true,
        approvalStatus: 'approved',
        tags: ['security', 'critical'],
        commentsCount: 1,
        attachmentsCount: 0,
        timeEntries: [],
        isTimerRunning: false,
        projectId: 1,
        createdAt: '2024-01-18T00:00:00Z',
        updatedAt: '2024-01-21T16:00:00Z'
      }
    ];

    setProjects(sampleProjects);
    setTodos(sampleTodos);
  }, []);

  // Enhanced task management functions
  const handleTaskUpdate = (taskId: number, updates: Partial<EnhancedTodo>) => {
    setTodos(prev => prev.map(todo =>
      todo.id === taskId ? { ...todo, ...updates } : todo
    ));
  };

  const handleStartTimer = async (taskId: number) => {
    // In real implementation, this would call the API
    setActiveTimers(prev => new Set([...prev, taskId]));
    setTodos(prev => prev.map(todo =>
      todo.id === taskId ? { ...todo, isTimerRunning: true } : todo
    ));
  };

  const handleStopTimer = async (taskId: number) => {
    // In real implementation, this would call the API
    setActiveTimers(prev => {
      const newSet = new Set(prev);
      newSet.delete(taskId);
      return newSet;
    });
    setTodos(prev => prev.map(todo =>
      todo.id === taskId ? { ...todo, isTimerRunning: false } : todo
    ));
  };

  const handleAddTimeEntry = async (taskId: number, entry: Omit<TimeEntry, 'id'>) => {
    // In real implementation, this would call the API
    const newEntry: TimeEntry = {
      ...entry,
      id: Date.now()
    };

    setTodos(prev => prev.map(todo =>
      todo.id === taskId ? {
        ...todo,
        timeEntries: [...(todo.timeEntries || []), newEntry],
        timeSpentMinutes: todo.timeSpentMinutes + entry.durationMinutes
      } : todo
    ));
  };

  const handleUpdateChecklist = async (taskId: number, checklist: ChecklistItem[]) => {
    // In real implementation, this would call the API
    const completedItems = checklist.filter(item => item.completed).length;
    const progress = checklist.length > 0 ? Math.round((completedItems / checklist.length) * 100) : 0;

    setTodos(prev => prev.map(todo =>
      todo.id === taskId ? { ...todo, checklist, progress } : todo
    ));
  };

  const handleViewTaskDetails = (taskId: number) => {
    // In real implementation, this would open a detailed task modal
    console.log('View task details:', taskId);
  };

  const addTodo = (todo: Omit<EnhancedTodo, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newTodo: EnhancedTodo = {
      ...todo,
      id: Date.now(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    setTodos(prev => [...prev, newTodo]);
  };

  const toggleTodo = (id: number) => {
    setTodos(prev => prev.map(todo =>
      todo.id === id ? {
        ...todo,
        status: todo.status === 'done' ? 'todo' : 'done',
        progress: todo.status === 'done' ? 0 : 100
      } : todo
    ));
  };

  const deleteTodo = (id: number) => {
    setTodos(prev => prev.filter(todo => todo.id !== id));
  };

  const addProject = (project: Omit<Project, 'id' | 'todos'>) => {
    const newProject: Project = {
      ...project,
      id: Date.now().toString(),
      todos: []
    };
    setProjects(prev => [...prev, newProject]);
  };

  const filteredTodos = todos.filter(todo => {
    const matchesSearch = todo.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (todo.description && todo.description.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesFilter = filterStatus === 'all' ||
                         (filterStatus === 'completed' && todo.status === 'done') ||
                         (filterStatus === 'pending' && todo.status !== 'done') ||
                         (filterStatus === 'in_progress' && todo.status === 'in_progress') ||
                         (filterStatus === 'blocked' && todo.isBlocked) ||
                         (filterStatus === todo.priority);
    return matchesSearch && matchesFilter;
  });

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-50 border-green-200';
      case 'planning': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'completed': return 'text-gray-600 bg-gray-50 border-gray-200';
      case 'on-hold': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const renderDashboard = () => (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Calendar className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Projects</p>
              <p className="text-2xl font-bold text-gray-900">{projects.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Completed Tasks</p>
              <p className="text-2xl font-bold text-gray-900">{todos.filter(t => t.status === 'done').length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Clock className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending Tasks</p>
              <p className="text-2xl font-bold text-gray-900">{todos.filter(t => t.status !== 'done').length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">High Priority</p>
              <p className="text-2xl font-bold text-gray-900">{todos.filter(t => (t.priority === 'high' || t.priority === 'critical') && t.status !== 'done').length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Projects */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Projects</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {projects.slice(0, 3).map(project => (
              <div key={project.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{project.name}</h4>
                  <p className="text-sm text-gray-600">{project.description}</p>
                  <div className="mt-2 flex items-center space-x-4">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(project.status)}`}>
                      {project.status}
                    </span>
                    <span className="text-sm text-gray-500">{project.progress}% complete</span>
                  </div>
                </div>
                <div className="ml-4">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${project.progress}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Tasks */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Recent Tasks</h3>
        </div>
        <div className="p-6">
          <div className="space-y-3">
            {todos.slice(0, 5).map(todo => (
              <div key={todo.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <input
                  type="checkbox"
                  checked={todo.status === 'done'}
                  onChange={() => toggleTodo(todo.id)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <div className="flex-1">
                  <p className={`font-medium ${todo.status === 'done' ? 'line-through text-gray-500' : 'text-gray-900'}`}>
                    {todo.title}
                  </p>
                  <p className="text-sm text-gray-600">{todo.description}</p>
                  <div className="flex items-center gap-2 mt-1">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      todo.status === 'done' ? 'bg-green-100 text-green-800' :
                      todo.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {todo.status.replace('_', ' ')}
                    </span>
                    {todo.assignedTo && (
                      <span className="text-xs text-gray-500">{todo.assignedTo.name}</span>
                    )}
                  </div>
                </div>
                <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getPriorityColor(todo.priority)}`}>
                  {todo.priority}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderProjects = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Projects</h2>
        <button
          onClick={() => setShowCreateProject(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>New Project</span>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {projects.map(project => (
          <div key={project.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-lg font-semibold text-gray-900">{project.name}</h3>
              <button className="text-gray-400 hover:text-gray-600">
                <MoreVertical className="h-4 w-4" />
              </button>
            </div>

            <p className="text-gray-600 mb-4">{project.description}</p>

            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(project.status)}`}>
                  {project.status}
                </span>
                <span className="text-sm text-gray-500">{project.progress}%</span>
              </div>

              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${project.progress}%` }}
                ></div>
              </div>

              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <div className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4" />
                  <span>{new Date(project.endDate).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Users className="h-4 w-4" />
                  <span>{project.teamMembers.length}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderTodos = () => (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <h2 className="text-2xl font-bold text-gray-900">Tasks & Todos</h2>
        <button
          onClick={() => setShowCreateTodo(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>New Task</span>
        </button>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search tasks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-400" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Tasks</option>
              <option value="pending">Pending</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="blocked">Blocked</option>
              <option value="high">High Priority</option>
              <option value="medium">Medium Priority</option>
              <option value="low">Low Priority</option>
              <option value="critical">Critical Priority</option>
            </select>
          </div>
        </div>
      </div>

      {/* Enhanced Tasks List */}
      <div className="space-y-4">
        {filteredTodos.map(todo => (
          <EnhancedTaskCard
            key={todo.id}
            task={todo}
            onTaskUpdate={handleTaskUpdate}
            onStartTimer={handleStartTimer}
            onStopTimer={handleStopTimer}
            onAddTimeEntry={handleAddTimeEntry}
            onUpdateChecklist={handleUpdateChecklist}
            onViewDetails={handleViewTaskDetails}
          />
        ))}
        {filteredTodos.length === 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
            <p className="text-gray-500">No tasks found matching your criteria.</p>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Project & Task Management</h1>
        <p className="text-gray-600">Manage your projects, tasks, todos, and team collaboration</p>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 mb-8">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'dashboard', name: 'Dashboard', icon: Calendar },
            { id: 'projects', name: 'Projects', icon: Users },
            { id: 'todos', name: 'Tasks', icon: CheckCircle },
            { id: 'templates', name: 'Templates', icon: Template },
            { id: 'time-tracking', name: 'Time Tracking', icon: Timer },
            { id: 'calendar', name: 'Calendar', icon: Calendar }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.name}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="min-h-screen">
        {activeTab === 'dashboard' && renderDashboard()}
        {activeTab === 'projects' && renderProjects()}
        {activeTab === 'todos' && renderTodos()}
        {activeTab === 'templates' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
            <Template className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Task Templates</h3>
            <p className="text-gray-600">Create and manage reusable task templates...</p>
            <button
              onClick={() => setShowCreateTemplate(true)}
              className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Create Template
            </button>
          </div>
        )}
        {activeTab === 'time-tracking' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-gray-900">Time Tracking</h2>
            </div>

            {/* Active Timers */}
            {activeTimers.size > 0 && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Active Timers</h3>
                <div className="space-y-3">
                  {todos.filter(todo => activeTimers.has(todo.id)).map(todo => (
                    <div key={todo.id} className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{todo.title}</p>
                        <p className="text-sm text-gray-600">Started: {new Date().toLocaleTimeString()}</p>
                      </div>
                      <button
                        onClick={() => handleStopTimer(todo.id)}
                        className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700"
                      >
                        Stop Timer
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Time Summary */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Timer className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Time Today</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {Math.floor(todos.reduce((acc, todo) => acc + todo.timeSpentMinutes, 0) / 60)}h {todos.reduce((acc, todo) => acc + todo.timeSpentMinutes, 0) % 60}m
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Tasks with Time</p>
                    <p className="text-2xl font-bold text-gray-900">{todos.filter(t => t.timeSpentMinutes > 0).length}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                <div className="flex items-center">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <Clock className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Active Timers</p>
                    <p className="text-2xl font-bold text-gray-900">{activeTimers.size}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Time Entries */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">Recent Time Entries</h3>
              </div>
              <div className="p-6">
                <div className="space-y-3">
                  {todos.filter(todo => todo.timeSpentMinutes > 0).slice(0, 5).map(todo => (
                    <div key={todo.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{todo.title}</p>
                        <p className="text-sm text-gray-600">
                          {Math.floor(todo.timeSpentMinutes / 60)}h {todo.timeSpentMinutes % 60}m logged
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-500">{todo.assignedTo?.name}</p>
                        <p className="text-xs text-gray-400">Updated {new Date(todo.updatedAt).toLocaleDateString()}</p>
                      </div>
                    </div>
                  ))}
                  {todos.filter(todo => todo.timeSpentMinutes > 0).length === 0 && (
                    <p className="text-gray-500 text-center py-8">No time entries yet. Start a timer on any task to begin tracking time.</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
        {activeTab === 'calendar' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Calendar View</h3>
            <p className="text-gray-600">Calendar integration coming soon...</p>
          </div>
        )}
      </div>

      {/* Create Project Modal Placeholder */}
      {showCreateProject && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Create New Project</h3>
            <p className="text-gray-600 mb-4">Project creation form will be implemented here.</p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowCreateProject(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={() => setShowCreateProject(false)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Create
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Create Todo Modal Placeholder */}
      {showCreateTodo && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Create New Task</h3>
            <p className="text-gray-600 mb-4">Task creation form will be implemented here.</p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowCreateTodo(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={() => setShowCreateTodo(false)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Create
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default ProjectTaskManagement;
