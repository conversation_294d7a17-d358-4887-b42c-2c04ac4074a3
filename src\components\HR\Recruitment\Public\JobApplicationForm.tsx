import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  CircularProgress,
  Stepper,
  Step,
  StepLabel,
  Paper,
  Divider
} from '@mui/material';
import {
  Person as PersonIcon,
  Work as WorkIcon,
  School as SchoolIcon,
  Description as DescriptionIcon,
  Send as SendIcon
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { recruitmentAPI } from '../../../../services/recruitmentAPI';

interface JobPosting {
  id: number;
  title: string;
  department: string;
  location: string;
  description: string;
  requirements: string;
  jobType: string;
  experienceLevel: string;
  workLocation: string;
}

interface ApplicationData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  country: string;
  postalCode: string;
  coverLetter: string;
  portfolioUrl: string;
  linkedinUrl: string;
  githubUrl: string;
  websiteUrl: string;
  source: string;
  referredBy: string;
  skills: string[];
  experience: Array<{
    company: string;
    position: string;
    startDate: string;
    endDate: string;
    current: boolean;
    description: string;
  }>;
  education: Array<{
    institution: string;
    degree: string;
    field: string;
    startDate: string;
    endDate: string;
    gpa: string;
  }>;
  expectedSalary: number;
  salaryCurrency: string;
  salaryPeriod: string;
  availableStartDate: string;
  noticePeriodDays: number;
  requiresVisa: boolean;
  willingToRelocate: boolean;
  willingToTravel: boolean;
}

const steps = ['Personal Info', 'Experience', 'Education', 'Additional Info'];

const JobApplicationForm: React.FC = () => {
  const { jobId } = useParams<{ jobId: string }>();
  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);
  const [jobPosting, setJobPosting] = useState<JobPosting | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const [applicationData, setApplicationData] = useState<ApplicationData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    country: '',
    postalCode: '',
    coverLetter: '',
    portfolioUrl: '',
    linkedinUrl: '',
    githubUrl: '',
    websiteUrl: '',
    source: 'company_website',
    referredBy: '',
    skills: [],
    experience: [],
    education: [],
    expectedSalary: 0,
    salaryCurrency: 'USD',
    salaryPeriod: 'yearly',
    availableStartDate: '',
    noticePeriodDays: 0,
    requiresVisa: false,
    willingToRelocate: false,
    willingToTravel: false
  });

  useEffect(() => {
    if (jobId) {
      fetchJobPosting();
    }
  }, [jobId]);

  const fetchJobPosting = async () => {
    try {
      setLoading(true);
      const response = await recruitmentAPI.getPublicJobPostingById(Number(jobId));
      setJobPosting(response.data.jobPosting);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch job posting');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof ApplicationData, value: any) => {
    setApplicationData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNext = () => {
    setActiveStep(prev => prev + 1);
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      const submitData = {
        ...applicationData,
        jobPostingId: Number(jobId)
      };

      await recruitmentAPI.createJobApplication(submitData);
      setSuccess(true);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to submit application');
    } finally {
      setSubmitting(false);
    }
  };

  const addExperience = () => {
    setApplicationData(prev => ({
      ...prev,
      experience: [
        ...prev.experience,
        {
          company: '',
          position: '',
          startDate: '',
          endDate: '',
          current: false,
          description: ''
        }
      ]
    }));
  };

  const addEducation = () => {
    setApplicationData(prev => ({
      ...prev,
      education: [
        ...prev.education,
        {
          institution: '',
          degree: '',
          field: '',
          startDate: '',
          endDate: '',
          gpa: ''
        }
      ]
    }));
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error && !jobPosting) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  if (success) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Card>
          <CardContent>
            <Typography variant="h4" color="success.main" gutterBottom>
              Application Submitted Successfully!
            </Typography>
            <Typography variant="body1" sx={{ mb: 3 }}>
              Thank you for your interest in the {jobPosting?.title} position. 
              We will review your application and get back to you soon.
            </Typography>
            <Button
              variant="contained"
              onClick={() => navigate('/jobs')}
            >
              View More Jobs
            </Button>
          </CardContent>
        </Card>
      </Box>
    );
  }

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="First Name"
                value={applicationData.firstName}
                onChange={(e) => handleInputChange('firstName', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={applicationData.lastName}
                onChange={(e) => handleInputChange('lastName', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={applicationData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Phone"
                value={applicationData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Cover Letter"
                multiline
                rows={4}
                value={applicationData.coverLetter}
                onChange={(e) => handleInputChange('coverLetter', e.target.value)}
                placeholder="Tell us why you're interested in this position..."
              />
            </Grid>
          </Grid>
        );

      case 1:
        return (
          <Box>
            <Box display="flex" justifyContent="between" alignItems="center" mb={2}>
              <Typography variant="h6">Work Experience</Typography>
              <Button variant="outlined" onClick={addExperience}>
                Add Experience
              </Button>
            </Box>
            {applicationData.experience.map((exp, index) => (
              <Paper key={index} sx={{ p: 2, mb: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Company"
                      value={exp.company}
                      onChange={(e) => {
                        const newExp = [...applicationData.experience];
                        newExp[index].company = e.target.value;
                        handleInputChange('experience', newExp);
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Position"
                      value={exp.position}
                      onChange={(e) => {
                        const newExp = [...applicationData.experience];
                        newExp[index].position = e.target.value;
                        handleInputChange('experience', newExp);
                      }}
                    />
                  </Grid>
                </Grid>
              </Paper>
            ))}
          </Box>
        );

      case 2:
        return (
          <Box>
            <Box display="flex" justifyContent="between" alignItems="center" mb={2}>
              <Typography variant="h6">Education</Typography>
              <Button variant="outlined" onClick={addEducation}>
                Add Education
              </Button>
            </Box>
            {applicationData.education.map((edu, index) => (
              <Paper key={index} sx={{ p: 2, mb: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Institution"
                      value={edu.institution}
                      onChange={(e) => {
                        const newEdu = [...applicationData.education];
                        newEdu[index].institution = e.target.value;
                        handleInputChange('education', newEdu);
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Degree"
                      value={edu.degree}
                      onChange={(e) => {
                        const newEdu = [...applicationData.education];
                        newEdu[index].degree = e.target.value;
                        handleInputChange('education', newEdu);
                      }}
                    />
                  </Grid>
                </Grid>
              </Paper>
            ))}
          </Box>
        );

      case 3:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Portfolio URL"
                value={applicationData.portfolioUrl}
                onChange={(e) => handleInputChange('portfolioUrl', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="LinkedIn URL"
                value={applicationData.linkedinUrl}
                onChange={(e) => handleInputChange('linkedinUrl', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Expected Salary"
                type="number"
                value={applicationData.expectedSalary}
                onChange={(e) => handleInputChange('expectedSalary', Number(e.target.value))}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>How did you hear about us?</InputLabel>
                <Select
                  value={applicationData.source}
                  label="How did you hear about us?"
                  onChange={(e) => handleInputChange('source', e.target.value)}
                >
                  <MenuItem value="company_website">Company Website</MenuItem>
                  <MenuItem value="job_board">Job Board</MenuItem>
                  <MenuItem value="linkedin">LinkedIn</MenuItem>
                  <MenuItem value="referral">Referral</MenuItem>
                  <MenuItem value="recruiter">Recruiter</MenuItem>
                  <MenuItem value="social_media">Social Media</MenuItem>
                  <MenuItem value="career_fair">Career Fair</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        );

      default:
        return null;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Job Posting Header */}
      {jobPosting && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h4" gutterBottom>
              Apply for {jobPosting.title}
            </Typography>
            <Typography variant="body1" color="textSecondary">
              {jobPosting.department} • {jobPosting.location}
            </Typography>
          </CardContent>
        </Card>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Application Form */}
      <Card>
        <CardContent>
          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {renderStepContent(activeStep)}

          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
            <Button
              disabled={activeStep === 0}
              onClick={handleBack}
            >
              Back
            </Button>
            <Box>
              {activeStep === steps.length - 1 ? (
                <Button
                  variant="contained"
                  onClick={handleSubmit}
                  disabled={submitting}
                  startIcon={submitting ? <CircularProgress size={20} /> : <SendIcon />}
                >
                  {submitting ? 'Submitting...' : 'Submit Application'}
                </Button>
              ) : (
                <Button
                  variant="contained"
                  onClick={handleNext}
                >
                  Next
                </Button>
              )}
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default JobApplicationForm;
