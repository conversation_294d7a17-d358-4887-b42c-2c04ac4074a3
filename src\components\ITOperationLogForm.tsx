import React, { useState, useEffect } from "react";
import { 
  AlignLeft, 
  Tag, 
  Folder, 
  User, 
  MapPin, 
  AlertTriangle, 
  CheckCircle, 
  Calendar, 
  Link, 
  FileText, 
  Upload,
  ArrowLeft
} from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import ITOperationLogApiService from '../services/ITOperationLogApiService';
import { ITOperationLogFormData } from '../services/ITOperationLogService';
import { IssueCategory, ImpactLevel, OperationStatus } from '../entities/ITOperationLog';

// Define props interface
export interface ITOperationLogFormProps {
  onSubmit?: (formData: ITOperationLogFormData) => void;
}

const ITOperationLogForm: React.FC<ITOperationLogFormProps> = ({ onSubmit }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditMode = !!id;
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const [formData, setFormData] = useState<ITOperationLogFormData>({
    title: "",
    description: "",
    issueCategory: IssueCategory.Other,
    assignedTo: "",
    loggedBy: "",
    location: "",
    impactLevel: ImpactLevel.Low,
    status: OperationStatus.Completed,
    dateOccurred: new Date().toISOString().split('T')[0], // Default to today
    dateResolved: "",
    tags: "",
    externalLinks: "",
    attachment: undefined,
    notes: ""
  });

  // For edit mode, fetch data from API
  useEffect(() => {
    if (isEditMode && id) {
      const fetchLogData = async () => {
        setIsLoading(true);
        try {
          console.log('Fetching log data for ID:', id);
          
          const { data, error } = await ITOperationLogApiService.getLogById(id);
          
          console.log('API Response:', { data, error });
          
          if (error) {
            toast.error(`Failed to load log: ${error}`);
            navigate('/it-logs'); // Navigate back to the list on error
            return;
          }
          
          if (!data || Object.keys(data).length === 0) {
            toast.error('Could not find the requested log');
            navigate('/it-logs'); // Navigate back to the list
            return;
          }
          
          // Now we know we have data, set it to the form
          const formattedData = {
            id: data.id,
            title: data.title || "",
            description: data.description || "",
            issueCategory: data.issueCategory || IssueCategory.Other,
            assignedTo: data.assignedTo || "",
            loggedBy: data.loggedBy || "",
            location: data.location || "",
            impactLevel: data.impactLevel || ImpactLevel.Low,
            status: data.status || OperationStatus.Completed,
            dateOccurred: data.dateOccurred ? new Date(data.dateOccurred).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
            dateResolved: data.dateResolved ? new Date(data.dateResolved).toISOString().split('T')[0] : "",
            tags: data.tags || "",
            externalLinks: data.externalLinks || "",
            attachment: data.attachment || undefined,
            notes: data.notes || "",
            createdById: data.createdById,
            lastModifiedById: data.lastModifiedById
          };
          
          console.log('Setting form data:', formattedData);
          setFormData(formattedData);
        } catch (err) {
          console.error("Error fetching log:", err);
          toast.error("Could not load the operation log data. Please try again.");
          navigate('/it-logs'); // Navigate back to the list
        } finally {
          setIsLoading(false);
        }
      };
      
      fetchLogData();
    }
  }, [id, isEditMode, navigate]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setSelectedFile(file);
  };

  // Handle file upload with better error handling
  const handleFileUpload = async (file: File) => {
    try {
      console.log(`Uploading file: ${file.name} (${file.size} bytes, ${file.type})`);
      
      // Create a simple FormData instance
      const formData = new FormData();
      formData.append('attachment', file);
      
      // Make a direct fetch call with no extra headers
      const response = await fetch('/api/it-logs/upload', {
        method: 'POST',
        body: formData
      });
      
      // First log the raw response for debugging
      console.log(`Upload response status: ${response.status}`);
      
      // Handle common HTTP errors
      if (response.status === 413) {
        throw new Error('File is too large. Maximum size is 10MB.');
      }
      
      if (!response.ok) {
        throw new Error(`Upload failed with status ${response.status}`);
      }
      
      // Try to parse the response as JSON
      const result = await response.json();
      console.log('Upload response:', result);
      
      if (!result.success) {
        throw new Error(result.message || 'Unknown error');
      }
      
      return result.data;
    } catch (error) {
      console.error('File upload error:', error);
      throw error;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Get the current user data from localStorage
      const userDataString = localStorage.getItem('userData');
      const userData = userDataString ? JSON.parse(userDataString) : null;
      
      // Create the data to send with the proper user ID
      const dataToSend = {
        ...formData,
        createdById: userData?.id || undefined,
        lastModifiedById: userData?.id || undefined
      };
      
      console.log('Sending data with user ID:', dataToSend);
      
      // First validate the form data
      if (!dataToSend.title || !dataToSend.description || !dataToSend.loggedBy || !dataToSend.dateOccurred) {
        toast.error("Please fill in all required fields");
        setIsSubmitting(false);
        return;
      }

      // Handle file upload with improved error handling
      if (selectedFile) {
        try {
          console.log('Starting file upload process for:', selectedFile.name);
          
          // Use the improved upload handler
          const uploadResult = await handleFileUpload(selectedFile);
          console.log('Upload successful:', uploadResult);
          
          // Update the data with the attachment filename
          dataToSend.attachment = uploadResult.filename;
        } catch (error) {
          console.error('File upload error during form submission:', error);
          toast.error('Failed to upload file: ' + (error instanceof Error ? error.message : String(error)));
          setIsSubmitting(false);
          return;
        }
      }

      // Call the onSubmit prop if provided
      if (onSubmit) {
        onSubmit(dataToSend);
        setIsSubmitting(false);
        return;
      }

      // Log data being sent for debugging
      console.log("Sending data to server:", dataToSend);
      
      // Call the API service
      let response;
      if (isEditMode && id) {
        response = await ITOperationLogApiService.updateLog(id, dataToSend);
      } else {
        response = await ITOperationLogApiService.createLog(dataToSend);
      }
      
      // Handle API response
      if (response.error) {
        toast.error(`Failed to ${isEditMode ? 'update' : 'create'} log: ${response.error}`);
        console.error("API Error:", response.error);
        setIsSubmitting(false);
        return;
      }
      
      // Success!
      toast.success(
        isEditMode 
          ? `Operation log "${formData.title}" updated successfully!` 
          : `Operation log "${formData.title}" created successfully!`,
        {
          duration: 4000,
          position: 'top-right',
        }
      );
      
      // Navigate after a slight delay to ensure the toast is visible
      setTimeout(() => {
        navigate('/it-logs');
      }, 500);
      
    } catch (err) {
      console.error("Error submitting form:", err);
      toast.error(`An unexpected error occurred. Please try again.`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate('/it-logs');
  };

  return (
    <div className="p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {isEditMode ? 'Edit Operation Log' : 'Create New Operation Log'}
            </h1>
            <p className="text-gray-600">
              {isEditMode 
                ? 'Update details of the IT operation log' 
                : 'Record a new IT operation or system change'
              }
            </p>
          </div>
          
          <button 
            onClick={handleCancel}
            className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Logs
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 flex items-center gap-2">
              <AlignLeft className="h-4 w-4 text-gray-500" />
              Log Title
            </label>
            <input 
              name="title" 
              value={formData.title} 
              onChange={handleChange} 
              placeholder="Enter a descriptive title" 
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
              required 
            />
          </div>
          
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 flex items-center gap-2">
              <FileText className="h-4 w-4 text-gray-500" />
              Description
            </label>
            <textarea 
              name="description" 
              value={formData.description} 
              onChange={handleChange} 
              placeholder="Describe what was done in detail..." 
              className="w-full p-2 border rounded h-24 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
              required 
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 flex items-center gap-2">
                <Folder className="h-4 w-4 text-gray-500" />
                Issue Category
              </label>
              <select 
                name="issueCategory" 
                value={formData.issueCategory} 
                onChange={handleChange} 
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value={IssueCategory.Network}>{IssueCategory.Network}</option>
                <option value={IssueCategory.Hardware}>{IssueCategory.Hardware}</option>
                <option value={IssueCategory.Software}>{IssueCategory.Software}</option>
                <option value={IssueCategory.Email}>{IssueCategory.Email}</option>
                <option value={IssueCategory.VPN}>{IssueCategory.VPN}</option>
                <option value={IssueCategory.Other}>{IssueCategory.Other}</option>
              </select>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-gray-500" />
                Impact Level
              </label>
              <select 
                name="impactLevel" 
                value={formData.impactLevel} 
                onChange={handleChange} 
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value={ImpactLevel.Low}>{ImpactLevel.Low}</option>
                <option value={ImpactLevel.Medium}>{ImpactLevel.Medium}</option>
                <option value={ImpactLevel.High}>{ImpactLevel.High}</option>
                <option value={ImpactLevel.Critical}>{ImpactLevel.Critical}</option>
              </select>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 flex items-center gap-2">
                <User className="h-4 w-4 text-gray-500" />
                Assigned To
              </label>
              <input 
                name="assignedTo" 
                value={formData.assignedTo || ""} 
                onChange={handleChange} 
                placeholder="Name of person assigned" 
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 flex items-center gap-2">
                <User className="h-4 w-4 text-gray-500" />
                Logged By
              </label>
              <input 
                name="loggedBy" 
                value={formData.loggedBy} 
                onChange={handleChange} 
                placeholder="Your name" 
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                required
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 flex items-center gap-2">
                <MapPin className="h-4 w-4 text-gray-500" />
                Location
              </label>
              <input 
                name="location" 
                value={formData.location || ""} 
                onChange={handleChange} 
                placeholder="Physical or system location" 
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-gray-500" />
                Status
              </label>
              <select 
                name="status" 
                value={formData.status} 
                onChange={handleChange} 
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value={OperationStatus.Completed}>{OperationStatus.Completed}</option>
                <option value={OperationStatus.InProgress}>{OperationStatus.InProgress}</option>
                <option value={OperationStatus.NeedsReview}>{OperationStatus.NeedsReview}</option>
              </select>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                Date Occurred
              </label>
              <input 
                name="dateOccurred" 
                type="date" 
                value={formData.dateOccurred} 
                onChange={handleChange} 
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
                required
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                Date Resolved
              </label>
              <input 
                name="dateResolved" 
                type="date" 
                value={formData.dateResolved || ""} 
                onChange={handleChange} 
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 flex items-center gap-2">
                <Tag className="h-4 w-4 text-gray-500" />
                Tags
              </label>
              <input 
                name="tags" 
                value={formData.tags || ""} 
                onChange={handleChange} 
                placeholder="Comma-separated tags" 
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 flex items-center gap-2">
                <Link className="h-4 w-4 text-gray-500" />
                External Links
              </label>
              <input 
                name="externalLinks" 
                value={formData.externalLinks || ""} 
                onChange={handleChange} 
                placeholder="Related URLs" 
                className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 flex items-center gap-2">
              <Upload className="h-4 w-4 text-gray-500" />
              Attachment
            </label>
            <input 
              type="file" 
              onChange={handleFileChange} 
              className="w-full p-2 border rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
            />
            {formData.attachment && (
              <div className="text-sm text-gray-600">
                Current file: {formData.attachment}
              </div>
            )}
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 flex items-center gap-2">
              <FileText className="h-4 w-4 text-gray-500" />
              Notes
            </label>
            <textarea 
              name="notes" 
              value={formData.notes || ""} 
              onChange={handleChange} 
              placeholder="Internal notes or additional information" 
              className="w-full p-2 border rounded h-24 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" 
            />
          </div>
          
          <div className="flex justify-end gap-3">
            <button 
              type="button" 
              onClick={handleCancel}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button 
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  {isEditMode ? 'Updating...' : 'Saving...'}
                </>
              ) : (
                <>{isEditMode ? 'Update Log' : 'Save Log'}</>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ITOperationLogForm;
