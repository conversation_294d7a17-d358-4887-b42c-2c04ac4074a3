import express from 'express';
import * as knowledgeBaseController from '../controllers/knowledgeBaseController';
import * as knowledgeCategoryController from '../controllers/knowledgeCategoryController';
import { auth, authorize } from '../middleware/auth';
import { UserRole } from '../../types/common';

const router = express.Router();

// Knowledge Base Article Routes - Public access for GET endpoints
router.get('/articles', knowledgeBaseController.getAllArticles);
router.get('/articles/:id', knowledgeBaseController.getArticleById);
router.get('/articles/:id/related', knowledgeBaseController.getRelatedArticles);
// Add dedicated endpoint for incrementing view count
router.post('/articles/:id/view', knowledgeBaseController.incrementArticleViewCount);
// Temporarily allow creating articles without authentication for testing
router.post('/articles', knowledgeBaseController.createArticle);
// Remove auth requirement for PUT and DELETE during testing phase
router.put('/articles/:id', knowledgeBaseController.updateArticle);
router.delete('/articles/:id', knowledgeBaseController.deleteArticle);

// Image upload route - remove auth requirement for testing
router.post('/upload-images', knowledgeBaseController.uploadImages);

// Knowledge Base Category Routes - Public access for GET endpoints
router.get('/categories', knowledgeCategoryController.getAllCategories);
router.get('/categories/:id', knowledgeCategoryController.getCategoryById);
// Temporarily allow creating categories without authentication for testing
router.post('/categories', knowledgeCategoryController.createCategory);
// Remove auth requirement for PUT and DELETE during testing phase
router.put('/categories/:id', knowledgeCategoryController.updateCategory);
router.delete('/categories/:id', knowledgeCategoryController.deleteCategory);

export default router; 