import { AppDataSource } from '../../config/database';
import { createAdmin } from '../utils/createAdmin';

async function createTestAdmin() {
  try {
    // Initialize the database connection
    await AppDataSource.initialize();
    console.log("Database connection initialized");

    // Create test admin using the utility
    await createAdmin({
      id: 'IT001',
      email: '<EMAIL>',
      password: 'admin123',
      name: 'Test Admin',
      isTest: true
    });

    console.log('Test admin user created successfully');
  } catch (error) {
    console.error('Error creating test admin:', error);
    throw error;
  } finally {
    // Close the database connection
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log("Database connection closed");
    }
  }
}

// Run the script
if (require.main === module) {
  createTestAdmin().catch(console.error);
} 