import React, { useState, useEffect } from 'react';
import { X, Clock } from 'lucide-react';
import { Shift } from '../../../types/attendance';

interface ShiftFormProps {
  onSubmit: (shift: Omit<Shift, 'id'>) => void;
  onCancel: () => void;
  shiftToEdit?: Shift;
}

const ShiftForm: React.FC<ShiftFormProps> = ({ onSubmit, onCancel, shiftToEdit }) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [startTime, setStartTime] = useState('09:00');
  const [endTime, setEndTime] = useState('17:00');
  const [breakDuration, setBreakDuration] = useState(60);
  const [workingDays, setWorkingDays] = useState<number[]>([1, 2, 3, 4, 5]); // Monday to Friday by default
  const [isFlexible, setIsFlexible] = useState(false);
  const [graceTimeInMinutes, setGraceTimeInMinutes] = useState(15);
  const [requiredWorkHours, setRequiredWorkHours] = useState(8);
  const [color, setColor] = useState('#3B82F6'); // Default blue
  const [isActive, setIsActive] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (shiftToEdit) {
      setName(shiftToEdit.name);
      setDescription(shiftToEdit.description || '');
      setStartTime(shiftToEdit.startTime);
      setEndTime(shiftToEdit.endTime);
      setBreakDuration(shiftToEdit.breakDuration);
      setWorkingDays(shiftToEdit.workingDays);
      setIsFlexible(shiftToEdit.isFlexible);
      setGraceTimeInMinutes(shiftToEdit.graceTimeInMinutes || 15);
      setRequiredWorkHours(shiftToEdit.requiredWorkHours);
      setColor(shiftToEdit.color || '#3B82F6');
      setIsActive(shiftToEdit.isActive);
    }
  }, [shiftToEdit]);

  const validate = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!name.trim()) {
      newErrors.name = 'Shift name is required';
    }
    
    if (!startTime) {
      newErrors.startTime = 'Start time is required';
    }
    
    if (!endTime) {
      newErrors.endTime = 'End time is required';
    }
    
    if (breakDuration < 0) {
      newErrors.breakDuration = 'Break duration cannot be negative';
    }
    
    if (workingDays.length === 0) {
      newErrors.workingDays = 'Select at least one working day';
    }
    
    if (requiredWorkHours <= 0) {
      newErrors.requiredWorkHours = 'Required work hours must be positive';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validate()) {
      return;
    }
    
    const shiftData: Omit<Shift, 'id'> = {
      name,
      description,
      startTime,
      endTime,
      breakDuration,
      workingDays,
      isFlexible,
      graceTimeInMinutes,
      requiredWorkHours,
      color,
      isActive
    };
    
    onSubmit(shiftData);
  };

  const toggleWorkingDay = (day: number) => {
    if (workingDays.includes(day)) {
      setWorkingDays(workingDays.filter(d => d !== day));
    } else {
      setWorkingDays([...workingDays, day].sort());
    }
  };

  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-3xl w-full mx-auto">
        <div className="flex justify-between items-center border-b border-gray-200 px-6 py-4">
          <h2 className="text-xl font-semibold text-gray-900">
            {shiftToEdit ? 'Edit Shift' : 'Create New Shift'}
          </h2>
          <button 
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-500"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <form onSubmit={handleSubmit} className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Shift Name*
                </label>
                <input
                  type="text"
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className={`mt-1 block w-full rounded-md border ${errors.name ? 'border-red-300' : 'border-gray-300'} shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                  placeholder="e.g., Morning Shift"
                />
                {errors.name && <p className="mt-1 text-xs text-red-600">{errors.name}</p>}
              </div>
              
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  Description
                </label>
                <textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={3}
                  className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Optional description of this shift"
                />
              </div>
              
              <div className="flex space-x-4">
                <div className="flex-1">
                  <label htmlFor="startTime" className="block text-sm font-medium text-gray-700">
                    Start Time*
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Clock className="h-4 w-4 text-gray-400" />
                    </div>
                    <input
                      type="time"
                      id="startTime"
                      value={startTime}
                      onChange={(e) => setStartTime(e.target.value)}
                      className={`block w-full pl-10 pr-3 py-2 rounded-md border ${errors.startTime ? 'border-red-300' : 'border-gray-300'} focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                    />
                  </div>
                  {errors.startTime && <p className="mt-1 text-xs text-red-600">{errors.startTime}</p>}
                </div>
                
                <div className="flex-1">
                  <label htmlFor="endTime" className="block text-sm font-medium text-gray-700">
                    End Time*
                  </label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Clock className="h-4 w-4 text-gray-400" />
                    </div>
                    <input
                      type="time"
                      id="endTime"
                      value={endTime}
                      onChange={(e) => setEndTime(e.target.value)}
                      className={`block w-full pl-10 pr-3 py-2 rounded-md border ${errors.endTime ? 'border-red-300' : 'border-gray-300'} focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                    />
                  </div>
                  {errors.endTime && <p className="mt-1 text-xs text-red-600">{errors.endTime}</p>}
                </div>
              </div>
              
              <div className="flex space-x-4">
                <div className="flex-1">
                  <label htmlFor="breakDuration" className="block text-sm font-medium text-gray-700">
                    Break Duration (minutes)
                  </label>
                  <input
                    type="number"
                    id="breakDuration"
                    value={breakDuration}
                    onChange={(e) => setBreakDuration(parseInt(e.target.value))}
                    min="0"
                    className={`mt-1 block w-full rounded-md border ${errors.breakDuration ? 'border-red-300' : 'border-gray-300'} shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                  />
                  {errors.breakDuration && <p className="mt-1 text-xs text-red-600">{errors.breakDuration}</p>}
                </div>
                
                <div className="flex-1">
                  <label htmlFor="requiredWorkHours" className="block text-sm font-medium text-gray-700">
                    Required Work Hours
                  </label>
                  <input
                    type="number"
                    id="requiredWorkHours"
                    value={requiredWorkHours}
                    onChange={(e) => setRequiredWorkHours(parseFloat(e.target.value))}
                    min="0"
                    step="0.5"
                    className={`mt-1 block w-full rounded-md border ${errors.requiredWorkHours ? 'border-red-300' : 'border-gray-300'} shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                  />
                  {errors.requiredWorkHours && <p className="mt-1 text-xs text-red-600">{errors.requiredWorkHours}</p>}
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Working Days
                </label>
                <div className="flex flex-wrap gap-2">
                  {dayNames.map((day, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => toggleWorkingDay(index)}
                      className={`rounded-md px-3 py-1.5 text-sm font-medium ${
                        workingDays.includes(index)
                          ? 'bg-blue-100 text-blue-800 border border-blue-200'
                          : 'bg-gray-100 text-gray-600 border border-gray-200'
                      }`}
                    >
                      {day.slice(0, 3)}
                    </button>
                  ))}
                </div>
                {errors.workingDays && <p className="mt-1 text-xs text-red-600">{errors.workingDays}</p>}
              </div>
              
              <div>
                <label htmlFor="color" className="block text-sm font-medium text-gray-700 mb-2">
                  Shift Color
                </label>
                <div className="flex items-center">
                  <input
                    type="color"
                    id="color"
                    value={color}
                    onChange={(e) => setColor(e.target.value)}
                    className="h-8 w-8 rounded-md border border-gray-300 cursor-pointer"
                  />
                  <span className="ml-2 text-sm text-gray-500">{color}</span>
                </div>
              </div>
              
              <div className="flex space-x-4">
                <div className="flex-1">
                  <label htmlFor="graceTimeInMinutes" className="block text-sm font-medium text-gray-700">
                    Grace Period (minutes)
                  </label>
                  <input
                    type="number"
                    id="graceTimeInMinutes"
                    value={graceTimeInMinutes}
                    onChange={(e) => setGraceTimeInMinutes(parseInt(e.target.value))}
                    min="0"
                    className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>
              
              <div className="flex space-x-6 mt-4">
                <div className="flex items-center">
                  <input
                    id="isFlexible"
                    type="checkbox"
                    checked={isFlexible}
                    onChange={(e) => setIsFlexible(e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isFlexible" className="ml-2 block text-sm text-gray-700">
                    Flexible Timing
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    id="isActive"
                    type="checkbox"
                    checked={isActive}
                    onChange={(e) => setIsActive(e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
                    Active
                  </label>
                </div>
              </div>
            </div>
          </div>
          
          <div className="mt-8 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {shiftToEdit ? 'Update Shift' : 'Create Shift'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ShiftForm; 