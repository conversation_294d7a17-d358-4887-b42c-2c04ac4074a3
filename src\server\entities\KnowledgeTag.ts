import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, <PERSON><PERSON>C<PERSON><PERSON>n, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { KnowledgeBase } from './KnowledgeBase';

@Entity('knowledge_tags')
export class KnowledgeTag {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'varchar', length: 50 })
  name!: string;

  @ManyToOne(() => KnowledgeBase, article => article.tags, {
    onDelete: 'CASCADE',
    nullable: false
  })
  @JoinColumn({ name: 'articleId' })
  article!: KnowledgeBase;

  @Column({ type: 'varchar', length: 36 })
  articleId!: string;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;
} 