import React, { useState, useEffect } from 'react';
import { 
  Building2, 
  FileText, 
  MapPin, 
  Edit, 
  Save, 
  X, 
  Plus,
  Trash2,
  Globe,
  Phone,
  Mail,
  Calendar,
  Users,
  Briefcase
} from 'lucide-react';
import { toast } from 'react-hot-toast';

interface CompanyBasicInfo {
  // Company Identity
  companyName: string;
  companyRegistrationName: string;
  registrationNumber: string;
  incorporationDate: string;
  companyType: string;
  taxId: string;
  
  // Contact Information
  primaryEmail: string;
  primaryPhone: string;
  website: string;
  
  // Legal Address
  legalAddress: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  
  // Business Information
  industry: string;
  businessDescription: string;
  employeeCount: number;
  foundedYear: number;
  
  // Additional Details
  companyLogo: string;
  isActive: boolean;
  lastUpdated: string;
}

interface CompanyBasicInformationProps {
  isEditMode: boolean;
  onSave: (data: CompanyBasicInfo) => void;
  onCancel: () => void;
}

const CompanyBasicInformation: React.FC<CompanyBasicInformationProps> = ({
  isEditMode,
  onSave,
  onCancel
}) => {
  const [formData, setFormData] = useState<CompanyBasicInfo>({
    companyName: 'InfraSpine Technologies',
    companyRegistrationName: 'InfraSpine Technologies Private Limited',
    registrationNumber: 'REG-2023-001234',
    incorporationDate: '2023-01-15',
    companyType: 'Private Limited Company',
    taxId: 'TAX-*********',
    primaryEmail: '<EMAIL>',
    primaryPhone: '+92-300-1234567',
    website: 'https://www.infraspine.com',
    legalAddress: {
      street: '123 Technology Park, Block A',
      city: 'Lahore',
      state: 'Punjab',
      postalCode: '54000',
      country: 'Pakistan'
    },
    industry: 'Information Technology',
    businessDescription: 'Leading provider of IT management solutions and enterprise software development services.',
    employeeCount: 150,
    foundedYear: 2023,
    companyLogo: '/images/infraspine-logo.png',
    isActive: true,
    lastUpdated: new Date().toISOString()
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [saving, setSaving] = useState(false);

  const companyTypes = [
    'Private Limited Company',
    'Public Limited Company',
    'Partnership',
    'Sole Proprietorship',
    'Limited Liability Partnership',
    'Corporation',
    'LLC',
    'Other'
  ];

  const industries = [
    'Information Technology',
    'Software Development',
    'Telecommunications',
    'Financial Services',
    'Healthcare',
    'Manufacturing',
    'Education',
    'Retail',
    'Consulting',
    'Other'
  ];

  const countries = [
    'Pakistan',
    'United States',
    'United Kingdom',
    'Canada',
    'Australia',
    'India',
    'UAE',
    'Saudi Arabia',
    'Other'
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof CompanyBasicInfo],
          [child]: value
        }
      }));
    } else if (type === 'number') {
      setFormData(prev => ({ ...prev, [name]: parseInt(value) || 0 }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.companyName.trim()) {
      newErrors.companyName = 'Company name is required';
    }

    if (!formData.companyRegistrationName.trim()) {
      newErrors.companyRegistrationName = 'Registration name is required';
    }

    if (!formData.registrationNumber.trim()) {
      newErrors.registrationNumber = 'Registration number is required';
    }

    if (!formData.primaryEmail.trim()) {
      newErrors.primaryEmail = 'Primary email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.primaryEmail)) {
      newErrors.primaryEmail = 'Invalid email format';
    }

    if (!formData.legalAddress.street.trim()) {
      newErrors['legalAddress.street'] = 'Street address is required';
    }

    if (!formData.legalAddress.city.trim()) {
      newErrors['legalAddress.city'] = 'City is required';
    }

    if (!formData.legalAddress.country.trim()) {
      newErrors['legalAddress.country'] = 'Country is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error('Please fix the errors before saving');
      return;
    }

    setSaving(true);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const updatedData = {
        ...formData,
        lastUpdated: new Date().toISOString()
      };

      onSave(updatedData);
      toast.success('Company information updated successfully');
    } catch (error) {
      toast.error('Failed to save company information');
    } finally {
      setSaving(false);
    }
  };

  if (!isEditMode) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <Building2 className="h-5 w-5 text-blue-600" />
              Company Information
            </h3>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Company Logo and Basic Info */}
            <div className="lg:col-span-1">
              <div className="text-center">
                {formData.companyLogo ? (
                  <img
                    src={formData.companyLogo}
                    alt={formData.companyName}
                    className="h-24 w-24 mx-auto object-contain bg-gray-50 rounded-lg p-2"
                  />
                ) : (
                  <div className="h-24 w-24 mx-auto bg-blue-100 rounded-lg flex items-center justify-center">
                    <Building2 className="h-12 w-12 text-blue-600" />
                  </div>
                )}
                <h2 className="mt-4 text-xl font-bold text-gray-900">{formData.companyName}</h2>
                <p className="text-gray-600">{formData.industry}</p>
                <div className="mt-4 space-y-2">
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
                    <Calendar className="h-4 w-4" />
                    Founded {formData.foundedYear}
                  </div>
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
                    <Users className="h-4 w-4" />
                    {formData.employeeCount} Employees
                  </div>
                </div>
              </div>
            </div>

            {/* Detailed Information */}
            <div className="lg:col-span-2 space-y-6">
              {/* Legal Information */}
              <div>
                <h4 className="text-md font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Legal Information
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Registration Name</label>
                    <p className="text-gray-900">{formData.companyRegistrationName}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Registration Number</label>
                    <p className="text-gray-900">{formData.registrationNumber}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Company Type</label>
                    <p className="text-gray-900">{formData.companyType}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Incorporation Date</label>
                    <p className="text-gray-900">{new Date(formData.incorporationDate).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Tax ID</label>
                    <p className="text-gray-900">{formData.taxId}</p>
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div>
                <h4 className="text-md font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  Contact Information
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Primary Email</label>
                    <p className="text-gray-900 flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      {formData.primaryEmail}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Primary Phone</label>
                    <p className="text-gray-900 flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      {formData.primaryPhone}
                    </p>
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-500">Website</label>
                    <p className="text-gray-900 flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      <a href={formData.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-700">
                        {formData.website}
                      </a>
                    </p>
                  </div>
                </div>
              </div>

              {/* Address Information */}
              <div>
                <h4 className="text-md font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Legal Address
                </h4>
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-gray-900">
                    {formData.legalAddress.street}<br />
                    {formData.legalAddress.city}, {formData.legalAddress.state} {formData.legalAddress.postalCode}<br />
                    {formData.legalAddress.country}
                  </p>
                </div>
              </div>

              {/* Business Description */}
              <div>
                <h4 className="text-md font-semibold text-gray-900 mb-3 flex items-center gap-2">
                  <Briefcase className="h-4 w-4" />
                  Business Description
                </h4>
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-gray-700 leading-relaxed">{formData.businessDescription}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Building2 className="h-5 w-5 text-blue-600" />
            Edit Company Information
          </h3>
          <div className="flex items-center gap-2">
            <button
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <X className="h-4 w-4 mr-2 inline" />
              Cancel
            </button>
            <button
              onClick={handleSubmit}
              disabled={saving}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  Save Changes
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="p-6 space-y-8">
        {/* Basic Company Information */}
        <div>
          <h4 className="text-md font-semibold text-gray-900 mb-4">Basic Information</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Company Name *
              </label>
              <input
                type="text"
                name="companyName"
                value={formData.companyName}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.companyName ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter company name"
              />
              {errors.companyName && (
                <p className="mt-1 text-sm text-red-600">{errors.companyName}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Industry
              </label>
              <select
                name="industry"
                value={formData.industry}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {industries.map(industry => (
                  <option key={industry} value={industry}>
                    {industry}
                  </option>
                ))}
              </select>
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business Description
              </label>
              <textarea
                name="businessDescription"
                value={formData.businessDescription}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Describe your business"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Founded Year
              </label>
              <input
                type="number"
                name="foundedYear"
                value={formData.foundedYear}
                onChange={handleInputChange}
                min="1900"
                max={new Date().getFullYear()}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Employee Count
              </label>
              <input
                type="number"
                name="employeeCount"
                value={formData.employeeCount}
                onChange={handleInputChange}
                min="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>

        {/* Legal Information */}
        <div>
          <h4 className="text-md font-semibold text-gray-900 mb-4">Legal Information</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Company Registration Name *
              </label>
              <input
                type="text"
                name="companyRegistrationName"
                value={formData.companyRegistrationName}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.companyRegistrationName ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Official registered company name"
              />
              {errors.companyRegistrationName && (
                <p className="mt-1 text-sm text-red-600">{errors.companyRegistrationName}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Registration Number *
              </label>
              <input
                type="text"
                name="registrationNumber"
                value={formData.registrationNumber}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.registrationNumber ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Company registration number"
              />
              {errors.registrationNumber && (
                <p className="mt-1 text-sm text-red-600">{errors.registrationNumber}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Company Type
              </label>
              <select
                name="companyType"
                value={formData.companyType}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {companyTypes.map(type => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Incorporation Date
              </label>
              <input
                type="date"
                name="incorporationDate"
                value={formData.incorporationDate}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tax ID
              </label>
              <input
                type="text"
                name="taxId"
                value={formData.taxId}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Tax identification number"
              />
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div>
          <h4 className="text-md font-semibold text-gray-900 mb-4">Contact Information</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Primary Email *
              </label>
              <input
                type="email"
                name="primaryEmail"
                value={formData.primaryEmail}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.primaryEmail ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="<EMAIL>"
              />
              {errors.primaryEmail && (
                <p className="mt-1 text-sm text-red-600">{errors.primaryEmail}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Primary Phone
              </label>
              <input
                type="tel"
                name="primaryPhone"
                value={formData.primaryPhone}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="******-567-8900"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Website
              </label>
              <input
                type="url"
                name="website"
                value={formData.website}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="https://www.company.com"
              />
            </div>
          </div>
        </div>

        {/* Legal Address */}
        <div>
          <h4 className="text-md font-semibold text-gray-900 mb-4">Legal Address</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Street Address *
              </label>
              <input
                type="text"
                name="legalAddress.street"
                value={formData.legalAddress.street}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors['legalAddress.street'] ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Street address"
              />
              {errors['legalAddress.street'] && (
                <p className="mt-1 text-sm text-red-600">{errors['legalAddress.street']}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                City *
              </label>
              <input
                type="text"
                name="legalAddress.city"
                value={formData.legalAddress.city}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors['legalAddress.city'] ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="City"
              />
              {errors['legalAddress.city'] && (
                <p className="mt-1 text-sm text-red-600">{errors['legalAddress.city']}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                State/Province
              </label>
              <input
                type="text"
                name="legalAddress.state"
                value={formData.legalAddress.state}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="State or Province"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Postal Code
              </label>
              <input
                type="text"
                name="legalAddress.postalCode"
                value={formData.legalAddress.postalCode}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Postal code"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Country *
              </label>
              <select
                name="legalAddress.country"
                value={formData.legalAddress.country}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors['legalAddress.country'] ? 'border-red-300' : 'border-gray-300'
                }`}
              >
                <option value="">Select country</option>
                {countries.map(country => (
                  <option key={country} value={country}>
                    {country}
                  </option>
                ))}
              </select>
              {errors['legalAddress.country'] && (
                <p className="mt-1 text-sm text-red-600">{errors['legalAddress.country']}</p>
              )}
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default CompanyBasicInformation;
