import { AppDataSource } from '../config/database';
import { AddProratedLeaveSettings1703800000000 } from '../migrations/1703800000000-AddProratedLeaveSettings';

async function runProratedLeaveMigration() {
  try {
    console.log('🚀 Initializing database connection...');
    
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
    }
    
    console.log('✅ Database connected successfully');
    
    const migration = new AddProratedLeaveSettings1703800000000();
    const queryRunner = AppDataSource.createQueryRunner();
    
    console.log('🔄 Running prorated leave settings migration...');
    await migration.up(queryRunner);
    
    await queryRunner.release();
    
    console.log('✅ Prorated leave settings migration completed successfully!');
    console.log('📋 Migration details:');
    console.log('   - Ensured settings column exists in leave_type_policies table');
    console.log('   - Updated existing records with default prorated leave settings');
    console.log('   - Added enableProratedLeave field to settings JSON');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
runProratedLeaveMigration(); 