import { DataSource } from 'typeorm';
import { AddHRRoles1747685483671 } from '../migrations/1747685483671-AddHRRoles';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Database configuration
const AppDataSource = new DataSource({
  type: "mysql",
  host: process.env.DB_HOST || "localhost",
  port: parseInt(process.env.DB_PORT || "3306"),
  username: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "root",
  database: process.env.DB_NAME || "ims_db",
  synchronize: false,
  logging: true,
  entities: [__dirname + "/../entities/**/*.{js,ts}"],
  subscribers: [],
  migrations: []
});

const runMigration = async () => {
  console.log("Starting migration to add HR roles...");

  try {
    // Initialize the data source
    await AppDataSource.initialize();
    console.log("Database connection established");

    // Create migration instance
    const migration = new AddHRRoles1747685483671();
    
    // Run the migration
    await migration.up(AppDataSource.createQueryRunner());
    console.log("Migration completed successfully!");
    
    // Close the connection
    await AppDataSource.destroy();
    console.log("Database connection closed");
    
    process.exit(0);
  } catch (error) {
    console.error("Error running migration:", error);
    process.exit(1);
  }
};

// Run the migration
runMigration(); 