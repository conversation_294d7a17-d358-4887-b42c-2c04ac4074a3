import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddDateFieldsToLeaveTypePolicy1703600000000 implements MigrationInterface {
  name = 'AddDateFieldsToLeaveTypePolicy1703600000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add effectiveFrom column
    await queryRunner.addColumn(
      'leave_type_policies',
      new TableColumn({
        name: 'effectiveFrom',
        type: 'date',
        isNullable: true,
      })
    );

    // Add validUntil column
    await queryRunner.addColumn(
      'leave_type_policies',
      new TableColumn({
        name: 'validUntil',
        type: 'date',
        isNullable: true,
      })
    );

    // Add genderEligibility column if it doesn't exist
    const table = await queryRunner.getTable('leave_type_policies');
    const genderColumn = table?.findColumnByName('genderEligibility');
    
    if (!genderColumn) {
      await queryRunner.addColumn(
        'leave_type_policies',
        new TableColumn({
          name: 'genderEligibility',
          type: 'varchar',
          length: '20',
          isNullable: true,
          default: "'all'",
        })
      );
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the added columns
    await queryRunner.dropColumn('leave_type_policies', 'effectiveFrom');
    await queryRunner.dropColumn('leave_type_policies', 'validUntil');
    
    // Check if genderEligibility column exists before dropping
    const table = await queryRunner.getTable('leave_type_policies');
    const genderColumn = table?.findColumnByName('genderEligibility');
    
    if (genderColumn) {
      await queryRunner.dropColumn('leave_type_policies', 'genderEligibility');
    }
  }
} 