import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import EmployeePortal from '../components/HR/employee/EmployeePortal';
import ProfileSettings from '../components/ProfileSettings';
import LoginPage from '../components/LoginPage';
import Register from '../components/Register';
import Dashboard from '../components/Dashboard';
import ProtectedRoute from '../components/ProtectedRoute';
import ServiceDesk from '../components/ServiceDesk';
import TicketsPage from '../components/TicketsPage';
import KnowledgeBase from '../components/KnowledgeBase';
import KnowledgeArticleView from '../components/KnowledgeArticleView';
import KnowledgeArticleEditor from '../components/KnowledgeArticleEditor';
import AssetManagement from '../components/AssetManagement';
import UserManagement from '../components/UserManagement';
import VendorManagement from '../components/VendorManagement';
import SystemAdmin from '../components/SystemAdmin';
import ReportsAnalytics from '../components/ReportsAnalytics';
import BudgetTracking from '../components/BudgetTracking';
import EmployeeManagement from '../components/EmployeeManagement';
import AttendanceManagement from '../components/attendance/AttendanceManagement';
import ConsolidatedLeaveManagement from '../components/attendance/ConsolidatedLeaveManagement';
import PayrollManagement from '../components/payroll/PayrollManagement';
import HRDashboard from '../components/HRDashboard';
import AddEmployeePage from '../pages/HR/AddEmployeePage';
import RecruitmentRoutes from '../components/HR/Recruitment/RecruitmentRoutes';
import LeaveManagementRoutes from './LeaveManagementRoutes';

const AppRoutes: React.FC = () => {
  const { user } = useAuth();

  // Helper function to determine if a user has access to a route
  const hasAccess = (allowedRoles: string[]) => {
    if (!user || !user.role) return false;
    return allowedRoles.includes(user.role);
  };

  return (
    <Routes>
      {/* Authentication Routes */}
      <Route path="/login" element={<LoginPage />} />
      <Route path="/register" element={<Register />} />
      
      {/* Profile */}
      <Route 
        path="/profile" 
        element={
          <ProtectedRoute>
            <ProfileSettings />
          </ProtectedRoute>
        } 
      />

              {/* Employee Portal */}
      <Route
        path="/employee-portal"
        element={
          <ProtectedRoute>
            <EmployeePortal />
          </ProtectedRoute>
        }
      />
      
      {/* IT Service Management Routes */}
      <Route 
        path="/dashboard" 
        element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/service-desk" 
        element={
          <ProtectedRoute>
            <ServiceDesk />
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/tickets" 
        element={
          <ProtectedRoute>
            <TicketsPage />
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/knowledge-base" 
        element={
          <ProtectedRoute>
            <KnowledgeBase />
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/knowledge-base/article/:id" 
        element={
          <ProtectedRoute>
            <KnowledgeArticleView />
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/knowledge-base/edit/:id" 
        element={
          <ProtectedRoute>
            <KnowledgeArticleEditor />
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/knowledge-base/new" 
        element={
          <ProtectedRoute>
            <KnowledgeArticleEditor />
          </ProtectedRoute>
        } 
      />
      
      {/* Asset Management Routes */}
      <Route 
        path="/asset-management" 
        element={
          <ProtectedRoute>
            <AssetManagement />
          </ProtectedRoute>
        } 
      />
      
      {/* HR Management Routes */}
      <Route 
        path="/hr/dashboard" 
        element={
          <ProtectedRoute>
            {hasAccess(['admin', 'hr', 'HR_ADMIN', 'HR_STAFF']) ? <HRDashboard /> : <Navigate to="/dashboard" />}
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/hr/employees" 
        element={
          <ProtectedRoute>
            {hasAccess(['admin', 'hr', 'HR_ADMIN', 'HR_STAFF']) ? <EmployeeManagement /> : <Navigate to="/dashboard" />}
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/hr/addemployee" 
        element={
          <ProtectedRoute>
            {hasAccess(['admin', 'hr', 'HR_ADMIN', 'HR_STAFF']) ? <AddEmployeePage /> : <Navigate to="/dashboard" />}
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/hr/addemployee/:id" 
        element={
          <ProtectedRoute>
            {hasAccess(['admin', 'hr', 'HR_ADMIN', 'HR_STAFF']) ? <AddEmployeePage /> : <Navigate to="/dashboard" />}
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/hr/attendance" 
        element={
          <ProtectedRoute>
            {hasAccess(['admin', 'hr', 'HR_ADMIN', 'HR_STAFF']) ? <AttendanceManagement /> : <Navigate to="/dashboard" />}
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/hr/leave" 
        element={
          <ProtectedRoute>
            {hasAccess(['admin', 'hr', 'HR_ADMIN', 'HR_STAFF']) ? <ConsolidatedLeaveManagement userRole="admin" currentUserId={1} currentUserName="Admin User" /> : <Navigate to="/dashboard" />}
          </ProtectedRoute>
        } 
      />
      <Route
        path="/hr/leave-management/*"
        element={
          <ProtectedRoute>
            {hasAccess(['admin', 'hr', 'HR_ADMIN', 'HR_STAFF']) ? <LeaveManagementRoutes /> : <Navigate to="/dashboard" />}
          </ProtectedRoute>
        }
      />
      <Route
        path="/hr/payroll"
        element={
          <ProtectedRoute>
            {hasAccess(['admin', 'hr']) ? <PayrollManagement /> : <Navigate to="/dashboard" />}
          </ProtectedRoute>
        }
      />
      <Route
        path="/hr/recruitment/*"
        element={
          <ProtectedRoute>
            {hasAccess(['admin', 'hr', 'HR_ADMIN', 'HR_STAFF']) ? <RecruitmentRoutes /> : <Navigate to="/dashboard" />}
          </ProtectedRoute>
        }
      />

      {/* Admin Routes */}
      <Route 
        path="/user-management" 
        element={
          <ProtectedRoute>
            {hasAccess(['admin']) ? <UserManagement /> : <Navigate to="/dashboard" />}
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/vendor-management" 
        element={
          <ProtectedRoute>
            {hasAccess(['admin', 'procurement']) ? <VendorManagement /> : <Navigate to="/dashboard" />}
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/system-admin" 
        element={
          <ProtectedRoute>
            {hasAccess(['admin']) ? <SystemAdmin /> : <Navigate to="/dashboard" />}
          </ProtectedRoute>
        } 
      />
      
      {/* Reporting & Analytics */}
      <Route 
        path="/reports" 
        element={
          <ProtectedRoute>
            {hasAccess(['admin', 'manager', 'hr']) ? <ReportsAnalytics /> : <Navigate to="/dashboard" />}
          </ProtectedRoute>
        } 
      />
      <Route 
        path="/budget" 
        element={
          <ProtectedRoute>
            {hasAccess(['admin', 'finance']) ? <BudgetTracking /> : <Navigate to="/dashboard" />}
          </ProtectedRoute>
        } 
      />
      
      {/* Default route */}
      <Route path="/" element={<Navigate to="/dashboard" />} />
      <Route path="*" element={<Navigate to="/dashboard" />} />
    </Routes>
  );
};

export default AppRoutes; 