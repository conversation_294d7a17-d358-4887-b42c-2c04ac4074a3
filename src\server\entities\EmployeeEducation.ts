import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { Employee } from './Employee';

@Entity('employee_education')
export class EmployeeEducation {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255,  nullable: false })
  educationLevel: string;

  @Column({ type: 'varchar', length: 255,  nullable: false })
  degree: string;

  @Column({ type: 'varchar', length: 255,  nullable: true })
  major: string;

  @Column({ type: 'varchar', length: 255,  nullable: false })
  institution: string;

  @Column({ type: 'varchar', length: 255,  nullable: false })
  graduationYear: string;

  @Column({ type: 'varchar', length: 255,  nullable: true })
  grade: string;

  // Relation to Employee
  @ManyToOne(() => Employee, employee => employee.education, { onDelete: 'CASCADE' })
  @JoinColumn()
  employee: Employee;

  // System columns
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 