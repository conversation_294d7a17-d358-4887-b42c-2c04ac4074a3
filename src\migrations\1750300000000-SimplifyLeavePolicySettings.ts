import { MigrationInterface, QueryRunner } from 'typeorm';

export class SimplifyLeavePolicySettings1750300000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Remove unnecessary columns from leave_policy_settings table
    // Keep only: minDaysNotice, allowLeaveModification, and system fields
    
    // Drop columns that are no longer needed
    const columnsToRemove = [
      'maxConsecutiveDays',
      'allowHalfDay', 
      'requiresApproval',
      'allowNegativeBalance',
      'restrictLeavesTaken',
      'autoApprovalLimit',
      'weekendsBetweenLeavesCount',
      'holidaysBetweenLeavesCount',
      'allowCompensatoryTimeOff',
      'compensatoryExpiryDays',
      'approvalWorkflow',
      'workingHoursPerDay',
      'maxDaysPerRequest',
      'allowBackdatedLeave',
      'requireDocuments',
      'autoCalculateWorkingDays'
    ];

    // Check if table exists before attempting to modify it
    const tableExists = await queryRunner.hasTable('leave_policy_settings');
    if (!tableExists) {
      console.log('Table leave_policy_settings does not exist, skipping column removal');
      return;
    }

    // Remove each column if it exists
    for (const columnName of columnsToRemove) {
      const hasColumn = await queryRunner.hasColumn('leave_policy_settings', columnName);
      if (hasColumn) {
        await queryRunner.dropColumn('leave_policy_settings', columnName);
        console.log(`Dropped column: ${columnName}`);
      } else {
        console.log(`Column ${columnName} does not exist, skipping`);
      }
    }

    console.log('✅ Leave policy settings table simplified successfully');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Re-add the removed columns in case we need to rollback
    const tableExists = await queryRunner.hasTable('leave_policy_settings');
    if (!tableExists) {
      console.log('Table leave_policy_settings does not exist, cannot rollback');
      return;
    }

    // Add back the columns with their default values
    await queryRunner.query(`
      ALTER TABLE leave_policy_settings 
      ADD COLUMN maxConsecutiveDays INT DEFAULT 30,
      ADD COLUMN allowHalfDay BOOLEAN DEFAULT TRUE,
      ADD COLUMN requiresApproval BOOLEAN DEFAULT TRUE,
      ADD COLUMN allowNegativeBalance BOOLEAN DEFAULT FALSE,
      ADD COLUMN restrictLeavesTaken JSON,
      ADD COLUMN autoApprovalLimit INT DEFAULT 0,
      ADD COLUMN weekendsBetweenLeavesCount BOOLEAN DEFAULT FALSE,
      ADD COLUMN holidaysBetweenLeavesCount BOOLEAN DEFAULT FALSE,
      ADD COLUMN allowCompensatoryTimeOff BOOLEAN DEFAULT TRUE,
      ADD COLUMN compensatoryExpiryDays INT DEFAULT 180,
      ADD COLUMN approvalWorkflow ENUM('single', 'multi', 'department') DEFAULT 'single',
      ADD COLUMN workingHoursPerDay INT DEFAULT 8,
      ADD COLUMN maxDaysPerRequest INT DEFAULT 30,
      ADD COLUMN allowBackdatedLeave BOOLEAN DEFAULT FALSE,
      ADD COLUMN requireDocuments BOOLEAN DEFAULT FALSE,
      ADD COLUMN autoCalculateWorkingDays BOOLEAN DEFAULT TRUE
    `);

    console.log('✅ Leave policy settings table columns restored');
  }
} 