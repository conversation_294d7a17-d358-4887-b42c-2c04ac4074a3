import React, { useState, useEffect } from 'react';
import { Search, Calendar, Users, X, Check } from 'lucide-react';
import { Shift } from '../../../types/attendance';

interface Employee {
  id: number;
  firstName: string;
  lastName: string;
  employeeId: string;
  department: string;
  designation: string;
  employmentStatus: string;
  joinDate?: string;
  mobileNumber?: string;
}

export interface ShiftAssignment {
  id?: number;
  employeeId: number;
  shiftId: number;
  startDate: string;
  endDate?: string;
  isPermanent: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface ShiftAssignmentProps {
  shifts: Shift[];
  onCancel: () => void;
  onAssign: (assignments: ShiftAssignment[]) => void;
  assignmentToEdit?: ShiftAssignment;
  employees?: Employee[];
}

const ShiftAssignment: React.FC<ShiftAssignmentProps> = ({ shifts, onCancel, onAssign, assignmentToEdit, employees: passedEmployees }) => {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedEmployees, setSelectedEmployees] = useState<number[]>([]);
  const [selectedShift, setSelectedShift] = useState<number | null>(null);
  const [startDate, setStartDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [endDate, setEndDate] = useState<string>('');
  const [isPermanent, setIsPermanent] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isEditMode, setIsEditMode] = useState(false);

  // Initialize edit mode if assignmentToEdit is provided
  useEffect(() => {
    if (assignmentToEdit) {
      setIsEditMode(true);
      setSelectedEmployees([assignmentToEdit.employeeId]);
      setSelectedShift(assignmentToEdit.shiftId);
      setStartDate(assignmentToEdit.startDate);
      setEndDate(assignmentToEdit.endDate || '');
      setIsPermanent(assignmentToEdit.isPermanent);
    }
  }, [assignmentToEdit]);

  // Use passed employees if in edit mode, otherwise fetch from API
  useEffect(() => {
    if (passedEmployees && passedEmployees.length > 0) {
      setEmployees(passedEmployees);
      setLoading(false);
    } else {
      fetchEmployees();
    }
  }, [passedEmployees]);

  // Fetch real employees from the database
  const fetchEmployees = async () => {
    try {
      console.log('Fetching employees for shift assignment...');
      const response = await fetch('/api/employees/listing');
      const data = await response.json();
      
      if (data.success && data.employees) {
        console.log(`Fetched ${data.employees.length} employees for shift assignment`);
        
        // Filter to only include active employees
        const activeEmployees = data.employees.filter((emp: any) => {
          const status = emp.status || emp.employmentStatus || 'active';
          return status.toLowerCase() === 'active';
        });
        
        // Map to the Employee interface
        const mappedEmployees = activeEmployees.map((emp: any) => ({
          id: emp.id,
          firstName: emp.firstName || '',
          lastName: emp.lastName || '',
          employeeId: emp.employeeId || `EMP${emp.id}`,
          department: emp.job?.department || emp.department || 'N/A',
          designation: emp.job?.designation || emp.designation || 'N/A',
          employmentStatus: emp.status || emp.employmentStatus || 'active',
          joinDate: emp.joinDate || emp.joiningDate,
          mobileNumber: emp.mobileNumber || emp.phoneNumber
        }));
        
        console.log(`Mapped ${mappedEmployees.length} active employees for assignment`);
        setEmployees(mappedEmployees);
      } else {
        console.error('Failed to fetch employees:', data.message);
        setErrors({ fetch: 'Failed to load employees: ' + (data.message || 'Unknown error') });
      }
    } catch (err) {
      console.error('Error fetching employees:', err);
      setErrors({ fetch: 'Network error while loading employees' });
    } finally {
      setLoading(false);
    }
  };

  const filteredEmployees = employees.filter(employee => {
    const fullName = `${employee.firstName} ${employee.lastName}`.toLowerCase();
    const searchLower = searchTerm.toLowerCase();
    
    return fullName.includes(searchLower) || 
           employee.employeeId.toLowerCase().includes(searchLower) ||
           employee.department.toLowerCase().includes(searchLower) ||
           employee.designation.toLowerCase().includes(searchLower);
  });
  
  const toggleEmployeeSelection = (employeeId: number) => {
    if (selectedEmployees.includes(employeeId)) {
      setSelectedEmployees(selectedEmployees.filter(id => id !== employeeId));
    } else {
      setSelectedEmployees([...selectedEmployees, employeeId]);
    }
  };
  
  const selectAllEmployees = () => {
    if (selectedEmployees.length === filteredEmployees.length) {
      setSelectedEmployees([]);
    } else {
      setSelectedEmployees(filteredEmployees.map(emp => emp.id));
    }
  };
  
  const validate = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (selectedEmployees.length === 0) {
      newErrors.employees = 'Select at least one employee';
    }
    
    if (selectedShift === null) {
      newErrors.shift = 'Select a shift';
    }
    
    if (!startDate) {
      newErrors.startDate = 'Start date is required';
    }
    
    if (!isPermanent && !endDate) {
      newErrors.endDate = 'End date is required for temporary assignments';
    }
    
    if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
      newErrors.dateRange = 'End date must be after start date';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validate()) {
      return;
    }
    
    const assignments: ShiftAssignment[] = selectedEmployees.map(employeeId => ({
      employeeId,
      shiftId: selectedShift!,
      startDate,
      endDate: isPermanent ? undefined : endDate,
      isPermanent
    }));
    
    onAssign(assignments);
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-6xl w-full mx-auto max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center border-b border-gray-200 dark:border-gray-700 px-6 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700">
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              {isEditMode ? 'Edit Shift Assignment' : 'Assign Shifts to Employees'}
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {isEditMode 
                ? 'Modify the shift assignment details' 
                : 'Select employees and assign them to work shifts'
              }
            </p>
          </div>
          <button 
            onClick={onCancel}
            className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 p-2 rounded-lg transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
          <form onSubmit={handleSubmit} className="px-6 py-6">
            {/* Error Display */}
            {errors.fetch && (
              <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-800">{errors.fetch}</p>
              </div>
            )}
            
            <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
              {/* Employee Selection - Left Side (3 columns) */}
              <div className="lg:col-span-3">
                {isEditMode ? (
                  // Edit mode: Show current employee in read-only mode
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      Employee
                    </label>
                    <div className="border border-gray-200 rounded-xl overflow-hidden shadow-sm bg-gray-50">
                      <div className="px-4 py-6">
                        {(() => {
                          const employee = employees.find(emp => emp.id === assignmentToEdit?.employeeId);
                          return employee ? (
                            <div className="flex items-center">
                              <div className="h-12 w-12 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center text-white font-semibold text-sm mr-4">
                                {employee.firstName.charAt(0)}{employee.lastName.charAt(0)}
                              </div>
                              <div>
                                <div className="text-lg font-semibold text-gray-900">
                                  {employee.firstName} {employee.lastName}
                                </div>
                                <div className="text-sm text-gray-500 font-mono">
                                  {employee.employeeId}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {employee.department} • {employee.designation}
                                </div>
                              </div>
                            </div>
                          ) : (
                            <p className="text-gray-500">Employee not found</p>
                          );
                        })()}
                      </div>
                    </div>
                  </div>
                ) : (
                  // Create mode: Show employee selection interface
                  <>
                    <div className="mb-6">
                      <label className="block text-sm font-semibold text-gray-700 mb-3">
                        Select Employees
                      </label>
                      
                      {/* Search Box */}
                      <div className="relative mb-4">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Search className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                          type="text"
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          placeholder="Search by name, ID, department..."
                          className="block w-full pl-11 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm placeholder-gray-400"
                        />
                      </div>
                      {errors.employees && <p className="mb-3 text-sm text-red-600 font-medium">{errors.employees}</p>}
                    </div>
                    
                    {/* Employee List */}
                    <div className="border border-gray-200 rounded-xl overflow-hidden shadow-sm">
                      {/* Header */}
                      <div className="bg-gray-50 px-4 py-3 border-b border-gray-200 flex justify-between items-center">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            checked={selectedEmployees.length === filteredEmployees.length && filteredEmployees.length > 0}
                            onChange={selectAllEmployees}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            disabled={filteredEmployees.length === 0}
                          />
                          <span className="ml-3 text-sm font-semibold text-gray-700">
                            {selectedEmployees.length} of {filteredEmployees.length} selected
                          </span>
                        </div>
                        <div className="flex items-center text-gray-500">
                          <Users className="h-4 w-4 mr-1" />
                          <span className="text-xs">{filteredEmployees.length} employees</span>
                        </div>
                      </div>
                      
                      {/* Employee Table */}
                      <div className="max-h-80 overflow-y-auto">
                        {loading ? (
                          <div className="flex justify-center items-center py-12">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                            <span className="ml-3 text-gray-600">Loading employees...</span>
                          </div>
                        ) : filteredEmployees.length === 0 ? (
                          <div className="text-center py-12 text-gray-500">
                            <Users className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                            <p className="text-lg font-medium">No employees found</p>
                            <p className="text-sm">Try adjusting your search criteria</p>
                          </div>
                        ) : (
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50 sticky top-0">
                              <tr>
                                <th scope="col" className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                  SELECT
                                </th>
                                <th scope="col" className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                  EMPLOYEE
                                </th>
                                <th scope="col" className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                  DEPARTMENT
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {filteredEmployees.map(employee => (
                                <tr 
                                  key={employee.id} 
                                  className={`hover:bg-blue-50 cursor-pointer transition-colors ${
                                    selectedEmployees.includes(employee.id) ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                                  }`}
                                  onClick={() => toggleEmployeeSelection(employee.id)}
                                >
                                  <td className="px-4 py-2 whitespace-nowrap">
                                    <input
                                      type="checkbox"
                                      checked={selectedEmployees.includes(employee.id)}
                                      onChange={(e) => {
                                        e.stopPropagation();
                                        toggleEmployeeSelection(employee.id);
                                      }}
                                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                  </td>
                                  <td className="px-4 py-2 whitespace-nowrap">
                                    <div className="flex items-center">
                                      <div className="h-8 w-8 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center text-white font-semibold text-xs mr-3">
                                        {employee.firstName.charAt(0)}{employee.lastName.charAt(0)}
                                      </div>
                                      <div>
                                        <div className="text-sm font-semibold text-gray-900">
                                          {employee.firstName} {employee.lastName}
                                        </div>
                                        <div className="text-xs text-gray-500 font-mono">
                                          {employee.employeeId}
                                        </div>
                                      </div>
                                    </div>
                                  </td>
                                  <td className="px-4 py-2 whitespace-nowrap">
                                    <div className="text-sm font-medium text-gray-900">{employee.department}</div>
                                    <div className="text-xs text-gray-500">{employee.designation}</div>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        )}
                      </div>
                    </div>
                  </>
                )}
              </div>
              
              {/* Assignment Details - Right Side (2 columns) */}
              <div className="lg:col-span-2">
                <div className="bg-gray-50 rounded-xl p-6 h-full">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">Assignment Details</h3>
                  
                  {/* Shift Selection */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      Select Shift
                    </label>
                    <select
                      value={selectedShift === null ? '' : selectedShift}
                      onChange={(e) => setSelectedShift(e.target.value ? parseInt(e.target.value) : null)}
                      className={`block w-full px-4 py-3 border ${
                        errors.shift ? 'border-red-300 bg-red-50' : 'border-gray-300 bg-white'
                      } rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm`}
                    >
                      <option value="">Select a shift</option>
                      {shifts.map(shift => (
                        <option key={shift.id} value={shift.id}>
                          {shift.name} ({shift.startTime} - {shift.endTime})
                        </option>
                      ))}
                    </select>
                    {errors.shift && <p className="mt-2 text-sm text-red-600 font-medium">{errors.shift}</p>}
                  </div>
                  
                  {/* Date Range */}
                  <div className="mb-6">
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      Assignment Date Range
                    </label>
                    <div className="space-y-3">
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Calendar className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          type="date"
                          value={startDate}
                          onChange={(e) => setStartDate(e.target.value)}
                          className={`block w-full pl-10 pr-3 py-3 border ${
                            errors.startDate ? 'border-red-300 bg-red-50' : 'border-gray-300 bg-white'
                          } rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm`}
                          placeholder="Start Date"
                        />
                      </div>
                      
                      <div className="flex items-center justify-center">
                        <span className="text-gray-400 text-sm font-medium">to</span>
                      </div>
                      
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Calendar className="h-4 w-4 text-gray-400" />
                        </div>
                        <input
                          type="date"
                          value={endDate}
                          onChange={(e) => setEndDate(e.target.value)}
                          className={`block w-full pl-10 pr-3 py-3 border ${
                            errors.endDate ? 'border-red-300 bg-red-50' : 'border-gray-300 bg-white'
                          } rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm ${
                            isPermanent ? 'opacity-50 cursor-not-allowed' : ''
                          }`}
                          disabled={isPermanent}
                          placeholder={isPermanent ? "Permanent assignment" : "End Date"}
                        />
                      </div>
                    </div>
                    
                    {errors.dateRange && <p className="mt-2 text-sm text-red-600 font-medium">{errors.dateRange}</p>}
                    {errors.startDate && <p className="mt-2 text-sm text-red-600 font-medium">{errors.startDate}</p>}
                    {errors.endDate && <p className="mt-2 text-sm text-red-600 font-medium">{errors.endDate}</p>}
                  </div>
                  
                  {/* Permanent Assignment */}
                  <div className="mb-8">
                    <div className="flex items-start">
                      <input
                        id="isPermanent"
                        type="checkbox"
                        checked={isPermanent}
                        onChange={(e) => setIsPermanent(e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                      />
                      <div className="ml-3">
                        <label htmlFor="isPermanent" className="block text-sm font-semibold text-gray-700">
                          Permanent Assignment
                        </label>
                        <p className="text-xs text-gray-500 mt-1">
                          If enabled, this shift will be assigned indefinitely
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  {/* Assignment Preview */}
                  {selectedEmployees.length > 0 && selectedShift !== null && (
                    <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <h4 className="text-sm font-semibold text-blue-900 mb-2">
                        {isEditMode ? 'Assignment Changes' : 'Assignment Preview'}
                      </h4>
                      <p className="text-sm text-blue-800">
                        {isEditMode ? 'Updating' : 'Assigning'} <span className="font-semibold">{shifts.find(s => s.id === selectedShift)?.name}</span> to{' '}
                        <span className="font-semibold">{selectedEmployees.length}</span> employee(s)
                      </p>
                      <p className="text-xs text-blue-600 mt-1">
                        {isPermanent ? 'Permanent assignment' : `From ${startDate} to ${endDate || 'not specified'}`}
                      </p>
                    </div>
                  )}
                  
                  {/* Submit Button */}
                  <button 
                    type="submit"
                    className="w-full py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-semibold text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={selectedEmployees.length === 0 || selectedShift === null}
                  >
                    <Check className="h-4 w-4 mr-2" />
                    {isEditMode 
                      ? 'Update Assignment' 
                      : `Assign Shift to ${selectedEmployees.length} Employee${selectedEmployees.length !== 1 ? 's' : ''}`
                    }
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ShiftAssignment; 