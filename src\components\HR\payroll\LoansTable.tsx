import React, { useState } from 'react';
import {
  <PERSON><PERSON>ard,
  Edit,
  Trash2,
  ChevronRight,
  CheckCircle,
  AlertTriangle,
  Clock,
  AlertCircle,
  XCircle
} from 'lucide-react';
import { Loan, LoanStatus, LoanType } from '../../../types/payroll';

interface LoansTableProps {
  loans: Loan[];
  isAdmin?: boolean;
  onEditLoan?: (loan: Loan) => void;
  onDeleteLoan?: (loanId: number) => void;
  onViewLoan?: (loan: Loan) => void;
}

const LoansTable: React.FC<LoansTableProps> = ({
  loans,
  isAdmin = false,
  onEditLoan,
  onDeleteLoan,
  onViewLoan
}) => {
  // Function to format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'PKR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Get status badge styles based on loan status
  const getStatusBadge = (status: LoanStatus) => {
    switch (status) {
      case LoanStatus.PENDING:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="mr-1 h-3 w-3" />
            Pending
          </span>
        );
      case LoanStatus.APPROVED:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="mr-1 h-3 w-3" />
            Approved
          </span>
        );
      case LoanStatus.ACTIVE:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <CreditCard className="mr-1 h-3 w-3" />
            Active
          </span>
        );
      case LoanStatus.COMPLETED:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
            <CheckCircle className="mr-1 h-3 w-3" />
            Completed
          </span>
        );
      case LoanStatus.REJECTED:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircle className="mr-1 h-3 w-3" />
            Rejected
          </span>
        );
      case LoanStatus.DEFAULTED:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <AlertTriangle className="mr-1 h-3 w-3" />
            Defaulted
          </span>
        );
      case LoanStatus.CANCELED:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            <XCircle className="mr-1 h-3 w-3" />
            Canceled
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  // Get loan type display name
  const getLoanTypeLabel = (type: LoanType) => {
    switch (type) {
      case LoanType.PERSONAL:
        return 'Personal';
      case LoanType.EDUCATION:
        return 'Education';
      case LoanType.HOUSING:
        return 'Housing';
      case LoanType.MEDICAL:
        return 'Medical';
      case LoanType.VEHICLE:
        return 'Vehicle';
      case LoanType.EMERGENCY:
        return 'Emergency';
      case LoanType.OTHER:
        return 'Other';
      default:
        return type;
    }
  };

  if (!loans || loans.length === 0) {
    return (
      <div className="bg-white shadow overflow-hidden sm:rounded-md p-6 text-center">
        <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No loans</h3>
        <p className="mt-1 text-sm text-gray-500">
          There are no loans to display.
        </p>
        {isAdmin && (
          <div className="mt-6">
            <button
              type="button"
              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              New Loan
            </button>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-md">
      <ul className="divide-y divide-gray-200">
        {loans.map((loan) => (
          <li key={loan.id}>
            <div 
              className="block hover:bg-gray-50 cursor-pointer"
              onClick={() => onViewLoan && onViewLoan(loan)}
            >
              <div className="px-4 py-4 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <p className="text-sm font-medium text-blue-600 truncate">
                      {loan.employeeName}
                    </p>
                    <p className="ml-2 text-sm text-gray-500">
                      ({getLoanTypeLabel(loan.loanType)} Loan)
                    </p>
                  </div>
                  <div className="ml-2 flex-shrink-0 flex">
                    {getStatusBadge(loan.status)}
                  </div>
                </div>
                <div className="mt-2 sm:flex sm:justify-between">
                  <div className="sm:flex">
                    <p className="flex items-center text-sm text-gray-500">
                      Amount: {formatCurrency(loan.amount)}
                    </p>
                    <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                      Remaining: {formatCurrency(loan.remainingAmount)}
                    </p>
                    <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                      {formatDate(loan.startDate)} - {formatDate(loan.endDate)}
                    </p>
                  </div>
                  <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                    {isAdmin && (
                      <div className="flex space-x-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onEditLoan && onEditLoan(loan);
                          }}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        {(loan.status === LoanStatus.PENDING || loan.status === LoanStatus.APPROVED) && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              onDeleteLoan && onDeleteLoan(loan.id);
                            }}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    )}
                    <ChevronRight className="ml-2 h-5 w-5 text-gray-400" />
                  </div>
                </div>
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default LoansTable; 