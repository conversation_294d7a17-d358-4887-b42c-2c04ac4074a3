import React from 'react';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { ChartData } from '../../types/dashboard';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface Props {
  priorityStats: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
}

export const PriorityBarChart: React.FC<Props> = ({ priorityStats }) => {
  const data = {
    labels: Object.keys(priorityStats).map(p => p.charAt(0).toUpperCase() + p.slice(1).toLowerCase()),
    datasets: [
      {
        label: 'Tickets',
        data: Object.values(priorityStats),
        backgroundColor: [
          'rgba(34, 197, 94, 0.9)',  // Low - Green
          'rgba(245, 158, 11, 0.9)', // Medium - Amber
          'rgba(249, 115, 22, 0.9)', // High - Orange
          'rgba(239, 68, 68, 0.9)',  // Critical - Red
        ],
        borderWidth: 2,
        borderRadius: 6,
        borderColor: [
          'rgb(21, 128, 61)',  // Dark Green
          'rgb(180, 83, 9)',   // Dark Amber
          'rgb(194, 65, 12)',  // Dark Orange
          'rgb(185, 28, 28)',  // Dark Red
        ],
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Tickets by Priority',
        padding: {
          top: 10,
          bottom: 20
        },
        font: {
          size: 16,
          weight: 'bold' as const
        }
      },
      tooltip: {
        callbacks: {
          label: (context: any) => {
            const value = context.raw;
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${context.label}: ${value} (${percentage}%)`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)',
          drawBorder: false,
        },
        ticks: {
          padding: 8,
          font: {
            size: 12
          }
        }
      },
      x: {
        grid: {
          display: false
        },
        ticks: {
          padding: 8,
          font: {
            size: 12,
            weight: 'bold' as const
          }
        }
      }
    },
    layout: {
      padding: {
        top: 20,
        right: 20,
        bottom: 20,
        left: 20
      }
    }
  };

  return (
    <div style={{ height: '300px', width: '100%', padding: '16px' }}>
      <Bar data={data} options={options} />
    </div>
  );
}; 