import { AppDataSource } from '../config/database';
import { Asset } from '../entities/Asset';

/**
 * <PERSON>ript to check if the asset table exists in the database
 * Run with: npx ts-node src/scripts/checkAssetTable.ts
 */
async function checkAssetTable() {
  try {
    console.log('Starting database connection...');
    await AppDataSource.initialize();
    console.log('Database connection established');

    // Check if asset table exists
    console.log('Checking if asset table exists...');
    const tables = await AppDataSource.query(`
      SELECT TABLE_NAME 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = DATABASE() 
      AND TABLE_NAME = 'assets'
    `);
    
    if (tables.length > 0) {
      console.log('Asset table exists');
      
      // Count assets in the table
      const assetRepo = AppDataSource.getRepository(Asset);
      const count = await assetRepo.count();
      console.log(`Asset table contains ${count} assets`);
      
      // List all assets
      if (count > 0) {
        const assets = await assetRepo.find({ 
          select: ['id', 'assetTag', 'assetType', 'manufacturer', 'model', 'serialNumber'] 
        });
        console.log('Assets in database:');
        console.table(assets.map(a => ({ 
          id: a.id, 
          assetTag: a.assetTag,
          type: a.assetType,
          model: a.model,
          serialNumber: a.serialNumber
        })));
      }
    } else {
      console.log('Asset table does not exist');
    }

    // Check if asset routes are registered
    console.log('Checking asset routes...');
    try {
      const assetRoutes = require('../server/routes/assetRoutes');
      console.log('Asset routes module exists');
      
      // Check if assetController exists
      const assetController = require('../server/controllers/assetController');
      console.log('Asset controller module exists');
      
      // Check if getAssets method exists
      if (assetController.assetController && assetController.assetController.getAssets) {
        console.log('getAssets method exists in assetController');
      } else {
        console.log('getAssets method does not exist in assetController');
      }
    } catch (error) {
      console.error('Error checking asset routes:', error);
    }

    process.exit(0);
  } catch (error) {
    console.error('Error checking asset table:', error);
    process.exit(1);
  }
}

// Run the function
checkAssetTable(); 