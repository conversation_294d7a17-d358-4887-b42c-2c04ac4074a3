import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { LeavePolicyConfiguration } from './LeavePolicyConfiguration';
import { Holiday } from './Holiday';

@Entity('holiday_calendars')
export class HolidayCalendar {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 100 })
  name: string;

  @Column({ type: 'int' })
  year: number;

  @Column({ type: 'json', nullable: true })
  applicableDepartments: string[];

  @Column({ type: 'json', nullable: true })
  applicableLocations: string[];

  @Column({ type: 'int' })
  policyConfigurationId: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => LeavePolicyConfiguration, config => config.holidayCalendars)
  @JoinColumn({ name: 'policyConfigurationId' })
  policyConfiguration: LeavePolicyConfiguration;

  @OneToMany(() => Holiday, holiday => holiday.calendar)
  holidays: Holiday[];
} 