export enum UserRole {
  // System Administration
  SYSTEM_ADMIN = 'SYSTEM_ADMIN',
  
  // HR Management Roles
  HR_ADMIN = 'HR_ADMIN',
  HR_STAFF = 'HR_STAFF',
  HR_SPECIALIST = 'HR_SPECIALIST',
  
  // IT Management Roles
  IT_ADMIN = 'IT_ADMIN',
  IT_STAFF = 'IT_STAFF',
  IT_SUPPORT = 'IT_SUPPORT',
  IT_TECHNICIAN = 'IT_TECHNICIAN',
  
  // Management Roles
  DEPT_HEAD = 'DEPT_HEAD',
  PROJECT_MANAGER = 'PROJECT_MANAGER',
  FINANCE_MANAGER = 'FINANCE_MANAGER',
  
  // General Staff
  SENIOR_EMPLOYEE = 'SENIOR_EMPLOYEE',
  EMPLOYEE = 'EMPLOYEE',
  
  // Specialized Roles
  SECURITY_OFFICER = 'SECURITY_OFFICER',
  
  // Dashboard and Analytics
  DASHBOARD_MANAGER = 'DASHBOARD_MANAGER',
  ANALYTICS_VIEWER = 'ANALYTICS_VIEWER',
  
  // Legacy support (can be removed later)
  CEO = 'CEO',
  VIEW = 'VIEW',
  ADMIN = 'ADMIN'
}

export enum TicketStatus {
  OPEN = 'OPEN',
  IN_PROGRESS = 'IN_PROGRESS',
  RESOLVED = 'RESOLVED'
}

export enum TicketPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export type StatusFilter = 'all' | TicketStatus;

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  department?: string;
  permissions?: UserPermissions;
}

export interface UserPermissions {
  // Ticket Management Permissions
  canCreateTickets: boolean;
  canCreateTicketsForOthers: boolean;
  canEditTickets: boolean;
  canDeleteTickets: boolean;
  canCloseTickets: boolean;
  canAssignTickets: boolean;
  canEscalateTickets: boolean;
  canViewAllTickets: boolean;
  canLockTickets: boolean;
  
  // HR Permissions
  canCreateEmployee: boolean;
  canEditEmployee: boolean;
  canDeleteEmployee: boolean;
  canViewEmployees: boolean;
  canManageAttendance: boolean;
  canManageLeave: boolean;
  canManagePayroll: boolean;
  canManagePerformance: boolean;
  
  // System Administration Permissions
  canAddUsers: boolean;
  canEditUsers: boolean;
  canDeleteUsers: boolean;
  canViewReports: boolean;
  canExportData: boolean;
  canImportData: boolean;
  canConfigureSystem: boolean;
  canManageRoles: boolean;
  canApproveRequests: boolean;
  canViewAllDepartments: boolean;
  canAccessAllModules: boolean;
  
  // Dashboard Permissions
  canViewDashboards: boolean;
  canViewAllDashboards: boolean;
  canCreateDashboards: boolean;
  canEditDashboards: boolean;
  canDeleteDashboards: boolean;
  canShareDashboards: boolean;
  canExportDashboardData: boolean;
  canConfigureDashboardAlerts: boolean;
  canManageDashboardUsers: boolean;
  
  // Basic Properties
  department: string;
  isAdmin: boolean;
}

export interface Attachment {
  id: string;
  fileName: string;
  fileUrl: string;
  fileType: string;
  uploadedBy: {
    id: string;
    name: string;
    role: UserRole;
  };
  uploadedAt: string;
  commentId?: number;
}

export interface Ticket {
  id: number;
  ticketNumber: string;
  title: string;
  description: string;
  status: TicketStatus;
  priority: TicketPriority;
  category: string;
  department: string;
  departmentChain: string[];
  visibleTo: string[];
  location?: string;
  project?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: {
    id: string;
    name: string;
    email: string;
    department: string;
    role: string;
  };
  assignedTo?: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  assignedToId?: string | null;
  lockedBy?: {
    id: string;
    name: string;
    email: string;
    role: string;
  } | null;
  lockedById?: string | null;
  lockedAt?: string | null;
  comments: Comment[];
  attachments: Attachment[];
  workflow?: {
    currentStage: string;
    history: {
      stage: string;
      timestamp: string;
      by: string;
      role: string;
      comment: string;
    }[];
  };
}

export interface Comment {
  id: string | number;
  content: string;
  type: 'system' | 'user';
  createdAt: string;
  updatedAt: string;
  ticketId: number;
  createdBy: {
    id: string;
    name: string;
    role: UserRole;
  };
  isSystemComment: boolean;
  attachments: Array<{
    id: string;
    fileName: string;
    fileUrl: string;
    fileType: string;
    uploadedBy: {
      id: string;
      name: string;
      role: UserRole;
    };
    uploadedAt: string;
  }>;
  status?: 'sent' | 'delivered' | 'read';
  readBy?: Array<{
    id: string;
    name: string;
  }>;
  reactions?: Array<{
    emoji: string;
    users: Array<{
      id: string;
      name: string;
    }>;
  }>;
  replyTo?: {
    id: number;
    content: string;
    createdBy: {
      id: string;
      name: string;
    };
  };
}

export interface ExtendedComment extends Omit<Comment, 'id'> {
  id: string | number;
}

export interface TempComment {
  id: string;
  content: string;
  type?: 'system' | 'user';
  createdAt: string;
  updatedAt?: string;
  createdBy: {
    id: string;
    name: string;
    role: UserRole;
  };
  ticketId: number;
  isSystemComment: boolean;
  attachments: Attachment[];
} 