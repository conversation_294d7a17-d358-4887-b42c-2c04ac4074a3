import React, { useState, useRef } from 'react';
import { X, Upload, AlertCircle, Check } from 'lucide-react';
import * as XLSX from 'xlsx';
import EmployeeService from '../../../services/EmployeeService';
import { toast } from 'react-hot-toast';

interface EmployeeImportProps {
  onImport: (data: any[]) => void;
  onClose: () => void;
}

const EmployeeImport: React.FC<EmployeeImportProps> = ({ onImport, onClose }) => {
  const [file, setFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const [importedData, setImportedData] = useState<any[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files ? e.target.files[0] : null;
    if (selectedFile) {
      // Check if the file is of valid type
      const validTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel', 'text/csv'];
      if (!validTypes.includes(selectedFile.type)) {
        setError('Please upload a valid file (Excel or CSV)');
        setFile(null);
        e.target.value = '';
        return;
      }
      
      setFile(selectedFile);
      setError(null);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    
    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile) {
      // Check if the file is of valid type
      const validTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel', 'text/csv'];
      if (!validTypes.includes(droppedFile.type)) {
        setError('Please upload a valid file (Excel or CSV)');
        return;
      }
      
      setFile(droppedFile);
      setError(null);
    }
  };

  const handleImport = async () => {
    if (!file) {
      setError('Please select a file first');
      return;
    }
    
    setLoading(true);
    setError(null);
    
    try {
      const reader = new FileReader();
      
      reader.onload = async (e) => {
        try {
          const result = e.target?.result;
          let data: any[] = [];
          
          if (typeof result === 'string' && file.name.endsWith('.csv')) {
            // Process CSV
            data = parseCSV(result);
          } else if (result) {
            // Process Excel
            const workbook = XLSX.read(result, { type: 'binary' });
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            data = XLSX.utils.sheet_to_json(worksheet);
          }
          
          console.log('Extracted data:', data.length, 'records');
          
          // Convert imported data to Employee format
          const employeeData = data.map((item, index) => {
            return {
              // Basic personal info
              firstName: item.firstName || item.FirstName || item['First Name'] || '',
              lastName: item.lastName || item.LastName || item['Last Name'] || '',
              middleName: item.middleName || item.MiddleName || item['Middle Name'] || '',
              gender: item.gender || item.Gender || '',
              dateOfBirth: item.dateOfBirth || item.DateOfBirth || item['Date of Birth'] || '',
              religion: item.religion || item.Religion || '',
              bloodType: item.bloodType || item.BloodType || item['Blood Type'] || item.bloodGroup || item.BloodGroup || item['Blood Group'] || '',
              
              // Identification
              employeeId: item.employeeId || item.EmployeeId || item['ID'] || item['Employee ID'] || '',
              cnicNumber: item.cnicNumber || item.CnicNumber || item['CNIC Number'] || '',
              cnicExpiryDate: item.cnicExpiryDate || item.CnicExpiryDate || item['CNIC Expiry Date'] || '',
              nationality: item.nationality || item.Nationality || '',
              maritalStatus: item.maritalStatus || item.MaritalStatus || item['Marital Status'] || '',
              
              // Additional Personal Info
              passportNumber: item.passportNumber || item.PassportNumber || item['Passport Number'] || '',
              
              // Contact Info
              mobileNumber: item.mobileNumber || item.MobileNumber || item['Mobile Number'] || '',
              officialNumber: item.officialNumber || item.OfficialNumber || item['Official Number'] || '',
              officialEmail: item.officialEmail || item.OfficialEmail || item['Official Email'] || '',
              personalEmail: item.personalEmail || item.PersonalEmail || item['Personal Email'] || '',
              currentAddress: item.currentAddress || item.CurrentAddress || item['Current Address'] || '',
              permanentAddress: item.permanentAddress || item.PermanentAddress || item['Permanent Address'] || '',
              emergencyContactName: item.emergencyContactName || item.EmergencyContactName || item['Emergency Contact Name'] || '',
              emergencyContactPhone: item.emergencyContactPhone || item.EmergencyContactPhone || item['Emergency Contact Phone'] || '',
              emergencyContactRelationship: item.emergencyContactRelationship || item.EmergencyContactRelationship || item['Emergency Contact Relationship'] || '',
              
              // Job Details
              department: item.department || item.Department || '',
              designation: item.designation || item.Designation || item.Position || item['Position'] || '',
              employmentStatus: item.employmentStatus || item.EmploymentStatus || item['Employment Status'] || 'active',
              employmentType: item.employmentType || item.EmploymentType || item['Employment Type'] || 'full-time',
              employeeLevel: item.employeeLevel || item.EmployeeLevel || item['Employee Level'] || '',
              project: item.project || item.Project || '',
              location: item.location || item.Location || '',
              joinDate: item.joinDate || item.JoinDate || item['Join Date'] || '',
              probationEndDate: item.probationEndDate || item.ProbationEndDate || item['Probation End Date'] || '',
              noticePeriod: item.noticePeriod || item.NoticePeriod || item['Notice Period'] || '',
              reportingTo: item.reportingTo || item.ReportingTo || item['Reporting To'] || '',
              confirmationDate: item.confirmationDate || item.ConfirmationDate || item['Confirmation Date'] || '',
              contractEndDate: item.contractEndDate || item.ContractEndDate || item['Contract End Date'] || '',
              workLocation: item.workLocation || item.WorkLocation || item['Work Location'] || '',
              remoteWork: item.remoteWork === 'true' || item.remoteWork === true || item.RemoteWork === 'true' || item.RemoteWork === 'Yes' || item['Remote Work'] === 'Yes' || false,
              
              // Attendance & Leave
              lastLeaveTaken: item.lastLeaveTaken || item.LastLeaveTaken || item['Last Leave Taken'] || '',
              
              // Compensation & Benefits
              salary: item.salary || item.Salary || '',
              salaryTier: item.salaryTier || item.SalaryTier || item['Salary Tier'] || '',
              paymentMode: item.paymentMode || item.PaymentMode || item['Payment Mode'] || '',
              bankName: item.bankName || item.BankName || item['Bank Name'] || '',
              bankBranch: item.bankBranch || item.BankBranch || item['Bank Branch'] || '',
              accountNumber: item.accountNumber || item.AccountNumber || item['Account Number'] || '',
              accountTitle: item.accountTitle || item.AccountTitle || item['Account Title'] || '',
              iban: item.iban || item.IBAN || item['IBAN'] || '',
              cashAmount: item.cashAmount || item.CashAmount || item['Cash Amount'] || '',
              bankAmount: item.bankAmount || item.BankAmount || item['Bank Amount'] || '',
              totalSalary: item.totalSalary || item.TotalSalary || item['Total Salary'] || '',
              
              // Allowances
              foodAllowanceInSalary: item.foodAllowanceInSalary === 'true' || item.foodAllowanceInSalary === true || 
                item.FoodAllowanceInSalary === 'true' || item['Food Allowance In Salary'] === 'Yes' || false,
              foodProvidedByCompany: item.foodProvidedByCompany === 'true' || item.foodProvidedByCompany === true || 
                item.FoodProvidedByCompany === 'true' || item['Food Provided By Company'] === 'Yes' || false,
              numberOfMeals: item.numberOfMeals || item.NumberOfMeals || item['Number Of Meals'] || '',
              fuelAllowanceInSalary: item.fuelAllowanceInSalary === 'true' || item.fuelAllowanceInSalary === true || 
                item.FuelAllowanceInSalary === 'true' || item['Fuel Allowance In Salary'] === 'Yes' || false,
              fuelInLiters: item.fuelInLiters || item.FuelInLiters || item['Fuel In Liters'] || '',
              fuelAmount: item.fuelAmount || item.FuelAmount || item['Fuel Amount'] || '',
              
              // Accommodation Details
              accommodationProvidedByEmployer: item.accommodationProvidedByEmployer === 'true' || item.accommodationProvidedByEmployer === true || 
                item.AccommodationProvidedByEmployer === 'true' || item['Accommodation Provided By Employer'] === 'Yes' || false,
              accommodationType: item.accommodationType || item.AccommodationType || item['Accommodation Type'] || '',
              accommodationAddress: item.accommodationAddress || item.AccommodationAddress || item['Accommodation Address'] || '',
              
              // Skills & Certifications
              professionalSkills: item.professionalSkills || item.ProfessionalSkills || item['Professional Skills'] || '',
              technicalSkills: item.technicalSkills || item.TechnicalSkills || item['Technical Skills'] || '',
              certifications: item.certifications || item.Certifications || '',
              languages: item.languages || item.Languages || '',
              
              // Health & Insurance Info
              healthInsuranceProvider: item.healthInsuranceProvider || item.HealthInsuranceProvider || item['Health Insurance Provider'] || '',
              healthInsurancePolicyNumber: item.healthInsurancePolicyNumber || item.HealthInsurancePolicyNumber || item['Health Insurance Policy Number'] || '',
              healthInsuranceExpiryDate: item.healthInsuranceExpiryDate || item.HealthInsuranceExpiryDate || item['Health Insurance Expiry Date'] || '',
              lifeInsuranceProvider: item.lifeInsuranceProvider || item.LifeInsuranceProvider || item['Life Insurance Provider'] || '',
              lifeInsurancePolicyNumber: item.lifeInsurancePolicyNumber || item.LifeInsurancePolicyNumber || item['Life Insurance Policy Number'] || '',
              lifeInsuranceExpiryDate: item.lifeInsuranceExpiryDate || item.LifeInsuranceExpiryDate || item['Life Insurance Expiry Date'] || '',
              vaccinationRecords: item.vaccinationRecords || item.VaccinationRecords || item['Vaccination Records'] || '',
              medicalHistory: item.medicalHistory || item.MedicalHistory || item['Medical History'] || '',
              
              // Vehicle Details
              vehicleType: item.vehicleType || item.VehicleType || item['Vehicle Type'] || '',
              registrationNumber: item.registrationNumber || item.RegistrationNumber || item['Registration Number'] || '',
              providedByCompany: item.providedByCompany === 'true' || item.providedByCompany === true || 
                item.ProvidedByCompany === 'true' || item['Provided By Company'] === 'Yes' || false,
              handingOverDate: item.handingOverDate || item.HandingOverDate || item['Handing Over Date'] || '',
              returnDate: item.returnDate || item.ReturnDate || item['Return Date'] || '',
              vehicleMakeModel: item.vehicleMakeModel || item.VehicleMakeModel || item['Vehicle Make Model'] || '',
              vehicleColor: item.vehicleColor || item.VehicleColor || item['Vehicle Color'] || '',
              mileageAtIssuance: item.mileageAtIssuance || item.MileageAtIssuance || item['Mileage At Issuance'] || '',
              
              // Professional/Work-Related Info
              workSchedule: item.workSchedule || item.WorkSchedule || item['Work Schedule'] || '',
              shiftType: item.shiftType || item.ShiftType || item['Shift Type'] || '',
              remoteWorkEligible: item.remoteWorkEligible === 'true' || item.remoteWorkEligible === true || 
                item.RemoteWorkEligible === 'true' || item['Remote Work Eligible'] === 'Yes' || false,
              nextReviewDate: item.nextReviewDate || item.NextReviewDate || item['Next Review Date'] || '',
              trainingRequirements: item.trainingRequirements || item.TrainingRequirements || item['Training Requirements'] || '',
              
              // Family Information
              spouseName: item.spouseName || item.SpouseName || item['Spouse Name'] || '',
              spouseDateOfBirth: item.spouseDateOfBirth || item.SpouseDateOfBirth || item['Spouse Date Of Birth'] || '',
              spouseOccupation: item.spouseOccupation || item.SpouseOccupation || item['Spouse Occupation'] || '',
              spouseEmployer: item.spouseEmployer || item.SpouseEmployer || item['Spouse Employer'] || '',
              spouseContactNumber: item.spouseContactNumber || item.SpouseContactNumber || item['Spouse Contact Number'] || '',
              spouseCNIC: item.spouseCNIC || item.SpouseCNIC || item['Spouse CNIC'] || '',
              
              // Additional metadata
              notes: item.notes || item.Notes || '',
              specialInstructions: item.specialInstructions || item.SpecialInstructions || item['Special Instructions'] || '',
              updatedAt: item.updatedAt || item.UpdatedAt || item['Updated At'] || '',
              
              // Education Information
              educationDegree: item.educationDegree || item.EducationDegree || item['Degree'] || '',
              educationInstitution: item.educationInstitution || item.EducationInstitution || item['Institute'] || '',
              educationGraduationYear: item.educationGraduationYear || item.EducationGraduationYear || item['Year of Graduation'] || '',
              educationLevel: item.educationLevel || item.EducationLevel || item['Education Level'] || '',
              educationMajor: item.educationMajor || item.EducationMajor || item['Major/Field'] || '',
              educationGPA: item.educationGPA || item.EducationGPA || item['GPA'] || '',
              
              // Create an educationEntries array with the education data
              educationEntries: [{
                degree: item.educationDegree || item.EducationDegree || item['Degree'] || '',
                institution: item.educationInstitution || item.EducationInstitution || item['Institute'] || '',
                graduationYear: item.educationGraduationYear || item.EducationGraduationYear || item['Year of Graduation'] || '',
                educationLevel: item.educationLevel || item.EducationLevel || item['Education Level'] || '',
                major: item.educationMajor || item.EducationMajor || item['Major/Field'] || '',
                grade: item.educationGPA || item.EducationGPA || item['GPA'] || ''
              }],
            };
          });
          
          console.log('Converted data to Employee format:', employeeData.length, 'records');
          
          // Submit data to backend
          const { data: response, error } = await EmployeeService.importEmployees(employeeData);
          
          if (error) {
            console.error('Import error:', error);
            setError(error);
            setLoading(false);
            return;
          }
          
          console.log('Import response:', response);
          
          // Show success details
          setSuccess(true);
          const successCount = response?.importedEmployees?.length || 0;
          const failCount = response?.failedImports?.length || 0;
          
          toast.success(
            `Import completed: ${successCount} employees imported${failCount > 0 ? `, ${failCount} failed` : ''}`
          );
          
          // If there are failures, show more detailed information
          if (failCount > 0) {
            console.warn('Failed imports:', response?.failedImports);
            
            // Log the first few failures to help diagnose issues
            const sampleFailures = response?.failedImports?.slice(0, 5) || [];
            console.log('Sample failures:');
            sampleFailures.forEach((failure: any, index: number) => {
              console.log(`Failure ${index + 1}:`, {
                name: `${failure.firstName} ${failure.lastName}`,
                id: failure.employeeId || 'N/A',
                error: failure.error
              });
            });
            
            // Group errors by type to help identify patterns
            const errorTypes: Record<string, number> = {};
            response?.failedImports?.forEach((failure: any) => {
              const error = failure.error || 'Unknown error';
              errorTypes[error] = (errorTypes[error] || 0) + 1;
            });
            
            console.log('Error breakdown by type:', errorTypes);
            
            // Show toast with the most common error
            const mostCommonError = Object.entries(errorTypes)
              .sort((a: [string, number], b: [string, number]) => b[1] - a[1])[0];
              
            if (mostCommonError) {
              toast.error(
                `Most common error (${mostCommonError[1]} occurrences): ${mostCommonError[0]}`,
                { duration: 6000 }
              );
            } else {
              toast.error(
                'Some imports failed. Check console for details.',
                { duration: 5000 }
              );
            }
          }
          
          // Provide feedback on the outcome
          if (successCount > 0) {
            toast.success(
              'Successfully imported employee data including personal details, job info, and more.',
              { duration: 5000 }
            );
          } else if (failCount > 0) {
            toast.error(
              'All imports failed. Please check the data format and try again.',
              { duration: 5000 }
            );
          }
          
          setImportedData(response?.importedEmployees || []);
          onImport(response?.importedEmployees || []);
          
          // Only close on success, otherwise let user see the error
          if (successCount > 0) {
            setTimeout(() => {
              onClose();
            }, 3000);
          } else {
            setLoading(false);
          }
        } catch (error) {
          console.error('Error processing file:', error);
          setError('Error processing file. Please check the format and try again.');
          setLoading(false);
        }
      };
      
      reader.onerror = () => {
        console.error('Error reading file');
        setError('Error reading file');
        setLoading(false);
      };
      
      reader.readAsBinaryString(file);
    } catch (error) {
      console.error('Error importing data:', error);
      setError('An unexpected error occurred');
      setLoading(false);
    }
  };

  // Helper function to parse CSV
  const parseCSV = (csv: string): any[] => {
    // Normalize line endings to handle different formats (CRLF, CR, LF)
    const normalizedCsv = csv.replace(/\r\n|\r|\n/g, '\n');
    const lines = normalizedCsv.split('\n');
    const result: any[] = [];
    
    console.log(`CSV parsing: Found ${lines.length} lines (including header)`);
    
    // Helper function to safely parse CSV rows
    const parseCSVRow = (row: string) => {
      const values: string[] = [];
      let inQuotes = false;
      let currentValue = '';
      
      // Handle CSV with potential quotes and commas inside quotes
      for (let i = 0; i < row.length; i++) {
        const char = row[i];
        
        if (char === '"' && (i === 0 || row[i-1] !== '\\')) {
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          values.push(currentValue.trim());
          currentValue = '';
        } else {
          currentValue += char;
        }
      }
      
      // Add the last value
      values.push(currentValue.trim());
      return values;
    };
    
    // Parse headers using the same robust method
    const headers = parseCSVRow(lines[0]);
    console.log('CSV headers:', headers);
    
    // Helper function to clean up and normalize field values
    const normalizeValue = (value: string, fieldName: string): any => {
      // Handle empty values
      if (!value || value.trim() === '') {
        return '';
      }
      
      // Special handling for boolean fields (Yes/No, true/false)
      if (/Provided|Allowance|Eligible|remote/i.test(fieldName)) {
        if (/yes|true|1/i.test(value)) {
          return 'true';
        } else if (/no|false|0/i.test(value)) {
          return 'false';
        }
      }
      
      // Special handling for date fields
      if (/date|birth|expiry|join/i.test(fieldName)) {
        // Try to parse various date formats
        try {
          // Handle Excel date format (DD-MMM-YYYY)
          if (/\d{1,2}-[A-Za-z]{3}-\d{4}/.test(value)) {
            const dateParts = value.split('-');
            const day = parseInt(dateParts[0], 10);
            const month = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec']
              .indexOf(dateParts[1].toLowerCase().substring(0, 3));
            const year = parseInt(dateParts[2], 10);
            if (month !== -1) {
              const date = new Date(year, month, day);
              return date.toISOString().split('T')[0]; // YYYY-MM-DD
            }
          }
          
          // Try to parse as a date and return in ISO format
          const date = new Date(value);
          if (!isNaN(date.getTime())) {
            return date.toISOString().split('T')[0]; // YYYY-MM-DD
          }
        } catch (error) {
          console.warn(`Failed to parse date: ${value}`, error);
        }
      }
      
      // For numeric fields, ensure they're stored as strings
      if (/salary|amount|balance|rate/i.test(fieldName)) {
        // Remove any non-numeric characters except . and -
        const numericValue = value.replace(/[^0-9.-]/g, '');
        if (numericValue && !isNaN(Number(numericValue))) {
          return numericValue;
        }
      }
      
      return value;
    };
    
    // Process each line starting from the first data row (line 1)
    for (let i = 1; i < lines.length; i++) {
      if (!lines[i].trim()) continue; // Skip empty lines
      
      const obj: any = {};
      const currentLineValues = parseCSVRow(lines[i]);
      
      // Assign headers to values
      for (let j = 0; j < headers.length && j < currentLineValues.length; j++) {
        const header = headers[j];
        obj[header] = normalizeValue(currentLineValues[j], header);
      }
      
      result.push(obj);
    }
    
    console.log(`CSV parsing complete: Extracted ${result.length} records`);
    if (result.length > 0) {
      console.log('Sample first record:', result[0]);
    }
    
    return result;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md">
        <div className="flex justify-between items-center border-b border-gray-200 dark:border-gray-700 p-4">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white">Import Employees</h2>
          <button 
            onClick={onClose} 
            className="text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="p-4">
          <div 
            className={`border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center ${
              file ? 'border-green-400 bg-green-50 dark:bg-green-900/20' : 'border-gray-300 dark:border-gray-600'
            }`}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            {file ? (
              <div className="text-center">
                <Check className="h-8 w-8 text-green-500 mx-auto mb-2" />
                <p className="text-sm font-medium text-gray-900 dark:text-white">{file.name}</p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {(file.size / 1024).toFixed(2)} KB
                </p>
                <button
                  onClick={() => setFile(null)}
                  className="mt-2 text-xs text-red-600 dark:text-red-400 hover:underline"
                >
                  Remove
                </button>
              </div>
            ) : (
              <>
                <Upload className="h-8 w-8 text-gray-400 mb-2" />
                <p className="text-sm font-medium text-gray-900 dark:text-white">Drag & drop a file here</p>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Supports Excel (.xlsx, .xls) and CSV files
                </p>
                <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                  Maximum file size: 50MB
                </p>
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="mt-3 px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded"
                >
                  Browse Files
                </button>
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  accept=".xlsx,.xls,.csv"
                  className="hidden"
                />
              </>
            )}
          </div>
          
          {error && (
            <div className="mt-3 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded flex items-start">
              <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400 mr-2 mt-0.5 flex-shrink-0" />
              <p className="text-xs text-red-600 dark:text-red-400">{error}</p>
            </div>
          )}
          
          <div className="mt-4 flex gap-3 justify-end">
            <button
              onClick={onClose}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Cancel
            </button>
            <button
              onClick={handleImport}
              disabled={!file || loading}
              className={`px-3 py-2 rounded text-sm font-medium text-white ${
                !file || loading 
                  ? 'bg-blue-400 dark:bg-blue-500 cursor-not-allowed' 
                  : 'bg-blue-600 hover:bg-blue-700'
              }`}
            >
              {loading ? (
                <>
                  <span className="inline-block h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
                  Importing...
                </>
              ) : (
                'Import'
              )}
            </button>
          </div>
          
          {success && (
            <div className="mt-3 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded flex items-center">
              <Check className="h-5 w-5 text-green-600 dark:text-green-400 mr-2" />
              <p className="text-sm text-green-600 dark:text-green-400">
                Successfully imported {importedData.length} employees!
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EmployeeImport; 