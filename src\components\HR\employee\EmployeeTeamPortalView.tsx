import React, { useState, useEffect } from 'react';
import { Users, UserCheck, ClipboardList, Calendar, Clock, X, CheckCircle, Building2, Mail, Award, Grid, List, AlertCircle, Home, Filter } from 'lucide-react';
import { Tab } from '@headlessui/react';
import EmployeeAttendanceModal from '../attendance/EmployeeAttendanceModal';
import LeaveRequestsTable from '../leave/LeaveRequestsTable';
import { EmployeeTeamData } from '../../../services/EmployeePortalService';
import employeePortalService from '../../../services/EmployeePortalService';
import { Attendance, AttendanceStatus, LeaveRequest, LeaveStatus } from '../../../types/attendance';

interface EmployeeTeamPortalViewProps {
  teamData: EmployeeTeamData | null;
  employeeId?: number;
}

interface TeamMember {
  id: number;
  name: string;
  code?: string;
  email?: string;
  department?: string;
  position?: string;
  photo?: string;
}

// Team Attendance Overview Component (unchanged from your version)
interface TeamAttendanceOverviewProps {
  teamMembers: TeamMember[];
  onViewEmployeeAttendance: (member: TeamMember) => void;
}

const TeamAttendanceOverview: React.FC<TeamAttendanceOverviewProps> = ({
  teamMembers,
  onViewEmployeeAttendance
}) => {
  const [teamAttendanceData, setTeamAttendanceData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Filter states
  const [filters, setFilters] = useState({
    datePeriod: 'today',
    status: 'all',
    location: 'all',
    department: 'all',
    shift: 'all',
    workHours: 'all',
    searchText: ''
  });

  // Get unique values for filter dropdowns from actual attendance data
  const uniqueDepartments = [...new Set(
    teamAttendanceData.flatMap(member =>
      member.availableDepartments || [member.department]
    ).filter(Boolean)
  )];

  const uniqueLocations = [...new Set(
    teamAttendanceData.flatMap(member =>
      member.availableLocations || []
    ).filter(Boolean)
  )];

  const uniqueShifts = [...new Set(
    teamAttendanceData.flatMap(member =>
      member.availableShifts || []
    ).filter(Boolean)
  )];

  // Get unique status values from actual attendance records
  const uniqueStatuses = [...new Set(
    teamAttendanceData.flatMap(member =>
      member.attendanceRecords?.map((record: Attendance) => record.status) || [member.todayStatus]
    ).filter(Boolean)
  )];

  // Date period calculation function (same as EmployeeAttendanceModal)
  const getDateRangeFromPeriod = (period: string) => {
    const today = new Date();
    const startOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);

    switch (period) {
      case 'today':
        return { start: startOfToday, end: endOfToday };
      case 'yesterday':
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        return { start: new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate()),
                 end: new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59) };
      case 'this_week':
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay());
        return { start: new Date(startOfWeek.getFullYear(), startOfWeek.getMonth(), startOfWeek.getDate()),
                 end: endOfToday };
      case 'last_week':
        const lastWeekStart = new Date(today);
        lastWeekStart.setDate(today.getDate() - today.getDay() - 7);
        const lastWeekEnd = new Date(lastWeekStart);
        lastWeekEnd.setDate(lastWeekStart.getDate() + 6);
        return { start: new Date(lastWeekStart.getFullYear(), lastWeekStart.getMonth(), lastWeekStart.getDate()),
                 end: new Date(lastWeekEnd.getFullYear(), lastWeekEnd.getMonth(), lastWeekEnd.getDate(), 23, 59, 59) };
      case 'this_month':
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        return { start: startOfMonth, end: endOfToday };
      case 'last_month':
        const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0, 23, 59, 59);
        return { start: lastMonthStart, end: lastMonthEnd };
      case 'last_three_months':
        const threeMonthsStart = new Date(today.getFullYear(), today.getMonth() - 3, 1);
        return { start: threeMonthsStart, end: endOfToday };
      case 'last_six_months':
        const sixMonthsStart = new Date(today.getFullYear(), today.getMonth() - 6, 1);
        return { start: sixMonthsStart, end: endOfToday };
      case 'this_year':
        const startOfYear = new Date(today.getFullYear(), 0, 1);
        return { start: startOfYear, end: endOfToday };
      case 'last_year':
        const lastYearStart = new Date(today.getFullYear() - 1, 0, 1);
        const lastYearEnd = new Date(today.getFullYear() - 1, 11, 31, 23, 59, 59);
        return { start: lastYearStart, end: lastYearEnd };
      default:
        return null;
    }
  };

  // Apply filters to team attendance data
  const filteredTeamData = teamAttendanceData.filter(member => {
    // Date period filter - filter attendance records first, then check if member has any records in period
    let relevantRecords = member.attendanceRecords || [];
    if (filters.datePeriod !== 'all') {
      const dateRange = getDateRangeFromPeriod(filters.datePeriod);
      if (dateRange) {
        relevantRecords = relevantRecords.filter((record: Attendance) => {
          const recordDate = new Date(record.date);
          return recordDate >= dateRange.start && recordDate <= dateRange.end;
        });
        // If no records in the selected period, exclude this member
        if (relevantRecords.length === 0) return false;
      }
    }

    // Status filter - check against today's status or records in the filtered period
    if (filters.status !== 'all') {
      if (filters.datePeriod === 'today' || filters.datePeriod === 'all') {
        // For today or all time, check today's status
        if (member.todayStatus?.toLowerCase() !== filters.status.toLowerCase()) return false;
      } else {
        // For other periods, check if any record in the period matches the status
        const hasMatchingStatus = relevantRecords.some((record: Attendance) =>
          record.status?.toLowerCase() === filters.status.toLowerCase()
        );
        if (!hasMatchingStatus) return false;
      }
    }

    // Location filter
    if (filters.location !== 'all') {
      if (filters.location === 'office' || filters.location === 'remote') {
        // Check against work from home status
        const hasMatchingLocation = relevantRecords.some((record: Attendance) => {
          if (filters.location === 'remote') {
            return record.status === 'work_from_home' || record.isRemote;
          } else {
            return record.status !== 'work_from_home' && !record.isRemote;
          }
        });
        if (!hasMatchingLocation) return false;
      } else {
        // Check against specific location
        const hasMatchingLocation = relevantRecords.some((record: Attendance) =>
          record.location === filters.location
        );
        if (!hasMatchingLocation) return false;
      }
    }

    // Department filter
    if (filters.department !== 'all' && member.department !== filters.department) return false;

    // Shift filter
    if (filters.shift !== 'all') {
      const hasMatchingShift = relevantRecords.some((record: Attendance) => {
        let shiftName = record.shiftName;
        if (!shiftName && record.shift) {
          const shiftNames = {
            1: 'Morning Shift',
            2: 'Evening Shift',
            3: 'Night Shift',
            4: 'Flexible Hours'
          };
          shiftName = shiftNames[record.shift as keyof typeof shiftNames] || `Shift ${record.shift}`;
        }
        return shiftName === filters.shift;
      });
      if (!hasMatchingShift) return false;
    }

    // Work hours filter
    if (filters.workHours !== 'all') {
      const workHours = parseFloat(member.totalWorkHours || '0');
      switch (filters.workHours) {
        case 'full_day':
          if (workHours < 7) return false;
          break;
        case 'partial_day':
          if (workHours >= 7) return false;
          break;
        case 'overtime':
          if (workHours <= 8) return false;
          break;
      }
    }

    // Search filter
    if (filters.searchText) {
      const searchLower = filters.searchText.toLowerCase();
      const nameMatch = member.name?.toLowerCase().includes(searchLower);
      const deptMatch = member.department?.toLowerCase().includes(searchLower);
      const positionMatch = member.position?.toLowerCase().includes(searchLower);
      if (!nameMatch && !deptMatch && !positionMatch) return false;
    }

    return true;
  });

  useEffect(() => {
    if (teamMembers.length > 0) {
      loadTeamAttendanceOverview();
    }
    // eslint-disable-next-line
  }, [teamMembers.map(m => m.id).join(',')]);

  const loadTeamAttendanceOverview = async () => {
    setIsLoading(true);
    try {
      const attendancePromises = teamMembers.map(async (member) => {
        try {
          const endDate = new Date().toISOString().split('T')[0];
          const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
          const attendanceData = await employeePortalService.getEmployeeAttendance(member.id, startDate, endDate);
          const records = attendanceData?.records || [];
          const presentDays = records.filter(r => r.status === 'PRESENT').length;
          const lateDays = records.filter(r => r.status === 'LATE').length;
          const absentDays = records.filter(r => r.status === 'ABSENT').length;
          const totalWorkHours = records.reduce((sum, r) => sum + (r.workHours || 0), 0);
          const today = new Date().toISOString().split('T')[0];
          const todayRecord = records.find(r => r.date === today);

          // Extract additional data from attendance records for filtering
          const departments = [...new Set(records.map(r => r.department || r.employeeDepartment).filter(Boolean))];
          const locations = [...new Set(records.map(r => r.location).filter(Boolean))];
          const shifts = [...new Set(records.map(r => {
            if (r.shiftName) return r.shiftName;
            if (r.shift) {
              const shiftNames = { 1: 'Morning Shift', 2: 'Evening Shift', 3: 'Night Shift', 4: 'Flexible Hours' };
              return shiftNames[r.shift as keyof typeof shiftNames] || `Shift ${r.shift}`;
            }
            return null;
          }).filter(Boolean))];

          return {
            ...member,
            presentDays,
            lateDays,
            absentDays,
            totalWorkHours: totalWorkHours.toFixed(1),
            totalDays: records.length,
            checkInTime: todayRecord?.checkInTime || '-',
            checkOutTime: todayRecord?.checkOutTime || '-',
            todayStatus: todayRecord?.status || 'NOT_MARKED',
            // Store attendance records and extracted filter data
            attendanceRecords: records,
            availableDepartments: departments,
            availableLocations: locations,
            availableShifts: shifts
          };
        } catch (error) {
          return {
            ...member,
            presentDays: 0,
            lateDays: 0,
            absentDays: 0,
            totalWorkHours: '0.0',
            totalDays: 0,
            checkInTime: '-',
            checkOutTime: '-',
            todayStatus: 'NOT_MARKED',
            attendanceRecords: [],
            availableDepartments: [],
            availableLocations: [],
            availableShifts: []
          };
        }
      });
      const results = await Promise.all(attendancePromises);
      setTeamAttendanceData(results);
    } catch (error) {
      // error log
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-6">Team Attendance Overview</h3>

        {/* --- Team Summary Cards (ERP style) --- */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-2 mb-6">
          {/* Calculate summary stats */}
          {(() => {
            const totalMembers = teamAttendanceData.length;
            const totalDays = teamAttendanceData.reduce((sum, m) => sum + (m.totalDays || 0), 0) || 1;
            const presentDays = teamAttendanceData.reduce((sum, m) => sum + (m.presentDays || 0), 0);
            const lateDays = teamAttendanceData.reduce((sum, m) => sum + (m.lateDays || 0), 0);
            const absentDays = teamAttendanceData.reduce((sum, m) => sum + (m.absentDays || 0), 0);
            const workHours = teamAttendanceData.reduce((sum, m) => sum + parseFloat(m.totalWorkHours || '0'), 0);
            // Overtime: sum of hours above 8/day
            let overTime = 0;
            let remoteWork = 0;
            teamAttendanceData.forEach(m => {
              // Overtime: assume work hours above 8/day per present/late day
              // Here, we can't get per-day, so just estimate: (workHours - 8*presentDays)
              const memberOvertime = Math.max(0, parseFloat(m.totalWorkHours || '0') - 8 * (m.presentDays + m.lateDays));
              overTime += memberOvertime;
              // Remote: count as present if todayStatus is 'WORK_FROM_HOME' (if available)
              // For simplicity, count remote as 0 (unless you track remote days in data)
            });
            // If you have remote days in data, sum here. Otherwise, leave as 0.
            // For demo, remoteWork = 0.

            return [
              // Present Days
              <div key="present" className="bg-white rounded-lg shadow-sm p-3 border-l-4 border-blue-500 min-h-[78px] flex flex-col justify-between">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs font-medium text-gray-500">Present Days</p>
                    <div className="flex items-baseline mt-1">
                      <p className="text-lg font-bold text-gray-900">{presentDays}</p>
                      <p className="ml-1 text-xs text-green-500">{totalDays ? ((presentDays/totalDays)*100).toFixed(0) : 0}%</p>
                    </div>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-blue-50 flex items-center justify-center">
                    <CheckCircle className="h-4 w-4 text-blue-500" />
                  </div>
                </div>
              </div>,
              // Late Days
              <div key="late" className="bg-white rounded-lg shadow-sm p-3 border-l-4 border-yellow-500 min-h-[78px] flex flex-col justify-between">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs font-medium text-gray-500">Late Days</p>
                    <div className="flex items-baseline mt-1">
                      <p className="text-lg font-bold text-gray-900">{lateDays}</p>
                      <p className="ml-1 text-xs text-yellow-500">{totalDays ? ((lateDays/totalDays)*100).toFixed(0) : 0}%</p>
                    </div>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-yellow-50 flex items-center justify-center">
                    <Clock className="h-4 w-4 text-yellow-500" />
                  </div>
                </div>
              </div>,
              // Absent Days
              <div key="absent" className="bg-white rounded-lg shadow-sm p-3 border-l-4 border-red-500 min-h-[78px] flex flex-col justify-between">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs font-medium text-gray-500">Absent Days</p>
                    <div className="flex items-baseline mt-1">
                      <p className="text-lg font-bold text-gray-900">{absentDays}</p>
                      <p className="ml-1 text-xs text-red-500">{totalDays ? ((absentDays/totalDays)*100).toFixed(0) : 0}%</p>
                    </div>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-red-50 flex items-center justify-center">
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  </div>
                </div>
              </div>,
              // Work Hours
              <div key="workhours" className="bg-white rounded-lg shadow-sm p-3 border-l-4 border-green-500 min-h-[78px] flex flex-col justify-between">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs font-medium text-gray-500">Work Hours</p>
                    <div className="flex items-baseline mt-1">
                      <p className="text-lg font-bold text-gray-900">{workHours.toFixed(1)}</p>
                      <p className="ml-1 text-xs text-gray-500">total</p>
                    </div>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-green-50 flex items-center justify-center">
                    <Clock className="h-4 w-4 text-green-500" />
                  </div>
                </div>
              </div>,
              // Over Time
              <div key="overtime" className="bg-white rounded-lg shadow-sm p-3 border-l-4 border-orange-500 min-h-[78px] flex flex-col justify-between">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs font-medium text-gray-500">Over Time</p>
                    <div className="flex items-baseline mt-1">
                      <p className="text-lg font-bold text-gray-900">{overTime.toFixed(1)}</p>
                      <p className="ml-1 text-xs text-orange-500">hours</p>
                    </div>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-orange-50 flex items-center justify-center">
                    <Clock className="h-4 w-4 text-orange-500" />
                  </div>
                </div>
              </div>,
              // Remote Work (set to 0 for now)
              <div key="remote" className="bg-white rounded-lg shadow-sm p-3 border-l-4 border-purple-500 min-h-[78px] flex flex-col justify-between">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs font-medium text-gray-500">Remote Work</p>
                    <div className="flex items-baseline mt-1">
                      <p className="text-lg font-bold text-gray-900">{remoteWork}</p>
                      <p className="ml-1 text-xs text-purple-500">0%</p>
                    </div>
                  </div>
                  <div className="h-8 w-8 rounded-full bg-purple-50 flex items-center justify-center">
                    <Home className="h-4 w-4 text-purple-500" />
                  </div>
                </div>
              </div>,
            ];
          })()}
        </div>

        {/* --- End Team Summary Cards --- */}

        {/* Filters Section */}
        <div className="mb-4 bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <Filter className="h-4 w-4 text-gray-500 mr-2" />
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Filters</h3>
            </div>
            <button
              onClick={() => setFilters({
                datePeriod: 'today',
                status: 'all',
                location: 'all',
                department: 'all',
                shift: 'all',
                workHours: 'all',
                searchText: ''
              })}
              className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 font-medium"
            >
              Reset
            </button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-3">
            {/* Date Period */}
            <div>
              <select
                value={filters.datePeriod}
                onChange={(e) => setFilters(prev => ({ ...prev, datePeriod: e.target.value }))}
                className="w-full text-sm py-2 px-3 rounded border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
              >
                <option value="all">All Time</option>
                <option value="today">Today</option>
                <option value="yesterday">Yesterday</option>
                <option value="this_week">This Week</option>
                <option value="last_week">Last Week</option>
                <option value="this_month">This Month</option>
                <option value="last_month">Last Month</option>
                <option value="last_three_months">Last 3 Months</option>
                <option value="last_six_months">Last 6 Months</option>
                <option value="this_year">This Year</option>
                <option value="last_year">Last Year</option>
              </select>
            </div>

            {/* Status */}
            <div>
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                className="w-full text-sm py-2 px-3 rounded border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
              >
                <option value="all">All Status</option>
                {uniqueStatuses.map(status => (
                  <option key={status} value={status}>
                    {status === 'NOT_MARKED' ? 'Not Marked' :
                     status.charAt(0).toUpperCase() + status.slice(1).toLowerCase().replace('_', ' ')}
                  </option>
                ))}
              </select>
            </div>

            {/* Location */}
            <div>
              <select
                value={filters.location}
                onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                className="w-full text-sm py-2 px-3 rounded border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
              >
                <option value="all">All Locations</option>
                <option value="office">Office</option>
                <option value="remote">Remote</option>
                {uniqueLocations.map(location => (
                  <option key={location} value={location}>{location}</option>
                ))}
              </select>
            </div>

            {/* Department */}
            <div>
              <select
                value={filters.department}
                onChange={(e) => setFilters(prev => ({ ...prev, department: e.target.value }))}
                className="w-full text-sm py-2 px-3 rounded border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
              >
                <option value="all">All Departments</option>
                {uniqueDepartments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
            </div>

            {/* Shift */}
            <div>
              <select
                value={filters.shift}
                onChange={(e) => setFilters(prev => ({ ...prev, shift: e.target.value }))}
                className="w-full text-sm py-2 px-3 rounded border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
              >
                <option value="all">All Shifts</option>
                {uniqueShifts.map(shift => (
                  <option key={shift} value={shift}>{shift}</option>
                ))}
              </select>
            </div>

            {/* Work Hours */}
            <div>
              <select
                value={filters.workHours}
                onChange={(e) => setFilters(prev => ({ ...prev, workHours: e.target.value }))}
                className="w-full text-sm py-2 px-3 rounded border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
              >
                <option value="all">All Work Hours</option>
                <option value="full_day">Full Day (7+ hrs)</option>
                <option value="partial_day">Partial Day (&lt;7 hrs)</option>
                <option value="overtime">With Overtime</option>
              </select>
            </div>

            {/* Search */}
            <div>
              <input
                type="text"
                placeholder="Search..."
                value={filters.searchText}
                onChange={(e) => setFilters(prev => ({ ...prev, searchText: e.target.value }))}
                className="w-full text-sm py-2 px-3 rounded border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500 dark:focus:ring-blue-400"
              />
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check In</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check Out</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Work Hours</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredTeamData.length > 0 ? (
                filteredTeamData.map((member) => (
                <tr key={member.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {member.photo ? (
                        <img
                          src={member.photo}
                          alt={member.name}
                          className="h-8 w-8 rounded-full object-cover mr-3"
                        />
                      ) : (
                        <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center mr-3">
                          <Users className="h-4 w-4 text-gray-600" />
                        </div>
                      )}
                      <div>
                        <div className="text-sm font-medium text-gray-900">{member.name}</div>
                        <div className="text-sm text-gray-500">{member.position}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{member.department}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 text-green-500 mr-1" />
                      {member.checkInTime}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 text-red-500 mr-1" />
                      {member.checkOutTime}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{member.totalWorkHours}h</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      member.todayStatus === 'PRESENT' ? 'bg-green-100 text-green-800' :
                      member.todayStatus === 'LATE' ? 'bg-yellow-100 text-yellow-800' :
                      member.todayStatus === 'ABSENT' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {member.todayStatus === 'NOT_MARKED' ? 'Not Marked' : member.todayStatus}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => onViewEmployeeAttendance(member)}
                      className="text-blue-600 hover:text-blue-900 flex items-center"
                    >
                      <Calendar className="h-4 w-4 mr-1" />
                      View Details
                    </button>
                  </td>
                </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-sm text-gray-500">
                    <div className="flex flex-col items-center py-4">
                      <AlertCircle className="h-8 w-8 text-gray-300 mb-2" />
                      <p>No team members match your current filters</p>
                      <button
                        onClick={() => setFilters({
                          datePeriod: 'today',
                          status: 'all',
                          location: 'all',
                          department: 'all',
                          shift: 'all',
                          workHours: 'all',
                          searchText: ''
                        })}
                        className="mt-2 text-blue-600 hover:text-blue-500 text-sm font-medium"
                      >
                        Clear all filters
                      </button>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};


const EmployeeTeamPortalView: React.FC<EmployeeTeamPortalViewProps> = ({
  teamData,
  employeeId
}) => {
  // Modal
  const [showAttendanceModal, setShowAttendanceModal] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<TeamMember | null>(null);
  const [selectedEmployeeAttendances, setSelectedEmployeeAttendances] = useState<Attendance[]>([]);
  const [isLoadingAttendance, setIsLoadingAttendance] = useState(false);

  // Team
  const teamMembers: TeamMember[] = teamData?.reportees?.map(member => ({
    id: member.id,
    name: member.name,
    code: member.employeeCode || `EMP-${member.id}`,
    email: member.email,
    department: member.department,
    position: member.designation,
    photo: undefined
  })) || [];

  // Leave Requests
  const [teamLeaveRequests, setTeamLeaveRequests] = useState<LeaveRequest[]>([]);
  const [isLoadingLeaveRequests, setIsLoadingLeaveRequests] = useState(false);

  // View Toggle
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Handle opening attendance modal for a team member
  const handleOpenAttendanceModal = async (employee: TeamMember) => {
    setSelectedEmployee(employee);
    setShowAttendanceModal(true);
    setIsLoadingAttendance(true);
    try {
      const endDate = new Date().toISOString().split('T')[0];
      const startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      const attendanceData = await employeePortalService.getEmployeeAttendance(employee.id, startDate, endDate);
      if (attendanceData && attendanceData.records) {
        const transformedAttendance = attendanceData.records.map(record => ({
          id: record.id || `${employee.id}-${record.date}`,
          employeeId: employee.id,
          employeeName: employee.name,
          date: record.date,
          checkInTime: record.checkInTime || '',
          checkOutTime: record.checkOutTime,
          status: record.status as AttendanceStatus,
          workHours: record.workHours || null,
          notes: record.notes || null,
          shift: 1,
          location: record.location || null,
          isRemote: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }));
        setSelectedEmployeeAttendances(transformedAttendance);
      } else {
        setSelectedEmployeeAttendances([]);
      }
    } catch (error) {
      setSelectedEmployeeAttendances([]);
    } finally {
      setIsLoadingAttendance(false);
    }
  };

  // Load team leave requests when the leave tab is selected
  const loadTeamLeaveRequests = async () => {
    setIsLoadingLeaveRequests(true);
    try {
      const allRequests: LeaveRequest[] = [];
      for (const member of teamMembers) {
        try {
          const leaveData = await employeePortalService.getEmployeeLeaveData(member.id);
          if (leaveData?.recentRequests) {
            const transformedRequests = leaveData.recentRequests.map(request => ({
              id: request.id,
              employeeId: member.id,
              employeeName: member.name,
              leaveType: request.type,
              startDate: request.from,
              endDate: request.to,
              status: request.status as LeaveStatus,
              days: request.days,
              reason: request.reason || '',
              emergencyContact: request.emergencyContact || '',
              attachments: request.attachments || [],
              appliedDate: new Date().toISOString(),
              createdAt: new Date().toISOString(),
              approvedBy: undefined,
              approvedDate: undefined,
              rejectedBy: undefined,
              rejectedDate: undefined,
              rejectionReason: undefined
            }));
            allRequests.push(...transformedRequests);
          }
        } catch {}
      }
      setTeamLeaveRequests(allRequests);
    } catch {}
    finally {
      setIsLoadingLeaveRequests(false);
    }
  };

  // Tabs config
  const tabs = [
    {
      name: 'My Team',
      icon: Users,
      count: teamMembers.length
    },
    {
      name: 'Team Attendance',
      icon: UserCheck,
      count: teamMembers.length
    },
    {
      name: 'Team Leave Requests',
      icon: ClipboardList,
      count: teamLeaveRequests.length
    }
  ];

  // Load leaves on tab change
  const [selectedTabIndex, setSelectedTabIndex] = useState(0);
  useEffect(() => {
    if (selectedTabIndex === 2 && teamMembers.length > 0) {
      loadTeamLeaveRequests();
    }
    // eslint-disable-next-line
  }, [selectedTabIndex, teamMembers.length]);

  // No Team
  if (!teamData || !teamData.reportees || teamData.reportees.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-4">
        <div className="w-full">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Team Management</h1>
            <p className="text-gray-600">Manage and monitor your team's performance</p>
          </div>
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-12 text-center">
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
              <Users className="h-12 w-12 text-blue-600" />
            </div>
            <h3 className="text-2xl font-semibold text-gray-900 mb-3">No Team Members</h3>
            <p className="text-gray-600 text-lg max-w-md mx-auto">
              You don't have any team members reporting to you at this time.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 pt-1 pb-4 px-4">
      <div className="w-full">
        {/* Header Section */}
        <div className="mb-2">
          <h1 className="text-xl font-bold text-gray-900 mb-1">Team Management</h1>
          {/* Team Stats Cards Removed as per user request */}
        </div>

        <Tab.Group selectedIndex={selectedTabIndex} onChange={setSelectedTabIndex}>
          <Tab.List className="flex bg-white border-b border-gray-200 mb-4">
            {tabs.map((tab) => (
              <Tab
                key={tab.name}
                className={({ selected }) =>
                  `px-4 py-2 text-xs font-medium border-b-2 transition-colors duration-200 focus:outline-none ${
                    selected
                      ? 'border-blue-500 text-blue-600 bg-blue-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`
                }
              >
                <div className="flex items-center space-x-1.5">
                  <tab.icon className="h-3.5 w-3.5" />
                  <span>{tab.name}</span>
                </div>
              </Tab>
            ))}
          </Tab.List>
          <Tab.Panels>
            {/* Panel 1: My Team */}
            <Tab.Panel>
              <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-100">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-2">
                        <Users className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <h4 className="text-xl font-bold text-gray-900">My Team</h4>
                        <p className="text-sm text-gray-600">{teamMembers.length} team member{teamMembers.length !== 1 ? 's' : ''}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setViewMode('grid')}
                        className={`p-2 rounded-lg transition-colors ${
                          viewMode === 'grid'
                            ? 'bg-blue-600 text-white shadow-md'
                            : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-200'
                        }`}
                        title="Grid View"
                      >
                        <Grid className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => setViewMode('list')}
                        className={`p-2 rounded-lg transition-colors ${
                          viewMode === 'list'
                            ? 'bg-blue-600 text-white shadow-md'
                            : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-200'
                        }`}
                        title="List View"
                      >
                        <List className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  {viewMode === 'grid' ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
                      {teamMembers.map((member) => (
                        <div key={member.id} className="group bg-white rounded-2xl border border-gray-100 shadow-lg p-6 flex flex-col items-center justify-between transition-all duration-300 hover:shadow-2xl hover:border-blue-300 min-h-[250px]">
                          <div className="flex flex-col items-center mb-4 w-full">
                            <div className="bg-gradient-to-br from-blue-400 to-blue-200 rounded-full w-16 h-16 flex items-center justify-center shadow-lg mb-2 border-4 border-white">
                              <span className="text-white font-bold text-2xl">
                                {member.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                              </span>
                            </div>
                            <div className="flex flex-col items-center min-w-0 w-full">
                              <h5 className="text-lg font-bold text-gray-900 group-hover:text-blue-600 transition-colors text-center">
                                {member.name}
                              </h5>
                              <p className="text-sm text-gray-500 mb-2">{member.position || 'N/A'}</p>
                              <div className="mt-1 mb-2 space-y-1 w-full">
                                <div className="flex items-center text-xs text-gray-500 justify-center">
                                  <span className="font-medium">ID: {member.code}</span>
                                </div>
                                <div className="flex items-center text-xs text-gray-500 justify-center">
                                  <Building2 className="h-4 w-4 mr-2 text-gray-400" />
                                  <span>{member.department || 'N/A'}</span>
                                </div>
                                {member.email && (
                                  <div className="flex items-center text-xs text-gray-500 justify-center">
                                    <Mail className="h-4 w-4 mr-2 text-gray-400" />
                                    <span className="truncate">{member.email}</span>
                                  </div>
                                )}
                              </div>
                              <div className="flex items-center justify-center w-full mt-auto pt-4 border-t border-gray-100">
                                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold bg-green-100 text-green-700 border border-green-200 shadow-sm">
                                  <div className="w-1.5 h-1.5 bg-green-400 rounded-full mr-1.5"></div>
                                  Active
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {teamMembers.map((member) => (
                        <div key={member.id} className="group bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-all duration-200 hover:border-blue-300">
                          <div className="flex items-center space-x-4">
                            <div className="bg-gradient-to-br from-blue-400 to-blue-200 rounded-full w-12 h-12 flex items-center justify-center shadow-md border-2 border-white flex-shrink-0">
                              <span className="text-white font-bold text-lg">
                                {member.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                              </span>
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between">
                                <div className="flex-1 min-w-0 mr-4">
                                  <h5 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                                    {member.name}
                                  </h5>
                                  <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                                    <span className="font-medium">ID: {member.code}</span>
                                    <span>{member.position || 'N/A'}</span>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-6 text-sm text-gray-500 flex-shrink-0">
                                  <div className="flex items-center">
                                    <Building2 className="h-4 w-4 mr-1 text-gray-400" />
                                    <span>{member.department || 'N/A'}</span>
                                  </div>
                                  {member.email && (
                                    <div className="flex items-center">
                                      <Mail className="h-4 w-4 mr-1 text-gray-400" />
                                      <span>{member.email}</span>
                                    </div>
                                  )}
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold bg-green-100 text-green-700 border border-green-200">
                                    <div className="w-1.5 h-1.5 bg-green-400 rounded-full mr-1.5"></div>
                                    Active
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
          </Tab.Panel>

            {/* Panel 2: Team Attendance */}
            <Tab.Panel>
              <TeamAttendanceOverview
                teamMembers={teamMembers}
                onViewEmployeeAttendance={handleOpenAttendanceModal}
              />
            </Tab.Panel>

            {/* Panel 3: Team Leave Requests */}
            <Tab.Panel>
            {isLoadingLeaveRequests ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <LeaveRequestsTable
                paginatedRequests={teamLeaveRequests}
                employeeLeaveData={[]}
                onApprove={async () => {}}
                onReject={async () => {}}
                onViewDetails={() => {}}
                onCreateRequest={async () => {}}
                filtersBar={null}
                title="Team Leave Requests"
                showAddButton={false}
                leaveTypes={[]}
                currentUser={undefined}
                workflowEnabled={true}
                onWorkflowAction={async () => {}}
                currentPage={1}
                totalPages={1}
                totalItems={teamLeaveRequests.length}
                itemsPerPage={10}
                onPageChange={() => {}}
                onItemsPerPageChange={() => {}}
              />
            )}
            </Tab.Panel>
          </Tab.Panels>
        </Tab.Group>

        {/* Attendance Modal */}
        {showAttendanceModal && selectedEmployee && (
          <EmployeeAttendanceModal
            employeeId={selectedEmployee.id}
            employeeName={selectedEmployee.name}
            employeeDepartment={selectedEmployee.department}
            employeePosition={selectedEmployee.position}
            employeePhoto={selectedEmployee.photo}
            attendances={selectedEmployeeAttendances}
            onSubmitRegularization={() => {}}
            onClose={() => {
              setShowAttendanceModal(false);
              setSelectedEmployee(null);
              setSelectedEmployeeAttendances([]);
            }}
            canSubmitRegularization={false}
          />
        )}
      </div>
    </div>
  );
};
export default EmployeeTeamPortalView;
