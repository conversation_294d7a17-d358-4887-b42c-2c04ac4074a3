import React, { useState, useEffect } from 'react';
import { Mail, Save, AlertCircle, Send } from 'lucide-react';

interface Setting {
  id: string;
  category: string;
  key: string;
  value: string;
  description: string;
  lastUpdated?: string;
  updatedBy?: string;
}

interface EmailSettingsProps {
  settings: Setting[];
  onSettingChange: (id: string, value: string) => void;
  onSave: () => void;
  isSaving?: boolean;
  saveSuccess?: boolean;
}

const EmailSettings: React.FC<EmailSettingsProps> = ({
  settings,
  onSettingChange,
  onSave,
  isSaving = false,
  saveSuccess = false
}) => {
  const [localSettings, setLocalSettings] = useState<Setting[]>([]);
  const [hasChanges, setHasChanges] = useState(false);


  useEffect(() => {
    const emailSettings = settings.filter(setting => setting.category === 'email');
    setLocalSettings(emailSettings);
  }, [settings]);

  const handleChange = (id: string, value: string) => {
    setLocalSettings(prev => prev.map(setting => 
      setting.id === id ? { ...setting, value } : setting
    ));
    setHasChanges(true);
    onSettingChange(id, value);
  };

  const handleSave = () => {
    onSave();
    setHasChanges(false);
  };



  const isNumberField = (key: string) => {
    return key.toLowerCase().includes('port') || key.toLowerCase().includes('timeout');
  };

  const isPasswordField = (key: string) => {
    return key.toLowerCase().includes('password');
  };

  const isSelectField = (key: string) => {
    return key === 'smtpSecurity';
  };

  const getSelectOptions = (key: string) => {
    if (key === 'smtpSecurity') {
      return ['TLS', 'SSL', 'None'];
    }
    return [];
  };

  const [formData, setFormData] = useState({
    smtpServer: '',
    smtpPort: '587',
    smtpUsername: '',
    smtpPassword: '',
    smtpSecurity: 'TLS',
    emailFrom: '',
    emailFromName: '',
    emailTimeout: '30',
    enableEmailNotifications: false,
    emailTemplate: ''
  });

  const handleFormChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Mail className="h-6 w-6 text-green-600" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Email Settings</h2>
            <p className="text-sm text-gray-600">Configure SMTP server and email notification preferences</p>
          </div>
        </div>
        {hasChanges && (
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSaving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Save className="h-4 w-4" />
            )}
            {isSaving ? 'Saving...' : 'Save Changes'}
          </button>
        )}
      </div>

      {saveSuccess && (
        <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700">
          <AlertCircle className="h-4 w-4" />
          Email settings saved successfully!
        </div>
      )}

      <div className="bg-white rounded-lg border border-gray-200 divide-y divide-gray-200">
        {/* SMTP Server */}
        <div className="p-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            SMTP Server
          </label>
          <p className="text-sm text-gray-600 mb-3">Enter your SMTP server hostname (e.g., smtp.gmail.com)</p>
          <input
            type="text"
            value={formData.smtpServer}
            onChange={(e) => handleFormChange('smtpServer', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            placeholder="smtp.gmail.com"
          />
        </div>

        {/* SMTP Port */}
        <div className="p-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            SMTP Port
          </label>
          <p className="text-sm text-gray-600 mb-3">Port number for SMTP connection (587 for TLS, 465 for SSL)</p>
          <input
            type="number"
            value={formData.smtpPort}
            onChange={(e) => handleFormChange('smtpPort', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            placeholder="587"
            min="1"
            max="65535"
          />
        </div>

        {/* SMTP Username */}
        <div className="p-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            SMTP Username
          </label>
          <p className="text-sm text-gray-600 mb-3">Username for SMTP authentication</p>
          <input
            type="text"
            value={formData.smtpUsername}
            onChange={(e) => handleFormChange('smtpUsername', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            placeholder="<EMAIL>"
          />
        </div>

        {/* SMTP Password */}
        <div className="p-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            SMTP Password
          </label>
          <p className="text-sm text-gray-600 mb-3">Password for SMTP authentication</p>
          <input
            type="password"
            value={formData.smtpPassword}
            onChange={(e) => handleFormChange('smtpPassword', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            placeholder="Enter password"
          />
        </div>

        {/* SMTP Security */}
        <div className="p-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            SMTP Security
          </label>
          <p className="text-sm text-gray-600 mb-3">Security protocol for SMTP connection</p>
          <select
            value={formData.smtpSecurity}
            onChange={(e) => handleFormChange('smtpSecurity', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
          >
            <option value="TLS">TLS</option>
            <option value="SSL">SSL</option>
            <option value="None">None</option>
          </select>
        </div>

        {/* Email From */}
        <div className="p-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            From Email Address
          </label>
          <p className="text-sm text-gray-600 mb-3">Email address that will appear as sender</p>
          <input
            type="email"
            value={formData.emailFrom}
            onChange={(e) => handleFormChange('emailFrom', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            placeholder="<EMAIL>"
          />
        </div>

        {/* Email From Name */}
        <div className="p-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            From Name
          </label>
          <p className="text-sm text-gray-600 mb-3">Display name that will appear as sender</p>
          <input
            type="text"
            value={formData.emailFromName}
            onChange={(e) => handleFormChange('emailFromName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            placeholder="Your Company Name"
          />
        </div>

        {/* Email Timeout */}
        <div className="p-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            Email Timeout (seconds)
          </label>
          <p className="text-sm text-gray-600 mb-3">Timeout for email sending operations</p>
          <input
            type="number"
            value={formData.emailTimeout}
            onChange={(e) => handleFormChange('emailTimeout', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            placeholder="30"
            min="5"
            max="300"
          />
        </div>

        {/* Enable Email Notifications */}
        <div className="p-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            Email Notifications
          </label>
          <p className="text-sm text-gray-600 mb-3">Enable or disable email notifications</p>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="enableEmailNotifications"
              checked={formData.enableEmailNotifications}
              onChange={(e) => handleFormChange('enableEmailNotifications', e.target.checked)}
              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
            />
            <label htmlFor="enableEmailNotifications" className="ml-2 text-sm text-gray-700">
              {formData.enableEmailNotifications ? 'Enabled' : 'Disabled'}
            </label>
          </div>
        </div>

        {/* Email Template */}
        <div className="p-6">
          <label className="block text-sm font-medium text-gray-900 mb-1">
            Email Template
          </label>
          <p className="text-sm text-gray-600 mb-3">Default email template for notifications</p>
          <textarea
            value={formData.emailTemplate}
            onChange={(e) => handleFormChange('emailTemplate', e.target.value)}
            rows={6}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            placeholder="Dear {{name}},\n\nThis is a notification from our system.\n\nBest regards,\nYour Company"
          />
        </div>
      </div>
    </div>
  );
};

export default EmailSettings;