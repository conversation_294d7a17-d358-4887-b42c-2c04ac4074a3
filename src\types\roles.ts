/**
 * Comprehensive list of all roles in the system.
 * Roles are organized by category and follow a hierarchical structure.
 */
export enum UserRole {
  // System Administration (Highest Level)
  SYSTEM_ADMIN = 'SYSTEM_ADMIN',
  ADMIN = 'ADMIN',
  
  // IT Management Roles
  IT_ADMIN = 'IT_ADMIN',
  IT_STAFF = 'IT_STAFF',
  IT_SUPPORT = 'IT_SUPPORT',
  IT_TECHNICIAN = 'IT_TECHNICIAN',
  
  // HR Management Roles
  HR_ADMIN = 'HR_ADMIN',
  HR_STAFF = 'HR_STAFF',
  HR_SPECIALIST = 'HR_SPECIALIST',
  
  // Management Roles
  CEO = 'CEO',
  DEPT_HEAD = 'DEPT_HEAD',
  PROJECT_MANAGER = 'PROJECT_MANAGER',
  FINANCE_MANAGER = 'FINANCE_MANAGER',
  
  // General Staff Roles
  SENIOR_EMPLOYEE = 'SENIOR_EMPLOYEE',
  EMPLOYEE = 'EMPLOYEE',
  
  // Specialized Roles
  SECURITY_OFFICER = 'SECURITY_OFFICER',
  COMPLIANCE_OFFICER = 'COMPLIANCE_OFFICER',
  AUDITOR = 'AUDITOR',
  
  // Dashboard and Analytics
  DASHBOARD_MANAGER = 'DASHBOARD_MANAGER',
  ANALYTICS_VIEWER = 'ANALYTICS_VIEWER',
  
  // Basic View Access
  VIEW = 'VIEW'
}

/**
 * Categories of permissions in the system
 */
export enum PermissionCategory {
  TICKETS = 'tickets',
  HR = 'hr',
  SYSTEM = 'system',
  DASHBOARD = 'dashboard',
  REPORTING = 'reporting'
}

/**
 * Definition of permissions and their categories
 */
export const SYSTEM_PERMISSIONS = {
  // Ticket Management Permissions
  tickets: {
    CREATE: 'canCreateTickets',
    CREATE_FOR_OTHERS: 'canCreateTicketsForOthers',
    EDIT: 'canEditTickets',
    DELETE: 'canDeleteTickets',
    CLOSE: 'canCloseTickets',
    ASSIGN: 'canAssignTickets',
    ESCALATE: 'canEscalateTickets',
    VIEW_ALL: 'canViewAllTickets',
    LOCK: 'canLockTickets'
  },
  
  // HR Permissions
  hr: {
    CREATE_EMPLOYEE: 'canCreateEmployee',
    EDIT_EMPLOYEE: 'canEditEmployee',
    DELETE_EMPLOYEE: 'canDeleteEmployee',
    VIEW_EMPLOYEES: 'canViewEmployees',
    MANAGE_ATTENDANCE: 'canManageAttendance',
    MANAGE_LEAVE: 'canManageLeave',
    MANAGE_PAYROLL: 'canManagePayroll',
    MANAGE_PERFORMANCE: 'canManagePerformance'
  },
  
  // System Administration Permissions
  system: {
    ACCESS_ALL: 'canAccessAllModules',
    CONFIGURE: 'canConfigureSystem',
    MANAGE_ROLES: 'canManageRoles',
    ADD_USERS: 'canAddUsers',
    EDIT_USERS: 'canEditUsers',
    DELETE_USERS: 'canDeleteUsers',
    VIEW_REPORTS: 'canViewReports',
    EXPORT_DATA: 'canExportData',
    IMPORT_DATA: 'canImportData',
    VIEW_ALL_DEPARTMENTS: 'canViewAllDepartments',
    APPROVE_REQUESTS: 'canApproveRequests'
  },
  
  // Dashboard Permissions
  dashboard: {
    VIEW: 'canViewDashboards',
    VIEW_ALL: 'canViewAllDashboards',
    CREATE: 'canCreateDashboards',
    EDIT: 'canEditDashboards',
    DELETE: 'canDeleteDashboards',
    SHARE: 'canShareDashboards',
    EXPORT: 'canExportDashboardData',
    CONFIGURE_ALERTS: 'canConfigureDashboardAlerts',
    MANAGE_USERS: 'canManageDashboardUsers'
  }
};

/**
 * Role hierarchy definition
 * Each role inherits permissions from its parent role
 */
export const ROLE_HIERARCHY = {
  SYSTEM_ADMIN: null, // Top level role
  ADMIN: 'SYSTEM_ADMIN',
  
  IT_ADMIN: 'ADMIN',
  IT_STAFF: 'IT_ADMIN',
  IT_SUPPORT: 'IT_STAFF',
  IT_TECHNICIAN: 'IT_SUPPORT',
  
  HR_ADMIN: 'ADMIN',
  HR_STAFF: 'HR_ADMIN',
  HR_SPECIALIST: 'HR_STAFF',
  
  CEO: 'ADMIN',
  DEPT_HEAD: 'CEO',
  PROJECT_MANAGER: 'DEPT_HEAD',
  FINANCE_MANAGER: 'DEPT_HEAD',
  
  SENIOR_EMPLOYEE: 'EMPLOYEE',
  
  DASHBOARD_MANAGER: null, // Independent role for dashboard management
  ANALYTICS_VIEWER: 'DASHBOARD_MANAGER',
  
  VIEW: null // Base role with minimal permissions
};