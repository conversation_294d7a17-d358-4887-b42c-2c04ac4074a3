import { MigrationInterface, QueryRunner } from "typeorm";

export class AddOversightPermissions1746000000003 implements MigrationInterface {
    name = 'AddOversightPermissions1746000000003'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add oversightPermissions column if it doesn't exist
        const hasColumn = await queryRunner.hasColumn('users', 'oversightPermissions');
        
        if (!hasColumn) {
            await queryRunner.query(`
                ALTER TABLE users 
                ADD COLUMN oversightPermissions JSON NULL
            `);
            
            console.log('Added oversightPermissions column to users table');
        } else {
            console.log('oversightPermissions column already exists, skipping');
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Check if column exists before attempting to drop it
        const hasColumn = await queryRunner.hasColumn('users', 'oversightPermissions');
        
        if (hasColumn) {
            await queryRunner.query(`
                ALTER TABLE users 
                DROP COLUMN oversightPermissions
            `);
            
            console.log('Removed oversightPermissions column from users table');
        }
    }
} 