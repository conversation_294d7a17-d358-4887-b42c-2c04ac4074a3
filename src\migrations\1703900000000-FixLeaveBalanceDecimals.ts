import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixLeaveBalanceDecimals1703900000000 implements MigrationInterface {
    name = 'FixLeaveBalanceDecimals1703900000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        console.log('🔄 Converting leave balance decimal fields to integers...');
        
        try {
            // First, round all decimal values to integers
            await queryRunner.query(`
                UPDATE leave_balances 
                SET 
                    totalAllocated = ROUND(totalAllocated),
                    used = ROUND(used),
                    pending = ROUND(pending),
                    carriedForward = ROUND(carriedForward),
                    lapsed = ROUND(lapsed)
            `);

            await queryRunner.query(`
                UPDATE leave_allocations 
                SET 
                    policyAllocation = ROUND(policyAllocation),
                    manualAdjustment = ROUND(manualAdjustment),
                    carriedForward = ROUND(carriedForward)
            `);

            // Then alter the column types to INT
            await queryRunner.query(`
                ALTER TABLE leave_balances 
                MODIFY COLUMN totalAllocated INT DEFAULT 0,
                MODIFY COLUMN used INT DEFAULT 0,
                MODIFY COLUMN pending INT DEFAULT 0,
                MODIFY COLUMN carriedForward INT DEFAULT 0,
                MODIFY COLUMN lapsed INT DEFAULT 0
            `);

            await queryRunner.query(`
                ALTER TABLE leave_allocations 
                MODIFY COLUMN policyAllocation INT DEFAULT 0,
                MODIFY COLUMN manualAdjustment INT DEFAULT 0,
                MODIFY COLUMN carriedForward INT DEFAULT 0
            `);

            console.log('✅ Successfully converted leave balance fields to integers');

        } catch (error) {
            console.error('❌ Error converting leave balance fields:', error);
            throw error;
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        console.log('🔄 Reverting leave balance fields back to decimals...');
        
        try {
            // Revert back to decimal types
            await queryRunner.query(`
                ALTER TABLE leave_balances 
                MODIFY COLUMN totalAllocated DECIMAL(10,2) DEFAULT 0,
                MODIFY COLUMN used DECIMAL(10,2) DEFAULT 0,
                MODIFY COLUMN pending DECIMAL(10,2) DEFAULT 0,
                MODIFY COLUMN carriedForward DECIMAL(10,2) DEFAULT 0,
                MODIFY COLUMN lapsed DECIMAL(10,2) DEFAULT 0
            `);

            await queryRunner.query(`
                ALTER TABLE leave_allocations 
                MODIFY COLUMN policyAllocation DECIMAL(10,2) DEFAULT 0,
                MODIFY COLUMN manualAdjustment DECIMAL(10,2) DEFAULT 0,
                MODIFY COLUMN carriedForward DECIMAL(10,2) DEFAULT 0
            `);

            console.log('✅ Successfully reverted leave balance fields to decimals');

        } catch (error) {
            console.error('❌ Error reverting leave balance fields:', error);
            throw error;
        }
    }
} 