import React, { useState, useEffect } from 'react';
import { Zap, Save, AlertCircle, Activity, TrendingUp, BarChart3, RefreshCw } from 'lucide-react';

interface Setting {
  id: string;
  category: string;
  key: string;
  value: string;
  description: string;
  lastUpdated?: string;
  updatedBy?: string;
}

interface PerformanceSettingsProps {
  settings: Setting[];
  onSettingChange: (id: string, value: string) => void;
  onSave: () => void;
  isSaving?: boolean;
  saveSuccess?: boolean;
}

const PerformanceSettings: React.FC<PerformanceSettingsProps> = ({
  settings,
  onSettingChange,
  onSave,
  isSaving = false,
  saveSuccess = false
}) => {
  const [localSettings, setLocalSettings] = useState<Setting[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [performanceMetrics, setPerformanceMetrics] = useState({
    cpuUsage: 45,
    memoryUsage: 62,
    responseTime: 120,
    activeUsers: 234
  });
  const [optimizing, setOptimizing] = useState(false);

  useEffect(() => {
    const performanceSettings = settings.filter(setting => setting.category === 'performance');
    setLocalSettings(performanceSettings);
  }, [settings]);

  useEffect(() => {
    // Simulate real-time performance metrics updates
    const interval = setInterval(() => {
      setPerformanceMetrics(prev => ({
        cpuUsage: Math.max(20, Math.min(80, prev.cpuUsage + (Math.random() - 0.5) * 10)),
        memoryUsage: Math.max(30, Math.min(90, prev.memoryUsage + (Math.random() - 0.5) * 8)),
        responseTime: Math.max(50, Math.min(300, prev.responseTime + (Math.random() - 0.5) * 20)),
        activeUsers: Math.max(100, Math.min(500, prev.activeUsers + Math.floor((Math.random() - 0.5) * 20)))
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const handleChange = (id: string, value: string) => {
    setLocalSettings(prev => prev.map(setting => 
      setting.id === id ? { ...setting, value } : setting
    ));
    setHasChanges(true);
    onSettingChange(id, value);
  };

  const handleSave = () => {
    onSave();
    setHasChanges(false);
  };

  const handleOptimizePerformance = async () => {
    setOptimizing(true);
    
    // Simulate performance optimization
    setTimeout(() => {
      setPerformanceMetrics(prev => ({
        cpuUsage: Math.max(20, prev.cpuUsage * 0.8),
        memoryUsage: Math.max(30, prev.memoryUsage * 0.85),
        responseTime: Math.max(50, prev.responseTime * 0.7),
        activeUsers: prev.activeUsers
      }));
      setOptimizing(false);
    }, 3000);
  };

  const isNumberField = (key: string) => {
    return key.toLowerCase().includes('timeout') || 
           key.toLowerCase().includes('limit') || 
           key.toLowerCase().includes('size') || 
           key.toLowerCase().includes('users') || 
           key.toLowerCase().includes('concurrent');
  };

  const getPerformanceStatus = (value: number, type: string) => {
    if (type === 'cpu' || type === 'memory') {
      if (value < 50) return { color: 'text-green-600', bg: 'bg-green-500', status: 'Good' };
      if (value < 75) return { color: 'text-yellow-600', bg: 'bg-yellow-500', status: 'Fair' };
      return { color: 'text-red-600', bg: 'bg-red-500', status: 'High' };
    } else if (type === 'response') {
      if (value < 100) return { color: 'text-green-600', bg: 'bg-green-500', status: 'Fast' };
      if (value < 200) return { color: 'text-yellow-600', bg: 'bg-yellow-500', status: 'Normal' };
      return { color: 'text-red-600', bg: 'bg-red-500', status: 'Slow' };
    }
    return { color: 'text-gray-600', bg: 'bg-gray-500', status: 'Unknown' };
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Zap className="h-6 w-6 text-orange-600" />
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Performance Settings</h2>
            <p className="text-sm text-gray-600">Configure system performance, caching, and optimization settings</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={handleOptimizePerformance}
            disabled={optimizing}
            className="flex items-center gap-2 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {optimizing ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <TrendingUp className="h-4 w-4" />
            )}
            {optimizing ? 'Optimizing...' : 'Optimize Now'}
          </button>
          {hasChanges && (
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isSaving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save className="h-4 w-4" />
              )}
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          )}
        </div>
      </div>

      {saveSuccess && (
        <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700">
          <AlertCircle className="h-4 w-4" />
          Performance settings saved successfully!
        </div>
      )}

      {optimizing && (
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <RefreshCw className="h-4 w-4 text-orange-600 animate-spin" />
            <span className="text-sm font-medium text-orange-900">Performance Optimization in Progress</span>
          </div>
          <p className="text-sm text-orange-700">
            System is being optimized for better performance. This may take a few moments.
          </p>
        </div>
      )}

      {/* Real-time Performance Metrics */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center gap-3 mb-4">
          <Activity className="h-5 w-5 text-orange-600" />
          <h3 className="text-lg font-medium text-gray-900">Real-time Performance Metrics</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="text-sm font-medium text-gray-900">CPU Usage</div>
              <div className={`text-xs font-medium ${getPerformanceStatus(performanceMetrics.cpuUsage, 'cpu').color}`}>
                {getPerformanceStatus(performanceMetrics.cpuUsage, 'cpu').status}
              </div>
            </div>
            <div className="text-2xl font-semibold text-gray-900 mb-2">{performanceMetrics.cpuUsage.toFixed(1)}%</div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${getPerformanceStatus(performanceMetrics.cpuUsage, 'cpu').bg}`}
                style={{width: `${performanceMetrics.cpuUsage}%`}}
              ></div>
            </div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="text-sm font-medium text-gray-900">Memory Usage</div>
              <div className={`text-xs font-medium ${getPerformanceStatus(performanceMetrics.memoryUsage, 'memory').color}`}>
                {getPerformanceStatus(performanceMetrics.memoryUsage, 'memory').status}
              </div>
            </div>
            <div className="text-2xl font-semibold text-gray-900 mb-2">{performanceMetrics.memoryUsage.toFixed(1)}%</div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${getPerformanceStatus(performanceMetrics.memoryUsage, 'memory').bg}`}
                style={{width: `${performanceMetrics.memoryUsage}%`}}
              ></div>
            </div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="text-sm font-medium text-gray-900">Response Time</div>
              <div className={`text-xs font-medium ${getPerformanceStatus(performanceMetrics.responseTime, 'response').color}`}>
                {getPerformanceStatus(performanceMetrics.responseTime, 'response').status}
              </div>
            </div>
            <div className="text-2xl font-semibold text-gray-900 mb-2">{performanceMetrics.responseTime.toFixed(0)}ms</div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${getPerformanceStatus(performanceMetrics.responseTime, 'response').bg}`}
                style={{width: `${Math.min(100, (performanceMetrics.responseTime / 300) * 100)}%`}}
              ></div>
            </div>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="text-sm font-medium text-gray-900">Active Users</div>
              <BarChart3 className="h-4 w-4 text-blue-600" />
            </div>
            <div className="text-2xl font-semibold text-gray-900 mb-2">{performanceMetrics.activeUsers}</div>
            <div className="text-xs text-gray-600">
              Max: {localSettings.find(s => s.key === 'maxConcurrentUsers')?.value || '1000'}
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 divide-y divide-gray-200">
        {localSettings.map((setting) => (
          <div key={setting.id} className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-900 mb-1">
                  {setting.key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </label>
                <p className="text-sm text-gray-600 mb-3">{setting.description}</p>
                
                {setting.key.includes('enable') || setting.key.includes('Enable') ? (
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id={setting.id}
                      checked={setting.value === 'true'}
                      onChange={(e) => handleChange(setting.id, e.target.checked ? 'true' : 'false')}
                      className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                    />
                    <label htmlFor={setting.id} className="ml-2 text-sm text-gray-700">
                      {setting.value === 'true' ? 'Enabled' : 'Disabled'}
                    </label>
                  </div>
                ) : isNumberField(setting.key) ? (
                  <div className="flex items-center gap-3">
                    <input
                      type="number"
                      value={setting.value}
                      onChange={(e) => handleChange(setting.id, e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      placeholder={`Enter ${setting.key}`}
                      min="0"
                    />
                    {setting.key.includes('timeout') && (
                      <span className="text-sm text-gray-500">seconds</span>
                    )}
                    {setting.key.includes('size') && (
                      <span className="text-sm text-gray-500">MB</span>
                    )}
                    {setting.key.includes('users') && (
                      <span className="text-sm text-gray-500">users</span>
                    )}
                  </div>
                ) : (
                  <input
                    type="text"
                    value={setting.value}
                    onChange={(e) => handleChange(setting.id, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder={`Enter ${setting.key}`}
                  />
                )}
              </div>
            </div>
            
            {setting.lastUpdated && (
              <div className="mt-3 text-xs text-gray-500">
                Last updated: {new Date(setting.lastUpdated).toLocaleString()} by {setting.updatedBy}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default PerformanceSettings;