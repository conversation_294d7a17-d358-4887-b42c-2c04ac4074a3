import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { 
  LayoutDashboard,
  HeadphonesIcon,
  Ticket,
  FileQuestion,
  BookOpen,
  BookOpenCheck,
  Laptop,
  Smartphone,
  Network,
  Database,
  HardDrive,
  BarChart,
  Shield,
  Globe,
  Server,
  Cloud,
  ShieldCheck,
  KeyRound,
  Bell,
  AlertTriangle,
  FileCheck,
  Building2,
  FileText,
  Users,
  TrendingUp,
  ShoppingCart,
  FileSpreadsheet,
  DollarSign,
  CheckSquare,
  Settings,
  Save,
  Activity,
  ChevronDown,
  ChevronRight,
  FileBox,
  MonitorSmartphone,
  FileCode2,
  ServerCog,
  X,
  ClipboardList,
  Home,
  Plus,
  Package,
  Eye,
  Lock,
  Monitor,
  ChevronLeft,
  ChevronLeftSquare,
  ChevronRightSquare,
  PanelLeftClose,
  PanelLeftOpen,
  Printer,
  Mail,
  History,
  UserPlus,
  CalendarCheck,
  ClipboardCheck,
  Calendar,
  User,
  FolderO<PERSON>,
  Briefcase,
  BarChart<PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>
} from 'lucide-react';

interface MenuItem {
  id: string;
  icon: any;
  label: string;
  color: string;
  submenu?: MenuItem[];
  roles?: string[];
  path?: string;
}

interface SidebarProps {
  currentPage: string;
  onPageChange: (page: string) => void;
  isOpen: boolean;
  onClose: () => void;
  onCollapsedChange?: (collapsed: boolean) => void;
}

// Separate component for flyout menu
const FlyoutMenu = ({ 
  menuId, 
  menuItem, 
  onItemClick, 
  currentPage,
  onClose
}: { 
  menuId: string, 
  menuItem: MenuItem, 
  onItemClick: (id: string) => void,
  currentPage: string,
  onClose: () => void
}) => {
  const menuRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({ top: 0 });
  
  // Calculate position on mount and when window is resized
  useEffect(() => {
    const calculatePosition = () => {
      const menuItemEl = document.querySelector(`[data-menu-id="${menuId}"]`);
      if (menuItemEl) {
        const rect = menuItemEl.getBoundingClientRect();
        // Set the top position to align with the menu item
        setPosition({ top: rect.top });
        console.log('Flyout position calculated:', { top: rect.top });
      }
    };
    
    calculatePosition();
    
    // Recalculate on resize
    window.addEventListener('resize', calculatePosition);
    
    // Set up a small interval to keep the position updated
    // This helps with dynamic content that might shift the position
    const positionInterval = setInterval(calculatePosition, 200);
    
    return () => {
      window.removeEventListener('resize', calculatePosition);
      clearInterval(positionInterval);
    };
  }, [menuId]);

  // Handle clicks outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
        // Check if the click was on the parent menu item
        const menuItemEl = document.querySelector(`[data-menu-id="${menuId}"]`);
        if (!menuItemEl || !menuItemEl.contains(e.target as Node)) {
          onClose();
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [menuId, onClose]);
  
  return (
    <div 
      ref={menuRef}
      className="flyout-submenu"
      style={{ 
        position: 'fixed',
        left: '4rem',
        top: `${position.top}px`,
        minWidth: '200px',
        backgroundColor: '#1a2234',
        borderRadius: '0.5rem',
        zIndex: 9999,
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
      }}
    >
      <div className="px-3 py-2 border-b border-gray-700 mb-1 flex items-center gap-2">
        <menuItem.icon className={`h-4 w-4 ${menuItem.color}`} />
        <span className="text-sm font-medium text-white">{menuItem.label}</span>
      </div>
      {menuItem.submenu!.map(subItem => (
        <button
          key={subItem.id}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onItemClick(subItem.id);
            onClose();
          }}
          className={`w-full flex items-center gap-2 px-4 py-2 text-left text-sm ${
            currentPage === subItem.id
              ? 'bg-gray-700 text-white'
              : 'text-gray-300 hover:bg-gray-700/50 hover:text-white'
          }`}
        >
          {subItem.icon && <subItem.icon className={`h-4 w-4 ${subItem.color}`} />}
          <span>{subItem.label}</span>
        </button>
      ))}
    </div>
  );
};

export function Sidebar({ currentPage, onPageChange, isOpen, onClose, onCollapsedChange }: SidebarProps) {
  const { user } = useAuth();
  const [expandedMenus, setExpandedMenus] = useState<string[]>([]);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [isCollapsed, setIsCollapsed] = useState<boolean>(() => {
    // Get collapsed state from localStorage or default to false (expanded)
    const savedState = localStorage.getItem('sidebarCollapsed');
    return savedState ? JSON.parse(savedState) : false;
  });
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);
  const navigate = useNavigate();
  
  // Keep track of previous open state to handle transitions properly
  const prevOpenRef = useRef(isOpen);
  const sidebarRef = useRef<HTMLDivElement>(null);
  const submenuRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});

  // Save collapsed state to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', JSON.stringify(isCollapsed));
    // Notify parent component about collapsed state change if callback is provided
    if (onCollapsedChange) {
      onCollapsedChange(isCollapsed);
    }
  }, [isCollapsed, onCollapsedChange]);

  // Close flyout submenu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isCollapsed && activeSubmenu) {
        // Check if the click was on the sidebar
        if (sidebarRef.current && !sidebarRef.current.contains(event.target as Node)) {
          // Check if the click was on a flyout menu
          const flyoutMenu = document.querySelector('.flyout-submenu');
          if (!flyoutMenu || !flyoutMenu.contains(event.target as Node)) {
            setActiveSubmenu(null);
          }
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isCollapsed, activeSubmenu]);

  // Close active submenu when navigating to a new page
  useEffect(() => {
    setActiveSubmenu(null);
  }, [currentPage]);

  // Debug log for sidebar state changes
  useEffect(() => {
    console.log('Sidebar isOpen state changed:', isOpen);
    
    // Force a reflow when isOpen changes to ensure CSS transitions work
    if (sidebarRef.current) {
      // Force a reflow by accessing offsetHeight
      // eslint-disable-next-line no-unused-expressions
      sidebarRef.current.offsetHeight;
    }
  }, [isOpen]);

  // Auto-expand parent menu when a submenu item is active
  useEffect(() => {
    // Find the parent menu of the current page
    const findParentMenu = () => {
      for (const item of menuItems) {
        if (item.submenu?.some(subItem => subItem.id === currentPage)) {
          return item.id;
        }
      }
      return null;
    };

    const parentMenu = findParentMenu();
    console.log('Current page:', currentPage, 'Parent menu:', parentMenu);
    
    if (parentMenu && !expandedMenus.includes(parentMenu)) {
      console.log('Auto-expanding parent menu:', parentMenu);
      setExpandedMenus(prev => [...prev, parentMenu]);
    }
  }, [currentPage, expandedMenus]);

  // Handle sidebar state changes
  useEffect(() => {
    // If sidebar state changed from closed to open, ensure proper rendering
    if (isOpen && !prevOpenRef.current) {
      console.log('Sidebar transitioning from closed to open');
      // Force a reflow to ensure transitions work properly
      const forceReflow = () => {
        if (sidebarRef.current) {
          sidebarRef.current.classList.add('sidebar-force-reflow');
          // Force a reflow by accessing offsetHeight
          // eslint-disable-next-line no-unused-expressions
          sidebarRef.current.offsetHeight;
          
          // Remove the class after a short delay
          setTimeout(() => {
            if (sidebarRef.current) {
              sidebarRef.current.classList.remove('sidebar-force-reflow');
            }
          }, 10);
        }
      };
      
      // Execute the reflow after a short delay to ensure DOM is ready
      setTimeout(forceReflow, 50);
    }
    
    prevOpenRef.current = isOpen;
  }, [isOpen]);

  // Toggle sidebar collapsed state
  const toggleCollapsed = () => {
    setIsCollapsed(prev => !prev);
    // Close any open flyout submenu when toggling
    setActiveSubmenu(null);
  };

  // Detect if we're on mobile
  const isMobile = () => window.innerWidth < 1024;

  // Handle swipe to close
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.touches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!touchStart) return;
    
    const currentTouch = e.touches[0].clientX;
    const diff = touchStart - currentTouch;

    // If swiped left more than 50px, close the sidebar
    if (diff > 50) {
      onClose();
      setTouchStart(null);
    }
  };

  const handleTouchEnd = () => {
    setTouchStart(null);
  };

  // Handle page navigation with React Router
  const handlePageChange = (pageId: string, path?: string) => {
    console.log('Navigating to:', { pageId, path }); // Debug log
    if (path) {
      navigate(path);
    } else {
      // Handle special cases for items without explicit paths
      if (pageId === 'service-desk') {
        navigate('/service-desk');
      } else if (pageId === 'dashboard') {
        navigate('/dashboard');
      } else if (pageId === 'vendor-management') {
        navigate('/vendor-management');
      } else if (['services', 'contracts', 'agreements', 'performance', 'contacts'].includes(pageId)) {
        navigate(`/vendor-management/${pageId}`);
        // HR Management specific routing
      } else if (pageId === 'hr-management') {
        navigate('/hr/dashboard');
      } else if (pageId === 'employees') {
        navigate('/hr/employees');
      } else if (pageId === 'add-employee') {
        navigate('/hr/addemployee');
      } else if (pageId === 'attendance') {
        navigate('/hr/attendance');
      } else if (pageId === 'payroll-management') {
        navigate('/hr/payroll');
      } else if (pageId === 'recruitment') {
        navigate('/hr/recruitment');
      } else if (pageId === 'leave-management') {
        navigate('/hr/leave-management/overview');
        // Service Desk specific routing
      } else if (pageId === 'create-ticket') {
        navigate('/service-desk/create-ticket');
      } else if (pageId === 'tickets') {
        navigate('/service-desk/tickets');
      } else if (pageId === 'knowledge') {
        navigate('/service-desk/knowledge');
        // Asset Management specific routing
      } else if (pageId === 'hardware') {
        navigate('/asset-management/hardware');
      } else if (pageId === 'software-management') {
        navigate('/asset-management/software');
      } else if (pageId === 'mobile-devices') {
        navigate('/asset-management/mobile-devices');
      } else if (pageId === 'network-management') {
        navigate('/asset-management/network-devices');
        // System Admin specific routing
      } else if (pageId === 'users') {
        navigate('/users');
      } else if (pageId === 'roles') {
        navigate('/roles');
      } else if (pageId === 'system-logs') {
        navigate('/system-logs');
      } else if (pageId === 'system-settings') {
        navigate('/system-settings');
      } else if (pageId === 'company-profile') {
        navigate('/company-profile');
      } else {
        // For any other pageId, try to navigate directly to that route
        navigate(`/${pageId}`);
      }
    }
    onPageChange(pageId);
    if (window.innerWidth < 1024) {
      onClose();
    }
  };

  // Handle escape key to close sidebar
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleEscape);
    return () => window.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  const menuItems: MenuItem[] = [
    {
      id: 'dashboard',
      icon: LayoutDashboard,
      label: 'Dashboard',
      color: 'text-blue-400',
      roles: ['IT_ADMIN', 'SYSTEM_ADMIN', 'IT_STAFF', 'HR_ADMIN', 'DEPT_HEAD', 'DASHBOARD_MANAGER', 'ANALYTICS_VIEWER']
    },
    {
      id: 'employee-portal',
      icon: User,
      label: 'Employee Portal',
      color: 'text-pink-400',
      path: '/employee-portal',
      roles: ['EMPLOYEE', 'SENIOR_EMPLOYEE', 'IT_ADMIN', 'SYSTEM_ADMIN', 'IT_STAFF', 'HR_STAFF', 'HR_ADMIN', 'HR_SPECIALIST', 'DEPT_HEAD', 'PROJECT_MANAGER', 'FINANCE_MANAGER', 'SECURITY_OFFICER'] // Allow all employees to access this
    },
    {
      id: 'project-management',
      icon: FolderOpen,
      label: 'Project Management',
      color: 'text-purple-400',
      path: '/project-management',
      roles: ['IT_ADMIN', 'SYSTEM_ADMIN', 'PROJECT_MANAGER', 'DEPT_HEAD', 'IT_STAFF', 'HR_ADMIN', 'SENIOR_EMPLOYEE'],
      submenu: [
        { id: 'project-dashboard', icon: BarChart, label: 'Dashboard', color: 'text-purple-400', path: '/project-management' },
        { id: 'projects', icon: FolderOpen, label: 'Projects', color: 'text-purple-400', path: '/projects' },
        { id: 'tasks', icon: CheckSquare, label: 'Tasks', color: 'text-purple-400', path: '/tasks' },
        { id: 'project-calendar', icon: Calendar, label: 'Calendar', color: 'text-purple-400', path: '/project-calendar' }
      ]
    },
    {
      id: 'service-desk',
      icon: HeadphonesIcon,
      label: 'Service Desk',
      color: 'text-green-400',
      path: '/service-desk',
      roles: ['IT_ADMIN', 'SYSTEM_ADMIN', 'IT_STAFF', 'IT_SUPPORT', 'IT_TECHNICIAN', 'EMPLOYEE', 'SENIOR_EMPLOYEE', 'DEPT_HEAD', 'PROJECT_MANAGER', 'HR_ADMIN', 'HR_STAFF'],
      submenu: [
        { id: 'create-ticket', icon: Plus, label: 'Create Ticket', color: 'text-green-400', path: '/service-desk/create-ticket' },
        { id: 'tickets', icon: Ticket, label: 'All Tickets', color: 'text-green-400', path: '/service-desk/tickets' },
        { id: 'knowledge', icon: BookOpen, label: 'Knowledge Base', color: 'text-green-400', path: '/service-desk/knowledge' }
      ]
    },
    {
      id: 'hr-management',
      icon: Users,
      label: 'HR Management',
      color: 'text-indigo-400',
      path: '/hr/dashboard',
      roles: ['IT_ADMIN', 'SYSTEM_ADMIN', 'HR_ADMIN', 'HR_STAFF', 'HR_SPECIALIST', 'HR-Users', 'DEPT_HEAD'],
      submenu: [
        { 
          id: 'employees',
          icon: Users,
          label: 'Employee Management',
          color: 'text-indigo-400',
          path: '/hr/employees'
        },
        {
          id: 'add-employee',
          icon: Plus,
          label: 'Add Employee',
          color: 'text-indigo-400',
          path: '/hr/addemployee'
        },
        {
          id: 'attendance',
          icon: CalendarCheck,
          label: 'Attendance Management',
          color: 'text-indigo-400',
          path: '/hr/attendance'
        },
        {
          id: 'leave-management',
          icon: Calendar,
          label: 'Leave Management',
          color: 'text-indigo-400',
          path: '/hr/leave-management/overview'
        },
        {
          id: 'payroll-management',
          icon: DollarSign,
          label: 'Payroll Management',
          color: 'text-indigo-400',
          path: '/hr/payroll'
        },
        {
          id: 'recruitment',
          icon: Briefcase,
          label: 'Recruitment Management',
          color: 'text-indigo-400',
          path: '/hr/recruitment'
        },
        {
          id: 'team-dashboard',
          icon: Users,
          label: 'Team Dashboard',
          color: 'text-indigo-400',
          path: '/hr/team-dashboard'
        }
      ]
    },
    {
      id: 'email-management',
      icon: Mail,
      label: 'Email Management',
      color: 'text-indigo-400',
      roles: ['IT_ADMIN', 'SYSTEM_ADMIN', 'IT_STAFF', 'IT_SUPPORT'],
      submenu: [
        { id: 'create-email', icon: Plus, label: 'Create Email', color: 'text-indigo-400', path: '/email-management/create' },
        { id: 'email-accounts', icon: UserPlus, label: 'Email Accounts', color: 'text-indigo-400', path: '/email-management/accounts' }
      ]
    },
    {
      id: 'asset-management',
      icon: Laptop,
      label: 'Asset Management',
      color: 'text-purple-400',
      path: '/asset-management',
      roles: ['IT_ADMIN', 'IT_STAFF'],
      submenu: [
        { id: 'hardware', icon: MonitorSmartphone, label: 'Hardware', color: 'text-purple-400', path: '/asset-management/hardware' },
        { id: 'software-management', icon: Package, label: 'Software', color: 'text-purple-400', path: '/asset-management/software' },
        { id: 'mobile-devices', icon: Smartphone, label: 'Mobile Devices', color: 'text-purple-400', path: '/asset-management/mobile-devices' },
        { id: 'network-management', icon: Network, label: 'Network Devices', color: 'text-purple-400', path: '/asset-management/network-devices' }
      ]
    },
    {
      id: 'it-procurement',
      icon: ShoppingCart,
      label: 'IT Procurement',
      color: 'text-green-400',
      roles: ['IT_ADMIN', 'IT_STAFF'],
      submenu: [
        { id: 'purchase-requests', icon: FileSpreadsheet, label: 'Purchase Requests', color: 'text-green-400' },
        { id: 'purchase-orders', icon: ShoppingCart, label: 'Purchase Orders', color: 'text-green-400' },
        { id: 'budget', icon: DollarSign, label: 'Budget', color: 'text-green-400' },
        { id: 'approvals', icon: CheckSquare, label: 'Approvals', color: 'text-green-400' }
      ]
    },
    {
      id: 'vendor-management',
      icon: Building2,
      label: 'Vendor Management',
      color: 'text-orange-400',
      roles: ['IT_ADMIN', 'IT_STAFF'],
      submenu: [
        { id: 'services', icon: FileText, label: 'Services', color: 'text-orange-400', path: '/vendor-management/services' },
        { id: 'contracts', icon: FileText, label: 'Contracts', color: 'text-orange-400', path: '/vendor-management/contracts' },
        { id: 'agreements', icon: FileText, label: 'Agreements', color: 'text-orange-400', path: '/vendor-management/agreements' },
        { id: 'performance', icon: BarChart, label: 'Performance', color: 'text-orange-400', path: '/vendor-management/performance' },
        { id: 'contacts', icon: Users, label: 'Contacts', color: 'text-orange-400', path: '/vendor-management/contacts' },
        { id: 'printer-maintenance', icon: Printer, label: 'Printer Maintenance', color: 'text-orange-400', path: '/printer-maintenance' }
      ]
    },
    {
      id: 'infrastructure',
      icon: ServerCog,
      label: 'IT Infrastructure',
      color: 'text-blue-400',
      roles: ['IT_ADMIN', 'IT_STAFF'],
      submenu: [
        { id: 'servers', icon: Server, label: 'Servers', color: 'text-blue-400' },
        { id: 'storage', icon: HardDrive, label: 'Storage', color: 'text-blue-400' },
        { id: 'network', icon: Network, label: 'Network', color: 'text-blue-400' },
        { id: 'cloud-services', icon: Cloud, label: 'Cloud Services', color: 'text-blue-400' }
      ]
    },
    {
      id: 'domain-hosting',
      icon: Globe,
      label: 'Domain & Hosting',
      color: 'text-orange-400',
      roles: ['IT_ADMIN', 'IT_STAFF'],
      submenu: [
        { id: 'domain-management', icon: Globe, label: 'Domain Management', color: 'text-orange-400' },
        { id: 'hosting-services', icon: Server, label: 'Hosting Services', color: 'text-orange-400' },
        { id: 'ssl-certificates', icon: Shield, label: 'SSL Certificates', color: 'text-orange-400' }
      ]
    },
    {
      id: 'database-management',
      icon: Database,
      label: 'Database Management',
      color: 'text-cyan-400',
      roles: ['IT_ADMIN', 'IT_STAFF'],
      submenu: [
        { id: 'database-instances', icon: HardDrive, label: 'Database Instances', color: 'text-cyan-400' },
        { id: 'backup-management', icon: Save, label: 'Backup Management', color: 'text-cyan-400' },
        { id: 'performance-monitoring', icon: Activity, label: 'Performance Monitoring', color: 'text-cyan-400' }
      ]
    },
    {
      id: 'it-operations-log',
      icon: FileText,
      label: 'IT Operation Logs',
      color: 'text-yellow-400',
      path: '/it-logs',
      roles: ['IT_ADMIN', 'IT_STAFF'] // adjust based on your role system
    },
    
    {
      id: 'security',
      icon: ShieldCheck,
      label: 'Security Management',
      color: 'text-red-400',
      roles: ['IT_ADMIN', 'IT_STAFF'],
      submenu: [
        { id: 'access-control', icon: Lock, label: 'Access Control', color: 'text-red-400' },
        { id: 'security-monitoring', icon: Eye, label: 'Security Monitoring', color: 'text-red-400' },
        { id: 'incidents', icon: AlertTriangle, label: 'Incidents', color: 'text-red-400' },
        { id: 'compliance', icon: FileCheck, label: 'Compliance', color: 'text-red-400' }
      ]
    },
    {
      id: 'it-billing',
      icon: DollarSign,
      label: 'IT Billing & Invoicing',
      color: 'text-green-400',
      roles: ['IT_ADMIN', 'IT_STAFF'],
      submenu: [
        { 
          id: 'billing-invoice',
          icon: FileText,
          label: 'Billing & Invoices',
          color: 'text-green-400',
          path: '/it-billing/billing-invoice'
        },
        { 
          id: 'budget-tracking',
          icon: BarChart,
          label: 'Budget Tracking',
          color: 'text-green-400',
          path: '/it-billing/budget-tracking'
        }
      ]
    },
    {
      id: 'reports-analytics',
      icon: TrendingUp,
      label: 'Reports & Analytics',
      color: 'text-blue-400',
      roles: ['IT_ADMIN', 'IT_STAFF'],
      submenu: [
        { id: 'performance', icon: BarChart, label: 'Performance', color: 'text-blue-400' },
        { id: 'usage-statistics', icon: TrendingUp, label: 'Usage Statistics', color: 'text-blue-400' },
        { id: 'audit-logs', icon: FileBox, label: 'Audit Logs', color: 'text-blue-400' },
        { id: 'cost-analysis', icon: DollarSign, label: 'Cost Analysis', color: 'text-blue-400' }
      ]
    },
    {
      id: 'system-admin',
      icon: Settings,
      label: 'System Admin',
      color: 'text-purple-400',
      roles: ['IT_ADMIN'],
      submenu: [
        { id: 'users', icon: Users, label: 'Users', color: 'text-purple-400' },
        { id: 'roles', icon: Shield, label: 'Roles & Permissions', color: 'text-purple-400' },
        { id: 'system-logs', icon: Activity, label: 'System Logs', color: 'text-purple-400', path: '/system-logs' },
        { id: 'system-settings', icon: Settings, label: 'Settings', color: 'text-purple-400' },
        { id: 'company-profile', icon: Building2, label: 'Company Profile', color: 'text-purple-400', path: '/company-profile' }
      ]
    }
  ];

  const filteredMenuItems = menuItems.filter(item => {
    if (!user || !item.roles) return false;
    return item.roles.includes(user.role);
  });

  const toggleSubmenu = (menuId: string) => {
    console.log('Toggling submenu:', menuId, 'Current expanded menus:', expandedMenus);
    
    if (isCollapsed) {
      // In collapsed mode, we use the flyout menu
      console.log('Collapsed mode: toggling flyout menu for', menuId, 'current active:', activeSubmenu);
      
      // If this menu is already active, close it, otherwise open it and close any other open menu
      setActiveSubmenu(prevActive => prevActive === menuId ? null : menuId);
    } else {
      // In expanded mode, we use the original accordion behavior
      // Check if this menu is already expanded
      const isCurrentlyExpanded = expandedMenus.includes(menuId);
      console.log('Is currently expanded:', isCurrentlyExpanded);
      
      // Force a small delay to ensure state updates properly
      setTimeout(() => {
        // Update state based on current expansion state
        setExpandedMenus(prev => {
          const newState = isCurrentlyExpanded
            ? prev.filter(id => id !== menuId)
            : [...prev, menuId];
          
          console.log('New expanded menus state:', newState);
          return newState;
        });
        
        // Force a reflow to ensure the UI updates
        if (sidebarRef.current) {
          // Force a reflow by accessing offsetHeight
          // eslint-disable-next-line no-unused-expressions
          sidebarRef.current.offsetHeight;
        }
      }, 10);
    }
  };

  const renderMenuItem = (item: MenuItem, level: number = 0) => {
    // Check if this item is active directly or if any of its submenu items are active
    const isActive = currentPage === item.id;
    const hasActiveSubmenu = item.submenu?.some(subItem => currentPage === subItem.id) || false;
    const isExpanded = expandedMenus.includes(item.id);
    const hasSubmenu = item.submenu && item.submenu.length > 0;
    const Icon = item.icon;
    const indentClass = level > 0 ? 'pl-4' : '';
    const isFlyoutActive = activeSubmenu === item.id;

    if (!item.roles || (user && item.roles.includes(user.role))) {
      return (
        <div key={item.id} className={`${indentClass} relative`}>
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('Menu item clicked:', item.id, 'Has submenu:', hasSubmenu);
              
              if (hasSubmenu) {
                toggleSubmenu(item.id);
              } else {
                handlePageChange(item.id, item.path);
                setActiveSubmenu(null);
              }
            }}
            data-expanded={isExpanded || isFlyoutActive ? 'true' : 'false'}
            data-has-submenu={hasSubmenu ? 'true' : 'false'}
            data-menu-id={item.id}
            aria-label={hasSubmenu && isCollapsed ? `${item.label} (menu)` : item.label}
            className={`sidebar-menu-item w-full flex ${isCollapsed ? 'justify-center items-center' : 'items-start'} gap-2 px-3 py-2 rounded-lg transition-colors ${
              isActive || hasActiveSubmenu || isFlyoutActive
                ? 'bg-gray-700 text-white'
                : 'text-gray-300 hover:bg-gray-700/50 hover:text-white'
            }`}
          >
            <Icon className={`${isCollapsed ? 'h-5 w-5' : 'h-5 w-5'} ${item.color}`} />
            {!isCollapsed && (
              <span className="flex-1 text-sm font-medium text-start transition-opacity duration-200">{item.label}</span>
            )}
            {hasSubmenu && !isCollapsed && (
              <div className={`transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}>
                <ChevronDown className="h-4 w-4" />
              </div>
            )}
          </button>
          
          {/* Flyout submenu for collapsed state */}
          {hasSubmenu && isCollapsed && isFlyoutActive && (
            <FlyoutMenu 
              menuId={item.id}
              menuItem={item}
              onItemClick={handlePageChange}
              currentPage={currentPage}
              onClose={() => setActiveSubmenu(null)}
            />
          )}
          
          {/* Regular accordion submenu for expanded state */}
          {hasSubmenu && !isCollapsed && (
            <div className={`sidebar-submenu mt-1 space-y-1 ${isExpanded ? 'sidebar-submenu-expanded' : 'sidebar-submenu-collapsed'}`}>
              {isExpanded && item.submenu!.map(subItem => renderMenuItem(subItem, level + 1))}
            </div>
          )}
        </div>
      );
    }
    return null;
  };

  // Debug log for activeSubmenu changes
  useEffect(() => {
    console.log('Active submenu changed:', activeSubmenu);
  }, [activeSubmenu]);

  return (
    <aside
      ref={sidebarRef}
      className={`sidebar-container fixed inset-y-0 left-0 ${isCollapsed ? 'w-16' : 'w-64'} bg-[#1a2234] dark:bg-gray-900 flex flex-col z-30 transition-all duration-300 ease-in-out`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      data-testid="sidebar"
      data-collapsed={isCollapsed ? 'true' : 'false'}
      style={{ width: isCollapsed ? '4rem' : '16rem' }}
    >
      <div className={`flex items-center h-16 ${isCollapsed ? 'px-3 justify-center' : 'px-6'} border-b border-gray-700`}>
        {!isCollapsed ? (
          <>
            <div className="flex items-center gap-3">
              <Monitor className="h-6 w-6 text-blue-400" />
              <span className="text-lg font-medium text-white">Grand IT-MS</span>
            </div>
            <button
              onClick={onClose}
              className="lg:hidden ml-auto p-2 rounded-lg text-gray-400 hover:text-white hover:bg-gray-700"
            >
              <X className="h-5 w-5" />
            </button>
          </>
        ) : (
          <Monitor className="h-6 w-6 text-blue-400" />
        )}
      </div>

      <nav className={`flex-1 overflow-y-auto py-4 ${isCollapsed ? 'px-1' : 'px-3'}`}>
        <div className="space-y-1">
          {filteredMenuItems.map(item => renderMenuItem(item))}
        </div>
      </nav>
      
      {/* Collapse/Expand Toggle Button */}
      <div className="border-t border-gray-700 p-3 flex justify-center">
        <button 
          onClick={toggleCollapsed}
          className="p-2 rounded-lg text-gray-400 hover:text-white hover:bg-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50"
          aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          title={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {isCollapsed ? (
            <PanelLeftOpen className="h-5 w-5" />
          ) : (
            <PanelLeftClose className="h-5 w-5" />
          )}
        </button>
      </div>
    </aside>
  );
}