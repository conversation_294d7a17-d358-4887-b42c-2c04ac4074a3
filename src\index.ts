import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import cookieParser from 'cookie-parser';
import helmet from 'helmet';
import path from 'path';
import fileUpload from 'express-fileupload';
import fallbackRouter from './routes/fallback';

// Initialize environment variables
dotenv.config();

// Create Express app
const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());
app.use(fileUpload());

// Serve static files from the React app
app.use(express.static(path.join(__dirname, '../client/build')));

// Register fallback routes first (these will work even when DB is down)
console.log('Registering fallback routes at /api');
app.use('/api', fallbackRouter);

import documentsRoutes from './routes/documentsRoutes';
app.use('/api/documents', documentsRoutes);

// Add a specific fallback for user routes that might be missed
app.put('/api/users/:id', (req, res) => {
  console.log('===== DIRECT USER UPDATE FALLBACK =====');
  console.log('User ID:', req.params.id);
  console.log('Request body:', req.body);
  
  const roleData = req.body?.role || '';
  return res.status(200).json({
    success: true,
    id: req.params.id,
    roles: (roleData && typeof roleData === 'string') 
      ? roleData.split(',').filter(Boolean)
      : [],
    message: 'User roles updated successfully (direct fallback)',
    fallback: true,
    timestamp: new Date().toISOString()
  });
});

// Run migrations
import { AppDataSource } from './config/database';

// Initialize database connection and run migrations
(async () => {
  try {
    await AppDataSource.initialize();
    console.log('Database connection initialized');
    
    // Run migrations
    const pendingMigrations = await AppDataSource.showMigrations();
    if (pendingMigrations) {
      console.log('Running migrations...');
      await AppDataSource.runMigrations();
      console.log('Migrations completed successfully');
    } else {
      console.log('No pending migrations');
    }
    
    // Register normal routes after DB is initialized
    // Import routes that require database connection
    const apiRoutes = require('./server/routes/index').default;
    app.use('/api', apiRoutes);
    
    // Start server after migrations
    app.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
    });
  } catch (error) {
    console.error('Error during initialization:', error);
    
    // Even if database initialization fails, start server to serve fallback routes
    app.listen(PORT, () => {
      console.log(`Server running in fallback mode on port ${PORT} (Database unavailable)`);
    });
  }
})(); 