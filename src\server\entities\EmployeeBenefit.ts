import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn
} from 'typeorm';
import { Employee } from './Employee';

@Entity('employee_benefits')
export class EmployeeBenefit {
  @PrimaryGeneratedColumn()
  id: number;

  // Compensation
  @Column({ type: 'varchar', length: 255,  nullable: true })
  totalSalary: string;
  
  @Column({ type: 'varchar', length: 255,  nullable: true })
  salaryTier: string;
  
  @Column({ type: 'varchar', length: 255,  nullable: true })
  salaryTrench: string;
  
  @Column({ type: 'varchar', length: 255,  nullable: true })
  cashAmount: string;
  
  @Column({ type: 'varchar', length: 255,  nullable: true })
  bankAmount: string;
  
  @Column({ type: 'varchar', length: 255,  nullable: true })
  paymentMode: string;
  
  // Allowances
  @Column({ type: 'boolean',  nullable: true, default: false })
  foodAllowanceInSalary: boolean;
  
  @Column({ type: 'boolean',  nullable: true, default: false })
  fuelAllowanceInSalary: boolean;
  
  @Column({ type: 'varchar', length: 20,  nullable: true })
  numberOfMeals: string;
  
  @Column({ type: 'varchar', length: 255,  nullable: true })
  fuelInLiters: string;
  
  @Column({ type: 'varchar', length: 255,  nullable: true })
  fuelAmount: string;
  
  @Column({ type: 'boolean',  nullable: true, default: false })
  foodProvidedByCompany: boolean;
  
  // Bank Details
  @Column({ type: 'varchar', length: 100,  nullable: true })
  bankName: string;
  
  @Column({ type: 'varchar', length: 255,  nullable: true })
  bankBranch: string;
  
  @Column({ type: 'varchar', length: 20,  nullable: true })
  accountNumber: string;
  
  @Column({ type: 'varchar', length: 100,  nullable: true })
  accountTitle: string;
  
  @Column({ type: 'varchar', length: 255,  nullable: true })
  iban: string;
  
  // Health & Insurance
  @Column({ type: 'varchar', length: 255,  nullable: true })
  healthInsuranceProvider: string;
  
  @Column({ type: 'varchar', length: 20,  nullable: true })
  healthInsurancePolicyNumber: string;
  
  @Column({ type: 'date',  nullable: true })
  healthInsuranceExpiryDate: string;
  
  @Column({ type: 'varchar', length: 255,  nullable: true })
  lifeInsuranceProvider: string;
  
  @Column({ type: 'varchar', length: 20,  nullable: true })
  lifeInsurancePolicyNumber: string;
  
  @Column({ type: 'date',  nullable: true })
  lifeInsuranceExpiryDate: string;
  
  // Accommodation
  @Column({ type: 'boolean',  nullable: true, default: false })
  accommodationProvidedByEmployer: boolean;
  
  @Column({ type: 'varchar', length: 50,  nullable: true })
  accommodationType: string;
  
  @Column({ type: 'text', nullable: true })
  accommodationAddress: string;
  
  // Relation to Employee
  @OneToOne(() => Employee, employee => employee.benefit, { onDelete: 'CASCADE' })
  @JoinColumn()
  employee: Employee;

  // System columns
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 