import React, { useState, useEffect } from 'react';
import { User, Filter, Clock, AlertTriangle } from 'lucide-react';

interface UserActivityStats {
  username: string;
  actionCount: number;
  lastActivity: Date | null;
  typeCounts: {
    info: number;
    success: number;
    warning: number;
    error: number;
  };
}

interface UserActivitySummaryProps {
  logs: Array<{
    user: string;
    type: 'info' | 'warning' | 'error' | 'success';
    timestamp: string;
  }>;
  onSelectUser: (username: string) => void;
}

export const UserActivitySummary: React.FC<UserActivitySummaryProps> = ({ logs, onSelectUser }) => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [showAll, setShowAll] = useState(false);
  
  // Update current time every minute for relative time displays
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute
    
    return () => clearInterval(interval);
  }, []);
  
  // Format relative time (e.g., "5 minutes ago")
  const getRelativeTime = (date: Date) => {
    const diffMs = currentTime.getTime() - date.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffSecs < 60) return 'just now';
    if (diffMins < 60) return `${diffMins} ${diffMins === 1 ? 'minute' : 'minutes'} ago`;
    if (diffHours < 24) return `${diffHours} ${diffHours === 1 ? 'hour' : 'hours'} ago`;
    if (diffDays < 7) return `${diffDays} ${diffDays === 1 ? 'day' : 'days'} ago`;
    
    return date.toLocaleDateString();
  };
  
  // Get unique users from logs
  const uniqueLogUsers = Array.from(new Set(logs.map(log => log.user)));
  
  // For empty state
  if (!logs || logs.length === 0) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
        <div className="flex items-center gap-2 text-yellow-800">
          <AlertTriangle className="h-5 w-5 text-yellow-600" />
          <span className="font-medium">No activity logs available</span>
        </div>
        <p className="text-sm text-yellow-700 mt-1">
          User activity will appear here once actions are recorded in the system.
        </p>
      </div>
    );
  }
  
  // Calculate user activity statistics
  const userActivityStats = uniqueLogUsers.map(username => {
    const userLogs = logs.filter(log => log.user === username);
    const actionCount = userLogs.length;
    const lastActivity = userLogs.length > 0 ? 
      new Date(Math.max(...userLogs.map(log => new Date(log.timestamp).getTime()))) : 
      null;
    
    // Count actions by type
    const typeCounts = {
      info: userLogs.filter(log => log.type === 'info').length,
      success: userLogs.filter(log => log.type === 'success').length,
      warning: userLogs.filter(log => log.type === 'warning').length,
      error: userLogs.filter(log => log.type === 'error').length
    };
    
    return {
      username,
      actionCount,
      lastActivity,
      typeCounts
    };
  }).sort((a, b) => b.actionCount - a.actionCount); // Sort by most active
  
  // Top users or all if requested
  const displayUsers = showAll ? userActivityStats : userActivityStats.slice(0, 5);
  
  if (displayUsers.length === 0) {
    return null;
  }
  
  return (
    <div className="bg-gray-50 rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-md font-medium text-gray-700">
          Real-time User Activity Summary
          <span className="ml-2 text-xs text-gray-500">
            (As of {currentTime.toLocaleTimeString()})
          </span>
        </h3>
        {userActivityStats.length > 5 && (
          <button 
            onClick={() => setShowAll(!showAll)}
            className="text-xs text-blue-600 hover:text-blue-800 py-1 px-2 rounded border border-blue-200 bg-blue-50"
          >
            {showAll ? 'Show Top 5' : 'Show All Users'}
          </button>
        )}
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-4">
        {displayUsers.map(userStat => (
          <div key={userStat.username} className="bg-white p-3 rounded-lg shadow-sm hover:shadow transition-shadow duration-200">
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center gap-1">
                <User className="h-4 w-4 text-gray-400" />
                <span className="font-medium text-sm truncate" title={userStat.username}>
                  {userStat.username}
                </span>
              </div>
              <button 
                onClick={() => onSelectUser(userStat.username)}
                className="text-xs text-blue-600 hover:text-blue-800"
              >
                View Logs
              </button>
            </div>
            <div className="flex flex-col text-xs">
              <div className="flex justify-between text-gray-600">
                <span>Actions:</span>
                <span className="font-medium">{userStat.actionCount}</span>
              </div>
              <div className="flex justify-between text-gray-600">
                <span>Last Activity:</span>
                <span className="font-medium flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {userStat.lastActivity ? getRelativeTime(userStat.lastActivity) : '-'}
                </span>
              </div>
              
              {/* Action Type Distribution */}
              <div className="mt-2">
                <div className="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                  {userStat.actionCount > 0 && (
                    <>
                      <div 
                        className="h-full bg-blue-500" 
                        style={{ 
                          width: `${(userStat.typeCounts.info / userStat.actionCount) * 100}%`,
                          float: 'left'
                        }} 
                        title={`Info: ${userStat.typeCounts.info}`}
                      />
                      <div 
                        className="h-full bg-green-500" 
                        style={{ 
                          width: `${(userStat.typeCounts.success / userStat.actionCount) * 100}%`,
                          float: 'left'
                        }}
                        title={`Success: ${userStat.typeCounts.success}`}
                      />
                      <div 
                        className="h-full bg-yellow-500" 
                        style={{ 
                          width: `${(userStat.typeCounts.warning / userStat.actionCount) * 100}%`,
                          float: 'left'
                        }}
                        title={`Warning: ${userStat.typeCounts.warning}`}
                      />
                      <div 
                        className="h-full bg-red-500" 
                        style={{ 
                          width: `${(userStat.typeCounts.error / userStat.actionCount) * 100}%`,
                          float: 'left'
                        }}
                        title={`Error: ${userStat.typeCounts.error}`}
                      />
                    </>
                  )}
                </div>
                
                <div className="flex justify-between text-[9px] mt-1 text-gray-500">
                  <span>Info: {userStat.typeCounts.info}</span>
                  <span>Success: {userStat.typeCounts.success}</span>
                  <span>Warning: {userStat.typeCounts.warning}</span>
                  <span>Error: {userStat.typeCounts.error}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}; 