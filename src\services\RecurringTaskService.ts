import { AppDataSource } from '../config/database';
import { Task, TaskRecurrenceType, TaskStatus } from '../entities/Task';
import { Project } from '../entities/Project';
import { User } from '../entities/User';
import { In, Not } from 'typeorm';
import * as cron from 'node-cron';

export class RecurringTaskService {
  private taskRepository: any;
  private projectRepository: any;
  private userRepository: any;
  private scheduledJobs: Map<string, cron.ScheduledTask> = new Map();

  constructor() {
    try {
      this.taskRepository = AppDataSource.getRepository(Task);
      this.projectRepository = AppDataSource.getRepository(Project);
      this.userRepository = AppDataSource.getRepository(User);
      this.initializeService();
    } catch (error) {
      console.error('❌ Error initializing RecurringTaskService repositories:', error);
      console.log('⚠️ Continuing without repository initialization...');
    }
  }

  private async initializeService() {
    console.log('🔄 Initializing Recurring Task Service...');
    
    // Schedule the main recurring task processor to run every hour
    const mainJob = cron.schedule('0 * * * *', async () => {
      await this.processRecurringTasks();
    }, {
      timezone: 'UTC'
    });

    this.scheduledJobs.set('main-processor', mainJob);
    
    // Load existing recurring tasks and schedule them
    await this.loadAndScheduleExistingTasks();
    
    console.log('✅ Recurring Task Service initialized successfully');
  }

  private async loadAndScheduleExistingTasks() {
    try {
      // Check if database connection is available and Task entity exists
      if (!AppDataSource.isInitialized) {
        console.warn('⚠️ Database not initialized, skipping recurring task loading');
        return;
      }

      // Check if Task entity is registered
      const taskMetadata = AppDataSource.getMetadata(Task);
      if (!taskMetadata) {
        console.warn('⚠️ Task entity not found in database metadata, skipping recurring task loading');
        return;
      }

      const recurringTasks = await this.taskRepository.find({
        where: {
          recurrenceType: In([TaskRecurrenceType.DAILY, TaskRecurrenceType.WEEKLY, TaskRecurrenceType.MONTHLY, TaskRecurrenceType.QUARTERLY, TaskRecurrenceType.YEARLY]),
          isActive: true,
          isTemplate: false
        },
        relations: ['project', 'assignedTo', 'createdBy']
      });

      console.log(`📋 Found ${recurringTasks.length} recurring tasks to schedule`);

      for (const task of recurringTasks) {
        await this.scheduleRecurringTask(task);
      }
    } catch (error) {
      console.error('❌ Error loading existing recurring tasks:', error);
      // Don't throw the error, just log it and continue
      console.log('⚠️ Continuing without recurring task scheduling...');
    }
  }

  async createRecurringTask(taskData: Partial<Task>): Promise<Task> {
    try {
      // Validate recurrence configuration
      if (!taskData.recurrenceType || taskData.recurrenceType === TaskRecurrenceType.NONE) {
        throw new Error('Recurrence type is required for recurring tasks');
      }

      if (!taskData.recurrenceConfig) {
        throw new Error('Recurrence configuration is required');
      }

      // Create the recurring task
      const recurringTask = this.taskRepository.create({
        ...taskData,
        isTemplate: false // Recurring tasks are not templates
      });

      const savedTask = await this.taskRepository.save(recurringTask);

      // Schedule the recurring task
      await this.scheduleRecurringTask(savedTask);

      console.log(`✅ Created recurring task: ${savedTask.title} (ID: ${savedTask.id})`);
      return savedTask;
    } catch (error) {
      console.error('❌ Error creating recurring task:', error);
      throw error;
    }
  }

  private async scheduleRecurringTask(task: Task) {
    try {
      const cronExpression = this.generateCronExpression(task.recurrenceType, task.recurrenceConfig);
      
      if (!cronExpression) {
        console.warn(`⚠️ Could not generate cron expression for task ${task.id}`);
        return;
      }

      // Remove existing job if it exists
      const existingJobKey = `task-${task.id}`;
      if (this.scheduledJobs.has(existingJobKey)) {
        this.scheduledJobs.get(existingJobKey)?.destroy();
        this.scheduledJobs.delete(existingJobKey);
      }

      // Create new scheduled job
      const job = cron.schedule(cronExpression, async () => {
        await this.createTaskInstance(task);
      }, {
        timezone: 'UTC'
      });

      this.scheduledJobs.set(existingJobKey, job);
      
      console.log(`📅 Scheduled recurring task: ${task.title} with cron: ${cronExpression}`);
    } catch (error) {
      console.error(`❌ Error scheduling recurring task ${task.id}:`, error);
    }
  }

  private generateCronExpression(recurrenceType: TaskRecurrenceType, config: any): string | null {
    const interval = config?.interval || 1;
    
    switch (recurrenceType) {
      case TaskRecurrenceType.DAILY:
        // Run at 9 AM every N days
        return `0 9 */${interval} * *`;
        
      case TaskRecurrenceType.WEEKLY:
        // Run at 9 AM on specified days of week (default Monday)
        const daysOfWeek = config?.daysOfWeek || [1]; // Monday = 1
        return `0 9 * * ${daysOfWeek.join(',')}`;
        
      case TaskRecurrenceType.MONTHLY:
        // Run at 9 AM on specified day of month (default 1st)
        const dayOfMonth = config?.dayOfMonth || 1;
        return `0 9 ${dayOfMonth} */${interval} *`;
        
      case TaskRecurrenceType.QUARTERLY:
        // Run at 9 AM on 1st day of quarter months (Jan, Apr, Jul, Oct)
        return `0 9 1 1,4,7,10 *`;
        
      case TaskRecurrenceType.YEARLY:
        // Run at 9 AM on January 1st every N years
        return `0 9 1 1 *`;
        
      default:
        return null;
    }
  }

  private async createTaskInstance(originalTask: Task) {
    try {
      // Check if we should stop creating instances
      if (await this.shouldStopRecurrence(originalTask)) {
        console.log(`🛑 Stopping recurrence for task: ${originalTask.title}`);
        await this.stopRecurringTask(originalTask.id);
        return;
      }

      // Check if an instance for today already exists
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const existingInstance = await this.taskRepository.findOne({
        where: {
          originalTaskId: originalTask.id,
          createdAt: {
            $gte: today,
            $lt: tomorrow
          } as any
        }
      });

      if (existingInstance) {
        console.log(`⏭️ Task instance already exists for today: ${originalTask.title}`);
        return;
      }

      // Create new task instance
      const newTaskData = {
        title: this.generateInstanceTitle(originalTask.title),
        description: originalTask.description,
        projectId: originalTask.projectId,
        assignedToId: originalTask.assignedToId,
        priority: originalTask.priority,
        type: originalTask.type,
        estimatedHours: originalTask.estimatedHours,
        tags: originalTask.tags,
        checklist: originalTask.checklist,
        requiresApproval: originalTask.requiresApproval,
        approvalWorkflow: originalTask.approvalWorkflow,
        createdById: originalTask.createdById,
        originalTaskId: originalTask.id,
        recurrenceType: TaskRecurrenceType.NONE, // Instances are not recurring
        dueDate: this.calculateDueDate(originalTask),
        status: 'todo',
        progress: 0,
        timeSpentMinutes: 0,
        isBlocked: false,
        isActive: true
      };

      const newTask = this.taskRepository.create(newTaskData);
      const savedTask = await this.taskRepository.save(newTask);

      console.log(`✅ Created recurring task instance: ${savedTask.title} (ID: ${savedTask.id})`);

      // Update occurrence count if tracking
      if (originalTask.recurrenceConfig?.maxOccurrences) {
        const currentCount = await this.getOccurrenceCount(originalTask.id);
        if (currentCount >= originalTask.recurrenceConfig.maxOccurrences) {
          await this.stopRecurringTask(originalTask.id);
        }
      }

      return savedTask;
    } catch (error) {
      console.error(`❌ Error creating task instance for ${originalTask.id}:`, error);
    }
  }

  private generateInstanceTitle(originalTitle: string): string {
    const today = new Date();
    const dateStr = today.toISOString().split('T')[0];
    return `${originalTitle} - ${dateStr}`;
  }

  private calculateDueDate(originalTask: Task): Date | null {
    if (!originalTask.dueDate) return null;

    const today = new Date();
    const originalDue = new Date(originalTask.dueDate);
    
    // Calculate days from creation to due date
    const originalCreated = new Date(originalTask.createdAt);
    const daysDiff = Math.ceil((originalDue.getTime() - originalCreated.getTime()) / (1000 * 60 * 60 * 24));
    
    // Apply same difference to today
    const newDueDate = new Date(today);
    newDueDate.setDate(newDueDate.getDate() + daysDiff);
    
    return newDueDate;
  }

  private async shouldStopRecurrence(task: Task): Promise<boolean> {
    // Check if end date is reached
    if (task.recurrenceConfig?.endDate) {
      const endDate = new Date(task.recurrenceConfig.endDate);
      if (new Date() > endDate) {
        return true;
      }
    }

    // Check if max occurrences reached
    if (task.recurrenceConfig?.maxOccurrences) {
      const count = await this.getOccurrenceCount(task.id);
      if (count >= task.recurrenceConfig.maxOccurrences) {
        return true;
      }
    }

    // Check if original task is inactive
    const currentTask = await this.taskRepository.findOne({
      where: { id: task.id }
    });

    return !currentTask || !currentTask.isActive;
  }

  private async getOccurrenceCount(originalTaskId: number): Promise<number> {
    return await this.taskRepository.count({
      where: { originalTaskId }
    });
  }

  async stopRecurringTask(taskId: number) {
    try {
      const jobKey = `task-${taskId}`;
      
      if (this.scheduledJobs.has(jobKey)) {
        this.scheduledJobs.get(jobKey)?.destroy();
        this.scheduledJobs.delete(jobKey);
        console.log(`🛑 Stopped recurring task schedule: ${taskId}`);
      }

      // Optionally mark the original task as inactive
      await this.taskRepository.update(taskId, { isActive: false });
    } catch (error) {
      console.error(`❌ Error stopping recurring task ${taskId}:`, error);
    }
  }

  async updateRecurringTask(taskId: number, updates: Partial<Task>) {
    try {
      await this.taskRepository.update(taskId, updates);
      
      // If recurrence config changed, reschedule
      if (updates.recurrenceType || updates.recurrenceConfig) {
        const updatedTask = await this.taskRepository.findOne({
          where: { id: taskId },
          relations: ['project', 'assignedTo', 'createdBy']
        });
        
        if (updatedTask) {
          await this.scheduleRecurringTask(updatedTask);
        }
      }
    } catch (error) {
      console.error(`❌ Error updating recurring task ${taskId}:`, error);
      throw error;
    }
  }

  private async processRecurringTasks() {
    console.log('🔄 Processing recurring tasks...');
    
    try {
      // This method can be used for additional processing
      // like cleanup, notifications, etc.
      
      // Clean up old completed instances (older than 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      await this.taskRepository.update(
        {
          originalTaskId: { $ne: null } as any,
          status: 'done',
          updatedAt: { $lt: thirtyDaysAgo } as any
        },
        { isActive: false }
      );
      
      console.log('✅ Recurring tasks processing completed');
    } catch (error) {
      console.error('❌ Error processing recurring tasks:', error);
    }
  }

  async getRecurringTaskInstances(originalTaskId: number, limit: number = 10) {
    return await this.taskRepository.find({
      where: { originalTaskId },
      order: { createdAt: 'DESC' },
      take: limit,
      relations: ['assignedTo', 'project']
    });
  }

  async getRecurringTaskStats(originalTaskId: number) {
    const instances = await this.taskRepository.find({
      where: { originalTaskId }
    });

    const total = instances.length;
    const completed = instances.filter((task: any) => task.status === 'done').length;
    const pending = total - completed;
    const totalTimeSpent = instances.reduce((sum: number, task: any) => sum + (task.timeSpentMinutes || 0), 0);

    return {
      total,
      completed,
      pending,
      completionRate: total > 0 ? Math.round((completed / total) * 100) : 0,
      totalTimeSpent,
      averageTimePerInstance: total > 0 ? Math.round(totalTimeSpent / total) : 0
    };
  }

  // Cleanup method to be called when shutting down
  destroy() {
    console.log('🛑 Shutting down Recurring Task Service...');
    
    for (const [key, job] of this.scheduledJobs) {
      job.destroy();
      console.log(`🛑 Destroyed job: ${key}`);
    }
    
    this.scheduledJobs.clear();
    console.log('✅ Recurring Task Service shutdown complete');
  }
}
