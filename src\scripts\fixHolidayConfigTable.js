const mysql = require('mysql2/promise');

async function fixHolidayConfigTable() {
  let connection;
  
  try {
    console.log('✅ Connecting to database...');
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: 'root',
      database: 'ims_db'
    });

    console.log('🔧 Fixing holiday_configurations table structure...');

    // Check existing columns first
    const [existingColumns] = await connection.execute(
      'DESCRIBE holiday_configurations'
    );
    
    const columnNames = existingColumns.map(col => col.Field);
    console.log('Existing columns:', columnNames);

    // Add missing columns one by one
    const columnsToAdd = [
      { name: 'name', sql: 'ALTER TABLE holiday_configurations ADD COLUMN name VARCHAR(100) DEFAULT "Holiday Configuration"' },
      { name: 'isActive', sql: 'ALTER TABLE holiday_configurations ADD COLUMN isActive BOOLEAN DEFAULT TRUE' },
      { name: 'createdBy', sql: 'ALTER TABLE holiday_configurations ADD COLUMN createdBy VARCHAR(100) DEFAULT NULL' },
      { name: 'updatedBy', sql: 'ALTER TABLE holiday_configurations ADD COLUMN updatedBy VARCHAR(100) DEFAULT NULL' },
      { name: 'notes', sql: 'ALTER TABLE holiday_configurations ADD COLUMN notes TEXT DEFAULT NULL' }
    ];

    for (const col of columnsToAdd) {
      if (!columnNames.includes(col.name)) {
        try {
          await connection.execute(col.sql);
          console.log(`✅ Added column: ${col.name}`);
        } catch (error) {
          console.log(`⚠️ Failed to add ${col.name}: ${error.message}`);
        }
      } else {
        console.log(`✅ Column ${col.name} already exists`);
      }
    }

    // Modify existing columns to match entity
    const modifyQueries = [
      'ALTER TABLE holiday_configurations MODIFY COLUMN organizationId VARCHAR(100) NOT NULL DEFAULT "default"',
      'ALTER TABLE holiday_configurations MODIFY COLUMN timezone VARCHAR(50) DEFAULT "UTC"'
    ];

    for (const query of modifyQueries) {
      try {
        await connection.execute(query);
        console.log(`✅ Modified column structure`);
      } catch (error) {
        console.log(`⚠️ Modify failed: ${error.message}`);
      }
    }

    // Fix weekendDays column to have proper data
    console.log('🔧 Fixing weekendDays data...');
    const [configs] = await connection.execute(
      'SELECT id, weekendDays FROM holiday_configurations'
    );

    for (const config of configs) {
      let weekendDaysValue = config.weekendDays;
      
      // If it's not a proper JSON array, fix it
      if (typeof weekendDaysValue === 'string' && !weekendDaysValue.startsWith('[')) {
        weekendDaysValue = '[0, 6]'; // Default to Sunday and Saturday
        
        await connection.execute(
          'UPDATE holiday_configurations SET weekendDays = ? WHERE id = ?',
          [weekendDaysValue, config.id]
        );
        console.log(`✅ Fixed weekendDays for config ${config.id}`);
      }
    }

    // Check for existing records and insert default if needed
    const [existingRows] = await connection.execute(
      'SELECT COUNT(*) as count FROM holiday_configurations WHERE organizationId = "default"'
    );

    if (existingRows[0].count === 0) {
      console.log('📝 Inserting default holiday configuration...');
      const insertDefaultSQL = `
        INSERT INTO holiday_configurations (organizationId, name, weekendDays, timezone, workingHours, holidaySettings, isActive)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `;

      await connection.execute(insertDefaultSQL, [
        'default',
        'Default Holiday Configuration',
        '[0, 6]', // JSON string for weekend days
        'UTC',
        JSON.stringify({ start: '09:00', end: '17:00' }),
        JSON.stringify({ 
          allowOptionalHolidays: true,
          maxOptionalHolidaysPerYear: 5,
          requireApprovalForOptional: false
        }),
        true
      ]);

      console.log('✅ Default configuration inserted');
    } else {
      console.log(`✅ Found ${existingRows[0].count} existing default configuration(s)`);
    }

    // Verify final table structure
    console.log('🔍 Verifying updated table structure...');
    const [columns] = await connection.execute(
      'DESCRIBE holiday_configurations'
    );

    console.log('📋 Updated table columns:');
    columns.forEach(col => {
      console.log(`   • ${col.Field} (${col.Type}) - ${col.Null === 'YES' ? 'nullable' : 'not null'}`);
    });

    console.log('✅ Holiday configuration table fixed successfully!');

  } catch (error) {
    console.error('❌ Error fixing holiday configuration table:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔐 Database connection closed');
    }
  }
}

// Run the script
fixHolidayConfigTable(); 