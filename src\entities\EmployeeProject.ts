import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { Employee } from './Employee';

@Entity('employee_projects')
export class EmployeeProject {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 100,  nullable: false })
  projectName: string;

  @Column({ type: 'varchar', length: 255,  nullable: false })
  role: string;

  @Column({ type: 'date',  nullable: true })
  startDate: string;

  @Column({ type: 'date',  nullable: true })
  endDate: string;

  @Column({ type: 'boolean',  nullable: false, default: false })
  currentProject: boolean;

  @Column({ type: 'varchar', length: 255,  nullable: true })
  technologies: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 255,  nullable: true })
  achievements: string;

  @Column({ type: 'varchar', length: 255,  nullable: true })
  teamSize: string;

  @Column({ type: 'varchar', length: 100,  nullable: true })
  clientName: string;

  // Relation to Employee
  @ManyToOne(() => Employee, employee => employee.projects, { onDelete: 'CASCADE' })
  @JoinColumn()
  employee: Employee;

  // System columns
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 