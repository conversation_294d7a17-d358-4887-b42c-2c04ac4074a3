import React, { useState, useMemo } from 'react';
import { 
  FileText, 
  Plus, 
  Clock, 
  Calendar, 
  User, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Edit3, 
  Eye, 
  Filter,
  Search,
  Download,
  RefreshCw,
  TrendingUp,
  BarChart3,
  MoreHorizontal,
  ChevronDown,
  ChevronUp,
  ExternalLink,
  X,
  MessageSquare
} from 'lucide-react';
import { AttendanceRegularizationRequest } from '../../../types/attendance';

interface RegularizationManagerProps {
  mode?: 'form' | 'table' | 'both';
  showFormButton?: boolean;
  requests: AttendanceRegularizationRequest[];
  employeeId?: number; // Made optional for admin mode
  employeeName?: string; // Made optional for admin mode
  onSubmit: (request: Omit<AttendanceRegularizationRequest, 'id'>) => void;
  onCancel?: () => void;
  onApprove?: (requestId: number, comments?: string) => void;
  onReject?: (requestId: number, comments: string) => void; // Made comments required for reject
  onEdit?: (request: AttendanceRegularizationRequest) => void;
  isSubmitting?: boolean;
  compact?: boolean;
  isAdmin?: boolean;
  showFilters?: boolean;
  showStats?: boolean;
  employees?: Array<{ id: number; name: string; employeeId?: string; department?: string }>; // Employee list for admin mode
}

const RegularizationManager: React.FC<RegularizationManagerProps> = ({
  mode = 'both',
  showFormButton = false,
  requests,
  employeeId,
  employeeName,
  onSubmit,
  onCancel,
  onApprove,
  onReject,
  onEdit,
  isSubmitting = false,
  compact = false,
  isAdmin = false,
  showFilters = true,
  showStats = true,
  employees = []
}) => {
  const [showForm, setShowForm] = useState(mode === 'form');
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    type: '',  // Removed predefined 'check-in' value
    requestedTime: '',
    reason: '',
    selectedEmployeeId: '',
    selectedEmployeeName: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'date' | 'status' | 'type'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [expandedRequest, setExpandedRequest] = useState<number | null>(null);
  const [employeeSearchTerm, setEmployeeSearchTerm] = useState('');
  const [isEmployeeDropdownOpen, setIsEmployeeDropdownOpen] = useState(false);
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [actionRequestId, setActionRequestId] = useState<number | null>(null);

  // Form validation
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    if (!formData.date) newErrors.date = 'Date is required';
    if (!formData.type) newErrors.type = 'Type is required';
    if (!formData.reason.trim()) newErrors.reason = 'Reason is required';
    if (isAdmin && !formData.selectedEmployeeId) newErrors.employee = 'Employee selection is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Update the form reset logic
  const resetForm = () => {
    setFormData({
      date: new Date().toISOString().split('T')[0],
      type: '',  // Removed predefined 'check-in' value
      requestedTime: '',
      reason: '',
      selectedEmployeeId: '',
      selectedEmployeeName: ''
    });
    setErrors({});
    setEmployeeSearchTerm('');
    setIsEmployeeDropdownOpen(false);
  };

  // Update the cancel handler
  const handleCancel = () => {
    resetForm();
    setShowForm(false);
    onCancel && onCancel();
  };

  // Form submit
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    // Find the selected employee's full details
    const selectedEmployee = employees.find(emp => emp.id.toString() === formData.selectedEmployeeId);
    
    const request: Omit<AttendanceRegularizationRequest, 'id'> = {
      employeeId: isAdmin ? parseInt(formData.selectedEmployeeId) : (employeeId || 0),
      employeeName: isAdmin ? (selectedEmployee ? 
        `${selectedEmployee.name} (${selectedEmployee.employeeId}) - ${selectedEmployee.department || ''}` 
        : formData.selectedEmployeeName) 
        : (employeeName || ''),
      date: formData.date,
      type: formData.type,
      requestedTime: formData.requestedTime || '',
      reason: formData.reason.trim(),
      status: 'pending',
      createdAt: new Date().toISOString()
    };
    onSubmit(request);
    resetForm();
    setShowForm(false);
  };

  // Filtered and sorted requests
  const filteredRequests = useMemo(() => {
    let filtered = requests.filter(request => {
      const matchesSearch = request.reason.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           request.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           request.employeeName.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || request.status === statusFilter;
      const matchesType = typeFilter === 'all' || request.type === typeFilter;
      return matchesSearch && matchesStatus && matchesType;
    });

    filtered.sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case 'date':
          comparison = new Date(a.date).getTime() - new Date(b.date).getTime();
          break;
        case 'status':
          comparison = a.status.localeCompare(b.status);
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
      }
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [requests, searchTerm, statusFilter, typeFilter, sortBy, sortOrder]);

  // Statistics
  const stats = useMemo(() => {
    const total = requests.length;
    const pending = requests.filter(r => r.status === 'pending').length;
    const approved = requests.filter(r => r.status === 'approved').length;
    const rejected = requests.filter(r => r.status === 'rejected').length;
    const approvalRate = total > 0 ? ((approved / total) * 100).toFixed(1) : '0';

    return { total, pending, approved, rejected, approvalRate };
  }, [requests]);

  // Helper functions
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).replace(',', '');
  };

  const formatTimeOnly = (timeString: string) => {
    if (!timeString || typeof timeString !== 'string') return '—';
    // If it's already in HH:mm format, convert to 12-hour format
    if (/^\d{2}:\d{2}$/.test(timeString)) {
      const [hours, minutes] = timeString.split(':');
      const date = new Date();
      date.setHours(parseInt(hours), parseInt(minutes), 0, 0);
      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    }
    // Otherwise, try to format it as a date
    const date = new Date(timeString);
    if (isNaN(date.getTime())) return '—';
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  };

  const getStatusBadge = (status: string) => {
    const base = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium gap-1';
    switch (status.toLowerCase()) {
      case 'approved': 
        return <span className={`${base} bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-400 border border-green-200 dark:border-green-800`}>
          <CheckCircle className="h-3 w-3" />Approved
        </span>;
      case 'rejected': 
        return <span className={`${base} bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400 border border-red-200 dark:border-red-800`}>
          <XCircle className="h-3 w-3" />Rejected
        </span>;
      default: 
        return <span className={`${base} bg-yellow-50 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400 border border-yellow-200 dark:border-yellow-800`}>
          <Clock className="h-3 w-3" />Pending
        </span>;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'check-in': return <Clock className="h-4 w-4 text-blue-500" />;
      case 'check-out': return <Clock className="h-4 w-4 text-purple-500" />;
      case 'absent': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'work-from-home': return <User className="h-4 w-4 text-green-500" />;
      case 'half-day': return <BarChart3 className="h-4 w-4 text-orange-500" />;
      case 'overtime': return <TrendingUp className="h-4 w-4 text-indigo-500" />;
      default: return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'check-in': return 'text-blue-600 dark:text-blue-400';
      case 'check-out': return 'text-purple-600 dark:text-purple-400';
      case 'absent': return 'text-red-600 dark:text-red-400';
      case 'work-from-home': return 'text-green-600 dark:text-green-400';
      case 'half-day': return 'text-orange-600 dark:text-orange-400';
      case 'overtime': return 'text-indigo-600 dark:text-indigo-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const filteredEmployees = useMemo(() => {
    return employees.filter(emp => {
      const searchStr = `${emp.name} ${emp.employeeId || ''} ${emp.department || ''}`.toLowerCase();
      return searchStr.includes(employeeSearchTerm.toLowerCase());
    });
  }, [employees, employeeSearchTerm]);

  // Statistics Cards
  const StatisticsCards = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/30 rounded-xl p-4 border border-blue-200 dark:border-blue-700/30">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-blue-600 dark:text-blue-400">Total Requests</p>
            <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">{stats.total}</p>
          </div>
          <div className="p-2 bg-blue-500/10 rounded-lg">
            <FileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-800/30 rounded-xl p-4 border border-yellow-200 dark:border-yellow-700/30">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-yellow-600 dark:text-yellow-400">Pending</p>
            <p className="text-2xl font-bold text-yellow-900 dark:text-yellow-100">{stats.pending}</p>
          </div>
          <div className="p-2 bg-yellow-500/10 rounded-lg">
            <AlertCircle className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/30 rounded-xl p-4 border border-green-200 dark:border-green-700/30">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-green-600 dark:text-green-400">Approved</p>
            <p className="text-2xl font-bold text-green-900 dark:text-green-100">{stats.approved}</p>
          </div>
          <div className="p-2 bg-green-500/10 rounded-lg">
            <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/30 rounded-xl p-4 border border-purple-200 dark:border-purple-700/30">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-purple-600 dark:text-purple-400">Approval Rate</p>
            <p className="text-2xl font-bold text-purple-900 dark:text-purple-100">{stats.approvalRate}%</p>
          </div>
          <div className="p-2 bg-purple-500/10 rounded-lg">
            <TrendingUp className="h-6 w-6 text-purple-600 dark:text-purple-400" />
          </div>
        </div>
      </div>
    </div>
  );

  // Enhanced Form
  const EnhancedForm = () => (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg w-full relative">
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-700 dark:to-purple-700 px-6 py-4">
        <h3 className="text-lg font-semibold text-white flex items-center">
          <FileText className="h-5 w-5 mr-2" />
          {isAdmin ? 'HR Admin - New Regularization Request' : 'New Regularization Request'}
        </h3>
        <p className="text-blue-100 text-sm mt-1">
          {isAdmin 
            ? 'Submit a regularization request on behalf of an employee' 
            : 'Submit a request to correct your attendance record'
          }
        </p>
      </div>
      
      <form onSubmit={handleSubmit} className="p-6">
        <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
          <div className="sm:col-span-3">
            <label htmlFor="reg-date" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Calendar className="h-4 w-4 inline mr-1" />
              Date *
            </label>
            <input 
              type="date" 
              id="reg-date" 
              value={formData.date} 
              onChange={e => setFormData(f => ({ ...f, date: e.target.value }))} 
              className={`block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm transition-all duration-200 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 focus:outline-none ${errors.date ? 'border-red-500 focus:ring-red-500/20 focus:border-red-500' : ''}`} 
              required 
            />
            {errors.date && <p className="mt-1 text-xs text-red-600 dark:text-red-400">{errors.date}</p>}
          </div>
          
          {isAdmin && (
            <div className="sm:col-span-6 relative">
              <label htmlFor="reg-employee" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <User className="h-4 w-4 inline mr-1" />
                Select Employee *
              </label>
              <div className="relative">
                <div
                  role="button"
                  onClick={() => setIsEmployeeDropdownOpen(!isEmployeeDropdownOpen)}
                  className={`relative w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg pl-3 pr-10 py-2 text-left cursor-default focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 sm:text-sm ${
                    errors.employee ? 'border-red-500 focus:ring-red-500/20 focus:border-red-500' : ''
                  }`}
                >
                  <span className="block truncate text-gray-500 dark:text-gray-400">
                    {formData.selectedEmployeeId 
                      ? employees.find(e => e.id.toString() === formData.selectedEmployeeId)?.name || 'Select an employee...'
                      : 'Select an employee...'}
                  </span>
                  <span className="absolute inset-y-0 right-0 flex items-center pr-2">
                    {formData.selectedEmployeeId && (
                      <span
                        role="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          setFormData(prev => ({
                            ...prev,
                            selectedEmployeeId: '',
                            selectedEmployeeName: ''
                          }));
                        }}
                        className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 mr-2"
                      >
                        <XCircle className="h-4 w-4" />
                      </span>
                    )}
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  </span>
                </div>

                {isEmployeeDropdownOpen && (
                  <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg max-h-60 rounded-lg text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
                    <div className="sticky top-0 z-10 bg-white dark:bg-gray-800 px-3 py-2">
                      <div className="relative flex items-center">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                        <input
                          type="text"
                          className="w-full pl-9 pr-3 py-1.5 bg-transparent text-sm text-gray-900 dark:text-gray-100 placeholder-gray-500 focus:outline-none"
                          placeholder="Search employees..."
                          value={employeeSearchTerm}
                          onChange={(e) => setEmployeeSearchTerm(e.target.value)}
                          onClick={(e) => e.stopPropagation()}
                        />
                        {employeeSearchTerm && (
                          <span
                            role="button"
                            onClick={(e) => {
                              e.stopPropagation();
                              setEmployeeSearchTerm('');
                            }}
                            className="absolute right-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                          >
                            <XCircle className="h-4 w-4" />
                          </span>
                        )}
                      </div>
                    </div>

                    <div>
                      {filteredEmployees.length === 0 ? (
                        <div className="relative cursor-default select-none py-2 px-3 text-gray-500 dark:text-gray-400">
                          No employees found
                        </div>
                      ) : (
                        <>
                          <span
                            role="button"
                            onClick={() => {
                              setFormData(prev => ({
                                ...prev,
                                selectedEmployeeId: '',
                                selectedEmployeeName: ''
                              }));
                              setIsEmployeeDropdownOpen(false);
                              setEmployeeSearchTerm('');
                            }}
                            className="w-full text-left px-3 py-2 text-sm text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700/50 border-b border-gray-200 dark:border-gray-700"
                          >
                            Clear selection
                          </span>
                          {filteredEmployees.map((emp) => (
                            <div
                              key={emp.id}
                              className={`relative cursor-pointer select-none py-2 px-3 hover:bg-gray-100 dark:hover:bg-gray-700/50 ${
                                formData.selectedEmployeeId === emp.id.toString()
                                  ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                                  : 'text-gray-900 dark:text-gray-100'
                              }`}
                              onClick={() => {
                                setFormData(prev => ({
                                  ...prev,
                                  selectedEmployeeId: emp.id.toString(),
                                  selectedEmployeeName: `${emp.name} (${emp.employeeId}) - ${emp.department || ''}`
                                }));
                                setIsEmployeeDropdownOpen(false);
                                setEmployeeSearchTerm('');
                              }}
                            >
                              <div className="flex items-center justify-between">
                                <span className="font-medium">{emp.name}</span>
                                <span className="text-gray-500 dark:text-gray-400 text-sm">
                                  {emp.employeeId}
                                </span>
                              </div>
                              {emp.department && (
                                <span className="text-gray-500 dark:text-gray-400 text-sm block mt-0.5">
                                  {emp.department}
                                </span>
                              )}
                            </div>
                          ))}
                        </>
                      )}
                    </div>
                  </div>
                )}
              </div>
              {errors.employee && <p className="mt-1 text-xs text-red-600 dark:text-red-400">{errors.employee}</p>}
            </div>
          )}
          
          <div className="sm:col-span-3">
            <label htmlFor="reg-type" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <FileText className="h-4 w-4 inline mr-1" />
              Request Type *
            </label>
            <select 
              id="reg-type" 
              value={formData.type} 
              onChange={e => setFormData(f => ({ ...f, type: e.target.value }))} 
              className={`block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm transition-all duration-200 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 focus:outline-none appearance-none bg-no-repeat ${errors.type ? 'border-red-500 focus:ring-red-500/20 focus:border-red-500' : ''}`}
              style={{
                backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e")`,
                backgroundPosition: 'right 0.75rem center',
                backgroundSize: '1.5em 1.5em',
                paddingRight: '2.5rem'
              }}
              required
            >
              <option value="">Select type...</option>
              <option value="check-in">Check-in Correction</option>
              <option value="check-out">Check-out Correction</option>
              <option value="absent">Absent to Present</option>
              <option value="work-from-home">Work From Home</option>
              <option value="half-day">Half Day</option>
              <option value="overtime">Overtime Request</option>
            </select>
            {errors.type && <p className="mt-1 text-xs text-red-600 dark:text-red-400">{errors.type}</p>}
          </div>
          
          <div className="sm:col-span-3">
            <label htmlFor="reg-time" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Clock className="h-4 w-4 inline mr-1" />
              Requested Time
            </label>
            <input 
              type="time" 
              id="reg-time" 
              value={formData.requestedTime} 
              onChange={e => setFormData(f => ({ ...f, requestedTime: e.target.value }))} 
              className={`block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm transition-all duration-200 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 focus:outline-none ${errors.requestedTime ? 'border-red-500 focus:ring-red-500/20 focus:border-red-500' : ''}`} 
            />
            {errors.requestedTime && <p className="mt-1 text-xs text-red-600 dark:text-red-400">{errors.requestedTime}</p>}
          </div>
          
          <div className="sm:col-span-6">
            <label htmlFor="reg-reason" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <FileText className="h-4 w-4 inline mr-1" />
              Reason for Regularization *
            </label>
            <textarea 
              id="reg-reason" 
              rows={4} 
              value={formData.reason} 
              onChange={e => setFormData(f => ({ ...f, reason: e.target.value }))} 
              className={`block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm transition-all duration-200 focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 focus:outline-none resize-none ${errors.reason ? 'border-red-500 focus:ring-red-500/20 focus:border-red-500' : ''}`} 
              placeholder="Please provide a reason for this regularization request..." 
              required 
            />
            {errors.reason && <p className="mt-1 text-xs text-red-600 dark:text-red-400">{errors.reason}</p>}
            <div className="flex justify-between items-center mt-1">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {formData.reason.length}/500 characters maximum
              </p>
            </div>
          </div>
        </div>
        
        <div className="mt-6 flex justify-end gap-3">
          {showFormButton && (
            <button 
              type="button" 
              onClick={handleCancel} 
              className="px-4 py-2 rounded-lg bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 text-sm font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200"
            >
              Cancel
            </button>
          )}
          <button 
            type="submit" 
            disabled={isSubmitting} 
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:focus:ring-blue-400 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Submitting...
              </>
            ) : (
              <>
                <FileText className="h-4 w-4 mr-2" />
                {isAdmin ? 'Submit Request for Employee' : 'Submit Request'}
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );

  // Enhanced Table
  const EnhancedTable = () => (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
      {/* Table Header */}
      <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Regularization Requests
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {filteredRequests.length} of {requests.length} requests
            </p>
          </div>
          
          {showFilters && (
            <div className="flex flex-wrap items-center gap-2">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search requests..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 focus:outline-none"
                />
              </div>
              
              {/* Status Filter */}
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 focus:outline-none"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="rejected">Rejected</option>
              </select>
              
              {/* Type Filter */}
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 focus:outline-none"
              >
                <option value="all">All Types</option>
                <option value="check-in">Check-in</option>
                <option value="check-out">Check-out</option>
                <option value="absent">Absent</option>
                <option value="work-from-home">Work From Home</option>
                <option value="half-day">Half Day</option>
                <option value="overtime">Overtime</option>
              </select>

              {/* Add Request Button */}
              {(mode === 'both' || (isAdmin && mode === 'table')) && showFormButton && !showForm && (
                <button
                  onClick={() => setShowForm(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
                >
                  <Plus className="h-4 w-4 mr-2" /> 
                  New Request
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Table Content */}
      <div className="overflow-x-visible">
        <table className="min-w-full w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-100 dark:bg-gray-900">
            <tr>
              {isAdmin && (
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider align-middle border-r border-gray-200 dark:border-gray-800">
                  Employee
                </th>
              )}
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider align-middle border-r border-gray-200 dark:border-gray-800">
                Type
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider align-middle border-r border-gray-200 dark:border-gray-800">
                Date
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider align-middle border-r border-gray-200 dark:border-gray-800">
                Time
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider align-middle border-r border-gray-200 dark:border-gray-800">
                Reason
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider align-middle border-r border-gray-200 dark:border-gray-800">
                Submitted
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider align-middle border-r border-gray-200 dark:border-gray-800">
                Status
              </th>
              {isAdmin && (
                <th className="px-4 py-3 text-right text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider align-middle">
                  Actions
                </th>
              )}
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {filteredRequests.length === 0 ? (
              <tr>
                <td colSpan={isAdmin ? 8 : 6} className="px-6 py-12 text-center">
                  <div className="flex flex-col items-center">
                    <FileText className="h-12 w-12 text-gray-400 mb-4" />
                    <p className="text-gray-500 dark:text-gray-400 text-sm">
                      {searchTerm || statusFilter !== 'all' || typeFilter !== 'all' 
                        ? 'No requests match your filters' 
                        : 'No regularization requests found'}
                    </p>
                    {searchTerm || statusFilter !== 'all' || typeFilter !== 'all' && (
                      <button 
                        onClick={() => {
                          setSearchTerm('');
                          setStatusFilter('all');
                          setTypeFilter('all');
                        }}
                        className="mt-2 text-blue-600 dark:text-blue-400 text-sm hover:underline"
                      >
                        Clear filters
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ) : (
              filteredRequests.map((request, index) => (
                <tr 
                  key={request.id || index} 
                  className={`group transition-colors duration-150 cursor-pointer align-middle ${
                    index % 2 === 0 ? 'bg-white dark:bg-gray-800' : 'bg-gray-50 dark:bg-gray-900/50'
                  } hover:bg-blue-50 dark:hover:bg-blue-900/30`}
                  onClick={() => setExpandedRequest(expandedRequest === request.id ? null : (request.id ?? null))}
                >
                  {isAdmin && (
                    <td className="px-4 py-3 whitespace-nowrap text-sm align-middle border-r border-gray-200 dark:border-gray-800">
                      <div className="flex items-center space-x-1.5">
                        <User className="h-4 w-4 text-gray-400 flex-shrink-0" />
                        <div className="flex flex-col">
                          <span className="font-medium text-gray-900 dark:text-gray-100 max-w-[200px] truncate" title={request.employeeName}>
                            {request.employeeName.split(' - ')[0].split(' (')[0]}
                          </span>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {(() => {
                              const idMatch = request.employeeName.match(/\((.*?)\)/);
                              const id = idMatch ? idMatch[1] : request.employeeId;
                              return id || 'ID Not Assigned';
                            })()}
                          </span>
                          <span className="text-sm text-gray-400 dark:text-gray-500 truncate max-w-[180px]">
                            {(() => {
                              const parts = request.employeeName.split(' - ');
                              return parts.length > 1 ? parts[1] : '';
                            })()}
                          </span>
                        </div>
                      </div>
                    </td>
                  )}
                  <td className="px-4 py-3 whitespace-nowrap text-sm align-middle border-r border-gray-200 dark:border-gray-800">
                    <div className="flex items-center space-x-1.5">
                      {getTypeIcon(request.type)}
                      <span className={`font-medium ${getTypeColor(request.type)}`}> 
                        {request.type.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 align-middle border-r border-gray-200 dark:border-gray-800">
                    <div className="flex items-center space-x-1.5">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      {formatDate(request.date)}
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 align-middle border-r border-gray-200 dark:border-gray-800">
                    <div className="flex items-center space-x-1.5">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span>{formatTimeOnly(request.requestedTime) || '—'}</span>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100 align-middle border-r border-gray-200 dark:border-gray-800">
                    <div className="max-w-[200px] truncate" title={request.reason}>
                      {request.reason}
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 align-middle border-r border-gray-200 dark:border-gray-800">
                    <div className="flex flex-col">
                      <span className="flex items-center space-x-1.5">
                        <Clock className="h-4 w-4 text-gray-400" />
                        {formatTime(request.createdAt)}
                      </span>
                      <span className="flex items-center space-x-1.5">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        {formatDate(request.createdAt)}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap align-middle border-r border-gray-200 dark:border-gray-800">
                    {getStatusBadge(request.status)}
                  </td>
                  {isAdmin && (
                    <td className="px-4 py-3 whitespace-nowrap align-middle text-right">
                      <div className="flex items-center justify-end -space-x-1">
                        {request.status === 'pending' && (
                          <>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleApproveClick(request.id!);
                              }}
                              className="relative inline-flex items-center justify-center p-2 rounded-full text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 hover:bg-green-50 dark:hover:bg-green-900/30 transition-colors"
                              title="Approve"
                            >
                              <CheckCircle className="h-4 w-4" />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRejectClick(request.id!);
                              }}
                              className="relative inline-flex items-center justify-center p-2 rounded-full text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/30 transition-colors"
                              title="Reject"
                            >
                              <XCircle className="h-4 w-4" />
                            </button>
                          </>
                        )}
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onEdit && onEdit(request);
                          }}
                          className="relative inline-flex items-center justify-center p-2 rounded-full text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors"
                          title="Edit"
                        >
                          <Edit3 className="h-4 w-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setExpandedRequest(expandedRequest === request.id ? null : (request.id ?? null));
                          }}
                          className="relative inline-flex items-center justify-center p-2 rounded-full text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-900/30 transition-colors"
                          title="View Details"
                        >
                          {expandedRequest === request.id ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                        </button>
                      </div>
                    </td>
                  )}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );

  // Modal for expanded request details
  const RequestDetailsModal = () => {
    if (!expandedRequest) return null;
    const request = requests.find(r => r.id === expandedRequest);
    if (!request) return null;

    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40 backdrop-blur-sm p-4">
        <div className="bg-white dark:bg-gray-800 rounded-2xl w-full max-w-2xl relative shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden">
          {/* Header */}
          <div className="px-6 py-4 bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-700 dark:to-purple-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <FileText className="h-5 w-5 text-white" />
                <h3 className="text-lg font-semibold text-white">Regularization Request Details</h3>
              </div>
              <button
                onClick={() => setExpandedRequest(null)}
                className="text-white/80 hover:text-white transition-colors"
                aria-label="Close"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <p className="mt-1 text-blue-100 text-sm">
              Review complete request information
            </p>
          </div>

          {/* Content */}
          <div className="p-6 space-y-3">
            {/* Primary Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mb-0">
              {/* Employee Info (if admin) */}
              {isAdmin && (
                <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                    <User className="h-4 w-4 text-gray-400" />
                    Employee Information
                  </h4>
                  <div className="space-y-2">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {request.employeeName.split(' - ')[0]}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      ID: {(() => {
                        const idMatch = request.employeeName.match(/\((.*?)\)/);
                        return idMatch ? idMatch[1] : request.employeeId || 'Not Assigned';
                      })()}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {request.employeeName.split(' - ')[1] || 'No Department'}
                    </div>
                  </div>
                </div>
              )}

              {/* Request Type & Status */}
              <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <FileText className="h-4 w-4 text-gray-400" />
                  Request Details
                </h4>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Type</span>
                    <span className="flex items-center gap-1.5">
                      {getTypeIcon(request.type)}
                      <span className={`text-sm font-medium ${getTypeColor(request.type)}`}>
                        {request.type.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                      </span>
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Status</span>
                    {getStatusBadge(request.status)}
                  </div>
                  <div className="flex flex-col mt-2 mb-4">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Reason</span>
                    <div className="text-sm text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 p-2 rounded border border-gray-200 dark:border-gray-700 mt-1">
                      {request.reason}
                    </div>
                  </div>
                </div>
              </div>

              {/* Date & Time */}
              <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-400" />
                  Time Details
                </h4>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Correction Date</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white flex items-center gap-1.5">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      {formatDate(request.date)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Requested Time</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white flex items-center gap-1.5">
                      <Clock className="h-4 w-4 text-gray-400" />
                      {formatTimeOnly(request.requestedTime) || '—'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Submission Info */}
              <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-400" />
                  Submission Details
                </h4>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Submitted Time</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white flex items-center gap-1.5">
                      <Clock className="h-4 w-4 text-gray-400" />
                      {formatTime(request.createdAt)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Submitted Date</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white flex items-center gap-1.5">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      {formatDate(request.createdAt)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Rejection Reason Section - Only for rejected requests */}
            {request.status === 'rejected' && request.approverComments && (
              <div className="bg-red-50 dark:bg-red-900/30 rounded-xl p-4 border border-red-200 dark:border-red-700 mt-4">
                <h4 className="text-sm font-medium text-red-700 dark:text-red-400 mb-3 flex items-center gap-2">
                  <XCircle className="h-4 w-4 text-red-400" />
                  Rejection Reason
                </h4>
                <div className="text-sm text-red-800 dark:text-red-300 bg-white dark:bg-gray-800 p-2 rounded border border-red-200 dark:border-red-700 whitespace-pre-wrap">
                  {request.approverComments}
                </div>
              </div>
            )}

            {/* Actions */}
            {isAdmin && request.status === 'pending' && (
              <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRejectClick(request.id!);
                  }}
                  className="px-4 py-2 rounded-lg bg-red-50 text-red-700 hover:bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50 text-sm font-medium transition-colors flex items-center gap-1.5"
                >
                  <XCircle className="h-4 w-4" />
                  Reject Request
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleApproveClick(request.id!);
                  }}
                  className="px-4 py-2 rounded-lg bg-green-50 text-green-700 hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50 text-sm font-medium transition-colors flex items-center gap-1.5"
                >
                  <CheckCircle className="h-4 w-4" />
                  Approve Request
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Action Confirmation Modals
  const ActionConfirmationModal = ({ 
    isOpen, 
    onClose, 
    type, 
    requestId 
  }: { 
    isOpen: boolean; 
    onClose: () => void; 
    type: 'approve' | 'reject'; 
    requestId: number | null; 
  }) => {
    const [actionReason, setActionReason] = React.useState('');
    const [actionError, setActionError] = React.useState('');

    React.useEffect(() => {
      if (isOpen) {
        setActionReason('');
        setActionError('');
      }
    }, [isOpen, requestId]);

    if (!isOpen || !requestId) return null;
    const request = requests.find(r => r.id === requestId);
    if (!request) return null;

    const handleSubmit = () => {
      if (type === 'reject' && !actionReason.trim()) {
        setActionError('Please provide a reason for rejection');
        return;
      }
      
      if (type === 'approve') {
        onApprove && onApprove(requestId, actionReason.trim() || undefined);
      } else {
        onReject && onReject(requestId, actionReason.trim());
      }
      
      setActionReason('');
      setActionError('');
      onClose();
      setExpandedRequest(null);
    };

    // Extract employee info from the name string
    const parseEmployeeInfo = (fullName: string) => {
      // Example format: "John Doe (GC-1234) - IT Department"
      const nameParts = fullName.split(' - ');
      const nameWithId = nameParts[0] || '';
      const department = nameParts[1] || '';
      
      const nameMatch = nameWithId.match(/(.*?)\s*\((.*?)\)/);
      if (nameMatch) {
        return {
          name: nameMatch[1].trim(),
          id: nameMatch[2].trim(),
          department: department.trim()
        };
      }
      
      // Fallback if no ID in parentheses
      return {
        name: nameWithId.trim(),
        id: request.employeeId?.toString() || 'Not Assigned',
        department: department.trim()
      };
    };

    const employeeInfo = parseEmployeeInfo(request.employeeName);

    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40 backdrop-blur-sm p-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-xl shadow-2xl">
          {/* Header */}
          <div className={`px-5 py-3 bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-700 dark:to-purple-700 rounded-t-lg`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-white">
                {type === 'approve' ? (
                  <CheckCircle className="h-5 w-5" />
                ) : (
                  <FileText className="h-5 w-5" />
                )}
                <div>
                  <h3 className="text-lg font-semibold">
                    {type === 'approve' ? 'Approve Request' : 'Reject Request'}
                  </h3>
                  <p className="text-sm text-blue-100">
                    Review request information
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-white/80 hover:text-white transition-colors"
                aria-label="Close"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>

          <div className="p-5 grid grid-cols-2 gap-3">
            {/* Employee Information Card */}
            <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-3">
              <div className="flex items-center gap-1.5 mb-2">
                <User className="h-4 w-4 text-gray-400" />
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                  Employee Information
                </h4>
              </div>
              <div className="space-y-2">
                <div className="text-sm text-gray-900 dark:text-white font-medium">
                  {employeeInfo.name}
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1.5">
                  <FileText className="h-3.5 w-3.5" />
                  ID: {employeeInfo.id}
                </div>
                {employeeInfo.department && (
                  <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1.5">
                    <User className="h-3.5 w-3.5" />
                    {employeeInfo.department}
                  </div>
                )}
              </div>
            </div>

            {/* Request Details Card */}
            <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-3">
              <div className="flex items-center gap-1.5 mb-2">
                <FileText className="h-4 w-4 text-gray-400" />
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                  Request Details
                </h4>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500 dark:text-gray-400">Type</span>
                  <span className={`text-xs ${getTypeColor(request.type)} font-medium flex items-center gap-1.5`}>
                    {getTypeIcon(request.type)}
                    {request.type.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500 dark:text-gray-400">Status</span>
                  {getStatusBadge(request.status)}
                </div>
                <div className="flex flex-col mt-2 mb-4">
                  <span className="text-sm text-gray-500 dark:text-gray-400">Reason</span>
                  <div className="text-sm text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 p-2 rounded border border-gray-200 dark:border-gray-700 mt-1">
                    {request.reason}
                  </div>
                </div>
              </div>
            </div>

            {/* Time Details Card */}
            <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-3">
              <div className="flex items-center gap-1.5 mb-2">
                <Clock className="h-4 w-4 text-gray-400" />
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                  Time Details
                </h4>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500 dark:text-gray-400">Date</span>
                  <div className="flex items-center gap-1.5">
                    <Calendar className="h-3.5 w-3.5 text-gray-400" />
                    <span className="text-xs text-gray-900 dark:text-white">
                      {formatDate(request.date)}
                    </span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500 dark:text-gray-400">Time</span>
                  <div className="flex items-center gap-1.5">
                    <Clock className="h-3.5 w-3.5 text-gray-400" />
                    <span className="text-xs text-gray-900 dark:text-white">
                      {formatTimeOnly(request.requestedTime) || '—'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Submission Details Card */}
            <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-3">
              <div className="flex items-center gap-1.5 mb-2">
                <Clock className="h-4 w-4 text-gray-400" />
                <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                  Submission Details
                </h4>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500 dark:text-gray-400">Time</span>
                  <div className="flex items-center gap-1.5">
                    <Clock className="h-3.5 w-3.5 text-gray-400" />
                    <span className="text-xs text-gray-900 dark:text-white">
                      {formatTime(request.createdAt)}
                    </span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-gray-500 dark:text-gray-400">Date</span>
                  <div className="flex items-center gap-1.5">
                    <Calendar className="h-3.5 w-3.5 text-gray-400" />
                    <span className="text-xs text-gray-900 dark:text-white">
                      {formatDate(request.createdAt)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Comments/Reason Input - Full Width */}
            <div className="col-span-2">
              <label 
                htmlFor="action-reason" 
                className="block text-sm font-medium text-gray-900 dark:text-white mb-1.5"
              >
                {type === 'approve' ? 'Approval Comments (Optional)' : 'Rejection Reason *'}
              </label>
              <textarea
                id="action-reason"
                value={actionReason}
                onChange={(e) => {
                  setActionReason(e.target.value);
                  if (actionError) setActionError('');
                }}
                placeholder={type === 'approve' 
                  ? 'Add any optional comments for approval...'
                  : 'Please provide a reason for rejecting this request...'}
                className={`block w-full px-3 py-2 rounded-lg border ${
                  actionError
                    ? 'border-red-500 focus:ring-red-500/20 focus:border-red-500'
                    : 'border-gray-300 dark:border-gray-600 focus:ring-blue-500/20 focus:border-blue-500'
                } bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 text-sm focus:outline-none focus:ring-2 transition-colors`}
                rows={3}
              />
              {actionError && (
                <p className="mt-1.5 text-xs text-red-600 dark:text-red-400">{actionError}</p>
              )}
            </div>

            {/* Action Buttons */}
            <div className="col-span-2 flex justify-end gap-2 mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={onClose}
                className="px-4 py-2 rounded-lg bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 text-sm font-medium transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-1.5 ${
                  type === 'approve'
                    ? 'bg-green-600 text-white hover:bg-green-700'
                    : 'bg-red-600 text-white hover:bg-red-700'
                }`}
              >
                {type === 'approve' ? (
                  <>
                    <CheckCircle className="h-4 w-4" />
                    Confirm Approval
                  </>
                ) : (
                  <>
                    <XCircle className="h-4 w-4" />
                    Confirm Rejection
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Update the click handlers in the table and modal
  const handleApproveClick = (requestId: number) => {
    setActionRequestId(requestId);
    setShowApproveModal(true);
  };

  const handleRejectClick = (requestId: number) => {
    setActionRequestId(requestId);
    setShowRejectModal(true);
  };

  // Update the table actions
  const tableActions = (request: AttendanceRegularizationRequest) => (
    <div className="flex items-center justify-end -space-x-1">
      {request.status === 'pending' && (
        <>
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleApproveClick(request.id!);
            }}
            className="relative inline-flex items-center justify-center p-2 rounded-full text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 hover:bg-green-50 dark:hover:bg-green-900/30 transition-colors"
            title="Approve"
          >
            <CheckCircle className="h-4 w-4" />
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleRejectClick(request.id!);
            }}
            className="relative inline-flex items-center justify-center p-2 rounded-full text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/30 transition-colors"
            title="Reject"
          >
            <XCircle className="h-4 w-4" />
          </button>
        </>
      )}
      <button
        onClick={(e) => {
          e.stopPropagation();
          onEdit && onEdit(request);
        }}
        className="relative inline-flex items-center justify-center p-2 rounded-full text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors"
        title="Edit"
      >
        <Edit3 className="h-4 w-4" />
      </button>
      <button
        onClick={(e) => {
          e.stopPropagation();
          setExpandedRequest(expandedRequest === request.id ? null : (request.id ?? null));
        }}
        className="relative inline-flex items-center justify-center p-2 rounded-full text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-900/30 transition-colors"
        title="View Details"
      >
        {expandedRequest === request.id ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
      </button>
    </div>
  );

  // Update the RequestDetailsModal actions
  const modalActions = (request: AttendanceRegularizationRequest) => (
    request.status === 'pending' && (
      <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleRejectClick(request.id!);
          }}
          className="px-4 py-2 rounded-lg bg-red-50 text-red-700 hover:bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50 text-sm font-medium transition-colors flex items-center gap-1.5"
        >
          <XCircle className="h-4 w-4" />
          Reject Request
        </button>
        <button
          onClick={(e) => {
            e.stopPropagation();
            handleApproveClick(request.id!);
          }}
          className="px-4 py-2 rounded-lg bg-green-50 text-green-700 hover:bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-900/50 text-sm font-medium transition-colors flex items-center gap-1.5"
        >
          <CheckCircle className="h-4 w-4" />
          Approve Request
        </button>
      </div>
    )
  );

  return (
    <div className="space-y-6">
      {/* Show Add Request button only if form is hidden */}
      {/* {(mode === 'both' || (isAdmin && mode === 'table')) && showFormButton && !showForm && (
        <div className="flex justify-end">
          <button
            onClick={() => setShowForm(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
          >
            <Plus className="h-4 w-4 mr-2" /> 
            New Request
          </button>
        </div>
      )} */}

      {/* Statistics Cards */}
      {showStats && requests.length > 0 && StatisticsCards()}

      {/* Show form in a modal popup if toggled open */}
      {(mode === 'both' || (isAdmin && mode === 'table')) && showFormButton && showForm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40 backdrop-blur-sm">
          <div className="bg-white dark:bg-gray-800 rounded-xl w-full max-w-2xl relative p-0 m-0 flex flex-col">
            <button
              onClick={() => { setShowForm(false); onCancel && onCancel(); }}
              className="absolute top-3 right-3 text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 text-2xl font-bold"
              aria-label="Close"
            >
              ×
            </button>
            <div className="flex-1 flex flex-col justify-center">
              {EnhancedForm()}
            </div>
          </div>
        </div>
      )}

      {/* Always show table in both/table modes */}
      {(mode === 'both' || mode === 'table') && EnhancedTable()}

      {/* Expanded Row Details (now as modal) */}
      {RequestDetailsModal()}
      
      {/* Action Confirmation Modal - use a single modal for both approve and reject */}
      <ActionConfirmationModal
        isOpen={showApproveModal || showRejectModal}
        onClose={() => {
          setShowApproveModal(false);
          setShowRejectModal(false);
        }}
        type={showApproveModal ? 'approve' : 'reject'}
        requestId={actionRequestId}
      />
    </div>
  );
};

export default RegularizationManager; 