const http = require('http');

const options = {
  hostname: 'localhost',
  port: 5000,
  path: '/api/employees/list',
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
  }
};

console.log('Testing Employee List API...');
const req = http.request(options, (res) => {
  console.log(`Status Code: ${res.statusCode}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('Response:');
    try {
      const parsedData = JSON.parse(data);
      console.log(JSON.stringify(parsedData, null, 2));
    } catch (e) {
      console.log('Raw data:', data);
      console.error('Error parsing JSON:', e);
    }
  });
});

req.on('error', (error) => {
  console.error('Error making request:', error.message);
});

req.end(); 