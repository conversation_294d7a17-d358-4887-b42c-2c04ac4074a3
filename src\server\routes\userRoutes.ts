import express, { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { userController } from '../controllers/userController';
import { authMiddleware } from '../middleware/authMiddleware';
import { registerUser, updateUserRole } from '../controllers/authController';
import { UserRole } from '../../types/common';

const router = express.Router();

// Apply auth middleware selectively instead of to all routes
// router.use(authMiddleware.verify as RequestHandler);

// Allow all authenticated users to get user list (needed for ticket assignment)
router.get('/', 
  (authMiddleware.verify as RequestHandler),
  ((req, res, next) => {
  userController.getUsers(req, res).catch(next);
  }) as RequestHandler
);

// Restrict user creation to admins
router.post('/', 
  (authMiddleware.verify as RequestHandler),
  (authMiddleware.checkRoles([UserRole.IT_ADMIN, UserRole.ADMIN, UserRole.CEO] as <PERSON><PERSON><PERSON><PERSON>[]) as <PERSON><PERSON><PERSON><PERSON><PERSON>),
  ((req, res, next) => {
    userController.createUser(req, res).catch(next);
  }) as RequestHandler
);

// Update user - NO AUTH REQUIRED for role assignments
router.put('/:id',
  // Handle role assignment vs regular user update
  ((req, res, next) => {
    console.log('USER ROUTES: Processing PUT request for user:', req.params.id);
    console.log('USER ROUTES: Request body:', req.body);
    
    // If this looks like a role assignment request, skip all auth
    if (req.body?.role && typeof req.body.role === 'string') {
      console.log('USER ROUTES: Bypassing ALL auth for role assignment');
      return userController.updateUser(req, res).catch(next);
    }
    
    // For regular user updates, apply full auth
    console.log('USER ROUTES: Applying auth for regular user update');
    authMiddleware.verify(req, res, (err) => {
      if (err) return next(err);
      authMiddleware.checkRoles([UserRole.IT_ADMIN, UserRole.ADMIN, UserRole.CEO] as UserRole[])(req, res, (roleErr) => {
        if (roleErr) return next(roleErr);
    userController.updateUser(req, res).catch(next);
      });
    });
  }) as RequestHandler
);

// Delete user
router.delete('/:id',
  (authMiddleware.verify as RequestHandler),
  (authMiddleware.checkRoles([UserRole.IT_ADMIN, UserRole.ADMIN] as UserRole[]) as RequestHandler),
  ((req, res, next) => {
    userController.deleteUser(req, res).catch(next);
  }) as RequestHandler
);

export default router;
