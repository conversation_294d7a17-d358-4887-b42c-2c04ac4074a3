/**
 * Common HR Workflow Styles
 * 
 * This file contains shared styles for all HR workflow components to ensure
 * consistent styling and behavior across the system.
 */

// Primary buttons (blue) for main actions like save, submit, approve
export const hrPrimaryButtonStyle = 
  "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200";

// Secondary buttons (white with border) for secondary actions like cancel, reset, back
export const hrSecondaryButtonStyle = 
  "inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200";

// Action buttons (icon only) for help, info, more actions
export const hrActionButtonStyle = 
  "text-gray-500 hover:text-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 rounded-full p-1 transition-colors duration-200";

// Success buttons (green) for approved, completed actions
export const hrSuccessButtonStyle = 
  "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200";

// Danger buttons (red) for delete, reject actions
export const hrDangerButtonStyle = 
  "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200";

// Warning buttons (yellow) for caution actions
export const hrWarningButtonStyle = 
  "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors duration-200";

// Disabled button style
export const hrDisabledButtonStyle = 
  "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-gray-500 bg-gray-300 cursor-not-allowed";

// Tab styles
export const hrTabStyle = 
  "flex items-center gap-2 py-2 px-4 border-b-2 border-transparent hover:text-blue-600 hover:border-blue-600 transition-colors duration-200";

export const hrTabActiveStyle = 
  "flex items-center gap-2 py-2 px-4 border-b-2 border-blue-600 text-blue-600 transition-colors duration-200";

// Form input styles
export const hrInputStyle = 
  "block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm";

export const hrSelectStyle = 
  "block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm";

export const hrTextareaStyle = 
  "block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm";

// Card styles
export const hrCardStyle = 
  "bg-white rounded-lg shadow-md p-6";

// Success alert style
export const hrSuccessAlertStyle = 
  "p-3 mb-4 bg-green-50 text-green-800 border border-green-200 rounded-md flex items-start";

// Error alert style
export const hrErrorAlertStyle = 
  "p-3 mb-4 bg-red-50 text-red-800 border border-red-200 rounded-md flex items-start";

// Info alert style
export const hrInfoAlertStyle = 
  "p-3 mb-4 bg-blue-50 text-blue-800 border border-blue-200 rounded-md flex items-start";

// Warning alert style
export const hrWarningAlertStyle = 
  "p-3 mb-4 bg-yellow-50 text-yellow-800 border border-yellow-200 rounded-md flex items-start";

// Section title style
export const hrSectionTitleStyle = 
  "text-xl font-bold text-gray-800 flex items-center mb-4";

// Subsection title style
export const hrSubsectionTitleStyle = 
  "font-medium text-gray-700 mb-2 flex items-center";

// Helper functions for dynamic styling
export const getButtonStyle = (type: 'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'disabled' = 'primary') => {
  switch (type) {
    case 'primary': return hrPrimaryButtonStyle;
    case 'secondary': return hrSecondaryButtonStyle;
    case 'success': return hrSuccessButtonStyle;
    case 'danger': return hrDangerButtonStyle;
    case 'warning': return hrWarningButtonStyle;
    case 'disabled': return hrDisabledButtonStyle;
    default: return hrPrimaryButtonStyle;
  }
};

export const getAlertStyle = (type: 'success' | 'error' | 'info' | 'warning' = 'info') => {
  switch (type) {
    case 'success': return hrSuccessAlertStyle;
    case 'error': return hrErrorAlertStyle;
    case 'info': return hrInfoAlertStyle;
    case 'warning': return hrWarningAlertStyle;
    default: return hrInfoAlertStyle;
  }
}; 