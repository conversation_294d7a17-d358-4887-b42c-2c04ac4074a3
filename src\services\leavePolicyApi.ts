import { 
  HolidayCalendar,
  Holiday,
  LeavePolicyVersion
} from '../types/attendance';

interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  message?: string;
}

// Complete Leave Policy Configuration Interface
export interface LeavePolicyConfiguration {
  id?: number;
  companyId?: number;
  leaveTypes: LeaveTypePolicy[];
  holidayCalendars: HolidayCalendar[];
  version: string;
  effectiveDate: string;
  isActive: boolean;
  createdBy: number;
  createdAt: string;
  updatedAt: string;
}

export interface LeaveTypePolicy {
  id: number;
  leaveType: string; // Dynamic leave type
  enabled: boolean;
  displayName: string;
  description: string;
  settings: any; // Individual leave type settings

  applicableRoles: string[];
  applicableDepartments: string[];
  maxDaysPerYear: number;
  minServicePeriod: number; // months
  allowCarryForward: boolean;
  carryForwardLimit: number;
  encashmentAllowed: boolean;
  documentRequired: boolean;
  color?: string;
  sortOrder?: number;
  isActive: boolean;
  category?: string;
  genderEligibility?: string;
  effectiveFrom?: string;
  validUntil?: string;
}

// API Base URL - should be configured via environment variables
const API_BASE_URL = (typeof process !== 'undefined' && process.env?.REACT_APP_API_URL) || 'http://localhost:5000/api';

// Default fallback data
const DEFAULT_POLICY_CONFIGURATION: LeavePolicyConfiguration = {
  id: 1,
  companyId: 1,
          leaveTypes: [],
        holidayCalendars: [],
  version: 'v1.0.0',
  effectiveDate: new Date().toISOString(),
  isActive: true,
  createdBy: 1,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
};

// Mock data - remove hardcoded leave types, make it empty/dynamic
const mockLeavePolicies: LeavePolicyConfiguration[] = [];

const mockLeaveTypes: LeaveTypePolicy[] = [];

// Helper function for API calls with graceful fallback
const apiCall = async <T>(
  endpoint: string, 
  options: RequestInit = {},
  fallbackData?: T
): Promise<ApiResponse<T>> => {
  try {
    const authToken = localStorage.getItem('authToken');
    // Remove debug console.log for production
    // console.log('🔍 API Call Debug Info:', {
    //   endpoint: `${API_BASE_URL}${endpoint}`,
    //   method: options.method || 'GET',
    //   hasAuthToken: !!authToken,
    //   authTokenPreview: authToken ? `${authToken.substring(0, 10)}...` : 'none'
    // });

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
        ...options.headers,
      },
      ...options,
    });

    // If endpoint doesn't exist (404), return fallback data silently
    if (response.status === 404 && fallbackData !== undefined) {
      return {
        success: true,
        data: fallbackData,
        message: 'Using default data (endpoint not implemented)'
      };
    }

    // Check if response is JSON
    const contentType = response.headers.get('content-type');
    const isJson = contentType && contentType.includes('application/json');

    let data;
    if (isJson) {
      try {
        data = await response.json();
      } catch (jsonError) {
        if (fallbackData !== undefined) {
          return {
            success: true,
            data: fallbackData,
            message: 'Using default data (JSON parse error)'
          };
        }
        throw new Error(`Failed to parse JSON response: ${jsonError}`);
      }
    } else {
      // If not JSON and we have fallback data, use it
      if (fallbackData !== undefined) {
        return {
          success: true,
          data: fallbackData,
          message: 'Using default data (non-JSON response)'
        };
      }
      throw new Error(`Server returned non-JSON response (${response.status}): ${response.statusText}`);
    }

    if (!response.ok) {
      if (fallbackData !== undefined) {
        return {
          success: true,
          data: fallbackData,
          message: 'Using default data (API error)'
        };
      }
      throw new Error(data?.message || `HTTP error! status: ${response.status}`);
    }

    return {
      success: true,
      data: data.data || data,
      message: data.message
    };
  } catch (error) {
    // If we have fallback data, use it instead of failing
    if (fallbackData !== undefined) {
      return {
        success: true,
        data: fallbackData,
        message: 'Using default data (API unavailable)'
      };
    }

    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`API call failed for ${endpoint}:`, errorMessage);
    
    return {
      success: false,
      data: {} as T,
      error: errorMessage
    };
  }
};



// Leave Policy Configuration API
export const leavePolicyApi = {
  // Get current active leave policy configuration
  getCurrent: async (): Promise<ApiResponse<LeavePolicyConfiguration>> => {
    return apiCall<LeavePolicyConfiguration>('/leave-policies/current');
  },

  // Get leave policy by ID
  getById: async (id: number): Promise<ApiResponse<LeavePolicyConfiguration>> => {
    return apiCall<LeavePolicyConfiguration>(`/leave-policies/${id}`);
  },

  // Get all leave policy versions
  getVersions: async (page = 1, limit = 10): Promise<PaginatedResponse<LeavePolicyVersion>> => {
    const response = await apiCall<any>(`/leave-policies/versions?page=${page}&limit=${limit}`);
    return {
      ...response,
      data: response.data?.items || [],
      pagination: response.data?.pagination || { page, limit, total: 0, totalPages: 0 }
    };
  },

  // Create new leave policy configuration
  create: async (policyData: Omit<LeavePolicyConfiguration, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<LeavePolicyConfiguration>> => {
    return apiCall<LeavePolicyConfiguration>('/leave-policies', {
      method: 'POST',
      body: JSON.stringify(policyData)
    });
  },

  // Update existing leave policy configuration
  update: async (id: number, policyData: Partial<LeavePolicyConfiguration>): Promise<ApiResponse<LeavePolicyConfiguration>> => {
    return apiCall<LeavePolicyConfiguration>(`/leave-policies/${id}`, {
      method: 'PUT',
      body: JSON.stringify(policyData)
    });
  },

  // Delete leave policy configuration
  delete: async (id: number): Promise<ApiResponse<void>> => {
    return apiCall<void>(`/leave-policies/${id}`, {
      method: 'DELETE'
    });
  },

  // Activate a specific policy version
  activate: async (id: number): Promise<ApiResponse<LeavePolicyConfiguration>> => {
    return apiCall<LeavePolicyConfiguration>(`/leave-policies/${id}/activate`, {
      method: 'POST'
    });
  }
};

// Leave Types API
export const leaveTypesApi = {
  // Get all leave types
  getAll: async (): Promise<ApiResponse<LeaveTypePolicy[]>> => {
    return apiCall<LeaveTypePolicy[]>('/leave-policies/leave-types');
  },

  // Get active leave types only
  getActive: async (): Promise<ApiResponse<LeaveTypePolicy[]>> => {
    return apiCall<LeaveTypePolicy[]>('/leave-policies/leave-types/active');
  },

  // Get leave type by ID
  getById: async (id: number): Promise<ApiResponse<LeaveTypePolicy>> => {
    return apiCall<LeaveTypePolicy>(`/leave-policies/leave-types/${id}`);
  },

  // Create new leave type
  create: async (leaveType: Omit<LeaveTypePolicy, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<LeaveTypePolicy>> => {
    return apiCall<LeaveTypePolicy>('/leave-policies/leave-types', {
      method: 'POST',
      body: JSON.stringify(leaveType)
    });
  },

  // Update leave type
  update: async (id: number, leaveType: Partial<LeaveTypePolicy>): Promise<ApiResponse<LeaveTypePolicy>> => {
    return apiCall<LeaveTypePolicy>(`/leave-policies/leave-types/${id}`, {
      method: 'PUT',
      body: JSON.stringify(leaveType)
    });
  },

  // Delete leave type
  delete: async (id: number): Promise<ApiResponse<void>> => {
    return apiCall<void>(`/leave-policies/leave-types/${id}`, {
      method: 'DELETE'
    });
  },

  // Bulk update leave types
  bulkUpdate: async (leaveTypes: LeaveTypePolicy[]): Promise<ApiResponse<LeaveTypePolicy[]>> => {
    return apiCall<LeaveTypePolicy[]>('/leave-policies/leave-types/bulk', {
      method: 'PUT',
      body: JSON.stringify({ leaveTypes })
    });
  }
};



// Holiday Calendar API
export const holidayCalendarApi = {
  // Get all holiday calendars
  getAll: async (): Promise<ApiResponse<HolidayCalendar[]>> => {
    return apiCall<HolidayCalendar[]>('/leave-policies/holiday-calendars');
  },

  // Get holiday calendar by ID
  getById: async (id: number): Promise<ApiResponse<HolidayCalendar>> => {
    return apiCall<HolidayCalendar>(`/leave-policies/holiday-calendars/${id}`);
  },

  // Create new holiday calendar
  create: async (calendar: Omit<HolidayCalendar, 'id'>): Promise<ApiResponse<HolidayCalendar>> => {
    return apiCall<HolidayCalendar>('/leave-policies/holiday-calendars', {
      method: 'POST',
      body: JSON.stringify(calendar)
    });
  },

  // Update holiday calendar
  update: async (id: number, calendar: Partial<HolidayCalendar>): Promise<ApiResponse<HolidayCalendar>> => {
    return apiCall<HolidayCalendar>(`/leave-policies/holiday-calendars/${id}`, {
      method: 'PUT',
      body: JSON.stringify(calendar)
    });
  },

  // Delete holiday calendar
  delete: async (id: number): Promise<ApiResponse<void>> => {
    return apiCall<void>(`/leave-policies/holiday-calendars/${id}`, {
      method: 'DELETE'
    });
  }
};

// Holiday API
export const holidayApi = {
  // Get holidays by calendar ID
  getByCalendar: async (calendarId: number): Promise<ApiResponse<Holiday[]>> => {
    return apiCall<Holiday[]>(`/leave-policies/holiday-calendars/${calendarId}/holidays`);
  },

  // Create new holiday
  create: async (holiday: Omit<Holiday, 'id'>): Promise<ApiResponse<Holiday>> => {
    return apiCall<Holiday>('/leave-policies/holidays', {
      method: 'POST',
      body: JSON.stringify(holiday)
    });
  },

  // Update holiday
  update: async (id: number, holiday: Partial<Holiday>): Promise<ApiResponse<Holiday>> => {
    return apiCall<Holiday>(`/leave-policies/holidays/${id}`, {
      method: 'PUT',
      body: JSON.stringify(holiday)
    });
  },

  // Delete holiday
  delete: async (id: number): Promise<ApiResponse<void>> => {
    return apiCall<void>(`/leave-policies/holidays/${id}`, {
      method: 'DELETE'
    });
  },

  // Bulk create holidays
  bulkCreate: async (holidays: Omit<Holiday, 'id'>[]): Promise<ApiResponse<Holiday[]>> => {
    return apiCall<Holiday[]>('/leave-policies/holidays/bulk', {
      method: 'POST',
      body: JSON.stringify({ holidays })
    });
  }
}; 