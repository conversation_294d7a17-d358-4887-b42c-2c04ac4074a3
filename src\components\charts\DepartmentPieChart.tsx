import React from 'react';
import { Pie } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';

ChartJS.register(ArcElement, Tooltip, Legend);

interface DepartmentInfo {
  name: string;
  ticketCount: number;
  color: string;
}

interface DepartmentPieChartProps {
  departmentStats: Record<string, DepartmentInfo>;
}

export const DepartmentPieChart: React.FC<DepartmentPieChartProps> = ({ departmentStats }) => {
  // Add debugging logs
  console.log('Raw department stats:', departmentStats);
  
  // Filter out departments with zero tickets
  const departments = Object.values(departmentStats).filter(dept => dept.ticketCount > 0);
  console.log('Filtered departments:', departments);
  
  const data = {
    labels: departments.map(dept => `${dept.name} (${dept.ticketCount})`),
    datasets: [
      {
        data: departments.map(dept => dept.ticketCount),
        backgroundColor: departments.map(dept => dept.color),
        borderColor: departments.map(dept => dept.color),
        borderWidth: 1,
      },
    ],
  };
  console.log('Chart data:', data);

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
        labels: {
          padding: 20,
          font: {
            size: 12
          },
          generateLabels: (chart: any) => {
            const datasets = chart.data.datasets;
            return chart.data.labels.map((label: string, i: number) => ({
              text: label,
              fillStyle: datasets[0].backgroundColor[i],
              strokeStyle: datasets[0].borderColor[i],
              lineWidth: 1,
              hidden: false,
              index: i
            }));
          }
        }
      },
      tooltip: {
        callbacks: {
          label: (context: any) => {
            const label = context.label || '';
            const value = context.raw || 0;
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `${label}: ${percentage}%`;
          }
        }
      }
    },
  };

  // If there's no data, show a message
  if (departments.length === 0) {
    console.log('No departments to display');
    return (
      <div className="flex items-center justify-center h-[300px] text-gray-500">
        No department data available
      </div>
    );
  }

  return (
    <div style={{ height: '300px', width: '100%' }}>
      <Pie data={data} options={options} />
    </div>
  );
}; 