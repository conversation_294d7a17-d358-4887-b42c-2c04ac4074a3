import { <PERSON>tity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, ManyToMany, JoinTable } from 'typeorm';
import { User } from './User';
import { Asset } from './Asset';
import { Vendor } from './Vendor';
import { LicenseType, PaymentFrequency, SoftwareStatus } from '../types/SoftwareLicense';

@Entity()
export class SoftwareLicense {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Basic Information
  @Column({ type: 'varchar' })
  name: string;

  @ManyToOne(() => Vendor)
  vendor: Vendor;

  @Column({ type: 'varchar' })
  category: string;

  @Column({ type: 'varchar' })
  type: LicenseType;

  @Column({ type: 'varchar' })
  department: string;

  @Column({ type: 'varchar' })
  status: SoftwareStatus;

  // Licensing & Cost
  @Column({ type: 'varchar', nullable: true })
  licenseKey: string;

  @Column({ type: 'int' })
  totalSeats: number;

  @Column({ type: 'int', default: 0 })
  usedSeats: number;

  @Column({ type: 'timestamp' })
  purchaseDate: Date;

  @Column({ type: 'timestamp', nullable: true })
  expiryDate: Date | null;

  @Column({ type: 'varchar' })
  paymentFrequency: PaymentFrequency;

  @Column('decimal', { precision: 10, scale: 2 })
  costPKR: number;

  @Column('decimal', { precision: 10, scale: 2 })
  costUSD: number;

  @Column({ type: 'varchar' })
  paidBy: string;

  @Column({ type: 'varchar', nullable: true })
  invoiceUrl: string | null;

  @Column({ type: 'boolean', default: false })
  autoRenew: boolean;

  // Assignment
  @ManyToMany(() => User)
  @JoinTable({
    name: '_assigned_users',
    joinColumn: {
      name: 'softwareLicenseId',
      referencedColumnName: 'id'
    },
    inverseJoinColumn: {
      name: 'userId',
      referencedColumnName: 'id'
    }
  })
  assignedUsers: User[];

  @ManyToMany(() => Asset)
  @JoinTable({
    name: 'software_license_linked_assets',
    joinColumn: {
      name: 'softwareLicenseId',
      referencedColumnName: 'id'
    },
    inverseJoinColumn: {
      name: 'assetId',
      referencedColumnName: 'id'
    }
  })
  linkedAssets: Asset[];

  @Column('text', { nullable: true })
  notes: string | null;

  // Advanced Options
  @Column({ type: 'boolean', default: false })
  currencyConverter: boolean;

  @Column({ type: 'boolean', default: false })
  renewalReminder: boolean;

  @Column({ type: 'boolean', default: false })
  multiLocationUse: boolean;

  @Column({ type: 'varchar', nullable: true })
  loginSharingInfo: string | null;

  @Column('simple-array', { nullable: true })
  socialMediaLinks: string[];

  // Metadata
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'varchar', nullable: true })
  createdBy: string | null;

  @Column({ type: 'varchar', nullable: true })
  updatedBy: string | null;
} 