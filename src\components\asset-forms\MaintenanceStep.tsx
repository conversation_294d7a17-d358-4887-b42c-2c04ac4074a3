import React from 'react';
import { Asset } from '../../types/asset';

interface MaintenanceStepProps {
  formData: Partial<Asset>;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  errors: Record<string, string>;
}

export const MaintenanceStep: React.FC<MaintenanceStepProps> = ({
  formData,
  onChange,
  errors,
}) => {
  const inputClassName = "w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors";
  const labelClassName = "block text-sm font-medium text-gray-700 mb-1";

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <label htmlFor="lastMaintenance" className={labelClassName}>Last Maintenance Date</label>
        <input
          type="date"
          id="lastMaintenance"
          name="lastMaintenance"
          value={formData.lastMaintenance || ''}
          onChange={onChange}
          className={inputClassName}
        />
      </div>

      <div>
        <label htmlFor="maintenanceBy" className={labelClassName}>Maintenance Performed By</label>
        <select
          id="maintenanceBy"
          name="maintenanceBy"
          value={formData.maintenanceBy || ''}
          onChange={onChange}
          className={inputClassName}
        >
          <option value="">Select Maintainer</option>
          <option value="Vendor">Vendor</option>
          <option value="IT Team">IT Team</option>
          <option value="Third-Party">Third-Party</option>
        </select>
      </div>

      <div className="col-span-2">
        <label htmlFor="notes" className={labelClassName}>Maintenance Notes</label>
        <textarea
          id="notes"
          name="notes"
          value={formData.notes || ''}
          onChange={onChange}
          className={`${inputClassName} h-32`}
        />
      </div>
    </div>
  );
}; 