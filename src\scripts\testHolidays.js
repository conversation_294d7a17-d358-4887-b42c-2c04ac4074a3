const mysql = require('mysql2/promise');
require('dotenv').config();

async function testHolidays() {
  let connection;
  
  try {
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USERNAME || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_DATABASE || 'ims_db',
      port: process.env.DB_PORT || 3306
    });

    console.log('✅ Connected to database');

    // Test if holidays table exists and has data
    const [holidays] = await connection.execute('SELECT * FROM holidays ORDER BY date');
    
    console.log(`\n📋 Found ${holidays.length} holidays in database:`);
    holidays.forEach(holiday => {
      let metadata = {};
      try {
        if (holiday.metadata && typeof holiday.metadata === 'string') {
          metadata = JSON.parse(holiday.metadata);
        } else if (holiday.metadata && typeof holiday.metadata === 'object') {
          metadata = holiday.metadata;
        }
      } catch (e) {
        console.log(`   Warning: Could not parse metadata for ${holiday.name}`);
        metadata = {};
      }
      console.log(`   • ${holiday.name} (${holiday.date}) - ${holiday.type} - ${metadata.religion || 'General'}`);
    });

    // Test configuration
    const [configs] = await connection.execute('SELECT * FROM holiday_configurations');
    console.log(`\n⚙️ Found ${configs.length} holiday configuration(s)`);
    
    console.log('\n✅ Database test completed successfully!');
    
  } catch (error) {
    console.error('❌ Error testing holidays:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the test
testHolidays(); 