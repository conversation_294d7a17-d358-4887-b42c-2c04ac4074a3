import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class UpdateAssetIdToAutoIncrement1746000000004 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Only add a uuid column if it does not exist
        const [{ count }] = await queryRunner.query(`
            SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'assets' AND COLUMN_NAME = 'uuid'
        `);
        if (Number(count) === 0) {
            await queryRunner.addColumn(
                "assets",
                new TableColumn({
                    name: "uuid",
                    type: "varchar",
                    length: "36",
                    isNullable: true,
                    isUnique: true
                })
            );
        }
        // No changes to PK or id column
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove the uuid column if it exists
        const [{ count }] = await queryRunner.query(`
            SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'assets' AND COLUMN_NAME = 'uuid'
        `);
        if (Number(count) > 0) {
            await queryRunner.dropColumn("assets", "uuid");
        }
    }
} 