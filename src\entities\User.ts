import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, <PERSON>umn, CreateDateColumn, UpdateDateColumn, OneToMany, BeforeInsert, BeforeUpdate } from 'typeorm';
import { Ticket } from './Ticket';
import { Comment } from './Comment';
import { KnowledgeBase } from './KnowledgeBase';
import { Attachment } from './Attachment';
import { IsEmail, IsNotEmpty, MinLength, IsEnum, Length } from 'class-validator';
import bcrypt from 'bcryptjs';
import { UserRole } from '../types/common';
import { UserPermissions } from './UserPermissions';
import { UserRoleAssignment } from './UserRoleAssignment';
import { Role } from './Role';

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100 })
  @IsNotEmpty({ message: 'Name is required' })
  @Length(2, 100, { message: 'Name must be between 2 and 100 characters' })
  name: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  @IsEmail({}, { message: 'Invalid email format' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @Column({ type: 'varchar', length: 255 })
  @IsNotEmpty({ message: 'Password is required' })
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  password: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.EMPLOYEE
  })
  @IsEnum(UserRole)
  role: UserRole;

  @Column({ type: 'varchar', length: 50 })
  @IsNotEmpty({ message: 'Department is required' })
  @Length(2, 50, { message: 'Department must be between 2 and 50 characters' })
  department: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @Length(2, 100, { message: 'Project name must be between 2 and 100 characters' })
  project?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  @Length(2, 100, { message: 'Location must be between 2 and 100 characters' })
  location?: string;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column('json', { nullable: true })
  permissions: any;

  @Column('json', { nullable: true })
  oversightPermissions: {
    departments: string[];
    projects: string[];
    locations: string[];
    hasFullAccess: boolean;
  };

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @OneToMany(() => Ticket, ticket => ticket.createdBy)
  createdTickets: Ticket[];

  @OneToMany(() => Ticket, ticket => ticket.assignedTo)
  assignedTickets: Ticket[];

  @OneToMany(() => Comment, comment => comment.createdBy)
  comments: Comment[];

  @OneToMany(() => KnowledgeBase, article => article.createdBy)
  knowledgeArticles: KnowledgeBase[];

  @OneToMany(() => Attachment, attachment => attachment.uploadedBy)
  uploads: Attachment[];

  @OneToMany(() => UserRoleAssignment, assignment => assignment.user, { eager: false, cascade: true })
  roleAssignments: UserRoleAssignment[];

  @Column('simple-array', { nullable: true })
  dashboardAccess: string[];

  private tempPassword: string | null = null;

  setPassword(password: string) {
    this.tempPassword = password;
  }

  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword() {
    if (this.tempPassword) {
      const salt = await bcrypt.genSalt(10);
      this.password = await bcrypt.hash(this.tempPassword, salt);
      this.tempPassword = null;
    }
  }

  async validatePassword(password: string): Promise<boolean> {
    try {
      return await bcrypt.compare(password, this.password);
    } catch (error) {
      console.error('Password validation error:', error);
      return false;
    }
  }

  // Helper methods to check permissions
  canCreateTickets(): boolean {
    return this.permissions?.canCreateTickets ?? false;
  }

  canCommentOnTicket(ticket: Ticket): boolean {
    return this.role !== 'EMPLOYEE' || ticket.createdById === this.id;
  }

  canReplyToAnyTicket(): boolean {
    return ['IT_ADMIN', 'IT_STAFF', 'HR_ADMIN'].includes(this.role);
  }

  canResolveTicket(): boolean {
    return ['IT_ADMIN', 'IT_STAFF', 'HR_ADMIN'].includes(this.role);
  }

  canDeleteTicket(): boolean {
    return this.role === 'IT_ADMIN';
  }

  // HR permissions
  canManageEmployees(): boolean {
    return ['IT_ADMIN', 'HR_ADMIN', 'HR_STAFF'].includes(this.role) || 
           (this.permissions?.canCreateEmployee || 
            this.permissions?.canEditEmployee || 
            this.permissions?.canDeleteEmployee || 
            this.permissions?.canViewEmployees) || false;
  }

  // Exclude password when converting to JSON
  toJSON() {
    const { password, tempPassword, ...rest } = this;
    return rest;
  }

  async getAllRoles(): Promise<Role[]> {
    const roles: Role[] = [];
    
    if (this.roleAssignments && this.roleAssignments.length > 0) {
      for (const assignment of this.roleAssignments) {
        // Skip expired temporary roles
        if (assignment.expiresAt && new Date(assignment.expiresAt) < new Date()) {
          continue;
        }
        roles.push(assignment.role);
      }
    }
    
    return roles;
  }

  async hasPermission(permissionId: string): Promise<boolean> {
    // First check direct permissions
    if (this.permissions && this.permissions[permissionId]) {
      return true;
    }
    
    // Then check role-based permissions
    const roles = await this.getAllRoles();
    for (const role of roles) {
      const rolePermissions = await role.getAllPermissions();
      if (rolePermissions.includes(permissionId)) {
        return true;
      }
    }
    
    return false;
  }
} 