import { DataSource } from 'typeorm';
import { User } from '../entities/User';
import { UserRole, UserPermissions } from '../types/common';
import { Ticket } from '../entities/Ticket';
import { Comment } from '../entities/Comment';
import { Attachment } from '../entities/Attachment';
import bcrypt from 'bcryptjs';

const AppDataSource = new DataSource({
  type: "mysql",
  host: "localhost",
  port: 3306,
  username: "root",
  password: "root",
  database: "ims_db",
  entities: [User, Ticket, Comment, Attachment],
  synchronize: false,
  logging: true, // Enable logging
});

async function createAdminUser() {
  try {
    // Initialize database connection
    await AppDataSource.initialize();
    console.log("Database connection initialized");

    const userRepository = AppDataSource.getRepository(User);
    console.log("User repository created");

    // Delete existing admin if exists
    const existingAdmin = await userRepository.findOne({ where: { email: '<EMAIL>' } });
    console.log("Checked for existing admin:", existingAdmin ? "found" : "not found");
    
    if (existingAdmin) {
      await userRepository.remove(existingAdmin);
      console.log('Existing admin user deleted');
    }

    // Hash password manually since BeforeInsert hook might not work in scripts
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('admin123', salt);

    // Create admin user permissions
    const adminPermissions: UserPermissions = {
      canCreateTickets: true,
      canCreateTicketsForOthers: true,
      canEditTickets: true,
      canDeleteTickets: true,
      canAssignTickets: true,
      canEscalateTickets: true,
      canViewAllTickets: true,
      department: 'IT Department',
      isAdmin: true
    };

    console.log("Creating admin user with permissions:", adminPermissions);

    // Create admin user
    const adminUser = userRepository.create({
      name: 'Admin User',
      email: '<EMAIL>',
      password: hashedPassword,
      role: UserRole.IT_ADMIN,
      department: 'IT Department',
      isActive: true,
      permissions: adminPermissions,
    });

    console.log("Admin user object created:", { ...adminUser, password: '[HIDDEN]' });

    await userRepository.save(adminUser);
    console.log('Admin user saved successfully');
  } catch (error) {
    console.error('Error creating admin user:', error);
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Stack trace:', error.stack);
    }
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log("Database connection closed");
    }
  }
}

createAdminUser(); 