import { En<PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { LeaveTypePolicy } from './LeaveTypePolicy';
import { HolidayCalendar } from './HolidayCalendar';

@Entity('leave_policy_configurations')
export class LeavePolicyConfiguration {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int', nullable: true })
  companyId: number;



  @Column({ type: 'varchar', length: 50 })
  version: string;

  @Column({ type: 'timestamp' })
  effectiveDate: Date;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'int' })
  createdBy: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @OneToMany(() => LeaveTypePolicy, leaveType => leaveType.policyConfiguration)
  leaveTypes: LeaveTypePolicy[];



  @OneToMany(() => HolidayCalendar, holidayCalendar => holidayCalendar.policyConfiguration)
  holidayCalendars: HolidayCalendar[];
} 