import { Router } from 'express';
import { ticketController } from '../controllers/ticketController';
import { authMiddleware } from '../middleware/authMiddleware';

const router = Router();

// Protect all routes with authentication
router.use(authMiddleware.verify);

// Ticket routes
router.post('/', ticketController.createTicket);
router.get('/', ticketController.getTickets);
router.patch('/:ticketId/status', ticketController.updateTicketStatus);

// Comment routes
router.post('/:ticketId/comments', ticketController.addComment);

// Admin routes
router.delete('/:ticketId', authMiddleware.checkRoles(['IT_ADMIN']), ticketController.deleteTicket);

export default router; 