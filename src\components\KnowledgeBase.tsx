import React, { useState, useMemo, useEffect } from 'react';
import {
  Search,
  Book,
  BookOpen,
  ChevronRight,
  ThumbsUp,
  ThumbsDown,
  MessageSquare,
  Share2,
  Printer,
  Clock,
  Tag,
  User,
  Plus,
  Menu,
  X,
  FileText,
  Filter,
  ArrowUpRight,
  BookMarked,
  Bookmark,
  Eye,
  Star,
  Layout,
  Settings,
  HelpCircle,
  Bell,
  Edit,
  Trash2
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { ArticleForm } from './ArticleForm';
import { UserRole } from '../types/common';
import { KnowledgeStatus } from '../entities/KnowledgeBase';
import { KnowledgeArticle, KnowledgeCategory } from '../types/knowledge';
import KnowledgeBaseHeader from './KnowledgeBaseHeader';
import { useNavigate } from 'react-router-dom';

interface KnowledgeBaseProps {
  articles: KnowledgeArticle[];
  categories: KnowledgeCategory[];
  selectedCategory: string;
  searchQuery: string;
  isLoading: boolean;
  error: string | null;
  canCreateArticle: boolean;
  onCategorySelect: (categoryId: string) => void;
  onSearchChange: (query: string) => void;
  onSearchSubmit: (e: React.FormEvent) => void;
  onArticleClick: (articleId: string) => void;
}

export function KnowledgeBase({
  articles,
  categories,
  selectedCategory,
  searchQuery,
  isLoading,
  error,
  canCreateArticle,
  onCategorySelect,
  onSearchChange,
  onSearchSubmit,
  onArticleClick
}: KnowledgeBaseProps) {
  const navigate = useNavigate();

  const handleCreateArticle = () => {
    navigate('/service-desk/knowledge/create');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white p-8">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Use the new KnowledgeBaseHeader component */}
      <KnowledgeBaseHeader 
        searchQuery={searchQuery}
        onSearchChange={(e) => onSearchChange(e.target.value)}
        onSearchSubmit={onSearchSubmit}
      />

      {/* Categories section */}
      <div className="bg-gray-50 border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <div className="flex items-center mb-2">
            <Tag className="h-4 w-4 text-gray-500 mr-1.5" />
            <h2 className="text-base font-medium text-gray-900">Categories</h2>
          </div>
          
          <div className="flex flex-wrap gap-1.5 pb-1">
            <button
              onClick={() => onCategorySelect('')}
              className={`px-2.5 py-1 rounded-md text-xs font-medium transition-colors ${
                selectedCategory === '' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              All
            </button>
            
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => onCategorySelect(category.id)}
                className={`px-2.5 py-1 rounded-md text-xs font-medium transition-colors ${
                  selectedCategory === category.id 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Articles grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="space-y-6">
          {articles.map(article => (
            <div 
              key={article.id}
              onClick={() => onArticleClick(article.id)}
              className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow cursor-pointer flex flex-col md:flex-row"
            >
              {/* Article image (left side) */}
              <div className="relative md:w-80 h-56 md:h-auto bg-gradient-to-r from-blue-500 to-indigo-600 flex-shrink-0">
                {/* If you have article images, you can use them here */}
                {article.isFeatured && (
                  <div className="absolute top-3 left-3">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      Featured
                    </span>
                  </div>
                )}
              </div>
              
              {/* Article content (right side) */}
              <div className="p-6 flex-1">
                <div className="flex flex-col h-full justify-between">
                  <div>
                    <h3 className="text-2xl font-semibold text-gray-900 mb-3">
                      {article.title}
                    </h3>
                    <p className="text-gray-600 mb-4">
                      {article.summary || article.content}
                    </p>
                  </div>
                  
                  <div className="mt-auto">
                    {/* Author and metadata */}
                    <div className="flex items-center mb-4">
                      <img 
                        src={`https://ui-avatars.com/api/?name=${encodeURIComponent(article.createdBy?.name || 'Unknown')}&background=random`} 
                        alt={article.createdBy?.name || 'Unknown'}
                        className="w-8 h-8 rounded-full mr-3"
                      />
                      <span className="font-medium text-gray-700">{article.createdBy?.name || 'Unknown'}</span>
                      <div className="flex items-center ml-4">
                        <Clock className="h-4 w-4 text-gray-400 mr-1" />
                        <span className="text-gray-500">Updated {new Date().toLocaleDateString('en-US', { month: 'numeric', day: 'numeric', year: 'numeric' })}</span>
                      </div>
                    </div>
                    
                    {/* Category and views */}
                    <div className="flex items-center justify-between">
                      <span className="inline-flex items-center px-3 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800">
                        {article.category.name}
                      </span>
                      <div className="flex items-center text-sm text-gray-500">
                        <Star className="h-4 w-4 text-yellow-400 mr-1" />
                        <span>{article.viewCount} views</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Create Article Button */}
      {canCreateArticle && (
        <button
          onClick={handleCreateArticle}
          className="fixed bottom-8 right-8 inline-flex items-center px-4 py-3 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
        >
          <Plus className="h-5 w-5 mr-2" />
          Create Article
        </button>
      )}
    </div>
  );
}