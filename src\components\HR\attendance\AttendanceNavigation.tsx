import React, { useState } from 'react';
import { 
  Calendar, 
  ClipboardCheck, 
  Clock, 
  FileText, 
  UserCog, 
  Settings, 
  Users,
  CalendarDays,
  CalendarRange,
  UserCheck,
  BarChart3,
  ShieldCheck,
  HelpCircle
} from 'lucide-react';
import { hrTabStyle, hrTabActiveStyle } from '../../../styles/hrWorkflow';

interface AttendanceNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  pendingRegularizations?: number;
  userRole: 'employee' | 'manager' | 'admin';
}

const AttendanceNavigation: React.FC<AttendanceNavigationProps> = ({
  activeTab,
  onTabChange,
  pendingRegularizations = 0,
  userRole = 'employee'
}) => {
  // Group tabs by categories to follow HR workflow
  const employeeTabs = [
    { id: 'daily', label: 'My Attendance', icon: <ClipboardCheck className="h-4 w-4" /> },
    { id: 'mark', label: 'Clock In/Out', icon: <Clock className="h-4 w-4" /> },
    { id: 'timesheet', label: 'My Timesheet', icon: <FileText className="h-4 w-4" /> },
    { id: 'request', label: 'Request', icon: <UserCheck className="h-4 w-4" /> },
  ];

  const managerTabs = [
    { id: 'team', label: 'Team Attendance', icon: <Users className="h-4 w-4" /> },
    { id: 'regularization', label: 'Approvals', icon: <ShieldCheck className="h-4 w-4" />, badge: pendingRegularizations },
    { id: 'calendar', label: 'Team Calendar', icon: <CalendarDays className="h-4 w-4" /> },
  ];

  const adminTabs = [
    { id: 'reports', label: 'Reports & Analytics', icon: <BarChart3 className="h-4 w-4" /> },
    { id: 'shifts', label: 'Shift Management', icon: <CalendarRange className="h-4 w-4" /> },
    { id: 'holidays', label: 'Holidays', icon: <Calendar className="h-4 w-4" /> },
    { id: 'settings', label: 'Configuration', icon: <Settings className="h-4 w-4" /> },
  ];

  const advancedTab = { id: 'advanced', label: 'Advanced Time Tracking', icon: <UserCog className="h-4 w-4" /> };

  const getTabClassName = (tabId: string) => {
    return tabId === activeTab ? hrTabActiveStyle : hrTabStyle;
  };

  return (
    <div className="flex flex-col space-y-4 w-full">
      {/* Main navigation */}
      <div className="flex flex-col">
        <div className="border-b pb-1 mb-2">
          <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider">My Activities</h3>
          <div className="flex mt-1 overflow-x-auto hide-scrollbar">
            {employeeTabs.map(tab => (
              <button
                key={tab.id}
                className={getTabClassName(tab.id)}
                onClick={() => onTabChange(tab.id)}
              >
                {tab.icon}
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {(userRole === 'manager' || userRole === 'admin') && (
          <div className="border-b pb-1 mb-2">
            <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider">Team Management</h3>
            <div className="flex mt-1 overflow-x-auto hide-scrollbar">
              {managerTabs.map(tab => (
                <button
                  key={tab.id}
                  className={getTabClassName(tab.id)}
                  onClick={() => onTabChange(tab.id)}
                >
                  <div className="relative">
                    {tab.icon}
                    {tab.badge && tab.badge > 0 && (
                      <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                        {tab.badge}
                      </span>
                    )}
                  </div>
                  <span>{tab.label}</span>
                  {tab.id === 'regularization' && tab.badge && tab.badge > 0 && (
                    <span className="ml-1 text-xs bg-red-100 text-red-800 rounded-full px-2 py-0.5">
                      {tab.badge}
                    </span>
                  )}
                </button>
              ))}
            </div>
          </div>
        )}

        {userRole === 'admin' && (
          <div className="border-b pb-1 mb-2">
            <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider">Administration</h3>
            <div className="flex mt-1 overflow-x-auto hide-scrollbar">
              {adminTabs.map(tab => (
                <button
                  key={tab.id}
                  className={getTabClassName(tab.id)}
                  onClick={() => onTabChange(tab.id)}
                >
                  {tab.icon}
                  <span>{tab.label}</span>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
      
      {/* Advanced Time Tracking is available to all roles */}
      <div className="border-t pt-2">
        <button
          className={`${getTabClassName(advancedTab.id)} w-full justify-center bg-gradient-to-r from-purple-50 to-blue-50 hover:from-purple-100 hover:to-blue-100 border border-blue-100`}
          onClick={() => onTabChange(advancedTab.id)}
        >
          {advancedTab.icon}
          <span className="text-purple-700">{advancedTab.label}</span>
        </button>
      </div>

      {/* Help & support */}
      <div className="fixed bottom-4 right-4">
        <button
          className="bg-blue-600 hover:bg-blue-700 text-white rounded-full p-2 shadow-lg transition-colors duration-200"
          onClick={() => window.open('/attendance-help', '_blank')}
          aria-label="Help with attendance module"
        >
          <HelpCircle className="h-5 w-5" />
        </button>
      </div>
    </div>
  );
};

export default AttendanceNavigation; 