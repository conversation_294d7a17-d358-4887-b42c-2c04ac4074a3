interface AnimatedStatProps {
  label: string;
  value: string;
  color?: string;
}

export const AnimatedStat: React.FC<AnimatedStatProps> = ({ label, value, color = 'text-gray-900' }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="text-gray-500 text-sm">{label}</div>
      <div className={`text-xl font-semibold ${color}`}>{value}</div>
    </motion.div>
  );
}; 