// Environment configuration
export const config = {
  // API Configuration
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api',
  environment: import.meta.env.VITE_ENVIRONMENT || 'development',
  
  // Authentication
  authEnabled: import.meta.env.VITE_AUTH_ENABLED === 'true',
  tokenStorageKey: import.meta.env.VITE_TOKEN_STORAGE_KEY || 'authToken',
  
  // Features
  enableMockData: import.meta.env.VITE_ENABLE_MOCK_DATA !== 'false', // Default to true for development
  enableLogging: import.meta.env.VITE_ENABLE_LOGGING === 'true',
  enableDebug: import.meta.env.VITE_ENABLE_DEBUG === 'true',
  
  // API Configuration
  apiTimeout: parseInt(import.meta.env.VITE_API_TIMEOUT || '30000'),
  apiRetryAttempts: parseInt(import.meta.env.VITE_API_RETRY_ATTEMPTS || '3'),
  
  // File Upload
  maxFileSize: parseInt(import.meta.env.VITE_MAX_FILE_SIZE || '5242880'), // 5MB
  allowedFileTypes: (import.meta.env.VITE_ALLOWED_FILE_TYPES || 'pdf,doc,docx,jpg,jpeg,png').split(','),
  
  // Pagination
  defaultPageSize: parseInt(import.meta.env.VITE_DEFAULT_PAGE_SIZE || '20'),
  maxPageSize: parseInt(import.meta.env.VITE_MAX_PAGE_SIZE || '100'),
  
  // Development
  isDevelopment: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,
};

// Validate required environment variables
export const validateEnvironment = (): void => {
  // In development, we use default values, so validation is not strictly required
  // This can be extended later if needed for production environments
  if (config.isProduction && !config.apiBaseUrl) {
    throw new Error('API base URL is required in production');
  }
};

export default config; 