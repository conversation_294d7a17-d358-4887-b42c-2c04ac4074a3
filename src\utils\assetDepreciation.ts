/**
 * Utility functions for calculating asset depreciation
 */

/**
 * Calculate straight-line depreciation
 * @param cost Initial cost of the asset
 * @param salvageValue Estimated salvage value at the end of useful life
 * @param usefulLifeYears Estimated useful life in years
 * @param ageInYears Current age of the asset in years
 * @returns Object containing depreciation information
 */
export function calculateStraightLineDepreciation(
  cost: number,
  salvageValue: number,
  usefulLifeYears: number,
  ageInYears: number
) {
  // Annual depreciation amount
  const annualDepreciation = (cost - salvageValue) / usefulLifeYears;
  
  // Total depreciation to date
  const totalDepreciation = Math.min(annualDepreciation * ageInYears, cost - salvageValue);
  
  // Current book value
  const currentValue = cost - totalDepreciation;
  
  // Depreciation rate (annual percentage)
  const depreciationRate = (annualDepreciation / cost) * 100;
  
  // Remaining useful life
  const remainingLifeYears = Math.max(usefulLifeYears - ageInYears, 0);
  
  return {
    annualDepreciation,
    totalDepreciation,
    currentValue,
    depreciationRate,
    remainingLifeYears
  };
}

/**
 * Calculate declining balance depreciation
 * @param cost Initial cost of the asset
 * @param salvageValue Estimated salvage value at the end of useful life
 * @param usefulLifeYears Estimated useful life in years
 * @param ageInYears Current age of the asset in years
 * @param rate Depreciation rate (typically 2 for double declining balance)
 * @returns Object containing depreciation information
 */
export function calculateDecliningBalanceDepreciation(
  cost: number,
  salvageValue: number,
  usefulLifeYears: number,
  ageInYears: number,
  rate: number = 2
) {
  // Depreciation rate
  const depreciationRate = (rate / usefulLifeYears) * 100;
  
  // Calculate current value using declining balance method
  let currentValue = cost;
  let totalDepreciation = 0;
  let annualDepreciation = 0;
  
  // Calculate depreciation for each year up to the current age
  for (let year = 1; year <= Math.min(ageInYears, usefulLifeYears); year++) {
    // Calculate depreciation for this year
    annualDepreciation = (currentValue * depreciationRate) / 100;
    
    // Switch to straight-line if it gives higher depreciation in later years
    const remainingYears = usefulLifeYears - year + 1;
    const straightLineAmount = (currentValue - salvageValue) / remainingYears;
    
    if (straightLineAmount > annualDepreciation) {
      annualDepreciation = straightLineAmount;
    }
    
    // Ensure we don't depreciate below salvage value
    if (currentValue - annualDepreciation < salvageValue) {
      annualDepreciation = currentValue - salvageValue;
    }
    
    // Update current value and total depreciation
    currentValue -= annualDepreciation;
    totalDepreciation += annualDepreciation;
    
    // Stop if we've reached salvage value
    if (currentValue <= salvageValue) {
      currentValue = salvageValue;
      break;
    }
  }
  
  // Remaining useful life
  const remainingLifeYears = Math.max(usefulLifeYears - ageInYears, 0);
  
  return {
    annualDepreciation,
    totalDepreciation,
    currentValue,
    depreciationRate,
    remainingLifeYears
  };
}

/**
 * Calculate units of production depreciation
 * @param cost Initial cost of the asset
 * @param salvageValue Estimated salvage value at the end of useful life
 * @param totalEstimatedUnits Total estimated units to be produced over the asset's life
 * @param unitsProduced Units produced so far
 * @returns Object containing depreciation information
 */
export function calculateUnitsOfProductionDepreciation(
  cost: number,
  salvageValue: number,
  totalEstimatedUnits: number,
  unitsProduced: number
) {
  // Depreciation per unit
  const depreciationPerUnit = (cost - salvageValue) / totalEstimatedUnits;
  
  // Total depreciation to date
  const totalDepreciation = Math.min(depreciationPerUnit * unitsProduced, cost - salvageValue);
  
  // Current book value
  const currentValue = cost - totalDepreciation;
  
  // Percentage of useful life used
  const percentageUsed = (unitsProduced / totalEstimatedUnits) * 100;
  
  // Remaining units
  const remainingUnits = Math.max(totalEstimatedUnits - unitsProduced, 0);
  
  return {
    depreciationPerUnit,
    totalDepreciation,
    currentValue,
    percentageUsed,
    remainingUnits
  };
}

/**
 * Calculate asset age in years from purchase date
 * @param purchaseDate Date when the asset was purchased
 * @returns Age of the asset in years (decimal)
 */
export function calculateAssetAge(purchaseDate: Date): number {
  const today = new Date();
  const diffTime = today.getTime() - purchaseDate.getTime();
  const diffYears = diffTime / (1000 * 60 * 60 * 24 * 365.25);
  return diffYears;
}

/**
 * Determine the default useful life based on asset type
 * @param assetType Type of the asset
 * @param category Category of the asset
 * @returns Estimated useful life in years
 */
export function getDefaultUsefulLife(assetType: string, category: string): number {
  // Default useful life values based on asset type and category
  const usefulLifeMap: Record<string, Record<string, number>> = {
    'Computing': {
      'Laptop': 3,
      'Desktop': 5,
      'Workstation': 5,
      'Server': 5,
      'Thin Client': 4,
      'Exchange Server': 5,
      'Storage Server': 5,
      'Other': 4
    },
    'Mobile Device': {
      'Smartphone': 2,
      'Other': 2
    },
    'Tablet': {
      'Android Tablet': 3,
      'iOS Tablet': 3,
      'Other': 3
    },
    'Networking': {
      'Router': 5,
      'Switch': 5,
      'Firewall': 5,
      'Load Balancer': 5,
      'Wireless Access Point': 4,
      'Modem': 3,
      'Other': 4
    },
    'Peripherals': {
      'Keyboard': 3,
      'Mouse': 2,
      'Docking Station': 4,
      'Projector': 5,
      'External Storage': 3,
      'Other': 3
    },
    'TV': {
      'Smart TV': 7,
      'LED TV': 7,
      'OLED TV': 7,
      'Other': 7
    },
    'LCD': {
      'LCD Monitor': 5,
      'Touchscreen Monitor': 5,
      'Other': 5
    },
    'Printer': {
      'Inkjet Printer': 4,
      'Laser Printer': 5,
      'Dot Matrix Printer': 7,
      'Other': 5
    },
    'Scanner': {
      'Flatbed Scanner': 5,
      'Sheet-fed Scanner': 5,
      'Handheld Scanner': 4,
      'Other': 5
    },
    'Security Equipment': {
      'Biometric Device': 7,
      'RFID Reader': 7,
      'Security Alarm': 10,
      'Other': 7
    },
    'Communication Equipment': {
      'IP Phone': 5,
      'VoIP Gateway': 5,
      'PBX System': 7,
      'Conference System': 5,
      'Intercom System': 7,
      'Other': 6
    },
    'Camera': {
      'IP Camera': 5,
      'Analog Camera': 5,
      'Other': 5
    },
    'DVR': {
      'HD DVR': 5,
      'SD DVR': 5,
      'Other': 5
    },
    'NVR': {
      'Network Video Recorder': 5,
      'Other': 5
    },
    'LED': {
      'LED Display': 7,
      'Other': 7
    }
  };

  // Get the useful life from the map, or use a default value
  return usefulLifeMap[assetType]?.[category] || 5;
} 