import React, { useMemo, useState } from 'react';
import { 
  User, Clock, XCircle, AlertTriangle, 
  CheckCircle, Activity, ArrowLeft, LogIn, LogOut, Hourglass,
  ListFilter, Bar<PERSON>hart, History
} from 'lucide-react';
import { SystemLog } from './types';
import { formatSystemDate } from './utils/dateFormat';
import { 
  extractUserLoginInfo, 
  UserLoginInfo,
  UserSession, 
  isSuccessfulLogin, 
  isFailedLogin, 
  isLogout
} from './utils/loginTracker';
import { UserActivityLog } from './UserActivityLog';
import { UserActivityTimeline } from './UserActivityTimeline';

interface UserLogViewProps {
  logs: SystemLog[];
  username: string;
  onBack: () => void;
  onViewLogDetails: (log: SystemLog) => void;
}

export const UserLogView: React.FC<UserLogViewProps> = ({ 
  logs,
  username,
  onBack,
  onViewLogDetails
}) => {
  // State for view toggle
  const [activeView, setActiveView] = useState<'summary' | 'sessions' | 'all-activity' | 'timeline'>('summary');
  
  // Filter logs for this user
  const userLogs = useMemo(() => {
    return logs.filter(log => log.user === username);
  }, [logs, username]);

  // Calculate user statistics
  const loginInfo = useMemo<UserLoginInfo | undefined>(() => {
    const userMap = extractUserLoginInfo(logs);
    return userMap.get(username);
  }, [logs, username]);

  // Sort user logs by timestamp (newest first)
  const sortedLogs = useMemo(() => {
    return [...userLogs].sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  }, [userLogs]);
  
  // Get the most active day
  const mostActiveDay = useMemo(() => {
    const dayCount = new Map<string, number>();
    
    userLogs.forEach(log => {
      const date = new Date(log.timestamp);
      const day = date.toISOString().split('T')[0];
      dayCount.set(day, (dayCount.get(day) || 0) + 1);
    });
    
    let maxCount = 0;
    let maxDay = '';
    
    dayCount.forEach((count, day) => {
      if (count > maxCount) {
        maxCount = count;
        maxDay = day;
      }
    });
    
    return { date: maxDay, count: maxCount };
  }, [userLogs]);
  
  // Get activity counts by type
  const activityStats = useMemo(() => {
    const stats = {
      total: userLogs.length,
      types: {
        error: 0,
        warning: 0,
        info: 0,
        success: 0
      },
      activities: {
        login: 0,
        logout: 0,
        failed: 0,
        other: 0
      }
    };

    userLogs.forEach(log => {
      // Count by type
      stats.types[log.type as keyof typeof stats.types]++;
      
      // Count by activity
      if (isSuccessfulLogin(log)) {
        stats.activities.login++;
      } else if (isFailedLogin(log)) {
        stats.activities.failed++;
      } else if (isLogout(log)) {
        stats.activities.logout++;
      } else {
        stats.activities.other++;
      }
    });

    return stats;
  }, [userLogs]);

  // Sort sessions by login time (newest first)
  const sortedSessions = useMemo(() => {
    if (!loginInfo || !loginInfo.sessions) return [];
    return [...loginInfo.sessions].sort((a, b) => 
      new Date(b.loginTime).getTime() - new Date(a.loginTime).getTime()
    );
  }, [loginInfo]);
  
  // Format duration to hours and minutes
  const formatDuration = (minutes: number | null) => {
    if (minutes === null) return 'Session active';
    if (minutes < 1) return 'Less than a minute';
    if (minutes < 60) return `${minutes} minutes`;
    
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (remainingMinutes === 0) {
      return hours === 1 ? '1 hour' : `${hours} hours`;
    }
    
    return `${hours} hour${hours !== 1 ? 's' : ''} ${remainingMinutes} minute${remainingMinutes !== 1 ? 's' : ''}`;
  };

  // Render different content based on active view
  const renderContent = () => {
    switch (activeView) {
      case 'summary':
        return (
          <>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 flex flex-col items-center justify-center border border-blue-100 dark:border-blue-800/50">
                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">{activityStats.total}</div>
                <div className="text-sm text-blue-800 dark:text-blue-300">Total Activities</div>
              </div>
              
              <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 flex flex-col items-center justify-center border border-green-100 dark:border-green-800/50">
                <div className="text-3xl font-bold text-green-600 dark:text-green-400">{activityStats.activities.login}</div>
                <div className="text-sm text-green-800 dark:text-green-300">Successful Logins</div>
              </div>
              
              <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 flex flex-col items-center justify-center border border-red-100 dark:border-red-800/50">
                <div className="text-3xl font-bold text-red-600 dark:text-red-400">{activityStats.activities.failed}</div>
                <div className="text-sm text-red-800 dark:text-red-300">Failed Login Attempts</div>
              </div>
              
              <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 flex flex-col items-center justify-center border border-purple-100 dark:border-purple-800/50">
                <div className="text-3xl font-bold text-purple-600 dark:text-purple-400">
                  {loginInfo?.averageSessionDuration ? `${loginInfo.averageSessionDuration}` : '-'}
                </div>
                <div className="text-sm text-purple-800 dark:text-purple-300">Avg. Session (min)</div>
              </div>
            </div>
            
            {/* Recent Activity */}
            <div>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-3 flex items-center gap-2">
                <Activity className="h-5 w-5 text-blue-500" />
                Recent Activity
              </h3>
              
              <div className="space-y-3">
                {sortedLogs.slice(0, 10).map(log => (
                  <div 
                    key={log.id}
                    className="p-3 bg-gray-50 dark:bg-gray-800/70 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700/50 cursor-pointer transition-colors"
                    onClick={() => onViewLogDetails(log)}
                  >
                    <div className="flex items-center gap-2 mb-1">
                      {log.type === 'success' && <CheckCircle className="h-4 w-4 text-green-500" />}
                      {log.type === 'error' && <XCircle className="h-4 w-4 text-red-500" />}
                      {log.type === 'warning' && <AlertTriangle className="h-4 w-4 text-yellow-500" />}
                      {log.type === 'info' && <Activity className="h-4 w-4 text-blue-500" />}
                      
                      <span className="font-medium text-gray-800 dark:text-gray-200">{log.action}</span>
                      
                      <span className="text-xs text-gray-500 dark:text-gray-400 ml-auto">
                        {formatSystemDate(log.timestamp)}
                      </span>
                    </div>
                    
                    <div className="text-sm text-gray-600 dark:text-gray-400 line-clamp-1">
                      {log.details}
                    </div>
                  </div>
                ))}
                
                {sortedLogs.length === 0 && (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                    No activity recorded for this user.
                  </div>
                )}
              </div>
            </div>
          </>
        );

      case 'sessions':
        return (
          <div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-3 flex items-center gap-2">
              <Clock className="h-5 w-5 text-blue-500" />
              Login Sessions
            </h3>
            
            {sortedSessions.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Login Time
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Logout Time
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Duration
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        IP Address
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {sortedSessions.map((session, index) => (
                      <tr 
                        key={`session-${index}`}
                        className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                      >
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">
                          <div className="flex items-center gap-1.5">
                            <LogIn className="h-4 w-4 text-green-500" />
                            {formatSystemDate(session.loginTime)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">
                          {session.logoutTime ? (
                            <div className="flex items-center gap-1.5">
                              <LogOut className="h-4 w-4 text-red-500" />
                              {formatSystemDate(session.logoutTime)}
                            </div>
                          ) : (
                            <div className="flex items-center gap-1.5">
                              <Hourglass className="h-4 w-4 text-blue-500" />
                              <span className="text-blue-600 dark:text-blue-400">Session Active</span>
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">
                          {formatDuration(session.duration)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-400 font-mono">
                          {session.ipAddress || 'Unknown'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                No login sessions recorded for this user.
              </div>
            )}
          </div>
        );

      case 'all-activity':
        return (
          <UserActivityLog 
            logs={logs} 
            username={username} 
            onViewLogDetails={onViewLogDetails} 
          />
        );
        
      case 'timeline':
        return (
          <UserActivityTimeline
            logs={logs}
            username={username}
            onViewLogDetails={onViewLogDetails}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <button
          onClick={onBack}
          className="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          <span>Back to All Logs</span>
        </button>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 flex items-center gap-2 mb-6">
          <User className="h-5 w-5 text-blue-500" />
          User Activity: {username}
        </h2>

        {/* View switcher tabs */}
        <div className="flex flex-wrap border-b border-gray-200 dark:border-gray-700 mb-6">
          <button
            onClick={() => setActiveView('summary')}
            className={`px-4 py-2 text-sm font-medium border-b-2 focus:outline-none ${
              activeView === 'summary'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
              <div className="flex items-center gap-1.5">
              <BarChart className="h-4 w-4" />
              Activity Summary
            </div>
          </button>
          <button
            onClick={() => setActiveView('timeline')}
            className={`px-4 py-2 text-sm font-medium border-b-2 focus:outline-none ${
              activeView === 'timeline'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
              <div className="flex items-center gap-1.5">
              <History className="h-4 w-4" />
              Visual Timeline
            </div>
          </button>
          <button
            onClick={() => setActiveView('sessions')}
            className={`px-4 py-2 text-sm font-medium border-b-2 focus:outline-none ${
              activeView === 'sessions'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
              <div className="flex items-center gap-1.5">
              <Clock className="h-4 w-4" />
              Login Sessions
            </div>
          </button>
          <button
            onClick={() => setActiveView('all-activity')}
            className={`px-4 py-2 text-sm font-medium border-b-2 focus:outline-none ${
              activeView === 'all-activity'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
              <div className="flex items-center gap-1.5">
              <ListFilter className="h-4 w-4" />
              All Activity Logs
            </div>
          </button>
        </div>
        
        {/* Display the selected view content */}
        {renderContent()}
      </div>
    </div>
  );
}; 