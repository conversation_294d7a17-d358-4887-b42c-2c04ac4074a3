import React, { useMemo } from 'react';
import { User, Activity, CheckCircle, AlertTriangle, XCircle } from 'lucide-react';
import { SystemLog } from './types';

interface UserActivityChartProps {
  logs: SystemLog[];
  onUserSelect: (username: string) => void;
}

export const UserActivityChart: React.FC<UserActivityChartProps> = ({ logs, onUserSelect }) => {
  // Calculate user activity statistics
  const userStats = useMemo(() => {
    const users = new Map<string, {
      count: number;
      types: {
        info: number;
        success: number;
        warning: number;
        error: number;
      };
      lastActivity: Date;
    }>();

    // Collect stats from logs
    logs.forEach(log => {
      const username = log.user;
      const timestamp = new Date(log.timestamp);
      
      if (!users.has(username)) {
        users.set(username, {
          count: 0,
          types: { info: 0, success: 0, warning: 0, error: 0 },
          lastActivity: timestamp
        });
      }
      
      const userStat = users.get(username)!;
      userStat.count++;
      userStat.types[log.type]++;
      
      if (timestamp > userStat.lastActivity) {
        userStat.lastActivity = timestamp;
      }
    });
    
    // Convert to array and sort by activity count (most active first)
    return Array.from(users.entries())
      .map(([username, stats]) => ({ username, ...stats }))
      .sort((a, b) => b.count - a.count);
  }, [logs]);
  
  // Get relative time (e.g., "5 minutes ago")
  const getRelativeTime = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffSecs < 60) return 'just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return date.toLocaleDateString();
  };
  
  // Get icon based on most common log type
  const getUserIcon = (stats: { types: { info: number; success: number; warning: number; error: number } }) => {
    const { info, success, warning, error } = stats.types;
    const max = Math.max(info, success, warning, error);
    
    if (max === 0) return <User className="h-4 w-4 text-gray-400" />;
    
    if (error === max) return <XCircle className="h-4 w-4 text-red-500" />;
    if (warning === max) return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    if (success === max) return <CheckCircle className="h-4 w-4 text-green-500" />;
    return <Activity className="h-4 w-4 text-blue-500" />;
  };
  
  // Display at most 5 users
  const displayUsers = userStats.slice(0, 5);
  
  if (displayUsers.length === 0) {
    return null;
  }
  
  return (
    <div className="mb-3 w-full">
      <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">User Activity</h3>
      <div className="grid grid-cols-1 sm:grid-cols-5 gap-3">
        {displayUsers.map(user => (
          <div 
            key={user.username} 
            className="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm hover:shadow transition-shadow duration-200 border border-gray-100 dark:border-gray-700 cursor-pointer"
            onClick={() => onUserSelect(user.username)}
          >
            <div className="flex items-center justify-between mb-1">
              <div className="flex items-center gap-1.5">
                {getUserIcon(user)}
                <span className="font-medium text-sm truncate" title={user.username}>
                  {user.username}
                </span>
              </div>
              <span className="font-bold text-sm">{user.count}</span>
            </div>
            
            <div className="mt-2">
              <div className="h-1.5 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                {user.count > 0 && (
                  <>
                    <div 
                      className="h-full bg-blue-500 float-left" 
                      style={{ width: `${(user.types.info / user.count) * 100}%` }}
                      title={`Info: ${user.types.info}`}
                    />
                    <div 
                      className="h-full bg-green-500 float-left" 
                      style={{ width: `${(user.types.success / user.count) * 100}%` }}
                      title={`Success: ${user.types.success}`}
                    />
                    <div 
                      className="h-full bg-yellow-500 float-left" 
                      style={{ width: `${(user.types.warning / user.count) * 100}%` }}
                      title={`Warning: ${user.types.warning}`}
                    />
                    <div 
                      className="h-full bg-red-500 float-left" 
                      style={{ width: `${(user.types.error / user.count) * 100}%` }}
                      title={`Error: ${user.types.error}`}
                    />
                  </>
                )}
              </div>
              <div className="flex justify-between mt-1 text-xs text-gray-500 dark:text-gray-400">
                <span>{getRelativeTime(user.lastActivity)}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};