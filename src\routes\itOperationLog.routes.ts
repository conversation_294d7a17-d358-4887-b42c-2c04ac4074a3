import express from 'express';
import { Router } from 'express';
import ITOperationLogController from '../controllers/ITOperationLogController';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { requireAuth } from '../middleware/auth';

// Create router
const router: Router = express.Router();

// Use a more specific name to avoid conflicts
const itLogsUploadsDir = path.join(process.cwd(), 'public', 'uploads', 'it-logs');
if (!fs.existsSync(itLogsUploadsDir)) {
  fs.mkdirSync(itLogsUploadsDir, { recursive: true });
  console.log('Created IT logs upload directory:', itLogsUploadsDir);
}

// Configure multer storage with simple option values
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, itLogsUploadsDir);
  },
  filename: function (req, file, cb) {
    // Create a simple filename with timestamp and original extension
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'it-log-' + uniqueSuffix + ext);
  }
});

// Create a simple multer upload instance for the main routes
const upload = multer({ 
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB
});

// Create a separate upload instance just for the standalone upload route
const singleUpload = multer({ 
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB
}).single('attachment');

// Disable express-fileupload for all routes in this router
router.use((req: any, res: any, next: any) => {
  // Disable express-fileupload for this route
  if (req.files) {
    console.log('Disabling express-fileupload middleware for IT logs routes');
    delete req.files;
  }
  next();
});

// Authentication middleware
router.use(requireAuth);

// Auth protected routes
// Get all operation logs with filtering and pagination
router.get('/', ITOperationLogController.getAllLogs);

// Get operation log statistics
router.get('/statistics', ITOperationLogController.getStatistics);

// Get a single operation log by ID
router.get('/:id', ITOperationLogController.getLogById);

// Create a new operation log
router.post('/', upload.single('attachment'), ITOperationLogController.createLog);

// Update an existing operation log
router.put('/:id', upload.single('attachment'), ITOperationLogController.updateLog);

// Delete an operation log
router.delete('/:id', ITOperationLogController.deleteLog);

// Add a debug endpoint
router.post('/debug', (req, res) => {
  console.log('Debug log received:', req.body);
  res.status(200).json({ success: true, message: 'Debug log received' });
});

// Simplified standalone upload endpoint
router.post('/upload', (req, res) => {
  console.log('File upload request received');

  // The simplest possible implementation
  singleUpload(req, res, function(err) {
    if (err) {
      console.error('Multer error:', err);
      return res.status(500).json({
        success: false,
        message: 'File upload failed',
        error: err.message
      });
    }

    // Check if we received a file
    if (!req.file) {
      console.error('No file received');
      return res.status(400).json({
        success: false,
        message: 'No file received'
      });
    }

    console.log('File uploaded successfully:', req.file);
    
    // Return the file info
    return res.status(200).json({
      success: true,
      data: {
        filename: req.file.filename,
        originalname: req.file.originalname,
        path: `/uploads/it-logs/${req.file.filename}`,
        size: req.file.size,
        mimetype: req.file.mimetype
      }
    });
  });
});

export default router; 