import { Router } from 'express';
import { HolidayController } from '../controllers/HolidayController';

const router = Router();
const holidayController = new HolidayController();

// Holiday CRUD routes
router.get('/', (req, res) => holidayController.getHolidays(req, res));
router.get('/upcoming', (req, res) => holidayController.getUpcomingHolidays(req, res));
router.get('/config', (req, res) => holidayController.getHolidayConfiguration(req, res));
router.get('/:id', (req, res) => holidayController.getHoliday(req, res));

router.post('/', (req, res) => holidayController.createHoliday(req, res));
router.post('/import', (req, res) => holidayController.importHolidays(req, res));
router.post('/config', (req, res) => holidayController.saveHolidayConfiguration(req, res));

router.put('/:id', (req, res) => holidayController.updateHoliday(req, res));
router.patch('/:id/toggle-status', (req, res) => holidayController.toggleHolidayStatus(req, res));

router.delete('/:id', (req, res) => holidayController.deleteHoliday(req, res));

export default router; 