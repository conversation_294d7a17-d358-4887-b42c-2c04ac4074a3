import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { IsNotEmpty, IsOptional, IsNumber, IsDateString } from 'class-validator';
import { Task } from './Task';
import { User } from './User';

export enum TimeEntryType {
  MANUAL = 'manual',
  TIMER = 'timer',
  IMPORTED = 'imported'
}

@Entity('task_time_entries')
export class TaskTimeEntry {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int' })
  @IsNotEmpty({ message: 'Duration is required' })
  @IsNumber({}, { message: 'Duration must be a number' })
  durationMinutes: number;

  @Column({ type: 'timestamp' })
  @IsNotEmpty({ message: 'Start time is required' })
  startTime: Date;

  @Column({ type: 'timestamp', nullable: true })
  @IsOptional()
  endTime: Date;

  @Column({ type: 'text', nullable: true })
  @IsOptional()
  description: string;

  @Column({
    type: 'enum',
    enum: TimeEntryType,
    default: TimeEntryType.MANUAL
  })
  type: TimeEntryType;

  @Column({ type: 'boolean', default: false })
  isBillable: boolean;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  @IsOptional()
  @IsNumber({}, { message: 'Hourly rate must be a number' })
  hourlyRate: number;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  // Relations
  @ManyToOne(() => Task, task => task.timeEntries, { nullable: false })
  @JoinColumn({ name: 'taskId' })
  task: Task;

  @Column({ type: 'int' })
  taskId: number;

  @ManyToOne(() => User, { nullable: false })
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ type: 'uuid' })
  userId: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;
}
