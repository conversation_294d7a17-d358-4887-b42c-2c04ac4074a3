import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>rid,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  IconButton,
  Menu,
  MenuItem,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  LinearProgress
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Edit as EditIcon,
  Publish as PublishIcon,
  Close as CloseIcon,
  Delete as DeleteIcon,
  Share as ShareIcon,
  MoreVert as MoreVertIcon,
  People as PeopleIcon,
  Visibility as VisibilityIcon,
  TrendingUp as TrendingUpIcon,
  LocationOn as LocationIcon,
  Work as WorkIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { recruitmentAPI } from '../../../../services/recruitmentAPI';

interface JobPosting {
  id: number;
  title: string;
  description: string;
  requirements: string;
  responsibilities?: string;
  benefits?: string;
  department: string;
  location: string;
  jobType: string;
  experienceLevel: string;
  workLocation: string;
  status: string;
  minSalary?: number;
  maxSalary?: number;
  salaryCurrency?: string;
  numberOfPositions?: number;
  applicationDeadline?: string;
  requiredSkills?: string[];
  preferredSkills?: string[];
  isActive: boolean;
  isUrgent: boolean;
  isFeatured: boolean;
  viewCount: number;
  applicationCount: number;
  createdAt: string;
  publishedAt?: string;
  createdBy: {
    id: string;
    firstName: string;
    lastName: string;
  };
  hiringManager?: {
    id: string;
    firstName: string;
    lastName: string;
  };
  applications?: Array<{
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    status: string;
    rating: number;
    createdAt: string;
  }>;
}

const JobPostingDetails: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [jobPosting, setJobPosting] = useState<JobPosting | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  useEffect(() => {
    if (id) {
      fetchJobPosting();
    }
  }, [id]);

  const fetchJobPosting = async () => {
    try {
      setLoading(true);
      const response = await recruitmentAPI.getJobPostingById(Number(id));
      setJobPosting(response.data.jobPosting);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to fetch job posting');
    } finally {
      setLoading(false);
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handlePublish = async () => {
    if (!jobPosting) return;
    try {
      await recruitmentAPI.publishJobPosting(jobPosting.id);
      await fetchJobPosting();
      handleMenuClose();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to publish job posting');
    }
  };

  const handleClose = async () => {
    if (!jobPosting) return;
    try {
      await recruitmentAPI.closeJobPosting(jobPosting.id);
      await fetchJobPosting();
      handleMenuClose();
    } catch (err: any) {
      setError(err.response?.data?.error || 'Failed to close job posting');
    }
  };

  const getStatusColor = (status: string) => {
    const statusColors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      draft: 'default',
      published: 'success',
      paused: 'warning',
      closed: 'error',
      cancelled: 'error'
    };
    return statusColors[status] || 'default';
  };

  const getApplicationStatusColor = (status: string) => {
    const statusColors: Record<string, 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning'> = {
      submitted: 'info',
      under_review: 'warning',
      interviewing: 'primary',
      hired: 'success',
      rejected: 'error'
    };
    return statusColors[status] || 'default';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const isExpired = (deadline?: string) => {
    if (!deadline) return false;
    return new Date(deadline) < new Date();
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error || !jobPosting) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error || 'Job posting not found'}
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="between" alignItems="center" mb={3}>
        <Box display="flex" alignItems="center">
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/hr/recruitment/job-postings')}
            sx={{ mr: 2 }}
          >
            Back to Job Postings
          </Button>
          <Typography variant="h4" component="h1">
            Job Posting Details
          </Typography>
        </Box>
        <Box>
          <Button
            variant="outlined"
            startIcon={<EditIcon />}
            onClick={() => navigate(`/hr/recruitment/job-postings/${jobPosting.id}/edit`)}
            sx={{ mr: 1 }}
          >
            Edit
          </Button>
          <IconButton onClick={handleMenuClick}>
            <MoreVertIcon />
          </IconButton>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Job Information */}
        <Grid item xs={12} md={8}>
          {/* Basic Info */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box display="flex" justifyContent="between" alignItems="start" mb={2}>
                <Box>
                  <Typography variant="h4" gutterBottom>
                    {jobPosting.title}
                    {jobPosting.isUrgent && (
                      <Chip label="Urgent" color="error" size="small" sx={{ ml: 1 }} />
                    )}
                    {jobPosting.isFeatured && (
                      <Chip label="Featured" color="primary" size="small" sx={{ ml: 1 }} />
                    )}
                  </Typography>
                  <Box display="flex" alignItems="center" gap={2} mb={1}>
                    <Box display="flex" alignItems="center">
                      <WorkIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} />
                      <Typography variant="body2">{jobPosting.department}</Typography>
                    </Box>
                    <Box display="flex" alignItems="center">
                      <LocationIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} />
                      <Typography variant="body2">{jobPosting.location}</Typography>
                    </Box>
                    <Box display="flex" alignItems="center">
                      <ScheduleIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        {jobPosting.jobType.replace(/_/g, ' ')} • {jobPosting.workLocation}
                      </Typography>
                    </Box>
                  </Box>
                  <Typography variant="body2" color="textSecondary">
                    {jobPosting.experienceLevel.replace(/_/g, ' ')} level
                  </Typography>
                </Box>
                <Chip
                  label={jobPosting.status.replace(/_/g, ' ').toUpperCase()}
                  color={getStatusColor(jobPosting.status)}
                />
              </Box>

              <Divider sx={{ my: 2 }} />

              <Typography variant="h6" gutterBottom>
                Job Description
              </Typography>
              <Typography variant="body1" sx={{ mb: 3, whiteSpace: 'pre-wrap' }}>
                {jobPosting.description}
              </Typography>

              <Typography variant="h6" gutterBottom>
                Requirements
              </Typography>
              <Typography variant="body1" sx={{ mb: 3, whiteSpace: 'pre-wrap' }}>
                {jobPosting.requirements}
              </Typography>

              {jobPosting.responsibilities && (
                <>
                  <Typography variant="h6" gutterBottom>
                    Responsibilities
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 3, whiteSpace: 'pre-wrap' }}>
                    {jobPosting.responsibilities}
                  </Typography>
                </>
              )}

              {jobPosting.benefits && (
                <>
                  <Typography variant="h6" gutterBottom>
                    Benefits
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 3, whiteSpace: 'pre-wrap' }}>
                    {jobPosting.benefits}
                  </Typography>
                </>
              )}

              {/* Skills */}
              {(jobPosting.requiredSkills?.length || jobPosting.preferredSkills?.length) && (
                <>
                  <Divider sx={{ my: 2 }} />
                  {jobPosting.requiredSkills && jobPosting.requiredSkills.length > 0 && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="h6" gutterBottom>
                        Required Skills
                      </Typography>
                      <Box>
                        {jobPosting.requiredSkills.map((skill, index) => (
                          <Chip
                            key={index}
                            label={skill}
                            color="primary"
                            variant="outlined"
                            sx={{ mr: 1, mb: 1 }}
                          />
                        ))}
                      </Box>
                    </Box>
                  )}
                  {jobPosting.preferredSkills && jobPosting.preferredSkills.length > 0 && (
                    <Box>
                      <Typography variant="h6" gutterBottom>
                        Preferred Skills
                      </Typography>
                      <Box>
                        {jobPosting.preferredSkills.map((skill, index) => (
                          <Chip
                            key={index}
                            label={skill}
                            variant="outlined"
                            sx={{ mr: 1, mb: 1 }}
                          />
                        ))}
                      </Box>
                    </Box>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          {/* Statistics */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Statistics
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <Box display="flex" justifyContent="between" alignItems="center" mb={1}>
                  <Typography variant="body2">Applications</Typography>
                  <Typography variant="h6" color="primary">
                    {jobPosting.applicationCount}
                  </Typography>
                </Box>
                <Box display="flex" justifyContent="between" alignItems="center" mb={1}>
                  <Typography variant="body2">Views</Typography>
                  <Typography variant="h6">
                    {jobPosting.viewCount}
                  </Typography>
                </Box>
                <Box display="flex" justifyContent="between" alignItems="center">
                  <Typography variant="body2">Conversion Rate</Typography>
                  <Typography variant="h6">
                    {jobPosting.viewCount > 0 
                      ? ((jobPosting.applicationCount / jobPosting.viewCount) * 100).toFixed(1)
                      : 0}%
                  </Typography>
                </Box>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle2" gutterBottom>
                Details
              </Typography>
              <Typography variant="body2" sx={{ mb: 1 }}>
                <strong>Created:</strong> {formatDate(jobPosting.createdAt)}
              </Typography>
              {jobPosting.publishedAt && (
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Published:</strong> {formatDate(jobPosting.publishedAt)}
                </Typography>
              )}
              {jobPosting.applicationDeadline && (
                <Typography 
                  variant="body2" 
                  sx={{ mb: 1 }}
                  color={isExpired(jobPosting.applicationDeadline) ? 'error' : 'textPrimary'}
                >
                  <strong>Deadline:</strong> {formatDate(jobPosting.applicationDeadline)}
                  {isExpired(jobPosting.applicationDeadline) && ' (Expired)'}
                </Typography>
              )}
              {jobPosting.numberOfPositions && (
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Positions:</strong> {jobPosting.numberOfPositions}
                </Typography>
              )}
              {(jobPosting.minSalary || jobPosting.maxSalary) && (
                <Typography variant="body2">
                  <strong>Salary:</strong> {jobPosting.salaryCurrency || '$'}
                  {jobPosting.minSalary?.toLocaleString()} - {jobPosting.salaryCurrency || '$'}
                  {jobPosting.maxSalary?.toLocaleString()}
                </Typography>
              )}
            </CardContent>
          </Card>

          {/* Team */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Team
              </Typography>
              
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                  {getInitials(jobPosting.createdBy.firstName, jobPosting.createdBy.lastName)}
                </Avatar>
                <Box>
                  <Typography variant="subtitle2">
                    {jobPosting.createdBy.firstName} {jobPosting.createdBy.lastName}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    Created by
                  </Typography>
                </Box>
              </Box>

              {jobPosting.hiringManager && (
                <Box display="flex" alignItems="center">
                  <Avatar sx={{ mr: 2, bgcolor: 'secondary.main' }}>
                    {getInitials(jobPosting.hiringManager.firstName, jobPosting.hiringManager.lastName)}
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle2">
                      {jobPosting.hiringManager.firstName} {jobPosting.hiringManager.lastName}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      Hiring Manager
                    </Typography>
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              
              <Button
                fullWidth
                variant="outlined"
                startIcon={<PeopleIcon />}
                onClick={() => navigate(`/hr/recruitment/applications?jobPostingId=${jobPosting.id}`)}
                sx={{ mb: 1 }}
              >
                View Applications ({jobPosting.applicationCount})
              </Button>
              
              <Button
                fullWidth
                variant="outlined"
                startIcon={<ShareIcon />}
                sx={{ mb: 1 }}
              >
                Share Job Posting
              </Button>
              
              <Button
                fullWidth
                variant="outlined"
                startIcon={<TrendingUpIcon />}
                onClick={() => navigate(`/hr/recruitment/job-postings/${jobPosting.id}/analytics`)}
              >
                View Analytics
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Applications */}
        {jobPosting.applications && jobPosting.applications.length > 0 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Recent Applications
                </Typography>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Candidate</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Rating</TableCell>
                        <TableCell>Applied</TableCell>
                        <TableCell align="right">Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {jobPosting.applications.slice(0, 5).map((application) => (
                        <TableRow key={application.id} hover>
                          <TableCell>
                            <Box display="flex" alignItems="center">
                              <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                                {getInitials(application.firstName, application.lastName)}
                              </Avatar>
                              <Box>
                                <Typography variant="subtitle2">
                                  {application.firstName} {application.lastName}
                                </Typography>
                                <Typography variant="body2" color="textSecondary">
                                  {application.email}
                                </Typography>
                              </Box>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={application.status.replace(/_/g, ' ').toUpperCase()}
                              color={getApplicationStatusColor(application.status)}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Box display="flex" alignItems="center">
                              {[...Array(5)].map((_, i) => (
                                <Box
                                  key={i}
                                  sx={{
                                    width: 8,
                                    height: 8,
                                    borderRadius: '50%',
                                    bgcolor: i < application.rating ? 'warning.main' : 'grey.300',
                                    mr: 0.5
                                  }}
                                />
                              ))}
                            </Box>
                          </TableCell>
                          <TableCell>{formatDate(application.createdAt)}</TableCell>
                          <TableCell align="right">
                            <Button
                              size="small"
                              startIcon={<VisibilityIcon />}
                              onClick={() => navigate(`/hr/recruitment/applications/${application.id}`)}
                            >
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
                {jobPosting.applications.length > 5 && (
                  <Box sx={{ mt: 2, textAlign: 'center' }}>
                    <Button
                      variant="outlined"
                      onClick={() => navigate(`/hr/recruitment/applications?jobPostingId=${jobPosting.id}`)}
                    >
                      View All Applications ({jobPosting.applicationCount})
                    </Button>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {jobPosting.status === 'draft' && (
          <MenuItem onClick={handlePublish}>
            <PublishIcon sx={{ mr: 1 }} />
            Publish
          </MenuItem>
        )}
        {jobPosting.status === 'published' && (
          <MenuItem onClick={handleClose}>
            <CloseIcon sx={{ mr: 1 }} />
            Close
          </MenuItem>
        )}
        <MenuItem onClick={() => navigate(`/hr/recruitment/job-postings/${jobPosting.id}/edit`)}>
          <EditIcon sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ShareIcon sx={{ mr: 1 }} />
          Share
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default JobPostingDetails;
