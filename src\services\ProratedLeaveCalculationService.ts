import { AppDataSource } from '../config/database';
import { LeaveTypePolicy } from '../entities/LeaveTypePolicy';
import { Employee } from '../server/entities/Employee';
import { LeaveAllocation } from '../entities/LeaveAllocation';

export interface ProratedLeaveCalculation {
  leaveTypeId: number;
  leaveType: string;
  displayName: string;
  originalEntitlement: number;
  proratedEntitlement: number;
  prorationFactor: number;
  calculationMethod: string;
  joiningDate: Date;
  leaveYearStart: Date;
  leaveYearEnd: Date;
}

export class ProratedLeaveCalculationService {
  private leaveTypePolicyRepository = AppDataSource.getRepository(LeaveTypePolicy);
  private employeeRepository = AppDataSource.getRepository(Employee);
  private leaveAllocationRepository = AppDataSource.getRepository(LeaveAllocation);

  /**
   * Calculate prorated leave entitlements for an employee
   */
  async calculateProratedLeaveForEmployee(employeeId: number): Promise<ProratedLeaveCalculation[]> {
    try {
      // Get employee details with job information
      const employee = await this.employeeRepository.findOne({
        where: { id: employeeId },
        relations: ['job']
      });

      if (!employee) {
        throw new Error('Employee not found');
      }

      if (!employee.job?.joinDate) {
        throw new Error('Employee joining date not found');
      }

      // Get all active leave types with prorated leave enabled
      const leaveTypes = await this.leaveTypePolicyRepository.find({
        where: { isActive: true }
      });

      const proratedLeaveTypes = leaveTypes.filter(lt => 
        lt.settings && lt.settings.enableProratedLeave === true
      );

      const calculations: ProratedLeaveCalculation[] = [];

      for (const leaveType of proratedLeaveTypes) {
        const calculation = await this.calculateProratedEntitlement(
          new Date(employee.job.joinDate),
          leaveType
        );
        calculations.push(calculation);
      }

      return calculations;
    } catch (error) {
      console.error('Error calculating prorated leave:', error);
      throw error;
    }
  }

  /**
   * Calculate prorated entitlement for a specific leave type
   */
  private async calculateProratedEntitlement(
    joiningDate: Date,
    leaveType: LeaveTypePolicy
  ): Promise<ProratedLeaveCalculation> {
    const currentYear = new Date().getFullYear();
    const leaveYearStart = new Date(currentYear, 0, 1); // January 1st
    const leaveYearEnd = new Date(currentYear, 11, 31); // December 31st

    // If employee joined before the current leave year, give full entitlement
    if (joiningDate <= leaveYearStart) {
      return {
        leaveTypeId: leaveType.id,
        leaveType: leaveType.leaveType,
        displayName: leaveType.displayName,
        originalEntitlement: leaveType.maxDaysPerYear,
        proratedEntitlement: leaveType.maxDaysPerYear,
        prorationFactor: 1.0,
        calculationMethod: 'Full entitlement (joined before leave year)',
        joiningDate,
        leaveYearStart,
        leaveYearEnd
      };
    }

    // If employee joined after the leave year, no entitlement
    if (joiningDate > leaveYearEnd) {
      return {
        leaveTypeId: leaveType.id,
        leaveType: leaveType.leaveType,
        displayName: leaveType.displayName,
        originalEntitlement: leaveType.maxDaysPerYear,
        proratedEntitlement: 0,
        prorationFactor: 0,
        calculationMethod: 'No entitlement (joined after leave year)',
        joiningDate,
        leaveYearStart,
        leaveYearEnd
      };
    }

    // Calculate proration based on months worked in the year using the formula: (Months Worked / 12) × Annual Leave Quota
    const monthsWorked = this.calculateMonthsWorked(joiningDate, leaveYearEnd);
    const totalMonths = 12;
    const prorationFactor = monthsWorked / totalMonths;
    const proratedEntitlement = Math.round(leaveType.maxDaysPerYear * prorationFactor);

    return {
      leaveTypeId: leaveType.id,
      leaveType: leaveType.leaveType,
      displayName: leaveType.displayName,
      originalEntitlement: leaveType.maxDaysPerYear,
      proratedEntitlement,
      prorationFactor,
      calculationMethod: `Prorated Leave = (${monthsWorked}/12) × ${leaveType.maxDaysPerYear} = ${proratedEntitlement} days`,
      joiningDate,
      leaveYearStart,
      leaveYearEnd
    };
  }

  /**
   * Calculate months worked from joining date to end of year
   */
  private calculateMonthsWorked(joiningDate: Date, yearEnd: Date): number {
    const joiningMonth = joiningDate.getMonth(); // 0-based (January = 0)
    const joiningYear = joiningDate.getFullYear();
    const yearEndYear = yearEnd.getFullYear();

    if (joiningYear !== yearEndYear) {
      return 0;
    }

    // Calculate months worked including the joining month
    // Formula: 12 - joining month (since January = 0, February = 1, etc.)
    const monthsWorked = 12 - joiningMonth;
    return monthsWorked;
  }

  /**
   * Apply prorated leave allocations for an employee
   */
  async applyProratedLeaveAllocations(employeeId: number): Promise<LeaveAllocation[]> {
    try {
      const calculations = await this.calculateProratedLeaveForEmployee(employeeId);
      const allocations: LeaveAllocation[] = [];
      const currentYear = new Date().getFullYear();

      for (const calculation of calculations) {
        // Check if allocation already exists for this employee and leave type
        const existingAllocation = await this.leaveAllocationRepository.findOne({
          where: {
            employeeId: employeeId,
            leaveType: calculation.leaveType,
            year: currentYear
          }
        });

        if (existingAllocation) {
          // Update existing allocation
          existingAllocation.policyAllocation = calculation.proratedEntitlement;
          existingAllocation.notes = `Prorated allocation: ${calculation.calculationMethod}`;
          existingAllocation.updatedAt = new Date();

          const updatedAllocation = await this.leaveAllocationRepository.save(existingAllocation);
          allocations.push(updatedAllocation);
        } else {
          // Create new allocation
          const currentYear = new Date().getFullYear();
          const newAllocation = this.leaveAllocationRepository.create({
            employeeId: employeeId,
            leaveType: calculation.leaveType,
            year: currentYear,
            policyAllocation: calculation.proratedEntitlement,
            manualAdjustment: 0,
            carriedForward: 0,
            source: 'POLICY',
            notes: `Prorated allocation: ${calculation.calculationMethod}`,
            isActive: true
          });

          const savedAllocation = await this.leaveAllocationRepository.save(newAllocation);
          allocations.push(savedAllocation);
        }
      }

      return allocations;
    } catch (error) {
      console.error('Error applying prorated leave allocations:', error);
      throw error;
    }
  }

  /**
   * Get prorated leave summary for an employee
   */
  async getProratedLeaveSummary(employeeId: number): Promise<{
    employee: Employee;
    calculations: ProratedLeaveCalculation[];
    totalOriginalEntitlement: number;
    totalProratedEntitlement: number;
    totalSavings: number;
  }> {
    const employee = await this.employeeRepository.findOne({
      where: { id: employeeId }
    });

    if (!employee) {
      throw new Error('Employee not found');
    }

    const calculations = await this.calculateProratedLeaveForEmployee(employeeId);
    
    const totalOriginalEntitlement = calculations.reduce((sum, calc) => sum + calc.originalEntitlement, 0);
    const totalProratedEntitlement = calculations.reduce((sum, calc) => sum + calc.proratedEntitlement, 0);
    const totalSavings = totalOriginalEntitlement - totalProratedEntitlement;

    return {
      employee,
      calculations,
      totalOriginalEntitlement,
      totalProratedEntitlement,
      totalSavings
    };
  }
}

export const proratedLeaveCalculationService = new ProratedLeaveCalculationService(); 