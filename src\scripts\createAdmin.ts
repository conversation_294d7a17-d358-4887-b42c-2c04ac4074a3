import { AppDataSource } from '../config/database';
import { User } from '../entities/User';
import { UserRole } from '../types/common';
import bcrypt from 'bcryptjs';

async function createAdminUser() {
  try {
    // Initialize the data source
    await AppDataSource.initialize();
    console.log('Database connection initialized');

    // Check if admin user already exists
    const existingAdmin = await AppDataSource.getRepository(User).findOne({
      where: { email: '<EMAIL>' }
    });

    if (existingAdmin) {
      console.log('Admin user already exists');
      return;
    }

    // Create admin user
    const admin = new User();
    admin.name = 'Admin';
    admin.email = '<EMAIL>';
    admin.role = UserRole.IT_ADMIN;
    admin.department = 'IT';
    admin.isActive = true;
    admin.permissions = {
      canCreateTickets: true,
      canCreateTicketsForOthers: true,
      canEditTickets: true,
      canDeleteTickets: true,
      canAssignTickets: true,
      canEscalateTickets: true,
      canViewAllTickets: true,
      department: 'IT',
      isAdmin: true
    };
    admin.createdAt = new Date();
    admin.updatedAt = new Date();
    
    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('admin123', salt);
    admin.password = hashedPassword;

    // Save admin user
    await AppDataSource.getRepository(User).save(admin);
    console.log('Admin user created successfully');

  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    // Close the connection
    await AppDataSource.destroy();
    console.log('Database connection closed');
  }
}

// Run the script
createAdminUser(); 