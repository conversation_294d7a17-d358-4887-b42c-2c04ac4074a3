import { MigrationInterface, QueryRunner } from "typeorm";

export class AddEmployeeVehiclesTable1746000000018 implements MigrationInterface {
    name = 'AddEmployeeVehiclesTable1746000000018';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create the employee_vehicles table
        await queryRunner.query(`
            CREATE TABLE \`employee_vehicles\` (
                \`id\` int NOT NULL AUTO_INCREMENT,
                \`vehicleType\` varchar(255) NULL,
                \`registrationNumber\` varchar(255) NULL,
                \`providedByCompany\` tinyint NULL DEFAULT 0,
                \`handingOverDate\` varchar(255) NULL,
                \`returnDate\` varchar(255) NULL,
                \`vehicleMakeModel\` varchar(255) NULL,
                \`vehicleColor\` varchar(255) NULL,
                \`mileageAtIssuance\` varchar(255) NULL,
                \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                \`employeeId\` int NULL,
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB
        `);

        // Add foreign key constraint
        await queryRunner.query(`
            ALTER TABLE \`employee_vehicles\` 
            ADD CONSTRAINT \`FK_employee_vehicles_employee\` 
            FOREIGN KEY (\`employeeId\`) REFERENCES \`employees\`(\`id\`) 
            ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the foreign key constraint
        await queryRunner.query(`
            ALTER TABLE \`employee_vehicles\` 
            DROP FOREIGN KEY \`FK_employee_vehicles_employee\`
        `);
        
        // Drop the table
        await queryRunner.query(`DROP TABLE \`employee_vehicles\``);
    }
} 