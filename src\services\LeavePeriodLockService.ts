import { LeavePeriodLock } from '../types/attendance';

export class LeavePeriodLockService {
  private static instance: LeavePeriodLockService;

  public static getInstance(): LeavePeriodLockService {
    if (!LeavePeriodLockService.instance) {
      LeavePeriodLockService.instance = new LeavePeriodLockService();
    }
    return LeavePeriodLockService.instance;
  }

  /**
   * Get all period locks
   */
  async getPeriodLocks(): Promise<LeavePeriodLock[]> {
    // Mock data - in real implementation, fetch from database
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1;

    const locks: LeavePeriodLock[] = [];

    // Auto-lock past months (older than 2 months)
    for (let month = 1; month < currentMonth - 1; month++) {
      locks.push({
        id: month,
        year: currentYear,
        month,
        lockType: 'all',
        isLocked: true,
        lockedBy: 1, // System user
        lockedAt: new Date(currentYear, month, 1).toISOString(),
        reason: 'Automatic lock for past period'
      });
    }

    // Previous year - all months locked
    for (let month = 1; month <= 12; month++) {
      locks.push({
        id: month + 100,
        year: currentYear - 1,
        month,
        lockType: 'all',
        isLocked: true,
        lockedBy: 1,
        lockedAt: new Date(currentYear - 1, month, 1).toISOString(),
        reason: 'Automatic lock for previous year'
      });
    }

    return locks;
  }

  /**
   * Check if a period is locked
   */
  async isPeriodLocked(year: number, month: number, lockType: 'requests' | 'adjustments' | 'all' = 'all'): Promise<boolean> {
    const locks = await this.getPeriodLocks();
    
    const periodLock = locks.find(lock => 
      lock.year === year && 
      lock.month === month && 
      lock.isLocked &&
      (lock.lockType === 'all' || lock.lockType === lockType)
    );

    return !!periodLock;
  }

  /**
   * Lock a period
   */
  async lockPeriod(
    year: number, 
    month: number, 
    lockType: 'requests' | 'adjustments' | 'all',
    lockedBy: number,
    reason?: string
  ): Promise<LeavePeriodLock> {
    // Validate inputs
    if (year < 2020 || year > new Date().getFullYear() + 1) {
      throw new Error('Invalid year');
    }

    if (month < 1 || month > 12) {
      throw new Error('Invalid month');
    }

    const existingLock = await this.isPeriodLocked(year, month, lockType);
    if (existingLock) {
      throw new Error(`Period ${year}-${month.toString().padStart(2, '0')} is already locked`);
    }

    const newLock: LeavePeriodLock = {
      id: Date.now(), // Mock ID
      year,
      month,
      lockType,
      isLocked: true,
      lockedBy,
      lockedAt: new Date().toISOString(),
      reason: reason || `Manual lock for ${year}-${month.toString().padStart(2, '0')}`
    };

    // Mock save - in real implementation, save to database
    console.log('Locking period:', newLock);

    return newLock;
  }

  /**
   * Unlock a period
   */
  async unlockPeriod(
    year: number, 
    month: number, 
    lockType: 'requests' | 'adjustments' | 'all',
    unlockedBy: number,
    unlockDate?: string
  ): Promise<LeavePeriodLock> {
    const locks = await this.getPeriodLocks();
    const existingLock = locks.find(lock => 
      lock.year === year && 
      lock.month === month && 
      (lock.lockType === 'all' || lock.lockType === lockType)
    );

    if (!existingLock) {
      throw new Error(`No lock found for period ${year}-${month.toString().padStart(2, '0')}`);
    }

    const updatedLock: LeavePeriodLock = {
      ...existingLock,
      isLocked: false,
      unlockDate: unlockDate || new Date().toISOString()
    };

    // Mock update - in real implementation, update database
    console.log('Unlocking period:', updatedLock);

    return updatedLock;
  }

  /**
   * Get lock status for a date range
   */
  async getLockStatus(startDate: string, endDate: string): Promise<{
    isLocked: boolean;
    lockedPeriods: { year: number; month: number; lockType: string }[];
    message?: string;
  }> {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const lockedPeriods = [];

    // Check each month in the range
    let current = new Date(start.getFullYear(), start.getMonth(), 1);
    const endMonth = new Date(end.getFullYear(), end.getMonth(), 1);

    while (current <= endMonth) {
      const year = current.getFullYear();
      const month = current.getMonth() + 1;
      
      const isLocked = await this.isPeriodLocked(year, month);
      if (isLocked) {
        const locks = await this.getPeriodLocks();
        const lock = locks.find(l => l.year === year && l.month === month && l.isLocked);
        if (lock) {
          lockedPeriods.push({
            year,
            month,
            lockType: lock.lockType
          });
        }
      }

      current.setMonth(current.getMonth() + 1);
    }

    const isLocked = lockedPeriods.length > 0;
    const message = isLocked 
      ? `Cannot edit: ${lockedPeriods.length} period(s) are locked`
      : 'All periods are available for editing';

    return {
      isLocked,
      lockedPeriods,
      message
    };
  }

  /**
   * Auto-lock past periods based on configuration
   */
  async autoLockPastPeriods(monthsToKeepOpen: number = 2): Promise<{
    lockedPeriods: number;
    errors: string[];
  }> {
    const result = {
      lockedPeriods: 0,
      errors: [] as string[]
    };

    const today = new Date();
    const cutoffDate = new Date(today.getFullYear(), today.getMonth() - monthsToKeepOpen, 1);

    // Lock all periods before cutoff date
    let current = new Date(2020, 0, 1); // Start from 2020
    
    while (current < cutoffDate) {
      const year = current.getFullYear();
      const month = current.getMonth() + 1;

      try {
        const isAlreadyLocked = await this.isPeriodLocked(year, month);
        if (!isAlreadyLocked) {
          await this.lockPeriod(year, month, 'all', 1, 'Automatic lock for past period');
          result.lockedPeriods++;
        }
      } catch (error) {
        result.errors.push(`Failed to lock ${year}-${month.toString().padStart(2, '0')}: ${error}`);
      }

      current.setMonth(current.getMonth() + 1);
    }

    return result;
  }

  /**
   * Get periods that can be edited
   */
  async getEditablePeriods(): Promise<{
    year: number;
    month: number;
    monthName: string;
    canEditRequests: boolean;
    canEditAdjustments: boolean;
  }[]> {
    const today = new Date();
    const editablePeriods = [];

    // Check current year and next year
    for (let yearOffset = -1; yearOffset <= 1; yearOffset++) {
      const year = today.getFullYear() + yearOffset;
      
      for (let month = 1; month <= 12; month++) {
        const requestsLocked = await this.isPeriodLocked(year, month, 'requests');
        const adjustmentsLocked = await this.isPeriodLocked(year, month, 'adjustments');
        const allLocked = await this.isPeriodLocked(year, month, 'all');

        const canEditRequests = !requestsLocked && !allLocked;
        const canEditAdjustments = !adjustmentsLocked && !allLocked;

        if (canEditRequests || canEditAdjustments) {
          editablePeriods.push({
            year,
            month,
            monthName: new Date(year, month - 1, 1).toLocaleString('default', { month: 'long' }),
            canEditRequests,
            canEditAdjustments
          });
        }
      }
    }

    return editablePeriods;
  }

  /**
   * Validate if an action can be performed on a date
   */
  async validateAction(
    actionDate: string, 
    actionType: 'request' | 'adjustment' | 'edit'
  ): Promise<{
    allowed: boolean;
    reason?: string;
  }> {
    const date = new Date(actionDate);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;

    const lockType = actionType === 'request' ? 'requests' : 
                    actionType === 'adjustment' ? 'adjustments' : 'all';

    const isLocked = await this.isPeriodLocked(year, month, lockType);

    if (isLocked) {
      return {
        allowed: false,
        reason: `Period ${year}-${month.toString().padStart(2, '0')} is locked for ${actionType}s`
      };
    }

    return { allowed: true };
  }
}

export const leavePeriodLockService = LeavePeriodLockService.getInstance(); 