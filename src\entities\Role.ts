import { <PERSON><PERSON>ty, PrimaryGeneratedC<PERSON>umn, Column, CreateDateColumn, UpdateDateColumn, ManyToMany, JoinTable, ManyToOne, JoinColumn, OneToMany } from 'typeorm';
import { IsNotEmpty, Length, IsEnum } from 'class-validator';
import { User } from './User';
import { UserRoleAssignment } from './UserRoleAssignment';

export enum RoleCategory {
  SYSTEM = 'system',
  DASHBOARD = 'dashboard',
  CUSTOM = 'custom'
}

@Entity('roles')
export class Role {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 100, unique: true })
  @IsNotEmpty({ message: 'Role name is required' })
  @Length(2, 100, { message: 'Role name must be between 2 and 100 characters' })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: RoleCategory,
    default: RoleCategory.SYSTEM
  })
  @IsEnum(RoleCategory, { message: 'Invalid role category' })
  category: RoleCategory;

  @Column('simple-array')
  permissions: string[];

  @Column('simple-array', { nullable: true })
  dashboardAccess: string[];

  @Column({ type: 'varchar', nullable: true })
  parentId: string | null;

  @ManyToOne(() => Role, { nullable: true })
  @JoinColumn({ name: 'parentId' })
  parentRole: Role | null;

  @OneToMany(() => Role, role => role.parentRole)
  childRoles: Role[];

  @OneToMany(() => UserRoleAssignment, assignment => assignment.role)
  userAssignments: UserRoleAssignment[];

  @Column({ type: 'int', default: 0 })
  userCount: number;

  @Column({ type: 'varchar', nullable: true })
  updatedBy: string | null;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  // Helper method to get all permissions including inherited from parent
  async getAllPermissions(): Promise<string[]> {
    const directPermissions = [...this.permissions];
    
    if (this.parentRole) {
      const parentPermissions = await this.parentRole.getAllPermissions();
      // Combine and remove duplicates
      return [...new Set([...directPermissions, ...parentPermissions])];
    }
    
    return directPermissions;
  }
} 