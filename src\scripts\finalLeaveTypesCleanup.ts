import { AppDataSource } from '../config/database';

async function finalLeaveTypesCleanup() {
  try {
    console.log('🔍 Initializing database connection...');
    await AppDataSource.initialize();
    
    const queryRunner = AppDataSource.createQueryRunner();
    
    await queryRunner.startTransaction();
    
    try {
      console.log('🧹 Final leave types cleanup - Creating clean standard leave types...\n');
      
      // Get the active policy configuration ID
      const policyConfig = await queryRunner.query(`
        SELECT id FROM leave_policy_configurations 
        WHERE isActive = 1 ORDER BY id DESC LIMIT 1
      `);
      
      if (policyConfig.length === 0) {
        throw new Error('No active policy configuration found');
      }
      
      const policyConfigId = policyConfig[0].id;
      console.log(`📋 Using policy configuration ID: ${policyConfigId}`);
      
      // Step 1: Delete ALL existing leave type policies
      console.log('\n🗑️  Step 1: Removing all existing leave type policies...');
      await queryRunner.query(`DELETE FROM leave_type_policies`);
      console.log('   ✅ All existing leave type policies deleted');
      
      // Step 2: Delete ALL existing leave allocation and balance data
      console.log('\n🗑️  Step 2: Removing all existing leave allocation and balance data...');
      await queryRunner.query(`DELETE FROM leave_allocations`);
      await queryRunner.query(`DELETE FROM leave_balances`);
      console.log('   ✅ All existing leave data deleted');
      
      // Step 3: Create clean standard leave types
      console.log('\n✨ Step 3: Creating clean standard leave types...');
      
      const standardLeaveTypes = [
        {
          leaveType: 'ANNUAL_LEAVE',
          displayName: 'Annual Leave',
          description: 'Annual vacation leave',
          maxDaysPerYear: 25,
          sortOrder: 1,
          category: 'general'
        },
        {
          leaveType: 'SICK_LEAVE',
          displayName: 'Sick Leave',
          description: 'Medical leave for illness',
          maxDaysPerYear: 15,
          sortOrder: 2,
          category: 'medical'
        },
        {
          leaveType: 'CASUAL_LEAVE',
          displayName: 'Casual Leave',
          description: 'Casual leave for personal matters',
          maxDaysPerYear: 10,
          sortOrder: 3,
          category: 'general'
        }
      ];
      
      for (const leaveType of standardLeaveTypes) {
        console.log(`   📝 Creating ${leaveType.leaveType} (${leaveType.displayName})...`);
        
        await queryRunner.query(`
          INSERT INTO leave_type_policies (
            leaveType, enabled, displayName, description, settings, 
            applicableRoles, applicableDepartments, maxDaysPerYear, 
            minServicePeriod, allowCarryForward, carryForwardLimit, 
            encashmentAllowed, documentRequired, sortOrder, isActive, 
            category, genderEligibility, allowHalfDay, minDaysNotice,
            policyConfigurationId
          ) VALUES (
            ?, 1, ?, ?, '{}', 
            '[]', '[]', ?, 0, 0, 0, 0, 0, ?, 1, 
            ?, 'all', 1, 0, ?
          )
        `, [
          leaveType.leaveType,
          leaveType.displayName,
          leaveType.description,
          leaveType.maxDaysPerYear,
          leaveType.sortOrder,
          leaveType.category,
          policyConfigId
        ]);
        
        console.log(`   ✅ Created ${leaveType.leaveType}`);
      }
      
      await queryRunner.commitTransaction();
      
      console.log('\n🎉 FINAL CLEANUP COMPLETE!');
      console.log('📋 Your system now has clean, standard leave types:');
      standardLeaveTypes.forEach(lt => {
        console.log(`   ✅ ${lt.leaveType} (${lt.displayName}) - ${lt.maxDaysPerYear} days`);
      });
      
      console.log('\n🔒 WHAT WAS DONE:');
      console.log('   🗑️  Removed all old, duplicate, and confusing leave types');
      console.log('   🗑️  Cleared all old leave allocation and balance data');
      console.log('   ✨ Created 3 clean, standard leave types');
      console.log('   🎯 No more CUSTOM_* or duplicate leave types');
      
      console.log('\n📝 NEXT STEPS:');
      console.log('   1. Refresh your Leave Management page');
      console.log('   2. You will see 3 clean leave types: Annual, Sick, and Casual Leave');
      console.log('   3. You can now start fresh with proper leave allocations');
      console.log('   4. Configure the leave types as needed in Policy Configuration');
      console.log('   5. The Leave Allocations tab will work properly without confusion');
      
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
    
  } catch (error) {
    console.error('❌ Error in final leave types cleanup:', error);
    throw error;
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('\n📪 Database connection closed');
    }
  }
}

// Run the final cleanup
if (require.main === module) {
  finalLeaveTypesCleanup()
    .then(() => {
      console.log('✅ Final leave types cleanup completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Final leave types cleanup failed:', error);
      process.exit(1);
    });
}

export { finalLeaveTypesCleanup }; 