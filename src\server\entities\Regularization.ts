import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn
} from 'typeorm';
import { Employee } from './Employee';

@Entity('regularizations')
export class Regularization {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'employee_id', type: 'varchar' })
  employeeId: string;

  @Column({ name: 'employee_name' })
  employeeName: string;

  @Column({ type: 'date' })
  date: Date;

  @Column()
  type: string;

  @Column({ name: 'requested_time', type: 'time', nullable: true })
  requestedTime?: string;

  @Column({ type: 'text' })
  reason: string;

  @Column({ default: 'pending' })
  status: string;

  @Column({ name: 'approver_comments', type: 'text', nullable: true })
  approverComments?: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne(() => Employee, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;
} 