import { AppDataSource } from '../config/database';
import { Asset, AssetType, AssetStatus } from '../entities/Asset';
import { User } from '../entities/User';
import { Between, FindOptionsWhere, ILike, In, IsNull, LessThan, MoreThan, Not } from 'typeorm';
import { 
  calculateStraightLineDepreciation, 
  calculateAssetAge, 
  getDefaultUsefulLife 
} from '../utils/assetDepreciation';
import { validate } from 'class-validator';

export const assetRepository = {
  // Helper function to standardize status values
  standardizeStatus(status: string | null | undefined): AssetStatus {
    if (!status) return AssetStatus.Active; // Default to Active if no status provided
    
    // Try to match with enum values
    const statusKey = Object.keys(AssetStatus).find(
      key => key.toLowerCase() === status.toLowerCase()
    );
    
    if (statusKey) {
      return AssetStatus[statusKey as keyof typeof AssetStatus];
    }
    
    // Default to Active if no match
    return AssetStatus.Active;
  },

  async findAll(options?: {
    search?: string;
    assetType?: string;
    status?: string;
    department?: string;
    assignedToId?: string;
    location?: string;
    warrantyExpiring?: boolean;
    needsMaintenance?: boolean;
    page?: number;
    limit?: number;
  }) {
    const { 
      search, 
      assetType, 
      status, 
      department, 
      assignedToId, 
      location,
      warrantyExpiring,
      needsMaintenance,
      page = 1, 
      limit = 100
    } = options || {};

    const where: FindOptionsWhere<Asset> = {};
    
    if (search) {
      where.serialNumber = ILike(`%${search}%`);
    }
    
    if (assetType) {
      // Convert string to AssetType enum
      const assetTypeKey = Object.keys(AssetType).find(
        key => key.toLowerCase() === assetType.toLowerCase()
      );
      
      if (assetTypeKey) {
        where.assetType = AssetType[assetTypeKey as keyof typeof AssetType];
      }
    }
    
    if (status) {
      // Convert string to AssetStatus enum
      const statusKey = Object.keys(AssetStatus).find(
        key => key.toLowerCase() === status.toLowerCase()
      );
      
      if (statusKey) {
        where.status = AssetStatus[statusKey as keyof typeof AssetStatus];
      }
    }
    
    if (department) {
      where.department = department;
    }
    
    if (assignedToId) {
      where.assignedToId = assignedToId;
    }
    
    if (location) {
      where.location = location;
    }
    
    if (warrantyExpiring) {
      const threeMonthsFromNow = new Date();
      threeMonthsFromNow.setMonth(threeMonthsFromNow.getMonth() + 3);
      where.warrantyExpiry = LessThan(threeMonthsFromNow);
    }
    
    if (needsMaintenance) {
      const today = new Date();
      where.nextMaintenance = LessThan(today);
    }

    const [assets, total] = await AppDataSource.getRepository(Asset).findAndCount({
      where,
      relations: ['assignedTo'],
      skip: (page - 1) * limit,
      take: limit,
      order: {
        createdAt: 'ASC'
      }
    });

    return {
      assets,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    };
  },

  async findById(id: string | number) {
    try {
      console.log('=== ASSET REPOSITORY FIND BY ID ===');
      console.log('Looking for asset with ID:', id);
      
      if (!id) {
        console.error('Invalid ID provided:', id);
        return null;
      }
      
      // Try to parse the ID as a number if it's a string
      let numericId: number | null = null;
      if (typeof id === 'string') {
        const parsedId = parseInt(id, 10);
        if (!isNaN(parsedId)) {
          numericId = parsedId;
        }
      } else {
        numericId = id;
      }
      
      // First try to find by numeric ID (primary key)
      if (numericId !== null) {
        console.log('Attempting to find by numeric ID:', numericId);
        let asset = await AppDataSource.getRepository(Asset).findOne({
          where: { id: numericId },
          relations: ['assignedTo']
        });
        
        if (asset) {
          console.log('Asset found by numeric ID:', asset.id);
          console.log('Asset tag:', asset.assetTag);
          return asset;
        }
      }
      
      // If not found and the ID looks like a formatted asset tag (e.g., AST-2025-7589)
      if (typeof id === 'string' && id.includes('-')) {
        console.log('Asset not found by numeric ID, trying to find by assetTag:', id);
        
        // Try to find by asset tag
        const asset = await AppDataSource.getRepository(Asset).findOne({
          where: { assetTag: id },
          relations: ['assignedTo']
        });
        
        if (asset) {
          console.log('Asset found by assetTag:', asset.id);
          return asset;
        }
      }
      
      // If still not found, log the failure
      console.log('Asset not found by numeric ID or assetTag');
      return null;
    } catch (error: any) {
      console.error('Error finding asset by ID:', error);
      throw new Error(`Failed to find asset by ID: ${error.message}`);
    }
  },

  async findBySerialNumber(serialNumber: string) {
    try {
      console.log('Finding asset by serial number:', serialNumber);
      const asset = await AppDataSource.getRepository(Asset).findOne({
        where: { serialNumber }
      });
      
      console.log('Found asset:', asset);
      return asset;
    } catch (error: any) {
      console.error('Error finding asset by serial number:', error);
      throw new Error(`Failed to find asset by serial number: ${error.message}`);
    }
  },

  async create(assetData: Partial<Asset>): Promise<Asset> {
    console.log('=== ASSET REPOSITORY CREATE ===');
    console.log('Creating asset with data:', assetData);
    
    try {
      // Ensure we don't try to set an ID when creating a new asset
      // This will allow the database to auto-generate the ID
      if (assetData.id) {
        delete assetData.id;
        console.log('Removed provided ID to use auto-increment');
      }
      
      // Generate asset tag if not provided
      if (!assetData.assetTag) {
        // Get the current year
        const currentYear = new Date().getFullYear();
        
        // Find the highest existing asset number for this year
        const assetRepo = AppDataSource.getRepository(Asset);
        const highestAsset = await assetRepo
          .createQueryBuilder('asset')
          .where('asset.assetTag LIKE :pattern', { pattern: `AST-${currentYear}-%` })
          .orderBy('asset.assetTag', 'DESC')
          .getOne();
        
        let nextNumber = 1;
        if (highestAsset && highestAsset.assetTag) {
          // Extract the number from the highest asset tag
          const match = highestAsset.assetTag.match(/AST-\d+-(\d+)/);
          if (match && match[1]) {
            nextNumber = parseInt(match[1], 10) + 1;
          }
        }
        
        // Format the new asset tag: AST-YYYY-NNNN
        const assetTag = `AST-${currentYear}-${nextNumber.toString().padStart(4, '0')}`;
        console.log('Generated new asset tag:', assetTag);
        
        // Set the assetTag
        assetData.assetTag = assetTag;
      }
      
      // Standardize status value
      if (assetData.status) {
        assetData.status = this.standardizeStatus(assetData.status as string);
        console.log('Standardized status:', assetData.status);
      }

      // Handle date conversions
      const dateFields = ['purchaseDate', 'warrantyExpiry', 'assignedAt', 'lastMaintenance', 'nextMaintenance'] as const;
      type DateField = typeof dateFields[number];

      dateFields.forEach((field: DateField) => {
        const value = assetData[field as keyof typeof assetData];
        if (value === undefined || value === null || (typeof value === 'string' && value === '')) {
          console.log(`Setting empty ${field} to null`);
          (assetData[field as keyof typeof assetData] as any) = null;
        } else if (typeof value === 'string') {
          try {
            console.log(`Converting ${field} from string to Date:`, value);
            const dateValue = new Date(value);
            
            // Check if the date is valid
            if (isNaN(dateValue.getTime())) {
              console.log(`Invalid date for ${field}, setting to null`);
              (assetData[field as keyof typeof assetData] as any) = null;
            } else {
              (assetData[field as keyof typeof assetData] as any) = dateValue;
            }
          } catch (error) {
            console.error(`Error converting ${field} to Date:`, error);
            (assetData[field as keyof typeof assetData] as any) = null;
          }
        }
      });

      // Ensure internetAccess is a boolean
      assetData.internetAccess = Boolean(assetData.internetAccess);

      // Handle numeric fields
      const numericFields = ['cost', 'maintenanceCost'] as const;
      type NumericField = typeof numericFields[number];
      
      numericFields.forEach((field: NumericField) => {
        const value = assetData[field as keyof typeof assetData];
        if (value === undefined || value === null || (typeof value === 'string' && value === '')) {
          console.log(`Setting empty ${field} to null`);
          (assetData[field as keyof typeof assetData] as any) = null;
        } else if (typeof value === 'string') {
          try {
            console.log(`Converting ${field} from string to number:`, value);
            const numValue = parseFloat(value);
            if (!isNaN(numValue)) {
              (assetData[field as keyof typeof assetData] as any) = numValue;
            } else {
              console.log(`Invalid number for ${field}, setting to null`);
              (assetData[field as keyof typeof assetData] as any) = null;
            }
          } catch (error) {
            console.error(`Error converting ${field} to number:`, error);
            (assetData[field as keyof typeof assetData] as any) = null;
          }
        }
      });

      // Ensure attributes is an object
      if (!assetData.attributes || typeof assetData.attributes === 'string') {
        try {
          assetData.attributes = typeof assetData.attributes === 'string' 
            ? JSON.parse(assetData.attributes)
            : {};
        } catch (error) {
          console.error('Error parsing attributes JSON:', error);
          assetData.attributes = {};
        }
      }

      // Create the asset entity
      console.log('Creating asset with data:', JSON.stringify(assetData, null, 2));
      const assetRepository = AppDataSource.getRepository(Asset);
      
      const asset = assetRepository.create(assetData);

      // Validate the asset before saving
      const errors = await validate(asset);
      if (errors.length > 0) {
        console.error('Validation errors:', errors);
        const errorMessages = errors
          .map(error => error.constraints ? Object.values(error.constraints) : [])
          .flat()
          .join(', ');
        throw new Error(`Asset validation failed: ${errorMessages}`);
      }

      // Save the asset
      console.log('Saving asset to database');
      const savedAsset = await assetRepository.save(asset);
      console.log('Asset saved successfully:', JSON.stringify(savedAsset, null, 2));

      return savedAsset;
    } catch (error: any) {
      console.error('Error in create method:', error);
      console.error('Error stack:', error.stack);
      
      if (error.code === 'ER_DUP_ENTRY') {
        throw new Error('An asset with this serial number already exists');
      }
      
      throw error;
    }
  },

  async update(id: string, assetData: Partial<Asset>) {
    console.log('=== ASSET REPOSITORY UPDATE ===');
    console.log('Updating asset with ID:', id);
    console.log('Update data keys:', Object.keys(assetData));
    
    if (!id) {
      console.error('Invalid ID provided for update:', id);
      throw new Error('Invalid ID provided for update');
    }
    
    // First check if the asset exists
    let existingAsset = await this.findById(id);
    if (!existingAsset) {
      console.error('Asset not found for update with ID:', id);
      throw new Error(`Asset not found with ID: ${id}`);
    }
    
    console.log('Found existing asset:', existingAsset.id, existingAsset.assetTag);
    
    // Standardize status value
    if (assetData.status) {
      assetData.status = this.standardizeStatus(assetData.status as string);
      console.log('Standardized status:', assetData.status);
    }
    
    // Convert date strings to Date objects or null if empty
    const dateFields = ['purchaseDate', 'warrantyExpiry', 'assignedAt', 'lastMaintenance', 'nextMaintenance'] as const;
    type DateField = typeof dateFields[number];
    
    dateFields.forEach((field: DateField) => {
      const value = assetData[field];
      if (typeof value === 'string') {
        if (value === '') {
          console.log(`Setting empty ${field} to null`);
          (assetData[field] as any) = null;
        } else {
          console.log(`Converting ${field} from string to Date:`, value);
          (assetData[field] as any) = new Date(value);
        }
      }
    });
    
    // Ensure internetAccess is a boolean if it's provided
    if (assetData.internetAccess !== undefined) {
      assetData.internetAccess = Boolean(assetData.internetAccess);
    }
    
    // Ensure attributes is an object if it's provided
    if (assetData.attributes !== undefined) {
      if (assetData.attributes === null) {
        assetData.attributes = {};
      } else if (typeof assetData.attributes === 'string') {
        try {
          assetData.attributes = JSON.parse(assetData.attributes);
        } catch (error) {
          assetData.attributes = {};
        }
      }
    }
    
    console.log('Updating asset with processed data keys:', Object.keys(assetData));
    
    try {
      // Use the repository to update the asset
      const assetRepo = AppDataSource.getRepository(Asset);
      
      // Log the update query for debugging
      console.log(`Executing update query for asset with ID: ${id}`);
      
      // Try two different update approaches
      let updateResult;
      try {
        // First try the standard update method
        updateResult = await assetRepo.update(id, assetData);
        console.log('Update result:', updateResult);
      } catch (updateError) {
        console.error('Error with standard update, trying save method instead:', updateError);
        
        // If standard update fails, try the save method which is more forgiving
        const mergedAsset = assetRepo.merge(existingAsset, assetData);
        await assetRepo.save(mergedAsset);
        console.log('Asset updated using save method');
        updateResult = { affected: 1 }; // Simulate update result
      }
      
      if (updateResult.affected === 0) {
        console.error('No rows affected by update. Asset may not exist with ID:', id);
        throw new Error(`Failed to update asset: No rows affected. Asset may not exist with ID: ${id}`);
      }
      
      // Fetch the updated asset
      const updatedAsset = await this.findById(id);
      if (!updatedAsset) {
        console.error('Failed to fetch updated asset after update with ID:', id);
        throw new Error('Failed to fetch updated asset after update');
      }
      
      console.log('Asset updated successfully:', updatedAsset.id);
      return updatedAsset;
    } catch (error: any) {
      console.error('Error updating asset in database:', error);
      console.error('Error stack:', error.stack);
      
      if (error.code) {
        console.error('SQL Error Code:', error.code);
        console.error('SQL Error Message:', error.sqlMessage);
      }
      
      throw new Error(`Failed to update asset in database: ${error.message || 'Unknown error'}`);
    }
  },

  async delete(id: string) {
    return AppDataSource.getRepository(Asset).delete(id);
  },

  async assignToUser(assetId: string, userId: string) {
    const asset = await this.findById(assetId);
    if (!asset) {
      throw new Error('Asset not found');
    }

    const user = await AppDataSource.getRepository(User).findOneBy({ id: userId });
    if (!user) {
      throw new Error('User not found');
    }

    asset.assignedTo = user;
    asset.assignedToId = userId;
    asset.assignedAt = new Date();
    
    return AppDataSource.getRepository(Asset).save(asset);
  },

  async unassignFromUser(assetId: string) {
    const asset = await this.findById(assetId);
    if (!asset) {
      throw new Error('Asset not found');
    }

    asset.assignedTo = undefined;
    asset.assignedToId = undefined;
    asset.assignedAt = undefined;
    
    return AppDataSource.getRepository(Asset).save(asset);
  },

  async getAssetDepreciation(assetId: string) {
    const asset = await this.findById(assetId);
    
    if (!asset || !asset.purchaseDate || !asset.cost) {
      throw new Error('Asset not found or missing required data for depreciation calculation');
    }
    
    const purchaseDate = new Date(asset.purchaseDate);
    const cost = asset.cost;
    const salvageValue = cost * 0.1; // Assume 10% salvage value by default
    const ageInYears = calculateAssetAge(purchaseDate);
    const usefulLifeYears = getDefaultUsefulLife(asset.assetType, asset.category);
    
    // Calculate depreciation using straight-line method
    const depreciation = calculateStraightLineDepreciation(
      cost,
      salvageValue,
      usefulLifeYears,
      ageInYears
    );
    
    return {
      asset,
      purchaseDate,
      cost,
      salvageValue,
      ageInYears,
      usefulLifeYears,
      ...depreciation
    };
  },

  async getAssetStats() {
    const assetRepo = AppDataSource.getRepository(Asset);
    
    const total = await assetRepo.count();
    
    const byStatus = await assetRepo
      .createQueryBuilder('asset')
      .select('asset.status, COUNT(asset.id) as count')
      .groupBy('asset.status')
      .getRawMany();
    
    const byType = await assetRepo
      .createQueryBuilder('asset')
      .select('asset.assetType, COUNT(asset.id) as count')
      .groupBy('asset.assetType')
      .getRawMany();
    
    const byDepartment = await assetRepo
      .createQueryBuilder('asset')
      .select('asset.department, COUNT(asset.id) as count')
      .groupBy('asset.department')
      .getRawMany();
    
    const totalCost = await assetRepo
      .createQueryBuilder('asset')
      .select('SUM(asset.cost)', 'sum')
      .getRawOne();
    
    const maintenanceCost = await assetRepo
      .createQueryBuilder('asset')
      .select('SUM(asset.maintenanceCost)', 'sum')
      .getRawOne();
    
    const assignedCount = await assetRepo.count({
      where: {
        assignedToId: Not(IsNull())
      }
    });
    
    const warrantyExpiringCount = await assetRepo.count({
      where: {
        warrantyExpiry: LessThan(new Date(new Date().setMonth(new Date().getMonth() + 3)))
      }
    });
    
    const needsMaintenanceCount = await assetRepo.count({
      where: {
        nextMaintenance: LessThan(new Date())
      }
    });

    // Calculate total depreciation and current value
    const assets = await assetRepo.find({
      where: {
        purchaseDate: Not(IsNull()),
        cost: Not(IsNull())
      }
    });

    let totalDepreciation = 0;
    let currentValue = 0;

    for (const asset of assets) {
      if (asset.purchaseDate && asset.cost) {
        const purchaseDate = new Date(asset.purchaseDate);
        const cost = asset.cost;
        const salvageValue = cost * 0.1; // Assume 10% salvage value
        const ageInYears = calculateAssetAge(purchaseDate);
        const usefulLifeYears = getDefaultUsefulLife(asset.assetType, asset.category);
        
        const depreciation = calculateStraightLineDepreciation(
          cost,
          salvageValue,
          usefulLifeYears,
          ageInYears
        );
        
        totalDepreciation += depreciation.totalDepreciation;
        currentValue += depreciation.currentValue;
      }
    }
    
    return {
      total,
      byStatus: byStatus.reduce((acc, curr) => {
        acc[curr.status.toLowerCase()] = parseInt(curr.count);
        return acc;
      }, {}),
      byType: byType.reduce((acc, curr) => {
        acc[curr.assetType.toLowerCase()] = parseInt(curr.count);
        return acc;
      }, {}),
      byDepartment: byDepartment.reduce((acc, curr) => {
        acc[curr.department] = parseInt(curr.count);
        return acc;
      }, {}),
      totalCost: parseFloat(totalCost?.sum || '0'),
      maintenanceCost: parseFloat(maintenanceCost?.sum || '0'),
      assignedCount,
      warrantyExpiringCount,
      needsMaintenanceCount,
      totalDepreciation,
      currentValue
    };
  },

  async findByAssetTag(assetTag: string) {
    try {
      console.log('Finding asset by assetTag:', assetTag);
      const asset = await AppDataSource.getRepository(Asset).findOne({
        where: { assetTag }
      });
      
      console.log('Found asset by assetTag:', asset);
      return asset;
    } catch (error: any) {
      console.error('Error finding asset by assetTag:', error);
      throw new Error(`Failed to find asset by assetTag: ${error.message}`);
    }
  }
}; 