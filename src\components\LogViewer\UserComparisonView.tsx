import React, { useState, useMemo } from 'react';
import { 
  User, Activity, CheckCircle, XCircle, AlertTriangle, 
  Clock, Info, ArrowLeft, BarChart2, Users, Shield,
  Check, ArrowUpDown
} from 'lucide-react';
import { SystemLog } from './types';
import { formatSystemDate } from './utils/dateFormat';
import { 
  extractUserLoginInfo, 
  UserLoginInfo, 
  isSuccessfulLogin, 
  isFailedLogin, 
  isLogout 
} from './utils/loginTracker';

interface UserComparisonViewProps {
  logs: SystemLog[];
  onBack: () => void;
  onViewUserDetails: (username: string) => void;
  onViewLogDetails: (log: SystemLog) => void;
}

// Activity metric calculation for a single user
interface UserActivityMetrics {
  username: string;
  department: string;
  totalActivities: number;
  successfulLogins: number;
  failedLogins: number;
  logouts: number;
  errorCount: number;
  warningCount: number;
  infoCount: number;
  successCount: number;
  averageSessionDuration: number | null;
  lastActivity: Date | null;
  riskScore: number;
  activityDistribution: {
    login: number;
    logout: number;
    error: number;
    warning: number;
    info: number;
    success: number;
  };
}

export const UserComparisonView: React.FC<UserComparisonViewProps> = ({ 
  logs, 
  onBack, 
  onViewUserDetails,
  onViewLogDetails
}) => {
  // State for selected users, department filter, and sorting
  const [selectedUsernames, setSelectedUsernames] = useState<string[]>([]);
  const [departmentFilter, setDepartmentFilter] = useState<string>('All');
  const [sortBy, setSortBy] = useState<keyof UserActivityMetrics>('totalActivities');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [showRiskScoring, setShowRiskScoring] = useState<boolean>(false);
  
  // Extract all unique users and departments from logs
  const { uniqueUsers, uniqueDepartments } = useMemo(() => {
    const userMap = new Map<string, string>(); // username -> department
    
    logs.forEach(log => {
      // Extract department from log details if available
      let department = 'Unknown';
      const departmentMatch = log.details.match(/Department:\s*([^|]+)/i);
      if (departmentMatch && departmentMatch[1]) {
        department = departmentMatch[1].trim();
      }
      
      if (!userMap.has(log.user)) {
        userMap.set(log.user, department);
      }
    });
    
    return {
      uniqueUsers: Array.from(userMap.keys()),
      uniqueDepartments: ['All', ...new Set(Array.from(userMap.values()))]
    };
  }, [logs]);
  
  // Calculate metrics for each user
  const userMetrics = useMemo((): UserActivityMetrics[] => {
    const metrics: UserActivityMetrics[] = [];
    const loginInfoMap = extractUserLoginInfo(logs);
    
    // Group logs by user
    const userLogsMap = new Map<string, SystemLog[]>();
    logs.forEach(log => {
      if (!userLogsMap.has(log.user)) {
        userLogsMap.set(log.user, []);
      }
      userLogsMap.get(log.user)!.push(log);
    });
    
    // Process each user
    for (const username of uniqueUsers) {
      const userLogs = userLogsMap.get(username) || [];
      const loginInfo = loginInfoMap.get(username);
      
      // Skip users that don't match department filter
      let department = 'Unknown';
      const departmentMatch = userLogs.length > 0 ? 
        userLogs[0].details.match(/Department:\s*([^|]+)/i) : null;
      if (departmentMatch && departmentMatch[1]) {
        department = departmentMatch[1].trim();
      }
      
      if (departmentFilter !== 'All' && department !== departmentFilter) {
        continue;
      }
      
      // Count activities by type
      let successfulLogins = 0;
      let failedLogins = 0;
      let logouts = 0;
      let errorCount = 0;
      let warningCount = 0;
      let infoCount = 0;
      let successCount = 0;
      
      userLogs.forEach(log => {
        if (isSuccessfulLogin(log)) successfulLogins++;
        if (isFailedLogin(log)) failedLogins++;
        if (isLogout(log)) logouts++;
        
        if (log.type === 'error') errorCount++;
        if (log.type === 'warning') warningCount++;
        if (log.type === 'info') infoCount++;
        if (log.type === 'success') successCount++;
      });
      
      // Find the most recent activity
      const lastActivityLog = userLogs.length > 0 ? 
        userLogs.reduce((latest, log) => 
          new Date(log.timestamp) > new Date(latest.timestamp) ? log : latest
        ) : null;
      
      // Calculate risk score
      // - High failed login ratio increases risk
      // - More errors and warnings increase risk
      // - Many activities with few logouts increases risk
      const failedLoginRatio = successfulLogins > 0 ? 
        failedLogins / successfulLogins : (failedLogins > 0 ? 2 : 0);
      
      const errorRatio = userLogs.length > 0 ? 
        (errorCount + warningCount) / userLogs.length : 0;
      
      const logoutRatio = successfulLogins > 0 ? 
        logouts / successfulLogins : 1;
      
      // Risk score is from 0-100, higher means more risk
      let riskScore = 
        (failedLoginRatio * 30) + // Up to 30 points for failed logins
        (errorRatio * 30) + // Up to 30 points for errors/warnings
        ((1 - logoutRatio) * 20) + // Up to 20 points for missing logouts
        (Math.min(10, failedLogins) * 2); // Up to 20 points for raw failed login count
      
      // Cap score at 100
      riskScore = Math.min(100, Math.round(riskScore));
      
      metrics.push({
        username,
        department,
        totalActivities: userLogs.length,
        successfulLogins,
        failedLogins,
        logouts,
        errorCount,
        warningCount,
        infoCount,
        successCount,
        averageSessionDuration: loginInfo?.averageSessionDuration || null,
        lastActivity: lastActivityLog ? new Date(lastActivityLog.timestamp) : null,
        riskScore,
        activityDistribution: {
          login: successfulLogins,
          logout: logouts,
          error: errorCount,
          warning: warningCount,
          info: infoCount,
          success: successCount
        }
      });
    }
    
    return metrics;
  }, [logs, uniqueUsers, departmentFilter]);
  
  // Sort metrics by selected field
  const sortedMetrics = useMemo(() => {
    return [...userMetrics].sort((a, b) => {
      let valueA = a[sortBy];
      let valueB = b[sortBy];
      
      if (valueA === null && valueB === null) return 0;
      if (valueA === null) return 1;
      if (valueB === null) return -1;
      
      // Special case for dates
      if (sortBy === 'lastActivity') {
        valueA = valueA ? (valueA as Date).getTime() : 0;
        valueB = valueB ? (valueB as Date).getTime() : 0;
      }
      
      const comparison = valueA < valueB ? -1 : valueA > valueB ? 1 : 0;
      return sortDirection === 'asc' ? comparison : -comparison;
    });
  }, [userMetrics, sortBy, sortDirection]);
  
  // Toggle user selection
  const toggleUserSelection = (username: string) => {
    setSelectedUsernames(prev => {
      if (prev.includes(username)) {
        return prev.filter(u => u !== username);
      } else {
        return [...prev, username];
      }
    });
  };
  
  // Toggle sort direction or set new sort field
  const toggleSort = (field: keyof UserActivityMetrics) => {
    if (sortBy === field) {
      setSortDirection(prev => (prev === 'asc' ? 'desc' : 'asc'));
    } else {
      setSortBy(field);
      setSortDirection('desc');
    }
  };
  
  // Format relative time from Date
  const formatRelativeTime = (date: Date | null): string => {
    if (!date) return 'Never';
    
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffSecs < 60) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return date.toLocaleDateString();
  };
  
  // Get risk level label and color
  const getRiskLevelInfo = (score: number): { 
    label: string; 
    color: string; 
    bgColor: string;
    textColor: string;
  } => {
    if (score >= 75) {
      return { 
        label: 'High Risk', 
        color: 'border-red-500', 
        bgColor: 'bg-red-100 dark:bg-red-900/30',
        textColor: 'text-red-700 dark:text-red-300'
      };
    } else if (score >= 40) {
      return { 
        label: 'Medium Risk', 
        color: 'border-amber-500', 
        bgColor: 'bg-amber-100 dark:bg-amber-900/30',
        textColor: 'text-amber-700 dark:text-amber-300'
      };
    } else {
      return { 
        label: 'Low Risk', 
        color: 'border-green-500', 
        bgColor: 'bg-green-100 dark:bg-green-900/30',
        textColor: 'text-green-700 dark:text-green-300'
      };
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <button 
          onClick={onBack}
          className="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          <span>Back to All Logs</span>
        </button>
      </div>
      
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100 flex items-center gap-2 mb-6">
          <Users className="h-5 w-5 text-blue-500" />
          User Activity Comparison
        </h2>
        
        {/* Filters and controls */}
        <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
          <div className="flex flex-wrap gap-2 items-center">
            <label className="text-sm text-gray-600 dark:text-gray-300">Department:</label>
            <select
              value={departmentFilter}
              onChange={(e) => setDepartmentFilter(e.target.value)}
              className="border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 text-sm bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
            >
              {uniqueDepartments.map(dept => (
                <option key={dept} value={dept}>{dept}</option>
              ))}
            </select>
          </div>
          
          <div className="flex gap-2">
            <button
              className={`flex items-center gap-1 px-3 py-1.5 text-sm rounded-md transition-colors ${
                showRiskScoring 
                ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900/40 dark:text-indigo-300' 
                : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
              }`}
              onClick={() => setShowRiskScoring(!showRiskScoring)}
            >
              <Shield className="h-4 w-4" />
              {showRiskScoring ? 'Hide Risk Scoring' : 'Show Risk Scoring'}
            </button>
            
            <button
              className="flex items-center gap-1 px-3 py-1.5 text-sm rounded-md bg-blue-100 text-blue-700 dark:bg-blue-900/40 dark:text-blue-300 transition-colors"
              onClick={() => {
                if (selectedUsernames.length > 0) {
                  // If only one user is selected, go to their detail view
                  if (selectedUsernames.length === 1) {
                    onViewUserDetails(selectedUsernames[0]);
                  } else {
                    // In a real implementation, this would show a comparison of just the selected users
                    alert(`Compare: ${selectedUsernames.join(', ')}`);
                  }
                }
              }}
              disabled={selectedUsernames.length === 0}
            >
              <BarChart2 className="h-4 w-4" />
              Compare Selected ({selectedUsernames.length})
            </button>
          </div>
        </div>
        
        {/* Metrics table */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-4 py-3 text-center">
                  <span className="sr-only">Select</span>
                </th>
                <th 
                  className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700/50"
                  onClick={() => toggleSort('username')}
                >
                  <div className="flex items-center gap-1">
                    User
                    {sortBy === 'username' && (
                      <ArrowUpDown className="h-3 w-3" />
                    )}
                  </div>
                </th>
                <th 
                  className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700/50"
                  onClick={() => toggleSort('department')}
                >
                  <div className="flex items-center gap-1">
                    Department
                    {sortBy === 'department' && (
                      <ArrowUpDown className="h-3 w-3" />
                    )}
                  </div>
                </th>
                <th 
                  className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700/50"
                  onClick={() => toggleSort('totalActivities')}
                >
                  <div className="flex items-center gap-1">
                    Activities
                    {sortBy === 'totalActivities' && (
                      <ArrowUpDown className="h-3 w-3" />
                    )}
                  </div>
                </th>
                <th 
                  className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700/50"
                  onClick={() => toggleSort('successfulLogins')}
                >
                  <div className="flex items-center gap-1">
                    Logins
                    {sortBy === 'successfulLogins' && (
                      <ArrowUpDown className="h-3 w-3" />
                    )}
                  </div>
                </th>
                <th 
                  className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700/50"
                  onClick={() => toggleSort('failedLogins')}
                >
                  <div className="flex items-center gap-1">
                    Failed
                    {sortBy === 'failedLogins' && (
                      <ArrowUpDown className="h-3 w-3" />
                    )}
                  </div>
                </th>
                <th
                  className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700/50"
                  onClick={() => toggleSort('averageSessionDuration')}
                >
                  <div className="flex items-center gap-1">
                    Avg. Session
                    {sortBy === 'averageSessionDuration' && (
                      <ArrowUpDown className="h-3 w-3" />
                    )}
                  </div>
                </th>
                <th
                  className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700/50"
                  onClick={() => toggleSort('lastActivity')}
                >
                  <div className="flex items-center gap-1">
                    Last Activity
                    {sortBy === 'lastActivity' && (
                      <ArrowUpDown className="h-3 w-3" />
                    )}
                  </div>
                </th>
                {showRiskScoring && (
                  <th
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700/50"
                    onClick={() => toggleSort('riskScore')}
                  >
                    <div className="flex items-center gap-1">
                      Risk Score
                      {sortBy === 'riskScore' && (
                        <ArrowUpDown className="h-3 w-3" />
                      )}
                    </div>
                  </th>
                )}
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Activity Types
                </th>
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {sortedMetrics.map((metrics) => {
                const { label, color, bgColor, textColor } = getRiskLevelInfo(metrics.riskScore);
                return (
                  <tr key={metrics.username} className="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                    <td className="px-4 py-4 whitespace-nowrap text-center">
                      <div 
                        className={`w-5 h-5 border rounded flex items-center justify-center cursor-pointer ${
                          selectedUsernames.includes(metrics.username) 
                            ? 'bg-blue-500 border-blue-500 dark:bg-blue-600 dark:border-blue-600' 
                            : 'border-gray-300 dark:border-gray-600'
                        }`}
                        onClick={() => toggleUserSelection(metrics.username)}
                      >
                        {selectedUsernames.includes(metrics.username) && (
                          <Check className="h-3 w-3 text-white" />
                        )}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div 
                        className="font-medium text-sm text-gray-900 dark:text-white cursor-pointer hover:text-blue-600 dark:hover:text-blue-400"
                        onClick={() => onViewUserDetails(metrics.username)}
                      >
                        {metrics.username}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                      {metrics.department}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-gray-200">
                      {metrics.totalActivities}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-1">
                        <span className="text-green-600 dark:text-green-400">
                          <Activity className="h-4 w-4" />
                        </span>
                        <span className="text-sm text-gray-800 dark:text-gray-200">
                          {metrics.successfulLogins}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="flex items-center gap-1">
                        <span className="text-red-600 dark:text-red-400">
                          <XCircle className="h-4 w-4" />
                        </span>
                        <span className="text-sm text-gray-800 dark:text-gray-200">
                          {metrics.failedLogins}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                      {metrics.averageSessionDuration !== null
                        ? `${metrics.averageSessionDuration} min`
                        : '-'}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                      {metrics.lastActivity
                        ? formatRelativeTime(metrics.lastActivity)
                        : '-'}
                    </td>
                    {showRiskScoring && (
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex items-center gap-2">
                          <div className={`w-10 text-center rounded-full px-2 py-0.5 text-xs font-medium ${bgColor} ${textColor}`}>
                            {metrics.riskScore}
                          </div>
                          <div className={`text-xs font-medium ${textColor}`}>
                            {label}
                          </div>
                        </div>
                      </td>
                    )}
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 overflow-hidden">
                        {Object.entries(metrics.activityDistribution).map(([type, count], index) => {
                          if (count === 0) return null;
                          
                          // Calculate percentage of this activity type
                          const percentage = (count / metrics.totalActivities) * 100;
                          
                          // Pick a color based on activity type
                          let color;
                          switch (type) {
                            case 'login': color = 'bg-green-500'; break;
                            case 'logout': color = 'bg-blue-500'; break;
                            case 'error': color = 'bg-red-500'; break;
                            case 'warning': color = 'bg-amber-500'; break;
                            case 'success': color = 'bg-emerald-500'; break;
                            default: color = 'bg-indigo-500'; break;
                          }
                          
                          return (
                            <div 
                              key={type}
                              className={`${color} h-full float-left`} 
                              style={{ width: `${percentage}%` }}
                              title={`${type}: ${count} (${percentage.toFixed(1)}%)`}
                            />
                          );
                        })}
                      </div>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => onViewUserDetails(metrics.username)}
                        className="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        View Details
                      </button>
                    </td>
                  </tr>
                );
              })}
              
              {sortedMetrics.length === 0 && (
                <tr>
                  <td colSpan={showRiskScoring ? 11 : 10} className="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                    <Users className="h-12 w-12 mx-auto text-gray-400 mb-2" />
                    <p>No users found matching the current filter.</p>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}; 