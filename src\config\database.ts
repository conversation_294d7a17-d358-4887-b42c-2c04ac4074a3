import { DataSource } from 'typeorm';
import { User } from '../entities/User';
import { Ticket } from '../entities/Ticket';
import { Comment } from '../entities/Comment';
import { Attachment } from '../entities/Attachment';
import { KnowledgeBase } from '../entities/KnowledgeBase';
import { KnowledgeCategory } from '../entities/KnowledgeCategory';
import { KnowledgeTag } from '../entities/KnowledgeTag';
import { Asset } from '../entities/Asset';
import { AssetMaintenance } from '../entities/AssetMaintenance';
import { Vendor } from '../entities/Vendor';
import { PrinterMaintenance } from '../entities/PrinterMaintenance';
import { SoftwareLicense } from '../entities/SoftwareLicense';
import { EmailAccount } from '../entities/EmailAccount';
import { BillingInvoice } from '../entities/BillingInvoice';
import { ITOperationLog } from '../entities/ITOperationLog';
import { SystemLog } from '../entities/SystemLog';
import { Role } from '../entities/Role';
import { RoleTemplate } from '../entities/RoleTemplate';
import { PermissionGroup } from '../entities/PermissionGroup';
import { UserRoleAssignment } from '../entities/UserRoleAssignment';
import { Employee } from '../server/entities/Employee';
import { EmployeeContact } from '../server/entities/EmployeeContact';
import { EmployeeJob } from '../server/entities/EmployeeJob';
import { EmployeeEducation } from '../server/entities/EmployeeEducation';
import { EmployeeExperience } from '../server/entities/EmployeeExperience';
import { EmployeeFamily } from '../server/entities/EmployeeFamily';
import { EmployeeDocument } from '../server/entities/EmployeeDocument';
import { EmployeeBenefit } from '../server/entities/EmployeeBenefit';
import { EmployeeDevice } from '../server/entities/EmployeeDevice';
import { EmployeeProject } from '../server/entities/EmployeeProject';
import { EmployeeSkill } from '../server/entities/EmployeeSkill';
import { EmployeeVehicle } from '../server/entities/EmployeeVehicle';
import { EmployeeHealth } from '../server/entities/EmployeeHealth';
import { Attendance } from '../server/entities/Attendance';
import { Regularization } from '../server/entities/Regularization';
import { Shift } from '../server/entities/Shift';
import { ShiftAssignment } from '../server/entities/ShiftAssignment';
import { UserRole } from '../types/common';
import logger from '../utils/logger';
import mysql from 'mysql2/promise';
import 'reflect-metadata';
import dotenv from 'dotenv';
import { Holiday } from '../entities/Holiday';
import { HolidayConfiguration } from '../entities/HolidayConfiguration';
import { LeaveRequest } from '../entities/LeaveRequest';
import { LeaveApproval } from '../entities/LeaveApproval';
import { LeaveBalance } from '../entities/LeaveBalance';
import { LeaveAllocation } from '../entities/LeaveAllocation';
import { JobPosting } from '../entities/JobPosting';
import { JobApplication } from '../entities/JobApplication';
import { Interview } from '../entities/Interview';
import { ApplicationEvaluation } from '../entities/ApplicationEvaluation';
import { InterviewFeedback } from '../entities/InterviewFeedback';

import { LeavePolicyConfiguration } from '../entities/LeavePolicyConfiguration';
import { LeaveTypePolicy } from '../entities/LeaveTypePolicy';

import { HolidayCalendar } from '../entities/HolidayCalendar';
import { Task } from '../entities/Task';
import { TaskComment } from '../entities/TaskComment';
import { TaskAttachment } from '../entities/TaskAttachment';
import { TaskTimeEntry } from '../entities/TaskTimeEntry';
import { TaskTemplate } from '../entities/TaskTemplate';
import { Project } from '../entities/Project';
import { ProjectMember } from '../entities/ProjectMember';

// Load environment variables
dotenv.config();

const {
  DB_HOST = 'localhost',
  DB_PORT = '3306',
  DB_USER = 'root',
  DB_PASSWORD = 'root',
  DB_NAME = 'ims_db'
} = process.env;

export const AppDataSource = new DataSource({
  type: 'mysql',
  host: DB_HOST,
  port: parseInt(DB_PORT, 10),
  username: DB_USER,
  password: DB_PASSWORD,
  database: DB_NAME,
  entities: [
    User, 
    Ticket, 
    Comment, 
    Attachment, 
    KnowledgeBase, 
    KnowledgeCategory, 
    KnowledgeTag,
    Asset,
    AssetMaintenance,
    Vendor,
    PrinterMaintenance,
    SoftwareLicense,
    EmailAccount,
    BillingInvoice,
    ITOperationLog,
    SystemLog,
    Role,
    RoleTemplate,
    PermissionGroup,
    UserRoleAssignment,
    Employee,
    EmployeeContact,
    EmployeeJob,
    EmployeeEducation,
    EmployeeExperience,
    EmployeeFamily,
    EmployeeDocument,
    EmployeeBenefit,
    EmployeeDevice,
    EmployeeProject,
    EmployeeSkill,
    EmployeeVehicle,
    EmployeeHealth,
    Attendance,
    Regularization,
    Shift,
    ShiftAssignment,
    Holiday,
    HolidayConfiguration,
    LeaveRequest,
    LeaveApproval,
    LeaveBalance,
    LeaveAllocation,
    JobPosting,
    JobApplication,
    Interview,
    ApplicationEvaluation,
    InterviewFeedback,

    LeavePolicyConfiguration,
    LeaveTypePolicy,
    HolidayCalendar,
    Task,
    TaskComment,
    TaskAttachment,
    TaskTimeEntry,
    TaskTemplate,
    Project,
    ProjectMember
  ],
  migrations: [__dirname + '/../migrations/*.ts'],
  synchronize: false,
  // Professional logging configuration
  logging: process.env.NODE_ENV === 'production' 
    ? ['error'] // Production: Only errors
    : process.env.DB_DEBUG === 'true' 
      ? true // Development with DB_DEBUG=true: All queries
      : ['error', 'warn'], // Development default: Errors and warnings only
  logger: 'advanced-console',
  connectTimeout: 30000, // 30 seconds
  maxQueryExecutionTime: 10000, // Log slow queries (>10s)
  timezone: 'Z', // Set timezone to UTC
  extra: {
    // Connection pool configuration
    connectionLimit: 10  // Maximum number of connections in the pool
  }
});

export const initializeDatabase = async () => {
  try {
    logger.info('Starting database initialization...', {
      host: DB_HOST,
      port: DB_PORT,
      database: DB_NAME
    });
    
    // First, create a MySQL connection without database
    const connection = await mysql.createConnection({
      host: DB_HOST,
      port: parseInt(DB_PORT, 10),
      user: DB_USER,
      password: DB_PASSWORD
    });

    // Create database if it doesn't exist
    await connection.query(`CREATE DATABASE IF NOT EXISTS ${DB_NAME}`);
    logger.info('Database created or already exists');

    // Close the connection
    await connection.end();

    // Initialize TypeORM connection with retries
    let retries = 5;
    while (retries > 0) {
      try {
        await AppDataSource.initialize();
        logger.info('Database connection established successfully');
        return true;
      } catch (error) {
        retries--;
        if (retries === 0) {
          throw error;
        }
        logger.warn(`Failed to connect to database. Retries left: ${retries}`, { error });
        // Wait for 5 seconds before retrying
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
  } catch (error) {
    logger.error('Fatal error during database initialization:', error);
    throw error;
  }
}; 