import { AppDataSource, initializeDatabase } from '../../config/database';
import { User } from '../../entities/User';

async function checkAdmin() {
  try {
    await initializeDatabase();
    console.log('Database connection initialized');

    const userRepository = AppDataSource.getRepository(User);
    
    // Find the admin user
    const adminUser = await userRepository.findOne({
      where: { email: '<EMAIL>' }
    });

    console.log('Admin user details:', adminUser);

  } catch (error) {
    console.error('Error checking admin:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('Database connection closed');
    }
  }
}

checkAdmin().then(() => process.exit(0)).catch(() => process.exit(1)); 