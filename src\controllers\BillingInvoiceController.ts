import { Request, Response } from 'express';
import { BillingInvoiceService, BillingInvoiceFormData } from '../services/BillingInvoiceService';
import { ApprovalStatus } from '../entities/BillingInvoice';

export const BillingInvoiceController = {
  // Get all invoices with pagination, search, and filters
  async getAllInvoices(req: Request, res: Response): Promise<void> {
    try {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;
      const search = req.query.search as string || '';
      
      // Extract filters from query params
      const filters: Record<string, any> = {};
      
      if (req.query.department) {
        filters.department = req.query.department as string;
      }
      
      if (req.query.billingCategory) {
        filters.billingCategory = req.query.billingCategory as string;
      }
      
      if (req.query.approvalStatus) {
        filters.approvalStatus = req.query.approvalStatus as string;
      }
      
      if (req.query.isRecurring !== undefined) {
        filters.isRecurring = req.query.isRecurring === 'true';
      }
      
      const result = await BillingInvoiceService.getAllInvoices(page, limit, search, filters);
      
      res.status(200).json({
        success: true,
        data: result.data,
        pagination: {
          total: result.total,
          page,
          limit,
          totalPages: Math.ceil(result.total / limit)
        }
      });
    } catch (error) {
      console.error('Error in getAllInvoices controller:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch invoices',
        error: (error as Error).message
      });
    }
  },
  
  // Get invoice by ID
  async getInvoiceById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const invoice = await BillingInvoiceService.getInvoiceById(id);
      
      res.status(200).json({
        success: true,
        data: invoice
      });
    } catch (error) {
      console.error(`Error in getInvoiceById controller for ID ${req.params.id}:`, error);
      
      if ((error as Error).message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: `Invoice with ID ${req.params.id} not found`,
          error: (error as Error).message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Failed to fetch invoice',
          error: (error as Error).message
        });
      }
    }
  },
  
  // Create a new invoice
  async createInvoice(req: Request, res: Response): Promise<void> {
    try {
      console.log('Received create invoice request:', {
        body: req.body,
        user: req.user?.id,
        hasFileUrl: !!req.body.invoiceFileUrl
      });
      
      const formData: BillingInvoiceFormData = req.body;
      
      // Set current user as creator/modifier
      const userId = req.user?.id || 'system'; // Assuming user is attached to request by auth middleware
      formData.createdBy = userId;
      formData.lastModifiedBy = userId;
      formData.lastModifiedDate = new Date().toISOString();
      
      // Ensure invoiceFileUrl is set to empty string if not provided
      formData.invoiceFileUrl = formData.invoiceFileUrl || '';
      
      // Ensure numeric values are properly parsed
      formData.amount = typeof formData.amount === 'string' ? parseFloat(formData.amount) : (formData.amount || 0);
      formData.tax = typeof formData.tax === 'string' ? parseFloat(formData.tax) : (formData.tax || 0);
      formData.totalAmount = typeof formData.totalAmount === 'string' ? parseFloat(formData.totalAmount) : (formData.totalAmount || 0);
      
      // Validate dates
      try {
        console.log('Validating dates in controller:', {
          invoiceDate: formData.invoiceDate,
          dueDate: formData.dueDate,
          invoiceDateType: typeof formData.invoiceDate,
          dueDateType: typeof formData.dueDate
        });
        
        if (formData.invoiceDate) {
          // Ensure date is in correct format
          const invoiceDate = new Date(formData.invoiceDate);
          if (isNaN(invoiceDate.getTime())) {
            throw new Error('Invalid invoice date format');
          }
          console.log('Validated invoice date:', invoiceDate.toISOString());
        }
        
        if (formData.dueDate) {
          // Ensure date is in correct format
          const dueDate = new Date(formData.dueDate);
          if (isNaN(dueDate.getTime())) {
            throw new Error('Invalid due date format');
          }
          console.log('Validated due date:', dueDate.toISOString());
        }
      } catch (dateError) {
        console.error('Date validation error:', dateError);
        res.status(400).json({
          success: false,
          message: 'Invalid date format',
          error: dateError instanceof Error ? dateError.message : 'Unknown date error'
        });
        return;
      }
      
      const invoice = await BillingInvoiceService.createInvoice(formData);
      
      res.status(201).json({
        success: true,
        message: 'Invoice created successfully',
        data: invoice
      });
    } catch (error) {
      console.error('Error in createInvoice controller:', error);
      
      // Handle different error cases
      if (error instanceof Error && error.message.includes('already exists')) {
        res.status(409).json({
          success: false,
          message: error.message,
          error: 'Duplicate invoice number'
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Failed to create invoice',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  },
  
  // Update an existing invoice
  async updateInvoice(req: Request, res: Response): Promise<void> {
    try {
      console.log('Received update invoice request:', {
        id: req.params.id,
        body: req.body,
        user: req.user?.id,
        hasFileUrl: !!req.body.invoiceFileUrl,
        fileUrlIfPresent: req.body.invoiceFileUrl ? req.body.invoiceFileUrl.substring(0, 50) + '...' : 'none'
      });
      
      const { id } = req.params;
      const formData: BillingInvoiceFormData = req.body;
      
      // Set current user as modifier
      const userId = req.user?.id || 'system'; // Assuming user is attached to request by auth middleware
      formData.lastModifiedBy = userId;
      formData.lastModifiedDate = new Date().toISOString();
      
      // Ensure invoiceFileUrl is set to empty string if not provided
      formData.invoiceFileUrl = formData.invoiceFileUrl || '';
      
      // Ensure numeric values are properly parsed
      formData.amount = typeof formData.amount === 'string' ? parseFloat(formData.amount) : (formData.amount || 0);
      formData.tax = typeof formData.tax === 'string' ? parseFloat(formData.tax) : (formData.tax || 0);
      formData.totalAmount = typeof formData.totalAmount === 'string' ? parseFloat(formData.totalAmount) : (formData.totalAmount || 0);
      
      // Validate dates
      try {
        console.log('Validating dates in controller:', {
          invoiceDate: formData.invoiceDate,
          dueDate: formData.dueDate,
          invoiceDateType: typeof formData.invoiceDate,
          dueDateType: typeof formData.dueDate
        });
        
        if (formData.invoiceDate) {
          // Ensure date is in correct format
          const invoiceDate = new Date(formData.invoiceDate);
          if (isNaN(invoiceDate.getTime())) {
            throw new Error('Invalid invoice date format');
          }
          console.log('Validated invoice date:', invoiceDate.toISOString());
        }
        
        if (formData.dueDate) {
          // Ensure date is in correct format
          const dueDate = new Date(formData.dueDate);
          if (isNaN(dueDate.getTime())) {
            throw new Error('Invalid due date format');
          }
          console.log('Validated due date:', dueDate.toISOString());
        }
      } catch (dateError) {
        console.error('Date validation error:', dateError);
        res.status(400).json({
          success: false,
          message: 'Invalid date format',
          error: dateError instanceof Error ? dateError.message : 'Unknown date error'
        });
        return;
      }
      
      const invoice = await BillingInvoiceService.updateInvoice(id, formData);
      
      res.status(200).json({
        success: true,
        message: 'Invoice updated successfully',
        data: invoice
      });
    } catch (error) {
      console.error(`Error in updateInvoice controller for ID ${req.params.id}:`, error);
      
      if (error instanceof Error && error.message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: `Invoice with ID ${req.params.id} not found`,
          error: error.message
        });
      } else if (error instanceof Error && error.message.includes('already exists')) {
        res.status(409).json({
          success: false,
          message: error.message,
          error: 'Duplicate invoice number'
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Failed to update invoice',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  },
  
  // Delete an invoice
  async deleteInvoice(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const result = await BillingInvoiceService.deleteInvoice(id);
      
      if (result) {
        res.status(200).json({
          success: true,
          message: 'Invoice deleted successfully'
        });
      } else {
        res.status(404).json({
          success: false,
          message: `Invoice with ID ${id} not found`
        });
      }
    } catch (error) {
      console.error(`Error in deleteInvoice controller for ID ${req.params.id}:`, error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete invoice',
        error: (error as Error).message
      });
    }
  },
  
  // Update approval status
  async updateApprovalStatus(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { status } = req.body;
      
      if (!Object.values(ApprovalStatus).includes(status as ApprovalStatus)) {
        res.status(400).json({
          success: false,
          message: `Invalid approval status: ${status}. Must be one of: ${Object.values(ApprovalStatus).join(', ')}`
        });
        return;
      }
      
      // Set current user as approver
      const userId = req.user?.id || 'system'; // Assuming user is attached to request by auth middleware
      
      const invoice = await BillingInvoiceService.updateApprovalStatus(
        id, 
        status as ApprovalStatus,
        status === ApprovalStatus.APPROVED ? userId : undefined
      );
      
      res.status(200).json({
        success: true,
        message: `Invoice approval status updated to ${status}`,
        data: invoice
      });
    } catch (error) {
      console.error(`Error in updateApprovalStatus controller for ID ${req.params.id}:`, error);
      
      if ((error as Error).message.includes('not found')) {
        res.status(404).json({
          success: false,
          message: `Invoice with ID ${req.params.id} not found`,
          error: (error as Error).message
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Failed to update approval status',
          error: (error as Error).message
        });
      }
    }
  }
};

export default BillingInvoiceController; 