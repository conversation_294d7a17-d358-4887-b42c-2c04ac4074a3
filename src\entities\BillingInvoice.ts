import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  ManyToMany,
  JoinTable
} from 'typeorm';
import { Vendor } from './Vendor';
import { Asset } from './Asset';
import { SoftwareLicense } from './SoftwareLicense';
import { User } from './User';

// Define the approval status enum
export enum ApprovalStatus {
  PENDING = 'Pending',
  APPROVED = 'Approved',
  REJECTED = 'Rejected'
}

// Define the billing frequency enum
export enum BillingFrequency {
  MONTHLY = 'Monthly',
  YEARLY = 'Yearly',
  ONE_TIME = 'One-Time'
}

@Entity('billing_invoices')
export class BillingInvoice {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  invoiceNumber: string;

  @Column()
  vendorName: string;

  @ManyToOne(() => Vendor, { nullable: true })
  @JoinColumn({ name: 'vendor_id' })
  vendor: Vendor;

  @Column({ type: 'varchar' })
  serviceProduct: string;

  @Column({ type: 'varchar' })
  department: string;

  @Column({ type: 'varchar' })
  billingCategory: string;

  @Column({ type: 'date' })
  invoiceDate: Date;

  @Column({ type: 'date' })
  dueDate: Date;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ type: 'varchar', length: 3, default: 'USD' })
  currency: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  tax: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  totalAmount: number;

  @Column({ type: 'varchar', nullable: true })
  paymentMethod: string;

  @Column({ type: 'varchar', nullable: true })
  accountTitle: string;

  @Column({ type: 'varchar', nullable: true })
  mobileNumber: string;
  
  @Column({ type: 'varchar', nullable: true })
  checkNumber: string;
  
  @Column({ type: 'varchar', nullable: true })
  bankName: string;
  
  @Column({ type: 'varchar', nullable: true })
  receivedBy: string;
  
  @Column({ type: 'varchar', nullable: true })
  receiptNumber: string;
  
  @Column({ type: 'text', nullable: true })
  paymentNotes: string;

  @Column({ type: 'varchar', nullable: true })
  accountNumber: string;

  @Column({ type: 'varchar', nullable: true })
  cardNumber: string;

  @Column({ type: 'varchar', nullable: true })
  expiryDate: string;

  @Column({ type: 'varchar', nullable: true })
  cvv: string;

  @Column({ type: 'varchar', nullable: true })
  invoiceFileUrl: string;

  @Column({ type: 'text', nullable: false })
  notes: string;

  @Column({ type: 'boolean', default: false })
  isRecurring: boolean;

  @Column({
    type: 'enum',
    enum: BillingFrequency,
    nullable: true
  })
  billingFrequency: BillingFrequency;

  @Column({ type: 'int', nullable: true })
  reminderDays: number;

  @ManyToMany(() => Asset)
  @JoinTable({
    name: 'billing_invoice_assets',
    joinColumn: { name: 'invoice_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'asset_id', referencedColumnName: 'id' }
  })
  linkedAssets: Asset[];

  @ManyToMany(() => SoftwareLicense)
  @JoinTable({
    name: 'billing_invoice_software',
    joinColumn: { name: 'invoice_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'software_id', referencedColumnName: 'id' }
  })
  linkedSoftware: SoftwareLicense[];

  @ManyToMany(() => User)
  @JoinTable({
    name: 'billing_invoice_users',
    joinColumn: { name: 'invoice_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'user_id', referencedColumnName: 'id' }
  })
  assignedUsers: User[];

  @Column({
    type: 'enum',
    enum: ApprovalStatus,
    default: ApprovalStatus.PENDING
  })
  approvalStatus: ApprovalStatus;

  @Column({ type: 'varchar' })
  createdBy: string;

  @Column({ type: 'varchar', nullable: true })
  approvedBy: string;

  @Column({ type: 'varchar' })
  lastModifiedBy: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 