import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Palette, Keyboard } from 'lucide-react';
import { useTheme, ThemeMode, ThemeColor, KeyboardShortcut } from './ThemeContext';

interface ThemeSettingsProps {
  onClose?: () => void;
}

export const ThemeSettings: React.FC<ThemeSettingsProps> = ({ onClose }) => {
  const { theme, setTheme, shortcuts } = useTheme();
  const [activeTab, setActiveTab] = useState<'theme' | 'shortcuts'>('theme');

  const themeOptions: { value: ThemeMode; label: string; icon: React.ReactNode }[] = [
    { value: 'light', label: 'Light', icon: <Sun className="h-4 w-4" /> },
    { value: 'dark', label: 'Dark', icon: <Moon className="h-4 w-4" /> },
    { value: 'system', label: 'System', icon: <Monitor className="h-4 w-4" /> },
  ];

  const colorOptions: { value: ThemeColor; label: string; colorClass: string }[] = [
    { value: 'blue', label: 'Blue', colorClass: 'bg-blue-600' },
    { value: 'purple', label: 'Purple', colorClass: 'bg-purple-600' },
    { value: 'green', label: 'Green', colorClass: 'bg-emerald-600' },
    { value: 'red', label: 'Red', colorClass: 'bg-red-600' },
    { value: 'orange', label: 'Orange', colorClass: 'bg-orange-600' },
  ];

  const fontSizeOptions = [
    { value: 'sm', label: 'Small' },
    { value: 'md', label: 'Medium' },
    { value: 'lg', label: 'Large' },
  ];

  return (
    <div className="p-4 max-w-md w-full rounded-lg shadow-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">Settings</h3>
        <button 
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <span className="sr-only">Close</span>
          <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      </div>

      <div className="flex border-b border-gray-200 dark:border-gray-700 mb-4">
        <button
          className={`py-2 px-4 ${
            activeTab === 'theme'
              ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400'
              : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
          }`}
          onClick={() => setActiveTab('theme')}
        >
          <div className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            <span>Appearance</span>
          </div>
        </button>
        <button
          className={`py-2 px-4 ${
            activeTab === 'shortcuts'
              ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400'
              : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
          }`}
          onClick={() => setActiveTab('shortcuts')}
        >
          <div className="flex items-center gap-2">
            <Keyboard className="h-4 w-4" />
            <span>Shortcuts</span>
          </div>
        </button>
      </div>

      {activeTab === 'theme' ? (
        <div className="space-y-6">
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Theme Mode</h4>
            <div className="grid grid-cols-3 gap-2">
              {themeOptions.map((option) => (
                <button
                  key={option.value}
                  className={`flex items-center justify-center gap-2 px-3 py-2 rounded-md text-sm ${
                    theme.mode === option.value
                      ? 'bg-blue-50 text-blue-700 dark:bg-blue-900 dark:text-blue-200 border border-blue-200 dark:border-blue-700'
                      : 'bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 border border-gray-200 dark:border-gray-600'
                  }`}
                  onClick={() => setTheme({ ...theme, mode: option.value })}
                >
                  {option.icon}
                  {option.label}
                </button>
              ))}
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Accent Color</h4>
            <div className="grid grid-cols-5 gap-2">
              {colorOptions.map((option) => (
                <button
                  key={option.value}
                  className={`flex items-center justify-center h-10 rounded-md ${
                    theme.color === option.value
                      ? 'ring-2 ring-offset-2 ring-blue-500 dark:ring-blue-400'
                      : ''
                  }`}
                  onClick={() => setTheme({ ...theme, color: option.value })}
                  title={option.label}
                >
                  <div className={`h-6 w-6 rounded-full ${option.colorClass}`}></div>
                </button>
              ))}
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Font Size</h4>
            <div className="grid grid-cols-3 gap-2">
              {fontSizeOptions.map((option) => (
                <button
                  key={option.value}
                  className={`px-3 py-2 rounded-md text-sm ${
                    theme.fontSize === option.value
                      ? 'bg-blue-50 text-blue-700 dark:bg-blue-900 dark:text-blue-200 border border-blue-200 dark:border-blue-700'
                      : 'bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 border border-gray-200 dark:border-gray-600'
                  }`}
                  onClick={() => setTheme({ ...theme, fontSize: option.value as any })}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Keyboard Shortcuts</h4>
          
          {shortcuts.length > 0 ? (
            <div className="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Shortcut
                    </th>
                    <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Description
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-700 divide-y divide-gray-200 dark:divide-gray-600">
                  {shortcuts.map((shortcut, index) => (
                    <tr key={index}>
                      <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                        <div className="flex items-center gap-1">
                          {shortcut.ctrl && <kbd className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded">Ctrl</kbd>}
                          {shortcut.alt && <kbd className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded">Alt</kbd>}
                          {shortcut.shift && <kbd className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded">Shift</kbd>}
                          <kbd className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded">{shortcut.key.toUpperCase()}</kbd>
                        </div>
                      </td>
                      <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                        {shortcut.description}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center p-4 text-gray-500 dark:text-gray-400">
              No keyboard shortcuts registered.
            </div>
          )}

          <div className="mt-4 bg-blue-50 dark:bg-blue-900 p-3 rounded-md">
            <p className="text-sm text-blue-700 dark:text-blue-200">
              <span className="font-medium">Default shortcuts:</span>
            </p>
            <ul className="mt-2 space-y-1 text-sm text-blue-700 dark:text-blue-200">
              <li>• <kbd className="px-1 py-0.5 text-xs bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded">F</kbd>: Focus search</li>
              <li>• <kbd className="px-1 py-0.5 text-xs bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded">R</kbd>: Refresh logs</li>
              <li>• <kbd className="px-1 py-0.5 text-xs bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded">T</kbd>: Toggle view mode</li>
              <li>• <kbd className="px-1 py-0.5 text-xs bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded">D</kbd>: Toggle dark mode</li>
              <li>• <kbd className="px-1 py-0.5 text-xs bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded">Esc</kbd>: Close modals</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}; 