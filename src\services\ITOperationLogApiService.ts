import api, { safelyHandleResponse } from './api';
import { ITOperationLogFormData } from './ITOperationLogService';

const ITOperationLogApiService = {
  // Get all operation logs with optional filtering and pagination
  getAllLogs: async (options?: {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
    search?: string;
    issueCategory?: string;
    status?: string;
    impactLevel?: string;
    dateFrom?: string;
    dateTo?: string;
    tags?: string | string[];
  }) => {
    const params = new URLSearchParams();
    
    // Add all options to query params
    if (options) {
      Object.entries(options).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (Array.isArray(value)) {
            value.forEach(v => params.append(`${key}[]`, v));
          } else {
            params.append(key, value.toString());
          }
        }
      });
    }
    
    return await safelyHandleResponse(
      api.get(`/it-logs?${params.toString()}`)
    );
  },
  
  // Get a single operation log by ID
  getLogById: async (id: string) => {
    console.log('API Service: Getting log by ID:', id);
    try {
      const response = await safelyHandleResponse(
        api.get(`/it-logs/${id}`)
      );
      
      // Check if the response follows your backend format
      if (response.data && response.data.success && response.data.data) {
        // Return just the log data portion
        return { data: response.data.data, error: null };
      }
      
      console.error('Unexpected API response format:', response);
      return { data: null, error: 'Invalid response format from server' };
    } catch (error) {
      console.error('Error in getLogById:', error);
      return { data: null, error: 'Failed to fetch log details' };
    }
  },
  
  // Create a new operation log with debugging
  createLog: async (logData: ITOperationLogFormData) => {
    try {
      console.log('Submitting log data to server:', JSON.stringify(logData, null, 2));
      
      // Create a simpler version of data to send
      const sanitizedData = {
        title: logData.title,
        description: logData.description,
        issueCategory: logData.issueCategory,
        loggedBy: logData.loggedBy,
        impactLevel: logData.impactLevel,
        status: logData.status,
        dateOccurred: logData.dateOccurred,
        // Only include optional fields if they have values
        ...(logData.assignedTo ? { assignedTo: logData.assignedTo } : {}),
        ...(logData.location ? { location: logData.location } : {}),
        ...(logData.dateResolved ? { dateResolved: logData.dateResolved } : {}),
        ...(logData.tags ? { tags: logData.tags } : {}),
        ...(logData.externalLinks ? { externalLinks: logData.externalLinks } : {}),
        ...(logData.attachment ? { attachment: logData.attachment } : {}),
        ...(logData.notes ? { notes: logData.notes } : {})
      };
      
      console.log('Sending sanitized data:', JSON.stringify(sanitizedData, null, 2));
      
      return await safelyHandleResponse(
        api.post('/it-logs', sanitizedData)
      );
    } catch (error) {
      console.error('Error in createLog API service:', error);
      return { data: null, error: 'Failed to create log. See console for details.' };
    }
  },
  
  // Update an existing operation log
  updateLog: async (id: string, logData: ITOperationLogFormData) => {
    return await safelyHandleResponse(
      api.put(`/it-logs/${id}`, logData)
    );
  },
  
  // Delete an operation log
  deleteLog: async (id: string) => {
    return await safelyHandleResponse(
      api.delete(`/it-logs/${id}`)
    );
  },
  
  // Upload an attachment for an operation log
  uploadAttachment: async (file: File) => {
    try {
      console.log('Uploading file:', file.name, file.size, file.type);
      
      const formData = new FormData();
      formData.append('file', file);
      
      // Get base URL without the '/api' prefix
      const baseURL = window.location.hostname.includes('localhost') 
        ? 'http://localhost:5000'  // Development
        : ''; // Production (relative URL)
      
      // Use direct fetch instead of axios for file uploads
      const response = await fetch(`${baseURL}/api/it-logs/upload`, {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Upload failed: ${response.status} ${response.statusText} - ${errorText}`);
      }
      
      const data = await response.json();
      return { data, error: null };
    } catch (error) {
      console.error('Error uploading file:', error);
      return { data: null, error: error instanceof Error ? error.message : 'Unknown error during upload' };
    }
  },
  
  // Get statistics for operation logs
  getStatistics: async () => {
    return await safelyHandleResponse(
      api.get(`/it-logs/statistics`)
    );
  },
  
  // Debug endpoint
  debug: async () => {
    return await safelyHandleResponse(
      api.get('/debug/routes')
    );
  },
  
  // Add this function to your ITOperationLogApiService
  testAuth: async () => {
    return await safelyHandleResponse(
      api.get('/auth/check')
    );
  }
};

export default ITOperationLogApiService; 