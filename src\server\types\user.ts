type UserRole = 'IT_ADMIN' | 'IT_STAFF' | 'EMPLOYEE' | 'CEO' | 'FINANCE_MANAGER' | 'DEPT_HEAD' | 'VIEW';

export interface User {
  id: string;
  name: string;
  email: string;
  password: string;
  role: UserR<PERSON>;
  department: string;
  permissions: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserCreateInput {
  name: string;
  email: string;
  password: string;
  role: UserRole;
  department: string;
  permissions: string[];
  isActive?: boolean;
}
