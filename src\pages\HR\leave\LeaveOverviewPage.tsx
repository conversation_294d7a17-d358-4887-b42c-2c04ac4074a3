import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import LeaveOverview from '../../../components/HR/leave/LeaveOverview';
import { leaveRequestsApi } from '../../../services/leaveApi';
import { employeeApi } from '../../../services/employeeApi';
import { LeaveRequest, LeaveBalance } from '../../../types/attendance';

interface EmployeeLeaveData {
  employeeId: number;
  employeeName: string;
  employeeCode: string;
  department: string;
  position: string;
  leaveBalances: LeaveBalance[];
  recentRequests: LeaveRequest[];
  totalDaysUsed: number;
  totalDaysRemaining: number;
}

const LeaveOverviewPage: React.FC = () => {
  const { user } = useAuth();
  const [employeeLeaveData, setEmployeeLeaveData] = useState<EmployeeLeaveData[]>([]);
  const [allLeaveRequests, setAllLeaveRequests] = useState<LeaveRequest[]>([]);
  const [departments, setDepartments] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // Load data logic here (similar to LeaveManagement component)
      const employeesResponse = await employeeApi.getAll();
      const requestsResponse = await leaveRequestsApi.getAll();

      if (employeesResponse.success) {
        // Process employee data
        const employees = employeesResponse.data || [];
        const depts = Array.from(new Set(employees.map((emp: any) => emp.department).filter(Boolean)));
        setDepartments(depts);
      }

      if (requestsResponse.success) {
        setAllLeaveRequests(requestsResponse.data || []);
      }

      // Process employee leave data
      // This would include the same logic from LeaveManagement component
      setEmployeeLeaveData([]);
    } catch (error) {
      console.error('Error loading overview data:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <LeaveOverview
      employeeLeaveData={employeeLeaveData}
      allLeaveRequests={allLeaveRequests}
      departments={departments}
      loading={loading}
    />
  );
};

export default LeaveOverviewPage; 