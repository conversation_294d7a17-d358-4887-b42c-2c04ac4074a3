import { AppDataSource } from '../config/database';

/**
 * <PERSON><PERSON><PERSON> to diagnose and fix leave type constraint issues
 * This will help identify why "Leave type already exists" error occurs during updates
 */
async function fixLeaveTypeConstraints() {
  try {
    await AppDataSource.initialize();
    console.log('🔌 Database connection established');

    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();

    // Check current table structure and constraints
    console.log('📋 Checking leave_type_policies table structure...');
    
    const tableInfo = await queryRunner.query(`
      DESCRIBE leave_type_policies
    `);
    
    console.log('📊 Table structure:');
    console.table(tableInfo);

    // Check for indexes and constraints
    console.log('🔍 Checking indexes and constraints...');
    
    const indexes = await queryRunner.query(`
      SHOW INDEX FROM leave_type_policies
    `);
    
    console.log('📈 Indexes:');
    console.table(indexes);

    // Check for foreign key constraints
    const constraints = await queryRunner.query(`
      SELECT 
        CONSTRAINT_NAME,
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
      WHERE TABLE_NAME = 'leave_type_policies' 
      AND TABLE_SCHEMA = DATABASE()
    `);
    
    console.log('🔗 Constraints:');
    console.table(constraints);

    // Check current data for duplicates
    console.log('🔍 Checking for duplicate leaveType values...');
    
    const duplicates = await queryRunner.query(`
      SELECT 
        leaveType, 
        policyConfigurationId,
        COUNT(*) as count 
      FROM leave_type_policies 
      GROUP BY leaveType, policyConfigurationId
      HAVING COUNT(*) > 1
    `);
    
    if (duplicates.length > 0) {
      console.log('⚠️  Found duplicate leave types:');
      console.table(duplicates);
      
      // Show details of duplicates
      for (const dup of duplicates) {
        const details = await queryRunner.query(`
          SELECT id, leaveType, displayName, policyConfigurationId, createdAt, updatedAt
          FROM leave_type_policies 
          WHERE leaveType = ? AND policyConfigurationId = ?
          ORDER BY createdAt DESC
        `, [dup.leaveType, dup.policyConfigurationId]);
        
        console.log(`\n📋 Details for duplicate leaveType "${dup.leaveType}":`);
        console.table(details);
      }
    } else {
      console.log('✅ No duplicate leave types found');
    }

    // Check if there's a unique constraint we need to handle
    const uniqueConstraints = await queryRunner.query(`
      SELECT CONSTRAINT_NAME, COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE TABLE_NAME = 'leave_type_policies' 
      AND TABLE_SCHEMA = DATABASE()
      AND CONSTRAINT_NAME LIKE '%UNIQUE%'
    `);
    
    if (uniqueConstraints.length > 0) {
      console.log('🔒 Found unique constraints:');
      console.table(uniqueConstraints);
    } else {
      console.log('ℹ️  No explicit unique constraints found on leave_type_policies');
    }

    // Suggest fixing approach
    console.log('\n💡 Suggested fixes:');
    console.log('1. Check if the business logic requires unique leaveType per policy configuration');
    console.log('2. If duplicates exist, clean them up by keeping the most recent entry');
    console.log('3. Consider adding a proper unique constraint: UNIQUE(leaveType, policyConfigurationId)');

    await queryRunner.release();
    
  } catch (error) {
    console.error('❌ Error diagnosing leave type constraints:', error);
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
    }
  }
}

// Run the script if executed directly
if (require.main === module) {
  fixLeaveTypeConstraints();
}

export { fixLeaveTypeConstraints }; 